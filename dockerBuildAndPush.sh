#!/bin/bash

# Set the version
version=$(date +%Y%m%d%H%M%S)

# Docker registry credentials
registry="481061297250.dkr.ecr.us-east-2.amazonaws.com"


# Login to docker registry
aws ecr get-login-password --region us-east-2 --profile aeidev | docker login --username AWS --password-stdin $registry

# Build the Docker image
docker build --platform linux/amd64 -t actiontec/ocplatform:$version .

docker tag actiontec/ocplatform:$version $registry/actiontec/ocplatform:$version

# Push the Docker image
docker push $registry/actiontec/ocplatform:$version