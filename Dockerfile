FROM openjdk:8-jdk

ENV TZ GMT

VOLUME /tmp

ADD build/libs/iot-cloud-backend*.jar backend.jar
ADD src/main/resources/application-local.properties application.properties
ADD src/main/resources/log4j2-docker.xml log4j2.xml
#EXPOSE 9092
EXPOSE 8888
RUN bash -c 'touch /backend.jar'
RUN bash -c 'touch /application.properties'

ENTRYPOINT ["java", \
             "-Dlog4j.configurationFile=log4j2.xml", \
             "-jar", \
             "/backend.jar", "--spring.config.location=application.properties"]
