package com.actiontec.optim.platform.service;

import org.junit.Assert;
import org.junit.Test;

public class SimpleAesEcbCryptoServiceTest {
    @Test
    public void test() throws Exception {
        SimpleAesEcbCryptoService simpleAesEcbCryptoService = new SimpleAesEcbCryptoService();
        String origStr = "original string";
        String key = "0123456789abcdef";
        String encryptedStr = simpleAesEcbCryptoService.encrypt(origStr, key);
        String decryptedStr = simpleAesEcbCryptoService.decrypt(encryptedStr, key);
        Assert.assertNotEquals(origStr, encryptedStr);
        Assert.assertEquals(origStr, decryptedStr);
    }
}
