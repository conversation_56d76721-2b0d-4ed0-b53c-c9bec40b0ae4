package com.actiontec.optim.platform.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Assert;
import org.junit.Test;

public class NetworkSsidVirtualKeyTest {

    @Test
    public void testToJsonString() throws Exception {
        SsidCompositeKey[] ssidCompositeKeys = new SsidCompositeKey[2];
        ssidCompositeKeys[0] = new SsidCompositeKey("2.4G", "Primary");
        ssidCompositeKeys[1] = new SsidCompositeKey("5G", "Primary");
        NetworkSsidVirtualKey key = new NetworkSsidVirtualKey("sn123", ssidCompositeKeys);
        String jsonStr = key.toJsonString();

        ObjectMapper objectMapper = new ObjectMapper();
        NetworkSsidVirtualKey keyAfter = objectMapper.readValue(jsonStr, NetworkSsidVirtualKey.class);
        Assert.assertEquals(key.getSerial(), keyAfter.getSerial());
        Assert.assertArrayEquals(key.getSsidCompositeKeys(), keyAfter.getSsidCompositeKeys());

    }
}
