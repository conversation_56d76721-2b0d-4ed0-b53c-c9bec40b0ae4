package com.actiontec.optim.util;

//import org.junit.Assert;

import org.junit.Test;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

public class MacOuiUtilTest {
    @Test
    public void testMaskLocallyAdministeredBit() {
        assertNull(MacOuiUtil.maskLocallyAdministeredBit(null));

        assertEquals("20", MacOuiUtil.maskLocallyAdministeredBit("20"));
        assertEquals("88:EF:64", MacOuiUtil.maskLocallyAdministeredBit("88:EF:64"));

        assertEquals("21:45", MacOuiUtil.maskLocallyAdministeredBit("23:45"));
        assertEquals("98:CC:22:33", MacOuiUtil.maskLocallyAdministeredBit("9A:CC:22:33"));
        assertEquals("3c:01:45:67:89:00", MacOuiUtil.maskLocallyAdministeredBit("3e:01:45:67:89:00"));

        assertEquals("aDart", MacOuiUtil.maskLocallyAdministeredBit("aFart"));
    }
}
