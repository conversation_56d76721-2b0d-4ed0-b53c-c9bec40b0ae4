package com.actiontec.optim.util;

import java.util.regex.Pattern;

public class SimpleVersion implements Comparable<SimpleVersion> {
    private static final String DELIMITER = ".";
    private static final int MAX_FIELD_WIDTH = 8;
    private String verString;

    public SimpleVersion(String verString) {
        this.verString = verString;
    }

    public String getVerString() {
        return verString;
    }

    @Override
    public int compareTo(SimpleVersion anotherVer) {
        String s1 = normalizeVerString(verString);
        String s2 = normalizeVerString(anotherVer.getVerString());
        return s1.compareTo(s2);
    }

    public boolean isNewerThan(SimpleVersion anotherVer) {
        return compareTo(anotherVer) > 0;
    }

    public boolean isOlderThan(SimpleVersion anotherVer) {
        return compareTo(anotherVer) < 0;
    }

    public boolean isNewerThan(String anotherVerString) {
        return isNewerThan(new SimpleVersion(anotherVerString));
    }

    public boolean isOlderThan(String anotherVerString) {
        return isOlderThan(new SimpleVersion(anotherVerString));
    }

    private static String normalizeVerString(String verString) {
        return normalizeVerString(verString, DELIMITER, MAX_FIELD_WIDTH);
    }

    private static String normalizeVerString(String verString, String delimiter, int maxFieldWidth) {
        String[] split = Pattern.compile(delimiter, Pattern.LITERAL).split(verString);
        StringBuilder sb = new StringBuilder();
        for (String s : split) {
            sb.append(String.format("%" + maxFieldWidth + 's', s));
        }
        return sb.toString();
    }
}