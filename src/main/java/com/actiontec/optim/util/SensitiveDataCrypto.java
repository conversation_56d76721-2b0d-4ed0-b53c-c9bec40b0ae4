package com.actiontec.optim.util;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Utility class for AES encryption/decryption of sensitive data
 */
public class SensitiveDataCrypto {
    private static final Logger logger = LogManager.getLogger(SensitiveDataCrypto.class);
    private static final String ALGORITHM = "AES";
    private static final String SECRET_KEY = "Xj9Yzw5Kp2Lm8Qn4Hv6Bg7Tf3Rd1Ac0E";

    private SensitiveDataCrypto() {
        // Private constructor to prevent instantiation
    }

    /**
     * Encrypt password using AES algorithm
     * @param password password to encrypt
     * @return Base64 encoded encrypted password
     */
    public static String encrypt(String password) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encryptedBytes = cipher.doFinal(password.getBytes());
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            logger.error("Error encrypting password", e);
            throw new RuntimeException("Error encrypting password", e);
        }
    }

    /**
     * Decrypt AES encrypted password
     * @param encryptedPassword Base64 encoded encrypted password
     * @return decrypted password
     */
    public static String decrypt(String encryptedPassword) {
        try {
            SecretKeySpec secretKey = new SecretKeySpec(SECRET_KEY.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            byte[] decryptedBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedPassword));
            return new String(decryptedBytes);
        } catch (Exception e) {
            logger.error("Error decrypting password", e);
            throw new RuntimeException("Error decrypting password", e);
        }
    }
}