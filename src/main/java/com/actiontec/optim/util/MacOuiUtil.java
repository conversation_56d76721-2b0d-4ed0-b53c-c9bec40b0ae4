package com.actiontec.optim.util;

import java.util.HashMap;

public class MacOuiUtil {
    private static final HashMap<Character, Character> LOCALLY_ADMIN_CHAR_MAPPING_TABLE = new HashMap<>();

    static {
        //XXX: java 8 don't have a simple way to create an immutable map
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('2', '0');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('3', '1');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('6', '4');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('7', '5');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('a', '8');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('b', '9');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('e', 'c');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('f', 'd');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('A', '8');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('B', '9');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('E', 'C');
        LOCALLY_ADMIN_CHAR_MAPPING_TABLE.put('F', 'D');
    }

    public static String maskLocallyAdministeredBit(String macInOctet) {
        String result = macInOctet;
        if (macInOctet != null && macInOctet.length() >= 2) {
            Character x = LOCALLY_ADMIN_CHAR_MAPPING_TABLE.get(macInOctet.charAt(1));
            if (x != null) {
                StringBuilder sb = new StringBuilder(macInOctet);
                sb.setCharAt(1, x);
                result = sb.toString();
            }
        }
        return result;
    }
}
