package com.actiontec.optim.util;

import com.incs83.util.CommonUtils;

public class LinuxAccountUtils {
    /**
     * Generate an 8-character username that complies with Linux standards
     * Uses UUID and ensures it starts with a letter
     *
     * @return Linux-compliant username
     */
    public static String generateLinuxUsername() {
        String uuid = CommonUtils.generateUUID().replaceAll("-", "").substring(0, 8);
        String username = uuid.toLowerCase();

        // Linux requires usernames to start with a letter
        if (!Character.isLetter(username.charAt(0))) {
            username = "u" + username.substring(1);
        }

        return username;
    }

    /**
     * Generate an 8-character password that complies with Linux standards
     * Uses UUID and ensures it contains uppercase, digit, and special character
     *
     * @return Linux-compliant password
     */
    public static String generateLinuxPassword() {
        String uuid = CommonUtils.generateUUID().replaceAll("-", "").substring(0, 8);
        StringBuilder password = new StringBuilder(uuid);

        // Linux password requirements: uppercase, digit, and special character
        password.setCharAt(0, Character.toUpperCase(password.charAt(0)));

        if (!password.toString().matches(".*\\d.*")) {
            password.setCharAt(1, '1');
        }

        // Replace last character with special character
        password.setCharAt(password.length() - 1, '!');

        return password.toString();
    }
}
