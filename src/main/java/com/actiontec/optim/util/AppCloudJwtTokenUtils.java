package com.actiontec.optim.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.Jwts;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Map;

public class AppCloudJwtTokenUtils {
    /**
     * Extract kid from JWT token header
     */
    public static String extractKid(String token) {
        try {
            // JWT格式: header.payload.signature
            String[] chunks = token.split("\\.");
            if (chunks.length < 2) {
                return CustomStringUtils.EMPTY;
            }

            // 解碼header部分（Base64 URL解碼）
            String header = new String(Base64.getUrlDecoder().decode(chunks[0]));

            // 使用ObjectMapper解析JSON
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, Object> headerMap = objectMapper.readValue(header, Map.class);

            String kid = (String) headerMap.get("kid");
            return CustomStringUtils.isNotEmpty(kid) ? kid : CustomStringUtils.EMPTY;
        } catch (Exception e) {
            return CustomStringUtils.EMPTY;
        }
    }

    /**
     * Extract role ID from JWT token claims
     */
    public static String extractRoleId(String token, String signingKey) {
        return Jwts.parser()
                .setSigningKey(signingKey)
                .parseClaimsJws(token)
                .getBody()
                .get("roleId", String.class);
    }

    /**
     * Parse PEM format public key
     */
    public static PublicKey parsePublicKey(String publicKeyPem, String publicKeyAlgorithm) throws Exception {
        String publicKeyContent = publicKeyPem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(publicKeyContent);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(publicKeyAlgorithm);
        return keyFactory.generatePublic(spec);
    }
}
