package com.actiontec.optim.util;

import io.jsonwebtoken.Jwts;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

public class AppCloudJwtTokenUtils {
    /**
     * Extract kid from JWT token header
     */
    public static String extractKid(String token) {
        return Jwts.parser()
                .parseClaimsJws(token)
                .getHeader()
                .getKeyId();
    }

    /**
     * Parse PEM format public key
     */
    public static PublicKey parsePublicKey(String publicKeyPem, String publicKeyAlgorithm) throws Exception {
        String publicKeyContent = publicKeyPem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(publicKeyContent);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(publicKeyAlgorithm);
        return keyFactory.generatePublic(spec);
    }
}
