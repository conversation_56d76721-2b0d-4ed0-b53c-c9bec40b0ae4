package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

public class CpeSmmAppStatsDto {
    public static final String COLLECTION_cpeSmmAppStats = "cpeSmmAppStats";

    public enum CpeSmmAppFields {
        isp,
        serviceId,
        version,
        actionState,
        desiredState,
        online,
        jobId,
        count,
        avgCpu,
        maxCpu,
        minCpu,
        totalCpu,
        avgMem,
        maxMem,
        minMem,
        totalMem,
        avgFsUsed,
        maxFsUsed,
        minFsUsed,
        totalFsUsed,
        timestamp
    }

    private String isp;
    private String serviceId;
    private String version;
    private String actionState;
    private String desiredState;
    private Boolean online;
    private Long count;
    private Double avgCpu;
    private Long maxCpu;
    private Long minCpu;
    private Long totalCpu;
    private Double avgMem;
    private Long maxMem;
    private Long minMem;
    private Long totalMem;
    private Double avgFsUsed;
    private Long maxFsUsed;
    private Long minFsUsed;
    private Long totalFsUsed;

    public static CpeSmmAppStatsDto fromBasicDbObject(BasicDBObject dbObj) {
        CpeSmmAppStatsDto cpeSmmAppStatsDto = new CpeSmmAppStatsDto();
        cpeSmmAppStatsDto.setIsp(dbObj.getString(CpeSmmAppFields.isp.name()));
        cpeSmmAppStatsDto.setServiceId(dbObj.getString(CpeSmmAppFields.serviceId.name()));
        cpeSmmAppStatsDto.setVersion(dbObj.getString(CpeSmmAppFields.version.name(), ""));
        cpeSmmAppStatsDto.setActionState(dbObj.getString(CpeSmmAppFields.actionState.name()));
        cpeSmmAppStatsDto.setDesiredState(dbObj.getString(CpeSmmAppFields.desiredState.name()));
        cpeSmmAppStatsDto.setOnline(dbObj.getBoolean(CpeSmmAppFields.online.name()));
        cpeSmmAppStatsDto.setCount(dbObj.getLong(CpeSmmAppFields.count.name(), 0));
        cpeSmmAppStatsDto.setAvgCpu(dbObj.getDouble(CpeSmmAppFields.avgCpu.name(), 0));
        cpeSmmAppStatsDto.setMaxCpu(dbObj.getLong(CpeSmmAppFields.maxCpu.name(), 0));
        cpeSmmAppStatsDto.setMinCpu(dbObj.getLong(CpeSmmAppFields.minCpu.name(), 0));
        cpeSmmAppStatsDto.setTotalCpu(dbObj.getLong(CpeSmmAppFields.totalCpu.name(), 0));
        cpeSmmAppStatsDto.setAvgMem(dbObj.getDouble(CpeSmmAppFields.avgMem.name(), 0));
        cpeSmmAppStatsDto.setMaxMem(dbObj.getLong(CpeSmmAppFields.maxMem.name(), 0));
        cpeSmmAppStatsDto.setMinMem(dbObj.getLong(CpeSmmAppFields.minMem.name(), 0));
        cpeSmmAppStatsDto.setTotalMem(dbObj.getLong(CpeSmmAppFields.totalMem.name(), 0));
        cpeSmmAppStatsDto.setAvgFsUsed(dbObj.getDouble(CpeSmmAppFields.avgFsUsed.name(), 0));
        cpeSmmAppStatsDto.setMaxFsUsed(dbObj.getLong(CpeSmmAppFields.maxFsUsed.name(), 0));
        cpeSmmAppStatsDto.setMinFsUsed(dbObj.getLong(CpeSmmAppFields.minFsUsed.name(), 0));
        cpeSmmAppStatsDto.setTotalFsUsed(dbObj.getLong(CpeSmmAppFields.totalFsUsed.name(), 0));
        return cpeSmmAppStatsDto;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getActionState() {
        return actionState;
    }

    public void setActionState(String actionState) {
        this.actionState = actionState;
    }

    public String getDesiredState() {
        return desiredState;
    }

    public void setDesiredState(String desiredState) {
        this.desiredState = desiredState;
    }

    public Boolean getOnline() {
        return online;
    }

    public void setOnline(Boolean online) {
        this.online = online;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public Double getAvgCpu() {
        return avgCpu;
    }

    public void setAvgCpu(Double avgCpu) {
        this.avgCpu = avgCpu;
    }

    public Long getMaxCpu() {
        return maxCpu;
    }

    public void setMaxCpu(Long maxCpu) {
        this.maxCpu = maxCpu;
    }

    public Long getMinCpu() {
        return minCpu;
    }

    public void setMinCpu(Long minCpu) {
        this.minCpu = minCpu;
    }

    public Long getTotalCpu() {
        return totalCpu;
    }

    public void setTotalCpu(Long totalCpu) {
        this.totalCpu = totalCpu;
    }

    public Double getAvgMem() {
        return avgMem;
    }

    public void setAvgMem(Double avgMem) {
        this.avgMem = avgMem;
    }

    public Long getMaxMem() {
        return maxMem;
    }

    public void setMaxMem(Long maxMem) {
        this.maxMem = maxMem;
    }

    public Long getMinMem() {
        return minMem;
    }

    public void setMinMem(Long minMem) {
        this.minMem = minMem;
    }

    public Long getTotalMem() {
        return totalMem;
    }

    public void setTotalMem(Long totalMem) {
        this.totalMem = totalMem;
    }

    public Double getAvgFsUsed() {
        return avgFsUsed;
    }

    public void setAvgFsUsed(Double avgFsUsed) {
        this.avgFsUsed = avgFsUsed;
    }

    public Long getMaxFsUsed() {
        return maxFsUsed;
    }

    public void setMaxFsUsed(Long maxFsUsed) {
        this.maxFsUsed = maxFsUsed;
    }

    public Long getMinFsUsed() {
        return minFsUsed;
    }

    public void setMinFsUsed(Long minFsUsed) {
        this.minFsUsed = minFsUsed;
    }

    public Long getTotalFsUsed() {
        return totalFsUsed;
    }

    public void setTotalFsUsed(Long totalFsUsed) {
        this.totalFsUsed = totalFsUsed;
    }

}