package com.actiontec.optim.mongodb.dto;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ApDetailDto {
    private static final Logger LOGGER = LogManager.getLogger(ApDetailDto.class);
    public static final String COLLECTION_apDetail = "apDetail";

    public enum ApDetailFields {
        userId,
        serialNumber,
        macAddress,
        timestamp,
        type,
        controller,
        modelName,
        buildVersion,
        baseMacAddress,
        ssids,
        wifiRadios,
        DSL,
        mocas,
        mocaDevices,
        voip,
        network,
        pon,
        wan,
        friendlyName,
        sendingTime,
        receivedTime,
        ntInfoTimestamp,
        ntReportTimestamp
    }

    private String userId;
    private String serialNumber;
    private String macAddress;
    private String baseMacAddress;
    private String type;
    private boolean controller;
    private String modelName;
    private String buildVersion;
    private String friendlyName;
    private List<RadioDto> wifiRadios;
    private List<SsidDto> ssids;
    private DslDto dslDto;
    private List<MocaDto> mocas;
    private List<MocaDeviceDto> mocaDevices;
    private VoipDto voipDto;
    private NetworkDto networkDto;
    private PonDto ponDto;
    private WanDto wanDto;
    private long sendingTime;
    private long receivedTime;
    private long timestamp;
    private long ntInfoTimestamp;
    private long ntReportTimestamp;

    public static ApDetailDto fromBasicDbObject(BasicDBObject dbObj) {
        ApDetailDto result = new ApDetailDto();
        result.setUserId(dbObj.getString(ApDetailFields.userId.name()));
        result.setSerialNumber(dbObj.getString(ApDetailFields.serialNumber.name()));
        result.setMacAddress(dbObj.getString(ApDetailFields.macAddress.name()));
        result.setBaseMacAddress(dbObj.getString(ApDetailFields.baseMacAddress.name()));
        result.setType(dbObj.getString(ApDetailFields.type.name()));
        result.setController(dbObj.getBoolean(ApDetailFields.controller.name(), false));
        result.setModelName(dbObj.getString(ApDetailFields.modelName.name()));
        result.setBuildVersion(dbObj.getString(ApDetailFields.buildVersion.name()));
        result.setFriendlyName(dbObj.getString(ApDetailFields.friendlyName.name()));
        result.setSendingTime(dbObj.getLong(ApDetailFields.sendingTime.name(), 0L));
        result.setTimestamp(dbObj.getLong(ApDetailFields.timestamp.name(), 0L));
        result.setNtInfoTimestamp(dbObj.getLong(ApDetailFields.ntInfoTimestamp.name(), 0L));
        result.setNtReportTimestamp(dbObj.getLong(ApDetailFields.ntReportTimestamp.name(), 0L));
        result.setMocaDevices(MocaDeviceDto.fromBasicDbList((BasicDBList) dbObj.get(ApDetailFields.mocaDevices.name())));
        Optional.ofNullable(dbObj.get(ApDetailFields.wifiRadios.name())).ifPresent(list->result.setWifiRadios(RadioDto.fromBasicDbList((BasicDBList)list)));
        result.setSsids(SsidDto.fromBasicDbList((BasicDBList)dbObj.get(ApDetailFields.ssids.name())));
        result.setNtReportTimestamp(dbObj.getLong(ApDetailFields.ntReportTimestamp.name(),0));
        return result;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getBaseMacAddress() {
        return baseMacAddress;
    }

    public void setBaseMacAddress(String baseMacAddress) {
        this.baseMacAddress = baseMacAddress;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isController() {
        return controller;
    }

    public void setController(boolean controller) {
        this.controller = controller;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getBuildVersion() {
        return buildVersion;
    }

    public void setBuildVersion(String buildVersion) {
        this.buildVersion = buildVersion;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public List<RadioDto> getWifiRadios() {
        return wifiRadios;
    }

    public void setWifiRadios(List<RadioDto> wifiRadios) {
        this.wifiRadios = wifiRadios;
    }

    public List<SsidDto> getSsids() {
        return ssids;
    }

    public void setSsids(List<SsidDto> ssids) {
        this.ssids = ssids;
    }

    public DslDto getDslDto() {
        return dslDto;
    }

    public void setDslDto(DslDto dslDto) {
        this.dslDto = dslDto;
    }

    public List<MocaDto> getMocas() {
        return mocas;
    }

    public void setMocas(List<MocaDto> mocas) {
        this.mocas = mocas;
    }

    public List<MocaDeviceDto> getMocaDevices() {
        return mocaDevices;
    }

    public void setMocaDevices(List<MocaDeviceDto> mocaDevices) {
        this.mocaDevices = mocaDevices;
    }

    public VoipDto getVoipDto() {
        return voipDto;
    }

    public void setVoipDto(VoipDto voipDto) {
        this.voipDto = voipDto;
    }

    public NetworkDto getNetworkDto() {
        return networkDto;
    }

    public void setNetworkDto(NetworkDto networkDto) {
        this.networkDto = networkDto;
    }

    public PonDto getPonDto() {
        return ponDto;
    }

    public void setPonDto(PonDto ponDto) {
        this.ponDto = ponDto;
    }

    public WanDto getWanDto() {
        return wanDto;
    }

    public void setWanDto(WanDto wanDto) {
        this.wanDto = wanDto;
    }

    public long getSendingTime() {
        return sendingTime;
    }

    public void setSendingTime(long sendingTime) {
        this.sendingTime = sendingTime;
    }

    public long getReceivedTime() {
        return receivedTime;
    }

    public void setReceivedTime(long receivedTime) {
        this.receivedTime = receivedTime;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getNtInfoTimestamp() {
        return ntInfoTimestamp;
    }

    public void setNtInfoTimestamp(long ntInfoTimestamp) {
        this.ntInfoTimestamp = ntInfoTimestamp;
    }

    public long getNtReportTimestamp() {
        return ntReportTimestamp;
    }

    public void setNtReportTimestamp(long ntReportTimestamp) {
        this.ntReportTimestamp = ntReportTimestamp;
    }

    public static class SsidDto {
        public enum SsidDtoFields {
            ssidId,
            BSSID,
            SSID,
            Enable,
            SecurityMode,
            Password,
            RadioKey,
            SSIDKey,
            BroadcastSSID
        }

        private String ssidId;
        private String bssid;
        private String ssid;
        private Boolean enabled;
        private String securityMode;
        private String password;
        private RadioEnum radioEnum;
        private String ssidKey;
        private String broadcastSsid;
        private String serialNumber;
        private long lastReportTime;

        public static SsidDto fromBasicDbObject(BasicDBObject dbObj) {
            SsidDto result = new SsidDto();
            result.setBssid(dbObj.getString(SsidDtoFields.BSSID.name()));
            result.setSsid(dbObj.getString(SsidDtoFields.SSID.name()));
            result.setEnabled(dbObj.getBoolean(SsidDtoFields.Enable.name()));
            result.setSecurityMode(dbObj.getString(SsidDtoFields.SecurityMode.name()));
            result.setPassword(dbObj.getString(SsidDtoFields.Password.name()));
            result.setRadioEnum(RadioEnum.parseByRadioKey(dbObj.getString(SsidDtoFields.RadioKey.name())).orElse(null));
            result.setSsidKey(dbObj.getString(SsidDtoFields.SSIDKey.name()));
            result.setBroadcastSsid(dbObj.getString(SsidDtoFields.BroadcastSSID.name()));

            return result;
        }

        public static List<SsidDto> fromBasicDbList(BasicDBList dbList) {
            List<SsidDto> result = new ArrayList<>();
            if (Objects.isNull(dbList))
                return result;

            int i = 0;
            for (Object dbObj : dbList) {
                i++;
                SsidDto ssidDto = fromBasicDbObject((BasicDBObject) dbObj);
                if (StringUtils.isBlank(ssidDto.getSsidId())) {
                    //XXX: hack for legacy agent which does not report ssidId (OC-1715). gen ssidId based on its array index.
                    ssidDto.setSsidId(String.valueOf(i));
                }
                result.add(ssidDto);
            }
            return result;
        }

        public String getSsidId() {
            return ssidId;
        }

        public void setSsidId(String ssidId) {
            this.ssidId = ssidId;
        }

        public String getBssid() {
            return bssid;
        }

        public void setBssid(String bssid) {
            this.bssid = bssid;
        }

        public String getSsid() {
            return ssid;
        }

        public void setSsid(String ssid) {
            this.ssid = ssid;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public String getSecurityMode() {
            return securityMode;
        }

        public void setSecurityMode(String securityMode) {
            this.securityMode = securityMode;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public RadioEnum getRadioEnum() {
            return radioEnum;
        }

        public void setRadioEnum(RadioEnum radioEnum) {
            this.radioEnum = radioEnum;
        }

        public String getSsidKey() {
            return ssidKey;
        }

        public void setSsidKey(String ssidKey) {
            this.ssidKey = ssidKey;
        }

        public String getBroadcastSsid() {
            return broadcastSsid;
        }

        public void setBroadcastSsid(String broadcastSsid) {
            this.broadcastSsid = broadcastSsid;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public long getLastReportTime() {
            return lastReportTime;
        }

        public void setLastReportTime(long lastReportTime) {
            this.lastReportTime = lastReportTime;
        }
    }

    public static class RadioDto {
        public enum RadioFields {
            radioKey,
            enable,
            status,
            channel,
            channelWidth,
            operatingStandards,
            autoChannelEnable,
            dfsEnable,
            devicesAssociated,
            ntReportTimestamp,
            bytesSent,
            bytesReceived,
            avgBytesSent,
            avgBytesReceived,
            isBackhaul
        }

        private RadioEnum radioKey;
        private boolean enable;
        private String status;
        private int channel;
        private int channelWidth;
        private List<String> operatingStandards;
        private boolean autoChannelEnable;
        private boolean dfsEnable;
        private boolean isBackhaul;
        private long bytesSent;
        private long bytesReceived;
        private double avgBytesSent;
        private double avgBytesReceived;
        private int devicesAssociated;
        private long lastReportTime;

        public static RadioDto fromBasicDbObject(BasicDBObject dbObj) {
            RadioDto radioDto = new RadioDto();
            try {
                Optional.ofNullable(dbObj.getString(RadioFields.radioKey.name())).ifPresent(s -> radioDto.setRadioKey(RadioEnum.parseByRadioKey(s).get()));
                radioDto.setEnable(dbObj.getBoolean(RadioFields.enable.name()));
                radioDto.setStatus(dbObj.getString(RadioFields.status.name()));
                radioDto.setChannel(dbObj.getInt(RadioFields.channel.name()));
                radioDto.setChannelWidth(dbObj.getInt(RadioFields.channelWidth.name()));

                Object operatingStandardsObj = dbObj.get(RadioFields.operatingStandards.name());
                if (operatingStandardsObj != null) {
                    if (operatingStandardsObj instanceof BasicDBList) { // XXX: for backward compatible
                        List<String> opStandards = new ArrayList<>();
                        for (Object opStandard : (BasicDBList) operatingStandardsObj) {
                            opStandards.add(opStandard.toString());
                        }
                        radioDto.setOperatingStandards(opStandards);
                    } else {
                        radioDto.setOperatingStandards(Arrays.asList(dbObj.getString(RadioFields.operatingStandards.name()).split(",")));
                    }
                }

                radioDto.setAutoChannelEnable(dbObj.getBoolean(RadioFields.autoChannelEnable.name()));
                radioDto.setDfsEnable(dbObj.getBoolean(RadioFields.dfsEnable.name()));
                radioDto.setBackhaul(dbObj.getBoolean(RadioFields.isBackhaul.name()));
                radioDto.setBytesSent(dbObj.getLong(RadioFields.bytesSent.name(), 0L));
                radioDto.setBytesReceived(dbObj.getLong(RadioFields.bytesReceived.name(), 0L));
                radioDto.setAvgBytesSent(dbObj.getDouble(RadioFields.avgBytesSent.name(), 0));
                radioDto.setAvgBytesReceived(dbObj.getDouble(RadioFields.avgBytesReceived.name(), 0));
                radioDto.setDevicesAssociated(dbObj.getInt(RadioFields.devicesAssociated.name(), 0));
            } catch (Exception e) {
                LOGGER.debug("failed to convert mongodb obj.", e.getMessage());
                throw new RuntimeException(e);
            }
            return radioDto;
        }

        public static List<RadioDto> fromBasicDbList(BasicDBList dbList) {
            List<RadioDto> result = new ArrayList<>();
            if (dbList == null || dbList.isEmpty())
                return result;
            for (Object dbObj : dbList) {
                RadioDto radioDto = fromBasicDbObject((BasicDBObject) dbObj);
                result.add(radioDto);
            }
            return result;
        }

        public RadioEnum getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(RadioEnum radioKey) {
            this.radioKey = radioKey;
        }

        public boolean isEnable() {
            return enable;
        }

        public void setEnable(boolean enable) {
            this.enable = enable;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public int getChannel() {
            return channel;
        }

        public void setChannel(int channel) {
            this.channel = channel;
        }

        public int getChannelWidth() {
            return channelWidth;
        }

        public void setChannelWidth(int channelWidth) {
            this.channelWidth = channelWidth;
        }

        public List<String> getOperatingStandards() {
            return operatingStandards;
        }

        public void setOperatingStandards(List<String> operatingStandards) {
            this.operatingStandards = operatingStandards;
        }

        public boolean isAutoChannelEnable() {
            return autoChannelEnable;
        }

        public void setAutoChannelEnable(boolean autoChannelEnable) {
            this.autoChannelEnable = autoChannelEnable;
        }

        public boolean isDfsEnable() {
            return dfsEnable;
        }

        public void setDfsEnable(boolean dfsEnable) {
            this.dfsEnable = dfsEnable;
        }

        public boolean isBackhaul() {
            return isBackhaul;
        }

        public void setBackhaul(boolean backhaul) {
            isBackhaul = backhaul;
        }

        public long getBytesSent() {
            return bytesSent;
        }

        public void setBytesSent(long bytesSent) {
            this.bytesSent = bytesSent;
        }

        public long getBytesReceived() {
            return bytesReceived;
        }

        public void setBytesReceived(long bytesReceived) {
            this.bytesReceived = bytesReceived;
        }

        public double getAvgBytesSent() {
            return avgBytesSent;
        }

        public void setAvgBytesSent(double avgBytesSent) {
            this.avgBytesSent = avgBytesSent;
        }

        public double getAvgBytesReceived() {
            return avgBytesReceived;
        }

        public void setAvgBytesReceived(double avgBytesReceived) {
            this.avgBytesReceived = avgBytesReceived;
        }

        public int getDevicesAssociated() {
            return devicesAssociated;
        }

        public void setDevicesAssociated(int devicesAssociated) {
            this.devicesAssociated = devicesAssociated;
        }

        public long getLastReportTime() {
            return lastReportTime;
        }

        public void setLastReportTime(long lastReportTime) {
            this.lastReportTime = lastReportTime;
        }
    }

    public static class DslDto {
        private static final Map<Integer, Integer> DslReasonMap = Stream.of(
                new AbstractMap.SimpleEntry<>(0, 0),
                new AbstractMap.SimpleEntry<>(1, 1),
                new AbstractMap.SimpleEntry<>(2, 2),
                new AbstractMap.SimpleEntry<>(4, 3),
                new AbstractMap.SimpleEntry<>(8, 4),
                new AbstractMap.SimpleEntry<>(10, 5),
                new AbstractMap.SimpleEntry<>(20, 6),
                new AbstractMap.SimpleEntry<>(40, 7),
                new AbstractMap.SimpleEntry<>(80, 8),
                new AbstractMap.SimpleEntry<>(100, 9),
                new AbstractMap.SimpleEntry<>(200, 10),
                new AbstractMap.SimpleEntry<>(400, 11),
                new AbstractMap.SimpleEntry<>(800, 12),
                new AbstractMap.SimpleEntry<>(1000, 13),
                new AbstractMap.SimpleEntry<>(2000, 14),
                new AbstractMap.SimpleEntry<>(4000, 15),
                new AbstractMap.SimpleEntry<>(8000, 16),
                new AbstractMap.SimpleEntry<>(10000, 17),
                new AbstractMap.SimpleEntry<>(20000, 18),
                new AbstractMap.SimpleEntry<>(40000, 19),
                new AbstractMap.SimpleEntry<>(80000, 20),
                new AbstractMap.SimpleEntry<>(100000, 21),
                new AbstractMap.SimpleEntry<>(200000, 22),
                new AbstractMap.SimpleEntry<>(400000, 23),
                new AbstractMap.SimpleEntry<>(800000, 24),
                new AbstractMap.SimpleEntry<>(1000000, 25),
                new AbstractMap.SimpleEntry<>(2000000, 26)
        ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));

        public enum DslDtoFields {
            Status,
            LastChange,
            DownstreamLineRate,
            UpstreamLineRate,
            D1Attenuation,
            D2Attenuation,
            D3Attenuation,
            U1Attenuation,
            U2Attenuation,
            U3Attenuation,
            DownstreamPower,
            UpstreamPower,
            LinkRetrain,
            Uptime,
            StartTime,
            DownstreamSNR,
            UpstreamSNR,
            NearEndCRC,
            FarEndCRC,
            NearEndRSFEC,
            FarEndRSFEC,
            RxRsUncorrectable,
            TxRsUncorrectable,
            TrafficType,
            ErroredSecs,
            SeverelyErroredSecs,
            LastRetrainReason,
            LineNumber
        }

        private String status;
        private long lastChange;
        private int downstreamLineRate;
        private int upstreamLineRate;
        private double d1Attenuation;
        private double d2Attenuation;
        private double d3Attenuation;
        private double u1Attenuation;
        private double u2Attenuation;
        private double u3Attenuation;
        private double downstreamPower;
        private double upstreamPower;
        private int linkRetrain;
        private int uptime;
        private long startTime;
        private double downstreamSNR;
        private double upstreamSNR;
        private int nearEndCrc;
        private int farEndCrc;
        private int nearEndRSFEC;
        private int farEndRSFEC;
        private int rxRsUncorrectable;
        private int txRsUncorrectable;
        private String trafficType;
        private int erroredSecs;
        private int severelyErroredSecs;
        private int lastRetrainReason;
        private int lineNumber;

        public static DslDto fromBasicDbObject(BasicDBObject dbObj) {

            DslDto result = new DslDto();

            result.setStatus((dbObj.getInt(DslDtoFields.Status.name())) == 1 ? ApplicationConstants.DSL_UP : ApplicationConstants.DSL_DOWN);
            result.setLastChange(dbObj.getLong(DslDtoFields.LastChange.name()));
            result.setDownstreamLineRate(dbObj.getInt(DslDtoFields.DownstreamLineRate.name()));
            result.setUpstreamLineRate(dbObj.getInt(DslDtoFields.UpstreamLineRate.name()));
            result.setD1Attenuation(dbObj.getDouble(DslDtoFields.D1Attenuation.name()));
            result.setD2Attenuation(dbObj.getDouble(DslDtoFields.D2Attenuation.name()));
            result.setD3Attenuation(dbObj.getDouble(DslDtoFields.D3Attenuation.name()));
            result.setU1Attenuation(dbObj.getDouble(DslDtoFields.U1Attenuation.name()));
            result.setU2Attenuation(dbObj.getDouble(DslDtoFields.U2Attenuation.name()));
            result.setU3Attenuation(dbObj.getDouble(DslDtoFields.U3Attenuation.name()));
            result.setDownstreamPower(dbObj.getDouble(DslDtoFields.DownstreamPower.name()));
            result.setUpstreamPower(dbObj.getDouble(DslDtoFields.UpstreamPower.name()));
            result.setLinkRetrain(dbObj.getInt(DslDtoFields.LinkRetrain.name()));
            result.setUptime(dbObj.getInt(DslDtoFields.Uptime.name()));
            result.setStartTime(dbObj.getLong(DslDtoFields.StartTime.name()));
            result.setDownstreamSNR(dbObj.getDouble(DslDtoFields.DownstreamSNR.name()));
            result.setUpstreamSNR(dbObj.getDouble(DslDtoFields.UpstreamSNR.name()));
            result.setNearEndCrc(dbObj.getInt(DslDtoFields.NearEndCRC.name()));
            result.setFarEndCrc(dbObj.getInt(DslDtoFields.FarEndCRC.name()));
            result.setNearEndRSFEC(dbObj.getInt(DslDtoFields.NearEndRSFEC.name()));
            result.setFarEndRSFEC(dbObj.getInt(DslDtoFields.FarEndRSFEC.name()));
            result.setRxRsUncorrectable(dbObj.getInt(DslDtoFields.RxRsUncorrectable.name()));
            result.setTxRsUncorrectable(dbObj.getInt(DslDtoFields.TxRsUncorrectable.name()));
            result.setTrafficType(dbObj.getString(DslDtoFields.TrafficType.name()));
            result.setErroredSecs(dbObj.getInt(DslDtoFields.ErroredSecs.name(), 0));
            result.setSeverelyErroredSecs(dbObj.getInt(DslDtoFields.SeverelyErroredSecs.name()));
            result.setLastRetrainReason(DslReasonMap.get(dbObj.getInt(DslDtoFields.LastRetrainReason.name())));
            result.setLineNumber(dbObj.getInt(DslDtoFields.LineNumber.name()));

            return result;
        }

        public static List<DslDto> fromBasicDbList(BasicDBList dslList) {

            List<DslDto> result = new ArrayList<>();
            if (dslList == null)
                return result;

            for (Object dsl : dslList) {
                DslDto dslDto = fromBasicDbObject((BasicDBObject) dsl);
                result.add(dslDto);
            }
            return result;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public long getLastChange() {
            return lastChange;
        }

        public void setLastChange(long lastChange) {
            this.lastChange = lastChange;
        }

        public int getDownstreamLineRate() {
            return downstreamLineRate;
        }

        public void setDownstreamLineRate(int downstreamLineRate) {
            this.downstreamLineRate = downstreamLineRate;
        }

        public int getUpstreamLineRate() {
            return upstreamLineRate;
        }

        public void setUpstreamLineRate(int upstreamLineRate) {
            this.upstreamLineRate = upstreamLineRate;
        }

        public double getD1Attenuation() {
            return d1Attenuation;
        }

        public void setD1Attenuation(double d1Attenuation) {
            this.d1Attenuation = d1Attenuation;
        }

        public double getD2Attenuation() {
            return d2Attenuation;
        }

        public void setD2Attenuation(double d2Attenuation) {
            this.d2Attenuation = d2Attenuation;
        }

        public double getD3Attenuation() {
            return d3Attenuation;
        }

        public void setD3Attenuation(double d3Attenuation) {
            this.d3Attenuation = d3Attenuation;
        }

        public double getU1Attenuation() {
            return u1Attenuation;
        }

        public void setU1Attenuation(double u1Attenuation) {
            this.u1Attenuation = u1Attenuation;
        }

        public double getU2Attenuation() {
            return u2Attenuation;
        }

        public void setU2Attenuation(double u2Attenuation) {
            this.u2Attenuation = u2Attenuation;
        }

        public double getU3Attenuation() {
            return u3Attenuation;
        }

        public void setU3Attenuation(double u3Attenuation) {
            this.u3Attenuation = u3Attenuation;
        }

        public double getDownstreamPower() {
            return downstreamPower;
        }

        public void setDownstreamPower(double downstreamPower) {
            this.downstreamPower = downstreamPower;
        }

        public double getUpstreamPower() {
            return upstreamPower;
        }

        public void setUpstreamPower(double upstreamPower) {
            this.upstreamPower = upstreamPower;
        }

        public int getLinkRetrain() {
            return linkRetrain;
        }

        public void setLinkRetrain(int linkRetrain) {
            this.linkRetrain = linkRetrain;
        }

        public int getUptime() {
            return uptime;
        }

        public void setUptime(int uptime) {
            this.uptime = uptime;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public double getDownstreamSNR() {
            return downstreamSNR;
        }

        public void setDownstreamSNR(double downstreamSNR) {
            this.downstreamSNR = downstreamSNR;
        }

        public double getUpstreamSNR() {
            return upstreamSNR;
        }

        public void setUpstreamSNR(double upstreamSNR) {
            this.upstreamSNR = upstreamSNR;
        }

        public int getNearEndCrc() {
            return nearEndCrc;
        }

        public void setNearEndCrc(int nearEndCrc) {
            this.nearEndCrc = nearEndCrc;
        }

        public int getFarEndCrc() {
            return farEndCrc;
        }

        public void setFarEndCrc(int farEndCrc) {
            this.farEndCrc = farEndCrc;
        }

        public int getNearEndRSFEC() {
            return nearEndRSFEC;
        }

        public void setNearEndRSFEC(int nearEndRSFEC) {
            this.nearEndRSFEC = nearEndRSFEC;
        }

        public int getFarEndRSFEC() {
            return farEndRSFEC;
        }

        public void setFarEndRSFEC(int farEndRSFEC) {
            this.farEndRSFEC = farEndRSFEC;
        }

        public int getRxRsUncorrectable() {
            return rxRsUncorrectable;
        }

        public void setRxRsUncorrectable(int rxRsUncorrectable) {
            this.rxRsUncorrectable = rxRsUncorrectable;
        }

        public int getTxRsUncorrectable() {
            return txRsUncorrectable;
        }

        public void setTxRsUncorrectable(int txRsUncorrectable) {
            this.txRsUncorrectable = txRsUncorrectable;
        }

        public String getTrafficType() {
            return trafficType;
        }

        public void setTrafficType(String trafficType) {
            this.trafficType = trafficType;
        }

        public int getErroredSecs() {
            return erroredSecs;
        }

        public void setErroredSecs(int erroredSecs) {
            this.erroredSecs = erroredSecs;
        }

        public int getSeverelyErroredSecs() {
            return severelyErroredSecs;
        }

        public void setSeverelyErroredSecs(int severelyErroredSecs) {
            this.severelyErroredSecs = severelyErroredSecs;
        }

        public int getLastRetrainReason() {
            return lastRetrainReason;
        }

        public void setLastRetrainReason(int lastRetrainReason) {
            this.lastRetrainReason = lastRetrainReason;
        }

        public int getLineNumber() {
            return lineNumber;
        }

        public void setLineNumber(int lineNumber) {
            this.lineNumber = lineNumber;
        }
    }

    public static class MocaDto {
        private static final Logger logger = LogManager.getLogger(MocaDto.class);
        public enum MocaFields {
            portNo,
            isUpstream,
            macAddress,
            mocaVer,
            status,
            operatingFrequency,
            lastChangeTime,
            stats;
        }

        private int portNo;
        private boolean isUpstream;
        private String macAddress;
        private String mocaVer;
        private int status;
        private long operatingFrequency;
        private long lastChangeTime;
        private MocaStatsDto stats;

        public static class MocaStatsDto {
            private static final Logger logger = LogManager.getLogger(MocaStatsDto.class);

            public enum MocaStatsFields {
                bytesSent,
                bytesReceived
            }

            private long bytesSent;
            private long bytesReceived;

            public static MocaStatsDto fromBasicDbObject(BasicDBObject dbObj) {
                try {
                    MocaStatsDto mocaStatsDto = new MocaStatsDto();
                    mocaStatsDto.setBytesSent(dbObj.getLong(MocaStatsFields.bytesSent.name()));
                    mocaStatsDto.setBytesReceived(dbObj.getLong(MocaStatsFields.bytesReceived.name()));
                    return mocaStatsDto;
                } catch (Exception e) {
                    logger.error("failed in fromBasicDbObject", e);
                    throw new RuntimeException(e);
                }
            }

            public long getBytesSent() {
                return bytesSent;
            }

            public void setBytesSent(long bytesSent) {
                this.bytesSent = bytesSent;
            }

            public long getBytesReceived() {
                return bytesReceived;
            }

            public void setBytesReceived(long bytesReceived) {
                this.bytesReceived = bytesReceived;
            }
        }

        public static MocaDto fromBasicDbObject(BasicDBObject dbObj) {
            try {
                MocaDto mocaDto = new MocaDto();
                mocaDto.setPortNo(dbObj.getInt(MocaFields.portNo.name()));
                mocaDto.setUpstream(dbObj.getBoolean(MocaFields.isUpstream.name()));
                mocaDto.setMacAddress(dbObj.getString(MocaFields.macAddress.name()));
                mocaDto.setMocaVer(dbObj.getString(MocaFields.mocaVer.name()));
                mocaDto.setStatus(dbObj.getInt(MocaFields.status.name()));
                if(dbObj.containsField(MocaFields.operatingFrequency.name())){
                    mocaDto.setOperatingFrequency(dbObj.getLong(MocaFields.operatingFrequency.name()));
                }
                mocaDto.setLastChangeTime(dbObj.getLong(MocaFields.lastChangeTime.name()));
                mocaDto.setStats(MocaStatsDto.fromBasicDbObject((BasicDBObject) dbObj.get(MocaFields.stats.name())));
                return mocaDto;
            } catch (Exception e) {
                logger.error("failed in fromBasicDbObject", e);
                throw new RuntimeException(e);
            }
        }

        public static List<MocaDto> fromBasicDbList(BasicDBList dbList) {
            List<MocaDto> result = new ArrayList<>();
            for (Object dbObj : dbList) {
                MocaDto mocaDto = fromBasicDbObject((BasicDBObject) dbObj);
                result.add(mocaDto);
            }
            return result;
        }

        public int getPortNo() {
            return portNo;
        }

        public void setPortNo(int portNo) {
            this.portNo = portNo;
        }

        public boolean isUpstream() {
            return isUpstream;
        }

        public void setUpstream(boolean upstream) {
            isUpstream = upstream;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getMocaVer() {
            return mocaVer;
        }

        public void setMocaVer(String mocaVer) {
            this.mocaVer = mocaVer;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public long getOperatingFrequency() {
            return operatingFrequency;
        }

        public void setOperatingFrequency(long operatingFrequency) {
            this.operatingFrequency = operatingFrequency;
        }

        public long getLastChangeTime() {
            return lastChangeTime;
        }

        public void setLastChangeTime(long lastChangeTime) {
            this.lastChangeTime = lastChangeTime;
        }

        public MocaStatsDto getStats() {
            return stats;
        }

        public void setStats(MocaStatsDto stats) {
            this.stats = stats;
        }
    }

    public static class MocaDeviceDto {
        private static final Logger logger = LogManager.getLogger(MocaDeviceDto.class);

        public enum Fields {
            macAddress,
            txPHYRate,
            rxPHYRate,
            txPower,
            rxPower,
            attenuation,
            mode,
            portNo,
            upstream,
            uptime
        }

        private String macAddress;
        private double txPhyRate;
        private double rxPhyRate;
        private double txPower;
        private double rxPower;
        private double attenuation;
        private String mode;
        private Long portNo;
        private boolean isUpstream;
        private long uptime;

        public static MocaDeviceDto fromBasicDbObject(BasicDBObject dbObj) {
            try {
                MocaDeviceDto mocaDeviceDto = new MocaDeviceDto();
                mocaDeviceDto.setMacAddress(dbObj.getString(Fields.macAddress.name()));
                mocaDeviceDto.setTxPhyRate(dbObj.getDouble(Fields.txPHYRate.name()));
                mocaDeviceDto.setRxPhyRate(dbObj.getDouble(Fields.rxPHYRate.name()));
                mocaDeviceDto.setTxPower(dbObj.getDouble(Fields.txPower.name()));
                mocaDeviceDto.setRxPower(dbObj.getDouble(Fields.rxPower.name()));
                mocaDeviceDto.setAttenuation(dbObj.getDouble(Fields.attenuation.name()));
                mocaDeviceDto.setMode(dbObj.getString(Fields.mode.name()));
                if (dbObj.containsField(Fields.portNo.name())) {
                    mocaDeviceDto.setPortNo(dbObj.getLong(Fields.portNo.name()));
                } else {
                    mocaDeviceDto.setPortNo(1L);
                }
                if (dbObj.containsField(Fields.upstream.name())) {
                    mocaDeviceDto.setUpstream(dbObj.getBoolean(Fields.upstream.name()));
                }
                if (dbObj.containsField(Fields.uptime.name())) {
                    mocaDeviceDto.setUptime(dbObj.getLong(Fields.uptime.name()));
                }
                return mocaDeviceDto;
            } catch (Exception e) {
                logger.error("failed in fromBasicDbObject", e);
                throw new RuntimeException(e);
            }
        }

        public static List<MocaDeviceDto> fromBasicDbList(BasicDBList dbList) {
            List<MocaDeviceDto> result = new ArrayList<>();
            if (dbList == null) {
                return result;
            }

            for (Object dbObj : dbList) {
                MocaDeviceDto mocaDeviceDto = fromBasicDbObject((BasicDBObject) dbObj);
                result.add(mocaDeviceDto);
            }
            return result;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public double getTxPhyRate() {
            return txPhyRate;
        }

        public void setTxPhyRate(double txPhyRate) {
            this.txPhyRate = txPhyRate;
        }

        public double getRxPhyRate() {
            return rxPhyRate;
        }

        public void setRxPhyRate(double rxPhyRate) {
            this.rxPhyRate = rxPhyRate;
        }

        public double getTxPower() {
            return txPower;
        }

        public void setTxPower(double txPower) {
            this.txPower = txPower;
        }

        public double getRxPower() {
            return rxPower;
        }

        public void setRxPower(double rxPower) {
            this.rxPower = rxPower;
        }

        public double getAttenuation() {
            return attenuation;
        }

        public void setAttenuation(double attenuation) {
            this.attenuation = attenuation;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public Long getPortNo() {
            return portNo;
        }

        public void setPortNo(Long portNo) {
            this.portNo = portNo;
        }

        public Boolean getUpstream() {
            return isUpstream;
        }

        public void setUpstream(Boolean upstream) {
            isUpstream = upstream;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }
    }

    public static class VoipDto {
        public enum VoipDtoFields {
            Active,
            Enable,
            BoundIfName,
            BoundIpAddr,
            Network,
            Lines
        }

        private Boolean active;
        private Boolean enable;
        private String boundIfName;
        private String boundIpAddr;
        private NetworkDto network;
        private List<LineDto> lines;

        public static VoipDto fromBasicDBOject(BasicDBObject dbObj) {

            VoipDto voipDto = new VoipDto();

            voipDto.setActive(dbObj.getBoolean(VoipDtoFields.Active.name(), false));
            voipDto.setEnable(dbObj.getBoolean(VoipDtoFields.Enable.name(), false));
            voipDto.setBoundIfName(dbObj.getString(VoipDtoFields.BoundIfName.name()));
            voipDto.setBoundIpAddr(dbObj.getString(VoipDtoFields.BoundIpAddr.name()));
            voipDto.setNetwork(NetworkDto.fromBasicDBObject((BasicDBObject) dbObj.get(VoipDtoFields.Network.name())));
            voipDto.setLines(LineDto.fromBasicDBList((BasicDBList) dbObj.get(VoipDtoFields.Lines.name())));

            return voipDto;
        }

        public Boolean getActive() {
            return active;
        }

        public void setActive(Boolean active) {
            this.active = active;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public String getBoundIfName() {
            return boundIfName;
        }

        public void setBoundIfName(String boundIfName) {
            this.boundIfName = boundIfName;
        }

        public String getBoundIpAddr() {
            return boundIpAddr;
        }

        public void setBoundIpAddr(String boundIpAddr) {
            this.boundIpAddr = boundIpAddr;
        }

        public NetworkDto getNetwork() {
            return network;
        }

        public void setNetwork(NetworkDto network) {
            this.network = network;
        }

        public List<LineDto> getLines() {
            return lines;
        }

        public void setLines(List<LineDto> lines) {
            this.lines = lines;
        }

        public static class NetworkDto {
            public enum NetworkDtoFields {
                Enable,
                Status,
                ProxyServer,
                ProxyServerPort,
                ProxyServerTransport,
                RegistrarServer,
                RegistrarServerPort,
                RegistrarServerTransport,
                OutboundProxy,
                OutboundProxyPort,
                RegistrationPeriod,
                RegisterRetryInterval
            }

            private Boolean enable;
            private String status;
            private String proxyServer;
            private int proxyServerPort;
            private String proxyServerTransport;
            private String registrarServer;
            private int registrarServerPort;
            private String registrarServerTransport;
            private String outboundProxy;
            private int outboundProxyPort;
            private int registrationPeriod;
            private int registerRetryInterval;

            public static NetworkDto fromBasicDBObject(BasicDBObject dbObj) {

                NetworkDto networkDto = new NetworkDto();

                networkDto.setEnable(dbObj.getBoolean(NetworkDtoFields.Enable.name(), false));
                networkDto.setStatus(dbObj.getString(NetworkDtoFields.Status.name()));
                networkDto.setProxyServer(dbObj.getString(NetworkDtoFields.ProxyServer.name()));
                networkDto.setProxyServerPort(dbObj.getInt(NetworkDtoFields.ProxyServerPort.name(), 0));
                networkDto.setProxyServerTransport(dbObj.getString(NetworkDtoFields.ProxyServerTransport.name()));
                networkDto.setRegistrarServer(dbObj.getString(NetworkDtoFields.RegistrarServer.name()));
                networkDto.setRegistrarServerPort(dbObj.getInt(NetworkDtoFields.RegistrarServerPort.name(), 0));
                networkDto.setRegistrarServerTransport(dbObj.getString(NetworkDtoFields.RegistrarServerTransport.name()));
                networkDto.setOutboundProxy(dbObj.getString(NetworkDtoFields.OutboundProxy.name()));
                networkDto.setOutboundProxyPort(dbObj.getInt(NetworkDtoFields.OutboundProxyPort.name(), 0));
                networkDto.setRegistrationPeriod(dbObj.getInt(NetworkDtoFields.RegistrationPeriod.name(), 0));
                networkDto.setRegisterRetryInterval(dbObj.getInt(NetworkDtoFields.RegisterRetryInterval.name(), 0));

                return networkDto;
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }

            public String getStatus() {
                return status;
            }

            public void setStatus(String status) {
                this.status = status;
            }

            public String getProxyServer() {
                return proxyServer;
            }

            public void setProxyServer(String proxyServer) {
                this.proxyServer = proxyServer;
            }

            public int getProxyServerPort() {
                return proxyServerPort;
            }

            public void setProxyServerPort(int proxyServerPort) {
                this.proxyServerPort = proxyServerPort;
            }

            public String getProxyServerTransport() {
                return proxyServerTransport;
            }

            public void setProxyServerTransport(String proxyServerTransport) {
                this.proxyServerTransport = proxyServerTransport;
            }

            public String getRegistrarServer() {
                return registrarServer;
            }

            public void setRegistrarServer(String registrarServer) {
                this.registrarServer = registrarServer;
            }

            public int getRegistrarServerPort() {
                return registrarServerPort;
            }

            public void setRegistrarServerPort(int registrarServerPort) {
                this.registrarServerPort = registrarServerPort;
            }

            public String getRegistrarServerTransport() {
                return registrarServerTransport;
            }

            public void setRegistrarServerTransport(String registrarServerTransport) {
                this.registrarServerTransport = registrarServerTransport;
            }

            public String getOutboundProxy() {
                return outboundProxy;
            }

            public void setOutboundProxy(String outboundProxy) {
                this.outboundProxy = outboundProxy;
            }

            public int getOutboundProxyPort() {
                return outboundProxyPort;
            }

            public void setOutboundProxyPort(int outboundProxyPort) {
                this.outboundProxyPort = outboundProxyPort;
            }

            public int getRegistrationPeriod() {
                return registrationPeriod;
            }

            public void setRegistrationPeriod(int registrationPeriod) {
                this.registrationPeriod = registrationPeriod;
            }

            public int getRegisterRetryInterval() {
                return registerRetryInterval;
            }

            public void setRegisterRetryInterval(int registerRetryInterval) {
                this.registerRetryInterval = registerRetryInterval;
            }
        }

        public static class LineDto {
            public enum LineDtoFields {
                LineNumber,
                Client,
                CurrentStatus,
                CurrentStats,
                DeltaStats
            }

            private int lineNumber;
            private ClientDto client;
            private CurrentStatusDto currentStatus;
            private StatsDto currentStats;
            private StatsDto deltaStatsDto;

            public static LineDto fromBasicDbObject(BasicDBObject dbObj) {

                LineDto lineDto = new LineDto();

                lineDto.setLineNumber(dbObj.getInt(LineDtoFields.LineNumber.name(), 0));
                lineDto.setClient(ClientDto.fromBasicDBObject((BasicDBObject) dbObj.get(LineDtoFields.Client.name())));
                lineDto.setCurrentStatus(CurrentStatusDto.fromBasicDBObject((BasicDBObject) dbObj.get(LineDtoFields.CurrentStatus.name())));
                lineDto.setCurrentStats(StatsDto.fromBasicDBObject((BasicDBObject) dbObj.get(LineDtoFields.CurrentStats.name())));
                lineDto.setDeltaStatsDto(StatsDto.fromBasicDBObject((BasicDBObject) dbObj.get(LineDtoFields.DeltaStats.name())));

                return lineDto;
            }

            public static List<LineDto> fromBasicDBList(BasicDBList dbList) {

                List<LineDto> lineDtoList = new ArrayList<>();
                for (Object line : dbList) {
                    LineDto lineDto = fromBasicDbObject((BasicDBObject) line);
                    lineDtoList.add(lineDto);
                }

                return lineDtoList;
            }

            public int getLineNumber() {
                return lineNumber;
            }

            public void setLineNumber(int lineNumber) {
                this.lineNumber = lineNumber;
            }

            public ClientDto getClient() {
                return client;
            }

            public void setClient(ClientDto client) {
                this.client = client;
            }

            public CurrentStatusDto getCurrentStatus() {
                return currentStatus;
            }

            public void setCurrentStatus(CurrentStatusDto currentStatus) {
                this.currentStatus = currentStatus;
            }

            public StatsDto getCurrentStats() {
                return currentStats;
            }

            public void setCurrentStats(StatsDto currentStats) {
                this.currentStats = currentStats;
            }

            public StatsDto getDeltaStatsDto() {
                return deltaStatsDto;
            }

            public void setDeltaStatsDto(StatsDto deltaStatsDto) {
                this.deltaStatsDto = deltaStatsDto;
            }

            public static class ClientDto {
                public enum ClientDtoFields {
                    Enable,
                    Status,
                    AuthUserName,
                    AuthPassword,
                    RegisterUri,
                    ClientId
                }

                private Boolean enable;
                private String status;
                private String authUserName;
                private String authPassword;
                private String registerUri;
                private int clientId;

                public static ClientDto fromBasicDBObject(BasicDBObject dbObj) {

                    ClientDto clientDto = new ClientDto();

                    clientDto.setEnable(dbObj.getBoolean(ClientDtoFields.Enable.name()));
                    clientDto.setStatus(dbObj.getString(ClientDtoFields.Status.name()));
                    clientDto.setAuthUserName(dbObj.getString(ClientDtoFields.AuthUserName.name()));
                    clientDto.setAuthPassword(dbObj.getString(ClientDtoFields.AuthPassword.name()));
                    clientDto.setRegisterUri(dbObj.getString(ClientDtoFields.RegisterUri.name()));
                    clientDto.setClientId(dbObj.getInt(ClientDtoFields.ClientId.name()));

                    return clientDto;
                }

                public Boolean getEnable() {
                    return enable;
                }

                public void setEnable(Boolean enable) {
                    this.enable = enable;
                }

                public String getStatus() {
                    return status;
                }

                public void setStatus(String status) {
                    this.status = status;
                }

                public String getAuthUserName() {
                    return authUserName;
                }

                public void setAuthUserName(String authUserName) {
                    this.authUserName = authUserName;
                }

                public String getAuthPassword() {
                    return authPassword;
                }

                public void setAuthPassword(String authPassword) {
                    this.authPassword = authPassword;
                }

                public String getRegisterUri() {
                    return registerUri;
                }

                public void setRegisterUri(String registerUri) {
                    this.registerUri = registerUri;
                }

                public int getClientId() {
                    return clientId;
                }

                public void setClientId(int clientId) {
                    this.clientId = clientId;
                }
            }

            public static class CurrentStatusDto {
                public enum CurrentStatusDtoFields {
                    HookStatus,
                    CallDuration
                }

                private String hookStatus;
                private int callDuration;

                public static CurrentStatusDto fromBasicDBObject(BasicDBObject dbObj) {

                    CurrentStatusDto currentStatusDto = new CurrentStatusDto();

                    currentStatusDto.setHookStatus(dbObj.getString(CurrentStatusDtoFields.HookStatus.name()));
                    currentStatusDto.setCallDuration(dbObj.getInt(CurrentStatusDtoFields.CallDuration.name(), 0));

                    return currentStatusDto;
                }

                public String getHookStatus() {
                    return hookStatus;
                }

                public void setHookStatus(String hookStatus) {
                    this.hookStatus = hookStatus;
                }

                public int getCallDuration() {
                    return callDuration;
                }

                public void setCallDuration(int callDuration) {
                    this.callDuration = callDuration;
                }
            }

            public static class StatsDto {
                public enum StatsDtoFields {
                    IncomingCallsReceived,
                    IncomingCallsConnected,
                    IncomingCallsFailed,
                    IncomingCallsDropped,
                    IncomingCallsTotalCallTime,
                    OutgoingCallsAttempted,
                    OutgoingCallsConnected,
                    OutgoingCallsFailed,
                    OutgoingCallsDropped,
                    OutgoingCallsTotalCallTime,
                    PacketsReceived,
                    PacketsSent,
                    PacketsLost,
                    BytesSent,
                    BytesReceived
                }

                private long incomingCallsReceived;
                private long incomingCallsConnected;
                private long incomingCallsFailed;
                private long incomingCallsDropped;
                private long incomingCallsTotalCallTime;
                private long outgoingCallsAttempted;
                private long outgoingCallsConnected;
                private long outgoingCallsFailed;
                private long outgoingCallsDropped;
                private long outgoingCallsTotalCallTime;
                private long packetsReceived;
                private long packetsSent;
                private long packetsLost;
                private long bytesSent;
                private long bytesReceived;

                public static StatsDto fromBasicDBObject(BasicDBObject dbObj) {

                    StatsDto statsDto = new StatsDto();

                    statsDto.setIncomingCallsReceived(dbObj.getLong(StatsDtoFields.IncomingCallsReceived.name(), 0L));
                    statsDto.setIncomingCallsConnected(dbObj.getLong(StatsDtoFields.IncomingCallsConnected.name(), 0L));
                    statsDto.setIncomingCallsFailed(dbObj.getLong(StatsDtoFields.IncomingCallsFailed.name(), 0L));
                    statsDto.setIncomingCallsDropped(dbObj.getLong(StatsDtoFields.IncomingCallsDropped.name(), 0L));
                    statsDto.setIncomingCallsTotalCallTime(dbObj.getLong(StatsDtoFields.IncomingCallsTotalCallTime.name(), 0L));
                    statsDto.setOutgoingCallsAttempted(dbObj.getLong(StatsDtoFields.OutgoingCallsAttempted.name(), 0L));
                    statsDto.setOutgoingCallsConnected(dbObj.getLong(StatsDtoFields.OutgoingCallsConnected.name(), 0L));
                    statsDto.setOutgoingCallsFailed(dbObj.getLong(StatsDtoFields.OutgoingCallsFailed.name(), 0L));
                    statsDto.setOutgoingCallsDropped(dbObj.getLong(StatsDtoFields.OutgoingCallsDropped.name(), 0L));
                    statsDto.setOutgoingCallsTotalCallTime(dbObj.getLong(StatsDtoFields.IncomingCallsTotalCallTime.name(), 0L));
                    statsDto.setPacketsReceived(dbObj.getLong(StatsDtoFields.PacketsReceived.name(), 0L));
                    statsDto.setPacketsLost(dbObj.getLong(StatsDtoFields.PacketsLost.name(), 0L));
                    statsDto.setBytesSent(dbObj.getLong(StatsDtoFields.BytesSent.name(), 0L));
                    statsDto.setBytesReceived(dbObj.getLong(StatsDtoFields.BytesReceived.name(), 0L));

                    return statsDto;
                }

                public long getIncomingCallsReceived() {
                    return incomingCallsReceived;
                }

                public void setIncomingCallsReceived(long incomingCallsReceived) {
                    this.incomingCallsReceived = incomingCallsReceived;
                }

                public long getIncomingCallsConnected() {
                    return incomingCallsConnected;
                }

                public void setIncomingCallsConnected(long incomingCallsConnected) {
                    this.incomingCallsConnected = incomingCallsConnected;
                }

                public long getIncomingCallsFailed() {
                    return incomingCallsFailed;
                }

                public void setIncomingCallsFailed(long incomingCallsFailed) {
                    this.incomingCallsFailed = incomingCallsFailed;
                }

                public long getIncomingCallsDropped() {
                    return incomingCallsDropped;
                }

                public void setIncomingCallsDropped(long incomingCallsDropped) {
                    this.incomingCallsDropped = incomingCallsDropped;
                }

                public long getIncomingCallsTotalCallTime() {
                    return incomingCallsTotalCallTime;
                }

                public void setIncomingCallsTotalCallTime(long incomingCallsTotalCallTime) {
                    this.incomingCallsTotalCallTime = incomingCallsTotalCallTime;
                }

                public long getOutgoingCallsAttempted() {
                    return outgoingCallsAttempted;
                }

                public void setOutgoingCallsAttempted(long outgoingCallsAttempted) {
                    this.outgoingCallsAttempted = outgoingCallsAttempted;
                }

                public long getOutgoingCallsConnected() {
                    return outgoingCallsConnected;
                }

                public void setOutgoingCallsConnected(long outgoingCallsConnected) {
                    this.outgoingCallsConnected = outgoingCallsConnected;
                }

                public long getOutgoingCallsFailed() {
                    return outgoingCallsFailed;
                }

                public void setOutgoingCallsFailed(long outgoingCallsFailed) {
                    this.outgoingCallsFailed = outgoingCallsFailed;
                }

                public long getOutgoingCallsDropped() {
                    return outgoingCallsDropped;
                }

                public void setOutgoingCallsDropped(long outgoingCallsDropped) {
                    this.outgoingCallsDropped = outgoingCallsDropped;
                }

                public long getOutgoingCallsTotalCallTime() {
                    return outgoingCallsTotalCallTime;
                }

                public void setOutgoingCallsTotalCallTime(long outgoingCallsTotalCallTime) {
                    this.outgoingCallsTotalCallTime = outgoingCallsTotalCallTime;
                }

                public long getPacketsReceived() {
                    return packetsReceived;
                }

                public void setPacketsReceived(long packetsReceived) {
                    this.packetsReceived = packetsReceived;
                }

                public long getPacketsSent() {
                    return packetsSent;
                }

                public void setPacketsSent(long packetsSent) {
                    this.packetsSent = packetsSent;
                }

                public long getPacketsLost() {
                    return packetsLost;
                }

                public void setPacketsLost(long packetsLost) {
                    this.packetsLost = packetsLost;
                }

                public long getBytesSent() {
                    return bytesSent;
                }

                public void setBytesSent(long bytesSent) {
                    this.bytesSent = bytesSent;
                }

                public long getBytesReceived() {
                    return bytesReceived;
                }

                public void setBytesReceived(long bytesReceived) {
                    this.bytesReceived = bytesReceived;
                }
            }
        }
    }

    public static class NetworkDto {
        public enum Fields {
            Interfaces,
            DnsServer
        }

        private InterfaceDto[] interfaces;
        private DnsServerDto dnsServer;

        public static class InterfaceDto {
            public enum Fields {
                Upstream,
                Ipv6
            }

            private boolean isUpstream;
            private Ipv6Dto ipv6;

            public static class Ipv6Dto {
                public enum Fields {
                    Enable,
                    Addresses,
                    Protocol,
                    DefaultGateway
                }

                private boolean enabled;
                private List<AddressDto> addresses;
                private String protocol;
                private String defaultGateway;

                public static class AddressDto {
                    public enum Fields {
                        Type,
                        Address,
                        Origin,
                        LifetimeState,
                        Lifetime
                    }

                    private String type;
                    private String address;
                    private String origin;
                    private String lifetimeState;
                    private Long lifetime;

                    public static AddressDto fromBasicDbObject(BasicDBObject dbObj) {
                        AddressDto addressDto = new AddressDto();
                        addressDto.setType(dbObj.getString(Fields.Type.name()));
                        addressDto.setAddress(dbObj.getString(Fields.Address.name()));
                        addressDto.setOrigin(dbObj.getString(Fields.Origin.name()));
                        addressDto.setLifetimeState(dbObj.getString(Fields.LifetimeState.name()));
                        if (dbObj.containsField(Fields.Lifetime.name())) {
                            addressDto.setLifetime(dbObj.getLong(Fields.Lifetime.name()) * 1000);
                        }

                        if(addressDto.getType() == null || addressDto.getType().equals("")) {
                            return null;
                        }

                        return addressDto;
                    }

                    public static List<AddressDto> fromBasicDbList(BasicDBList dbList) {
                        List<AddressDto> result = new ArrayList<>();
                        for (Object dbObj : dbList) {
                            AddressDto addressDto = fromBasicDbObject((BasicDBObject) dbObj);
                            if(addressDto != null) {
                                result.add(addressDto);
                            }
                        }
                        return result;
                    }

                    public String getType() {
                        return type;
                    }

                    public void setType(String type) {
                        this.type = type;
                    }

                    public String getAddress() {
                        return address;
                    }

                    public void setAddress(String address) {
                        this.address = address;
                    }

                    public String getOrigin() {
                        return origin;
                    }

                    public void setOrigin(String origin) {
                        this.origin = origin;
                    }

                    public String getLifetimeState() {
                        return lifetimeState;
                    }

                    public void setLifetimeState(String lifetimeState) {
                        this.lifetimeState = lifetimeState;
                    }

                    public Long getLifetime() {
                        return lifetime;
                    }

                    public void setLifetime(Long lifetime) {
                        this.lifetime = lifetime;
                    }
                }

                public static Ipv6Dto fromBasicDbObject(BasicDBObject dbObj) {
                    Ipv6Dto ipv6Dto = new Ipv6Dto();
                    ipv6Dto.setEnabled(dbObj.getBoolean(Fields.Enable.name()));
                    if (dbObj.containsField(Fields.Addresses.name()) && dbObj.get(Fields.Addresses.name()) != null) {
                        ipv6Dto.setAddresses(AddressDto.fromBasicDbList((BasicDBList) dbObj.get(Fields.Addresses.name())));
                    } else {
                        ipv6Dto.setAddresses(new ArrayList<>());
                    }
                    ipv6Dto.setProtocol(dbObj.getString(Fields.Protocol.name()));
                    ipv6Dto.setDefaultGateway(dbObj.getString(Fields.DefaultGateway.name()));
                    return ipv6Dto;
                }

                public boolean isEnabled() {
                    return enabled;
                }

                public void setEnabled(boolean enabled) {
                    this.enabled = enabled;
                }

                public List<AddressDto> getAddresses() {
                    return addresses;
                }

                public void setAddresses(List<AddressDto> addresses) {
                    this.addresses = addresses;
                }

                public String getProtocol() {
                    return protocol;
                }

                public void setProtocol(String protocol) {
                    this.protocol = protocol;
                }

                public String getDefaultGateway() {
                    return defaultGateway;
                }

                public void setDefaultGateway(String defaultGateway) {
                    this.defaultGateway = defaultGateway;
                }
            }

            public static InterfaceDto fromBasicDbObject(BasicDBObject dbObj) {
                InterfaceDto interfaceDto = new InterfaceDto();
                interfaceDto.setUpstream(dbObj.getBoolean(Fields.Upstream.name()));
                interfaceDto.setIpv6(Ipv6Dto.fromBasicDbObject((BasicDBObject) dbObj.get(Fields.Ipv6.name())));
                return interfaceDto;
            }

            public static InterfaceDto[] fromBasicDbList(BasicDBList dbList) {
                InterfaceDto[] result = new InterfaceDto[dbList.size()];
                int i = 0;
                for (Object dbObj : dbList) {
                    result[i++] = fromBasicDbObject((BasicDBObject) dbObj);
                }

                return result;
            }

            public boolean isUpstream() {
                return isUpstream;
            }

            public void setUpstream(boolean upstream) {
                isUpstream = upstream;
            }

            public Ipv6Dto getIpv6() {
                return ipv6;
            }

            public void setIpv6(Ipv6Dto ipv6) {
                this.ipv6 = ipv6;
            }
        }

        public static class DnsServerDto {
            public enum Fields {
                Ipv6
            }

            private List<Ipv6Dto> ipv6s;

            public static class Ipv6Dto {
                public enum Fields {
                    Address,
                    Type
                }

                private String address;
                private String type;

                public static Ipv6Dto fromBasicDbObject(BasicDBObject dbObj) {
                    Ipv6Dto ipv6Dto = new Ipv6Dto();
                    ipv6Dto.setAddress(dbObj.getString(Fields.Address.name()));
                    ipv6Dto.setType(dbObj.getString(Fields.Type.name()));
                    return ipv6Dto;
                }

                public static List<Ipv6Dto> fromBasicDbList(BasicDBList dbList) {
                    List<Ipv6Dto> result = new ArrayList<>();
                    for (Object dbObj : dbList) {
                        Ipv6Dto ipv6Dto = fromBasicDbObject((BasicDBObject) dbObj);
                        if(ipv6Dto.getType() != null) {
                            result.add(ipv6Dto);
                        }
                    }
                    return result;
                }

                public String getAddress() {
                    return address;
                }

                public void setAddress(String address) {
                    this.address = address;
                }

                public String getType() {
                    return type;
                }

                public void setType(String type) {
                    this.type = type;
                }
            }

            public static DnsServerDto fromBasicDbObject(BasicDBObject dbObj) {
                DnsServerDto dnsServerDto = new DnsServerDto();
                List<Ipv6Dto> ipv6s = Ipv6Dto.fromBasicDbList((BasicDBList) dbObj.get(Fields.Ipv6.name()));
                dnsServerDto.setIpv6s(ipv6s);
                return dnsServerDto;
            }

            public List<Ipv6Dto> getIpv6s() {
                return ipv6s;
            }

            public void setIpv6s(List<Ipv6Dto> ipv6s) {
                this.ipv6s = ipv6s;
            }
        }

        public static NetworkDto fromBasicDbObject(BasicDBObject dbObj) {
            NetworkDto networkDto = new NetworkDto();
            networkDto.setInterfaces(InterfaceDto.fromBasicDbList((BasicDBList) dbObj.get(Fields.Interfaces.name())));
            networkDto.setDnsServer(DnsServerDto.fromBasicDbObject((BasicDBObject) dbObj.get(Fields.DnsServer.name())));
            return networkDto;
        }

        public InterfaceDto[] getInterfaces() {
            return interfaces;
        }

        public void setInterfaces(InterfaceDto[] interfaces) {
            this.interfaces = interfaces;
        }

        public DnsServerDto getDnsServer() {
            return dnsServer;
        }

        public void setDnsServer(DnsServerDto dnsServer) {
            this.dnsServer = dnsServer;
        }
    }

    public static class PonDto {
        public enum PonDtoFields {
            SerialNumber,
            MACAddress,
            FWVersion,
            Status,
            LastChange,
            Temperature,
            TemperatureWarningThreshold,
            TemperatureAlarmThreshold
        }

        private String serialNumber;
        private String macAddress;
        private String fwVersion;
        private String status;
        private Long lastChange;
        private int temperature;
        private int temperatureWarningThreshold;
        private int temperatureAlarmThreshold;

        public static PonDto fromBasicDBOject(BasicDBObject dbObj) {

            PonDto ponDto = new PonDto();

            ponDto.setSerialNumber(dbObj.getString(PonDtoFields.SerialNumber.name()));
            ponDto.setMacAddress(dbObj.getString(PonDtoFields.MACAddress.name()));
            ponDto.setFwVersion(dbObj.getString(PonDtoFields.FWVersion.name()));
            ponDto.setStatus(dbObj.getString(PonDtoFields.Status.name()));
            ponDto.setLastChange(Objects.isNull(dbObj.get(PonDtoFields.LastChange.name())) ? 0 : (dbObj.getLong(PonDtoFields.LastChange.name()) * 1000));
            ponDto.setTemperature(Objects.isNull(dbObj.get(PonDtoFields.Temperature.name())) ? 0 : dbObj.getInt(PonDtoFields.Temperature.name()));
            ponDto.setTemperatureWarningThreshold(Objects.isNull(dbObj.get(PonDtoFields.TemperatureWarningThreshold.name())) ? 0 : dbObj.getInt(PonDtoFields.TemperatureWarningThreshold.name()));
            ponDto.setTemperatureAlarmThreshold(Objects.isNull(dbObj.get(PonDtoFields.TemperatureAlarmThreshold.name())) ? 0 : dbObj.getInt(PonDtoFields.TemperatureAlarmThreshold.name()));

            return ponDto;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getFwVersion() {
            return fwVersion;
        }

        public void setFwVersion(String fwVersion) {
            this.fwVersion = fwVersion;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Long getLastChange() {
            return lastChange;
        }

        public void setLastChange(Long lastChange) {
            this.lastChange = lastChange;
        }

        public int getTemperature() {
            return temperature;
        }

        public void setTemperature(int temperature) {
            this.temperature = temperature;
        }

        public int getTemperatureWarningThreshold() {
            return temperatureWarningThreshold;
        }

        public void setTemperatureWarningThreshold(int temperatureWarningThreshold) {
            this.temperatureWarningThreshold = temperatureWarningThreshold;
        }

        public int getTemperatureAlarmThreshold() {
            return temperatureAlarmThreshold;
        }

        public void setTemperatureAlarmThreshold(int temperatureAlarmThreshold) {
            this.temperatureAlarmThreshold = temperatureAlarmThreshold;
        }
    }

    public static class WanDto {
        public enum WanDtoFields {
            devIpAddress,
            leaseTimeRemaining,
            phyTransmitRate,
            phyReceiveRate,
            gateway,
            dhcp,
            dns1,
            dns2,
            status,
            macAddress,
            phyType,
            lastChange
        }

        private String devIpAddress;
        private Long leaseTimeRemaining;
        private String gateway;
        private Long phyTransmitRate;
        private Long phyReceiveRate;
        private String dhcp;
        private String dns1;
        private String dns2;
        private String status;
        private String macAddress;
        private String phyType;
        private Long lastChange;


        public static WanDto fromBasicDBOject(BasicDBObject wanDetails) {
            WanDto wanDto = new WanDto();
            wanDto.setDevIpAddress(Objects.isNull(wanDetails.get("devIpAddress")) ? null : wanDetails.get("devIpAddress").toString());
            wanDto.setLeaseTimeRemaining(Objects.isNull(wanDetails.get("leaseTimeRemaining")) ? 0 : Long.valueOf(wanDetails.get("leaseTimeRemaining").toString()));
            wanDto.setGateway(Objects.isNull(wanDetails.get("gateway")) ? null : wanDetails.get("gateway").toString());
            wanDto.setPhyTransmitRate(Objects.isNull(wanDetails.get("phyTransmitRate")) ? 0 : Long.valueOf(wanDetails.get("phyTransmitRate").toString()));
            wanDto.setPhyReceiveRate(Objects.isNull(wanDetails.get("phyReceiveRate")) ? 0 : Long.valueOf(wanDetails.get("phyReceiveRate").toString()));
            wanDto.setDhcp(Objects.isNull(wanDetails.get("dhcp")) ? null : wanDetails.get("dhcp").toString());
            wanDto.setDns1(Objects.isNull(wanDetails.get("dns1")) ? null : wanDetails.get("dns1").toString());
            wanDto.setDns2(Objects.isNull(wanDetails.get("dns2")) ? null : wanDetails.get("dns2").toString());
            wanDto.setStatus(Objects.isNull(wanDetails.get("status")) ? null : wanDetails.get("status").toString());
            wanDto.setMacAddress(Objects.isNull(wanDetails.get("macAddress")) ? null : wanDetails.get("macAddress").toString());
            wanDto.setPhyType(Objects.isNull(wanDetails.get("phyType")) ? null : wanDetails.get("phyType").toString());
            wanDto.setLastChange(Objects.isNull(wanDetails.get("lastChange")) ? 0 : Long.valueOf(wanDetails.get("lastChange").toString())*1000L);

            return wanDto;
        }

        public String getDevIpAddress() {
            return devIpAddress;
        }

        public void setDevIpAddress(String devIpAddress) {
            this.devIpAddress = devIpAddress;
        }

        public Long getLeaseTimeRemaining() {
            return leaseTimeRemaining;
        }

        public void setLeaseTimeRemaining(Long leaseTimeRemaining) {
            this.leaseTimeRemaining = leaseTimeRemaining;
        }

        public String getGateway() {
            return gateway;
        }

        public void setGateway(String gateway) {
            this.gateway = gateway;
        }

        public Long getPhyTransmitRate() {
            return phyTransmitRate;
        }

        public void setPhyTransmitRate(Long phyTransmitRate) {
            this.phyTransmitRate = phyTransmitRate;
        }

        public Long getPhyReceiveRate() {
            return phyReceiveRate;
        }

        public void setPhyReceiveRate(Long phyReceiveRate) {
            this.phyReceiveRate = phyReceiveRate;
        }

        public String getDhcp() {
            return dhcp;
        }

        public void setDhcp(String dhcp) {
            this.dhcp = dhcp;
        }

        public String getDns1() {
            return dns1;
        }

        public void setDns1(String dns1) {
            this.dns1 = dns1;
        }

        public String getDns2() {
            return dns2;
        }

        public void setDns2(String dns2) {
            this.dns2 = dns2;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getPhyType() {
            return phyType;
        }

        public void setPhyType(String phyType) {
            this.phyType = phyType;
        }

        public Long getLastChange() {
            return lastChange;
        }

        public void setLastChange(Long lastChange) {
            this.lastChange = lastChange;
        }
    }
}
