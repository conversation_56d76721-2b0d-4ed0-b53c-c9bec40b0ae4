package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

public class WifiStationDto {

    public static final String COLLECTION = "stationDetail";
    public enum Fields {
        userId,
        serialNumber,
        macAddress,
        operatingStandards,
        rssi,
        bytesSent,
        bytesReceived,
        avgBytesSent,
        avgBytesReceived,
        errorsSent,
        retransSent,
        lastDataDownlinkRate,
        lastDataUplinkRate,
        airTimePercentage,
        bssid,
        radioKey,
        ssidKey,
        dualBandSupported,
        dfsSupported,
        ieee80211kSupported,
        ieee80211vSupported,
        ieee80211rSupported,
        sendingTime,
        timestamp,
        ntTimestamp,
        friendlyName
    }

    private String userId;
    private String serialNumber;
    private String macAddress;
    private String operatingStandards;
    private long rssi;
    private long bytesSent;
    private long bytesReceived;
    private double avgBytesSent;
    private double avgBytesReceived;
    private long errorsSent;
    private long retransSent;
    private long lastDataDownlinkRate;
    private long lastDataUplinkRate;
    private double airTimePercentage;
    private String bssid;
    private String radioKey;
    private String ssidKey;
    private boolean dualBandSupported;
    private boolean dfsSupported;
    private boolean ieee80211kSupported;
    private boolean ieee80211vSupported;
    private boolean ieee80211rSupported;
    private long sendingTime;
    private long timestamp;
    private long ntTimestamp;
    private String friendlyName;

    public static WifiStationDto fromBasicDbObject(BasicDBObject dbObj) {
        WifiStationDto wifiStationDto = new WifiStationDto();

        wifiStationDto.setUserId(dbObj.getString(WifiStationDto.Fields.userId.name()));
        wifiStationDto.setSerialNumber(dbObj.getString(WifiStationDto.Fields.serialNumber.name()));
        wifiStationDto.setMacAddress(dbObj.getString(WifiStationDto.Fields.macAddress.name()));
        wifiStationDto.setOperatingStandards(dbObj.getString(Fields.operatingStandards.name()));
        wifiStationDto.setRssi(dbObj.getLong(WifiStationDto.Fields.rssi.name(), 0));
        wifiStationDto.setBytesSent(dbObj.getLong(Fields.bytesSent.name(), 0));
        wifiStationDto.setBytesReceived(dbObj.getLong(Fields.bytesReceived.name(), 0));
        wifiStationDto.setAvgBytesSent(dbObj.getDouble(Fields.avgBytesSent.name(), 0));
        wifiStationDto.setAvgBytesReceived(dbObj.getDouble(Fields.avgBytesReceived.name(), 0));
        wifiStationDto.setErrorsSent(dbObj.getLong(Fields.errorsSent.name(), 0));
        wifiStationDto.setRetransSent(dbObj.getLong(Fields.retransSent.name(), 0));
        wifiStationDto.setLastDataDownlinkRate(dbObj.getLong(Fields.lastDataDownlinkRate.name(), 0));
        wifiStationDto.setLastDataUplinkRate(dbObj.getLong(Fields.lastDataUplinkRate.name(), 0));
        wifiStationDto.setAirTimePercentage(dbObj.getDouble(Fields.airTimePercentage.name(), 0));
        wifiStationDto.setBssid(dbObj.getString(WifiStationDto.Fields.bssid.name()));
        wifiStationDto.setRadioKey(dbObj.getString(WifiStationDto.Fields.radioKey.name()));
        wifiStationDto.setSsidKey(dbObj.getString(WifiStationDto.Fields.ssidKey.name()));
        wifiStationDto.setDualBandSupported(dbObj.getBoolean(Fields.dualBandSupported.name(), false));
        wifiStationDto.setDfsSupported(dbObj.getBoolean(Fields.dfsSupported.name(), false));
        wifiStationDto.setIeee80211kSupported(dbObj.getBoolean(Fields.ieee80211kSupported.name(), false));
        wifiStationDto.setIeee80211vSupported(dbObj.getBoolean(Fields.ieee80211vSupported.name(), false));
        wifiStationDto.setIeee80211rSupported(dbObj.getBoolean(Fields.ieee80211rSupported.name(), false));
        wifiStationDto.setSendingTime(dbObj.getLong(WifiStationDto.Fields.sendingTime.name(), 0));
        wifiStationDto.setTimestamp(dbObj.getLong(WifiStationDto.Fields.timestamp.name(), 0));
        wifiStationDto.setNtTimestamp(dbObj.getLong(Fields.ntTimestamp.name(), 0));
        wifiStationDto.setFriendlyName(dbObj.getString(Fields.friendlyName.name()));

        return wifiStationDto;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getOperatingStandards() {
        return operatingStandards;
    }

    public void setOperatingStandards(String operatingStandards) {
        this.operatingStandards = operatingStandards;
    }

    public long getRssi() {
        return rssi;
    }

    public void setRssi(long rssi) {
        this.rssi = rssi;
    }

    public long getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(long bytesSent) {
        this.bytesSent = bytesSent;
    }

    public long getBytesReceived() {
        return bytesReceived;
    }

    public void setBytesReceived(long bytesReceived) {
        this.bytesReceived = bytesReceived;
    }

    public double getAvgBytesSent() {
        return avgBytesSent;
    }

    public void setAvgBytesSent(double avgBytesSent) {
        this.avgBytesSent = avgBytesSent;
    }

    public double getAvgBytesReceived() {
        return avgBytesReceived;
    }

    public void setAvgBytesReceived(double avgBytesReceived) {
        this.avgBytesReceived = avgBytesReceived;
    }

    public long getErrorsSent() {
        return errorsSent;
    }

    public void setErrorsSent(long errorsSent) {
        this.errorsSent = errorsSent;
    }

    public long getRetransSent() {
        return retransSent;
    }

    public void setRetransSent(long retransSent) {
        this.retransSent = retransSent;
    }

    public long getLastDataDownlinkRate() {
        return lastDataDownlinkRate;
    }

    public void setLastDataDownlinkRate(long lastDataDownlinkRate) {
        this.lastDataDownlinkRate = lastDataDownlinkRate;
    }

    public long getLastDataUplinkRate() {
        return lastDataUplinkRate;
    }

    public void setLastDataUplinkRate(long lastDataUplinkRate) {
        this.lastDataUplinkRate = lastDataUplinkRate;
    }

    public double getAirTimePercentage() {
        return airTimePercentage;
    }

    public void setAirTimePercentage(double airTimePercentage) {
        this.airTimePercentage = airTimePercentage;
    }

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }

    public String getRadioKey() {
        return radioKey;
    }

    public void setRadioKey(String radioKey) {
        this.radioKey = radioKey;
    }

    public String getSsidKey() {
        return ssidKey;
    }

    public void setSsidKey(String ssidKey) {
        this.ssidKey = ssidKey;
    }

    public boolean isDualBandSupported() {
        return dualBandSupported;
    }

    public void setDualBandSupported(boolean dualBandSupported) {
        this.dualBandSupported = dualBandSupported;
    }

    public boolean isDfsSupported() {
        return dfsSupported;
    }

    public void setDfsSupported(boolean dfsSupported) {
        this.dfsSupported = dfsSupported;
    }

    public boolean isIeee80211kSupported() {
        return ieee80211kSupported;
    }

    public void setIeee80211kSupported(boolean ieee80211kSupported) {
        this.ieee80211kSupported = ieee80211kSupported;
    }

    public boolean isIeee80211vSupported() {
        return ieee80211vSupported;
    }

    public void setIeee80211vSupported(boolean ieee80211vSupported) {
        this.ieee80211vSupported = ieee80211vSupported;
    }

    public boolean isIeee80211rSupported() {
        return ieee80211rSupported;
    }

    public void setIeee80211rSupported(boolean ieee80211rSupported) {
        this.ieee80211rSupported = ieee80211rSupported;
    }

    public long getSendingTime() {
        return sendingTime;
    }

    public void setSendingTime(long sendingTime) {
        this.sendingTime = sendingTime;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getNtTimestamp() {
        return ntTimestamp;
    }

    public void setNtTimestamp(long ntTimestamp) {
        this.ntTimestamp = ntTimestamp;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }
}
