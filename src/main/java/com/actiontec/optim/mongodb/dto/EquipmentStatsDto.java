package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

public class EquipmentStatsDto {
    public static final String COLLECTION = "equipmentStats";
    public static final String STATS = "stats";
    public enum Fields {
        timestamp,
        userId,
        serialNumber,
        cpuUsage,
        memUsage
    }

    private Long timestamp;
    private String userId;
    private String serialNumber;
    private Long cpuUsage;
    private Long memUsage;

    public static EquipmentStatsDto fromBasicDbObject(BasicDBObject dbObj) {
        EquipmentStatsDto equipmentStatsDto = new EquipmentStatsDto();

        equipmentStatsDto.setUserId(dbObj.getString(Fields.userId.name()));
        equipmentStatsDto.setSerialNumber(dbObj.getString(Fields.serialNumber.name()));

        BasicDBObject statsObj = (BasicDBObject) dbObj.get(STATS);
        equipmentStatsDto.setTimestamp(statsObj.getLong(Fields.timestamp.name(), 0L));
        equipmentStatsDto.setCpuUsage(statsObj.getLong(Fields.cpuUsage.name()));
        equipmentStatsDto.setMemUsage(statsObj.getLong(Fields.memUsage.name()));
        return equipmentStatsDto;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Long getCpuUsage() {
        return cpuUsage;
    }

    public void setCpuUsage(Long cpuUsage) {
        this.cpuUsage = cpuUsage;
    }

    public Long getMemUsage() {
        return memUsage;
    }

    public void setMemUsage(Long memUsage) {
        this.memUsage = memUsage;
    }
}
