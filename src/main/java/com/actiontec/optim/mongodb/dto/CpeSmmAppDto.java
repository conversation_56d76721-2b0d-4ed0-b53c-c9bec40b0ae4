package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class CpeSmmAppDto {

    public static final String COLLECTION_cpeSmmApp = "cpeSmmApp";

    public enum CpeSmmAppFields {
        isp,
        serviceTelephoneNumber,
        userId,
        serialNumber,
        serviceId,
        serviceVer,
        applicationId,
        desiredState,
        actualState,
        actionState,
        jobId,
        actionId,
        attemptCount,
        actualServiceState,
        rptModel,
        rptFwVer,
        rptApplicationId,
        rptStatus,
        rptAutoStart,
        rptProvisionStatus,
        rptVersion,
        rptActionId,
        system,
        forbiddenOperations,
        sendingTime,
        receivedTime,
        ntTimestamp,
        lastRpcTime
    }

    private String isp;
    private String serviceTelephoneNumber;
    private String userId;
    private String serialNumber;
    private String serviceId;
    private String serviceVer;
    private String applicationId;
    private String desiredState;
    private String actualState;
    private String actionState;
    private String actionId;
    private String jobId;
    private int attemptCount;
    private String actualServiceState;
    private String rptModel;
    private String rptFwVer;
    private String rptApplicationId;
    private String rptStatus;
    private boolean rptAutoStart;
    private String rptProvisionStatus;
    private String rptVersion;
    private String rptActionId;
    private SystemDto system;
    private List<String> forbiddenOperations;
    private long sendingTime;
    private long receivedTime;
    private long ntTimestamp;
    private long lastRpcTime;

    public static List<String> getForbiddenOperations(BasicDBList dbList) {
        List<String> forbiddenOperationList = new ArrayList<>();
        for(Object dbObj : dbList) {
            forbiddenOperationList.add(String.valueOf(dbObj));
        }
        return forbiddenOperationList;
    }

    public static CpeSmmAppDto fromBasicDbObject(BasicDBObject dbObj) {
        CpeSmmAppDto cpeSmmAppDto = new CpeSmmAppDto();
        cpeSmmAppDto.setIsp(dbObj.getString(CpeSmmAppFields.isp.name(), ""));
        cpeSmmAppDto.setServiceTelephoneNumber(dbObj.getString(CpeSmmAppFields.serviceTelephoneNumber.name(), ""));
        cpeSmmAppDto.setUserId(dbObj.getString(CpeSmmAppFields.userId.name(), ""));
        cpeSmmAppDto.setSerialNumber(dbObj.getString(CpeSmmAppFields.serialNumber.name(), ""));
        cpeSmmAppDto.setServiceId(dbObj.getString(CpeSmmAppFields.serviceId.name(), ""));
        cpeSmmAppDto.setServiceVer(dbObj.getString(CpeSmmAppFields.serviceVer.name(), ""));
        cpeSmmAppDto.setApplicationId(dbObj.getString(CpeSmmAppFields.applicationId.name(), ""));
        cpeSmmAppDto.setDesiredState(dbObj.getString(CpeSmmAppFields.desiredState.name(), ""));
        cpeSmmAppDto.setActualState(dbObj.getString(CpeSmmAppFields.actualState.name(), ""));
        cpeSmmAppDto.setActionState(dbObj.getString(CpeSmmAppFields.actionState.name(), ""));
        cpeSmmAppDto.setJobId(dbObj.getString(CpeSmmAppFields.jobId.name(), ""));
        cpeSmmAppDto.setActionId(dbObj.getString(CpeSmmAppFields.actionId.name(), ""));
        cpeSmmAppDto.setAttemptCount(dbObj.getInt(CpeSmmAppFields.attemptCount.name(), 0));
        cpeSmmAppDto.setActualServiceState(dbObj.getString(CpeSmmAppFields.actualServiceState.name(), ""));
        cpeSmmAppDto.setRptModel(dbObj.getString(CpeSmmAppFields.rptModel.name(), ""));
        cpeSmmAppDto.setRptFwVer(dbObj.getString(CpeSmmAppFields.rptFwVer.name(), ""));
        cpeSmmAppDto.setRptApplicationId(dbObj.getString(CpeSmmAppFields.rptApplicationId.name(), ""));
        cpeSmmAppDto.setRptStatus(dbObj.getString(CpeSmmAppFields.rptStatus.name(), ""));
        cpeSmmAppDto.setRptAutoStart(dbObj.getBoolean(CpeSmmAppFields.rptAutoStart.name(), false));
        cpeSmmAppDto.setRptProvisionStatus(dbObj.getString(CpeSmmAppFields.rptProvisionStatus.name(), ""));
        cpeSmmAppDto.setRptVersion(dbObj.getString(CpeSmmAppFields.rptVersion.name(), ""));
        cpeSmmAppDto.setRptActionId(dbObj.getString(CpeSmmAppFields.rptActionId.name(), ""));
        if (Objects.nonNull(dbObj.get(CpeSmmAppFields.system.name())))
            cpeSmmAppDto.setSystem(SystemDto.fromBasicDbObject((BasicDBObject) dbObj.get(CpeSmmAppFields.system.name())));
        if (Objects.nonNull(dbObj.get(CpeSmmAppFields.forbiddenOperations.name())))
            cpeSmmAppDto.setForbiddenOperations(getForbiddenOperations((BasicDBList) dbObj.get(CpeSmmAppFields.forbiddenOperations.name())));
        cpeSmmAppDto.setSendingTime(dbObj.getLong(CpeSmmAppFields.sendingTime.name(), 0));
        cpeSmmAppDto.setReceivedTime(dbObj.getLong(CpeSmmAppFields.receivedTime.name(), 0));
        cpeSmmAppDto.setNtTimestamp(dbObj.getLong(CpeSmmAppFields.ntTimestamp.name(), 0));
        cpeSmmAppDto.setLastRpcTime(dbObj.getLong(CpeSmmAppFields.lastRpcTime.name(), 0));

        return cpeSmmAppDto;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getServiceTelephoneNumber() {
        return serviceTelephoneNumber;
    }

    public void setServiceTelephoneNumber(String serviceTelephoneNumber) {
        this.serviceTelephoneNumber = serviceTelephoneNumber;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getServiceVer() {
        return serviceVer;
    }

    public void setServiceVer(String serviceVer) {
        this.serviceVer = serviceVer;
    }

    public String getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId;
    }

    public String getDesiredState() {
        return desiredState;
    }

    public void setDesiredState(String desiredState) {
        this.desiredState = desiredState;
    }

    public String getActualState() {
        return actualState;
    }

    public void setActualState(String actualState) {
        this.actualState = actualState;
    }

    public String getActionState() {
        return actionState;
    }

    public void setActionState(String actionState) {
        this.actionState = actionState;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getActionId() {
        return actionId;
    }

    public void setActionId(String actionId) {
        this.actionId = actionId;
    }

    public int getAttemptCount() {
        return attemptCount;
    }

    public void setAttemptCount(int attemptCount) {
        this.attemptCount = attemptCount;
    }

    public String getActualServiceState() {
        return actualServiceState;
    }

    public void setActualServiceState(String actualServiceState) {
        this.actualServiceState = actualServiceState;
    }

    public String getRptModel() {
        return rptModel;
    }

    public void setRptModel(String rptModel) {
        this.rptModel = rptModel;
    }

    public String getRptFwVer() {
        return rptFwVer;
    }

    public void setRptFwVer(String rptFwVer) {
        this.rptFwVer = rptFwVer;
    }

    public String getRptApplicationId() {
        return rptApplicationId;
    }

    public void setRptApplicationId(String rptApplicationId) {
        this.rptApplicationId = rptApplicationId;
    }

    public String getRptStatus() {
        return rptStatus;
    }

    public void setRptStatus(String rptStatus) {
        this.rptStatus = rptStatus;
    }

    public boolean getRptAutoStart() {
        return rptAutoStart;
    }

    public void setRptAutoStart(boolean rptAutoStart) {
        this.rptAutoStart = rptAutoStart;
    }

    public String getRptProvisionStatus() {
        return rptProvisionStatus;
    }

    public void setRptProvisionStatus(String rptProvisionStatus) {
        this.rptProvisionStatus = rptProvisionStatus;
    }

    public String getRptVersion() {
        return rptVersion;
    }

    public void setRptVersion(String rptVersion) {
        this.rptVersion = rptVersion;
    }

    public String getRptActionId() {
        return rptActionId;
    }

    public void setRptActionId(String rptActionId) {
        this.rptActionId = rptActionId;
    }

    public SystemDto getSystem() {
        return system;
    }

    public void setSystem(SystemDto system) {
        this.system = system;
    }

    public List<String> getForbiddenOperations() {
        return forbiddenOperations;
    }

    public void setForbiddenOperations(List<String> forbiddenOperations) {
        this.forbiddenOperations = forbiddenOperations;
    }

    public long getSendingTime() {
        return sendingTime;
    }

    public void setSendingTime(long sendingTime) {
        this.sendingTime = sendingTime;
    }

    public long getReceivedTime() {
        return receivedTime;
    }

    public void setReceivedTime(long receivedTime) {
        this.receivedTime = receivedTime;
    }

    public long getNtTimestamp() {
        return ntTimestamp;
    }

    public void setNtTimestamp(long ntTimestamp) {
        this.ntTimestamp = ntTimestamp;
    }

    public long getLastRpcTime() {
        return lastRpcTime;
    }

    public void setLastRpcTime(long lastRpcTime) {
        this.lastRpcTime = lastRpcTime;
    }

    public static class SystemDto {

        public enum SystemFields {
            cpuUsage,
            memoryUsed,
            fsUsed
        }

        private double cpuUsage;
        private long memoryUsed;
        private List<FsUsedDto> fsUsed;

        public static SystemDto fromBasicDbObject(BasicDBObject dbObj) {
            SystemDto systemDto = new SystemDto();
            systemDto.setCpuUsage(dbObj.getDouble(SystemFields.cpuUsage.name(), 0));
            systemDto.setMemoryUsed(dbObj.getLong(SystemFields.memoryUsed.name(), 0));
            systemDto.setFsUsed(FsUsedDto.fromBasicDbList((BasicDBList) dbObj.get(SystemFields.fsUsed.name())));
            return systemDto;
        }

        public double getCpuUsage() {
            return cpuUsage;
        }

        public void setCpuUsage(double cpuUsage) {
            this.cpuUsage = cpuUsage;
        }

        public long getMemoryUsed() {
            return memoryUsed;
        }

        public void setMemoryUsed(long memoryUsed) {
            this.memoryUsed = memoryUsed;
        }

        public List<FsUsedDto> getFsUsed() {
            return fsUsed;
        }

        public void setFsUsed(List<FsUsedDto> fsUsed) {
            this.fsUsed = fsUsed;
        }
    }
}
