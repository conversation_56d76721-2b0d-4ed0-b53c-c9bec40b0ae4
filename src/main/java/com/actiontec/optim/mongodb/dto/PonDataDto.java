package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

public class PonDataDto {
    public enum PonDataFields {
        status,
        temperature,
        serialNumber,
        timestamp
    }

    private String status;
    private int temperature;
    private String serialNumber;
    private Long timestamp;

    public static PonDataDto fromBasicDbObject(BasicDBObject dbObj) {

        PonDataDto ponDataDto = new PonDataDto();

        ponDataDto.setStatus(dbObj.getString(PonDataFields.status.name()));
        ponDataDto.setTemperature(dbObj.getInt(PonDataFields.temperature.name()));
        ponDataDto.setSerialNumber(dbObj.getString(PonDataFields.serialNumber.name()));
        ponDataDto.setTimestamp(dbObj.getLong(PonDataFields.timestamp.name()));

        return ponDataDto;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getTemperature() {
        return temperature;
    }

    public void setTemperature(int temperature) {
        this.temperature = temperature;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
}
