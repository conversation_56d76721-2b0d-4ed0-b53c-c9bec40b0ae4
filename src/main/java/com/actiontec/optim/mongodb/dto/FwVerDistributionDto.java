package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

public class FwVerDistributionDto {
    private String isp;
    private String modelName;
    private String fwVersion;
    private int onlineUnits;
    private int offlineUnits;

    public static FwVerDistributionDto fromBasicDbObject(BasicDBObject dbObj) {
        FwVerDistributionDto fwVerDistributionDto = new FwVerDistributionDto();
        // FIXME: not hard-coded field name
        fwVerDistributionDto.setIsp(dbObj.getString("isp"));
        fwVerDistributionDto.setModelName(dbObj.getString("modelName"));
        fwVerDistributionDto.setFwVersion(dbObj.getString("fwVersion"));
        fwVerDistributionDto.setOfflineUnits(dbObj.getInt("offlineUnits"));
        fwVerDistributionDto.setOnlineUnits(dbObj.getInt("onlineUnits"));
        return fwVerDistributionDto;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getFwVersion() {
        return fwVersion;
    }

    public void setFwVersion(String fwVersion) {
        this.fwVersion = fwVersion;
    }

    public int getOnlineUnits() {
        return onlineUnits;
    }

    public void setOnlineUnits(int onlineUnits) {
        this.onlineUnits = onlineUnits;
    }

    public int getOfflineUnits() {
        return offlineUnits;
    }

    public void setOfflineUnits(int offlineUnits) {
        this.offlineUnits = offlineUnits;
    }
}
