package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;

import java.util.ArrayList;

public class OauthConfigDto {
    public static final String COLLECTION = "oAuthConfig";

    public enum Fields {
        id,
        name,
        provider,
        clientId,
        clientSecret,
        redirectUri,
        responseType,
        scope,
        openIdSupported,
        wellKnownConfig,
        isDefaultProvider,
        endpoints,
        extraClientData,
        ispIds,
        defaultIspId,
        roleId
    }

    private String id;
    private String name;
    private String provider;
    private String clientId;
    private String clientSecret;
    private String redirectUri;
    private String responseType;
    private String scope;
    private Boolean openIdSupported;
    private String wellKnownConfig;
    private Boolean isDefaultProvider;
    private OauthConfigEndpointDto endpoints;
    private OauthConfigExtraClientDataDto extraClientData;
    private ArrayList<String> ispIds;
    private String defaultIspId;
    private String roleId;

    public static OauthConfigDto fromBasicDbObject(BasicDBObject dbObj) {
        OauthConfigDto oauthConfigDto = new OauthConfigDto();
        oauthConfigDto.setId(dbObj.getString("_id"));
        oauthConfigDto.setName(dbObj.getString(Fields.name.name()));
        oauthConfigDto.setProvider(dbObj.getString(Fields.provider.name()));
        oauthConfigDto.setClientId(dbObj.getString(Fields.clientId.name()));
        oauthConfigDto.setClientSecret(dbObj.getString(Fields.clientSecret.name()));
        oauthConfigDto.setRedirectUri(dbObj.getString(Fields.redirectUri.name()));
        oauthConfigDto.setResponseType(dbObj.getString(Fields.responseType.name()));
        oauthConfigDto.setScope(dbObj.getString(Fields.scope.name()));
        oauthConfigDto.setOpenIdSupported(dbObj.getBoolean(Fields.openIdSupported.name()));
        oauthConfigDto.setWellKnownConfig(dbObj.getString(Fields.wellKnownConfig.name()));
        oauthConfigDto.setIsDefaultProvider(dbObj.getBoolean(Fields.isDefaultProvider.name()));
        oauthConfigDto.setEndpoints(OauthConfigEndpointDto.fromBasicDbObject((BasicDBObject) dbObj.get(Fields.endpoints.name())));
        oauthConfigDto.setExtraClientData(OauthConfigExtraClientDataDto.fromBasicDbObject((BasicDBObject) dbObj.get(Fields.extraClientData.name())));
        oauthConfigDto.setIspIds(fromBasicDbList((BasicDBList) dbObj.get(Fields.ispIds.name())));
        oauthConfigDto.setDefaultIspId(dbObj.getString(Fields.defaultIspId.name()));
        oauthConfigDto.setRoleId(dbObj.getString(Fields.roleId.name()));
        return oauthConfigDto;
    }

    public static ArrayList<String> fromBasicDbList(BasicDBList dbList) {
        ArrayList<String> result = new ArrayList<>();
        if (dbList == null || dbList.isEmpty())
            return result;
        for (Object dbObj : dbList) {
            result.add(dbObj.toString());
        }
        return result;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getResponseType() {
        return responseType;
    }

    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public Boolean getOpenIdSupported() {
        return openIdSupported;
    }

    public void setOpenIdSupported(Boolean openIdSupported) {
        this.openIdSupported = openIdSupported;
    }

    public String getWellKnownConfig() {
        return wellKnownConfig;
    }

    public void setWellKnownConfig(String wellKnownConfig) {
        this.wellKnownConfig = wellKnownConfig;
    }

    public Boolean getIsDefaultProvider() {
        return isDefaultProvider;
    }

    public void setIsDefaultProvider(Boolean defaultProvider) {
        isDefaultProvider = defaultProvider;
    }

    public OauthConfigEndpointDto getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(OauthConfigEndpointDto endpoints) {
        this.endpoints = endpoints;
    }

    public OauthConfigExtraClientDataDto getExtraClientData() {
        return extraClientData;
    }

    public void setExtraClientData(OauthConfigExtraClientDataDto extraClientData) {
        this.extraClientData = extraClientData;
    }

    public Boolean getDefaultProvider() {
        return isDefaultProvider;
    }

    public void setDefaultProvider(Boolean defaultProvider) {
        isDefaultProvider = defaultProvider;
    }

    public ArrayList<String> getIspIds() {
        return ispIds;
    }

    public void setIspIds(ArrayList<String> ispIds) {
        this.ispIds = ispIds;
    }

    public String getDefaultIspId() {
        return defaultIspId;
    }

    public void setDefaultIspId(String defaultIspId) {
        this.defaultIspId = defaultIspId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
