package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;

import java.util.ArrayList;
import java.util.List;

public class VoipDataDto {
    public enum VoipDataFields {
        timestamp,
        status,
        lines
    }

    private Long timestamp;
    private String status;
    private List<LineDto> lines;

    public static VoipDataDto fromBasicDbObject(BasicDBObject dbObj) {

        VoipDataDto voipDataDto = new VoipDataDto();

        voipDataDto.setTimestamp(dbObj.getLong(VoipDataFields.timestamp.name()));
        voipDataDto.setStatus(dbObj.getString(VoipDataFields.status.name()));
        voipDataDto.setLines(LineDto.fromBasicDBList((BasicDBList)dbObj.get(VoipDataFields.lines.name())));

        return voipDataDto;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public List<LineDto> getLines() {
        return lines;
    }

    public void setLines(List<LineDto> lines) {
        this.lines = lines;
    }

    public static class LineDto {
        public enum LineDtoFields {
            lineNumber,
            incomingCallsReceived,
            incomingCallsConnected,
            incomingCallsFailed,
            incomingCallsDropped,
            incomingCallsTotalCallTime,
            outgoingCallsAttempted,
            outgoingCallsConnected,
            outgoingCallsFailed,
            outgoingCallsDropped,
            outgoingCallsTotalCallTime,
            packetsReceived,
            packetsSent,
            packetsLost,
            bytesSent,
            bytesReceived
        }

        private int lineNumber;
        private long incomingCallsReceived;
        private long incomingCallsConnected;
        private long incomingCallsFailed;
        private long incomingCallsDropped;
        private long incomingCallsTotalCallTime;
        private long outgoingCallsAttempted;
        private long outgoingCallsConnected;
        private long outgoingCallsFailed;
        private long outgoingCallsDropped;
        private long outgoingCallsTotalCallTime;
        private long packetsReceived;
        private long packetsSent;
        private long packetsLost;
        private long bytesSent;
        private long bytesReceived;

        public static LineDto fromBasicDbObject(BasicDBObject dbObj) {
            LineDto lineDto = new LineDto();

            lineDto.setLineNumber(dbObj.getInt(LineDtoFields.lineNumber.name()));
            lineDto.setIncomingCallsReceived(dbObj.getLong(LineDtoFields.incomingCallsReceived.name()));
            lineDto.setIncomingCallsConnected(dbObj.getLong(LineDtoFields.incomingCallsConnected.name()));
            lineDto.setIncomingCallsFailed(dbObj.getLong(LineDtoFields.incomingCallsFailed.name()));
            lineDto.setIncomingCallsDropped(dbObj.getLong(LineDtoFields.incomingCallsDropped.name()));
            lineDto.setIncomingCallsTotalCallTime(dbObj.getLong(LineDtoFields.incomingCallsTotalCallTime.name()));
            lineDto.setOutgoingCallsAttempted(dbObj.getLong(LineDtoFields.outgoingCallsAttempted.name()));
            lineDto.setOutgoingCallsConnected(dbObj.getLong(LineDtoFields.outgoingCallsConnected.name()));
            lineDto.setOutgoingCallsFailed(dbObj.getLong(LineDtoFields.outgoingCallsFailed.name()));
            lineDto.setOutgoingCallsDropped(dbObj.getLong(LineDtoFields.outgoingCallsDropped.name()));
            lineDto.setOutgoingCallsTotalCallTime(dbObj.getLong(LineDtoFields.outgoingCallsTotalCallTime.name()));
            lineDto.setPacketsReceived(dbObj.getLong(LineDtoFields.packetsReceived.name()));
            lineDto.setPacketsSent(dbObj.getLong(LineDtoFields.packetsSent.name()));
            lineDto.setPacketsLost(dbObj.getLong(LineDtoFields.packetsLost.name()));
            lineDto.setBytesSent(dbObj.getLong(LineDtoFields.bytesSent.name()));
            lineDto.setBytesReceived(dbObj.getLong(LineDtoFields.bytesReceived.name()));

            return lineDto;
        }

        public static List<LineDto> fromBasicDBList(BasicDBList dbList) {

            List<LineDto> lineDtoList = new ArrayList<>();
            for (Object line : dbList) {
                LineDto lineDto = fromBasicDbObject((BasicDBObject) line);
                lineDtoList.add(lineDto);
            }

            return lineDtoList;
        }

        public int getLineNumber() {
            return lineNumber;
        }

        public void setLineNumber(int lineNumber) {
            this.lineNumber = lineNumber;
        }

        public long getIncomingCallsReceived() {
            return incomingCallsReceived;
        }

        public void setIncomingCallsReceived(long incomingCallsReceived) {
            this.incomingCallsReceived = incomingCallsReceived;
        }

        public long getIncomingCallsConnected() {
            return incomingCallsConnected;
        }

        public void setIncomingCallsConnected(long incomingCallsConnected) {
            this.incomingCallsConnected = incomingCallsConnected;
        }

        public long getIncomingCallsFailed() {
            return incomingCallsFailed;
        }

        public void setIncomingCallsFailed(long incomingCallsFailed) {
            this.incomingCallsFailed = incomingCallsFailed;
        }

        public long getIncomingCallsDropped() {
            return incomingCallsDropped;
        }

        public void setIncomingCallsDropped(long incomingCallsDropped) {
            this.incomingCallsDropped = incomingCallsDropped;
        }

        public long getIncomingCallsTotalCallTime() {
            return incomingCallsTotalCallTime;
        }

        public void setIncomingCallsTotalCallTime(long incomingCallsTotalCallTime) {
            this.incomingCallsTotalCallTime = incomingCallsTotalCallTime;
        }

        public long getOutgoingCallsAttempted() {
            return outgoingCallsAttempted;
        }

        public void setOutgoingCallsAttempted(long outgoingCallsAttempted) {
            this.outgoingCallsAttempted = outgoingCallsAttempted;
        }

        public long getOutgoingCallsConnected() {
            return outgoingCallsConnected;
        }

        public void setOutgoingCallsConnected(long outgoingCallsConnected) {
            this.outgoingCallsConnected = outgoingCallsConnected;
        }

        public long getOutgoingCallsFailed() {
            return outgoingCallsFailed;
        }

        public void setOutgoingCallsFailed(long outgoingCallsFailed) {
            this.outgoingCallsFailed = outgoingCallsFailed;
        }

        public long getOutgoingCallsDropped() {
            return outgoingCallsDropped;
        }

        public void setOutgoingCallsDropped(long outgoingCallsDropped) {
            this.outgoingCallsDropped = outgoingCallsDropped;
        }

        public long getOutgoingCallsTotalCallTime() {
            return outgoingCallsTotalCallTime;
        }

        public void setOutgoingCallsTotalCallTime(long outgoingCallsTotalCallTime) {
            this.outgoingCallsTotalCallTime = outgoingCallsTotalCallTime;
        }

        public long getPacketsReceived() {
            return packetsReceived;
        }

        public void setPacketsReceived(long packetsReceived) {
            this.packetsReceived = packetsReceived;
        }

        public long getPacketsSent() {
            return packetsSent;
        }

        public void setPacketsSent(long packetsSent) {
            this.packetsSent = packetsSent;
        }

        public long getPacketsLost() {
            return packetsLost;
        }

        public void setPacketsLost(long packetsLost) {
            this.packetsLost = packetsLost;
        }

        public long getBytesSent() {
            return bytesSent;
        }

        public void setBytesSent(long bytesSent) {
            this.bytesSent = bytesSent;
        }

        public long getBytesReceived() {
            return bytesReceived;
        }

        public void setBytesReceived(long bytesReceived) {
            this.bytesReceived = bytesReceived;
        }
    }
}
