package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class IspConfigurationDto {
    private static final Logger LOGGER = LogManager.getLogger(IspConfigurationDto.class);
    public static final String COLLECTION_ispConfiguration = "ispConfiguration";

    public enum IspConfigurationFields {
        _id,
        ispId,
        equipmentTypeIds,
        configuration
    }

    private String id;
    private String ispId;
    private List<String> equipmentTypeIds;
    private ConfigurationDto configuration;

    public static List<String> getEquipmentTypeIds(BasicDBList dbList) {
        List<String> equipmentTypeIds = new ArrayList<>();

        for(Object dbObj : dbList) {
            equipmentTypeIds.add(String.valueOf(dbObj));
        }

        return equipmentTypeIds;
    }

    public static IspConfigurationDto fromBasicDbObject(BasicDBObject dbObj) {
        IspConfigurationDto ispConfigurationDto = new IspConfigurationDto();
        ispConfigurationDto.setId(dbObj.getString(IspConfigurationFields._id.name()));
        ispConfigurationDto.setIspId(dbObj.getString(IspConfigurationFields.ispId.name()));
        ispConfigurationDto.setEquipmentTypeIds(getEquipmentTypeIds((BasicDBList) dbObj.get(IspConfigurationFields.equipmentTypeIds.name())));
        ispConfigurationDto.setConfiguration(ConfigurationDto.fromBasicDbObject((BasicDBObject) dbObj.get(IspConfigurationFields.configuration.name())));
        return ispConfigurationDto;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public List<String> getEquipmentTypeIds() {
        return equipmentTypeIds;
    }

    public void setEquipmentTypeIds(List<String> equipmentTypeIds) {
        this.equipmentTypeIds = equipmentTypeIds;
    }

    public ConfigurationDto getConfiguration() {
        return configuration;
    }

    public void setConfiguration(ConfigurationDto configuration) {
        this.configuration = configuration;
    }

    public static class ConfigurationDto {
        public enum ConfigurationFields {
            managementServer
        }

        private ManagementServerDto managementServer;

        public static ConfigurationDto fromBasicDbObject(BasicDBObject dbObj) {
            ConfigurationDto configurationDto = new ConfigurationDto();
            configurationDto.setManagementServer(ManagementServerDto.fromBasicDbObject((BasicDBObject) dbObj.get(ConfigurationFields.managementServer.name())));
            return configurationDto;
        }

        public ManagementServerDto getManagementServer() {
            return managementServer;
        }

        public void setManagementServer(ManagementServerDto managementServer) {
            this.managementServer = managementServer;
        }
    }

    public static class ManagementServerDto {
        public enum ManagementServerFields {
            enabled,
            url,
            username,
            password,
            periodicInformEnabled,
            periodicInformInterval,
            retryInterval,
            connectionRequestUrl,
            connectionRequestUsername,
            connectionRequestPassword
        }

        private Boolean enabled;
        private String url;
        private List<Map> username;
        private List<Map> password;
        private Boolean periodicInformEnabled;
        private Integer periodicInformInterval;
        private Integer retryInterval;
        private String connectionRequestUrl;
        private List<Map> connectionRequestUsername;
        private List<Map> connectionRequestPassword;

        public static List<Map> getMapList(BasicDBList dbList) {
            List<Map> result = new ArrayList<>();
            if (Objects.isNull(dbList))
                return result;

            for (Object dbObj : dbList) {
                Map objMap = ((BasicDBObject) dbObj).toMap();
                result.add(objMap);
            }

            return result;
        }

        public static ManagementServerDto fromBasicDbObject(BasicDBObject dbObj) {
            ManagementServerDto managementServerDto = new ManagementServerDto();
            managementServerDto.setEnabled(dbObj.getBoolean(ManagementServerFields.enabled.name()));
            managementServerDto.setUrl(dbObj.getString(ManagementServerFields.url.name()));
            managementServerDto.setUsername(getMapList((BasicDBList) dbObj.get(ManagementServerFields.username.name())));
            managementServerDto.setPassword(getMapList((BasicDBList) dbObj.get(ManagementServerFields.password.name())));
            managementServerDto.setPeriodicInformEnabled(dbObj.getBoolean(ManagementServerFields.periodicInformEnabled.name()));
            managementServerDto.setPeriodicInformInterval(dbObj.getInt(ManagementServerFields.periodicInformInterval.name()));
            managementServerDto.setRetryInterval(dbObj.getInt(ManagementServerFields.retryInterval.name()));
            managementServerDto.setConnectionRequestUrl(dbObj.getString(ManagementServerFields.connectionRequestUrl.name()));
            managementServerDto.setConnectionRequestUsername(getMapList((BasicDBList) dbObj.get(ManagementServerFields.connectionRequestUsername.name())));
            managementServerDto.setConnectionRequestPassword(getMapList((BasicDBList) dbObj.get(ManagementServerFields.connectionRequestPassword.name())));
            return managementServerDto;
        }

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public List<Map> getUsername() {
            return username;
        }

        public void setUsername(List<Map> username) {
            this.username = username;
        }

        public List<Map> getPassword() {
            return password;
        }

        public void setPassword(List<Map> password) {
            this.password = password;
        }

        public Boolean getPeriodicInformEnabled() {
            return periodicInformEnabled;
        }

        public void setPeriodicInformEnabled(Boolean periodicInformEnabled) {
            this.periodicInformEnabled = periodicInformEnabled;
        }

        public Integer getPeriodicInformInterval() {
            return periodicInformInterval;
        }

        public void setPeriodicInformInterval(Integer periodicInformInterval) {
            this.periodicInformInterval = periodicInformInterval;
        }

        public Integer getRetryInterval() {
            return retryInterval;
        }

        public void setRetryInterval(Integer retryInterval) {
            this.retryInterval = retryInterval;
        }

        public String getConnectionRequestUrl() {
            return connectionRequestUrl;
        }

        public void setConnectionRequestUrl(String connectionRequestUrl) {
            this.connectionRequestUrl = connectionRequestUrl;
        }

        public List<Map> getConnectionRequestUsername() {
            return connectionRequestUsername;
        }

        public void setConnectionRequestUsername(List<Map> connectionRequestUsername) {
            this.connectionRequestUsername = connectionRequestUsername;
        }

        public List<Map> getConnectionRequestPassword() {
            return connectionRequestPassword;
        }

        public void setConnectionRequestPassword(List<Map> connectionRequestPassword) {
            this.connectionRequestPassword = connectionRequestPassword;
        }
    }

}
