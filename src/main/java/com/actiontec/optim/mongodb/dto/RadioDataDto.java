package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class RadioDataDto {
    public enum  hourlyFields {
        timestamp,
        ntTimestamp
    }

    public enum radioFields {
        bytesSent,
        bytesReceived,
        avgBytesSent,
        avgBytesReceived,
        deltaBytesSent,
        deltaBytesReceived,
        occupancy,
        channelsInUse
    }

    private long timestamp;
    private double airTimeBusy;
    private int channel;
    private long bytesSent;
    private long bytesReceived;
    private long deltaBytesSent;
    private long deltaBytesReceived;
    private List<AirTimeBusyDeviceDistributionDto> airTimeBusyDeviceDistribution;

    public static RadioDataDto fromBasicDbObject(BasicDBObject hourlyObj, BasicDBObject radioObj, BasicDBList wifiStaObj) {

        RadioDataDto radioDataDto = new RadioDataDto();

        radioDataDto.setTimestamp(hourlyObj.getLong(hourlyFields.ntTimestamp.name()));
        radioDataDto.setAirTimeBusy(radioObj.getDouble(radioFields.occupancy.name()));
        radioDataDto.setChannel(radioObj.getInt(radioFields.channelsInUse.name()));
        radioDataDto.setBytesSent(radioObj.getLong(radioFields.bytesSent.name()));
        radioDataDto.setBytesReceived(radioObj.getLong(radioFields.bytesReceived.name()));
        radioDataDto.setDeltaBytesSent(Objects.isNull(radioObj.get(radioFields.deltaBytesSent.name())) ? 0 : radioObj.getLong(radioFields.deltaBytesSent.name()));
        radioDataDto.setDeltaBytesReceived(Objects.isNull(radioObj.get(radioFields.deltaBytesReceived.name())) ? 0 : radioObj.getLong(radioFields.deltaBytesReceived.name()));
        radioDataDto.setAirTimeBusyDeviceDistribution(AirTimeBusyDeviceDistributionDto.fromBasicDBList(wifiStaObj));

        return radioDataDto;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public double getAirTimeBusy() {
        return airTimeBusy;
    }

    public void setAirTimeBusy(double airTimeBusy) {
        this.airTimeBusy = airTimeBusy;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public long getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(long bytesSent) {
        this.bytesSent = bytesSent;
    }

    public long getBytesReceived() {
        return bytesReceived;
    }

    public void setBytesReceived(long bytesReceived) {
        this.bytesReceived = bytesReceived;
    }

    public long getDeltaBytesSent() {
        return deltaBytesSent;
    }

    public void setDeltaBytesSent(long deltaBytesSent) {
        this.deltaBytesSent = deltaBytesSent;
    }

    public long getDeltaBytesReceived() {
        return deltaBytesReceived;
    }

    public void setDeltaBytesReceived(long deltaBytesReceived) {
        this.deltaBytesReceived = deltaBytesReceived;
    }

    public List<AirTimeBusyDeviceDistributionDto> getAirTimeBusyDeviceDistribution() {
        return airTimeBusyDeviceDistribution;
    }

    public void setAirTimeBusyDeviceDistribution(List<AirTimeBusyDeviceDistributionDto> airTimeBusyDeviceDistribution) {
        this.airTimeBusyDeviceDistribution = airTimeBusyDeviceDistribution;
    }

    public static class AirTimeBusyDeviceDistributionDto {

        public enum wifiStaFields {
            macAddress,
            hostName,
            rssi,
            airTimePercentage
        }

        private double airTimeBusy;
        private String macAddress;
        private String name;
        private String deviceType;

        public static AirTimeBusyDeviceDistributionDto fromBasicDbObject(BasicDBObject dbObj) {

            AirTimeBusyDeviceDistributionDto airTimeBusyDeviceDistributionDto = new AirTimeBusyDeviceDistributionDto();

            airTimeBusyDeviceDistributionDto.setAirTimeBusy(dbObj.getDouble(wifiStaFields.airTimePercentage.name()));
            airTimeBusyDeviceDistributionDto.setMacAddress(dbObj.getString(wifiStaFields.macAddress.name()));

            return airTimeBusyDeviceDistributionDto;
        }

        public static List<AirTimeBusyDeviceDistributionDto> fromBasicDBList(BasicDBList dbList) {

            List<AirTimeBusyDeviceDistributionDto> airTimeBusyDeviceDistributionDtoList = new ArrayList<>();
            if(dbList != null) {
                for (Object obj : dbList) {
                    AirTimeBusyDeviceDistributionDto airTimeBusyDeviceDistributionDto = fromBasicDbObject((BasicDBObject) obj);
                    airTimeBusyDeviceDistributionDtoList.add(airTimeBusyDeviceDistributionDto);
                }
            }

            return airTimeBusyDeviceDistributionDtoList;
        }

        public double getAirTimeBusy() {
            return airTimeBusy;
        }

        public void setAirTimeBusy(double airTimeBusy) {
            this.airTimeBusy = airTimeBusy;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }
    }
}
