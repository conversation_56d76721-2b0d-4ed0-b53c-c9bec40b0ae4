package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;

import java.util.ArrayList;
import java.util.List;

public class FsUsedDto {

    public enum FsUsedFields {
        type,
        used,
        usage
    }

    private String type;
    private long used;
    private double usage;

    public static FsUsedDto fromBasicDbObject(BasicDBObject dbObj) {
        FsUsedDto fsUsedDto = new FsUsedDto();
        fsUsedDto.setType(dbObj.getString(FsUsedFields.type.name(), ""));
        fsUsedDto.setUsed(dbObj.getLong(FsUsedFields.used.name(), 0));
        fsUsedDto.setUsage(dbObj.getDouble(FsUsedFields.usage.name(), 0));
        return fsUsedDto;
    }

    public static List<FsUsedDto> fromBasicDbList(BasicDBList dbList) {
        List<FsUsedDto> fsUsedDtoList = new ArrayList<>();
        for(Object dbObj : dbList) {
            FsUsedDto fsUsedDto = fromBasicDbObject((BasicDBObject) dbObj);
            fsUsedDtoList.add(fsUsedDto);
        }
        return fsUsedDtoList;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getUsed() {
        return used;
    }

    public void setUsed(long used) {
        this.used = used;
    }

    public double getUsage() {
        return usage;
    }

    public void setUsage(double usage) {
        this.usage = usage;
    }
}
