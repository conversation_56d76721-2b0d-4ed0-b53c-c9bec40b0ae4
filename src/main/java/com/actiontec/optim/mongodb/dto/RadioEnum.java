package com.actiontec.optim.mongodb.dto;

import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public enum RadioEnum {
    RADIO_24G("2.4G", "2g", "2.4 GHz"),
    RADIO_5G("5G", "5g", "5 GHz"),
    RADIO_6G("6G", "6g", "6 GHz"),
    RADIO_5G_HI("5GHi", "5gHi", ""),
    RADIO_5G_LO("5GLo", "5gLo", "");

    private String radioKey;
    private String alias;
    private String fullName;

    private RadioEnum(String radioKey, String alias, String fullName) {
        this.radioKey = radioKey;
        this.alias = alias;
        this.fullName = fullName;
    }

    public static Optional<RadioEnum> parseByRadioKey(String radioKey) {
        for (RadioEnum radioEnum : RadioEnum.values()) {
            if (StringUtils.equals(radioEnum.radioKey, radioKey)) {
                return Optional.of(radioEnum);
            }
        }
        return Optional.empty();
    }

    public static String getRadioKeyByAlias(String alias) {
        for (RadioEnum radioEnum : RadioEnum.values()) {
            if (StringUtils.equals(radioEnum.alias, alias)) {
                return radioEnum.radioKey;
            }
        }
        return "";
    }

    public String getRadioKey() {
        return radioKey;
    }

    public String getAlias() {
        return alias;
    }
}
