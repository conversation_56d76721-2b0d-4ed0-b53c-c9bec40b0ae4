package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

public class RebootHistoryDto {
    public static final String COLLECTION = "rebootHistory";
    public static final String EVENT = "Reboot";
    public enum Fields {
        userId,
        serialNumber,
        uptime,
        timestamp,
        rebootReason
    }

    private String userId;
    private String serialNumber;
    private Long uptime;
    private Long timestamp;
    private String rebootReason;

    public static RebootHistoryDto fromBasicDbObject(BasicDBObject dbObj) {
        RebootHistoryDto rebootHistoryDto = new RebootHistoryDto();

        rebootHistoryDto.setUserId(dbObj.getString(Fields.userId.name()));
        rebootHistoryDto.setSerialNumber(dbObj.getString(Fields.serialNumber.name()));
        rebootHistoryDto.setRebootReason(dbObj.getString(Fields.rebootReason.name()));

        Long timestamp = dbObj.getLong(Fields.timestamp.name());
        Long uptime = dbObj.getLong(Fields.uptime.name());

        rebootHistoryDto.setTimestamp(uptime*1000);
        rebootHistoryDto.setUptime(timestamp-uptime);
        return rebootHistoryDto;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Long getUptime() {
        return uptime;
    }

    public void setUptime(Long uptime) {
        this.uptime = uptime;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getRebootReason() {
        return rebootReason;
    }

    public void setRebootReason(String rebootReason) {
        this.rebootReason = rebootReason;
    }
}
