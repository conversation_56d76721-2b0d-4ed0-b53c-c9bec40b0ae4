package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBObject;

import java.util.Objects;

public class SteerDataDto {
    public enum SteerDataFields {
        steerTarget2g2g,
        steerTarget2g5g,
        steerTarget5g2g,
        steerTarget5g5g,
        steerOther2g2g,
        steerOther2g5g,
        steerOther5g2g,
        steerOther5g5g,
        roam2g2g,
        roam2g5g,
        roam5g2g,
        roam5g5g
    }

    private long steerTarget2g2g;
    private long steerTarget2g5g;
    private long steerTarget5g2g;
    private long steerTarget5g5g;
    private long steerOther2g2g;
    private long steerOther2g5g;
    private long steerOther5g2g;
    private long steerOther5g5g;
    private long roam2g2g;
    private long roam2g5g;
    private long roam5g2g;
    private long roam5g5g;

    public static SteerDataDto fromBasicDbObject(BasicDBObject dbObj) {

        SteerDataDto steerDataDto = new SteerDataDto();

        steerDataDto.setSteerTarget2g2g(Objects.isNull(dbObj.get(SteerDataFields.steerTarget2g2g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerTarget2g2g.name()));
        steerDataDto.setSteerTarget2g5g(Objects.isNull(dbObj.get(SteerDataFields.steerTarget2g5g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerTarget2g5g.name()));
        steerDataDto.setSteerTarget5g2g(Objects.isNull(dbObj.get(SteerDataFields.steerTarget5g2g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerTarget5g2g.name()));
        steerDataDto.setSteerTarget5g5g(Objects.isNull(dbObj.get(SteerDataFields.steerTarget5g5g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerTarget5g5g.name()));
        steerDataDto.setSteerOther2g2g(Objects.isNull(dbObj.get(SteerDataFields.steerOther2g2g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerOther2g2g.name()));
        steerDataDto.setSteerOther2g5g(Objects.isNull(dbObj.get(SteerDataFields.steerOther2g5g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerOther2g5g.name()));
        steerDataDto.setSteerOther5g2g(Objects.isNull(dbObj.get(SteerDataFields.steerOther5g2g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerOther5g2g.name()));
        steerDataDto.setSteerOther5g5g(Objects.isNull(dbObj.get(SteerDataFields.steerOther5g5g.name())) ? 0 : dbObj.getLong(SteerDataFields.steerOther5g5g.name()));
        steerDataDto.setRoam2g2g(Objects.isNull(dbObj.get(SteerDataFields.roam2g2g.name())) ? 0 : dbObj.getLong(SteerDataFields.roam2g2g.name()));
        steerDataDto.setRoam2g5g(Objects.isNull(dbObj.get(SteerDataFields.roam2g5g.name())) ? 0 : dbObj.getLong(SteerDataFields.roam2g5g.name()));
        steerDataDto.setRoam5g2g(Objects.isNull(dbObj.get(SteerDataFields.roam5g2g.name())) ? 0 : dbObj.getLong(SteerDataFields.roam5g2g.name()));
        steerDataDto.setRoam5g5g(Objects.isNull(dbObj.get(SteerDataFields.roam5g5g.name())) ? 0 : dbObj.getLong(SteerDataFields.roam5g5g.name()));

        return steerDataDto;
    }

    public long getSteerTarget2g2g() {
        return steerTarget2g2g;
    }

    public void setSteerTarget2g2g(long steerTarget2g2g) {
        this.steerTarget2g2g = steerTarget2g2g;
    }

    public long getSteerTarget2g5g() {
        return steerTarget2g5g;
    }

    public void setSteerTarget2g5g(long steerTarget2g5g) {
        this.steerTarget2g5g = steerTarget2g5g;
    }

    public long getSteerTarget5g2g() {
        return steerTarget5g2g;
    }

    public void setSteerTarget5g2g(long steerTarget5g2g) {
        this.steerTarget5g2g = steerTarget5g2g;
    }

    public long getSteerTarget5g5g() {
        return steerTarget5g5g;
    }

    public void setSteerTarget5g5g(long steerTarget5g5g) {
        this.steerTarget5g5g = steerTarget5g5g;
    }

    public long getSteerOther2g2g() {
        return steerOther2g2g;
    }

    public void setSteerOther2g2g(long steerOther2g2g) {
        this.steerOther2g2g = steerOther2g2g;
    }

    public long getSteerOther2g5g() {
        return steerOther2g5g;
    }

    public void setSteerOther2g5g(long steerOther2g5g) {
        this.steerOther2g5g = steerOther2g5g;
    }

    public long getSteerOther5g2g() {
        return steerOther5g2g;
    }

    public void setSteerOther5g2g(long steerOther5g2g) {
        this.steerOther5g2g = steerOther5g2g;
    }

    public long getSteerOther5g5g() {
        return steerOther5g5g;
    }

    public void setSteerOther5g5g(long steerOther5g5g) {
        this.steerOther5g5g = steerOther5g5g;
    }

    public long getRoam2g2g() {
        return roam2g2g;
    }

    public void setRoam2g2g(long roam2g2g) {
        this.roam2g2g = roam2g2g;
    }

    public long getRoam2g5g() {
        return roam2g5g;
    }

    public void setRoam2g5g(long roam2g5g) {
        this.roam2g5g = roam2g5g;
    }

    public long getRoam5g2g() {
        return roam5g2g;
    }

    public void setRoam5g2g(long roam5g2g) {
        this.roam5g2g = roam5g2g;
    }

    public long getRoam5g5g() {
        return roam5g5g;
    }

    public void setRoam5g5g(long roam5g5g) {
        this.roam5g5g = roam5g5g;
    }
}
