package com.actiontec.optim.mongodb.dto;

import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class StationSpeedTestDto {
    private String userId;
    private String band;
    private String ipAddr;
    private String macAddr;
    private double dataRate;
    private double rxRate;
    private double txRate;
    private Date date;

    public static final String COLLECTION = "stationSpeedTest";
    public enum Fields {
        userId,
        band,
        ipAddr,
        macAddr,
        dataRate,
        rxRate,
        txRate,
        date
    }

    public static StationSpeedTestDto fromBasicDbObject(BasicDBObject dbObj) {
        StationSpeedTestDto stationSpeedTestDto = new StationSpeedTestDto();
        stationSpeedTestDto.setUserId(dbObj.getString(Fields.userId.name()));
        stationSpeedTestDto.setBand(dbObj.getString(Fields.band.name()));
        stationSpeedTestDto.setIpAddr(dbObj.getString(Fields.ipAddr.name()));
        stationSpeedTestDto.setMacAddr(dbObj.getString(Fields.macAddr.name()));
        stationSpeedTestDto.setDataRate(dbObj.getDouble(Fields.dataRate.name(), 0));
        stationSpeedTestDto.setRxRate(dbObj.getDouble(Fields.rxRate.name(), 0));
        stationSpeedTestDto.setTxRate(dbObj.getDouble(Fields.txRate.name(), 0));
        stationSpeedTestDto.setDate(dbObj.getDate((Fields.date.name())));
        return stationSpeedTestDto;
    }

    public static List<StationSpeedTestDto> fromBasicDbList(BasicDBList dbList) {
        List<StationSpeedTestDto> result = new ArrayList<>();
        for (Object dbObj : dbList) {
            StationSpeedTestDto stationSpeedTestDto = fromBasicDbObject((BasicDBObject) dbObj);
            result.add(stationSpeedTestDto);
        }
        return result;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getBand() {
        return band;
    }

    public void setBand(String band) {
        this.band = band;
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    public String getMacAddr() {
        return macAddr;
    }

    public void setMacAddr(String macAddr) {
        this.macAddr = macAddr;
    }

    public double getDataRate() {
        return dataRate;
    }

    public void setDataRate(double dataRate) {
        this.dataRate = dataRate;
    }

    public double getRxRate() {
        return rxRate;
    }

    public void setRxRate(double rxRate) {
        this.rxRate = rxRate;
    }

    public double getTxRate() {
        return txRate;
    }

    public void setTxRate(double txRate) {
        this.txRate = txRate;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }
}
