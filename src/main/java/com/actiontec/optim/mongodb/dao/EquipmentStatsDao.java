package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.EquipmentStatsDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Component
public class EquipmentStatsDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    private String pipelineQuery0 = "{ $match: { userId: '%s', serialNumber: '%s' }}";
    private String pipelineQuery1 = "{ $unwind: { path: '$stats' }}";
    private String pipelineQuery2 = "{ $match: { 'stats.timestamp': { $gte: %s } } }";

    public List<EquipmentStatsDto> listEquipmentStatsByUserIdAndSerial(String userId, String serial, Long duration) {

        ArrayList<EquipmentStatsDto> result = new ArrayList<>();
        List<DBObject> aggregatePipeline = new ArrayList<>();

        aggregatePipeline.add((BasicDBObject.parse(String.format(pipelineQuery0, userId, serial))));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery1)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery2, String.valueOf(System.currentTimeMillis() - duration * 1000))));

        Iterator iterator = at3Adapter.getMongoDbCollection(EquipmentStatsDto.COLLECTION).aggregate(aggregatePipeline).results().iterator();
        while (iterator.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) iterator.next();
            result.add(EquipmentStatsDto.fromBasicDbObject(resultObject));
        }

        return result;
    }
}
