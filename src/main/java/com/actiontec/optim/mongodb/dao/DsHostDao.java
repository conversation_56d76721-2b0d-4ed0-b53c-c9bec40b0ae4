package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.model.UserDeviceEthernet;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.DS_HOST_INSIGHTS;

@Component
public class DsHostDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public UserDeviceEthernet fetchInternetUsage(String userId, String macAddress) {

        List<BasicDBObject> pipeline = new ArrayList<>();

        // Step 1: Match userId
        pipeline.add(new BasicDBObject("$match", new BasicDBObject("userId", userId)));

        // Step 2: Sort by timestamp in descending order to get the latest record first
        pipeline.add(new BasicDBObject("$sort", new BasicDBObject("timestamp", -1)));

        // Step 3: Limit to the latest record
        pipeline.add(new BasicDBObject("$limit", 1));

        // Step 4: Project to convert devMap field to array
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("allDevMaps", new BasicDBObject("$objectToArray", "$$ROOT"))));

        // Step 5: Unwind to handle allDevMaps array
        pipeline.add(new BasicDBObject("$unwind", "$allDevMaps"));

        // Step 6: Match to filter only the date fields (regex match on the key)
        pipeline.add(new BasicDBObject("$match", new BasicDBObject("allDevMaps.k", new BasicDBObject("$regex", "^\\d{4}-\\d{2}-\\d{2}$"))));

        // Step 7: Convert the value (v) of `allDevMaps` into an array of objects
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("allDevMapData", new BasicDBObject("$objectToArray", "$allDevMaps.v"))));

        // Step 8: Unwind the newly created array to access each numbered key (0, 1, 2, ...)
        pipeline.add(new BasicDBObject("$unwind", "$allDevMapData"));

        // Step 9: Extract the devMap field from the number-keyed objects
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("devMap", "$allDevMapData.v.devMap")));

        // Step 10: Convert the devMap into an array of key-value pairs (MAC addresses)
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("devMapEntries", new BasicDBObject("$objectToArray", new BasicDBObject("$ifNull", Arrays.asList("$devMap", new BasicDBObject()))))));

        // Step 11: Unwind devMapEntries to process each MAC address individually
        pipeline.add(new BasicDBObject("$unwind", "$devMapEntries"));

        // Step 12: Match to filter by specific MAC address
        pipeline.add(new BasicDBObject("$match", new BasicDBObject("devMapEntries.k", macAddress)));

        // Step 13: Unwind the array of MAC address data (i.e., devMapEntries.v)
        pipeline.add(new BasicDBObject("$unwind", "$devMapEntries.v"));

        // Step 14: Sort by timestamp in descending order (again for safety if needed)
        pipeline.add(new BasicDBObject("$sort", new BasicDBObject("devMapEntries.v.timestamp", -1)));

        // Step 15: Limit to the latest MAC address record
        pipeline.add(new BasicDBObject("$limit", 5));

        // Step 16: Project only the necessary fields
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("_id", 0)
                .append("internetRx", "$devMapEntries.v.internetRx")
                .append("internetTx", "$devMapEntries.v.internetTx")
                .append("timestamp", "$devMapEntries.v.timestamp")));

        AggregationOutput aggregationOutput = at3Adapter.getMongoDbCollection(DS_HOST_INSIGHTS).aggregate(pipeline);
        Iterable<DBObject> results = aggregationOutput.results();

        UserDeviceEthernet userDeviceEthernet = new UserDeviceEthernet();
        userDeviceEthernet.setMacAddress(macAddress);

        Iterator<DBObject> iterator = results.iterator();
        while (iterator.hasNext()) {
            DBObject result = iterator.next();
            if (Objects.nonNull(result.get("internetTx")) && Objects.nonNull(result.get("internetRx"))) {
                try {
                    long internetTx = ((Double) result.get("internetTx")).longValue();
                    long internetRx = ((Double) result.get("internetRx")).longValue();
                    logger.info("tx:[{}] rx:[{}]", internetRx, internetTx);
                    userDeviceEthernet.setBytesSent(internetRx);
                    userDeviceEthernet.setBytesReceived(internetTx);
                    break;
                } catch (NumberFormatException e) {
                    logger.error("Invalid number format in result: " + result);
                }
            }
        }


        return userDeviceEthernet;
    }
}
