package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.SteerDataDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
public class SteerAggregateDao {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    At3Adapter at3Adapter;

    public static final String COLLECTION_statsSteeringPerIspPerHour = "statsSteeringPerIspPerHour";
    public static final String COLLECTION_statsSteeringPerIspPerDay = "statsSteeringPerIspPerDay";
    public static final String COLLECTION_statsSteeringPerUserPerHour = "statsSteeringPerUserPerHour";
    public static final String COLLECTION_statsSteeringPerUserPerDay = "statsSteeringPerUserPerDay";

    private String pipelineQueryTime = "{ $match: { statsDateTime: { $gte: { $date: '%s' }}}}";
    private String pipelineQueryIsp = "{ $match: { isp: '%s' }}";
    private String pipelineQueryUserId = "{ $match: { userId: '%s' }}";
    private String pipelineQuerySteerData = "{ $group: {" +
                                                    "_id: 'steerData'," +
                                                    "steerTarget2g2g: { $sum: '$steer.steer_target_2g_2g' }," +
                                                    "steerTarget2g5g: { $sum: '$steer.steer_target_2g_5g' }," +
                                                    "steerTarget5g2g: { $sum: '$steer.steer_target_5g_2g' }," +
                                                    "steerTarget5g5g: { $sum: '$steer.steer_target_5g_5g' }," +
                                                    "steerOther2g2g: { $sum: '$steer.steer_other_2g_2g' }," +
                                                    "steerOther2g5g: { $sum: '$steer.steer_other_2g_5g' }," +
                                                    "steerOther5g2g: { $sum: '$steer.steer_other_5g_2g' }," +
                                                    "steerOther2g2g: { $sum: '$steer.steer_other_5g_5g' }," +
                                                    "roam2g2g: { $sum: '$steer.roam_2g_2g' }," +
                                                    "roam2g5g: { $sum: '$steer.roam_2g_5g' }," +
                                                    "roam5g2g: { $sum: '$steer.roam_5g_2g' }," +
                                                    "roam5g5g: { $sum: '$steer.roam_5g_5g' }," +
                                            "} }";

    public SteerDataDto getSteerData(String ispName, String userId, ZonedDateTime startTime, String collection) {

        SteerDataDto steerDataDto = null;

        List<DBObject> aggregatePipeline = new ArrayList<>();

        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryTime, startTime.toInstant().toString())));

        if(ispName != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryIsp, ispName)));
        }

        if(userId != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryUserId, userId)));
        }

        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuerySteerData)));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(collection);
        AggregationOutput aggregationResult = dbCollection.aggregate(aggregatePipeline);

        for(DBObject result : aggregationResult.results()) {
            steerDataDto = SteerDataDto.fromBasicDbObject((BasicDBObject) result);
        }

        logger.debug("getPonpDataList succeeded. userId:[{}] startTime:[{}] result:[{}]", userId, startTime, aggregationResult.results());

        if(steerDataDto == null) {
            steerDataDto = new SteerDataDto();
        }
        return steerDataDto;
    }
}
