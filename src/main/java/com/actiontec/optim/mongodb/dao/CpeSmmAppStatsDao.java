package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.CpeSmmAppStatsDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

@Component
public class CpeSmmAppStatsDao {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public Long getLastTimestampForJob(String jobId) {
        Long timestamp = 0L;
        BasicDBObject query = new BasicDBObject();

        if (Objects.nonNull(jobId))
            query.put("jobId", jobId);
        else {
            query.put("jobId", new BasicDBObject("$exists", true));
            query.put("jobId", new BasicDBObject("$ne", "null"));
        }
        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", 0);

        BasicDBObject sort = new BasicDBObject();
        sort.put(CpeSmmAppStatsDto.CpeSmmAppFields.timestamp.name(), -1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppStatsDto.COLLECTION_cpeSmmAppStats);
        BasicDBObject cpeSmmAppStats = (BasicDBObject) dbCollection.findOne(query, projection, sort);

        if (Objects.nonNull(cpeSmmAppStats))
        {
            timestamp = cpeSmmAppStats.getLong(CpeSmmAppStatsDto.CpeSmmAppFields.timestamp.name(), 0);
        }

        logger.debug("getLastTimestamp  Last timestamp: [{}]", timestamp);
        return timestamp;
    }

    public Long getLastTimestamp(String isp) {

        Long timestamp = 0L;
        BasicDBObject query = new BasicDBObject();
        if (isp != null) {
            query.put(CpeSmmAppStatsDto.CpeSmmAppFields.isp.name(), isp);
        }

        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", 0);

        BasicDBObject sort = new BasicDBObject();
        sort.put(CpeSmmAppStatsDto.CpeSmmAppFields.timestamp.name(), -1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppStatsDto.COLLECTION_cpeSmmAppStats);
        BasicDBObject cpeSmmAppStats = (BasicDBObject) dbCollection.findOne(query, projection, sort);

        if (cpeSmmAppStats != null) {
            timestamp = cpeSmmAppStats.getLong(CpeSmmAppStatsDto.CpeSmmAppFields.timestamp.name(), 0);
        }

        logger.debug("getLastTimestamp ISP: [{}], Last timestamp: [{}]", isp, timestamp);
        return timestamp;
    }

    public List<CpeSmmAppStatsDto> findCpeSmmAppStatsByIspAndTimestamp(String isp, Long timestamp) {

        List<CpeSmmAppStatsDto> cpeSmmAppStatsDtoList = new ArrayList<>();
        List<DBObject> aggregatePipeline = new ArrayList<>();
        if (isp != null) {
            aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject(CpeSmmAppStatsDto.CpeSmmAppFields.isp.name(), isp)));
        }
        aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject(CpeSmmAppStatsDto.CpeSmmAppFields.timestamp.name(), timestamp)));
        String pipelineQuery1 = "{" +
                "  $group: {" +
                "    _id: {" +
                "      isp: '$isp'," +
                "      serviceId: '$serviceId'," +
                "      version: '$version'," +
                "      actionState: '$actionState'," +
                "      desiredState: '$desiredState'" +
                "    }," +
                "    count: { $sum: '$count' }," +
                "    maxCpu: { $max: '$maxCpu' }," +
                "    minCpu: { $min: '$minCpu' }," +
                "    totalCpu: { $sum: '$totalCpu' }," +
                "    maxMem: { $max: '$maxMem' }," +
                "    minMem: { $min: '$minMem' }," +
                "    totalMem: { $sum: '$totalMem' }," +
                "    maxFsUsed: { $max: '$maxFsUsed' }," +
                "    minFsUsed: { $min: '$minFsUsed' }," +
                "    totalFsUsed: { $sum: '$totalFsUsed' }" +
                "  }" +
                "}";

        String pipelineQuery2 = "{" +
                "  $project: {" +
                "    _id: false," +
                "    isp: '$_id.isp'," +
                "    serviceId: '$_id.serviceId'," +
                "    version: '$_id.version'," +
                "    actionState: '$_id.actionState'," +
                "    desiredState: '$_id.desiredState'," +
                "    online: '$_id.online'," +
                "    count: '$count'," +
                "    avgCpu: { $divide: [ '$totalCpu', '$count' ] }," +
                "    maxCpu: '$maxCpu'," +
                "    minCpu: '$minCpu'," +
                "    avgMem: { $divide: [ '$totalMem', '$count' ] }," +
                "    maxMem: '$maxMem'," +
                "    minMem: '$minMem'," +
                "    totalMem: '$totalMem'," +
                "    avgFsUsed: { $divide: [ '$totalFsUsed', '$count' ] }," +
                "    maxFsUsed: '$maxFsUsed'," +
                "    minFsUsed: '$minFsUsed'," +
                "    totalFsUsed: '$totalFsUsed'" +
                "  }" +
                "}";

        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery1)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery2)));
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppStatsDto.COLLECTION_cpeSmmAppStats);
        Iterator iterator = dbCollection.aggregate(aggregatePipeline).results().iterator();

        while (iterator.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) iterator.next();
            cpeSmmAppStatsDtoList.add(CpeSmmAppStatsDto.fromBasicDbObject(resultObject));
        }

        logger.debug("findCpeSmmAppStatsByIspAndTimestamp return size: [{}]", cpeSmmAppStatsDtoList.size());
        return cpeSmmAppStatsDtoList;
    }
}