package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.StationSpeedTestDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class StationSpeedTestDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public List<StationSpeedTestDto> listByUserId(String userId) {
        BasicDBObject query = new BasicDBObject();
        query.put(StationSpeedTestDto.Fields.userId.name(), userId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(StationSpeedTestDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        ArrayList<StationSpeedTestDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            result.add(StationSpeedTestDto.fromBasicDbObject(resultObject));
        }
        return result;
    }

    public List<StationSpeedTestDto> listByUserIdAndDeviceMac(String userId, String deviceMac) {
        BasicDBObject query = new BasicDBObject();
        query.put(StationSpeedTestDto.Fields.userId.name(), userId);
        query.put(StationSpeedTestDto.Fields.macAddr.name(), deviceMac);
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(StationSpeedTestDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        ArrayList<StationSpeedTestDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            result.add(StationSpeedTestDto.fromBasicDbObject(resultObject));
        }
        return result;
    }

    public Map<String, StationSpeedTestDto> listLatestByUserId(String userId) {
        List<StationSpeedTestDto> stationSpeedTestDtos = listByUserId(userId);
        stationSpeedTestDtos.sort((e1, e2) -> e2.getDate().compareTo(e1.getDate()));

        Map<String, StationSpeedTestDto> result = new HashMap<>();
        for (StationSpeedTestDto stationSpeedTestDto : stationSpeedTestDtos) {
            if (result.get(stationSpeedTestDto.getMacAddr()) == null) {
                result.put(stationSpeedTestDto.getMacAddr(), stationSpeedTestDto);
            }
        }
        return result;
    }

    public Optional<StationSpeedTestDto> findLatestByUserIdAndDeviceMac(String userId, String deviceMac) {
        List<StationSpeedTestDto> stationSpeedTestDtos = listByUserIdAndDeviceMac(userId, deviceMac);
        if( stationSpeedTestDtos.isEmpty()) {
            return Optional.empty();
        } else {
            if(stationSpeedTestDtos.size() >1) {
                stationSpeedTestDtos.sort((e1, e2) -> e2.getDate().compareTo(e1.getDate()));
            }
            return Optional.of(stationSpeedTestDtos.get(0));
        }
    }
}
