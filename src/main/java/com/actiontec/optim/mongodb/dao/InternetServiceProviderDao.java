package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InternetServiceProviderDao {
    private final Logger logger = LogManager.getLogger(this.getClass());
    public static String COLLECTION_internetServiceProvider = "internetServiceProvider";
    public static String FIELD_name = "name";
    public static String FIELD_id = "id";

    @Autowired
    private At3Adapter at3Adapter;

    public String getIspName(String compartmentId) {
        logger.debug("getIspName compartmentId:[{}]", compartmentId);
        BasicDBObject query = new BasicDBObject();
        query.put("id", compartmentId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(COLLECTION_internetServiceProvider);
        DBObject ispObject = dbCollection.findOne(query);
        String ispName = ((BasicDBObject) ispObject).getString(FIELD_name);
        logger.debug("getIspName compartmentId:[{}] ispName:[{}]", compartmentId, ispName);
        return ispName;
    }

    public String getIspIdByName(String ispName) {
        logger.debug("getIspIdByName ispName:[{}]", ispName);
        BasicDBObject query = new BasicDBObject();
        query.put("name", ispName);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(COLLECTION_internetServiceProvider);
        DBObject ispObject = dbCollection.findOne(query);

        String ispId = null;
        if (ispObject != null) {
            ispId = (String) ispObject.get(FIELD_id);
        } else {
            logger.warn("ISP not found for name: {}", ispName);
        }

        logger.info("getIspId name:[{}] ispId:[{}]", ispName, ispId);
        return ispId;
    }
}
