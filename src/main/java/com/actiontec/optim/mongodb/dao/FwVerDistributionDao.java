package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.FwVerDistributionDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

@Component
public class FwVerDistributionDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    private String pipelineQuery0 = "{ $match: { isp: '%s' }}";

    private String pipelineQuery1 = "{" +
            "  $group: {" +
            "    _id: {" +
            "      isp: '$isp'," +
            "      modelName: '$modelName'," +
            "      fwVersion: '$fwVersion'," +
            "      connectionStatus: { $cond:[ {$gt: [ '$sendingTime', %s ] }, 'online', 'offline' ]}" +
            "    }," +
            "    count: { $sum: 1 }" +
            "  }" +
            "}";

    private String pipelineQuery2 = "{" +
            "  $addFields: {" +
            "    onlineUnits: {" +
            "      $cond: [ {$eq: ['$_id.connectionStatus', 'online' ]}, '$count', 0 ]" +
            "    }," +
            "    offlineUnits: {" +
            "      $cond: [ {$eq: [ '$_id.connectionStatus', 'offline' ]}, '$count', 0 ]" +
            "    }" +
            "  }" +
            "}";

    private String pipelineQuery3 = "{" +
            "  $group: {" +
            "    _id: {" +
            "      isp:'$_id.isp'," +
            "      modelName:'$_id.modelName'," +
            "      fwVersion:'$_id.fwVersion'," +
            "    }," +
            "    onlineUnits: { $sum: '$onlineUnits' }," +
            "    offlineUnits: { $sum: '$offlineUnits' }" +
            "  }" +
            "}";

    private String pipelineQuery4 = "{" +
            "  $project: {" +
            "    _id: false," +
            "    isp: '$_id.isp'," +
            "    modelName: '$_id.modelName'," +
            "    fwVersion: '$_id.fwVersion'," +
            "    onlineUnits: '$onlineUnits'," +
            "    offlineUnits: '$offlineUnits'" +
            "  }" +
            "}";

    public List<FwVerDistributionDto> calculateFwVerDistribution(Optional<String> isp, int threshold) {
        logger.debug("calculateFwVerDistribution isp:[{}] threshold:[{}]", isp.orElse("null"), threshold);

        List<DBObject> aggregatePipeline = new ArrayList<>();
        if (isp.isPresent()) {
            aggregatePipeline.add((BasicDBObject.parse(String.format(pipelineQuery0, isp.get()))));
        }
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery1, String.valueOf(System.currentTimeMillis() - threshold * 60 * 1000))));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery2)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery3)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery4)));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);

        AggregationOutput aggregationResult = dbCollection.aggregate(aggregatePipeline);
        Iterator resultItr = aggregationResult.results().iterator();
        ArrayList<FwVerDistributionDto> result = new ArrayList<>();
        while (resultItr.hasNext()) {
            DBObject resultObject = (DBObject) resultItr.next();
            result.add(FwVerDistributionDto.fromBasicDbObject((BasicDBObject) resultObject));
        }

        logger.debug("calculateFwVerDistribution isp:[{}] threshold:[{}] result size:[{}]", isp.orElse("null"), threshold, result.size());
        return result;
    }
}
