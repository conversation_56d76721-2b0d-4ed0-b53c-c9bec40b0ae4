package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.HostDto;
import com.actiontec.optim.mongodb.dto.WifiStationDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class WifiStationDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public List<WifiStationDto> listByUserId(String userId, long baseNtTimestamp) {
        BasicDBObject query = new BasicDBObject();
        query.put(WifiStationDto.Fields.userId.name(), userId);
        query.put(WifiStationDto.Fields.ntTimestamp.name(), new BasicDBObject("$gte", baseNtTimestamp));

        BasicDBObject sort = new BasicDBObject();
        sort.put(HostDto.Fields.ntTimestamp.name(), -1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(WifiStationDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject()).sort(sort);

        ArrayList<WifiStationDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = (DBObject) dbCursor.next();
            result.add(WifiStationDto.fromBasicDbObject((BasicDBObject) resultObject));
        }
        return result;
    }

    public Map<String, WifiStationDto> listByNetworkId(String networkId) {
        BasicDBObject query = new BasicDBObject();
        query.put(WifiStationDto.Fields.userId.name(), networkId);

        BasicDBObject sort = new BasicDBObject();
        sort.put(HostDto.Fields.ntTimestamp.name(), -1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(WifiStationDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject()).sort(sort);

        Map<String, WifiStationDto> resultMap = new HashMap<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = dbCursor.next();
            WifiStationDto station = WifiStationDto.fromBasicDbObject((BasicDBObject) resultObject);

            if (station.getMacAddress() != null) {
                resultMap.put(station.getMacAddress(), station);
            }
        }
        return resultMap;
    }
}
