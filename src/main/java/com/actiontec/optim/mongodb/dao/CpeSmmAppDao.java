package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.CpeSmmAppDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CpeSmmAppDao {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public List<CpeSmmAppDto> findByStn(String stn) {
        logger.debug(".findByStnAndServiceId  stn:[{}]", stn);

        BasicDBObject query = new BasicDBObject();
        query.put(CpeSmmAppDto.CpeSmmAppFields.serviceTelephoneNumber.name(), stn);
        query.put(CpeSmmAppDto.CpeSmmAppFields.serviceId.name(), new BasicDBObject("$ne", null));
        query.put(CpeSmmAppDto.CpeSmmAppFields.rptVersion.name(), new BasicDBObject("$ne", null));
        query.put(CpeSmmAppDto.CpeSmmAppFields.serviceVer.name(), new BasicDBObject("$ne", null));
        query.put(CpeSmmAppDto.CpeSmmAppFields.desiredState.name(), new BasicDBObject("$ne", null));
        query.put(CpeSmmAppDto.CpeSmmAppFields.rptProvisionStatus.name(), new BasicDBObject("$ne", null));
        query.put(CpeSmmAppDto.CpeSmmAppFields.actionState.name(), new BasicDBObject("$ne", null));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppDto.COLLECTION_cpeSmmApp);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        List<CpeSmmAppDto> cpeSmmAppDtoList = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            cpeSmmAppDtoList.add(CpeSmmAppDto.fromBasicDbObject(resultObject));
        }

        return cpeSmmAppDtoList;
    }


    public List<CpeSmmAppDto> findByStnAndServiceId(String stn, String serviceId) {
        logger.debug(".findByStnAndServiceId  stn:[{}] serviceId:[{}]", stn, serviceId);

        BasicDBObject query = new BasicDBObject();
        query.put(CpeSmmAppDto.CpeSmmAppFields.serviceTelephoneNumber.name(), stn);
        query.put(CpeSmmAppDto.CpeSmmAppFields.serviceId.name(), serviceId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppDto.COLLECTION_cpeSmmApp);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        List<CpeSmmAppDto> cpeSmmAppDtoList = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            cpeSmmAppDtoList.add(CpeSmmAppDto.fromBasicDbObject(resultObject));
        }

        return cpeSmmAppDtoList;
    }

    public List<CpeSmmAppDto> findByUserIdAndSerial(String userId, String serial) {
        logger.debug("findByUserIdAndSerial. userId:[{}] serial:[{}]", userId, serial);

        BasicDBObject query = new BasicDBObject();
        query.put(CpeSmmAppDto.CpeSmmAppFields.userId.name(), userId);
        query.put(CpeSmmAppDto.CpeSmmAppFields.serialNumber.name(), serial);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppDto.COLLECTION_cpeSmmApp);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        List<CpeSmmAppDto> cpeSmmAppDtoList = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            cpeSmmAppDtoList.add(CpeSmmAppDto.fromBasicDbObject(resultObject));
        }

        return cpeSmmAppDtoList;
    }

    public List<CpeSmmAppDto> findByUserIdAndSerialAndContainerId(String userId, String serial, String containerId) {
        logger.debug("findByUserIdAndSerialAndContainerId. userId:[{}] serial:[{}] containerId[{}]", userId, serial, containerId);

        BasicDBObject query = new BasicDBObject();
        query.put(CpeSmmAppDto.CpeSmmAppFields.userId.name(), userId);
        query.put(CpeSmmAppDto.CpeSmmAppFields.serialNumber.name(), serial);
        query.put(CpeSmmAppDto.CpeSmmAppFields.serviceId.name(), containerId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppDto.COLLECTION_cpeSmmApp);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        List<CpeSmmAppDto> cpeSmmAppDtoList = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            cpeSmmAppDtoList.add(CpeSmmAppDto.fromBasicDbObject(resultObject));
        }

        return cpeSmmAppDtoList;
    }
}
