package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.RebootHistoryDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;

@Component
public class RebootHistoryDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public List<RebootHistoryDto> listRebootHistoryByUserIdAndSerial(String userId, String serial, Long duration) {

        BasicDBObject query = new BasicDBObject();
        query.put(RebootHistoryDto.Fields.userId.name(), userId);
        query.put(RebootHistoryDto.Fields.serialNumber.name(), serial);

        ZonedDateTime ldtNow = ZonedDateTime.now();
        query.put(RebootHistoryDto.Fields.uptime.name(), new BasicDBObject("$gte", ldtNow.toEpochSecond() - duration));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(RebootHistoryDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query);

        ArrayList<RebootHistoryDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = dbCursor.next();
            result.add(RebootHistoryDto.fromBasicDbObject((BasicDBObject) resultObject));
        }
        dbCursor.close();
        return result;
    }
}
