package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.IspConfigurationDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.swing.text.Document;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class IspConfigurationDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public List<IspConfigurationDto> listAllIspConfigurationDto() {

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(IspConfigurationDto.COLLECTION_ispConfiguration);
        DBCursor dbCursor = dbCollection.find();

        ArrayList<IspConfigurationDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            result.add(IspConfigurationDto.fromBasicDbObject(resultObject));
        }

        return result;
    }

    public void addIspConfiguration(HashMap<String, Object> configMap) {

        BasicDBObject configObj = new BasicDBObject(configMap);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(IspConfigurationDto.COLLECTION_ispConfiguration);
        dbCollection.save(configObj);
    }

    public List<IspConfigurationDto> findById(String configId) {

        BasicDBObject query = new BasicDBObject();
        query.put(IspConfigurationDto.IspConfigurationFields._id.name(), configId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(IspConfigurationDto.COLLECTION_ispConfiguration);
        DBCursor dbCursor = dbCollection.find(query);

        ArrayList<IspConfigurationDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            result.add(IspConfigurationDto.fromBasicDbObject(resultObject));
        }

        return result;
    }

    public void updateById(String configId, HashMap<String, Object> configMap) {

        BasicDBObject query = new BasicDBObject();
        query.put(IspConfigurationDto.IspConfigurationFields._id.name(), configId);

        BasicDBObject configObj = new BasicDBObject(configMap);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(IspConfigurationDto.COLLECTION_ispConfiguration);
        dbCollection.update(query, configObj);
    }

    public void removeById(String configId) {

        BasicDBObject query = new BasicDBObject();
        query.put(IspConfigurationDto.IspConfigurationFields._id.name(), configId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(IspConfigurationDto.COLLECTION_ispConfiguration);
        dbCollection.remove(query);
    }
}
