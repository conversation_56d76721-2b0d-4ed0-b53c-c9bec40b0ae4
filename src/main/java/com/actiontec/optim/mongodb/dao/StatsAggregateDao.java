package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.*;

@Component
public class StatsAggregateDao {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    At3Adapter at3Adapter;

    public static final String COLLECTION_statsPerIspPerHour = "statsPerIspPerHour";
    public static final String COLLECTION_statsPerIspPerDay = "statsPerIspPerDay";
    public static final String COLLECTION_statsPerUserPerHour = "statsPerUserPerHour";
    public static final String COLLECTION_statsPerUserPerDay = "statsPerUserPerDay";

    public static final String OPERATION_SUM = "$sum";
    public static final String OPERATION_AVG = "$avg";


    private String pipelineQueryIsp = "{ $match: { isp: '%s' }}";
    private String pipelineQueryUserId = "{ $match: { userId: '%s' }}";
    private String pipelineQueryStatsData = "{ $project: { statsData: { $objectToArray: '$statsData' }}}";
    private String pipelineQueryUnwindStatsData = "{ $unwind: '$statsData' }";
    private String pipelineQueryTime = "{ $match: { 'statsData.v.recordDateTime': { $gte: { $date: '%s' }}}}";

    public static int[] channels2g;
    public static int[] channels5g;
    public static int[] channels6g;

    static {
        channels2g = new int[]{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11};
        channels5g = new int[]{36, 40, 44, 48, 52, 56, 60, 64,
                100, 104, 108, 112, 116, 120, 124, 128, 132, 136, 140, 144, 149, 153, 157, 161, 165};
        channels6g = new int[]{1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 69, 73, 77, 81, 85, 89, 93, 97,
                101, 105, 109, 113, 117, 121, 125, 129, 133, 137, 141, 145, 149, 153, 157, 161, 165, 169, 173, 177, 181, 185, 189, 193, 197,
                201, 205, 209, 213, 217, 221, 225, 229, 233};
    }

    private Map<String, String> getAggregateParams(String equipmentType, RadioEnum radioEnum) {

        int channels[];

        if(radioEnum.getRadioKey().equals(ApplicationConstants.WIFI_2G)) {
            channels = channels2g;
        } else if(radioEnum.getRadioKey().equals(ApplicationConstants.WIFI_5G)) {
            channels = channels5g;
        } else if(radioEnum.getRadioKey().equals(ApplicationConstants.WIFI_6G)) {
            channels = channels6g;
        } else {
            return null;
        }

        Map<String, String> aggregateData = new HashMap<>();

        for(int ch : channels) {
            aggregateData.put("ch" + ch, "$statsData.v.channelDistribution." + equipmentType + "." + radioEnum.getAlias() + "." + ch);
        }

        return aggregateData;
    }

    public Map<String, Integer> getChannelData(String ispName, String userId, String equipmentType, RadioEnum radioEnum, Map<String, Object> dateTimeMap, String collection, String operation) {

        Map<String, Integer> statsData = new HashMap<>();
        List<DBObject> aggregatePipeline = new ArrayList<>();

        Map<String, String> aggregateData = getAggregateParams(equipmentType, radioEnum);
        if(aggregateData == null) {
            return null;
        }

        List<String> queryChannelData = new ArrayList<>();
        queryChannelData.add("{ $group: { _id: 'channelData'");
        for(Object key : aggregateData.keySet()) {
            queryChannelData.add(key.toString() + ": { " + operation + ": '" + aggregateData.get(key) + "' }");
        }
        queryChannelData.add("} }");

        aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject("statsDateTime", new BasicDBObject("$in", dateTimeMap.get("dates")))));

        if(ispName != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryIsp, ispName)));
        }

        if(userId != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryUserId, userId)));
        }

        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryStatsData)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryUnwindStatsData)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryTime, ((ZonedDateTime) dateTimeMap.get("startTime")).toInstant().toString())));
        aggregatePipeline.add(BasicDBObject.parse(String.format(String.join(",", queryChannelData))));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(collection);
        AggregationOutput aggregationResult = dbCollection.aggregate(aggregatePipeline);

        for(DBObject result : aggregationResult.results()) {
            for(Object key : aggregateData.keySet()) {
                statsData.put(key.toString(), ((BasicDBObject) result).getInt(key.toString()));
            }
        }
        return statsData;
    }

    public Float getAirTimeBusyData(String ispName, String userId, RadioEnum radioEnum, Map<String, Object> dateTimeMap, String collection, String operation) {

        Float statsData = 0.0f;
        List<DBObject> aggregatePipeline = new ArrayList<>();

        String aggregateData = "$statsData.v.radio." + radioEnum.getAlias() + ".occupancy";

        List<String> queryAirTimeBusyData = new ArrayList<>();
        queryAirTimeBusyData.add("{ $group: { _id: 'airTimeBusyData'");
        queryAirTimeBusyData.add("occupancy" + ": { " + operation + ": '" + aggregateData + "' }");
        queryAirTimeBusyData.add("} }");

        aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject("statsDateTime", new BasicDBObject("$in", dateTimeMap.get("dates")))));

        if(ispName != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryIsp, ispName)));
        }

        if(userId != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryUserId, userId)));
        }

        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryStatsData)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryUnwindStatsData)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryTime, ((ZonedDateTime) dateTimeMap.get("startTime")).toInstant().toString())));
        aggregatePipeline.add(BasicDBObject.parse(String.format(String.join(",", queryAirTimeBusyData))));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(collection);
        AggregationOutput aggregationResult = dbCollection.aggregate(aggregatePipeline);

        for(DBObject result : aggregationResult.results()) {
            statsData = Float.valueOf(((BasicDBObject) result).getString("occupancy"));
        }
        return statsData;
    }

    public Map<String, Float> getAirTimeBusyDistributionData(String ispName, String userId, RadioEnum radioEnum, Map<String, Object> dateTimeMap, String collection, String operation) {

        Map<String, Float> statsData = new HashMap<>();
        List<DBObject> aggregatePipeline = new ArrayList<>();

        List<String> queryAirTimeBusyDistributionData = new ArrayList<>();
        queryAirTimeBusyDistributionData.add("{ $group: { _id: 'airTimeBusyDistributionData'");
        queryAirTimeBusyDistributionData.add("'Very Low': { " + operation + ": '$statsData.v.metricLevels.perRadioOccupancy." + radioEnum.getAlias() + ".lt20' }");
        queryAirTimeBusyDistributionData.add("'Low': { " + operation + ": '$statsData.v.metricLevels.perRadioOccupancy." + radioEnum.getAlias() + ".gte20lt40' }");
        queryAirTimeBusyDistributionData.add("'Medium': { " + operation + ": '$statsData.v.metricLevels.perRadioOccupancy." + radioEnum.getAlias() + ".gte40lt60' }");
        queryAirTimeBusyDistributionData.add("'High': { " + operation + ": '$statsData.v.metricLevels.perRadioOccupancy." + radioEnum.getAlias() + ".gte60lt80' }");
        queryAirTimeBusyDistributionData.add("'Very High': { " + operation + ": '$statsData.v.metricLevels.perRadioOccupancy." + radioEnum.getAlias() + ".gte80' }");
        queryAirTimeBusyDistributionData.add("} }");

        aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject("statsDateTime", new BasicDBObject("$in", dateTimeMap.get("dates")))));

        if(ispName != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryIsp, ispName)));
        }

        if(userId != null) {
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryUserId, userId)));
        }

        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryStatsData)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryUnwindStatsData)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryTime, ((ZonedDateTime) dateTimeMap.get("startTime")).toInstant().toString())));
        aggregatePipeline.add(BasicDBObject.parse(String.format(String.join(",", queryAirTimeBusyDistributionData))));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(collection);
        AggregationOutput aggregationResult = dbCollection.aggregate(aggregatePipeline);

        for(DBObject result : aggregationResult.results()) {
            Map<String, Object> aggregateData = result.toMap();
            aggregateData.remove("_id");
            for(Object key : aggregateData.keySet()) {
                if (Objects.nonNull(aggregateData.get(key)))
                    statsData.put(key.toString(), Float.valueOf(aggregateData.get(key).toString()));
            }
        }
        return statsData;
    }
}
