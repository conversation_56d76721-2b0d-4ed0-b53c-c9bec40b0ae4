package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.HostDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class HostDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public List<HostDto> listByUserId(String userId, long baseNtTimestamp) {
        BasicDBObject query = new BasicDBObject();
        query.put(HostDto.Fields.userId.name(), userId);
        query.put(HostDto.Fields.ntTimestamp.name(), new BasicDBObject("$gte", baseNtTimestamp));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(HostDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        ArrayList<HostDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = (DBObject) dbCursor.next();
            result.add(HostDto.fromBasicDbObject((BasicDBObject) resultObject));
        }
        return result;
    }

    public List<HostDto> listByUserIdAndSerial(String userId, String serial, long baseNtTimestamp) {
        BasicDBObject query = new BasicDBObject();
        query.put(HostDto.Fields.userId.name(), userId);
        query.put(HostDto.Fields.serialNumber.name(), serial);
        query.put(HostDto.Fields.ntTimestamp.name(), new BasicDBObject("$gte", baseNtTimestamp));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(HostDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        ArrayList<HostDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = (DBObject) dbCursor.next();
            result.add(HostDto.fromBasicDbObject((BasicDBObject) resultObject));
        }
        return result;
    }

    public Optional<HostDto> findByUserIdAndSerialAndDeviceMac(String userId, String serial, String deviceMac, long baseNtTimestamp) {
        BasicDBObject query = new BasicDBObject();
        query.put(HostDto.Fields.userId.name(), userId);
        query.put(HostDto.Fields.serialNumber.name(), serial);
        query.put(HostDto.Fields.macAddress.name(), deviceMac);
        query.put(HostDto.Fields.ntTimestamp.name(), new BasicDBObject("$gte", baseNtTimestamp));
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(HostDto.COLLECTION);
        DBObject dbObject = dbCollection.findOne(query, new BasicDBObject());
        Optional<HostDto> result = Optional.ofNullable(dbObject).map(obj -> {
            return HostDto.fromBasicDbObject((BasicDBObject) obj);
        });

        return result;
    }

    public Map<String, HostDto> listByNetworkId(String networkId) {
        BasicDBObject query = new BasicDBObject();
        query.put(HostDto.Fields.userId.name(), networkId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(HostDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject());

        Map<String, HostDto> resultMap = new HashMap<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = dbCursor.next();
            HostDto host = HostDto.fromBasicDbObject((BasicDBObject) resultObject);

            if (host.getMacAddress() != null) {
                resultMap.put(host.getMacAddress(), host);
            }
        }
        return resultMap;
    }

    public Map<String, HostDto> findByNetworkIdAndDeviceMac(String networkId, String deviceMac) {
        BasicDBObject query = new BasicDBObject();
        query.put(HostDto.Fields.userId.name(), networkId);
        query.put(HostDto.Fields.macAddress.name(), deviceMac);

        BasicDBObject sort = new BasicDBObject("timestamp", -1);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(HostDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find(query, new BasicDBObject()).sort(sort);

        Map<String, HostDto> resultMap = new HashMap<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = dbCursor.next();
            HostDto host = HostDto.fromBasicDbObject((BasicDBObject) resultObject);

            String mac = host.getMacAddress();
            if (mac != null && !resultMap.containsKey(mac)) {
                resultMap.put(mac, host);
            }
        }
        return resultMap;
    }
}
