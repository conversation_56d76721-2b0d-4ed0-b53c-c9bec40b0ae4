package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.PonDataDto;
import com.actiontec.optim.mongodb.dto.RadioDataDto;
import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.mongodb.dto.VoipDataDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@Component
public class ApWiFiInsightsPerMinuteDao {
    private final Logger logger = LogManager.getLogger(this.getClass());
    public static final String COLLECTION_apWiFiInsightsPerMinute = "apWiFiInsightsPerMinute";

    @Autowired
    private At3Adapter at3Adapter;

    private String pipelineQuery0 = "{ $match: { userId: '%s', serialNumber: '%s' }}";
    private String pipelineQuery1 = "{ $project: { voipData: { $objectToArray: '$voipData' } } }";
    private String pipelineQuery2 = "{ $unwind: '$voipData' }";
    private String pipelineQuery3 = "{ $project: { voipData: '$voipData.v' } }";
    private String pipelineQuery4 = "{ $unwind: '$voipData' }";
    private String pipelineQuery5 = "{ $match: { 'voipData.timestamp': { $gte: %s } } }";

    public List<VoipDataDto> getVoipDataList(String serial, long duration) {
        logger.debug("findNetworkByUserIdAndSerial. serial:[{}] duration:[{}]", serial, duration);

        List<VoipDataDto> voipDataDtoList = new ArrayList<>();

        List<DBObject> aggregatePipeline = new ArrayList<>();
        aggregatePipeline.add((BasicDBObject.parse(String.format(pipelineQuery0, serial, serial))));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery1)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery2)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery3)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery4)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery5, String.valueOf(System.currentTimeMillis() - duration * 1000))));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(COLLECTION_apWiFiInsightsPerMinute);
        AggregationOutput aggregationResult = dbCollection.aggregate(aggregatePipeline);
        Iterator resultItr = aggregationResult.results().iterator();
        while (resultItr.hasNext()) {
            DBObject resultObject = (DBObject) resultItr.next();
            voipDataDtoList.add(VoipDataDto.fromBasicDbObject((BasicDBObject) resultObject.get("voipData")));
        }

        logger.debug("findNetworkByUserIdAndSerial succeeded. serial:[{}] duration:[{}] resultSize:[{}]", serial, duration, voipDataDtoList.size());
        return voipDataDtoList;
    }

    private String ponPipelineQuery0 = "{ $match: { userId: '%s', serialNumber: '%s' }}";
    private String ponPipelineQuery1 = "{ $project: { ponData: { $objectToArray: '$ponData' } } }";
    private String ponPipelineQuery2 = "{ $unwind: '$ponData' }";
    private String ponPipelineQuery3 = "{ $project: { ponData: '$ponData.v' } }";
    private String ponPipelineQuery4 = "{ $unwind: '$ponData' }";
    private String ponPipelineQuery5 = "{ $match: { 'ponData.timestamp': { $gte: %s } } }";

    public List<PonDataDto> getPonpDataList(String serial, long duration) {
        logger.debug("getPonpDataList. serial:[{}] duration:[{}]", serial, duration);

        List<PonDataDto> ponDataDtoList = new ArrayList<>();

        List<DBObject> aggregatePipeline = new ArrayList<>();
        aggregatePipeline.add((BasicDBObject.parse(String.format(ponPipelineQuery0, serial, serial))));
        aggregatePipeline.add(BasicDBObject.parse(String.format(ponPipelineQuery1)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(ponPipelineQuery2)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(ponPipelineQuery3)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(ponPipelineQuery4)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(ponPipelineQuery5, String.valueOf(System.currentTimeMillis() - duration * 1000))));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(COLLECTION_apWiFiInsightsPerMinute);
        AggregationOutput aggregationResult = dbCollection.aggregate(aggregatePipeline);
        Iterator resultItr = aggregationResult.results().iterator();
        while (resultItr.hasNext()) {
            DBObject resultObject = (DBObject) resultItr.next();
            ponDataDtoList.add(PonDataDto.fromBasicDbObject((BasicDBObject) resultObject.get("ponData")));
        }

        logger.debug("getPonpDataList succeeded. serial:[{}] duration:[{}] resultSize:[{}]", serial, duration, ponDataDtoList.size());
        return ponDataDtoList;
    }

    private String hourlyPipelineQuery0 = "{ $match: { userId: '%s', serialNumber: '%s' }}";
    private String hourlyPipelineQuery1 = "{ $project: { hourlyData: { $objectToArray: '$hourlyData' } } }";
    private String hourlyPipelineQuery2 = "{ $unwind: '$hourlyData' }";
    private String hourlyPipelineQuery3 = "{ $project: { hourlyData: '$hourlyData.v' } }";
    private String hourlyPipelineQuery4 = "{ $unwind: '$hourlyData' }";
    private String hourlyPipelineQuery5 = "{ $match: { 'hourlyData.timestamp': { $gte: %s } } }";

    public List<RadioDataDto> getHourlyDataList(String userId, String serial, String radioKey, long duration) {
        logger.debug("getHourlyDataList. userId:[{}] serial:[{}] duration:[{}]", userId, serial, duration);

        List<RadioDataDto> radioDataDtoList = new ArrayList<>();

        List<DBObject> aggregatePipeline = new ArrayList<>();
        aggregatePipeline.add((BasicDBObject.parse(String.format(hourlyPipelineQuery0, userId, serial))));
        aggregatePipeline.add(BasicDBObject.parse(String.format(hourlyPipelineQuery1)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(hourlyPipelineQuery2)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(hourlyPipelineQuery3)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(hourlyPipelineQuery4)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(hourlyPipelineQuery5, String.valueOf(System.currentTimeMillis() - duration * 1000))));

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(COLLECTION_apWiFiInsightsPerMinute);
        AggregationOptions aggregationOptions = AggregationOptions.builder().outputMode(AggregationOptions.OutputMode.CURSOR).build();
        Cursor cursor = dbCollection.aggregate(aggregatePipeline, aggregationOptions);
        
        while (cursor.hasNext()) {
            DBObject resultObject = cursor.next();
            DBObject hourlyData = (DBObject) resultObject.get("hourlyData");
            DBObject radioData = (DBObject) hourlyData.get("radio");
            DBObject wifiStaData = (DBObject) hourlyData.get("wifiSta");

            RadioEnum matchedRadioEnum = null;
            for(RadioEnum radioEnum: RadioEnum.values()){
                if(StringUtils.equals(radioEnum.getRadioKey(), radioKey)){
                    matchedRadioEnum = radioEnum;
                    DBObject radioObject = (DBObject) radioData.get(radioEnum.getAlias());
                    DBObject wifiStaObject = (DBObject) wifiStaData.get(radioEnum.getAlias());
                    radioDataDtoList.add(RadioDataDto.fromBasicDbObject((BasicDBObject) hourlyData, (BasicDBObject) radioObject, (BasicDBList) wifiStaObject));
                    break;
                }
            }

            if (matchedRadioEnum == null) {
                logger.warn("getHourlyDataList radio key compare fail serial:[{}] radioKey:[{}]", serial, radioKey);
            }
        }

        if(cursor != null) {
            cursor.close();
        }

        logger.debug("getHourlyDataList succeeded. serial:[{}] duration:[{}] resultSize:[{}]", serial, duration, radioDataDtoList.size());
        return radioDataDtoList;
    }
}
