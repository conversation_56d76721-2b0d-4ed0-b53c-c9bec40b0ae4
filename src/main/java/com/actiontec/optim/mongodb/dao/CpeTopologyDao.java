package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v6.dto.*;
import com.mongodb.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Component
public class CpeTopologyDao {
    public static final String COLLECTION_cpeTopology = "cpeTopology";

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    private List<IPAddressDTO> parseIpAddresses(BasicDBList ipList) {
        List<IPAddressDTO> result = new ArrayList<>();
        if (ipList == null) return result;

        for (Object ipObj : ipList) {
            BasicDBObject ip = (BasicDBObject) ipObj;
            IPAddressDTO ipDTO = new IPAddressDTO();
            ipDTO.setVersion(ip.getInt("version"));
            ipDTO.setAddress(ip.getString("address"));
            ipDTO.setType(ip.getString("type", ""));
            ipDTO.setLifetimeState(ip.getString("lifetimeState"));
            ipDTO.setLifetime(ip.getLong("lifetime"));
            result.add(ipDTO);
        }
        return result;
    }

    private List<ConnectionDTO> parseConnections(BasicDBList connList) {
        List<ConnectionDTO> result = new ArrayList<>();
        if (connList == null) return result;

        for (Object connObj : connList) {
            BasicDBObject conn = (BasicDBObject) connObj;
            logger.debug("conn:[{}]", conn.toJson());
            ConnectionDTO c = new ConnectionDTO();
            c.setType(conn.getString("type"));
            c.setUptime(conn.getInt("uptime", 0));

            if (conn.containsField("wireless")) {
                BasicDBObject w = (BasicDBObject) conn.get("wireless");
                WirelessDTO wDTO = new WirelessDTO();
                wDTO.setSsid(w.getString("ssid", ""));
                wDTO.setRadio(w.getString("radio"));
                wDTO.setRssi(w.getInt("rssi", 0));
                wDTO.setUplinkPhyRate(w.getLong("uplinkPhyRate", 0L));
                wDTO.setDownlinkPhyRate(w.getLong("downlinkPhyRate", 0L));
                c.setWireless(wDTO);
            }

            if (conn.containsField("ethernet")) {
                BasicDBObject e = (BasicDBObject) conn.get("ethernet");
                EthernetDTO eDTO = new EthernetDTO();
                eDTO.setPort(e.getInt("port"));
                eDTO.setBitRate(e.getLong("bitRate"));
                c.setEthernet(eDTO);
            }

            result.add(c);
        }
        return result;
    }

    private List<CapabilityDTO> parseCapabilities(BasicDBList capList) {
        List<CapabilityDTO> result = new ArrayList<>();
        if (capList == null) return result;

        for (Object capObj : capList) {
            BasicDBObject cap = (BasicDBObject) capObj;
            CapabilityDTO capDTO = new CapabilityDTO();
            capDTO.setType(cap.getString("type"));
            capDTO.setRadio(cap.getString("radio"));
            result.add(capDTO);
        }
        return result;
    }

    public List<SnapshotDataDTO> getTopologySnapshotData(String networkId, long startTime, long endTime, String deviceId) {

        List<BasicDBObject> pipeline = new ArrayList<>();

        pipeline.add(new BasicDBObject("$match", new BasicDBObject("network", networkId)));
        pipeline.add(new BasicDBObject("$unwind", "$data"));
        pipeline.add(new BasicDBObject("$match", new BasicDBObject("data.timeOfSnapshot",
                new BasicDBObject("$gte", startTime).append("$lte", endTime))));
        BasicDBObject groupFields = new BasicDBObject("_id", "$_id");
        groupFields.put("data", new BasicDBObject("$push", "$data"));
        pipeline.add(new BasicDBObject("$group", groupFields));

        List<SnapshotDataDTO> resultList = new ArrayList<>();

        Iterable<DBObject> results = at3Adapter.getMongoDbCollection(COLLECTION_cpeTopology)
                .aggregate(pipeline)
                .results();

        for (DBObject groupResult : results) {
            BasicDBList dataList = (BasicDBList) groupResult.get("data");

            for (Object dataObj : dataList) {
                BasicDBObject data = (BasicDBObject) dataObj;

                BasicDBList devList = (BasicDBList) data.get("devices");
                List<DeviceDTO> devices = new ArrayList<>();
                if (devList != null) {
                    for (Object devObj : devList) {
                        BasicDBObject dev = (BasicDBObject) devObj;

                        if (deviceId != null && !deviceId.equals(dev.getString("id"))) {
                            continue;
                        }

                        DeviceDTO d = new DeviceDTO();
                        d.setId(dev.getString("id"));
                        d.setHostname(dev.getString("hostname"));
                        d.setMacAddress(dev.getString("macAddress"));
                        d.setStatus(dev.getString("status"));
                        d.setType(dev.getString("type"));
                        d.setUpstreamId(dev.getString("upstreamId"));
                        d.setLastChangeTime(dev.getLong("lastChangeTime"));

                        d.setIpAddresses(parseIpAddresses((BasicDBList) dev.get("ipAddresses")));
                        d.setConnections(parseConnections((BasicDBList) dev.get("connections")));
                        d.setCapabilities(parseCapabilities((BasicDBList) dev.get("capabilities")));

                        devices.add(d);
                    }
                }

                if (devices.isEmpty()) {
                    continue;
                }

                SnapshotDataDTO snapshot = new SnapshotDataDTO();
                snapshot.setDevices(devices);

                BasicDBList radioList = (BasicDBList) data.get("radios");
                List<RadioDTO> radios = new ArrayList<>();
                if (radioList != null) {
                    for (Object radioObj : radioList) {
                        BasicDBObject radio = (BasicDBObject) radioObj;
                        RadioDTO r = new RadioDTO();
                        r.setBand(radio.getString("band"));
                        r.setEnabled(radio.getBoolean("enable"));
                        radios.add(r);
                    }
                }

                snapshot.setRadios(radios);
                snapshot.setTimeOfSnapshot(data.getLong("timeOfSnapshot"));
                resultList.add(snapshot);
            }
        }

        return resultList;
    }

    public List<DeviceDTO> getTopologyDevices(String networkId, String serial, String deviceId) {
        List<BasicDBObject> pipeline = new ArrayList<>();

        pipeline.add(new BasicDBObject("$match",
                new BasicDBObject("$and", Arrays.asList(
                        new BasicDBObject("network", networkId),
                        new BasicDBObject("serial", serial)
                ))
        ));
        pipeline.add(new BasicDBObject("$sort", new BasicDBObject("date", -1)));
        pipeline.add(new BasicDBObject("$limit", 1));
        pipeline.add(new BasicDBObject("$unwind", "$data"));
        pipeline.add(new BasicDBObject("$sort", new BasicDBObject("data.timeOfSnapshot", -1)));
        pipeline.add(new BasicDBObject("$limit", 1));

        BasicDBObject project = new BasicDBObject("_id", 0);

        if (deviceId != null) {
            // $filter macAddress
            BasicDBObject filter = new BasicDBObject("$filter", new BasicDBObject()
                    .append("input", "$data.devices")
                    .append("as", "device")
                    .append("cond", new BasicDBObject("$eq", Arrays.asList("$$device.macAddress", deviceId)))
            );
            project.append("devices", filter);
        } else {
            project.append("devices", "$data.devices");
        }

        pipeline.add(new BasicDBObject("$project", project));

        Iterable<DBObject> results = at3Adapter.getMongoDbCollection(COLLECTION_cpeTopology)
                .aggregate(pipeline)
                .results();

        List<DeviceDTO> devices = new ArrayList<>();

        for (DBObject result : results) {
            BasicDBList deviceList = (BasicDBList) result.get("devices");
            if (deviceList != null) {
                for (Object device : deviceList) {
                    BasicDBObject deviceObj = (BasicDBObject) device;

                    DeviceDTO d = new DeviceDTO();
                    d.setId(deviceObj.getString("id"));
                    d.setHostname(deviceObj.getString("hostname"));
                    d.setMacAddress(deviceObj.getString("macAddress"));
                    d.setStatus(deviceObj.getString("status"));
                    d.setType(deviceObj.getString("type"));
                    d.setUpstreamId(deviceObj.getString("upstreamId"));
                    d.setLastChangeTime(deviceObj.getLong("lastChangeTime"));

                    d.setIpAddresses(parseIpAddresses((BasicDBList) deviceObj.get("ipAddresses")));
                    d.setConnections(parseConnections((BasicDBList) deviceObj.get("connections")));
                    d.setCapabilities(parseCapabilities((BasicDBList) deviceObj.get("capabilities")));

                    devices.add(d);
                }
            }
        }

        return devices;
    }
}
