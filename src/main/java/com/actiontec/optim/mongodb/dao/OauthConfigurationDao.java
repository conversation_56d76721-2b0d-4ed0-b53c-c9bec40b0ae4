package com.actiontec.optim.mongodb.dao;

import com.actiontec.optim.mongodb.dto.OauthConfigDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class OauthConfigurationDao {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    public List<OauthConfigDto> findAll() {
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(OauthConfigDto.COLLECTION);
        DBCursor dbCursor = dbCollection.find();

        List<OauthConfigDto> result = new ArrayList<>();
        while (dbCursor.hasNext()) {
            DBObject resultObject = dbCursor.next();
            result.add(OauthConfigDto.fromBasicDbObject((BasicDBObject) resultObject));
        }
        dbCursor.close();
        return result;
    }

    public OauthConfigDto findById(String configId) {

        BasicDBObject query = new BasicDBObject();
        query.put("_id", configId);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(OauthConfigDto.COLLECTION);
        DBObject dbObject = dbCollection.findOne(query, new BasicDBObject());

        Optional<OauthConfigDto> result = Optional.ofNullable(dbObject).map(obj -> {
            return OauthConfigDto.fromBasicDbObject((BasicDBObject) obj);
        });

        if(result.isPresent()) {
            return result.get();
        }

        return null;
    }

    public OauthConfigDto findByDefault() {

        BasicDBObject query = new BasicDBObject();
        query.put("isDefaultProvider", true);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(OauthConfigDto.COLLECTION);
        DBObject dbObject = dbCollection.findOne(query, new BasicDBObject());

        Optional<OauthConfigDto> result = Optional.ofNullable(dbObject).map(obj -> {
            return OauthConfigDto.fromBasicDbObject((BasicDBObject) obj);
        });

        if(result.isPresent()) {
            return result.get();
        }

        return null;
    }

    public OauthConfigDto findByName(String configName) {

        BasicDBObject query = new BasicDBObject();
        query.put("name", configName);

        DBCollection dbCollection = at3Adapter.getMongoDbCollection(OauthConfigDto.COLLECTION);
        DBObject dbObject = dbCollection.findOne(query, new BasicDBObject());

        Optional<OauthConfigDto> result = Optional.ofNullable(dbObject).map(obj -> {
            return OauthConfigDto.fromBasicDbObject((BasicDBObject) obj);
        });

        if(result.isPresent()) {
            return result.get();
        }

        return null;
    }
}
