package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.model.DnsServer;
import com.actiontec.optim.platform.model.IpType;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class DnsServerMapper {
    private static final Logger logger = LogManager.getLogger(DnsServerMapper.class);

    public abstract DnsServer toDnsServer(ApDetailDto.NetworkDto.DnsServerDto.Ipv6Dto dnsServerIpv6Dto);

    public abstract List<DnsServer> toDnsServers(List<ApDetailDto.NetworkDto.DnsServerDto.Ipv6Dto> dnsServerIpv6Dtos);

    public DnsServer toDnsServer(ApDetailDto.NetworkDto.DnsServerDto.Ipv6Dto dnsServerIpv6Dto, IpType ipType) {
        DnsServer dnsServer = toDnsServer(dnsServerIpv6Dto);
        dnsServer.setIpType(ipType);
        return dnsServer;
    }

    public List<DnsServer> toDnsServers(List<ApDetailDto.NetworkDto.DnsServerDto.Ipv6Dto> dnsServerIpv6Dtos, IpType ipType) {
        List<DnsServer> dnsServers = toDnsServers(dnsServerIpv6Dtos);
        dnsServers.stream().forEach(dnsServer -> dnsServer.setIpType(ipType));
        return dnsServers;
    }
}
