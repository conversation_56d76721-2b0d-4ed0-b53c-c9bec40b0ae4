package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.model.NetworkMoca;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public abstract class NetworkMocaMapper {
    private static final Logger logger = LogManager.getLogger(NetworkMocaMapper.class);

    public abstract NetworkMoca toNetworkMoca(ApDetailDto.MocaDto mocaDto);

    @AfterMapping
    protected void afterNetworkMocaMapping(ApDetailDto.MocaDto mocaDto, @MappingTarget NetworkMoca networkMoca) {
        networkMoca.setId(String.valueOf(mocaDto.getPortNo()));
        networkMoca.setLastChangeTime(mocaDto.getLastChangeTime() * 1000);
        switch (mocaDto.getStatus()) {
            case 0:
                networkMoca.setStatus("Down");
                break;
            case 1:
                networkMoca.setStatus("Up");
                break;
            default:
                logger.error("Failed to convert Moca status:[{}]", mocaDto.getStatus());
                throw new RuntimeException("Failed to convert Moca status");
        }
    }

    public abstract NetworkMoca.MocaStats toNetworkMocaStats(ApDetailDto.MocaDto.MocaStatsDto mocaStatsDto);

    @Mapping(target = "transmitPhyRate", source = "txPhyRate")
    @Mapping(target = "receivePhyRate", source = "rxPhyRate")
    @Mapping(target = "transmitPowerLevel", source = "txPower")
    @Mapping(target = "receivePowerLevel", source = "rxPower")
    public abstract NetworkMoca.MocaNode toNetworkMocaNode(ApDetailDto.MocaDeviceDto mocaDeviceDto);

    public abstract List<NetworkMoca> toNetworkMoca(List<ApDetailDto.MocaDto> mocaDtos);

    public NetworkMoca.MocaNode toNetworkMocaNode(ApDetailDto.MocaDeviceDto mocaDeviceDto, long sendingTime) {
        NetworkMoca.MocaNode mocaNode = toNetworkMocaNode(mocaDeviceDto);
        mocaNode.setUptime(sendingTime - mocaNode.getUptime() * 1000);
        return mocaNode;
    }

    public List<NetworkMoca.MocaNode> toNetworkMocaNodes(List<ApDetailDto.MocaDeviceDto> mocaDeviceDtos, long sendingTime) {
        List<NetworkMoca.MocaNode> mocaNodes = new ArrayList<>();
        for(ApDetailDto.MocaDeviceDto mocaDeviceDto:mocaDeviceDtos){
            NetworkMoca.MocaNode mocaNode = toNetworkMocaNode(mocaDeviceDto, sendingTime);
            mocaNodes.add(mocaNode);
        }

        return mocaNodes;
    }
}
