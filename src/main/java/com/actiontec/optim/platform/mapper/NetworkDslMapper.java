package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.model.NetworkDsl;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class NetworkDslMapper {

    public abstract List<NetworkDsl.LinesDto> toLinesDto(List<ApDetailDto.DslDto> dslDto);

    @AfterMapping
    protected void afterMapping(ApDetailDto.DslDto dslDto, @MappingTarget NetworkDsl.LinesDto linesDto) {
        linesDto.setLastChangeTime(dslDto.getLastChange());
        linesDto.setErroredSeconds(dslDto.getErroredSecs());
        linesDto.setSeverelyErroredSeconds(dslDto.getSeverelyErroredSecs());
    }
}
