package com.actiontec.optim.platform.mapper.equipment;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.mapper.DnsServerMapper;
import com.actiontec.optim.platform.mapper.Ipv6AddressMapper;
import com.actiontec.optim.platform.model.DnsServer;
import com.actiontec.optim.platform.model.equipment.NetworkDownstreamContext;
import com.actiontec.optim.platform.model.equipment.NetworkDownstreamInterface;
import com.actiontec.optim.platform.model.equipment.NetworkUpstreamContext;
import com.actiontec.optim.platform.model.equipment.NetworkUpstreamInterface;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class NetworkStreamContextMapper {

    @Autowired
    private Ipv6AddressMapper ipv6AddressMapper;

    @Autowired
    private DnsServerMapper dnsServerMapper;

    public NetworkUpstreamContext toNetworkUpstreamContext(ApDetailDto apDetailDto) {
        NetworkUpstreamContext networkUpstreamContext = new NetworkUpstreamContext();

        ApDetailDto.NetworkDto networkDto = apDetailDto.getNetworkDto();
        if (networkDto != null) {
            List<ApDetailDto.NetworkDto.InterfaceDto> interfaceDtos = Arrays.asList(networkDto.getInterfaces()).stream()
                    .filter(interfaceDto -> interfaceDto.isUpstream())
                    .collect(Collectors.toList());

            List<NetworkUpstreamInterface> upstreamInterfaces = new ArrayList<>();
            int i = 0;
            for (ApDetailDto.NetworkDto.InterfaceDto interfaceDto : interfaceDtos) {
                i++;
                NetworkUpstreamInterface upstreamInterface = new NetworkUpstreamInterface();
                upstreamInterface.setId(String.valueOf(i));
                upstreamInterface.setLastReportTime(apDetailDto.getReceivedTime() * 1000);

                ApDetailDto.NetworkDto.InterfaceDto.Ipv6Dto ipv6Dto = interfaceDto.getIpv6();
                if (ipv6Dto != null) {
                    upstreamInterface.setIpv6Enabled(ipv6Dto.isEnabled());
                    upstreamInterface.setIpv6Addresses(ipv6AddressMapper.toIpv6Addresses(ipv6Dto.getAddresses()));
                    upstreamInterface.setIpv6Protocol(ipv6Dto.getProtocol());
                    upstreamInterface.setIpv6DefaultGateway(ipv6Dto.getDefaultGateway());
                }

                upstreamInterfaces.add(upstreamInterface);
            }
            networkUpstreamContext.setInterfaces(upstreamInterfaces);

            if (networkDto.getDnsServer() != null) {
                if (networkDto.getDnsServer().getIpv6s() != null) {
                    List<DnsServer> ipv6DnsServers = dnsServerMapper.toDnsServers(networkDto.getDnsServer().getIpv6s());
                    networkUpstreamContext.setIpv6DnsServers(ipv6DnsServers);
                }
            }
        }

        networkUpstreamContext.setWanDto(apDetailDto.getWanDto());
        networkUpstreamContext.setLastReportTime(apDetailDto.getNtReportTimestamp());

        return networkUpstreamContext;
    }

    public NetworkDownstreamContext toNetworkDownstreamContext(ApDetailDto apDetailDto) {
        NetworkDownstreamContext networkDownstreamContext = new NetworkDownstreamContext();

        ApDetailDto.NetworkDto networkDto = apDetailDto.getNetworkDto();
        if (networkDto != null) {
            List<ApDetailDto.NetworkDto.InterfaceDto> interfaceDtos = Arrays.asList(networkDto.getInterfaces()).stream()
                    .filter(interfaceDto -> !(interfaceDto.isUpstream()))
                    .collect(Collectors.toList());

            List<NetworkDownstreamInterface> downstreamInterfaces = new ArrayList<>();
            int i = 0;
            for (ApDetailDto.NetworkDto.InterfaceDto interfaceDto : interfaceDtos) {
                i++;
                NetworkDownstreamInterface downstreamInterface = new NetworkDownstreamInterface();
                downstreamInterface.setId(String.valueOf(i));
                downstreamInterface.setLastReportTime(apDetailDto.getReceivedTime() * 1000);

                ApDetailDto.NetworkDto.InterfaceDto.Ipv6Dto ipv6Dto = interfaceDto.getIpv6();
                if (ipv6Dto != null) {
                    downstreamInterface.setIpv6Enabled(ipv6Dto.isEnabled());
                    downstreamInterface.setIpv6Addresses(ipv6AddressMapper.toIpv6Addresses(ipv6Dto.getAddresses()));
                }

                downstreamInterfaces.add(downstreamInterface);
            }
            networkDownstreamContext.setInterfaces(downstreamInterfaces);
        }

        return networkDownstreamContext;
    }
}
