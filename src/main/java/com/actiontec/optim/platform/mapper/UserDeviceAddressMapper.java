package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.HostDto;
import com.actiontec.optim.platform.model.UserDeviceAddress;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UserDeviceAddressMapper {
    public UserDeviceAddress toUserDeviceAddress(HostDto.AddressDto addressDto);

    public List<UserDeviceAddress> toUserDeviceAddresses(List<HostDto.AddressDto> addressDtos);
}
