package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.FwVerDistributionDto;
import com.actiontec.optim.platform.model.FwVerDistribution;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface FwVerDistributionMapper {

    public FwVerDistribution toFwVerDistribution(FwVerDistributionDto fwVerDistributionDto);

    public List<FwVerDistribution> toFwVerDistribution(List<FwVerDistributionDto> fwVerDistributionDtos);
}