package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.CpeSmmAppDto;
import com.actiontec.optim.platform.api.v5.model.SmmContainerRequest;
import com.actiontec.optim.platform.api.v5.model.SmmLifeCycleActionRequest;
import com.actiontec.optim.platform.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface SmmContainerMapper {
    
    @Mapping(target = "serviceId", source = "service.serviceId")
    @Mapping(target = "applicationId", source = "smmApplication.id")
    @Mapping(target = "version", source = "smmApplication.version")
    @Mapping(target = "applicationUrl", source = "smmApplication.fileUrl")
    @Mapping(target = "applicationSize", source = "smmApplication.fileSize")
    public SmmContainerPayloadService toPayloadService(SmmApplication smmApplication, SmmContainerRequest.Service service);

    public SmmContainer.System toSmmContainerSystem(CpeSmmAppDto.SystemDto systemDto);

    @Mapping(target = "id", source = "cpeSmmAppDto.serviceId")
    @Mapping(target = "currentVersion", source = "cpeSmmAppDto.rptVersion")
    @Mapping(target = "targetVersion", source = "cpeSmmAppDto.serviceVer")
    @Mapping(target = "serviceStatus", source = "cpeSmmAppDto.desiredState")
    @Mapping(target = "provisionStatus", source = "cpeSmmAppDto.rptProvisionStatus")
    @Mapping(target = "actionStatus", source = "cpeSmmAppDto.actionState")
    public SmmLifeCycle toSmmLifeCycle(CpeSmmAppDto cpeSmmAppDto);

    @Mapping(target = "version", source = "smmLifeCycleActionRequest.targetVersion")
    public SmmContainerRequest.Service toSmmRequestService(String serviceId, SmmLifeCycleActionRequest smmLifeCycleActionRequest);

    @Mapping(target = "serviceId", source = "serviceBody.id")
    @Mapping(target = "version", source = "serviceBody.targetVersion")
    public SmmContainerRequest.Service toSmmRequestService(SmmServiceBody serviceBody);
}
