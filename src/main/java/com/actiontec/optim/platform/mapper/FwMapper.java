package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.platform.model.firmware.FwLog;
import com.actiontec.optim.platform.model.firmware.ResponsePayload;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public abstract class FwMapper {

    @Mapping(target = "action", source = "payload.action", qualifiedByName = "toUpperCase4Index0")
    @Mapping(target = "fileEntry", source = "payload.data.url")
    @Mapping(target = "resultState", source = "payload.result.state", qualifiedByName = "toUpperCase4Index0")
    @Mapping(target = "startTime", source = "payload.result.startTime", qualifiedByName = "toMs")
    @Mapping(target = "endTime", source = "payload.result.completeTime", qualifiedByName = "toMs")
    @Mapping(target = "faultCode", source = "payload.result.faultCode")
    public abstract FwLog toFwlog(ResponsePayload payload);

    @Named("toUpperCase4Index0")
    public static String toUpperCase4Index0(String string) {
        char[] charArray = string.toCharArray();
        if (97 <= charArray[0] && charArray[0] <= 122) {
            charArray[0]^= 32;
        }
        return String.valueOf(charArray);
    }


    @Named("toMs")
    public static long toMs(long in) {
        return in*1000;
    }
}
