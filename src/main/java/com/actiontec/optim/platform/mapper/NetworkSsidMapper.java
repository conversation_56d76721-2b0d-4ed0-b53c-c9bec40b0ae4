package com.actiontec.optim.platform.mapper;


import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.model.NetworkSsid;
import com.actiontec.optim.platform.model.SsidCompositeKey;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public abstract class NetworkSsidMapper {

    @Mapping(target = "bssids", source = "bssid", qualifiedByName = "singleElementToList")
    @Mapping(target = "name", source = "ssid")
    @Mapping(target = "radioEnums", source = "radioEnum", qualifiedByName = "singleElementToList")
    public abstract NetworkSsid toNetworkSsid(ApDetailDto.SsidDto ssidDto);

    public abstract List<NetworkSsid> toNetworkSsid(List<ApDetailDto.SsidDto> ssidDtos);

    @AfterMapping
    protected void afterMapping(ApDetailDto.SsidDto ssidDto, @MappingTarget NetworkSsid networkSsid) {
        ArrayList<SsidCompositeKey> ssidCompositeKeys = new ArrayList<>();
        ssidCompositeKeys.add(new SsidCompositeKey(ssidDto.getRadioEnum().getRadioKey(), ssidDto.getSsidKey()));
        networkSsid.setSsidCompositeKey(ssidCompositeKeys);
        networkSsid.setSerial(ssidDto.getSerialNumber());
    }

    @Named("singleElementToList")
    public static <T> ArrayList<T> mapSingleElementToList(T t) {
        ArrayList<T> result = new ArrayList<>();
        result.add(t);
        return result;
    }
}
