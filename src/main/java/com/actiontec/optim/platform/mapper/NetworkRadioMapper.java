package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.RadioDataDto;
import com.actiontec.optim.platform.model.NetworkRadio;
import com.actiontec.optim.platform.model.NetworkRadioSeries;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface NetworkRadioMapper {

    @Mapping(target = "enabled", source = "enable")
    @Mapping(target = "autoChannelEnabled", source = "autoChannelEnable")
    @Mapping(target = "dfsEnabled", source = "dfsEnable")
    public NetworkRadio toNetworkRadio(ApDetailDto.RadioDto radioDto);

    public List<NetworkRadio> toNetworkRadio(List<ApDetailDto.RadioDto> radioDtos);

    public List<NetworkRadioSeries> toNetworkRadioSeries(List<RadioDataDto> radioDataDtoList);

}
