package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.StationSpeedTestDto;
import com.actiontec.optim.platform.model.StationSpeedTest;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface StationSpeedTestMapper {
    public StationSpeedTest toStationSpeedTest(StationSpeedTestDto stationSpeedTestDto);

    public List<StationSpeedTest> toStationSpeedTests(List<StationSpeedTestDto> stationSpeedTestDtos);
}
