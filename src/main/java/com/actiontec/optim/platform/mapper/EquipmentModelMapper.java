package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.platform.api.v5.model.ModelHardwareDto;
import com.actiontec.optim.platform.api.v5.model.ModelSnapshotResponse;
import com.actiontec.optim.platform.model.ModelHardware;
import com.actiontec.optim.platform.model.ModelSnapshot;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface EquipmentModelMapper {

    public ModelSnapshotResponse toModelSnapshotResponse(ModelSnapshot modelSnapshot);
    public ModelHardwareDto toModelHardwareDto(ModelHardware modelHardware);
}
