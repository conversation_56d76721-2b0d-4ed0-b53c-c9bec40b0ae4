package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.platform.model.SmmApplication;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.entities.SmmApp;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class SmmApplicationMapper {

    @Mapping(target = "dependencies", source = "dependencies", qualifiedByName = "listToString")
    public abstract SmmApp toSmmApp(SmmApplication smmApplication);

    @Named("listToString")
    public static String listToString(List<SmmApplication.Dependency> dependencyList) throws Exception {
        String dependencies = new ObjectMapper().writeValueAsString(dependencyList);
        return dependencies;
    }
}
