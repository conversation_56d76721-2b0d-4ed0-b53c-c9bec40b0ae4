package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.OauthConfigDto;
import com.actiontec.optim.platform.model.OauthConfig;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OauthConfigMapper {

    public OauthConfig toOauthConfig(OauthConfigDto oauthConfigDto);
    public List<OauthConfig> toOauthConfigs(List<OauthConfigDto> oauthConfigDtos);
}
