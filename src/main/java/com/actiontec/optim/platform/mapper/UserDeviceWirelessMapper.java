package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.mongodb.dto.WifiStationDto;
import com.actiontec.optim.platform.model.UserDeviceWireless;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface UserDeviceWirelessMapper {
    public UserDeviceWireless toUserDeviceWireless(WifiStationDto wifiStationDto);

    public List<UserDeviceWireless> toUserDeviceWirelesses(List<WifiStationDto> wifiStationDtos);
}
