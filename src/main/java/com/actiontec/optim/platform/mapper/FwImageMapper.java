package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.platform.model.FwImage;
import com.incs83.app.entities.Firmware;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface FwImageMapper {

    @Mapping(target = "location", source = "fileLocation")
    @Mapping(target = "seqVersion", source = "sequenceVersion")
    @Mapping(target = "production", source = "isProduction")
    public Firmware toFirmware(FwImage fwImage);

    @Mapping(target = "fileLocation", source = "location")
    @Mapping(target = "sequenceVersion", source = "seqVersion")
    @Mapping(target = "isProduction", source = "production")
    public FwImage toFwImage(Firmware firmware);
}
