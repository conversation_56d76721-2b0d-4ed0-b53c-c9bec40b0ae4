package com.actiontec.optim.platform.mapper;

import com.actiontec.optim.platform.api.v5.model.SmmJobRequest;
import com.actiontec.optim.platform.api.v5.model.SmmServiceVo;
import com.incs83.app.entities.SmmJob;
import com.incs83.app.entities.SmmService;
import org.mapstruct.Mapper;

import java.util.Date;
import java.util.List;

@Mapper(componentModel = "spring")
public interface SmmServiceMapper {
    public SmmServiceVo toSmmServiceVo(SmmService smmService, List<String> versions);
    public SmmJob toSmmJob(String id, String groupId, String sourceVersion, String ispId, Date createdAt, String state, SmmJobRequest smmJobRequest);
}
