package com.actiontec.optim.platform.constant;

public interface ActiontecSQL {
    String GET_VERSION_FROM_FILE = "SELECT version FROM firmware fw INNER JOIN file f ON fw.fileId = f.id WHERE f.secureUrl = :secureUrl";
    String UPDATE_FIRMWARE_PRODUCTION = "update firmware set production=false where id in (select emf.firmware_id from equipment_model_firmware emf group by emf.firmware_id having sum(case when emf.equipment_model_id in (:equipmentModelIds) then 1 else 0 end) = :equipmentModelCount and count(distinct emf.equipment_model_id) = :equipmentModelCount)";
    String GET_FIRMWARE_BY_GROUP_AND_VERSION = "SELECT DISTINCT f FROM Firmware f JOIN FETCH f.equipmentModelFirmwares WHERE f.groupId = :groupId AND f.version = :version";

    String GET_TRACKING_EQUIPMENT_BY_SN = "from TrackingEquipment where serial =:serial";
    String UPDATE_TRACKING_EQUIPMENT_ONBOARD_INFO = "UPDATE TrackingEquipment SET uId =:uId, onboardAt=:onboardAt, onboardBy=:onboardBy WHERE id =:id";
    String CLEAR_TRACKING_EQUIPMENT_UID = "UPDATE TrackingEquipment SET uId = NULL WHERE id =:id";
    String GET_TRACKING_EQUIPMENT_COUNT = "SELECT count(id) FROM TrackingEquipment";
    String GET_TRACKING_EQUIPMENTS = "FROM TrackingEquipment";
    String GET_SUBSCRIBER_EXTRA_DATA = "FROM SubscriberExtraData where subscriber_id =:subscriber_id";
    String GET_USER_BY_EMAIL = "from User where email = :email";


}
