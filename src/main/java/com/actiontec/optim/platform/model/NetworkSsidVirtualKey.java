package com.actiontec.optim.platform.model;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.service.SimpleAesEcbCryptoService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.LinkedHashMap;
import java.util.List;

public class NetworkSsidVirtualKey {
    private static final Logger logger = LogManager.getLogger("com.actiontec.optim.platform.model.NetworkSsidVirtualKey");

    private static final String FIELD_serial = "serial";
    private static final String FIELD_ssidCompositeKeys = "ssidCompositeKeys";

    private String serial;

    private SsidCompositeKey[] ssidCompositeKeys;

    public static NetworkSsidVirtualKey fromVirtualKeyString(SimpleAesEcbCryptoService cryptoService, String virtualKeyStr) {
        try {
            String jsonStr = cryptoService.decrypt(virtualKeyStr, ApplicationConstants.ENCRYPT_KEY);
            ObjectMapper objectMapper = new ObjectMapper();
            NetworkSsidVirtualKey networkSsidVirtualKey = objectMapper.readValue(jsonStr, NetworkSsidVirtualKey.class);
            return networkSsidVirtualKey;
        } catch (Exception e) {
            logger.error("failed to parse NetworkSsidVirtualKey:[{}]", virtualKeyStr, e);
            throw new RuntimeException(e);
        }
    }

    //XXX: leave a default constructer for jackson mapper, bad design
    public NetworkSsidVirtualKey(){

    }

    public NetworkSsidVirtualKey(String serial, SsidCompositeKey[] ssidCompositeKeys) {
        this.serial = serial;
        this.ssidCompositeKeys = ssidCompositeKeys;
    }

    public NetworkSsidVirtualKey(String serial, List<SsidCompositeKey> ssidCompositeKeys) {
        this.serial = serial;
        this.ssidCompositeKeys = ssidCompositeKeys.toArray(new SsidCompositeKey[ssidCompositeKeys.size()]);
    }

    public String toJsonString() {
        LinkedHashMap<String, Object> jsonMap = new LinkedHashMap();
        jsonMap.put(FIELD_serial, serial);
        jsonMap.put(FIELD_ssidCompositeKeys, ssidCompositeKeys);

        String result = "";
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            result = objectMapper.writeValueAsString(jsonMap);
        } catch (JsonProcessingException e) {
            // should not happen
            logger.error("toJson failed. [{}]", e);
            throw new RuntimeException(e);
        }
        return result;
    }

    public String getEncryptedString(SimpleAesEcbCryptoService cryptoService) {
        String jsonStr = toJsonString();
        String result = "";
        try {
            result = cryptoService.encrypt(jsonStr, ApplicationConstants.ENCRYPT_KEY);
        } catch (Exception e) {
            // should not happend
            logger.error("toJson failed. [{}]", e);
            throw new RuntimeException(e);
        }
        return result;
    }

    public String getSerial() {
        return serial;
    }

    public SsidCompositeKey[] getSsidCompositeKeys() {
        return ssidCompositeKeys;
    }

    //XXX: leave a setter for jackson mapper, bad design
    public void setSerial(String serial) {
        this.serial = serial;
    }

    //XXX: leave a setter for jackson mapper, bad design
    public void setSsidCompositeKeys(SsidCompositeKey[] ssidCompositeKeys) {
        this.ssidCompositeKeys = ssidCompositeKeys;
    }
}
