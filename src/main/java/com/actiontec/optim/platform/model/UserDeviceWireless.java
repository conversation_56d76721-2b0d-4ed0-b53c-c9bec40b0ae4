package com.actiontec.optim.platform.model;

public class UserDeviceWireless {
    private String userId;
    private String serialNumber;
    private String macAddress;
    private String operatingStandards;
    private long rssi;
    private long bytesSent;
    private long bytesReceived;
    private double avgBytesSent;
    private double avgBytesReceived;
    private long errorsSent;
    private long retransSent;
    private long lastDataDownlinkRate;
    private long lastDataUplinkRate;
    private double airTimePercentage;
    private String bssid;
    private String radioKey;
    private String ssidKey;
    private boolean dualBandSupported;
    private boolean dfsSupported;
    private boolean ieee80211kSupported;
    private boolean ieee80211vSupported;
    private boolean ieee80211rSupported;
    private long sendingTime;
    private long timestamp;
    private long ntTimestamp;
    private String friendlyName;
    private String ssid;
    private int channel;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getOperatingStandards() {
        return operatingStandards;
    }

    public void setOperatingStandards(String operatingStandards) {
        this.operatingStandards = operatingStandards;
    }

    public long getRssi() {
        return rssi;
    }

    public void setRssi(long rssi) {
        this.rssi = rssi;
    }

    public long getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(long bytesSent) {
        this.bytesSent = bytesSent;
    }

    public long getBytesReceived() {
        return bytesReceived;
    }

    public void setBytesReceived(long bytesReceived) {
        this.bytesReceived = bytesReceived;
    }

    public double getAvgBytesSent() {
        return avgBytesSent;
    }

    public void setAvgBytesSent(double avgBytesSent) {
        this.avgBytesSent = avgBytesSent;
    }

    public double getAvgBytesReceived() {
        return avgBytesReceived;
    }

    public void setAvgBytesReceived(double avgBytesReceived) {
        this.avgBytesReceived = avgBytesReceived;
    }

    public long getErrorsSent() {
        return errorsSent;
    }

    public void setErrorsSent(long errorsSent) {
        this.errorsSent = errorsSent;
    }

    public long getRetransSent() {
        return retransSent;
    }

    public void setRetransSent(long retransSent) {
        this.retransSent = retransSent;
    }

    public long getLastDataDownlinkRate() {
        return lastDataDownlinkRate;
    }

    public void setLastDataDownlinkRate(long lastDataDownlinkRate) {
        this.lastDataDownlinkRate = lastDataDownlinkRate;
    }

    public long getLastDataUplinkRate() {
        return lastDataUplinkRate;
    }

    public void setLastDataUplinkRate(long lastDataUplinkRate) {
        this.lastDataUplinkRate = lastDataUplinkRate;
    }

    public double getAirTimePercentage() {
        return airTimePercentage;
    }

    public void setAirTimePercentage(double airTimePercentage) {
        this.airTimePercentage = airTimePercentage;
    }

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }

    public String getRadioKey() {
        return radioKey;
    }

    public void setRadioKey(String radioKey) {
        this.radioKey = radioKey;
    }

    public String getSsidKey() {
        return ssidKey;
    }

    public void setSsidKey(String ssidKey) {
        this.ssidKey = ssidKey;
    }

    public boolean isDualBandSupported() {
        return dualBandSupported;
    }

    public void setDualBandSupported(boolean dualBandSupported) {
        this.dualBandSupported = dualBandSupported;
    }

    public boolean isDfsSupported() {
        return dfsSupported;
    }

    public void setDfsSupported(boolean dfsSupported) {
        this.dfsSupported = dfsSupported;
    }

    public boolean isIeee80211kSupported() {
        return ieee80211kSupported;
    }

    public void setIeee80211kSupported(boolean ieee80211kSupported) {
        this.ieee80211kSupported = ieee80211kSupported;
    }

    public boolean isIeee80211vSupported() {
        return ieee80211vSupported;
    }

    public void setIeee80211vSupported(boolean ieee80211vSupported) {
        this.ieee80211vSupported = ieee80211vSupported;
    }

    public boolean isIeee80211rSupported() {
        return ieee80211rSupported;
    }

    public void setIeee80211rSupported(boolean ieee80211rSupported) {
        this.ieee80211rSupported = ieee80211rSupported;
    }

    public long getSendingTime() {
        return sendingTime;
    }

    public void setSendingTime(long sendingTime) {
        this.sendingTime = sendingTime;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getNtTimestamp() {
        return ntTimestamp;
    }

    public void setNtTimestamp(long ntTimestamp) {
        this.ntTimestamp = ntTimestamp;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }
}
