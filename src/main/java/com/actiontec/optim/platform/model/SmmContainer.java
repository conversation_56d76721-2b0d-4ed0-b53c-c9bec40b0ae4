package com.actiontec.optim.platform.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class SmmContainer {
    private String id;
    private String status;
    private Boolean autoStart;
    private Service service;
    private System system;
    private List<String> forbiddenOperations;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Boolean getAutoStart() {
        return autoStart;
    }

    public void setAutoStart(Boolean autoStart) {
        this.autoStart = autoStart;
    }

    public Service getService() {
        return service;
    }

    public void setService(Service service) {
        this.service = service;
    }

    public System getSystem() {
        return system;
    }

    public void setSystem(System system) {
        this.system = system;
    }

    public List<String> getForbiddenOperations() {
        return forbiddenOperations;
    }

    public void setForbiddenOperations(List<String> forbiddenOperations) {
        this.forbiddenOperations = forbiddenOperations;
    }

    public static class Service {
        private String serviceId;
        private String applicationId;
        private String version;
        private String provisionStatus;

        public String getServiceId() {
            return serviceId;
        }

        public void setServiceId(String serviceId) {
            this.serviceId = serviceId;
        }

        public String getApplicationId() {
            return applicationId;
        }

        public void setApplicationId(String applicationId) {
            this.applicationId = applicationId;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getProvisionStatus() {
            return provisionStatus;
        }

        public void setProvisionStatus(String provisionStatus) {
            this.provisionStatus = provisionStatus;
        }
    }

    public static class System {
        private double cpuUsage;
        private long memoryUsed;
        private List<FsUsed> fsUsed;

        public double getCpuUsage() {
            return cpuUsage;
        }

        public void setCpuUsage(double cpuUsage) {
            this.cpuUsage = cpuUsage;
        }

        public long getMemoryUsed() {
            return memoryUsed;
        }

        public void setMemoryUsed(long memoryUsed) {
            this.memoryUsed = memoryUsed;
        }

        public List<FsUsed> getFsUsed() {
            return fsUsed;
        }

        public void setFsUsed(List<FsUsed> fsUsed) {
            this.fsUsed = fsUsed;
        }
    }

}
