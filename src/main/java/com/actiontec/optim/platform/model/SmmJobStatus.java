package com.actiontec.optim.platform.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class SmmJobStatus implements  Comparable<SmmJobStatus>{
    private String id;
    private int count = 0;
    private int rate = 0;
    private String state;
    private long requestTime = 0;
    @JsonIgnore
    private String group;
    private String action;
    private String serviceId;
    private Source source;
    private String targetVersion;
    private boolean resourceCheck;
    private long createdAt;
    private long updatedAt;
    private UnitStats unitStats;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(long requestTime) {
        this.requestTime = requestTime;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public Source getSource() {
        return source;
    }

    public void setSource(Source source) {
        this.source = source;
    }

    public String getTargetVersion() {
        return targetVersion;
    }

    public void setTargetVersion(String targetVersion) {
        this.targetVersion = targetVersion;
    }

    public boolean isResourceCheck() {
        return resourceCheck;
    }

    public void setResourceCheck(boolean resourceCheck) {
        this.resourceCheck = resourceCheck;
    }

    public int getRate() {
        return rate;
    }

    public void setRate(int rate) {
        this.rate = rate;
    }

    public UnitStats getUnitStats() {
        return unitStats;
    }

    public void setUnitStats(UnitStats unitStats) {
        this.unitStats = unitStats;
    }

    @Override
    public int compareTo(SmmJobStatus obj) {
        return (int) (obj.requestTime - this.requestTime  );
    }

    public static class UnitStats {
        private long complete = 0;
        private long processing = 0;
        private long pending = 0;
        private long failed = 0;

        public long getComplete() {
            return complete;
        }

        public void setComplete(long complete) {
            this.complete = complete;
        }

        public long getProcessing() {
            return processing;
        }

        public void setProcessing(long processing) {
            this.processing = processing;
        }

        public long getPending() {
            return pending;
        }

        public void setPending(long pending) {
            this.pending = pending;
        }

        public long getFailed() {
            return failed;
        }

        public void setFailed(long failed) {
            this.failed = failed;
        }
    }
}
