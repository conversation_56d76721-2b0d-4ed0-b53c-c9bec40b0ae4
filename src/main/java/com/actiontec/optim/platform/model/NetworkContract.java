package com.actiontec.optim.platform.model;

public class NetworkContract {
    String id;
    String serviceTelephoneNumber;
    String name;
    ispDto isp;
    subscriptionDto subscription;
    Boolean ispManaged;

    public static class ispDto {
        String id;
        String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public static class subscriptionDto {
        double downlinkRate;
        double uplinkRate;

        public double getDownlinkRate() {
            return downlinkRate;
        }

        public void setDownlinkRate(double downlinkRate) {
            this.downlinkRate = downlinkRate;
        }

        public double getUplinkRate() {
            return uplinkRate;
        }

        public void setUplinkRate(double uplinkRate) {
            this.uplinkRate = uplinkRate;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getServiceTelephoneNumber() {
        return serviceTelephoneNumber;
    }

    public void setServiceTelephoneNumber(String serviceTelephoneNumber) {
        this.serviceTelephoneNumber = serviceTelephoneNumber;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ispDto getIsp() {
        return isp;
    }

    public void setIsp(ispDto isp) {
        this.isp = isp;
    }

    public subscriptionDto getSubscription() {
        return subscription;
    }

    public void setSubscription(subscriptionDto subscription) {
        this.subscription = subscription;
    }

    public Boolean getIspManaged() {
        return ispManaged;
    }

    public void setIspManaged(Boolean ispManaged) {
        this.ispManaged = ispManaged;
    }
}
