package com.actiontec.optim.platform.model.enums;

import org.apache.commons.lang3.StringUtils;

public enum SmmAppStateEnum {
    STATE_01("Active", "Init"),
    STATE_02("Active", "Done"),
    STATE_03("Active", "Failed"),
    STATE_04("Inactive", "Init"),
    STATE_05("Inactive", "Done"),
    STATE_06("Inactive", "Failed"),
    STATE_07("Paused", "Init"),
    STATE_08("Paused", "Done"),
    STATE_09("Paused", "Failed"),
    STATE_10("Manual", "Manual"),
    STATE_11("None", "None"),
    STATE_12("NotAvailable", "NotAvailable");


    private String desiredState;
    private String actionState;

    SmmAppStateEnum(String desiredState, String actionState) {
        this.desiredState = desiredState;
        this.actionState = actionState;
    }

    public static Boolean checkState(String desiredState, String actionState) {
        for(SmmAppStateEnum stateEnum : SmmAppStateEnum.values()) {
            if(StringUtils.equals(stateEnum.desiredState, desiredState) &&
                StringUtils.equals(stateEnum.actionState, actionState)) {
                return true;
            }
        }
        return false;
    }
}
