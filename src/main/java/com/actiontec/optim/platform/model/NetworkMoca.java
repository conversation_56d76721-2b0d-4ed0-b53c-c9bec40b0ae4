package com.actiontec.optim.platform.model;

public class NetworkMoca {
    private String id;
    private int portNo;
    private String macAddress;
    private String mocaVer;
    private String status;
    private long lastChangeTime;
    private Long lastReportTime;
    private Long operatingFrequency;
    private boolean isUpstream;
    private MocaStats stats;
    private MocaNode[] nodes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public int getPortNo() {
        return portNo;
    }

    public void setPortNo(int portNo) {
        this.portNo = portNo;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getMocaVer() {
        return mocaVer;
    }

    public void setMocaVer(String mocaVer) {
        this.mocaVer = mocaVer;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getLastChangeTime() {
        return lastChangeTime;
    }

    public void setLastChangeTime(long lastChangeTime) {
        this.lastChangeTime = lastChangeTime;
    }

    public Long getOperatingFrequency() {
        return operatingFrequency;
    }

    public void setOperatingFrequency(Long operatingFrequency) {
        this.operatingFrequency = operatingFrequency;
    }

    public boolean isUpstream() {
        return isUpstream;
    }

    public void setUpstream(boolean upstream) {
        isUpstream = upstream;
    }

    public Long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(Long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public MocaStats getStats() {
        return stats;
    }

    public void setStats(MocaStats stats) {
        this.stats = stats;
    }

    public MocaNode[] getNodes() {
        return nodes;
    }

    public void setNodes(MocaNode[] nodes) {
        this.nodes = nodes;
    }

    public static class MocaStats {
        private long bytesSent;
        private long bytesReceived;

        public long getBytesSent() {
            return bytesSent;
        }

        public void setBytesSent(long bytesSent) {
            this.bytesSent = bytesSent;
        }

        public long getBytesReceived() {
            return bytesReceived;
        }

        public void setBytesReceived(long bytesReceived) {
            this.bytesReceived = bytesReceived;
        }
    }

    public static class MocaNode {
        private String macAddress;
        private String mode;
        private long uptime;
        private long attenuation;
        private long transmitPhyRate;
        private long receivePhyRate;
        private long transmitPowerLevel;
        private long receivePowerLevel;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }

        public long getAttenuation() {
            return attenuation;
        }

        public void setAttenuation(long attenuation) {
            this.attenuation = attenuation;
        }

        public long getTransmitPhyRate() {
            return transmitPhyRate;
        }

        public void setTransmitPhyRate(long transmitPhyRate) {
            this.transmitPhyRate = transmitPhyRate;
        }

        public long getReceivePhyRate() {
            return receivePhyRate;
        }

        public void setReceivePhyRate(long receivePhyRate) {
            this.receivePhyRate = receivePhyRate;
        }

        public long getTransmitPowerLevel() {
            return transmitPowerLevel;
        }

        public void setTransmitPowerLevel(long transmitPowerLevel) {
            this.transmitPowerLevel = transmitPowerLevel;
        }

        public long getReceivePowerLevel() {
            return receivePowerLevel;
        }

        public void setReceivePowerLevel(long receivePowerLevel) {
            this.receivePowerLevel = receivePowerLevel;
        }
    }
}
