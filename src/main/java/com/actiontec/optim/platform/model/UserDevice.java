package com.actiontec.optim.platform.model;

import com.actiontec.optim.platform.model.enums.ConnectionStatus;
import com.actiontec.optim.platform.model.enums.UserDevicePhyType;
import com.actiontec.optim.platform.model.enums.UserDeviceType;

import java.util.List;

public class UserDevice {
    private String macAddress;
    private String friendlyName;
    private String hostName;
    private ConnectionStatus status;
    private UserDeviceType deviceType;
    private UserDevicePhyType phyType;
    private String vendor;
    private String upstream;
    private String wanMacAddress; // for extender only
    private String serialNumber; // for extender only
    private long downlinkPhyRate;
    private long uplinkPhyRate;
    private boolean internetAccessBlocked;
    private long internetAccessBlockStartTime;
    private long internetAccessBlockDuration;
    private long lastReportTime;
    private long ntTimestamp;

    private List<UserDeviceAddress> addresses;
    private UserDeviceWireless userDeviceWireless;
    private UserDeviceEthernet userDeviceEthernet;
    private StationSpeedTest stationSpeedTest;
    private List<MarkedAttributes> markedAttributes;

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getWanMacAddress() {
        return wanMacAddress;
    }

    public void setWanMacAddress(String wanMacAddress) {
        this.wanMacAddress = wanMacAddress;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public ConnectionStatus getStatus() {
        return status;
    }

    public void setStatus(ConnectionStatus status) {
        this.status = status;
    }

    public UserDeviceType getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(UserDeviceType deviceType) {
        this.deviceType = deviceType;
    }

    public UserDevicePhyType getPhyType() {
        return phyType;
    }

    public void setPhyType(UserDevicePhyType phyType) {
        this.phyType = phyType;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public String getUpstream() {
        return upstream;
    }

    public void setUpstream(String upstream) {
        this.upstream = upstream;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public long getDownlinkPhyRate() {
        return downlinkPhyRate;
    }

    public void setDownlinkPhyRate(long downlinkPhyRate) {
        this.downlinkPhyRate = downlinkPhyRate;
    }

    public long getUplinkPhyRate() {
        return uplinkPhyRate;
    }

    public void setUplinkPhyRate(long uplinkPhyRate) {
        this.uplinkPhyRate = uplinkPhyRate;
    }

    public boolean isInternetAccessBlocked() {
        return internetAccessBlocked;
    }

    public void setInternetAccessBlocked(boolean internetAccessBlocked) {
        this.internetAccessBlocked = internetAccessBlocked;
    }

    public long getInternetAccessBlockStartTime() {
        return internetAccessBlockStartTime;
    }

    public void setInternetAccessBlockStartTime(long internetAccessBlockStartTime) {
        this.internetAccessBlockStartTime = internetAccessBlockStartTime;
    }

    public long getInternetAccessBlockDuration() {
        return internetAccessBlockDuration;
    }

    public void setInternetAccessBlockDuration(long internetAccessBlockDuration) {
        this.internetAccessBlockDuration = internetAccessBlockDuration;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public long getNtTimestamp() {
        return ntTimestamp;
    }

    public void setNtTimestamp(long ntTimestamp) {
        this.ntTimestamp = ntTimestamp;
    }

    public List<UserDeviceAddress> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<UserDeviceAddress> addresses) {
        this.addresses = addresses;
    }

    public UserDeviceWireless getUserDeviceWireless() {
        return userDeviceWireless;
    }

    public void setUserDeviceWireless(UserDeviceWireless userDeviceWireless) {
        this.userDeviceWireless = userDeviceWireless;
    }

    public UserDeviceEthernet getUserDeviceEthernet() {
        return userDeviceEthernet;
    }

    public void setUserDeviceEthernet(UserDeviceEthernet userDeviceEthernet) {
        this.userDeviceEthernet = userDeviceEthernet;
    }

    public StationSpeedTest getStationSpeedTest() {
        return stationSpeedTest;
    }

    public void setStationSpeedTest(StationSpeedTest stationSpeedTest) {
        this.stationSpeedTest = stationSpeedTest;
    }

    public List<MarkedAttributes> getMarkedAttributes() {
        return markedAttributes;
    }

    public void setMarkedAttributes(List<MarkedAttributes> markedAttributes) {
        this.markedAttributes = markedAttributes;
    }
}
