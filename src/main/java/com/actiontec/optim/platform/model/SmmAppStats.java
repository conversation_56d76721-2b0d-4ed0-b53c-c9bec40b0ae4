package com.actiontec.optim.platform.model;

public class SmmAppStats {

    private String serviceId;
    private String serviceVersion;
    private String actionState;
    private String desiredState;
    private Long duration;
    private SmmAppStatsValues values;

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getServiceVersion() {
        return serviceVersion;
    }

    public void setServiceVersion(String serviceVersion) {
        this.serviceVersion = serviceVersion;
    }

    public String getActionState() {
        return actionState;
    }

    public void setActionState(String actionState) {
        this.actionState = actionState;
    }

    public String getDesiredState() {
        return desiredState;
    }

    public void setDesiredState(String desiredState) {
        this.desiredState = desiredState;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public SmmAppStatsValues getValues() {
        return values;
    }

    public void setValues(SmmAppStatsValues values) {
        this.values = values;
    }
}
