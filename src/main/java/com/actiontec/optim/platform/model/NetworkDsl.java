package com.actiontec.optim.platform.model;

import java.util.List;

public class NetworkDsl {

    private String id;
    private List<LinesDto> lines;
    private Long lastReportTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<LinesDto> getLines() {
        return lines;
    }

    public void setLines(List<LinesDto> lines) {
        this.lines = lines;
    }

    public Long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(Long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public static class LinesDto {

        private int lineNumber;
        private String status;
        private int uptime;
        private long lastChangeTime;
        private int downstreamLineRate;
        private int upstreamLineRate;
        private int farEndCrc;
        private int nearEndCrc;
        private int lastRetrainReason;
        private int linkRetrain;
        private int erroredSeconds;
        private int severelyErroredSeconds;

        public int getLineNumber() {
            return lineNumber;
        }

        public void setLineNumber(int lineNumber) {
            this.lineNumber = lineNumber;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public int getUptime() {
            return uptime;
        }

        public void setUptime(int uptime) {
            this.uptime = uptime;
        }

        public long getLastChangeTime() {
            return lastChangeTime;
        }

        public void setLastChangeTime(long lastChangeTime) {
            this.lastChangeTime = lastChangeTime;
        }

        public int getDownstreamLineRate() {
            return downstreamLineRate;
        }

        public void setDownstreamLineRate(int downstreamLineRate) {
            this.downstreamLineRate = downstreamLineRate;
        }

        public int getUpstreamLineRate() {
            return upstreamLineRate;
        }

        public void setUpstreamLineRate(int upstreamLineRate) {
            this.upstreamLineRate = upstreamLineRate;
        }

        public int getFarEndCrc() {
            return farEndCrc;
        }

        public void setFarEndCrc(int farEndCrc) {
            this.farEndCrc = farEndCrc;
        }

        public int getNearEndCrc() {
            return nearEndCrc;
        }

        public void setNearEndCrc(int nearEndCrc) {
            this.nearEndCrc = nearEndCrc;
        }

        public int getLastRetrainReason() {
            return lastRetrainReason;
        }

        public void setLastRetrainReason(int lastRetrainReason) {
            this.lastRetrainReason = lastRetrainReason;
        }

        public int getLinkRetrain() {
            return linkRetrain;
        }

        public void setLinkRetrain(int linkRetrain) {
            this.linkRetrain = linkRetrain;
        }

        public int getErroredSeconds() {
            return erroredSeconds;
        }

        public void setErroredSeconds(int erroredSeconds) {
            this.erroredSeconds = erroredSeconds;
        }

        public int getSeverelyErroredSeconds() {
            return severelyErroredSeconds;
        }

        public void setSeverelyErroredSeconds(int severelyErroredSeconds) {
            this.severelyErroredSeconds = severelyErroredSeconds;
        }
    }
}
