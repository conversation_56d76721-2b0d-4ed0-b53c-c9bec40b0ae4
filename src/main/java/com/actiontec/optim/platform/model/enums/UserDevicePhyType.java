package com.actiontec.optim.platform.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public enum UserDevicePhyType {
    Ethernet("Ethernet"),
    Wifi("WiFi"),
    <PERSON><PERSON>("MoCA"),
    Unknown("Unknown");

    private String formalName;

    UserDevicePhyType(String formalName) {
        this.formalName = formalName;
    }

    public static Optional<UserDevicePhyType> fromFormalName(String formalName) {
        for (UserDevicePhyType userDevicePhyType : UserDevicePhyType.values()) {
            if (StringUtils.equals(userDevicePhyType.formalName, formalName)) {
                return Optional.of(userDevicePhyType);
            }
        }
        return Optional.empty();
    }

    public String getFormalName() {
        return formalName;
    }
}
