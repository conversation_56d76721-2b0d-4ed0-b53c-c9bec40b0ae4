package com.actiontec.optim.platform.model.enums;

import java.util.Locale;

public enum EquipmentType {
    GATEWAY,
    EXTENDER;
    public static EquipmentType fromName(String name) {
        for (EquipmentType item:EquipmentType.values()) {
            if (item.name().equals(name))
                return item;
        }
        return GATEWAY;
    }

    public static String getFormalName(String name) {
        String value = fromName(name).name();
        return value.substring(0,1).toUpperCase().concat(value.substring(1).toLowerCase());
    }
}
