package com.actiontec.optim.platform.model.firmware;

import java.util.List;

public class FwPolicyBase extends PolicyBase{
    private Source source;
    private String targetImageId;

    public FwPolicyBase() {
        this.source = new Source();
    }

    public Source getSource() {
        return source;
    }

    public void setSource(Source source) {
        this.source = source;
    }

    public String getTargetImageId() {
        return targetImageId;
    }

    public void setTargetImageId(String targetImageId) {
        this.targetImageId = targetImageId;
    }

    public static class Source {
        private String ispId;
        private String modelId;
        private List<String> versions;

        public Source() {
        }

        public String getIspId() {
            return ispId;
        }

        public void setIspId(String ispId) {
            this.ispId = ispId;
        }

        public String getModelId() {
            return modelId;
        }

        public void setModelId(String modelId) {
            this.modelId = modelId;
        }

        public List<String> getVersions() {
            return versions;
        }

        public void setVersions(List<String> versions) {
            this.versions = versions;
        }
    }
}
