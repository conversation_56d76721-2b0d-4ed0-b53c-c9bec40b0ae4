package com.actiontec.optim.platform.model.firmware;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.LinkedList;
import java.util.List;

public class FwPolicyInfo extends FwPolicyBase {
    private String status;
    private String id;
    private UnitStats unitStats;
    private List<FailureStats> failureStats;
    @JsonIgnore
    private long updatedAt;

    public FwPolicyInfo() {
        this.unitStats = new UnitStats();
        this.failureStats = new LinkedList<FailureStats>();
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public UnitStats getUnitStats() {
        return unitStats;
    }

    public void setUnitStats(UnitStats unitStats) {
        this.unitStats = unitStats;
    }

    public List<FailureStats> getFailureStats() {
        return failureStats;
    }

    public void setFailureStats(List<FailureStats> failureStats) {
        this.failureStats = failureStats;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public static class UnitStats {
        private int complete = 0;
        private int processing = 0;
        private int failed = 0;

        public UnitStats() {
        }

        public int getComplete() {
            return complete;
        }

        public void setComplete(int complete) {
            this.complete = complete;
        }

        public int getProcessing() {
            return processing;
        }

        public void setProcessing(int processing) {
            this.processing = processing;
        }

        public int getFailed() {
            return failed;
        }

        public void setFailed(int failed) {
            this.failed = failed;
        }
    }

    public static class FailureStats {
        private int code;
        private int number;

        public FailureStats() {
        }

        public FailureStats(int code, int number) {
            this.code = code;
            this.number = number;
        }

        public int getCode() {
            return code;
        }

        public void setCode(int code) {
            this.code = code;
        }

        public int getNumber() {
            return number;
        }

        public void setNumber(int number) {
            this.number = number;
        }
    }
}
