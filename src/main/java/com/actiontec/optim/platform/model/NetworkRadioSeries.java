package com.actiontec.optim.platform.model;

import java.util.List;

public class NetworkRadioSeries {

    private long timestamp;
    private double airTimeBusy;
    private int channel;
    private long bytesSent;
    private long bytesReceived;
    private long deltaBytesSent;
    private long deltaBytesReceived;
    private List<AirTimeBusyDeviceDistributionDto> airTimeBusyDeviceDistribution;

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public double getAirTimeBusy() {
        return airTimeBusy;
    }

    public void setAirTimeBusy(double airTimeBusy) {
        this.airTimeBusy = airTimeBusy;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public long getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(long bytesSent) {
        this.bytesSent = bytesSent;
    }

    public long getBytesReceived() {
        return bytesReceived;
    }

    public void setBytesReceived(long bytesReceived) {
        this.bytesReceived = bytesReceived;
    }

    public long getDeltaBytesSent() {
        return deltaBytesSent;
    }

    public void setDeltaBytesSent(long deltaBytesSent) {
        this.deltaBytesSent = deltaBytesSent;
    }

    public long getDeltaBytesReceived() {
        return deltaBytesReceived;
    }

    public void setDeltaBytesReceived(long deltaBytesReceived) {
        this.deltaBytesReceived = deltaBytesReceived;
    }

    public List<AirTimeBusyDeviceDistributionDto> getAirTimeBusyDeviceDistribution() {
        return airTimeBusyDeviceDistribution;
    }

    public void setAirTimeBusyDeviceDistribution(List<AirTimeBusyDeviceDistributionDto> airTimeBusyDeviceDistribution) {
        this.airTimeBusyDeviceDistribution = airTimeBusyDeviceDistribution;
    }

    public static class AirTimeBusyDeviceDistributionDto {

        private double airTimeBusy;
        private String macAddress;
        private String name;
        private String deviceType;

        public double getAirTimeBusy() {
            return airTimeBusy;
        }

        public void setAirTimeBusy(double airTimeBusy) {
            this.airTimeBusy = airTimeBusy;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }
    }
}
