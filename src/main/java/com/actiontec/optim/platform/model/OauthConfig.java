package com.actiontec.optim.platform.model;

import java.util.ArrayList;

public class OauthConfig {
    private String id;
    private String name;
    private String provider;
    private String clientId;
    private String clientSecret;
    private String redirectUri;
    private String responseType;
    private String scope;
    private Boolean openIdSupported;
    private String wellKnownConfig;
    private Boolean isDefaultProvider;
    private OauthConfigEndpoint endpoints;
    private OauthConfigExtraClientData extraClientData;
    private ArrayList<String> ispIds;
    private String defaultIspId;
    private String roleId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getResponseType() {
        return responseType;
    }

    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public Boolean getOpenIdSupported() {
        return openIdSupported;
    }

    public void setOpenIdSupported(Boolean openIdSupported) {
        this.openIdSupported = openIdSupported;
    }

    public String getWellKnownConfig() {
        return wellKnownConfig;
    }

    public void setWellKnownConfig(String wellKnownConfig) {
        this.wellKnownConfig = wellKnownConfig;
    }

    public Boolean getIsDefaultProvider() {
        return isDefaultProvider;
    }

    public void setIsDefaultProvider(Boolean isDefaultProvider) {
        this.isDefaultProvider = isDefaultProvider;
    }

    public OauthConfigEndpoint getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(OauthConfigEndpoint endpoints) {
        this.endpoints = endpoints;
    }

    public OauthConfigExtraClientData getExtraClientData() {
        return extraClientData;
    }

    public void setExtraClientData(OauthConfigExtraClientData extraClientData) {
        this.extraClientData = extraClientData;
    }

    public ArrayList<String> getIspIds() {
        return ispIds;
    }

    public void setIspIds(ArrayList<String> ispIds) {
        this.ispIds = ispIds;
    }

    public String getDefaultIspId() {
        return defaultIspId;
    }

    public void setDefaultIspId(String defaultIspId) {
        this.defaultIspId = defaultIspId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
