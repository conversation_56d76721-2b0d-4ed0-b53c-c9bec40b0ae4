package com.actiontec.optim.platform.model;

import java.util.ArrayList;

public class NetworkVoiceStats {
    private Long timestamp;
    private String status;
    private ArrayList<LinesDto> lines;

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ArrayList<LinesDto> getLines() {
        return lines;
    }

    public void setLines(ArrayList<LinesDto> lines) {
        this.lines = lines;
    }

    public static class LinesDto {
        private long lineNumber;
        private long incomingCallsReceived;
        private long incomingCallsConnected;
        private long incomingCallsFailed;
        private long incomingCallsDropped;
        private long incomingCallsTotalCallTime;
        private long outgoingCallsAttempted;
        private long outgoingCallsConnected;
        private long outgoingCallsFailed;
        private long outgoingCallsDropped;
        private long outgoingCallsTotalCallTime;
        private long packetsReceived;
        private long packetsSent;
        private long packetsLost;
        private long bytesReceived;
        private long bytesSent;

        public long getLineNumber() {
            return lineNumber;
        }

        public void setLineNumber(long lineNumber) {
            this.lineNumber = lineNumber;
        }

        public long getIncomingCallsReceived() {
            return incomingCallsReceived;
        }

        public void setIncomingCallsReceived(long incomingCallsReceived) {
            this.incomingCallsReceived = incomingCallsReceived;
        }

        public long getIncomingCallsConnected() {
            return incomingCallsConnected;
        }

        public void setIncomingCallsConnected(long incomingCallsConnected) {
            this.incomingCallsConnected = incomingCallsConnected;
        }

        public long getIncomingCallsFailed() {
            return incomingCallsFailed;
        }

        public void setIncomingCallsFailed(long incomingCallsFailed) {
            this.incomingCallsFailed = incomingCallsFailed;
        }

        public long getIncomingCallsDropped() {
            return incomingCallsDropped;
        }

        public void setIncomingCallsDropped(long incomingCallsDropped) {
            this.incomingCallsDropped = incomingCallsDropped;
        }

        public long getIncomingCallsTotalCallTime() {
            return incomingCallsTotalCallTime;
        }

        public void setIncomingCallsTotalCallTime(long incomingCallsTotalCallTime) {
            this.incomingCallsTotalCallTime = incomingCallsTotalCallTime;
        }

        public long getOutgoingCallsAttempted() {
            return outgoingCallsAttempted;
        }

        public void setOutgoingCallsAttempted(long outgoingCallsAttempted) {
            this.outgoingCallsAttempted = outgoingCallsAttempted;
        }

        public long getOutgoingCallsConnected() {
            return outgoingCallsConnected;
        }

        public void setOutgoingCallsConnected(long outgoingCallsConnected) {
            this.outgoingCallsConnected = outgoingCallsConnected;
        }

        public long getOutgoingCallsFailed() {
            return outgoingCallsFailed;
        }

        public void setOutgoingCallsFailed(long outgoingCallsFailed) {
            this.outgoingCallsFailed = outgoingCallsFailed;
        }

        public long getOutgoingCallsDropped() {
            return outgoingCallsDropped;
        }

        public void setOutgoingCallsDropped(long outgoingCallsDropped) {
            this.outgoingCallsDropped = outgoingCallsDropped;
        }

        public long getOutgoingCallsTotalCallTime() {
            return outgoingCallsTotalCallTime;
        }

        public void setOutgoingCallsTotalCallTime(long outgoingCallsTotalCallTime) {
            this.outgoingCallsTotalCallTime = outgoingCallsTotalCallTime;
        }

        public long getPacketsReceived() {
            return packetsReceived;
        }

        public void setPacketsReceived(long packetsReceived) {
            this.packetsReceived = packetsReceived;
        }

        public long getPacketsSent() {
            return packetsSent;
        }

        public void setPacketsSent(long packetsSent) {
            this.packetsSent = packetsSent;
        }

        public long getPacketsLost() {
            return packetsLost;
        }

        public void setPacketsLost(long packetsLost) {
            this.packetsLost = packetsLost;
        }

        public long getBytesReceived() {
            return bytesReceived;
        }

        public void setBytesReceived(long bytesReceived) {
            this.bytesReceived = bytesReceived;
        }

        public long getBytesSent() {
            return bytesSent;
        }

        public void setBytesSent(long bytesSent) {
            this.bytesSent = bytesSent;
        }
    }
}
