package com.actiontec.optim.platform.model.equipment;

import com.actiontec.optim.platform.model.Ipv6Address;

import java.util.List;

public class NetworkUpstreamInterface {
    private String id;
    private Long lastReportTime;
    private boolean ipv6Enabled;
    private List<Ipv6Address> ipv6Addresses;
    private String ipv6Protocol;
    private String ipv6DefaultGateway;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(Long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public boolean isIpv6Enabled() {
        return ipv6Enabled;
    }

    public void setIpv6Enabled(boolean ipv6Enabled) {
        this.ipv6Enabled = ipv6Enabled;
    }

    public List<Ipv6Address> getIpv6Addresses() {
        return ipv6Addresses;
    }

    public void setIpv6Addresses(List<Ipv6Address> ipv6Addresses) {
        this.ipv6Addresses = ipv6Addresses;
    }

    public String getIpv6Protocol() {
        return ipv6Protocol;
    }

    public void setIpv6Protocol(String ipv6Protocol) {
        this.ipv6Protocol = ipv6Protocol;
    }

    public String getIpv6DefaultGateway() {
        return ipv6DefaultGateway;
    }

    public void setIpv6DefaultGateway(String ipv6DefaultGateway) {
        this.ipv6DefaultGateway = ipv6DefaultGateway;
    }
}
