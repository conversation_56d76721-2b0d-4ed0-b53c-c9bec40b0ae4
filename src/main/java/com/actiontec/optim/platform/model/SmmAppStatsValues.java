package com.actiontec.optim.platform.model;

public class SmmAppStatsValues {

    private Long count;
    private SmmAppStatsUsage cpuUsage;
    private SmmAppStatsUsage memoryUsed;
    private SmmAppStatsUsage fsUsed;

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public SmmAppStatsUsage getCpuUsage() {
        return cpuUsage;
    }

    public void setCpuUsage(SmmAppStatsUsage cpuUsage) {
        this.cpuUsage = cpuUsage;
    }

    public SmmAppStatsUsage getMemoryUsed() {
        return memoryUsed;
    }

    public void setMemoryUsed(SmmAppStatsUsage memoryUsed) {
        this.memoryUsed = memoryUsed;
    }

    public SmmAppStatsUsage getFsUsed() {
        return fsUsed;
    }

    public void setFsUsed(SmmAppStatsUsage fsUsed) {
        this.fsUsed = fsUsed;
    }
}
