package com.actiontec.optim.platform.model;

import java.util.ArrayList;

public class EquipmentModel {

    private String id;
    private String name;
    private String equipmentType;
    private ArrayList<String> ispIds;
    private ModelSnapshot snapshot;
    private ModelHardware hardware;
    private ArrayList<String> features;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(String equipmentType) {
        this.equipmentType = equipmentType;
    }

    public ArrayList<String> getIspIds() {
        return ispIds;
    }

    public void setIspIds(ArrayList<String> ispIds) {
        this.ispIds = ispIds;
    }

    public ModelSnapshot getSnapshot() {
        return snapshot;
    }

    public void setSnapshot(ModelSnapshot snapshot) {
        this.snapshot = snapshot;
    }

    public ModelHardware getHardware() {
        return hardware;
    }

    public void setHardware(ModelHardware hardware) {
        this.hardware = hardware;
    }

    public ArrayList<String> getFeatures() {
        return features;
    }

    public void setFeatures(ArrayList<String> features) {
        this.features = features;
    }
}
