package com.actiontec.optim.platform.model.firmware;

import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import java.util.List;

import static com.actiontec.optim.platform.constant.ApplicationConstants.FW_UPGRADE_RETRY_INTERVAL;
import static com.actiontec.optim.platform.constant.ApplicationConstants.FW_UPGRADE_RETRY_TIMES;

public class PolicyBase {
    private int count = 0;
    private int rate = 0;
    private ExecutionTime executionTime;

    @Valid
    private Retry retry;

    public PolicyBase() {
        this.executionTime = new ExecutionTime();
        this.retry = new Retry();
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getRate() {
        return rate;
    }

    public void setRate(int rate) {
        this.rate = rate;
    }

    public ExecutionTime getExecutionTime() {
        return executionTime;
    }

    public void setExecutionTime(ExecutionTime executionTime) {
        this.executionTime = executionTime;
    }

    public Retry getRetry() {
        return retry;
    }

    public void setRetry(Retry retry) {
        this.retry = retry;
    }

    public static class ExecutionTime {
        @Range(message = "UTC offset must be in the range of -18 to 18.", min=-18 , max=18)
        private int timeZone = 0;
        private List<List<Integer>>periods;

        public ExecutionTime() {
        }

        public int getTimeZone() {
            return timeZone;
        }

        public void setTimeZone(int timeZone) {
            this.timeZone = timeZone;
        }

        public List<List<Integer>> getPeriods() {
            return periods;
        }

        public void setPeriods(List<List<Integer>> periods) {
            this.periods = periods;
        }
    }

    public static class Retry {
        @Range(message = "Count must be in the range of 0 to 10.", min=0 , max=10)
        private int count = FW_UPGRADE_RETRY_TIMES;
        @Range(message = "Interval must be in the range of 0 to 86400.", min=0 , max=86400)
        private int interval = FW_UPGRADE_RETRY_INTERVAL;

        public int getCount() {
            return count;
        }

        public void setCount(int count) {
            this.count = count;
        }

        public int getInterval() {
            return interval;
        }

        public void setInterval(int interval) {
            this.interval = interval;
        }
    }
}
