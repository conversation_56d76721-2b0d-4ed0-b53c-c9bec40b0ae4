package com.actiontec.optim.platform.model.equipment;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.model.DnsServer;

import java.util.List;

public class NetworkUpstreamContext {
    private List<NetworkUpstreamInterface> interfaces;
    private List<DnsServer> ipv4DnsServers;
    private List<DnsServer> ipv6DnsServers;
    private ApDetailDto.WanDto wanDto;
    private long lastReportTime;

    public List<NetworkUpstreamInterface> getInterfaces() {
        return interfaces;
    }

    public void setInterfaces(List<NetworkUpstreamInterface> interfaces) {
        this.interfaces = interfaces;
    }

    public List<DnsServer> getIpv4DnsServers() {
        return ipv4DnsServers;
    }

    public void setIpv4DnsServers(List<DnsServer> ipv4DnsServers) {
        this.ipv4DnsServers = ipv4DnsServers;
    }

    public List<DnsServer> getIpv6DnsServers() {
        return ipv6DnsServers;
    }

    public void setIpv6DnsServers(List<DnsServer> ipv6DnsServers) {
        this.ipv6DnsServers = ipv6DnsServers;
    }

    public ApDetailDto.WanDto getWanDto() {
        return wanDto;
    }

    public void setWanDto(ApDetailDto.WanDto wanDto) {
        this.wanDto = wanDto;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }
}
