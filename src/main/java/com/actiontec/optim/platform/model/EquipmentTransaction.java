package com.actiontec.optim.platform.model;

public class EquipmentTransaction {
    String id;
    String status;
    String action;
    Long time;
    EquipmentTransactionPayload payload;
    EquipmentTransactionUser user;
    EquipmentTransactionUnitStats unitStats;
    EquipmentTransactionErrors errors;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public EquipmentTransactionPayload getPayload() {
        return payload;
    }

    public void setPayload(EquipmentTransactionPayload payload) {
        this.payload = payload;
    }

    public EquipmentTransactionUser getUser() {
        return user;
    }

    public void setUser(EquipmentTransactionUser user) {
        this.user = user;
    }

    public EquipmentTransactionUnitStats getUnitStats() {
        return unitStats;
    }

    public void setUnitStats(EquipmentTransactionUnitStats unitStats) {
        this.unitStats = unitStats;
    }

    public EquipmentTransactionErrors getErrors() {
        return errors;
    }

    public void setErrors(EquipmentTransactionErrors errors) {
        this.errors = errors;
    }
}
