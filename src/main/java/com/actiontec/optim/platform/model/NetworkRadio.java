package com.actiontec.optim.platform.model;

import com.actiontec.optim.mongodb.dto.RadioEnum;

import java.util.List;

public class NetworkRadio {
    private String serialNumber;
    private RadioEnum radioKey;
    private boolean enabled;
    private String status;
    private int channel;
    private int channelWidth;
    private List<String> operatingStandards;
    private boolean autoChannelEnabled;
    private boolean dfsEnabled;
    private boolean isBackhaul;
    private long lastReportTime;
    private long bytesSent;
    private long bytesReceived;
    private double avgBytesSent;
    private double avgBytesReceived;
    private int devicesAssociated;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public RadioEnum getRadioKey() {
        return radioKey;
    }

    public void setRadioKey(RadioEnum radioKey) {
        this.radioKey = radioKey;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public int getChannelWidth() {
        return channelWidth;
    }

    public void setChannelWidth(int channelWidth) {
        this.channelWidth = channelWidth;
    }

    public List<String> getOperatingStandards() {
        return operatingStandards;
    }

    public void setOperatingStandards(List<String> operatingStandards) {
        this.operatingStandards = operatingStandards;
    }

    public boolean isAutoChannelEnabled() {
        return autoChannelEnabled;
    }

    public void setAutoChannelEnabled(boolean autoChannelEnabled) {
        this.autoChannelEnabled = autoChannelEnabled;
    }

    public boolean isDfsEnabled() {
        return dfsEnabled;
    }

    public void setDfsEnabled(boolean dfsEnabled) {
        this.dfsEnabled = dfsEnabled;
    }

    public boolean isBackhaul() {
        return isBackhaul;
    }

    public void setBackhaul(boolean backhaul) {
        isBackhaul = backhaul;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public long getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(long bytesSent) {
        this.bytesSent = bytesSent;
    }

    public long getBytesReceived() {
        return bytesReceived;
    }

    public void setBytesReceived(long bytesReceived) {
        this.bytesReceived = bytesReceived;
    }

    public double getAvgBytesSent() {
        return avgBytesSent;
    }

    public void setAvgBytesSent(double avgBytesSent) {
        this.avgBytesSent = avgBytesSent;
    }

    public double getAvgBytesReceived() {
        return avgBytesReceived;
    }

    public void setAvgBytesReceived(double avgBytesReceived) {
        this.avgBytesReceived = avgBytesReceived;
    }

    public int getDevicesAssociated() {
        return devicesAssociated;
    }

    public void setDevicesAssociated(int devicesAssociated) {
        this.devicesAssociated = devicesAssociated;
    }

}
