package com.actiontec.optim.platform.model.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.Optional;

// Equipment and Device Type
public enum UserDeviceType {
    Gateway,
    Extender,
    SetTopBox,
    Other,
    MobilePhone, WiredPhone, Tablet, Laptop, Computer, Camera, Robot, Game, TV, Speaker, Printer, Bulb;

    public static Optional<UserDeviceType> fromName(String name) {
        for (UserDeviceType userDeviceType : UserDeviceType.values()) {
            if (StringUtils.equals(userDeviceType.name(), name)) {
                return Optional.of(userDeviceType);
            }
        }
        return Optional.empty();
    }

    public static Optional<UserDeviceType> getCustomType(Optional<UserDeviceType> userDeviceType, String name) {
        if ((userDeviceType.isPresent() && userDeviceType.get().ordinal() < Other.ordinal()) || Objects.isNull(name)) {
            return userDeviceType;
        } else if (StringUtils.isNotBlank(name)){
            UserDeviceType[] values = UserDeviceType.values();
            int i;
            for (i = Other.ordinal(); i < values.length; i++) {
                if (StringUtils.equals(values[i].name(), name)) {
                    return Optional.of(values[i]);
                }
            }
        }
        return Optional.empty();
    }

}