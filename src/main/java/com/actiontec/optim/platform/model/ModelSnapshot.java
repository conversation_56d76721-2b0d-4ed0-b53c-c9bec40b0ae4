package com.actiontec.optim.platform.model;

public class ModelSnapshot {

    private String fileName;
    private int fileSize;
    private String fileLocation;
    private String fileEntry;
    private String fileStatus;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileLocation() {
        return fileLocation;
    }

    public void setFileLocation(String fileLocation) {
        this.fileLocation = fileLocation;
    }

    public String getFileEntry() {
        return fileEntry;
    }

    public void setFileEntry(String fileEntry) {
        this.fileEntry = fileEntry;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }
}
