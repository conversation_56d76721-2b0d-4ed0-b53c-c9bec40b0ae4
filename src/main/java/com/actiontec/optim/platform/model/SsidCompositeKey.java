package com.actiontec.optim.platform.model;

import java.util.Objects;

public class SsidCompositeKey {
    private String radioKey;
    private String ssidKey;

    //XXX: leave a default constructer for jackson mapper, bad design
    public SsidCompositeKey() {
    }

    public SsidCompositeKey(String radioKey, String ssidKey) {
        this.radioKey = radioKey;
        this.ssidKey = ssidKey;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SsidCompositeKey that = (SsidCompositeKey) o;
        return Objects.equals(radioKey, that.radioKey) &&
                Objects.equals(ssidKey, that.ssidKey);
    }

    @Override
    public int hashCode() {
        return Objects.hash(radioKey, ssidKey);
    }

    public String getRadioKey() {
        return radioKey;
    }

    public String getSsidKey() {
        return ssidKey;
    }

    //XXX: leave a setter for jackson mapper, bad design
    public void setRadioKey(String radioKey) {
        this.radioKey = radioKey;
    }

    //XXX: leave a setter for jackson mapper, bad design
    public void setSsidKey(String ssidKey) {
        this.ssidKey = ssidKey;
    }
}
