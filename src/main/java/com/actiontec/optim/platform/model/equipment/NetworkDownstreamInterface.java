package com.actiontec.optim.platform.model.equipment;

import com.actiontec.optim.platform.model.Ipv6Address;

import java.util.List;

public class NetworkDownstreamInterface {
    private String id;
    private Long lastReportTime;
    private boolean ipv6Enabled;
    private List<Ipv6Address> ipv6Addresses;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(Long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public boolean isIpv6Enabled() {
        return ipv6Enabled;
    }

    public void setIpv6Enabled(boolean ipv6Enabled) {
        this.ipv6Enabled = ipv6Enabled;
    }

    public List<Ipv6Address> getIpv6Addresses() {
        return ipv6Addresses;
    }

    public void setIpv6Addresses(List<Ipv6Address> ipv6Addresses) {
        this.ipv6Addresses = ipv6Addresses;
    }
}
