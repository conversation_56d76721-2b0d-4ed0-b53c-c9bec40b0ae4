package com.actiontec.optim.platform.model;

import java.util.List;
import java.util.Map;

public class IspConfigurationManagementServer {
    Boolean enabled;
    String url;
    List<Map> username;
    List<Map> password;
    Boolean periodicInformEnabled;
    Integer periodicInformInterval;
    Integer retryInterval;
    String connectionRequestUrl;
    List<Map> connectionRequestUsername;
    List<Map> connectionRequestPassword;

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<Map> getUsername() {
        return username;
    }

    public void setUsername(List<Map> username) {
        this.username = username;
    }

    public List<Map> getPassword() {
        return password;
    }

    public void setPassword(List<Map> password) {
        this.password = password;
    }

    public Boolean getPeriodicInformEnabled() {
        return periodicInformEnabled;
    }

    public void setPeriodicInformEnabled(Boolean periodicInformEnabled) {
        this.periodicInformEnabled = periodicInformEnabled;
    }

    public Integer getPeriodicInformInterval() {
        return periodicInformInterval;
    }

    public void setPeriodicInformInterval(Integer periodicInformInterval) {
        this.periodicInformInterval = periodicInformInterval;
    }

    public Integer getRetryInterval() {
        return retryInterval;
    }

    public void setRetryInterval(Integer retryInterval) {
        this.retryInterval = retryInterval;
    }

    public String getConnectionRequestUrl() {
        return connectionRequestUrl;
    }

    public void setConnectionRequestUrl(String connectionRequestUrl) {
        this.connectionRequestUrl = connectionRequestUrl;
    }

    public List<Map> getConnectionRequestUsername() {
        return connectionRequestUsername;
    }

    public void setConnectionRequestUsername(List<Map> connectionRequestUsername) {
        this.connectionRequestUsername = connectionRequestUsername;
    }

    public List<Map> getConnectionRequestPassword() {
        return connectionRequestPassword;
    }

    public void setConnectionRequestPassword(List<Map> connectionRequestPassword) {
        this.connectionRequestPassword = connectionRequestPassword;
    }
}
