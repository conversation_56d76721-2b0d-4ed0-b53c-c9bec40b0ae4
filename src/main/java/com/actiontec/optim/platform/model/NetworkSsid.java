package com.actiontec.optim.platform.model;

import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.platform.service.SimpleAesEcbCryptoService;

import java.util.ArrayList;

public class NetworkSsid {

    private ArrayList<SsidCompositeKey> ssidCompositeKey;
    private String serial;
    private ArrayList<String> bssids;
    private String ssidKey;
    private ArrayList<RadioEnum> radioEnums;
    private Boolean enabled;
    private String name;
    private String password;
    private String securityMode;
    private String virtualKey;
    private ArrayList<String> radioVirtualKeys;
    private long lastReportTime;

    public void genVirtualKey(SimpleAesEcbCryptoService simpleAesEcbCryptoService) {
        NetworkSsidVirtualKey networkSsidVirtualKey = new NetworkSsidVirtualKey(serial, ssidCompositeKey);
        virtualKey = networkSsidVirtualKey.getEncryptedString(simpleAesEcbCryptoService);
    }

    public void genRadioVirtualKeys(SimpleAesEcbCryptoService simpleAesEcbCryptoService) {
        radioVirtualKeys = new ArrayList<>();
        for (RadioEnum radioEnum : radioEnums) {
            NetworkRadioVirtualKey networkRadioVirtualKey = new NetworkRadioVirtualKey(serial, radioEnum);
            radioVirtualKeys.add(networkRadioVirtualKey.getEncryptedString(simpleAesEcbCryptoService));
            //radioVirtualKeys.add(networkRadioVirtualKey.getEncryptedString());
        }
    }

    public ArrayList<SsidCompositeKey> getSsidCompositeKeys() {
        return ssidCompositeKey;
    }

    public void setSsidCompositeKey(ArrayList<SsidCompositeKey> ssidCompositeKey) {
        this.ssidCompositeKey = ssidCompositeKey;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public ArrayList<String> getBssids() {
        return bssids;
    }

    public void setBssids(ArrayList<String> bssids) {
        this.bssids = bssids;
    }

    public String getSsidKey() {
        return ssidKey;
    }

    public void setSsidKey(String ssidKey) {
        this.ssidKey = ssidKey;
    }

    public ArrayList<RadioEnum> getRadioEnums() {
        return radioEnums;
    }

    public void setRadioEnums(ArrayList<RadioEnum> radioEnums) {
        this.radioEnums = radioEnums;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSecurityMode() {
        return securityMode;
    }

    public void setSecurityMode(String securityMode) {
        this.securityMode = securityMode;
    }

    public String getVirtualKey() {
        return virtualKey;
    }

    public ArrayList<String> getRadioVirtualKeys() {
        return radioVirtualKeys;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }
}
