package com.actiontec.optim.platform.model.firmware;

public class ResponsePayload extends PayloadBase{
    private Data data;
    private Result result;

    public ResponsePayload() {
        this.data = new Data();
        this.result = new Result();
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }

    public class Data {
        private String url;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }

    public class Result {
        private String state;
        private long startTime;
        private long completeTime;
        private int faultCode;

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getCompleteTime() {
            return completeTime;
        }

        public void setCompleteTime(long completeTime) {
            this.completeTime = completeTime;
        }

        public int getFaultCode() {
            return faultCode;
        }

        public void setFaultCode(int faultCode) {
            this.faultCode = faultCode;
        }
    }
}
