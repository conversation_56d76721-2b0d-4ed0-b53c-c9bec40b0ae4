package com.actiontec.optim.platform.model;

public class NetworkVoiceNet {
    private Boolean enabled;
    private String status;
    private String proxyServer;
    private int proxyServerPort;
    private String proxyServerTransport;
    private String registrarServer;
    private int registrarServerPort;
    private String registrarServerTransport;
    private int registrationPeriod;
    private int registerRetryInterval;
    private String outboundProxy;
    private int outboundProxyPort;

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getProxyServer() {
        return proxyServer;
    }

    public void setProxyServer(String proxyServer) {
        this.proxyServer = proxyServer;
    }

    public int getProxyServerPort() {
        return proxyServerPort;
    }

    public void setProxyServerPort(int proxyServerPort) {
        this.proxyServerPort = proxyServerPort;
    }

    public String getProxyServerTransport() {
        return proxyServerTransport;
    }

    public void setProxyServerTransport(String proxyServerTransport) {
        this.proxyServerTransport = proxyServerTransport;
    }

    public String getRegistrarServer() {
        return registrarServer;
    }

    public void setRegistrarServer(String registrarServer) {
        this.registrarServer = registrarServer;
    }

    public int getRegistrarServerPort() {
        return registrarServerPort;
    }

    public void setRegistrarServerPort(int registrarServerPort) {
        this.registrarServerPort = registrarServerPort;
    }

    public String getRegistrarServerTransport() {
        return registrarServerTransport;
    }

    public void setRegistrarServerTransport(String registrarServerTransport) {
        this.registrarServerTransport = registrarServerTransport;
    }

    public int getRegistrationPeriod() {
        return registrationPeriod;
    }

    public void setRegistrationPeriod(int registrationPeriod) {
        this.registrationPeriod = registrationPeriod;
    }

    public int getRegisterRetryInterval() {
        return registerRetryInterval;
    }

    public void setRegisterRetryInterval(int registerRetryInterval) {
        this.registerRetryInterval = registerRetryInterval;
    }

    public String getOutboundProxy() {
        return outboundProxy;
    }

    public void setOutboundProxy(String outboundProxy) {
        this.outboundProxy = outboundProxy;
    }

    public int getOutboundProxyPort() {
        return outboundProxyPort;
    }

    public void setOutboundProxyPort(int outboundProxyPort) {
        this.outboundProxyPort = outboundProxyPort;
    }
}
