package com.actiontec.optim.platform.model;

import java.util.List;

public class FwImage {

    private String id;
    private String name;
    private String description;
    private String version;
    private String locationType;
    private String fileType;
    private String fileName;
    private int fileSize;
    private String fileLocation;
    private String secureUrl;
    private String fileEntry;
    private String fileStatus;
    private String username;
    private String password;
    private List<String> equipmentTypeIds;
    private String groupId;
    private String sequenceVersion;
    private Boolean isIsProduction;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getLocationType() {
        return locationType;
    }

    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileLocation() {
        return fileLocation;
    }

    public void setFileLocation(String fileLocation) {
        this.fileLocation = fileLocation;
    }

    public String getSecureUrl() {
        return secureUrl;
    }

    public void setSecureUrl(String secureUrl) {
        this.secureUrl = secureUrl;
    }

    public String getFileEntry() {
        return fileEntry;
    }

    public void setFileEntry(String fileEntry) {
        this.fileEntry = fileEntry;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public List<String> getEquipmentTypeIds() {
        return equipmentTypeIds;
    }

    public void setEquipmentTypeIds(List<String> equipmentTypeIds) {
        this.equipmentTypeIds = equipmentTypeIds;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getSequenceVersion() {
        return sequenceVersion;
    }

    public void setSequenceVersion(String sequenceVersion) {
        this.sequenceVersion = sequenceVersion;
    }

    public Boolean getIsProduction() {
        return isIsProduction;
    }

    public void setIsProduction(Boolean isProduction) {
        isIsProduction = isProduction;
    }
}
