package com.actiontec.optim.platform.model;

import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.service.SimpleAesEcbCryptoService;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Optional;

//@Configurable
@JsonDeserialize(using = NetworkRadioVirtualKey.Deserializer.class)
public class NetworkRadioVirtualKey {
    private final static Logger logger = LogManager.getLogger(NetworkRadioVirtualKey.class);

//    TODO: enable aspectj
//    @Autowired
//    private SimpleAesEcbCryptoService cryptoService;

    private String serial;
    private RadioEnum radioKey;

    public static NetworkRadioVirtualKey fromJsonString(String jsonStr) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            NetworkRadioVirtualKey networkSsidVirtualKey = objectMapper.readValue(jsonStr, NetworkRadioVirtualKey.class);
            return networkSsidVirtualKey;
        } catch (Exception e) {
            logger.error("failed to parse NetworkSsidVirtualKey:[{}]", jsonStr, e);
            throw new RuntimeException(e);
        }
    }

    public NetworkRadioVirtualKey(String serial, RadioEnum radioKey) {
        this.serial = serial;
        this.radioKey = radioKey;
    }

    public String toJsonString() {
        LinkedHashMap<String, Object> jsonMap = new LinkedHashMap();
        jsonMap.put("serial", serial);
        Optional.ofNullable(radioKey).ifPresent(p->jsonMap.put("radioKey", p.getRadioKey()));

        String result = "";
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            result = objectMapper.writeValueAsString(jsonMap);
        } catch (JsonProcessingException e) {
            // should not happen
            logger.error("toJson failed. [{}]", e);
            throw new RuntimeException(e);
        }
        return result;
    }

    public String getEncryptedString(SimpleAesEcbCryptoService cryptoService) {
        String jsonStr = toJsonString();
        return cryptoService.encrypt(jsonStr, ApplicationConstants.ENCRYPT_KEY);
    }

    public String getSerial() {
        return serial;
    }

    public RadioEnum getRadioKey() {
        return radioKey;
    }

    public static class Deserializer extends StdDeserializer<NetworkRadioVirtualKey> {
        public Deserializer() {
            this(null);
        }

        public Deserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public NetworkRadioVirtualKey deserialize(JsonParser jp, DeserializationContext ctxt)
                throws IOException, JsonProcessingException {
            JsonNode node = jp.getCodec().readTree(jp);
            String serial = node.get("serial").asText();
            String radioKeyStr = node.get("radioKey").asText();
            Optional<RadioEnum> radioEnumOptional = RadioEnum.parseByRadioKey(radioKeyStr);
            if (!radioEnumOptional.isPresent()) {
                throw new RuntimeException("failed to parse radioKey");
            }
            return new NetworkRadioVirtualKey(serial, radioEnumOptional.get());
        }
    }

}
