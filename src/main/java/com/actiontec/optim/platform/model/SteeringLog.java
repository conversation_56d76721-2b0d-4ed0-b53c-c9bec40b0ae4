package com.actiontec.optim.platform.model;

public class SteeringLog {
    private String macAddress;
    private String type;
    private long timestamp;
    private String log;
    private SteeringData steeringData;
    private DiagnosticData diagnosticData;
    private Origin origin;
    private Dest dest;
    private Result result;

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public SteeringData getSteeringData() {
        return steeringData;
    }

    public void setSteeringData(SteeringData steeringData) {
        this.steeringData = steeringData;
    }

    public DiagnosticData getDiagnosticData() {
        return diagnosticData;
    }

    public void setDiagnosticData(DiagnosticData diagnosticData) {
        this.diagnosticData = diagnosticData;
    }

    public Origin getOrigin() {
        return origin;
    }

    public void setOrigin(Origin origin) {
        this.origin = origin;
    }

    public Dest getDest() {
        return dest;
    }

    public void setDest(Dest dest) {
        this.dest = dest;
    }

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }
    public static class DiagnosticData {
        private String serialNumber;
        private String radioKey;
        private long trigger;
        private String triggerDescription;
        private long reasonCode;
        private String reasonDescription;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(String radioKey) {
            this.radioKey = radioKey;
        }

        public long getTrigger() {
            return trigger;
        }

        public void setTrigger(long trigger) {
            this.trigger = trigger;
        }

        public String getTriggerDescription() {
            return triggerDescription;
        }

        public void setTriggerDescription(String triggerDescription) {
            this.triggerDescription = triggerDescription;
        }

        public long getReasonCode() {
            return reasonCode;
        }

        public void setReasonCode(long reasonCode) {
            this.reasonCode = reasonCode;
        }

        public String getReasonDescription() {
            return reasonDescription;
        }

        public void setReasonDescription(String reasonDescription) {
            this.reasonDescription = reasonDescription;
        }
    }

    public static class SteeringData {
        private String type;
        private String result;
        private long time;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }
    }

    public static class Origin{
        private String serialNumber;
        private String radioKey;
        private long rssi;
        private long channel;
        private double phyRate;
        private long airTimeUsage;
        private long fat;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(String radioKey) {
            this.radioKey = radioKey;
        }

        public long getRssi() {
            return rssi;
        }

        public void setRssi(long rssi) {
            this.rssi = rssi;
        }

        public long getChannel() {
            return channel;
        }

        public void setChannel(long channel) {
            this.channel = channel;
        }

        public double getPhyRate() {
            return phyRate;
        }

        public void setPhyRate(double phyRate) {
            this.phyRate = phyRate;
        }

        public long getAirTimeUsage() {
            return airTimeUsage;
        }

        public void setAirTimeUsage(long airTimeUsage) {
            this.airTimeUsage = airTimeUsage;
        }

        public long getFat() {
            return fat;
        }

        public void setFat(long fat) {
            this.fat = fat;
        }
    }
    public static class Dest{
        private String serialNumber;
        private String radioKey;
        private long rssi;
        private long channel;
        private double phyRate;
        private long airTimeUsage;
        private long fat;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(String radioKey) {
            this.radioKey = radioKey;
        }

        public long getRssi() {
            return rssi;
        }

        public void setRssi(long rssi) {
            this.rssi = rssi;
        }

        public long getChannel() {
            return channel;
        }

        public void setChannel(long channel) {
            this.channel = channel;
        }

        public double getPhyRate() {
            return phyRate;
        }

        public void setPhyRate(double phyRate) {
            this.phyRate = phyRate;
        }

        public long getAirTimeUsage() {
            return airTimeUsage;
        }

        public void setAirTimeUsage(long airTimeUsage) {
            this.airTimeUsage = airTimeUsage;
        }

        public long getFat() {
            return fat;
        }

        public void setFat(long fat) {
            this.fat = fat;
        }

    }
    public static class Result{
        private String serialNumber;
        private String radioKey;
        private long rssi;
        private long channel;
        private double phyRate;
        private long airTimeUsage;
        private long fat;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(String radioKey) {
            this.radioKey = radioKey;
        }

        public long getRssi() {
            return rssi;
        }

        public void setRssi(long rssi) {
            this.rssi = rssi;
        }

        public long getChannel() {
            return channel;
        }

        public void setChannel(long channel) {
            this.channel = channel;
        }

        public double getPhyRate() {
            return phyRate;
        }

        public void setPhyRate(double phyRate) {
            this.phyRate = phyRate;
        }

        public long getAirTimeUsage() {
            return airTimeUsage;
        }

        public void setAirTimeUsage(long airTimeUsage) {
            this.airTimeUsage = airTimeUsage;
        }

        public long getFat() {
            return fat;
        }

        public void setFat(long fat) {
            this.fat = fat;
        }

    }
}
