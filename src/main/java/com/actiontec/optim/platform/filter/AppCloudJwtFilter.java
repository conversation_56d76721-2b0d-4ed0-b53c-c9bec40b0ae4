package com.actiontec.optim.platform.filter;

import com.actiontec.optim.platform.service.AppCloudService;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.entities.AppCloudConfig;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@WebFilter("/actiontec/api/v6/test")
@Component
public class AppCloudJwtFilter implements Filter {
    private static final Logger logger = LogManager.getLogger(AppCloudJwtFilter.class);

    @Autowired
    private AppCloudService appCloudService;

    @Autowired
    private ObjectMapper objectMapper;

    // Test RSA Public Key (hardcoded for now)
    private static final String TEST_RSA_PUBLIC_KEY = 
        "-----BEGIN PUBLIC KEY-----\n" +
        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk2QhF9mr4bp+Wu7OTgwe\n" +
        "9CSA9iFGubUmEx12sqwowk2UXRcj9LPCY8WEUDUtmh+B98bfUJMwNTqhqE9e1ZE8\n" +
        "DzaXon9RFiNpK9zMc/0eZbVgEJKW6MMjTRtJg4G818qcNacpptL9b1vlfCMOdObC\n" +
        "vwKsWaxERv7rxuxbKtsANexSfG7Umztqjd6ZddvmaJ1aMnhQoKi/ISFgrOqENyPM\n" +
        "AyOlKzeDPfYwQm9YzeUkbf8U952jZfJfL8DdV6JR6kVjvNEu90y6qx0ziqPLhb7Y\n" +
        "aqz5X+lfs3su6SA2YLWBobm9df1uivMzVC3Slgqtw1DFgvXwseDxbZS6KmegFpk2\n" +
        "dQIDAQAB\n" +
        "-----END PUBLIC KEY-----";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Initialization if needed
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) 
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;

        try {
            // 1. Extract Authorization header
            String authorizationHeader = httpRequest.getHeader("X-Authorization");
            if (CustomStringUtils.isEmpty(authorizationHeader)) {
                sendUnauthorizedResponse(httpResponse, "authentication failed");
                return;
            }

            // 2. Extract JWT token from header
            String token = extractTokenFromHeader(authorizationHeader);
            if (CustomStringUtils.isEmpty(token)) {
                sendUnauthorizedResponse(httpResponse, "authentication failed");
                return;
            }

            // 3. Extract kid from JWT header
            String kid = extractKidFromToken(token);
            if (CustomStringUtils.isEmpty(kid)) {
                sendUnauthorizedResponse(httpResponse, "authentication failed");
                return;
            }

            // 4. Get public key for validation
            PublicKey publicKey = getPublicKeyForValidation(kid);
            if (publicKey == null) {
                sendUnauthorizedResponse(httpResponse, "authentication failed, public key is null.");
                return;
            }

            // 5. Validate JWT token
            if (!validateJwtToken(token, publicKey)) {
                sendUnauthorizedResponse(httpResponse, "authentication failed, please check token is expired or public key is invalid.");
                return;
            }

            // 6. Token is valid, continue with the request
            logger.debug("App Cloud JWT token validation successful for kid: {}", kid);
            filterChain.doFilter(servletRequest, servletResponse);

        } catch (Exception e) {
            logger.error("Error in AppCloudJwtFilter: {}", e.getMessage(), e);
            sendUnauthorizedResponse(httpResponse, "authentication failed");
        }
    }

    @Override
    public void destroy() {
        // Cleanup if needed
    }

    /**
     * Extract token from Authorization header
     */
    private String extractTokenFromHeader(String authorizationHeader) {
        if (CustomStringUtils.isEmpty(authorizationHeader)) {
            return CustomStringUtils.EMPTY;
        }

        String[] parts = authorizationHeader.split(" ");
        if (parts.length == 2 && "Bearer".equalsIgnoreCase(parts[0])) {
            return parts[1];
        }

        return CustomStringUtils.EMPTY;
    }

    /**
     * Extract kid from JWT token header
     */
    private String extractKidFromToken(String token) {
        try {
            String[] chunks = token.split("\\.");
            if (chunks.length < 2) {
                return CustomStringUtils.EMPTY;
            }

            String header = new String(Base64.getUrlDecoder().decode(chunks[0]));
            @SuppressWarnings("unchecked")
            Map<String, Object> headerMap = objectMapper.readValue(header, Map.class);
            return (String) headerMap.get("kid");

        } catch (Exception e) {
            logger.debug("Failed to extract kid from token: {}", e.getMessage());
            return CustomStringUtils.EMPTY;
        }
    }

    /**
     * Get public key for validation
     * Get from database by kid, fallback to test public key if not found
     */
    private PublicKey getPublicKeyForValidation(String kid) {
        try {
            // Try to get from database first
            AppCloudConfig config = appCloudService.getAppCloudConfigByKid(kid);
            return parsePublicKey(config.getPublicKeyPem());
        } catch (Exception e) {
            logger.debug("Failed to get public key from database for kid: {}, using test key. Error: {}", kid, e.getMessage());

            // Fallback to test public key
            try {
                return parsePublicKey(TEST_RSA_PUBLIC_KEY);
            } catch (Exception ex) {
                logger.error("Failed to parse test public key: {}", ex.getMessage());
                return null;
            }
        }
    }

    /**
     * Parse PEM format public key
     */
    private PublicKey parsePublicKey(String publicKeyPem) throws Exception {
        String publicKeyContent = publicKeyPem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(publicKeyContent);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(spec);
    }

    /**
     * Validate JWT token with public key
     */
    private boolean validateJwtToken(String token, PublicKey publicKey) {
        try {
            Jws<Claims> jwsClaims = Jwts.parser()
                    .setSigningKey(publicKey)
                    .parseClaimsJws(token);

            // Check if token is expired
            if (isTokenExpired(jwsClaims.getBody().getExpiration())) {
                logger.debug("Token expired, expiration: {}", jwsClaims.getBody().getExpiration());
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.debug("JWT token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Check if token is expired
     */
    private boolean isTokenExpired(Date expiration) {
        if (expiration == null) {
            return true;
        }
        return expiration.before(new Date());
    }

    /**
     * Send unauthorized response
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("message", message);
        errorResponse.put("status", 401);

        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
    }
}
