package com.actiontec.optim.platform.filter;

import com.actiontec.optim.platform.service.AppCloudService;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.entities.AppCloudConfig;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@WebFilter("/actiontec/app/api/v6/test")
@Component
public class AppCloudJwtFilter implements Filter {
    private static final Logger logger = LogManager.getLogger(AppCloudJwtFilter.class);

    @Autowired
    private AppCloudService appCloudService;

    @Autowired
    private ObjectMapper objectMapper;

    // Test RSA Public Key (hardcoded for now)
    private static final String TEST_RSA_PUBLIC_KEY = 
        "-----BEGIN PUBLIC KEY-----\n" +
        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41\n" +
        "fGnJm6gOdrj8ym3rFkEjWT2btf+FxKaGZSoOq8qKwvXDsOLrJhVhsxzxe8KZqNQ5\n" +
        "qjpqUHdwBzWP/31Oqf5Lg5p2hKsTeNem/V41fGnJm6gOdrj8ym3rFkEjWT2btf+F\n" +
        "xKaGZSoOq8qKwvXDsOLrJhVhsxzxe8KZqNQ5qjpqUHdwBzWP/31Oqf5Lg5p2hKsT\n" +
        "eNem/V41fGnJm6gOdrj8ym3rFkEjWT2btf+FxKaGZSoOq8qKwvXDsOLrJhVhsxzx\n" +
        "e8KZqNQ5qjpqUHdwBzWP/31Oqf5Lg5p2hKsTeNem/V41fGnJm6gOdrj8ym3rFkEj\n" +
        "WT2btf+FxKaGZSoOq8qKwvXDsOLrJhVhsxzxe8KZqNQ5qjpqUHdwBzWP/31Oqf5L\n" +
        "QIDAQAB\n" +
        "-----END PUBLIC KEY-----";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Initialization if needed
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) 
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;

        try {
            // 1. Extract Authorization header
            String authorizationHeader = httpRequest.getHeader("X-Authorization");
            if (CustomStringUtils.isEmpty(authorizationHeader)) {
                sendUnauthorizedResponse(httpResponse, "Missing Authorization header");
                return;
            }

            // 2. Extract JWT token from header
            String token = extractTokenFromHeader(authorizationHeader);
            if (CustomStringUtils.isEmpty(token)) {
                sendUnauthorizedResponse(httpResponse, "Invalid Authorization header format");
                return;
            }

            // 3. Extract kid from JWT header
            String kid = extractKidFromToken(token);
            if (CustomStringUtils.isEmpty(kid)) {
                sendUnauthorizedResponse(httpResponse, "No kid found in JWT header");
                return;
            }

            // 4. Get public key for validation
            PublicKey publicKey = getPublicKeyForValidation(kid);
            if (publicKey == null) {
                sendUnauthorizedResponse(httpResponse, "No public key found for kid: " + kid);
                return;
            }

            // 5. Validate JWT token
            if (!validateJwtToken(token, publicKey)) {
                sendUnauthorizedResponse(httpResponse, "JWT token validation failed");
                return;
            }

            // 6. Token is valid, continue with the request
            logger.debug("App Cloud JWT token validation successful for kid: {}", kid);
            filterChain.doFilter(servletRequest, servletResponse);

        } catch (Exception e) {
            logger.error("Error in AppCloudJwtFilter: {}", e.getMessage(), e);
            sendUnauthorizedResponse(httpResponse, "Authentication failed");
        }
    }

    @Override
    public void destroy() {
        // Cleanup if needed
    }

    /**
     * Extract token from Authorization header
     */
    private String extractTokenFromHeader(String authorizationHeader) {
        if (CustomStringUtils.isEmpty(authorizationHeader)) {
            return CustomStringUtils.EMPTY;
        }

        String[] parts = authorizationHeader.split(" ");
        if (parts.length == 2 && "Bearer".equalsIgnoreCase(parts[0])) {
            return parts[1];
        }

        return CustomStringUtils.EMPTY;
    }

    /**
     * Extract kid from JWT token header
     */
    private String extractKidFromToken(String token) {
        try {
            String[] chunks = token.split("\\.");
            if (chunks.length < 2) {
                return CustomStringUtils.EMPTY;
            }

            String header = new String(Base64.getUrlDecoder().decode(chunks[0]));
            Map<String, Object> headerMap = objectMapper.readValue(header, Map.class);
            return (String) headerMap.get("kid");

        } catch (Exception e) {
            logger.debug("Failed to extract kid from token: {}", e.getMessage());
            return CustomStringUtils.EMPTY;
        }
    }

    /**
     * Get public key for validation
     * Currently uses test public key, will use database in the future
     */
    private PublicKey getPublicKeyForValidation(String kid) {
        try {
            // TODO: Get from database in the future
            // AppCloudConfig config = appCloudService.getAppCloudConfigByKid(kid);
            // return parsePublicKey(config.getPublicKeyPem());

            // Currently use test public key
            return parsePublicKey(TEST_RSA_PUBLIC_KEY);
        } catch (Exception e) {
            logger.debug("Failed to get public key for kid: {}, error: {}", kid, e.getMessage());
            return CustomStringUtils.EMPTY;
        }
    }

    /**
     * Parse PEM format public key
     */
    private PublicKey parsePublicKey(String publicKeyPem) throws Exception {
        String publicKeyContent = publicKeyPem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(publicKeyContent);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(spec);
    }

    /**
     * Validate JWT token with public key
     */
    private boolean validateJwtToken(String token, PublicKey publicKey) {
        try {
            Jws<Claims> jwsClaims = Jwts.parserBuilder()
                    .setSigningKey(publicKey)
                    .build()
                    .parseClaimsJws(token);

            // Check if token is expired
            if (isTokenExpired(jwsClaims.getBody().getExpiration())) {
                logger.debug("Token expired, expiration: {}", jwsClaims.getBody().getExpiration());
                return false;
            }

            return true;
        } catch (Exception e) {
            logger.debug("JWT token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Check if token is expired
     */
    private boolean isTokenExpired(Date expiration) {
        if (expiration == null) {
            return true;
        }
        return expiration.before(new Date());
    }

    /**
     * Send unauthorized response
     */
    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", "Unauthorized");
        errorResponse.put("message", message);
        errorResponse.put("status", 401);

        String jsonResponse = objectMapper.writeValueAsString(errorResponse);
        response.getWriter().write(jsonResponse);
    }
}
