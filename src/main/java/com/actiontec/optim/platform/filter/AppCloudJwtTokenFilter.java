package com.actiontec.optim.platform.filter;

import com.actiontec.optim.platform.api.v6.validator.AppCloudJwtTokenValidator;
import com.actiontec.optim.platform.service.AppCloudService;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.config.JwtConfig;
import com.incs83.security.auth.jwt.extractor.TokenExtractor;
import com.incs83.security.config.WebSecurityConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class AppCloudJwtTokenFilter extends OncePerRequestFilter {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private AppCloudService appCloudService;

    @Autowired
    private AppCloudJwtTokenValidator appCloudJwtTokenValidator;

    @Autowired
    private TokenExtractor tokenExtractor;

    @Autowired
    private JwtConfig jwtConfig;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        logger.info("Filter uri: {}", request.getRequestURI());

        try {
            // Extract the JWT token payload from the header using tokenExtractor
            String rawHeader = request.getHeader(WebSecurityConfig.JWT_TOKEN_HEADER_PARAM);
            String token = tokenExtractor.extract(rawHeader);

            String roleName = appCloudJwtTokenValidator.extractRoleName(token, jwtConfig.getTokenSigningKey());

            if (CustomStringUtils.isEmpty(token)) {
                sendUnauthorizedResponse(response, "Authentication failed: token is missing or empty.");
                return;
            }

            // Extract kid from JWT token header
            String kid = extractKidFromToken(token);
            if (CustomStringUtils.isEmpty(kid)) {
                sendUnauthorizedResponse(response, "Authentication failed: token kid is missing.");
                return;
            }

            // Get public key for validation
            PublicKey publicKey = getPublicKeyForValidation(kid);
            if (publicKey == null) {
                sendUnauthorizedResponse(response, "Authentication failed: public key is null.");
                return;
            }

            // Validate JWT token
            if (!validateJwtToken(token, publicKey)) {
                sendUnauthorizedResponse(response, "Authentication failed: invalid or expired token.");
                return;
            }

            logger.debug("App Cloud JWT token validation successful for kid: {}", kid);
            filterChain.doFilter(request, response);

        } catch (Exception e) {
            logger.error("Error in AppCloudJwtFilter: {}", e.getMessage(), e);
            sendUnauthorizedResponse(response, "Authentication failed due to internal error.");
        }


    }
}
