package com.actiontec.optim.platform.adapter;

import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ISPService;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.queries.CompartmentSQL;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.Role;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.mt.MongoTenantTemplate;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.constants.ApplicationCommonConstants.COMMON_DEVICE_STATUS_RETENTION_TIME;

@Service
public class At3Adapter {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private MongoTenantTemplate mongoTenantTemplate;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private ISPService ispService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ObjectMapper objectMapper;

    private HashMap<String, String> commonProps;

//    @PostConstruct
//    private void init(){
//        commonProps = commonService.read("CommonConfig");
//    }

    public HashMap<String, String> getCommonProps() {
        // XXX: no sync, should not have big pressure even if multiple threads trigger initialization in the same time.
        return commonService.read("CommonConfig");
    }

    public int getEquipmentOfflineMins() {
        int equipmentOfflineMins = 7;
        try {
            equipmentOfflineMins = Integer.valueOf(getCommonProps().get(COMMON_DEVICE_STATUS_RETENTION_TIME));
        } catch (Exception e) {
            logger.error("failed to getEquipmentOfflineMins", e);
        }
        return equipmentOfflineMins;
    }

    public String getRgwSerialByStn(String stn) {
        try {
            Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
            return equipment.getRgwSerial();
        } catch (ValidationException e) {
            throw new NoSuchEntityException();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public DBCollection getMongoDbCollection(String collection) {
        return mongoTenantTemplate.getCollection(collection);
    }

    public void findAndModifyCollection(HashMap<String, Object> params, String collection, String sortBy, BasicDBObject basicDBObject, boolean upsert) {
        BasicDBObject query = new BasicDBObject();
        BasicDBObject sort = new BasicDBObject();
        params.forEach(query::put);
        if (Objects.nonNull(sortBy))
            sort.put(sortBy, Integer.valueOf(-1));
        getMongoDbCollection(collection).findAndModify(query, (DBObject) null, sort, false, basicDBObject, true, upsert);
    }

    public void insertDataInCollection(String collection, DBObject object) {
        getMongoDbCollection(collection).insert(object);
    }

    public String getGroupIdByIspId(String ispId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);
        String groupId = null;
        try {
            List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_COMPARTMENT_BY_ISPID, params);
            if(compartmentList != null) {
                groupId = compartmentList.get(0).getId();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return groupId;
    }

    public String getGroupIdByClusterId(String clusterId) {
        try {
            return manageCommonService.getGroupIdFromClusterId(clusterId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String getIspNameByGroupId(String groupId) {
        String ispName = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, groupId);
            if (Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId())) {
                ISPDTO ispdto = ispService.getISPById(compartment.getIspId());
                ispName = ispdto.getData().getName();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ispName;
    }

    public String getIspNameById(String ispId) {
        String ispName = null;
        try {
            ISPDTO ispdto = ispService.getISPById(ispId);
            ispName = ispdto.getData().getName();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return ispName;
    }

    public String getIspId() {
        try {
            return manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String getIspIdByGroupId(String groupId) {
        try {
            return manageCommonService.getIspByGroupId(groupId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public <C extends Collection, E> JavaType getCollectionType(Class<C> collectionClass, Class<E> elementClass) {
        return objectMapper.getTypeFactory().constructCollectionType(collectionClass, elementClass);
    }

    public DBObject getDbObject(Object value) {
        BasicDBObject answer;
        try {
            Map m = objectMapper.convertValue(value, Map.class);
            answer = new BasicDBObject(m);
        } catch (Exception e) {
            logger.warn("Conversion has fallen back to generic Object -> DBObject, but unable to convert type {}. Returning null.",
                    value.getClass().getCanonicalName());
            return null;
        }
        return answer;
    }

    public String getRoleNameById(String id) {
        String name = "";
        try {
            Role role = (Role) dataAccessService.read(Role.class, id);
            if (role != null) {
                name = role.getName();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return name;
    }
}
