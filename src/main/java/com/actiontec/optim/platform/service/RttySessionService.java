package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.api.v6.enums.RttySessionStatus;
import com.actiontec.optim.platform.repository.RttySessionRepo;
import com.actiontec.optim.service.AuditService;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.entities.NCSEquipment;
import com.incs83.app.entities.RttyServerConfig;
import com.incs83.app.entities.RttySession;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.FORWARD_SLASH;

/**
 * RTTY會話服務
 */
@Service
public class RttySessionService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    MongoServiceImpl mongoService;

    @Autowired
    private RttySessionRepo rttySessionRepo;

    /**
     * Get all RTTY sessions
     *
     * @param queryDTO Query parameters
     * @return Paginated list of sessions
     * @throws Exception If query fails
     */
    public PaginationResponse<RttySessionDTO> getAllSessions(RttySessionQueryDTO queryDTO) throws Exception {
        return rttySessionRepo.getAllSessions(queryDTO);
    }

    public RttySession createSession(NCSEquipment equipment, Integer durationHours, RttyServerConfig rttyServerConfig) throws Exception {
        String id = CommonUtils.generateUUID();
        String token = CommonUtils.generateUUID();
        String sshUrl = generateRttySshUrl(rttyServerConfig, equipment);
        String httpUrl = generateRttyHttpUrl(rttyServerConfig, equipment);
        // Get current UTC time
        LocalDateTime createdAt = LocalDateTime.now(ZoneOffset.UTC);
        String createdBy = CommonUtils.getUserIdOfLoggedInUser();
        // Calculate expiration time (current time plus duration)
        LocalDateTime expiredAt = createdAt.plusHours(durationHours);

        // Set session properties
        RttySession rttySession = new RttySession(id, equipment.getId(), token,
                sshUrl, httpUrl, RttySessionStatus.INITIAL, rttyServerConfig.getId(), createdAt, createdBy, expiredAt);
        rttySessionRepo.createSession(rttySession);

        return rttySession;
    }

    /**
     * Get RTTY session by equipment ID
     *
     * @param equipmentId Equipment ID
     * @return Session details
     * @throws Exception If query fails
     */
    public RttySession getSessionByEquipmentId(String equipmentId) throws Exception {
        return rttySessionRepo.getSessionByEquipmentId(equipmentId);
    }

    public RttySession getSessionById(String id) throws Exception {
        return rttySessionRepo.getSessionById(id);
    }

    public void updateSession(RttySession rttySession) throws Exception {
        rttySessionRepo.updateSession(rttySession);
    }

    /**
     * Delete RTTY session
     *
     * @param sessionId Session ID
     * @throws Exception If deletion fails
     */
    public void deleteSession(String sessionId) throws Exception {
        // Validate session ID
        if (StringUtils.isEmpty(sessionId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Session ID cannot be empty");
        }

        // get session

        // send rpc to delete session

        // Delete session
        rttySessionRepo.deleteSession(sessionId);
    }

    // check session is over limit
    public void validateMaxSession(Integer maxSession) throws ValidationException {
        if (rttySessionRepo.countActiveSessions() >= maxSession) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max sessions reached");
        }
    }

    /**
     * Generate SSH URL for RTTY session
     * In fact this URL is websocket url
     *
     * @param rttyServerConfig
     * @param ncsEquipment
     * @return
     */
    private String generateRttySshUrl(RttyServerConfig rttyServerConfig, NCSEquipment ncsEquipment) {
        return WSS + rttyServerConfig.getHost() + RTTY_SSH_CONNECT_PATH + ncsEquipment.getId();
    }

    /**
     * Generate HTTP URL for RTTY session
     *
     * @param rttyServerConfig
     * @param ncsEquipment
     * @return
     * @throws ValidationException
     */
    private String generateRttyHttpUrl(RttyServerConfig rttyServerConfig, NCSEquipment ncsEquipment) throws ValidationException {
        // example: https://rttys-alpha.optimportal.com/web/{equipmentId}/http/{lanIp}
//        HashMap<String, Object> queryParam = new HashMap<>();
//        queryParam.put("userId", ncsEquipment.getNetworkId());
//        queryParam.put("serialNumber", ncsEquipment.getSerial());
//        DBObject result = mongoService.findOne(AP_DETAIL, queryParam);
//        if (result == null || ObjectUtils.isEmpty(result.get("lanIp"))) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Unable to get lanIp for " + ncsEquipment.getSerial());
//        }
//        String lanIp = result.get("lanIp").toString();

        return HTTPS + rttyServerConfig.getHost() + RTTY_HTTP_ACCESS_WEB_PATH + ncsEquipment.getId() + RTTY_HTTP_ACCESS_HTTP_PATH;
    }

    /**
     * Validate if a session's expiration time is valid (not exceeding 72 hours from creation)
     *
     * @param session The session to validate
     * @throws ValidationException If the expiration time exceeds the maximum allowed duration
     */
    public void validateSessionExpiredAt(RttySession session, Integer extendHours) throws Exception {
        // The Maximum allowed duration is 72 hours from creation
        LocalDateTime maxAllowedExpiredAt = session.getCreatedAt().plusHours(MAX_EXPIRED_HOURS);
        // Calculate new expiration time
        LocalDateTime currentExpiredAt = session.getExpiredAt();
        LocalDateTime newExpiredAt = currentExpiredAt.plusHours(extendHours);

        // Check if expiration time exceeds the maximum allowed
        if (newExpiredAt.isAfter(maxAllowedExpiredAt)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Total session duration cannot exceed 72 hours from creation time");
        }
    }
}
