package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.mapper.equipment.NetworkStreamContextMapper;
import com.actiontec.optim.platform.model.equipment.NetworkDownstreamContext;
import com.actiontec.optim.platform.model.equipment.NetworkUpstreamContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NetworkStreamService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private NetworkStreamContextMapper streamContextMapper;

    public NetworkUpstreamContext findUpstreamContextByStnAndSerial(String stn, String serial) {
        String userId = at3Adapter.getRgwSerialByStn(stn);

        ApDetailDto apDetail = apDetailDao.findNetworkByUserIdAndSerial(userId, serial);

        return streamContextMapper.toNetworkUpstreamContext(apDetail);
    }

    public NetworkDownstreamContext findDownstreamContextByStnAndSerial(String stn, String serial) {
        String userId = at3Adapter.getRgwSerialByStn(stn);

        ApDetailDto apDetail = apDetailDao.findNetworkByUserIdAndSerial(userId, serial);

        return streamContextMapper.toNetworkDownstreamContext(apDetail);
    }
}
