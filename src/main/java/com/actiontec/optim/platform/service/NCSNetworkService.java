package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.InternetServiceProviderDao;
import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.api.v6.mapper.ProxyCpeApiResponseMapper;
import com.actiontec.optim.platform.repository.ClusterNetworkRepo;
import com.actiontec.optim.platform.repository.NCSNetworkRepo;
import com.actiontec.optim.platform.repository.SubscriberNetworkRepo;
import com.actiontec.optim.service.CpeRpcService;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.NCSEquipment;
import com.incs83.app.entities.NCSNetwork;
import com.incs83.app.entities.SubscriberNetwork;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.DBObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

import static com.actiontec.optim.platform.constant.ApplicationConstants.STRING_EMPTY;
import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.RpcConstants.CPE_API_METHOD;
import static com.incs83.app.constants.misc.RpcConstants.NCS_NETWORK_CPE_URI;

@Service
public class NCSNetworkService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    NCSIspService ispService;

    @Autowired
    NCSGroupService ncsGroupService;

    @Autowired
    InternetServiceProviderDao internetServiceProviderDao;

    @Autowired
    NCSEquipmentService ncsEquipmentService;

    @Autowired
    private NCSNetworkRepo networkRepo;

    @Autowired
    private SubscriberNetworkRepo subscriberNetworkRepo;

    @Autowired
    private ClusterNetworkRepo clusterNetworkRepo;

    @Autowired
    private CommonService commonService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private CpeRpcService cpeRpcService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    ProxyCpeApiResponseMapper proxyCpeApiResponseMapper;

    // IAM network api
    public PaginationResponse<NCSNetworkDTO> getAllNetworks(NCSNetworkQueryDTO queryDTO) throws Exception {
        return networkRepo.getAllNetwork(queryDTO);
    }

    public Map<String, String> createNetwork(NCSNetworkRequest networkRequest) throws Exception {
        Map<String, String> result = new HashMap<>();
        String ispId = networkRequest.getIspId();
        String groupId = CommonUtils.getGroupIdOfLoggedInUser();
        String defaultClusterId;
        String subscriberId = networkRequest.getSubscriberId();
        String networkId;

        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(groupId);
            if (!StringUtils.equalsIgnoreCase(ispId, userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
            }
        }

        defaultClusterId = ncsGroupService.getDefaultClusterIdByIspId(ispId);

        // step 1: check ispId is existed
        String ispName = internetServiceProviderDao.getIspName(ispId);
        if (StringUtils.isEmpty(ispName)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The isp doesn't exist, please create isp.");
        }

        // step 2: create network data
        networkId = networkRepo.createNetwork(networkRequest);

        if (StringUtils.isEmpty(networkId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Create network failed");
        }

        // step 3: check equipment is under this isp
        if (CollectionUtils.isNotEmpty(networkRequest.getEquipmentSerialList())) {
            List<NCSEquipment> equipmentList = ncsEquipmentService.getNCSEquipmentListBySerialList(networkRequest.getEquipmentSerialList());
            if (equipmentList.size() != networkRequest.getEquipmentSerialList().size()) {
                networkRepo.deleteNetwork(networkId);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some equipment does not exist, please check equipment.");
            }

            List<String> equipmentIdRequest = equipmentList.stream()
                    .map(NCSEquipment::getId)
                    .collect(Collectors.toList());

            if (!ncsEquipmentService.checkEquipmentListByIspId(ispId, equipmentIdRequest)) {
                networkRepo.deleteNetwork(networkId);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some equipment is not under ispId: " + ispId + ", please check equipment.");
            }

            // step 4: update ncs_equipment networkId and bind to subscriber
            // check all equipments are not bound by other network
            List<NCSEquipment> ispUnbindEquipmentList = equipmentList.stream()
                    .filter(equipment -> StringUtils.equals(equipment.getIspId(), ispId) && StringUtils.isEmpty(equipment.getNetworkId()))
                    .collect(Collectors.toList());
            if (equipmentList.size() != ispUnbindEquipmentList.size()) {
                networkRepo.deleteNetwork(networkId);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some equipment is under other network, please check equipment");
            }
            ncsEquipmentService.updateNetworkAndSubscriberByIdList(ispId, equipmentIdRequest, networkId, subscriberId);
        }

        // step 5: create subscriber_network data
        subscriberNetworkRepo.create(subscriberId, networkId);

        // step 6: create cluster_network data
        clusterNetworkRepo.create(defaultClusterId, networkId);

        result.put("networkId", networkId);

        return result;
    }

    public NCSNetworkDetailDTO getNetworkDetailDTO(String networkId) throws Exception {
        NCSNetworkDetailDTO networkDetailDTO = null;
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            networkDetailDTO = networkRepo.getNetworkDetailDTOById(networkId);

            if (StringUtils.isEmpty(networkDetailDTO.getIspId()) || !StringUtils.equals(networkDetailDTO.getIspId(), userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
            }
        }

        if (ObjectUtils.isEmpty(networkDetailDTO)) {
            networkDetailDTO = networkRepo.getNetworkDetailDTOById(networkId);
        }

        // prepare network equipment serial list
        List<String> equipmentSerialList = ncsEquipmentService.getNCSEquipmentListByNetworkId(networkId)
                .stream()
                .map(NCSEquipment::getSerial)
                .collect(Collectors.toList());
        networkDetailDTO.setEquipmentSerialList(equipmentSerialList);

        return networkDetailDTO;
    }

    public void updateNetwork(String networkId, NCSNetworkRequest networkRequest) throws Exception {
        NCSNetworkDetailDTO networkDetailDTO = null;
        String ispId = networkRequest.getIspId();
        String subscriberId = networkRequest.getSubscriberId();
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            if (!StringUtils.equals(ispId, userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
            }
        }

        // step 1: check ispId is existed
        String ispName = internetServiceProviderDao.getIspName(ispId);
        if (StringUtils.isEmpty(ispName)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The isp doesn't exist, please create isp.");
        }

        // step 2: check subscriber_network mapping
        List<SubscriberNetwork> oldSubscriberNetworks = subscriberNetworkRepo.getSubscriberNetworkByNetworkId(networkId);
        if (CollectionUtils.isEmpty(oldSubscriberNetworks)) {
            // create subscriber_network data
            subscriberNetworkRepo.create(subscriberId, networkId);
        } else if (CollectionUtils.isNotEmpty(oldSubscriberNetworks) &&
                !StringUtils.equalsIgnoreCase(oldSubscriberNetworks.get(0).getSubscriberId(), subscriberId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The network belongs to another subscriber.");
        }

        // step 3: check equipment is under this isp
        if (CollectionUtils.isNotEmpty(networkRequest.getEquipmentSerialList())) {
            List<NCSEquipment> equipmentList = ncsEquipmentService.getNCSEquipmentListBySerialList(networkRequest.getEquipmentSerialList());
            List<String> equipmentIdRequest = equipmentList.stream().map(NCSEquipment::getId).collect(Collectors.toList());

            if (!ncsEquipmentService.checkEquipmentListByIspId(ispId, equipmentIdRequest)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some equipment is not under ispId: " + ispId + ", please check equipment.");
            }

            // step 4: clear ncs_equipment network mapping, just clear networkId
            ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);

            // step 5: update ncs_equipment networkId and bind to subscriber
            // check all equipments are not bound by other network
            List<NCSEquipment> otherNetworkEquipmentList = equipmentList.stream()
                    .filter(equipment -> StringUtils.equals(equipment.getIspId(), ispId)
                            && StringUtils.isNotEmpty(equipment.getNetworkId())
                            && !StringUtils.equals(equipment.getNetworkId(), networkId))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otherNetworkEquipmentList)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some equipment is under other network, please check equipment");
            }
            ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);
            ncsEquipmentService.updateNetworkAndSubscriberByIdList(ispId, equipmentIdRequest, networkId, subscriberId);
        } else {
            // clear all equipment mapping
            ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);
        }

        // step 7: update network name
        networkRepo.updateNetworkName(networkId, networkRequest);
    }

    public void deleteNetwork(String networkId) throws Exception {
        NCSNetworkDetailDTO networkDetailDTO = null;
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            networkDetailDTO = networkRepo.getNetworkDetailDTOById(networkId);

            if (StringUtils.isEmpty(networkDetailDTO.getIspId()) || !StringUtils.equals(networkDetailDTO.getIspId(), userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
            }
        }

        // clear subscriber_network mapping
        List<SubscriberNetwork> subscriberNetworks = subscriberNetworkRepo.getSubscriberNetworkByNetworkId(networkId);
        String subscriberId = null;
        if (CollectionUtils.isNotEmpty(subscriberNetworks)) {
            subscriberId = subscriberNetworks.get(0).getSubscriberId();
            subscriberNetworkRepo.deleteByNetworkId(networkId);

            // reset subscriberId in ncs_equipment
            ncsEquipmentService.clearSubscriberMappingBySubscriberId(subscriberId);
        }

        // clear ncs_equipment mapping, just clear networkId
        ncsEquipmentService.clearNetworkMappingByNetworkId(networkId);

        // delete cluster_network mapping
        clusterNetworkRepo.deleteByNetworkId(networkId);

        networkRepo.deleteNetwork(networkId);
    }

    public void checkQueryParameter(NCSNetworkQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            ispService.checkIspIdPresent(queryDTO.getIspId());
        }

        checkQuerySubscriber(queryDTO);
    }

    private void checkQuerySubscriber(NCSNetworkQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getSubscriberId())) {
            List<SubscriberNetwork> subscriberNetworkList = subscriberNetworkRepo.getSubscriberNetworkBySubscriberId(queryDTO.getSubscriberId());
            if (CollectionUtils.isEmpty(subscriberNetworkList)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Query subscriberId does not have any networks associated with it.");
            }
        }
    }

    // network api
    public CpeApiResponse getCpeApiProxy(String networkId, CpeApiRequest proxyRequest) throws Exception {
        CpeApiResponse cpeApiResponse = new CpeApiResponse();
        NCSNetworkDetailDTO networkDetailDTO = getNetworkDetailDTO(networkId);

        if (CollectionUtils.isEmpty(networkDetailDTO.getEquipmentSerialList())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Current network cannot find equipment.");
        }

        String gatewaySerial;
        if (networkDetailDTO.getEquipmentSerialList().size() > 1) {
            gatewaySerial = manageCommonService.getGatewaySerialByUserId(networkId).orElse(STRING_EMPTY);
            if (CustomStringUtils.isEmpty(gatewaySerial)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Current network cannot find gateway serial, networkId: " + networkId);
            }
        } else {
            gatewaySerial = networkDetailDTO.getEquipmentSerialList().get(0);
        }

        String tid = CommonUtils.generateUUID();
        sendCpeApiRequestToRpc30(tid, networkId, gatewaySerial, proxyRequest);

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("_id", tid);
        DBObject proxyResult = mongoService.findOne(TOPOLOGY_PROXY_RESULT, queryParams);
        if (proxyResult == null) {
            logger.error("proxyResult not found, please check networkId: {}, gateway serial: {}", networkId, gatewaySerial);
        } else {
            cpeApiResponse = proxyCpeApiResponseMapper.parseProxyResult(String.valueOf(proxyResult.get("rawResponse")));
        }

        return cpeApiResponse;
    }

    // send request and create mongo record.
    private void sendCpeApiRequestToRpc30(String tid, String networkId, String serial, CpeApiRequest proxyRequest) throws Exception {
        String rpcUri = NCS_NETWORK_CPE_URI;
        HashMap<String, Object> payloadHashMap = new HashMap<>();
        String payloadJsonString;

        payloadHashMap.put("paths", proxyRequest.getEndpoints());
        payloadJsonString = objectMapper.writeValueAsString(payloadHashMap);
        logger.info("cpe api payload: {}", payloadJsonString);
        
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
        
        cpeRpcService.sendRpc(networkId, serial, rpcUri, CPE_API_METHOD, payloadJsonString, tid);

        Map<String, Object> rpcResponseResult = null;
        HashMap<String, Object> proxyResultMap = new HashMap<>();
        proxyResultMap.put("_id", tid);
        proxyResultMap.put("networkId", networkId);
        proxyResultMap.put("serial", serial);
        proxyResultMap.put("payload", payloadJsonString);
        proxyResultMap.put("rpcVer", "3.0");
        proxyResultMap.put("timestamp", Instant.now().toEpochMilli());
        proxyResultMap.put("dateCreated", new Date());
        proxyResultMap.put("rpcUri", rpcUri);
        proxyResultMap.put("rawResponse", STRING_EMPTY);

        for (int i = 0; i < maxTries; i++) {
            try {
                Thread.sleep(THREAD_TO_SLEEP);
            } catch (InterruptedException e) {
                logger.error("waitingRpcResult interrupted. tid:[ " + tid + "]");
                throw e;
            }

            rpcResponseResult = cpeRpcService.readRpcResult(tid);
            if (MapUtils.isNotEmpty(rpcResponseResult)) {
                proxyResultMap.put("rawResponse", rpcResponseResult);
                proxyResultMap.put("result", EXECUTE_OK);
                mongoService.create(TOPOLOGY_PROXY_RESULT, proxyResultMap);
                break;
            }
        }

        if (MapUtils.isEmpty(rpcResponseResult)) {
            logger.error("rpcRequest timeout, serial: {} tid: {}", serial, tid);
            proxyResultMap.put("result", TIME_OUT.toUpperCase());
            mongoService.create(TOPOLOGY_PROXY_RESULT, proxyResultMap);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
        }
    }

    public PaginationResponse<NetworkOperationsDTO> getNetworksByKeyword(NetworkOperationsQueryDTO queryDTO) throws Exception {
        if (CustomStringUtils.isEmpty(queryDTO.getKeyword())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "keyword cannot be empty or blank");
        }

        return networkRepo.getNetworksByKeyword(queryDTO);
    }

    public NCSNetwork getNetworkById(String networkId) throws Exception {
        return networkRepo.getNetworkById(networkId);
    }
}
