package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.model.NetworkDsl;
import com.actiontec.optim.platform.repository.NetworkDslRepo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NetworkDslService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private NetworkDslRepo networkDslRepo;

    public NetworkDsl findByEquipmentId(String equipmentIdOrSerialOrSTN) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(equipmentIdOrSerialOrSTN);

        return networkDslRepo.findDslBySerialNumber(userId);
    }
}
