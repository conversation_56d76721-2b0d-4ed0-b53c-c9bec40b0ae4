package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.FwVerDistributionDao;
import com.actiontec.optim.mongodb.dao.InternetServiceProviderDao;
import com.actiontec.optim.mongodb.dto.FwVerDistributionDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.model.FwImageResponse;
import com.actiontec.optim.platform.constant.ActiontecSQL;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.mapper.FwImageMapper;
import com.actiontec.optim.platform.mapper.FwVerDistributionMapper;
import com.actiontec.optim.platform.model.FwImage;
import com.actiontec.optim.platform.model.FwVerDistribution;
import com.actiontec.optim.platform.repository.EquipmentModelIspConfigRepo;
import com.actiontec.optim.platform.repository.EquipmentModelRepo;
import com.incs83.app.constants.queries.CompartmentSQL;
import com.incs83.app.constants.queries.FirmwareSQL;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.Firmware;
import com.incs83.app.entities.EquipmentModelFirmware;
import com.incs83.app.entities.OptimFile;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class FwImageService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    DataAccessService dataAccessService;

    @Autowired
    private FwImageMapper fwImageMapper;

    @Autowired
    private FwFileService fwFileService;

    @Autowired
    private AwsS3Service awsS3Service;

    @Autowired
    private FwVerDistributionDao fwVerDistributionDao;

    @Autowired
    private FwVerDistributionMapper fwVerDistributionMapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private InternetServiceProviderDao internetServiceProviderDao;

    @Autowired
    private EquipmentModelRepo equipmentModelRepo;

    @Autowired
    private EquipmentModelIspConfigRepo equipmentModelIspConfigRepo;

    private boolean checkGroupExist(String groupId) {
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, groupId);
            return Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return false;
    }

    private boolean validateEquipmentTypeIds(ArrayList<String> equipmentTypeIds) throws Exception {

        if(ObjectUtils.isEmpty(equipmentTypeIds)) {
            return false;
        }

        int equipmentModelsNumByIds = equipmentModelRepo.countEquipmentModelByIds(equipmentTypeIds);
        return equipmentModelsNumByIds == equipmentTypeIds.size();
    }

    public Boolean createFwImage(FwImage fwImage, ArrayList<String> equipmentTypeIds) throws Exception {

        if (CommonUtils.isSysAdmin()) {
            boolean isGroupExist = checkGroupExist(fwImage.getGroupId());
            if (!isGroupExist) { return false; }
        } else {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            if(!fwImage.getGroupId().equals(groupId)) {
                return false;
            }
        }

        boolean isEquipmentTypeIdsValid = validateEquipmentTypeIds(equipmentTypeIds);
        if (!isEquipmentTypeIdsValid) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "equipmentTypeIds must be provided and contain valid equipment type IDs");
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("groupId", fwImage.getGroupId());
        param.put("version", fwImage.getVersion());
        List<Firmware> firmwareList = (List<Firmware>) dataAccessService.read(Firmware.class, ActiontecSQL.GET_FIRMWARE_BY_GROUP_AND_VERSION, param);

        for (Firmware firmware : firmwareList) {
            for (String equipmentTypeId : equipmentTypeIds) {
                if (CollectionUtils.isNotEmpty(firmware.getEquipmentModelFirmwares()) &&
                    firmware.getEquipmentModelFirmwares().stream()
                        .anyMatch(fem -> fem.getEquipmentModelId().equals(equipmentTypeId))) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Firmware image already exists");
                }
            }
        }

        fwImage.setId(CommonUtils.generateUUID());
        Firmware firmware = fwImageMapper.toFirmware(fwImage);
        Set<EquipmentModelFirmware> equipmentModelFirmwares = fwImage.getEquipmentTypeIds().stream()
                .map(equipmentTypeId -> {
                    EquipmentModelFirmware fem = new EquipmentModelFirmware();
                    fem.setEquipmentModelId(equipmentTypeId);
                    fem.setFirmwareId(fwImage.getId());
                    return fem;
                })
                .collect(Collectors.toSet());
        firmware.setEquipmentModelFirmwares(equipmentModelFirmwares);


        OptimFile optimFile = new OptimFile();
        optimFile.setId(CommonUtils.generateUUID());
        optimFile.setType(ApplicationConstants.OptimFileType.firmware.name());

        if(fwImage.getLocationType().equals(ApplicationConstants.TYPE_URI)) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_SUCCESS);
        } else {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);
        }

        optimFile.setFileName(fwImage.getFileName());
        optimFile.setFileSize(fwImage.getFileSize());
        dataAccessService.create(OptimFile.class, optimFile);

        // Update production status for other firmware with same equipment models
        if( fwImage.getIsProduction() != null && fwImage.getIsProduction() == true) {
            param.clear();
            param.put("equipmentModelIds", fwImage.getEquipmentTypeIds());
            param.put("equipmentModelCount", fwImage.getEquipmentTypeIds().size());
            dataAccessService.deleteUpdateNative(ActiontecSQL.UPDATE_FIRMWARE_PRODUCTION, param);
        }

        firmware.setOptimFile(optimFile);
        dataAccessService.create(Firmware.class, firmware);

        fwImage.setFileEntry("/actiontec/api/v5/files/" + firmware.getOptimFile().getId());

        return true;
    }

    private FwImage getFwImage(Firmware fw) {
        FwImage fwImage = fwImageMapper.toFwImage(fw);
        fwImage.setFileEntry("/actiontec/api/v5/files/" + fw.getOptimFile().getId());
        fwImage.setFileName(fw.getOptimFile().getFileName());
        fwImage.setFileSize(fw.getOptimFile().getFileSize());
        fwImage.setFileType(fw.getLocationType());
        fwImage.setFileStatus(fw.getOptimFile().getFileStatus());

        // Set equipmentTypeIds using EquipmentModelFirmwares
        if (!ObjectUtils.isEmpty(fw.getEquipmentModelFirmwares())) {
            List<String> equipmentTypeIds = fw.getEquipmentModelFirmwares().stream()
                .map(EquipmentModelFirmware::getEquipmentModelId)
                .collect(Collectors.toList());
            fwImage.setEquipmentTypeIds(equipmentTypeIds);
        } else {
            fwImage.setEquipmentTypeIds(new ArrayList<>());
        }

        return fwImage;
    }

    private void addFwImage(Set<String> grpIds, Set<String> eqpIds, List<FwImage> fwImages, Firmware fw) {

        // Check group conditions (if provided)
        if (!ObjectUtils.isEmpty(grpIds)) {
            boolean groupMatches = grpIds.stream()
                    .anyMatch(grpId -> grpId.equals(fw.getGroupId()));
            if (!groupMatches) {
                return; // Group condition not met, return directly
            }
        }

        // Check equipment model conditions (if provided)
        if (!ObjectUtils.isEmpty(eqpIds)) {
            boolean equipmentMatches = eqpIds.stream()
                    .anyMatch(eqpId -> fw.getEquipmentModelFirmwares().stream()
                            .anyMatch(fem -> fem.getEquipmentModelId().equals(eqpId)));
            if (!equipmentMatches) {
                return; // Equipment model condition not met, return directly
            }
        }

        // All provided conditions are met, add to result
        fwImages.add(getFwImage(fw));
    }

    public List<FwImage> findFwImagesByIds(String equipmentTypeIds, String groupIds, String ispId) throws Exception {

        List<FwImage> fwImages = new ArrayList<>();
        Set<String> grpIds = new HashSet<>();
        Set<String> eqpIds = new HashSet<>();

        String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
        String allIspId = internetServiceProviderDao.getIspIdByName(ApplicationConstants.ALL_ISP);
        String allGroupId = at3Adapter.getGroupIdByIspId(allIspId);

        if(groupIds != null) {
            grpIds.addAll(Arrays.asList(groupIds.split(",")));
        }

        if(ispId != null) {
            List<String> groupIdsByIspIdList = getGroupIdsByIspIdList(Arrays.asList(allIspId, ispId));
            grpIds.addAll(groupIdsByIspIdList);
        }

        if(equipmentTypeIds != null) {
            eqpIds.addAll(Arrays.asList(equipmentTypeIds.split(",")));
        }


        List<Firmware> firmwares = (List<Firmware>) dataAccessService.read(Firmware.class, FirmwareSQL.GET_ALL_FIRMWARE_AND_FETCH_EQUIPMENT_MODEL_FIRMWARE, new HashMap<String, Object>());

        if(!firmwares.isEmpty()) {
            boolean isSysAdmin = CommonUtils.isSysAdmin();
            boolean hasNoFilters = (grpIds.isEmpty() && eqpIds.isEmpty());

            for(Firmware fw : firmwares) {
                boolean isInAllowedGroups = fw.getGroupId().equals(userGroupId) || fw.getGroupId().equals(allGroupId);
                if(!isSysAdmin && !isInAllowedGroups ) {
                    continue;
                }

                if(hasNoFilters) {
                    fwImages.add(getFwImage(fw));
                } else {
                    addFwImage(grpIds, eqpIds, fwImages, fw);
                }
            }
        }

        return fwImages;
    }

    private List<String> getGroupIdsByIspIdList(List<String> ispIdList) {
        Map<String, Object> params = new HashMap<>();
        params.put("ispIdList", ispIdList);

        return (List<String>) dataAccessService.read(Compartment.class,CompartmentSQL.GET_COMPARTMENT_ID_BY_ISPID_LIST,params);
    }

    public FwImage findFwImageById(String imageId) throws Exception {

        FwImage fwImage = null;

        Firmware firmware = getFirmwareByIdAndFetchEquipmentModelFirmware(imageId);

        if(firmware != null) {
            fwImage = fwImageMapper.toFwImage(firmware);
            fwImage.setFileEntry("/actiontec/api/v5/files/" + firmware.getOptimFile().getId());
            fwImage.setFileName(firmware.getOptimFile().getFileName());
            fwImage.setFileSize(firmware.getOptimFile().getFileSize());
            fwImage.setFileType(firmware.getLocationType());
            fwImage.setFileStatus(firmware.getOptimFile().getFileStatus());
            fwImage.setSecureUrl(firmware.getOptimFile().getSecureUrl());

            // Set equipmentTypeIds using equipmentModelFirmwares
            if (CollectionUtils.isNotEmpty(firmware.getEquipmentModelFirmwares())) {
                List<String> equipmentTypeIds = firmware.getEquipmentModelFirmwares().stream()
                    .map(EquipmentModelFirmware::getEquipmentModelId)
                    .collect(Collectors.toList());
                fwImage.setEquipmentTypeIds(equipmentTypeIds);
            } else {
                fwImage.setEquipmentTypeIds(new ArrayList<>());
            }
        }
        return fwImage;
    }

    private Firmware getFirmwareByIdAndFetchEquipmentModelFirmware(String id) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", id);
        List<Firmware> firmwares = (List<Firmware>) dataAccessService.read(Firmware.class, FirmwareSQL.GET_FIRMWARE_BY_ID_AND_FETCH_EQUIPMENT_MODEL_FIRMWARE, params);
        Firmware firmware = firmwares.isEmpty() ? null : firmwares.get(0);
        return firmware;
    }

    public Boolean updateFwImageById(String imageId, FwImage fwImage, ArrayList<String> equipmentTypeIds) throws Exception {

        if (CommonUtils.isSysAdmin()) {
            boolean isGroupExist = checkGroupExist(fwImage.getGroupId());
            if (!isGroupExist) { return false; }
        } else {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            if(!fwImage.getGroupId().equals(groupId)) {
                return false;
            }
        }

        boolean equipmentTypeExist = validateEquipmentTypeIds(equipmentTypeIds);
        if (!equipmentTypeExist) { return false; }

        Firmware firmware = getFirmwareByIdAndFetchEquipmentModelFirmware(imageId);
        if(firmware == null) {
            return false;
        }

        firmware.setName((fwImage.getName() != null) ? fwImage.getName() : firmware.getName());
        firmware.setDescription((fwImage.getDescription() != null) ? fwImage.getDescription() : firmware.getDescription());
        firmware.setVersion((fwImage.getVersion() != null) ? fwImage.getVersion() : firmware.getVersion());
        firmware.setLocationType((fwImage.getLocationType() != null) ? fwImage.getLocationType() : firmware.getLocationType());
        firmware.getOptimFile().setFileName((fwImage.getFileName() != null) ? fwImage.getFileName() : firmware.getOptimFile().getFileName());
        firmware.getOptimFile().setFileSize(fwImage.getFileSize());
        firmware.setLocation((fwImage.getFileLocation() != null) ? fwImage.getFileLocation() : firmware.getLocation());
        firmware.setUsername((fwImage.getUsername() != null) ? fwImage.getUsername() : firmware.getUsername());
        firmware.setPassword((fwImage.getPassword() != null) ? fwImage.getPassword() : firmware.getPassword());
        firmware.setGroupId((fwImage.getGroupId() != null) ? fwImage.getGroupId() : firmware.getGroupId());
        firmware.setSeqVersion((fwImage.getSequenceVersion() != null) ? fwImage.getSequenceVersion() : firmware.getSeqVersion());
        firmware.setProduction((fwImage.getIsProduction() != null) ? fwImage.getIsProduction() : firmware.getProduction());


        // update firmwareEqupmentModels
        Set<EquipmentModelFirmware> firmwareEqupmentModels = firmware.getEquipmentModelFirmwares();
        Set<String> currentEquipmentModelIds = firmwareEqupmentModels.stream()
                .map(EquipmentModelFirmware::getEquipmentModelId)
                .collect(Collectors.toSet());
        Set<String> newIds = new HashSet<>(equipmentTypeIds);
        firmwareEqupmentModels.removeIf(model -> !newIds.contains(model.getEquipmentModelId()));
        newIds.stream()
                .filter(id -> !currentEquipmentModelIds.contains(id))
                .forEach(equipmentTypeId -> {
                    EquipmentModelFirmware fem = new EquipmentModelFirmware();
                    fem.setEquipmentModelId(equipmentTypeId);
                    fem.setFirmwareId(imageId);
                    firmwareEqupmentModels.add(fem);
                });

        // Update production status for other firmware with same equipment models
        if(firmware.getProduction() != null && firmware.getProduction() == true) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("equipmentModelIds", fwImage.getEquipmentTypeIds());
            param.put("equipmentModelCount", fwImage.getEquipmentTypeIds().size());
            dataAccessService.deleteUpdateNative(ActiontecSQL.UPDATE_FIRMWARE_PRODUCTION, param);
        }

        dataAccessService.update(Firmware.class, firmware);
        return true;
    }

    public Boolean deleteFwImageById(String imageId) throws Exception {

        Firmware firmware = (Firmware) dataAccessService.read(Firmware.class, imageId);
        if(firmware == null) {
            return false;
        }

        // Check if the equipmentModel is referenced by any equipmentModelIspConfig
        if (equipmentModelIspConfigRepo.existsByFirmwareId(firmware.getId())) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Cannot delete firmware because it is referenced by one or more equipment model ISP config");
        }

        if (!CommonUtils.isSysAdmin()) {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            if(!firmware.getGroupId().equals(groupId)) {
                return false;
            }
        }

        awsS3Service.deleteFile(firmware.getOptimFile());
        dataAccessService.delete(Firmware.class, imageId);
        return true;
    }


    public List<FwVerDistribution> calculateFwVerDistribution() throws Exception {
        Optional<String> optionalIsp = Optional.empty();
        if (!CommonUtils.isSysAdmin()) {
            String ispId = at3Adapter.getIspId();
            String ispName = internetServiceProviderDao.getIspName(ispId);
            optionalIsp = Optional.of(ispName);
        }

        HashMap<String, String> props = commonService.read(com.incs83.constants.ApplicationCommonConstants.COMMON_CONFIG);
        String thresholdStr = props.get(com.incs83.constants.ApplicationCommonConstants.COMMON_DEVICE_STATUS_RETENTION_TIME);
        int threshold = Integer.parseInt(thresholdStr);
        List<FwVerDistributionDto> fwVerDistributionDtos = fwVerDistributionDao.calculateFwVerDistribution(optionalIsp, threshold);
        return fwVerDistributionMapper.toFwVerDistribution(fwVerDistributionDtos);
    }

    public void populateResponseIspId(List<FwImageResponse> fwImageResponseList) {
        if(fwImageResponseList.isEmpty()) {
            return;
        }
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class);

        Map<String, String> groupIdToIspMap = compartmentList.stream()
                .collect(Collectors.toMap(
                        Compartment::getId,
                        Compartment::getIspId
                ));

        fwImageResponseList.forEach(response -> {
            String ispId = groupIdToIspMap.get(response.getGroupId());
            response.setIspId(ispId);
        });
    }
}
