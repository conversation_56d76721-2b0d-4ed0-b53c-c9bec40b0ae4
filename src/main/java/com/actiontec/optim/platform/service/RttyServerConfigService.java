package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.repository.RttyServerConfigRepo;
import com.incs83.app.entities.RttyServerConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * RTTY服務器配置服務
 */
@Service
public class RttyServerConfigService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private RttyServerConfigRepo rttyServerConfigRepo;

    public RttyServerConfig getServerConfig() throws Exception {
        return rttyServerConfigRepo.getServerConfig();
    }
}
