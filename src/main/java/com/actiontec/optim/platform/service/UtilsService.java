package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.exception.PermissionNotSupportException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Map;

@Service
public class UtilsService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private SimpleAesEcbCryptoService cryptoService;

    public Map<String, Object> getByVirtualKey(String virtualKey) throws IOException {
        if (CommonUtils.isSysAdmin() || CommonUtils.isGroupAdmin()) {
            if (StringUtils.isBlank(virtualKey))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid id to decrypt.");

            String jsonStr = cryptoService.decrypt(virtualKey, ApplicationConstants.ENCRYPT_KEY);
            return objectMapper.readValue(jsonStr, Map.class);
        } else {
            throw new PermissionNotSupportException();
        }
    }
}
