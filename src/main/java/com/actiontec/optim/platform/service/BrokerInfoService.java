package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.BrokerInfoDTO;
import com.actiontec.optim.platform.api.v6.dto.BrokerInfoQueryDTO;
import com.actiontec.optim.platform.api.v6.dto.BrokerInfoRequest;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.repository.BrokerInfoRepo;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.entities.BrokerInfo;
import com.incs83.exceptions.handler.ValidationException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import org.springframework.util.ObjectUtils;

@Service
public class BrokerInfoService {

    @Autowired
    private BrokerInfoRepo brokerInfoRepo;

    @Autowired
    private EquipmentRedirectService equipmentRedirectService;

    public PaginationResponse<BrokerInfoDTO> getAllBrokerInfos(BrokerInfoQueryDTO queryDTO) throws Exception {
        return brokerInfoRepo.getAllBrokerInfos(queryDTO);
    }

    @Transactional
    public Map<String, String> createBrokerInfo(BrokerInfoRequest request) throws Exception {
        validateBrokerInfoRequest(request);

        if (brokerInfoRepo.existsByEnvName(request.getEnvName())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Environment name already exists: " + request.getEnvName());
        }

        // only create check password
        if (StringUtils.isBlank(request.getPassword())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Password cannot be empty");
        }

        // If set as default, clear default status of other brokers
        // Although the frontend currently hides is_default, we handle it here just in case
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            brokerInfoRepo.clearDefaultStatus();
        }

        String id = brokerInfoRepo.createBrokerInfo(request);
        Map<String, String> result = new HashMap<>();
        result.put("id", id);
        return result;
    }

    public BrokerInfoDTO getBrokerInfo(String brokerId) throws Exception {
        return brokerInfoRepo.getBrokerInfoById(brokerId);
    }

    @Transactional
    public void updateBrokerInfo(String brokerId, BrokerInfoRequest request) throws Exception {
        BrokerInfoDTO oldBrokerInfo = getBrokerInfo(brokerId);
        if (ObjectUtils.isEmpty(oldBrokerInfo)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Broker info not found with id: " + brokerId);
        }
        validateBrokerInfoRequest(request);
        
        // Only check for env_name conflicts if the name has been changed
        if (!CustomStringUtils.equals(request.getEnvName(), oldBrokerInfo.getEnvName()) && brokerInfoRepo.existsByEnvName(request.getEnvName())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The modified environment name already exists: " + request.getEnvName());
        }

        if (Boolean.TRUE.equals(request.getIsDefault())) {
            brokerInfoRepo.clearDefaultStatus();
        }

        brokerInfoRepo.updateBrokerInfo(brokerId, request);
    }

    @Transactional
    public void deleteBrokerInfo(String brokerId) throws Exception {
        BrokerInfoDTO brokerInfo = getBrokerInfo(brokerId);
        if (ObjectUtils.isEmpty(brokerInfo)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Broker info not found with id: " + brokerId);
        }
        
        // Check if there are related equipment redirects
        if (equipmentRedirectService.hasRedirectSetting(brokerId)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot delete broker info with related equipment redirects");
        }

        brokerInfoRepo.delete(brokerId);
    }

    private void validateBrokerInfoRequest(BrokerInfoRequest request) throws Exception {
        if (StringUtils.isBlank(request.getEnvName())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Environment name cannot be empty");
        }

        if (StringUtils.isBlank(request.getBrokerUrl())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Broker URL cannot be empty");
        }

        if (Objects.isNull(request.getPort())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Broker port cannot be empty");
        }

        if (request.getPort() <= 0 || request.getPort() > 65535) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Broker port must be between 1 and 65535");
        }

        if (StringUtils.isBlank(request.getUserName())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Username cannot be empty");
        }
    }

    public String getDefaultBrokerId() throws Exception {
        return brokerInfoRepo.getDefaultBrokerId();
    }
}
