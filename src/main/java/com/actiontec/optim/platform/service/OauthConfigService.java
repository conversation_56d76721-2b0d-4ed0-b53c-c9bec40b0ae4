package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.OauthConfigurationDao;
import com.actiontec.optim.mongodb.dto.OauthConfigDto;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.mapper.OauthConfigMapper;
import com.actiontec.optim.platform.model.OauthConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OauthConfigService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private OauthConfigurationDao oauthConfigurationDao;

    @Autowired
    private OauthConfigMapper oauthConfigMapper;

    public Map<String, String> addOauthConfig(OauthConfig oauthConfig) throws Exception {
        Map<String, String> result = new HashMap<>();

        result.put("id", CommonUtils.generateUUID());
        HashMap<String, Object> oauthConfigMap = objectMapper.convertValue(oauthConfig, HashMap.class);
        oauthConfigMap.remove("id");
        oauthConfigMap.put("_id", result.get("id"));

        mongoService.create(OauthConfigDto.COLLECTION, oauthConfigMap);
        return result;
    }

    public void updateOauthConfig(String configId, OauthConfig oauthConfig) throws Exception {
        OauthConfigDto oauthConfigDto = oauthConfigurationDao.findById(configId);
        if(oauthConfigDto == null) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "OAuth configuration not found [" + configId + "]");
        }

        BasicDBObject query = new BasicDBObject();
        query.put("_id", configId);

        Map<String, Object> oauthConfigMap = objectMapper.convertValue(oauthConfig, Map.class);
        BasicDBObject update = new BasicDBObject();
        update.putAll(oauthConfigMap);

        mongoService.update(query, update, false, false, OauthConfigDto.COLLECTION);
    }

    public void deleteOauthConfig(String configId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("_id", configId);
        mongoService.deleteOne(params, OauthConfigDto.COLLECTION);
    }

    public List<OauthConfig> getOauthConfigs() {

        List<OauthConfigDto> oauthConfigDtoList = oauthConfigurationDao.findAll();
        List<OauthConfig> oauthConfigList =oauthConfigMapper.toOauthConfigs(oauthConfigDtoList);
        return oauthConfigList;
    }

    public OauthConfig getOauthConfigById(String configId) {
        OauthConfigDto oauthConfigDto = oauthConfigurationDao.findById(configId);
        if(oauthConfigDto == null) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "OAuth configuration not found [" + configId + "]");
        }

        OauthConfig oauthConfig = oauthConfigMapper.toOauthConfig(oauthConfigDto);
        return oauthConfig;
    }
}
