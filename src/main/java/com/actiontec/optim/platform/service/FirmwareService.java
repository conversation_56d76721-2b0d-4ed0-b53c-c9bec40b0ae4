package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.actiontec.optim.platform.api.v5.model.FirmwareActionRequest;
import com.actiontec.optim.platform.constant.ActiontecSQL;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.mapper.FwMapper;
import com.actiontec.optim.platform.model.firmware.FwInfo;
import com.actiontec.optim.platform.model.firmware.FwLog;
import com.actiontec.optim.platform.model.firmware.RequestPayload;
import com.actiontec.optim.platform.model.firmware.ResponsePayload;
import com.actiontec.optim.platform.model.FwImage;
import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class FirmwareService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private FwMapper fwMapper;
    @Autowired
    private FwImageService fwImageService;
    @Autowired
    private CpeRpcService cpeRpcService;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ObjectMapper objectMapper;

    private static final String FW_URL = "/cpe-acs/firmwares";
    private static final String Fw_ACTIONS_URL = "/cpe-acs/firmwares/actions";

    @PermissionHandle
    public List<FwInfo> getFirmwares(String stn, String equipmentId) throws InterruptedException {
        List<FwInfo> fwInfoList = new LinkedList<>();

        String userId = at3Adapter.getRgwSerialByStn(stn);
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, FW_URL, "GET", "{}",  ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

        if (rpcResult.get("payload") != null) {
            List<HashMap<String, String>> payloadList = objectMapper.convertValue(rpcResult.get("payload"), new TypeReference<List<HashMap<String, String>>>(){});
             String activeVersion = String.valueOf(payloadList.get(0).get("activeVersion"));
             String backupVersion = String.valueOf(payloadList.get(0).get("backupVersion"));

             if (StringUtils.isNotEmpty(activeVersion))
                 fwInfoList.add(new FwInfo(activeVersion, true));
             if (StringUtils.isNotEmpty(backupVersion))
                fwInfoList.add(new FwInfo(backupVersion, false));
        }

        return fwInfoList;
    }

    @PermissionHandle
    public List<FwLog> getFirmwareLogs(String stn, String equipmentId, String actionId) throws InterruptedException {
        List<FwLog> fwLogList = new LinkedList<>();
        String url = Fw_ACTIONS_URL + (Objects.nonNull(actionId)? "/"+actionId: "");
        String userId = at3Adapter.getRgwSerialByStn(stn);
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, url, "GET", "{}",  ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

        if (rpcResult.get("payload") != null) {
            List<ResponsePayload> responsePayloadList = objectMapper.convertValue(rpcResult.get("payload"), at3Adapter.getCollectionType(List.class, ResponsePayload.class));
            responsePayloadList.forEach(p->fwLogList.add(fwMapper.toFwlog(p)));
            fwLogList.forEach(p-> {
                String ver = "";
                if (StringUtils.isNotEmpty(p.getFileEntry())) {
                    HashMap<String, Object> param = new HashMap<>();
                    param.put("secureUrl", p.getFileEntry());
                    try {
                        ver = dataAccessService.readNative(ActiontecSQL.GET_VERSION_FROM_FILE, param).iterator().next().toString();
                    } catch (Exception e) {
                        logger.error("get version error from [{}]", p.getFileEntry(), e);
                    }
                }
                p.setVersion(ver);
            });
        }

        return fwLogList;
    }

    @PermissionHandle
    public Map<String, String> postAction(String stn, String equipmentId, FirmwareActionRequest actionRequest) throws Exception {
        Map<String, String> retVal = new HashMap<>();
        String userId = at3Adapter.getRgwSerialByStn(stn);
        FwImage fwImage = fwImageService.findFwImageById(actionRequest.getImageId());
        if (Objects.isNull(fwImage))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No such file for imageId:" + actionRequest.getImageId());

        RequestPayload payload = new RequestPayload();
        payload.setAction(actionRequest.getAction().toLowerCase());
        payload.setActionId(CommonUtils.getCurrentTimeInMillis()/1000 + "-" + CommonUtils.generateUUID());
        if (StringUtils.isNotEmpty(fwImage.getPassword()))
            payload.getData().setPassword(fwImage.getPassword());
        if (StringUtils.isNotEmpty(fwImage.getUsername()))
            payload.getData().setUsername(fwImage.getUsername());
        payload.getData().setUrl(fwImage.getSecureUrl());

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, Fw_ACTIONS_URL, "POST", objectMapper.writeValueAsString(payload),  ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

        if (rpcResult != null) {
            BasicDBObject query = new BasicDBObject();
            query.put("userId", userId);
            query.put("serialNumber", equipmentId);
            BasicDBObject updateFields = new BasicDBObject();
            updateFields.put("connectionStatus", "offline");
            BasicDBObject updatedDBObject = new BasicDBObject();
            updatedDBObject.put("$set", updateFields);
            at3Adapter.findAndModifyCollection(query, ActiontecConstants.AP_DETAIL, null, updatedDBObject, false);

            retVal.put("id", payload.getActionId());
        }

        return retVal;
    }
}
