package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.CpeTopologyDao;
import com.actiontec.optim.platform.api.v6.dto.SnapshotDataDTO;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TopologyService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    CpeTopologyDao cpeTopologyDao;

    public List<SnapshotDataDTO> getHistory(String networkId, long startTime, long endTime, String deviceId) {
        return cpeTopologyDao.getTopologySnapshotData(networkId, startTime, endTime, deviceId);
    }
}
