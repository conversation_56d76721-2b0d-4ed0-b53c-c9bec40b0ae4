package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.CpeSmmAppStatsDao;
import com.actiontec.optim.mongodb.dto.CpeSmmAppDto;
import com.actiontec.optim.mongodb.dto.CpeSmmAppStatsDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.ActionRequest;
import com.actiontec.optim.platform.api.v5.model.SmmJobRequest;
import com.actiontec.optim.platform.constant.SMMSQL;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.mapper.SmmServiceMapper;
import com.actiontec.optim.platform.model.SmmApplication;
import com.actiontec.optim.platform.model.SmmJobStatus;
import com.actiontec.optim.platform.model.Source;
import com.incs83.app.entities.SmmJob;
import com.incs83.app.entities.SmmService;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.text.SimpleDateFormat;
import java.util.stream.Collectors;


@Service
public class SmmJobService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    CpeSmmAppStatsDao cpeSmmAppStatsDao;
    @Autowired
    private SmmServicesService smmServicesService;
    @Autowired
    private SmmAppService smmAppService;
    @Autowired
    private SmmServiceMapper smmServiceMapper;

    private  SmmJobStatus toSmmJobStatus(SmmJob smmJob) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SmmJobStatus smmJobStatus = new SmmJobStatus();
        Source source = new Source();
        if (Objects.nonNull(smmJob.getSourceVersion()))
            source.setVersion(smmJob.getSourceVersion());
        if (Objects.nonNull(smmJob.getIspId()))
            source.setIspId(smmJob.getIspId());
        smmJobStatus.setSource(source);
        if (Objects.nonNull(smmJob.getTargetVersion()))
            smmJobStatus.setTargetVersion(smmJob.getTargetVersion());
        smmJobStatus.setAction(smmJob.getAction());
        smmJobStatus.setResourceCheck(smmJob.getResourceCheck());
        smmJobStatus.setCount(smmJob.getCount());
        smmJobStatus.setRate(smmJob.getRate());
        smmJobStatus.setServiceId(smmJob.getSmmService().getId());
        smmJobStatus.setId(smmJob.getId());
        smmJobStatus.setGroup(at3Adapter.getIspNameByGroupId(smmJob.getGroupId()));
        smmJobStatus.setState(smmJob.getState());
        smmJobStatus.setRequestTime(smmJob.getCreatedAt().getTime());
        smmJobStatus.setUnitStats(new SmmJobStatus.UnitStats());

        return smmJobStatus;
    }
    private Map<String, SmmJobStatus> getSmmJobStatusMap(String jobId) {
       Map<String, SmmJobStatus> smmJobStatusMap = new HashMap<>();
       try {
           if (Objects.nonNull(jobId)) {
               SmmJob smmJob = (SmmJob) dataAccessService.read(SmmJob.class, jobId);
               smmJobStatusMap.put(smmJob.getId(), toSmmJobStatus(smmJob));
           } else  {
               List<SmmJob> smmJobList = (List<SmmJob>) dataAccessService.read(SmmJob.class);
               for (SmmJob item: smmJobList) {
                   smmJobStatusMap.put(item.getId(), toSmmJobStatus(item));
               }
           }

       } catch (Exception e) {
           logger.error("get SmmJob error.", e);
       }

       return  smmJobStatusMap;
    }

    public List<SmmJobStatus> findSmmJobFromJobs(String jobId) throws Exception {
        Map<String, SmmJobStatus> smmJobStatusMap = getSmmJobStatusMap(jobId);
        if (smmJobStatusMap.isEmpty()) {
            if (jobId == null)
                return new ArrayList<>();
            else
                throw new NoSuchEntityException();
        }

        String userIsp = at3Adapter.getIspNameByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
        List<DBObject> aggregatePipeline = new ArrayList<>();
        if (CommonUtils.isSysAdmin() == false) {
            String pipelineQueryIsp = " {$match:{isp: '%s'}}";
            if (Objects.isNull(userIsp))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The user don't match any Group.");

            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQueryIsp, userIsp)));
        }
        Long timestamp = cpeSmmAppStatsDao.getLastTimestampForJob(jobId);

        if (timestamp.equals(0L) == false) {
            aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject(CpeSmmAppStatsDto.CpeSmmAppFields.timestamp.name(), timestamp)));
            if (Objects.isNull(jobId)) {
                String pipelineQuery0 = "    { " +
                        "        $match: {" +
                        "            'jobId': { " +
                        "                '$exists': true, " +
                        "                '$ne': null " +
                        "            }" +
                        "        }    " +
                        "    }";
                aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery0)));
            } else {
                String pipelineQuery0 = " {$match:{jobId: '%s'}}";
                aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery0, jobId)));
            }

            String pipelineQuery1 = "{$project: " +
                    "   {        jobId:1," +
                    "            processing: {$cond: [{$eq: ['$actionState', 'Init']}, '$count', 0]}," +
                    "            complete: {$cond: [{$eq: ['$actionState', 'Done']}, '$count', 0]}," +
                    "            failed: {$cond: [{$eq: ['$actionState', 'Failed']}, '$count', 0]}" +
                    "    }" +
                    "  }";
            String pipelineQuery2 = " { $group: " +
                    "   {" +
                    " _id: {jobId:'$jobId'}," +
                    "        totalProcessing: {$sum: '$processing'}," +
                    "        totalComplete: {$sum: '$complete'}," +
                    "        totalFailed: {$sum: '$failed'}" +
                    "   }" +
                    "  }";
            String pipelineQuery3 = "{$project: " +
                    " {_id:0, 'jobId':'$_id.jobId',totalProcessing:1, totalComplete:1, totalFailed:1}" +
                    "  }";

            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery1)));
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery2)));
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery3)));

            DBCollection dbCollection = at3Adapter.getMongoDbCollection(CpeSmmAppStatsDto.COLLECTION_cpeSmmAppStats);
            Iterator iterator = dbCollection.aggregate(aggregatePipeline).results().iterator();
            while (iterator.hasNext()) {
                BasicDBObject resultObject = (BasicDBObject) iterator.next();
                SmmJobStatus smmJobStatus = smmJobStatusMap.get(resultObject.getString("jobId"));
                if (Objects.nonNull(smmJobStatus)) {
                    long inprocessing = resultObject.getLong("totalProcessing", 0);
                    long completed = resultObject.getLong("totalComplete", 0);
                    long failed = resultObject.getLong("totalFailed", 0);
                    smmJobStatus.getUnitStats().setProcessing(inprocessing);
                    smmJobStatus.getUnitStats().setComplete(completed);
                    smmJobStatus.getUnitStats().setFailed(failed);
                    if (smmJobStatus.getCount() != 0 && smmJobStatus.getCount() <= (inprocessing+completed+failed))
                        smmJobStatus.setState("Completed");
                    smmJobStatusMap.put(smmJobStatus.getId(), smmJobStatus);
                }
            }
        }

        if (CommonUtils.isSysAdmin() == false) {
            return smmJobStatusMap.values().stream().filter(status-> StringUtils.equals(status.getGroup(), userIsp)).sorted().collect(Collectors.toList());
        }

        return smmJobStatusMap.values().stream().sorted(Comparator.comparing(SmmJobStatus::getGroup).thenComparing(SmmJobStatus::getRequestTime, Comparator.reverseOrder())).collect(Collectors.toList());
    }

    private String[] getApplicationIds(String serviceId, String version) throws Exception {
        Set<String> appIds = new HashSet<>();
        List<SmmApplication> smmApplicationList = smmAppService.findAppByVersion(serviceId, version);
        for (SmmApplication p: smmApplicationList) {
            appIds.add(p.getId());
        }

        return appIds.toArray(new String[0]);
    }

    public String postJob(SmmJobRequest smmJobRequest) throws Exception{
        long t1 = Calendar.getInstance().getTimeInMillis();
        String sourceVersion = Optional.ofNullable(smmJobRequest.getSource()).map(source -> source.getVersion()).orElse(null);
        String ispId = Optional.ofNullable(smmJobRequest.getSource()).map(source -> source.getIspId()).orElse(null);
        String targetVersion = StringUtils.isNotEmpty(smmJobRequest.getTargetVersion()) ? smmJobRequest.getTargetVersion():
                    ((SmmService) dataAccessService.read(SmmService.class, smmJobRequest.getServiceId())).getProductionVersion();

        if (StringUtils.isBlank(smmJobRequest.getServiceId()) || StringUtils.isBlank(targetVersion)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " Invalid input parameters for Smm Jobs!");
        }

        if ((StringUtils.isNotBlank(sourceVersion) &&  ((List<SmmApplication>) smmAppService.findAppByVersion(smmJobRequest.getServiceId(), sourceVersion)).size() < 1) ||
                ((List<SmmApplication>) smmAppService.findAppByVersion(smmJobRequest.getServiceId(), targetVersion)).size() < 1)
        {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " Invalid input parameters for Smm Jobs!!");
        }

        SmmService smmService = (SmmService) dataAccessService.read(SmmService.class, smmJobRequest.getServiceId());
        if(smmService == null) {
            throw new NoSuchEntityException();
        }
        SmmJob smmJob = smmServiceMapper.toSmmJob(CommonUtils.generateUUID(), CommonUtils.getGroupIdOfLoggedInUser(), sourceVersion, ispId, new Date(), "Stopped", smmJobRequest);
        smmJob.setSmmService(smmService);
        dataAccessService.create(SmmJob.class, smmJob);

        long t2 = Calendar.getInstance().getTimeInMillis();
        logger.debug("time spent: [{}].", t2 - t1);
        return smmJob.getId();
    }

    public String postActions(String jobId, ActionRequest request) throws Exception {
        Map<String, SmmJobStatus> smmJobStatusMap = getSmmJobStatusMap(jobId);
        if (smmJobStatusMap.isEmpty()) {
            throw new NoSuchEntityException();
        }
        SmmJobStatus job = smmJobStatusMap.get(jobId);
        String state = job.getState();
        if (state.equals("Complete") || state.equals("Canceled")) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid action to handle the Complete or Canceled Job.");
        }

        String action = request.getAction();
        String desiredState = null;
        if (StringUtils.equals(state, "Active")) {
            if (StringUtils.equals(action, "Stop"))
                desiredState = "Stopped";
            else if (StringUtils.equals(action, "Cancel"))
                desiredState = "Canceled";
        }
        else if (StringUtils.equals(state, "Stopped")) {
            if (StringUtils.equals(action, "Cancel"))
                desiredState = "Canceled";
            else if (StringUtils.equals(action, "Start"))
                desiredState = "Active";
        }

        if (StringUtils.isBlank(desiredState))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid action to handle SMM job.");
        else if (!desiredState.equals("Active")) {
            BasicDBObject query = new BasicDBObject();
            query.put("jobId", jobId);
            query.put("actionState", "Init");

            BasicDBObject insertFields = new BasicDBObject();
            insertFields.put("updatedAt", CommonUtils.getCurrentTimeInMillis());
            insertFields.put("actionState", "Done");
            BasicDBObject updatedDBObject = new BasicDBObject();
            updatedDBObject.put("$set", insertFields);

            at3Adapter.getMongoDbCollection(CpeSmmAppDto.COLLECTION_cpeSmmApp).updateMulti(query, updatedDBObject);
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("id", jobId);
        param.put("state", desiredState);
        param.put("updatedAt", new Date());
        dataAccessService.update(SmmJob.class, SMMSQL.UPDATE_JOB_STATE, param);

        return jobId;
    }
}
