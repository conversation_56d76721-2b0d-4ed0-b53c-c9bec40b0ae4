package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.business.v2.SimpleRpcService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.utils.EquipmentUtils;
import com.incs83.service.CommonService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.server.ResponseStatusException;

import java.time.ZonedDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_DIAGNOSTIC_CONFIG;
import static com.incs83.app.constants.misc.ActiontecConstants.RPC_POLL_COUNT;
import static com.incs83.app.constants.misc.ApplicationConstants.THREAD_TO_SLEEP;

@Service
public class DmAccessService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SimpleRpcService cpeRpcService;

    @Autowired
    private EquipmentUtils equipmentUtils;

    @Autowired
    private At3Adapter at3Adapter;

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.DATA_MODEL_ACCESS)
    public Map<String, Object> doDmAccess(String equipmentId, Map<String, Object> payloadMap) throws Exception {
        String userId = equipmentUtils.getEquipmentUserId(equipmentId);
        String rpcUri = RpcConstants.DM_ACCESS_URI;
        String payload = mapper.writeValueAsString(payloadMap);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userId, equipmentId, rpcUri, "POST", payload, max_Tries, THREAD_TO_SLEEP);
        if (!String.valueOf(rpcResult.get("code")).equals("200")) {
            logger.error("serial:[{}] response:[{}]", equipmentId, rpcResult.toString());
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, rpcUri + " RPC Failed");
        }

        List<Map<String, Object>> payloadList = (List<Map<String, Object>>) rpcResult.get("payload");

        Map<String, Object> result = payloadList.get(0);
        result.put("lastUpdateTime", ZonedDateTime.now().toInstant().toEpochMilli());
        return result;
    }
}
