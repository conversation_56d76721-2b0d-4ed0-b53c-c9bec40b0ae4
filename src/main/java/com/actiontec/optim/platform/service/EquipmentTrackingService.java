package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.TransactionRequest;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.model.*;
import com.incs83.app.entities.*;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.business.ESService;
import com.incs83.config.ESClientConfig;
import com.incs83.dao.Page;
import com.incs83.dto.ESRequest;
import com.incs83.dto.ElasticSearchDTO;
import com.incs83.enums.sql.Order;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.request.PaginatedRequest;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.Scroll;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

import static com.actiontec.optim.platform.constant.ActiontecSQL.GET_TRACKING_EQUIPMENTS;
import static com.actiontec.optim.platform.constant.ActiontecSQL.GET_TRACKING_EQUIPMENT_COUNT;
import static com.actiontec.optim.platform.constant.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.COMMON_CONFIG;
import static com.incs83.constants.ApplicationCommonConstants.COMMON_DEVICE_STATUS_RETENTION_TIME;

@Service
public class EquipmentTrackingService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private ApDetailDao apDetailDao;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private FwFileService fwFileService;
    @Autowired
    private CSVService csvService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ESService esService;
    @Autowired
    private ESClientConfig esClientConfig;


    @Value("${elastic-search.enable}")
    private Boolean elasticSearchEnable;

    public enum ActonType {Provision, Delete}
    public enum ContentType {csv, array, error}

    public Map<String, String> postTransaction(TransactionRequest request) throws Exception {
        Map<String, String> response = new HashMap<>(2);

        TrackingTransaction transaction = new TrackingTransaction();
        transaction.setId(CommonUtils.generateUUID());
        transaction.setStatus(ApplicationConstants.TRANSACTION_STATUS_INITIAL);
        if (request.getAction().equals(ActonType.Delete.name()))
            transaction.setAction(ActonType.Delete.name());
        else if (request.getAction().equals(ActonType.Provision.name()))
            transaction.setAction(ActonType.Provision.name());
        else
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid action");

        if (!Optional.ofNullable(at3Adapter.getIspNameById(request.getPayload().getIspId())).isPresent() || (CommonUtils.isGroupAdmin() && !CommonUtils.isSameGroup(at3Adapter.getGroupIdByIspId(request.getPayload().getIspId())))) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid ispId");
        } else {
            transaction.setIspId(request.getPayload().getIspId());
        }

        Optional.ofNullable(request.getPayload().getDescription()).ifPresent(transaction::setDescription);
        transaction.setCreatedBy(CommonUtils.getUserIdOfLoggedInUser());
        transaction.setCreatedAt(new Date());

        OptimFile optimFile = new OptimFile();
        optimFile.setId(CommonUtils.generateUUID());
        optimFile.setType(ApplicationConstants.OptimFileType.Tracking.name());
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);

        if (!StringUtils.isBlank(request.getPayload().getFileName()) && request.getPayload().getFileSize() > 0) {
            transaction.setType(ContentType.csv.name());

            optimFile.setFileName(request.getPayload().getFileName());
            optimFile.setFileSize(request.getPayload().getFileSize());
        } else if (StringUtils.isBlank(request.getPayload().getFileName()) && Objects.nonNull(request.getPayload().getSerialNumbers()) && request.getPayload().getSerialNumbers().size()>0){
            transaction.setType(ContentType.array.name());
            String fileName = "/tmp/" + "tr_" + CommonUtils.generateUUID();
            optimFile.setFileName(fileName);
            optimFile.setFileSize((int) csvService.converArray2CSV(request.getPayload().getSerialNumbers(), fileName));
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid payload");
        }
        transaction.setOptimFile(optimFile);

        dataAccessService.create(TrackingTransaction.class, transaction);

        if (transaction.getType().equals(ContentType.array.name())) {
           fwFileService.updateFile(transaction.getOptimFile().getId(), transaction.getOptimFile().getFileName());
            try {
                Files.delete(Paths.get(transaction.getOptimFile().getFileName()));
            } catch (IOException e) {
                logger.error("get error on delete file.", e);
            }
        }
        response.put("id", transaction.getId());
        response.put("fileEntry", PUBLIC_FILE_PATH + transaction.getOptimFile().getId());

        return response;
    }

    private Long getOnLineTime() {
        HashMap<String, String> comConfig = commonService.read(COMMON_CONFIG);
        int equipmentOfflineTime = 7;
        if (Objects.nonNull(comConfig) && Objects.nonNull(comConfig.get(COMMON_DEVICE_STATUS_RETENTION_TIME))) {
            try {
                equipmentOfflineTime = Integer.valueOf(comConfig.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
            } catch (Exception e) {
            }
        }
        long timeDiff = CommonUtils.getCurrentTimeInMillis() - equipmentOfflineTime * 60 * 1000;
        return timeDiff;
    }

    private List<TrackingEquipment> getRecordsFromES(Boolean pagination, Integer offset, Integer limit, String serialNumber, String ispId) {
        List<TrackingEquipment> records = new ArrayList<>();
        ElasticSearchDTO esResponse = null;
        String sortBy = "id";
        boolean exact = false;
        long t1 = CommonUtils.getCurrentTimeInMillis();
        long t2 = 0;

        String isp = null;
        if (Objects.nonNull(ispId))
            isp = at3Adapter.getIspNameById(ispId);
        if (Objects.nonNull(ispId) && Objects.isNull(isp))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid ispId");
        if (Objects.nonNull(serialNumber) && !ValidationUtil.isValidSnString(serialNumber))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "SerialNumber can't contain invalid Characters");

        if (pagination) {
            if (limit  > 500)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max cannot be greater than 500");
            if (offset < 0)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");

            HashMap<String, Object> esQueryParams = new HashMap<>();
            esQueryParams.put("isp", isp);
            esQueryParams.put("serial", serialNumber);

            ESRequest elasticSearchRequest = new ESRequest();
            elasticSearchRequest.setMax(limit).setOffset(offset).setSortBy(sortBy).setIndex(TRACKING_EQUIPMENT_INDEX);
            elasticSearchRequest.setQueryParams(esQueryParams);
            elasticSearchRequest.setShouldParams(null);
            elasticSearchRequest.setMustNotExistValue(null);
            esResponse = esService.searchWithFilter(elasticSearchRequest, null, null, exact);
            if (Objects.nonNull(esResponse) && esResponse.getObjects().size() > 0) {
                esResponse.getObjects().forEach(item-> {
                    TrackingEquipment equipment =  new TrackingEquipment();
                    equipment.setId(String.valueOf(item.get("id")));
                    equipment.setCreatedAt(new Date(Long.parseLong(String.valueOf(item.get("createdAt")))));
                    //equipment.setCreatedBy(String.valueOf(item.get("createdBy")));
                    Optional.ofNullable(item.get("isp")).ifPresent(p->equipment.setIspId(String.valueOf(p).split("\\|")[1]));
                    equipment.setSerial(String.valueOf(item.get("serial")));
                    Optional.ofNullable(item.get("uId")).ifPresent(p->equipment.setuId(String.valueOf(p)));
                    //Optional.ofNullable(item.get("onboardBy")).ifPresent(p->equipment.setOnboardBy(String.valueOf(p)));
                    Optional.ofNullable(item.get("onboardAt")).ifPresent(p->equipment.setOnboardAt(new Date(Long.parseLong(String.valueOf(p)))));
                    records.add(equipment);
                });
            }
            t2 = CommonUtils.getCurrentTimeInMillis();
        } else {
            RestHighLevelClient client = this.esClientConfig.getRestClient();
            SearchRequest searchRequest = new SearchRequest(TRACKING_EQUIPMENT_INDEX);
            Scroll scroll = new Scroll(TimeValue.timeValueMinutes(5L));
            searchRequest.scroll(scroll);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
            if (!StringUtils.isBlank(isp))
                boolQueryBuilder.must(QueryBuilders.wildcardQuery("isp", "*" + isp.toLowerCase() + "*"));
            if (Objects.nonNull(serialNumber))
                boolQueryBuilder.must(QueryBuilders.wildcardQuery("serial", "*" + serialNumber.toLowerCase() + "*"));
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.size(500);

            searchRequest.source(searchSourceBuilder);
            SearchResponse response = null;
            try {
                response = client.search(searchRequest, RequestOptions.DEFAULT);
                String scrollId = response.getScrollId();
                SearchHit[] searchHits = response.getHits().getHits();

                while (ArrayUtils.isNotEmpty(searchHits)) {
                    for(SearchHit hit: searchHits) {
                        Map item = hit.getSourceAsMap();
                        if (Objects.isNull(item))
                            continue;

                        TrackingEquipment equipment =  new TrackingEquipment();
                        equipment.setId(String.valueOf(item.get("id")));
                        equipment.setCreatedAt(new Date(Long.parseLong(String.valueOf(item.get("createdAt")))));
                        //equipment.setCreatedBy(String.valueOf(item.get("createdBy")));
                        Optional.ofNullable(item.get("isp")).ifPresent(p->equipment.setIspId(String.valueOf(p).split("\\|")[1]));
                        equipment.setSerial(String.valueOf(item.get("serial")));
                        Optional.ofNullable(item.get("uId")).ifPresent(p->equipment.setuId(String.valueOf(p)));
                        //Optional.ofNullable(item.get("onboardBy")).ifPresent(p->equipment.setOnboardBy(String.valueOf(p)));
                        Optional.ofNullable(item.get("onboardAt")).ifPresent(p->equipment.setOnboardAt(new Date(Long.parseLong(String.valueOf(p)))));
                        records.add(equipment);
                    }
                    SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                    scrollRequest.scroll(scroll);
                    response = client.scroll(scrollRequest, RequestOptions.DEFAULT);
                    scrollId = response.getScrollId();
                    searchHits = response.getHits().getHits();
                }

                ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
                clearScrollRequest.addScrollId(scrollId);
                client.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
                t2 = CommonUtils.getCurrentTimeInMillis();
            } catch (IOException e) {
                logger.error("Get error on client.search");
            }
        }

        logger.info("t2 - t1:[{}]", t2 -t1);
        return records;
    }

    public List<EquipmentTrackingRecord> getEquipmentTrackingRecords(Boolean pagination, Integer offset, Integer limit, String serialNumber, String ispId) throws Exception {

        List<EquipmentTrackingRecord> equipmentTrackingRecords = new ArrayList<>();

        Long onLineTime = getOnLineTime();

        List<TrackingEquipment> trackingEquipments = null;

        if (elasticSearchEnable) {
            trackingEquipments = getRecordsFromES(pagination, offset, limit, serialNumber, ispId);
        } else {
            if (pagination) {
                PaginatedRequest paginatedRequest = new PaginatedRequest();
                paginatedRequest.setOffset(offset);
                paginatedRequest.setMax(limit);
                paginatedRequest.setSortBy("createdAt");
                paginatedRequest.setOrder(Order.ASC);

                Page page = dataAccessService.read(TrackingEquipment.class, new HashMap<>(), paginatedRequest, GET_TRACKING_EQUIPMENTS, GET_TRACKING_EQUIPMENT_COUNT);
                trackingEquipments = page.getObjects();
            } else {
                trackingEquipments = (List<TrackingEquipment>) dataAccessService.read(TrackingEquipment.class);
            }
        }

        for(TrackingEquipment trackingEquipment : trackingEquipments) {

            if (false == elasticSearchEnable) {
                if (serialNumber != null) {
                    if (!trackingEquipment.getSerial().equals(serialNumber)) continue;
                }

                if (ispId != null) {
                    if (!trackingEquipment.getIspId().equals(ispId)) continue;
                }
            }

            EquipmentTrackingRecord equipmentTrackingRecord = new EquipmentTrackingRecord();

            equipmentTrackingRecord.setId(trackingEquipment.getId());
            equipmentTrackingRecord.setSerialNumber(trackingEquipment.getSerial());
            equipmentTrackingRecord.setIspId(trackingEquipment.getIspId());
            equipmentTrackingRecord.setProvisionedTime(trackingEquipment.getCreatedAt().getTime());
            equipmentTrackingRecord.setOnboardTime(trackingEquipment.getOnboardAt() == null ? 0L : trackingEquipment.getOnboardAt().getTime());

            Optional<ApDetailDto> optApDetailDto = apDetailDao.findBySerial(trackingEquipment.getSerial());
            if(optApDetailDto.isPresent()) {
                Long timestamp = optApDetailDto.get().getSendingTime();
                equipmentTrackingRecord.setEquipmentStatus(timestamp > onLineTime ? "Up" : "Down");
                equipmentTrackingRecord.setLastReportTime(optApDetailDto.get().getNtReportTimestamp());
                equipmentTrackingRecord.setFirstReportTime(0L);
            } else {
                equipmentTrackingRecord.setEquipmentStatus("Down");
                equipmentTrackingRecord.setLastReportTime(0L);
                equipmentTrackingRecord.setFirstReportTime(0L);
            }

            equipmentTrackingRecords.add(equipmentTrackingRecord);
        }

        return equipmentTrackingRecords;
    }

    public List<EquipmentTrackingRecord> getEquipmentTrackingRecordById(String id) throws Exception {

        List<EquipmentTrackingRecord> equipmentTrackingRecords = new ArrayList<>();

        TrackingEquipment trackingEquipment = (TrackingEquipment) dataAccessService.read(TrackingEquipment.class, id);
        if(trackingEquipment != null) {

            Long onLineTime = getOnLineTime();

            EquipmentTrackingRecord equipmentTrackingRecord = new EquipmentTrackingRecord();
            equipmentTrackingRecord.setId(trackingEquipment.getId());
            equipmentTrackingRecord.setSerialNumber(trackingEquipment.getSerial());
            equipmentTrackingRecord.setIspId(trackingEquipment.getIspId());
            equipmentTrackingRecord.setProvisionedTime(trackingEquipment.getCreatedAt().getTime());
            equipmentTrackingRecord.setOnboardTime(trackingEquipment.getOnboardAt() == null ? 0L : trackingEquipment.getOnboardAt().getTime());

            Optional<ApDetailDto> optApDetailDto = apDetailDao.findBySerial(trackingEquipment.getSerial());
            if(optApDetailDto.isPresent()) {
                Long timestamp = optApDetailDto.get().getSendingTime();
                equipmentTrackingRecord.setEquipmentStatus(timestamp > onLineTime ? "Up" : "Down");
                equipmentTrackingRecord.setLastReportTime(optApDetailDto.get().getNtReportTimestamp());
                equipmentTrackingRecord.setFirstReportTime(0L);
            } else {
                equipmentTrackingRecord.setEquipmentStatus("Down");
                equipmentTrackingRecord.setLastReportTime(0L);
                equipmentTrackingRecord.setFirstReportTime(0L);
            }

            equipmentTrackingRecords.add(equipmentTrackingRecord);
        }

        return equipmentTrackingRecords;
    }

    public List<EquipmentTransaction> getEquipmentTransactions() throws Exception {

        List<EquipmentTransaction> equipmentTransactions = new ArrayList<>();

        List<TrackingTransaction> trackingTransactions = (List<TrackingTransaction>) dataAccessService.read(TrackingTransaction.class);
        for(TrackingTransaction trackingTransaction : trackingTransactions) {
            EquipmentTransactionPayload payload = new EquipmentTransactionPayload();
            payload.setIspId(trackingTransaction.getIspId());
            payload.setDescription(trackingTransaction.getDescription());

            EquipmentTransactionUser user = new EquipmentTransactionUser();
            User u = (User) dataAccessService.read(User.class, trackingTransaction.getCreatedBy());
            if(u != null) {
                user.setId(u.getId());
                user.setName(u.getUsername());
                user.setRoleId(u.getUserRole().iterator().next().getId());
            }

            EquipmentTransactionUnitStats unitStats = new EquipmentTransactionUnitStats();
            unitStats.setComplete(trackingTransaction.getNoOfRecordUpdated());
            unitStats.setFailed(trackingTransaction.getNoOfRecordFailed() + trackingTransaction.getNoOfRecordInvalid());

            EquipmentTransactionErrors errors = new EquipmentTransactionErrors();

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            HashMap<String, Object> params = new HashMap<>();
            params.put("transactionId", trackingTransaction.getId());
            params.put("status", "Failed");

            List<DBObject> dbObjectList = mongoService.findList(params, null, TRANSACTION_RECORDS, mongoFieldOptions);
            List<String> errorSerialNumbers = new ArrayList<>();
            if(!dbObjectList.isEmpty()) {
                for(DBObject dbObject : dbObjectList) {
                    String serial = String.valueOf(dbObject.get("serialNumber"));
                    if(serial != null) {
                        errorSerialNumbers.add(serial);
                    }
                }
            }
            errors.setSerialNumber(errorSerialNumbers);


            if(trackingTransaction.getType().equals(ContentType.error.name())) {
                errors.setFileFormat(true);
            } else {
                errors.setFileFormat(false);
            }

            EquipmentTransaction equipmentTransaction = new EquipmentTransaction();
            equipmentTransaction.setId(trackingTransaction.getId());
            equipmentTransaction.setStatus(trackingTransaction.getStatus());
            equipmentTransaction.setAction(trackingTransaction.getAction());
            equipmentTransaction.setTime(trackingTransaction.getCreatedAt().getTime());
            equipmentTransaction.setPayload(payload);
            equipmentTransaction.setUser(user);
            equipmentTransaction.setUnitStats(unitStats);
            equipmentTransaction.setErrors(errors);

            equipmentTransactions.add(equipmentTransaction);
        }

        return equipmentTransactions;
    }

    public List<EquipmentTransaction> getEquipmentTransactionById(String transactionId) throws Exception {

        List<EquipmentTransaction> equipmentTransactions = new ArrayList<>();

        TrackingTransaction trackingTransaction = (TrackingTransaction) dataAccessService.read(TrackingTransaction.class, transactionId);

        EquipmentTransactionPayload payload = new EquipmentTransactionPayload();
        payload.setIspId(trackingTransaction.getIspId());
        payload.setDescription(trackingTransaction.getDescription());

        EquipmentTransactionUser user = new EquipmentTransactionUser();
        User u = (User) dataAccessService.read(User.class, trackingTransaction.getCreatedBy());
        if(u != null) {
            user.setId(u.getId());
            user.setName(u.getUsername());
            user.setRoleId(u.getUserRole().iterator().next().getId());
        }

        EquipmentTransactionUnitStats unitStats = new EquipmentTransactionUnitStats();
        unitStats.setComplete(trackingTransaction.getNoOfRecordUpdated());
        unitStats.setFailed(trackingTransaction.getNoOfRecordFailed() + trackingTransaction.getNoOfRecordInvalid());

        EquipmentTransactionErrors errors = new EquipmentTransactionErrors();

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, Object> params = new HashMap<>();
        params.put("transactionId", trackingTransaction.getId());
        params.put("status", "Failed");

        List<DBObject> dbObjectList = mongoService.findList(params, null, TRANSACTION_RECORDS, mongoFieldOptions);
        List<String> errorSerialNumbers = new ArrayList<>();
        if(!dbObjectList.isEmpty()) {
            for(DBObject dbObject : dbObjectList) {
                String serial = String.valueOf(dbObject.get("serialNumber"));
                if(serial != null) {
                    errorSerialNumbers.add(serial);
                }
            }
        }
        errors.setSerialNumber(errorSerialNumbers);


        if(trackingTransaction.getType().equals(ContentType.error.name())) {
            errors.setFileFormat(true);
        } else {
            errors.setFileFormat(false);
        }

        EquipmentTransaction equipmentTransaction = new EquipmentTransaction();
        equipmentTransaction.setId(trackingTransaction.getId());
        equipmentTransaction.setStatus(trackingTransaction.getStatus());
        equipmentTransaction.setAction(trackingTransaction.getAction());
        equipmentTransaction.setTime(trackingTransaction.getCreatedAt().getTime());
        equipmentTransaction.setPayload(payload);
        equipmentTransaction.setUser(user);
        equipmentTransaction.setUnitStats(unitStats);
        equipmentTransaction.setErrors(errors);

        equipmentTransactions.add(equipmentTransaction);

        return equipmentTransactions;
    }

}
