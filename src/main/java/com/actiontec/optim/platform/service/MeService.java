package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.MeRequest;
import com.actiontec.optim.platform.constant.ActiontecSQL;
import com.actiontec.optim.platform.model.Me;
import com.actiontec.optim.platform.model.MeAddressDto;
import com.actiontec.optim.platform.model.MeIspDto;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.entities.Subscriber;
import com.incs83.app.entities.SubscriberExtraData;
import com.incs83.app.entities.User;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.ApiException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public class MeService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    public enum authProvider {
        Actiontec,
        Microsoft,
        Google,
        Facebook,
        Others
    }

    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private At3Adapter at3Adapter;

    public Me getMeInfo() throws Exception {

        String subscriberId = ExecutionContext.get().getUsercontext().getId();
        Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, subscriberId);
        if(subscriber == null) {
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("subscriber_id", subscriberId);
        List<SubscriberExtraData> subscriberExtraDataList = (List<SubscriberExtraData>) dataAccessService.read(SubscriberExtraData.class, ActiontecSQL.GET_SUBSCRIBER_EXTRA_DATA, params);

        params.clear();
        params.put("email", subscriber.getEmail());
        List<User> users = (List<User>) dataAccessService.read(User.class, ActiontecSQL.GET_USER_BY_EMAIL, params);

        Me me = new Me();
        MeIspDto meIspDto = new MeIspDto();
        MeAddressDto meAddressDto = new MeAddressDto();

        me.setId(subscriber.getId());
        me.setFirstName(subscriber.getFirstName());
        me.setLastName(subscriber.getLastName());
        me.setEmail(subscriber.getEmail());
        me.setPhone(subscriber.getPhoneNo() != null ? subscriber.getPhoneNo() : "");
        me.setAvatarUrl(subscriber.getImageUrl() != null ? subscriber.getImageUrl() : "");
        me.setAuthProvider(users.isEmpty() ? authProvider.Others.name() : authProvider.Actiontec.name());

        meIspDto.setId(at3Adapter.getIspIdByGroupId(subscriber.getCompartment().iterator().next().getId()));
        meIspDto.setName(at3Adapter.getIspNameById(meIspDto.getId()));
        me.setIsp(meIspDto);

        if(subscriberExtraDataList != null && !subscriberExtraDataList.isEmpty()) {
            SubscriberExtraData subscriberExtraData = subscriberExtraDataList.get(0);
            me.setDisplayName(subscriberExtraData.getDisplayName() != null ? subscriberExtraData.getDisplayName() : "");
            meAddressDto.setCountry(subscriberExtraData.getCountry() != null ? subscriberExtraData.getCountry() : "");
            meAddressDto.setCity(subscriberExtraData.getCity() != null ? subscriberExtraData.getCity() : "");
            meAddressDto.setState(subscriberExtraData.getState() != null ? subscriberExtraData.getState() : "");
        } else {
            me.setDisplayName("");
            meAddressDto.setCountry("");
            meAddressDto.setCity("");
            meAddressDto.setState("");
        }

        me.setAddress(meAddressDto);
        return me;
    }

    public void putMeInfo(MeRequest request) throws Exception {

        String subscriberId = ExecutionContext.get().getUsercontext().getId();
        Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, subscriberId);
        if(subscriber == null) {
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("subscriber_id", subscriberId);
        List<SubscriberExtraData> subscriberExtraDataList = (List<SubscriberExtraData>) dataAccessService.read(SubscriberExtraData.class, ActiontecSQL.GET_SUBSCRIBER_EXTRA_DATA, params);

        SubscriberExtraData subscriberExtraData;
        if(subscriberExtraDataList != null && !subscriberExtraDataList.isEmpty()) {
            subscriberExtraData = subscriberExtraDataList.get(0);
            subscriberExtraData.setDisplayName(request.getDisplayName() != null ? request.getDisplayName() : "");
            dataAccessService.update(SubscriberExtraData.class, subscriberExtraData);
        } else {
            subscriberExtraData = new SubscriberExtraData();
            subscriberExtraData.setDisplayName(request.getDisplayName() != null ? request.getDisplayName() : "");
            subscriberExtraData.setSubscriber_id(subscriberId);
            CommonUtils.setCreateEntityFields(subscriberExtraData);
            CommonUtils.setUpdateEntityFields(subscriberExtraData);
            dataAccessService.create(SubscriberExtraData.class, subscriberExtraData);
        }
    }
}
