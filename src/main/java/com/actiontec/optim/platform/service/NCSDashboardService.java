package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v6.dto.NCSDashboardDTO;
import com.actiontec.optim.platform.repository.NCSDashboardRepo;
import com.incs83.app.business.v2.ManageClusterServices;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class NCSDashboardService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private ManageClusterServices manageClusterServices;

    @Autowired
    private NCSDashboardRepo ncsDashboardRepo;

    public NCSDashboardDTO getDashboardStats(String clusterId) throws Exception {
        NCSDashboardDTO dashboardDTO = new NCSDashboardDTO();

        String ispId = null;
        String ispName = null;

        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            // groupd admin can only access their own isp
            ispId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            ispName = at3Adapter.getIspNameById(ispId);
        } else if (CommonUtils.isSysAdmin()) {
            // sys admin can access by any cluster id and isp
            if (!clusterId.equals("0")) {
                ispName = manageClusterServices.getIspNameForDefaultCluster(clusterId);
                ispId = manageCommonService.getIspByName(ispName);
            }
        }

        logger.info("ispId: {}", ispId);
        logger.info("ispName: {}", ispName);

        NCSDashboardDTO.NetworkStats networkStats = ncsDashboardRepo.getNetworkStats(ispId, clusterId);
        dashboardDTO.setNetworkStats(networkStats);
        
        NCSDashboardDTO.EquipmentStats equipmentStats = ncsDashboardRepo.getEquipmentStats(ispName);
        dashboardDTO.setEquipmentStats(equipmentStats);
        
        NCSDashboardDTO.DeviceStats deviceStats = ncsDashboardRepo.getDeviceStats(ispName);
        dashboardDTO.setDeviceStats(deviceStats);
        
        return dashboardDTO;
    }
} 