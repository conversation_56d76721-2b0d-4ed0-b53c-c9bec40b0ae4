package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.model.NetworkOptical;
import com.actiontec.optim.platform.model.NetworkOpticalModule;
import com.actiontec.optim.platform.model.NetworkOpticalStats;
import com.actiontec.optim.platform.repository.NetworkOpticalRepo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OpticalService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private NetworkOpticalRepo networkOpticalRepo;

    public List<NetworkOptical> findOpticalByEquipmentId(String equipmentIdOrSerialOrSTN) throws Exception {

        String serial = at3Adapter.getRgwSerialByStn(equipmentIdOrSerialOrSTN);

        List<NetworkOptical> networkOpticalList = networkOpticalRepo.findOpticalBySerial(serial);
        if(networkOpticalList == null) {
            throw new NoSuchEntityException();
        }

        return networkOpticalList;
    }

    public List<NetworkOptical> findOpticalByEquipmentIdAndOpticsId(String equipmentIdOrSerialOrSTN, String opticsId) throws Exception {

        String serial = at3Adapter.getRgwSerialByStn(equipmentIdOrSerialOrSTN);

        List<NetworkOptical> networkOpticalList = networkOpticalRepo.findOpticalBySerialAndOpticsId(serial, opticsId);
        if(networkOpticalList == null) {
            throw new NoSuchEntityException();
        }

        return networkOpticalList;
    }

    public NetworkOpticalModule findOpticalModuleByEquipmentId(String equipmentIdOrSerialOrSTN) throws Exception {

        String serial = at3Adapter.getRgwSerialByStn(equipmentIdOrSerialOrSTN);

        NetworkOpticalModule networkOpticalModule = networkOpticalRepo.findOpticalModuleBySerial(serial);
        if(networkOpticalModule == null) {
            throw new NoSuchEntityException();
        }

        return networkOpticalModule;
    }

    public List<NetworkOpticalStats> findOpticalStatsByEquipmentIdAndDuration(String equipmentIdOrSerialOrSTN, Long duration) throws Exception {

        String serial = at3Adapter.getRgwSerialByStn(equipmentIdOrSerialOrSTN);

        List<NetworkOpticalStats> networkOpticalStatsList = networkOpticalRepo.findOpticalStatsBySerialAndDuration(serial, duration);
        if(networkOpticalStatsList == null) {
            throw new NoSuchEntityException();
        }

        return networkOpticalStatsList;
    }

}
