package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.*;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.SimpleRpcService;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.RttyRequest;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;

@Service
public class RemoteAccessService {

    private final Logger logger = LogManager.getLogger(this.getClass());
    @Value("${rtty.server:rttys.optimdev.io}")
    private String server;
    @Value("${rtty.port:5912}")
    private int port;
    private String token = "a1d4cdb1a3cd6a0e94aa3599afcddcf5";

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SimpleRpcService cpeRpcService;

    public RemoteAccessHttpResponse getHttpAccess(String equipmentId) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentId);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        RemoteAccessHttpResponse response = new RemoteAccessHttpResponse();
        String rpcUri = RpcConstants.REMOTE_ACCESS_HTTP_URI;

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userEquipment.getRgwSerial(), equipmentId, rpcUri, "GET", "{}", ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        if (!String.valueOf(rpcResult.get("code")).equals("200")) {
            logger.error("serial:[{}] response:[{}]", equipmentId, rpcResult.toString());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), rpcUri + " RPC Failed");
        }

        if (rpcResult.get("payload") != null) {
            List<Map<String, Object>> payloadList = (List<Map<String, Object>>) rpcResult.get("payload");
            Map payLoad = payloadList.get(0);
            int port = (int) payLoad.get("port");
            boolean enabled = (boolean)payLoad.get("enable");
            int remainingTime = (int) payLoad.get("duration");

            HashMap<String, String> queryParam = new HashMap<>();
            queryParam.put("userId", userEquipment.getRgwSerial());
            queryParam.put("serialNumber", userEquipment.getRgwSerial());
            DBObject apDetail = mongoService.findOne(AP_DETAIL, queryParam);
            if (Objects.isNull(apDetail)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Unable to fetch ISP for "+userEquipment.getRgwSerial() +" RGW.");
            }
            DBObject wanDetails = (DBObject) apDetail.get("wan");
            String wanIp = Objects.isNull(wanDetails.get("devIpAddress")) ? null : wanDetails.get("devIpAddress").toString();
            response.setEnabled(enabled);
            response.setPort(port);
            response.setUrl("http://" + wanIp);
            response.setRemainingTime(remainingTime*60);
        }

        return response;
    }

    public RemoteAccessResponse putHttpAccess(String equipmentId, RemoteAccessHttpRequest request) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentId);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Remote Access RPC Failed");

//        if (request.getDuration() > ApplicationConstants.REMOTE_ACCESS_HTTP_MAX_TIMEOUT_VALUE)
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " Bad duration for request.");

//        RemoteAccessResponse response = null;
//        String rpcUri = RpcConstants.REMOVE_ACCESS_HTTP_URI;
//        HashMap<String, Object> payloadMap = new HashMap<>();
//        payloadMap.put("enable", request.getEnabled());
//        if (request.getDuration() != -1)
//            payloadMap.put("duration", request.getDuration()/60);
//        if (request.getPort() != 0)
//            payloadMap.put("port", request.getPort());
//        String payload = objectMapper.writeValueAsString(payloadMap);
//
//        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userEquipment.getRgwSerial(), equipmentId, rpcUri, "PUT", payload, ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
//        if (!String.valueOf(rpcResult.get("code")).equals("200")) {
//            logger.error("serial:[{}] response:[{}]", equipmentId, rpcResult.toString());
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), rpcUri + " RPC Failed");
//        }
//
//        if (request.getEnabled() == true && rpcResult.get("payload") != null) {
//            List<Map<String, Object>> payloadList = (List<Map<String, Object>>) rpcResult.get("payload");
//            Map payLoad = payloadList.get(0);
//            int port = (int) payLoad.get("port");
//
//            HashMap<String, String> queryParam = new HashMap<>();
//            queryParam.put("userId", userEquipment.getRgwSerial());
//            queryParam.put("serialNumber", userEquipment.getRgwSerial());
//            DBObject apDetail = mongoService.findOne(AP_DETAIL, queryParam);
//            if (Objects.isNull(apDetail)) {
//                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Unable to fetch ISP for "+userEquipment.getRgwSerial() +" RGW.");
//            }
//
//            DBObject wanDetails = (DBObject) apDetail.get("wan");
//            String wanIp = Objects.isNull(wanDetails.get("devIpAddress")) ? null : wanDetails.get("devIpAddress").toString();
//            response = new RemoteAccessResponse();
//            response.setPort(port);
//            response.setUrl("http://" + wanIp);
//        }

//        return response;
    }

    public RemoteAccessRtty getRttyAccess(String equipmentId) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentId);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        RemoteAccessRtty response = new RemoteAccessRtty();
        String rpcUri = RpcConstants.REMOTE_ACCESS_RTTY_URI;

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userEquipment.getRgwSerial(), equipmentId, rpcUri, "GET", "{}", ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        if (!String.valueOf(rpcResult.get("code")).equals("200")) {
            logger.error("serial:[{}] response:[{}]", equipmentId, rpcResult.toString());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), rpcUri + " RPC Failed");
        }

        if (rpcResult.get("payload") != null) {
            List<RemoteAccessRtty> remoteAccessRttyList = objectMapper.convertValue(rpcResult.get("payload"), at3Adapter.getCollectionType(List.class, RemoteAccessRtty.class));
            response = remoteAccessRttyList.get(0);
        }

        return response;
    }

    public RemoteAccessRttyResponse putRttyAccess(String equipmentId, RemoteAccessRttyRequest request) throws Exception {
        if (!CommonUtils.isSysAdmin()) {
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        }

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentId);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        if (request.getDuration() > ApplicationConstants.REMOTE_ACCESS_RTTY_MAX_TIMEOUT_VALUE)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " Bad duration for request.");

        RemoteAccessRttyResponse response = null;
        RttyRequest rttyRequest = (RttyRequest) dataAccessService.read(RttyRequest.class, equipmentId);
        boolean newRequest = (Objects.isNull(rttyRequest))? true: false;
        String rpcUri = RpcConstants.REMOTE_ACCESS_RTTY_URI;
        HashMap<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("enable", request.getEnabled());
        if (request.getEnabled() == true) {
            if (newRequest == true || rttyRequest.getExpired().before(new Date())) {
                payloadMap.put("id", CommonUtils.generateUUID().substring(16));
                payloadMap.put("token", token);
                payloadMap.put("url", server);
                payloadMap.put("port", port);
            } else {
                payloadMap.put("id", rttyRequest.getId());
            }
            payloadMap.put("duration", request.getDuration());
            payloadMap.put("persistent", request.getPersistent());
        }
        String payload = objectMapper.writeValueAsString(payloadMap);
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userEquipment.getRgwSerial(), equipmentId, rpcUri, "PUT", payload, ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        if (!String.valueOf(rpcResult.get("code")).equals("200")) {
            logger.error("serial:[{}] response:[{}]", equipmentId, rpcResult.toString());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), rpcUri + " RPC Failed");
        }

        if (request.getEnabled() == false && Objects.nonNull(rttyRequest)) {
            dataAccessService.delete(RttyRequest.class, equipmentId);
        } else if (request.getEnabled() == true && rpcResult.get("payload") != null) {
            List<Map<String, Object>> payloadList = (List<Map<String, Object>>) rpcResult.get("payload");

            if (newRequest == true || rttyRequest.getExpired().before(new Date())) {
               rttyRequest = new RttyRequest();
               rttyRequest.setSerialNumber(equipmentId);
               rttyRequest.setToken(String.valueOf(payloadMap.get("token")));
            }

            Map payLoad = payloadList.get(0);
            rttyRequest.setEnabled((boolean)payLoad.get("enable"));
            rttyRequest.setId((String) payLoad.get("id"));
            rttyRequest.setUrl((String) payLoad.get("url"));
            rttyRequest.setPort((int) payLoad.get("port"));
            rttyRequest.setExpired(new Date(Long.valueOf(String.valueOf(payLoad.get("remainingTime")))*1000L + CommonUtils.getCurrentTimeInMillis()));
            rttyRequest.setPersistent((boolean)payLoad.get("persistent"));

            if (newRequest == true){
                dataAccessService.create(RttyRequest.class, rttyRequest);
            } else {
                dataAccessService.update(RttyRequest.class, rttyRequest);
            }

            response = new RemoteAccessRttyResponse();
            response.setUrl((String) payLoad.get("url"));
            response.setPort((int) payLoad.get("port"));
            response.setConnectionId((String) payLoad.get("id"));
        }

        return response;
    }
}
