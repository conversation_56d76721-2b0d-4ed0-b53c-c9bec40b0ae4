package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dao.CpeTopologyDao;
import com.actiontec.optim.mongodb.dao.HostDao;
import com.actiontec.optim.mongodb.dao.WifiStationDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.HostDto;
import com.actiontec.optim.mongodb.dto.WifiStationDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v6.dto.ConnectionDTO;
import com.actiontec.optim.platform.api.v6.dto.DeviceDTO;
import com.actiontec.optim.platform.model.UserDevice;
import com.actiontec.optim.platform.model.enums.*;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.Equipment;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class NetworkTopologyService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private HostDao hostDao;

    @Autowired
    private WifiStationDao wifiStationDao;

    @Autowired
    private ModelSpecificService modelSpecificService;

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private CpeTopologyDao cpeTopologyDao;;

    public Map<String, UserDevice> buildTopologyByNetwork(String networkId) throws Exception {
        logger.debug("buildTopologyByNetwork [{}]", networkId);
        return buildTopologyByNetworkId(networkId);
    }

    private Map<String, UserDevice> buildTopologyByNetworkId(String networkId) throws Exception {
        logger.debug("buildTopologyByNetworkId [{}]", networkId);

        Map<String, UserDevice> userDeviceMap = new HashMap<>();
        Map<String, ApDetailDto> apDetailDtoMap = apDetailDao.listByNetworkId(networkId);
        Map<String, HostDto> hostDtos = hostDao.listByNetworkId(networkId);
        Map<String, WifiStationDto> wifiStationDtos = wifiStationDao.listByNetworkId(networkId);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(networkId);
        List<DeviceDTO> deviceDtos = cpeTopologyDao.getTopologyDevices(userEquipment.getRgwSerial(), userEquipment.getServiceTelephoneNo(), null);

        for (DeviceDTO deviceDto : deviceDtos) {
            UserDevice userDevice = new UserDevice();
            String mac = deviceDto.getMacAddress();
            userDevice.setMacAddress(mac);

            userDevice.setStatus(ConnectionStatus.Up.name().equals(deviceDto.getStatus()) ?
                    ConnectionStatus.Up : ConnectionStatus.Down);

            switch (CpeDeviceType.valueOf(deviceDto.getType())) {
                case Router:
                    userDevice.setDeviceType(UserDeviceType.Gateway);
                    userDevice.setUpstream("Internet");
                    userDevice.setPhyType(UserDevicePhyType.Ethernet);
                    userDevice.setHostName(deviceDto.getHostname());
                    break;

                case Extender:
                    userDevice.setDeviceType(UserDeviceType.Extender);
                    userDevice.setHostName(deviceDto.getHostname() + "(EXT)");
                    userDevice.setUpstream(getSerialFromApDetail(apDetailDtoMap, deviceDto.getUpstreamId()));
                    userDevice.setPhyType(resolvePhyType(deviceDto));
                    break;

                default:
                    userDevice.setDeviceType(UserDeviceType.Other);
                    userDevice.setHostName(deviceDto.getHostname());
                    userDevice.setUpstream(getSerialFromApDetail(apDetailDtoMap, deviceDto.getUpstreamId()));
                    userDevice.setPhyType(resolvePhyType(deviceDto));
                    if (userDevice.getPhyType().equals(UserDevicePhyType.Ethernet)) {
                        HostDto hostDto = hostDtos.get(mac);
                        if (hostDto != null) {
                            userDevice.setFriendlyName(hostDto.getFriendlyName() != null ? hostDto.getFriendlyName() : "");
                        }
                    } else {
                        WifiStationDto wifiStationDto = wifiStationDtos.get(mac);
                        if (wifiStationDto != null) {
                            userDevice.setFriendlyName(wifiStationDto.getFriendlyName() != null ? wifiStationDto.getFriendlyName() : "");
                        }
                    }
                    break;
            }

            ApDetailDto apDetailDto = apDetailDtoMap.get(mac);
            if (apDetailDto != null) {
                userDevice.setSerialNumber(apDetailDto.getSerialNumber());
                userDevice.setNtTimestamp(apDetailDto.getNtInfoTimestamp());
                userDevice.setFriendlyName(apDetailDto.getFriendlyName());
            }

            userDeviceMap.put(mac, userDevice);
        }

        return userDeviceMap;
    }

    private String getSerialFromApDetail(Map<String, ApDetailDto> apDetailDtoMap, String mac) {
        ApDetailDto apDetailDto = apDetailDtoMap.get(mac);
        return apDetailDto != null ? apDetailDto.getSerialNumber() : "";
    }

    private UserDevicePhyType resolvePhyType(DeviceDTO deviceDto) {
        if (deviceDto.getConnections() != null && !deviceDto.getConnections().isEmpty()) {
            ConnectionDTO conn = deviceDto.getConnections().get(0);
            return CpeConnectionType.Wireless.name().equals(conn.getType())
                    ? UserDevicePhyType.Wifi : UserDevicePhyType.Ethernet;
        }
        return UserDevicePhyType.Unknown;
    }

    public Map<String, UserDevice> buildTopologyByStn(String stn) {
        logger.debug("buildTopologyByStn [{}]", stn);
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return buildTopologyByUserId(userId);
    }

    private Map<String, UserDevice> buildTopologyByUserId(String userId) {
        logger.debug("buildTopologyByUserId [{}]", userId);
        long currentMillis = System.currentTimeMillis();
        long onlineBaseMillis = currentMillis - at3Adapter.getEquipmentOfflineMins() * 60000;
        long validBaseMillis = currentMillis - UserDeviceService.DEVICE_VALID_MILLIS;

        List<ApDetailDto> apDetailDtos = apDetailDao.listByUserId(userId);
        ApDetailDto rgwApDetailDto = pickGateway(userId, apDetailDtos);
        String rootSerial = rgwApDetailDto.getSerialNumber();


        // 1. list all online user devices from hostnameDetail, group by serial, and order by group size.
        //    bigger group means higher topology level.
        List<HostDto> onlineHostDtos = hostDao.listByUserId(userId, onlineBaseMillis);
        Map<String, List<HostDto>> eqpSnToDevices = groupByUserId(onlineHostDtos);
        LinkedHashMap<String, List<HostDto>> orderedEqpSnToDevices = orderBySize(eqpSnToDevices);


        Map<String, UserDevice> userDeviceMap = new HashMap<>();
        List<UserDevice> extenderUserDevices = new ArrayList<>();
        List<HostDto> hostsFromRgw = orderedEqpSnToDevices.remove(rgwApDetailDto.getSerialNumber());
        if (hostsFromRgw != null) {
            for (HostDto hostDto : hostsFromRgw) {
                UserDevice userDevice = genUserDevice(hostDto, rgwApDetailDto.getSerialNumber());
                userDeviceMap.put(userDevice.getMacAddress(), userDevice);

                if (UserDeviceType.Extender == userDevice.getDeviceType()) {
                    extenderUserDevices.add(userDevice);
                }
            }
        }
        logger.debug("buildTopologyByUserId userId:[{}] hostsFromRgwSize:[{}] extenderUserDevicesSize:[{}]",
                userId,
                Optional.ofNullable(hostsFromRgw).map(e -> e.size()).orElse(0),
                extenderUserDevices.size());


        for (Map.Entry<String, List<HostDto>> mapEntry : orderedEqpSnToDevices.entrySet()) {
            logger.debug("buildTopologyByUserId userId:[{}] serial:[{}] deviceSize:[{}]",
                    userId, mapEntry.getKey(), mapEntry.getValue().size());

            for (HostDto hostDto : mapEntry.getValue()) {
                UserDevice userDevice = userDeviceMap.get(hostDto.getMacAddress());
//
                if (userDevice == null) {
//                    for (UserDevice extenderUserDevice : extenderUserDevices) {
//                        if (modelSpecificService.isSameEquipment(extenderUserDevice.getMacAddress(), hostDto.getMacAddress())) {
//                            userDevice = extenderUserDevice;
//                            UserDevicePhyType.fromFormalName(hostDto.getPhyType()).ifPresent(userDevice::setPhyType);
//                            userDevice.setUpstream(mapEntry.getKey());
//                            userDevice.setNtTimestamp(hostDto.getNtTimestamp());
//                            break;
//                        }
//                    }

//                    if (userDevice == null) {
//                        // user device does not appear in rgw dsHostList, ignore it
//                        logger.debug("buildTopologyByUserId user device not appear in rgw. userId:[{}] serial:[{}] deviceMac:[{}]",
//                                userId, mapEntry.getKey(), hostDto.getMacAddress());
//                    }
                    logger.debug("buildTopologyByUserId user device not appear in rgw. userId:[{}] serial:[{}] deviceMac:[{}]",
                            userId, mapEntry.getKey(), hostDto.getMacAddress());
                } else {
                    UserDevicePhyType.fromFormalName(hostDto.getPhyType()).ifPresent(userDevice::setPhyType);
                    userDevice.setUpstream(mapEntry.getKey());
                    userDevice.setNtTimestamp(hostDto.getNtTimestamp());
                }
            }
        }


        // wifiStaList only include online devices, use wifiStaList to rectify upstream
        List<WifiStationDto> wifiStationDtos = wifiStationDao.listByUserId(userId, onlineBaseMillis);
        for (WifiStationDto wifiStationDto : wifiStationDtos) {
            UserDevice userDevice = null;

            // XXX: hack for extender mac
            for (UserDevice extenderUserDevice : extenderUserDevices) {
                if (modelSpecificService.isSameEquipment(extenderUserDevice.getMacAddress(), wifiStationDto.getMacAddress())) {
                    userDevice = extenderUserDevice;
                    userDevice.setPhyType(UserDevicePhyType.Wifi);
                    userDevice.setUpstream(wifiStationDto.getSerialNumber());
                    userDevice.setNtTimestamp(wifiStationDto.getNtTimestamp());
                    userDevice.setWanMacAddress(wifiStationDto.getMacAddress());
                    break;
                }
            }

            userDevice = userDeviceMap.get(wifiStationDto.getMacAddress());
            if (userDevice != null) {
                userDevice.setFriendlyName(wifiStationDto.getFriendlyName());
                if (!StringUtils.equals(userDevice.getUpstream(), wifiStationDto.getSerialNumber())) {
                    if (wifiStationDto.getNtTimestamp() >= userDevice.getNtTimestamp()) {
                        userDevice.setPhyType(UserDevicePhyType.Wifi);
                        userDevice.setUpstream(wifiStationDto.getSerialNumber());
                        userDevice.setNtTimestamp(wifiStationDto.getNtTimestamp());
                    }
                }
            }
        }


        // add offline but valid user devices
        String serial = manageCommonService.getGatewaySerialByUserId(userId).orElseGet(()->userId);
        List<HostDto> validHostDtos = hostDao.listByUserIdAndSerial(userId, serial, validBaseMillis);
        Set<String> wirelessStaSet = wifiStationDao.listByUserId(userId, validBaseMillis).stream().map(WifiStationDto::getMacAddress).collect(Collectors.toSet());
        Set<String> mocaDeviceSet = rgwApDetailDto.getMocaDevices().stream().map(ApDetailDto.MocaDeviceDto::getMacAddress).collect(Collectors.toSet());
        for (HostDto hostDto : validHostDtos) {
            UserDevice userDevice = userDeviceMap.get(hostDto.getMacAddress());
            if (userDevice != null) {
                // do nothing
            } else {
                userDevice = new UserDevice();
                userDevice.setMacAddress(hostDto.getMacAddress());
                userDevice.setStatus(ConnectionStatus.Down);
                userDevice.setFriendlyName(hostDto.getFriendlyName());
                UserDeviceType.fromName(hostDto.getDeviceType()).ifPresent(userDevice::setDeviceType);
                UserDeviceType.getCustomType(Optional.ofNullable(userDevice.getDeviceType()), hostDto.getCustomType()).ifPresent(userDevice::setDeviceType);

                userDevice.setUpstream(rootSerial);
                userDevice.setPhyType(wirelessStaSet.contains(hostDto.getMacAddress())?UserDevicePhyType.Wifi:
                        mocaDeviceSet.contains(hostDto.getMacAddress())? UserDevicePhyType.Moca: UserDevicePhyType.Ethernet);

                userDeviceMap.put(userDevice.getMacAddress(), userDevice);

                if (UserDeviceType.Extender == userDevice.getDeviceType()) {
                    extenderUserDevices.add(userDevice);
                }
            }
        }


        // add gateway, or append info to extender
        for (ApDetailDto apDetailDto : apDetailDtos) {
            if (StringUtils.equals(EquipmentType.GATEWAY.name(), apDetailDto.getType())) {
                UserDevice userDevice = new UserDevice();
                userDevice.setSerialNumber(apDetailDto.getSerialNumber());
                userDevice.setMacAddress(apDetailDto.getMacAddress());
                userDevice.setDeviceType(UserDeviceType.Gateway);
                if (apDetailDto.getNtInfoTimestamp() >= onlineBaseMillis) {
                    userDevice.setStatus(ConnectionStatus.Up);
                } else {
                    userDevice.setStatus(ConnectionStatus.Down);
                }
                userDevice.setUpstream("Internet");
                userDevice.setPhyType(wirelessStaSet.contains(apDetailDto.getMacAddress())?UserDevicePhyType.Wifi:
                        mocaDeviceSet.contains(apDetailDto.getMacAddress())? UserDevicePhyType.Moca: UserDevicePhyType.Ethernet);
                userDevice.setNtTimestamp(apDetailDto.getNtInfoTimestamp());
                userDevice.setFriendlyName(apDetailDto.getFriendlyName());
                userDevice.setHostName(apDetailDto.getModelName());
                userDeviceMap.put(userDevice.getMacAddress(), userDevice);
            } else if (StringUtils.equals(EquipmentType.EXTENDER.name(), apDetailDto.getType())) {
                UserDevice matchedExtenderUserDevice = null;
                for (UserDevice userDevice : extenderUserDevices) {
                    if (modelSpecificService.isSameEquipment(userDevice.getMacAddress(), apDetailDto.getMacAddress())) {
                        userDevice.setSerialNumber(apDetailDto.getSerialNumber());
                        userDevice.setFriendlyName(!StringUtils.isBlank(apDetailDto.getFriendlyName())? apDetailDto.getFriendlyName(): apDetailDto.getModelName() + "(EXT)");
                        userDevice.setHostName(apDetailDto.getModelName());
                        userDevice.setPhyType(wirelessStaSet.contains(apDetailDto.getMacAddress())?UserDevicePhyType.Wifi:
                                mocaDeviceSet.contains(apDetailDto.getMacAddress())? UserDevicePhyType.Moca: UserDevicePhyType.Ethernet);
                        matchedExtenderUserDevice = userDevice;
                        break;
                    }
                }

                if (matchedExtenderUserDevice == null) {
                    UserDevice userDevice = new UserDevice();
                    userDevice.setSerialNumber(apDetailDto.getSerialNumber());
                    userDevice.setMacAddress(apDetailDto.getMacAddress());
                    userDevice.setWanMacAddress(apDetailDto.getMacAddress());
                    userDevice.setDeviceType(UserDeviceType.Extender);
                    userDevice.setStatus(ConnectionStatus.Down);
                    userDevice.setUpstream(rgwApDetailDto.getSerialNumber());
                    userDevice.setFriendlyName(!StringUtils.isBlank(apDetailDto.getFriendlyName())? apDetailDto.getFriendlyName(): apDetailDto.getModelName() + "(EXT)");
                    userDevice.setHostName(apDetailDto.getModelName());
                    userDevice.setNtTimestamp(apDetailDto.getNtInfoTimestamp());
                    userDeviceMap.put(userDevice.getMacAddress(), userDevice);
                }
            }
        }

//        for (ApDetailDto apDetailDto : apDetailDtos) {
//            UserDevice userDevice = userDeviceMap.get(apDetailDto.getMacAddress());
//            if (userDevice != null) {
//                userDevice.setSerialNumber(apDetailDto.getSerialNumber());
//            } else {
//                if (StringUtils.equals("GATEWAY", apDetailDto.getType())) {
//                    userDevice = new UserDevice();
//                    userDevice.setMacAddress(apDetailDto.getMacAddress());
//                    if (StringUtils.equals("GATEWAY", apDetailDto.getType())) {
//                        userDevice.setSerialNumber(apDetailDto.getSerialNumber());
//                        userDevice.setDeviceType("Gateway");
//                        userDevice.setStatus("Up");
//                    } else {
//                        userDevice.setDeviceType("Extender");
//                        userDevice.setStatus("Down");
//                    }
//                    userDevice.setUpstream(rootSerial);
//                    userDevice.setNtTimestamp(apDetailDto.getNtTimestamp());
//                    userDeviceMap.put(userDevice.getMacAddress(), userDevice);
//                }
//            }
//        }

        // exclude dual mac of extender
        userDeviceMap.entrySet().removeIf(e -> {
            UserDevice userDevice = e.getValue();
            if (UserDeviceType.Extender != userDevice.getDeviceType()) {
                for (UserDevice extenderUserDevice : extenderUserDevices) {
                    if (modelSpecificService.isSameEquipment(extenderUserDevice.getMacAddress(), userDevice.getMacAddress())) {
                        return true;
                    }
                }
            }
            return false;
        });


        logger.debug("buildTopologyByUserId [{}] size:[{}]", userId, userDeviceMap.size());
        return userDeviceMap;
    }

    private Map<String, List<HostDto>> groupByUserId(List<HostDto> hostDtos) {
        Map<String, List<HostDto>> result = new HashMap<>();
        for (HostDto hostDto : hostDtos) {
            List<HostDto> groupedHostDtos = result.get(hostDto.getSerialNumber());
            if (groupedHostDtos == null) {
                groupedHostDtos = new ArrayList<>();
                result.put(hostDto.getSerialNumber(), groupedHostDtos);
            }
            groupedHostDtos.add(hostDto);
        }

        return result;
    }

    private LinkedHashMap<String, List<HostDto>> orderBySize(Map<String, List<HostDto>> devicesListMap) {
        // size bigger first
        LinkedHashMap<String, List<HostDto>> result = devicesListMap.entrySet().stream()
                .sorted((e1, e2) -> Integer.compare(e2.getValue().size(), e1.getValue().size()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));
        return result;
    }

    private ApDetailDto pickGateway(String userId, List<ApDetailDto> apDetailDtos) {
        ApDetailDto gateway = null;
        for (ApDetailDto apDetailDto : apDetailDtos) {
            if (StringUtils.equals(apDetailDto.getType(), EquipmentType.GATEWAY.name())) {
                if (gateway == null) {
                    gateway = apDetailDto;
                } else {
                    logger.error("multiple gateways with userId:[{}]", userId);
                    throw new RuntimeException("multiple gateways in cluster");
                }
            }
        }

        if (gateway == null) {
            logger.error("no gateway with userId:[{}]", userId);
            throw new RuntimeException("no gateway in cluster");
        }
        return gateway;
    }

    private UserDevice genUserDevice(HostDto hostDto, String upstream) {
        UserDevice userDevice = new UserDevice();
        userDevice.setMacAddress(hostDto.getMacAddress());
        userDevice.setWanMacAddress(hostDto.getMacAddress());
        userDevice.setStatus(ConnectionStatus.Up);
        userDevice.setFriendlyName(hostDto.getFriendlyName());
        UserDeviceType.fromName(hostDto.getDeviceType()).ifPresent(userDevice::setDeviceType);
        UserDeviceType.getCustomType(Optional.ofNullable(userDevice.getDeviceType()), hostDto.getCustomType()).ifPresent(userDevice::setDeviceType);
        UserDevicePhyType.fromFormalName(hostDto.getPhyType()).ifPresent(userDevice::setPhyType);
        userDevice.setUpstream(upstream);
        userDevice.setNtTimestamp(hostDto.getNtTimestamp());
        return userDevice;
    }

}
