package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.StatsAggregateDao;
import com.actiontec.optim.mongodb.dao.SteerAggregateDao;
import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.mongodb.dto.SteerDataDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.model.AirTimeBusy;
import com.actiontec.optim.platform.model.AirTimeBusyDistribution;
import com.actiontec.optim.platform.model.ChannelSelectDistribution;
import com.actiontec.optim.platform.model.SteeringLogsCount;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

@Service
public class AggregationService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private SteerAggregateDao steerAggregateDao;

    @Autowired
    private StatsAggregateDao statsAggregateDao;

    private ZonedDateTime getTimeForCriteria(int days) {
        ZonedDateTime ldtNow = ZonedDateTime.now();

        if(days == ApplicationConstants.LAST_DAY) {
            return ldtNow.minusDays(days).truncatedTo(ChronoUnit.HOURS);
        }

        return ldtNow.minusDays(days).truncatedTo(ChronoUnit.DAYS);
    }

    private Map<String, Object> getDateTimeForCriteria(int days) {

        ZonedDateTime ldtNow = ZonedDateTime.now();

        Map<String, Object> dateTimeMap = new HashMap<>();
        List<Date> dateList = new ArrayList<>();

        if(days == ApplicationConstants.LAST_DAY) {
            ZonedDateTime truncatedDateTime = ldtNow.minusDays(days).truncatedTo(ChronoUnit.DAYS);

            while ( truncatedDateTime.isBefore(ldtNow) ) {
                dateList.add(Date.from(truncatedDateTime.toInstant()));
                truncatedDateTime = truncatedDateTime.plusDays(1);
            }
        } else {
            ZonedDateTime truncatedDateTime = ldtNow.minusDays(days).truncatedTo(ChronoUnit.DAYS).withDayOfMonth(1);

            while ( truncatedDateTime.isBefore(ldtNow) ) {
                dateList.add(Date.from(truncatedDateTime.toInstant()));
                truncatedDateTime = truncatedDateTime.plusMonths(1);
            }
        }

        dateTimeMap.put("dates", dateList);
        dateTimeMap.put("startTime", ldtNow.minusDays(days));

        return dateTimeMap;
    }

    private SteeringLogsCount.ValueDto genValueObject(String steeringEnd, String originRadioKey, String destRadioKey, long count) {

        SteeringLogsCount.ValueDto valueDto = new SteeringLogsCount.ValueDto();
        valueDto.setSteeringEnd(steeringEnd);
        valueDto.setOriginRadioKey(originRadioKey);
        valueDto.setDestRadioKey(destRadioKey);
        valueDto.setCount(count);

        return valueDto;
    }

    private SteeringLogsCount genSteerObject(SteerDataDto steerData, int duration) {

        SteeringLogsCount steeringLogsCount = new SteeringLogsCount();
        ArrayList<SteeringLogsCount.ValueDto> valueDtoList = new ArrayList<>();

        steeringLogsCount.setEventType(ApplicationConstants.EVENT_STEER);
        steeringLogsCount.setDuration(duration);

        valueDtoList.add(genValueObject(ApplicationConstants.TARGET, ApplicationConstants.WIFI_2G, ApplicationConstants.WIFI_2G, steerData.getSteerTarget2g2g()));
        valueDtoList.add(genValueObject(ApplicationConstants.TARGET, ApplicationConstants.WIFI_2G, ApplicationConstants.WIFI_5G, steerData.getSteerTarget2g5g()));
        valueDtoList.add(genValueObject(ApplicationConstants.TARGET, ApplicationConstants.WIFI_5G, ApplicationConstants.WIFI_2G, steerData.getSteerTarget5g2g()));
        valueDtoList.add(genValueObject(ApplicationConstants.TARGET, ApplicationConstants.WIFI_5G, ApplicationConstants.WIFI_5G, steerData.getSteerTarget5g5g()));

        valueDtoList.add(genValueObject(ApplicationConstants.OTHER, ApplicationConstants.WIFI_2G, ApplicationConstants.WIFI_2G, steerData.getSteerOther2g2g()));
        valueDtoList.add(genValueObject(ApplicationConstants.OTHER, ApplicationConstants.WIFI_2G, ApplicationConstants.WIFI_5G, steerData.getSteerOther2g5g()));
        valueDtoList.add(genValueObject(ApplicationConstants.OTHER, ApplicationConstants.WIFI_5G, ApplicationConstants.WIFI_2G, steerData.getSteerOther5g2g()));
        valueDtoList.add(genValueObject(ApplicationConstants.OTHER, ApplicationConstants.WIFI_5G, ApplicationConstants.WIFI_5G, steerData.getSteerOther5g5g()));

        steeringLogsCount.setValues(valueDtoList);

        return steeringLogsCount;
    }

    private SteeringLogsCount genRoamObject(SteerDataDto steerData, int duration) {

        SteeringLogsCount steeringLogsCount = new SteeringLogsCount();
        ArrayList<SteeringLogsCount.ValueDto> valueDtoList = new ArrayList<>();

        steeringLogsCount.setEventType(ApplicationConstants.EVENT_ROAM);
        steeringLogsCount.setDuration(duration);

        valueDtoList.add(genValueObject(ApplicationConstants.NA, ApplicationConstants.WIFI_2G, ApplicationConstants.WIFI_2G, steerData.getRoam2g2g()));
        valueDtoList.add(genValueObject(ApplicationConstants.NA, ApplicationConstants.WIFI_2G, ApplicationConstants.WIFI_5G, steerData.getRoam2g5g()));
        valueDtoList.add(genValueObject(ApplicationConstants.NA, ApplicationConstants.WIFI_5G, ApplicationConstants.WIFI_2G, steerData.getRoam5g2g()));
        valueDtoList.add(genValueObject(ApplicationConstants.NA, ApplicationConstants.WIFI_5G, ApplicationConstants.WIFI_5G, steerData.getRoam5g5g()));

        steeringLogsCount.setValues(valueDtoList);

        return steeringLogsCount;
    }

    public List<SteeringLogsCount> getSteeringLogsCount(String scope, String scopeId, String stn, String eventType) throws Exception {

        List<String> eventTypes = new ArrayList<String>(Arrays.asList(eventType.split(",")));

        String userId = null;
        String ispName = null;

        SteerDataDto steerDataToday = null;
        SteerDataDto steerDataLast7Days = null;
        SteerDataDto steerDataLast30Days = null;
        SteerDataDto steerDataLast90Days = null;

        List<SteeringLogsCount> steeringLogsCountList = new ArrayList<>();

        if(scope.equals(ApplicationConstants.CLUSTER)) {
            if(!scopeId.equals(String.valueOf(ApplicationConstants.ZERO))) {
                String groupId = at3Adapter.getGroupIdByClusterId(scopeId);
                ispName = at3Adapter.getIspNameByGroupId(groupId);
            }
        } else if(scope.equals(ApplicationConstants.GROUP)) {
            ispName = at3Adapter.getIspNameByGroupId(scopeId);
        } else if(scope.equals(ApplicationConstants.ISP)) {
            ispName = at3Adapter.getIspNameById(scopeId);
        } else {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get Steering Logs Count Failed");
        }

        if(!stn.isEmpty() && stn != null) {
            userId = at3Adapter.getRgwSerialByStn(stn);
            logger.debug("userId: {}", userId);
        }

        if(scopeId.equals(String.valueOf(ApplicationConstants.ZERO)) && userId == null) {
            steerDataToday = steerAggregateDao.getSteerData(null, null, getTimeForCriteria(ApplicationConstants.LAST_DAY), steerAggregateDao.COLLECTION_statsSteeringPerIspPerHour);
            steerDataLast7Days = steerAggregateDao.getSteerData(null, null, getTimeForCriteria(ApplicationConstants.LAST_7_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerIspPerDay);
            steerDataLast30Days = steerAggregateDao.getSteerData(null, null, getTimeForCriteria(ApplicationConstants.LAST_30_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerIspPerDay);
            steerDataLast90Days = steerAggregateDao.getSteerData(null, null, getTimeForCriteria(ApplicationConstants.LAST_90_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerIspPerDay);
        } else {
            if (userId != null) {
                steerDataToday = steerAggregateDao.getSteerData(ispName, userId, getTimeForCriteria(ApplicationConstants.LAST_DAY), steerAggregateDao.COLLECTION_statsSteeringPerUserPerHour);
                steerDataLast7Days = steerAggregateDao.getSteerData(ispName, userId, getTimeForCriteria(ApplicationConstants.LAST_7_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerUserPerDay);
                steerDataLast30Days = steerAggregateDao.getSteerData(ispName, userId, getTimeForCriteria(ApplicationConstants.LAST_30_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerUserPerDay);
                steerDataLast90Days = steerAggregateDao.getSteerData(ispName, userId, getTimeForCriteria(ApplicationConstants.LAST_90_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerUserPerDay);
            } else {
                steerDataToday = steerAggregateDao.getSteerData(ispName, null, getTimeForCriteria(ApplicationConstants.LAST_DAY), steerAggregateDao.COLLECTION_statsSteeringPerIspPerHour);
                steerDataLast7Days = steerAggregateDao.getSteerData(ispName, null, getTimeForCriteria(ApplicationConstants.LAST_7_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerIspPerDay);
                steerDataLast30Days = steerAggregateDao.getSteerData(ispName, null, getTimeForCriteria(ApplicationConstants.LAST_30_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerIspPerDay);
                steerDataLast90Days = steerAggregateDao.getSteerData(ispName, null, getTimeForCriteria(ApplicationConstants.LAST_90_DAYS), steerAggregateDao.COLLECTION_statsSteeringPerIspPerDay);
            }
        }

        for(String event : eventTypes) {
            if(event.equals(ApplicationConstants.EVENT_STEER)) {
                steeringLogsCountList.add(genSteerObject(steerDataToday, ApplicationConstants.LAST_DAY_HOUR));
                steeringLogsCountList.add(genSteerObject(steerDataLast7Days, ApplicationConstants.LAST_7_DAYS_HOUR));
                steeringLogsCountList.add(genSteerObject(steerDataLast30Days, ApplicationConstants.LAST_30_DAYS_HOUR));
                steeringLogsCountList.add(genSteerObject(steerDataLast90Days, ApplicationConstants.LAST_90_DAYS_HOUR));
            }

            if(event.equals(ApplicationConstants.EVENT_ROAM)) {
                steeringLogsCountList.add(genRoamObject(steerDataToday, ApplicationConstants.LAST_DAY_HOUR));
                steeringLogsCountList.add(genRoamObject(steerDataLast7Days, ApplicationConstants.LAST_7_DAYS_HOUR));
                steeringLogsCountList.add(genRoamObject(steerDataLast30Days, ApplicationConstants.LAST_30_DAYS_HOUR));
                steeringLogsCountList.add(genRoamObject(steerDataLast90Days, ApplicationConstants.LAST_90_DAYS_HOUR));
            }
        }

        return steeringLogsCountList;
    }

    private ChannelSelectDistribution genChannelSelectObject(String radioKey, Integer duration, String equipmentType, Map<String, Integer> channelData) {

        ChannelSelectDistribution channelSelectDistribution = new ChannelSelectDistribution();
        channelSelectDistribution.setRadioKey(radioKey);
        channelSelectDistribution.setDuration(duration);
        channelSelectDistribution.setEquipmentType(equipmentType);
        channelSelectDistribution.setValues(channelData);

        return channelSelectDistribution;
    }

    public List<ChannelSelectDistribution> getChannelSelectDistribution(String scope, String scopeId, String radios, String equipmentType) {

        List<String> equipmentTypes = new ArrayList<String>(Arrays.asList(equipmentType.split(",")));
        List<String> radioKeys = new ArrayList<String>(Arrays.asList(radios.split(",")));

        List<ChannelSelectDistribution> channelSelectDistributionList = new ArrayList<>();

        String userId = null;
        String ispName = null;

        Map<String, Integer> channelDataToday = null;
        Map<String, Integer> channelDataLast7Days = null;
        Map<String, Integer> channelDataLast30Days = null;
        Map<String, Integer> channelDataLast90Days = null;

        if(scope.equals(ApplicationConstants.CLUSTER)) {
            if(!scopeId.equals(String.valueOf(ApplicationConstants.ZERO))) {
                String groupId = at3Adapter.getGroupIdByClusterId(scopeId);
                ispName = at3Adapter.getIspNameByGroupId(groupId);
            }
        } else if(scope.equals(ApplicationConstants.GROUP)) {
            ispName = at3Adapter.getIspNameByGroupId(scopeId);
        } else if(scope.equals(ApplicationConstants.ISP)) {
            ispName = at3Adapter.getIspNameById(scopeId);
        } else {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get Steering Logs Count Failed");
        }

        for(String equipment : equipmentTypes) {
            for(String radio : radioKeys) {
                Optional<RadioEnum> radioEnumOptional = RadioEnum.parseByRadioKey(radio);

                String eqp_type = null;
                if(equipment.equals("RG")) {
                    eqp_type = "RGW";
                } else {
                    eqp_type = equipment;
                }

                if(scopeId.equals(String.valueOf(ApplicationConstants.ZERO)) && userId == null) {
                    channelDataToday = statsAggregateDao.getChannelData(null, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerIspPerHour, statsAggregateDao.OPERATION_SUM);
                    channelDataLast7Days = statsAggregateDao.getChannelData(null, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_SUM);
                    channelDataLast30Days = statsAggregateDao.getChannelData(null, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_SUM);
                    channelDataLast90Days = statsAggregateDao.getChannelData(null, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_SUM);
                } else {
                    if (userId != null) {
                        channelDataToday = statsAggregateDao.getChannelData(ispName, userId, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerUserPerHour, statsAggregateDao.OPERATION_SUM);
                        channelDataLast7Days = statsAggregateDao.getChannelData(ispName, userId, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_SUM);
                        channelDataLast30Days = statsAggregateDao.getChannelData(ispName, userId, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_SUM);
                        channelDataLast90Days = statsAggregateDao.getChannelData(ispName, userId, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_SUM);
                    } else {
                        channelDataToday = statsAggregateDao.getChannelData(ispName, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerIspPerHour, statsAggregateDao.OPERATION_SUM);
                        channelDataLast7Days = statsAggregateDao.getChannelData(ispName, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_SUM);
                        channelDataLast30Days = statsAggregateDao.getChannelData(ispName, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_SUM);
                        channelDataLast90Days = statsAggregateDao.getChannelData(ispName, null, eqp_type, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_SUM);
                    }
                }

                channelSelectDistributionList.add(genChannelSelectObject(radio, ApplicationConstants.LAST_DAY_HOUR, equipment, channelDataToday));
                channelSelectDistributionList.add(genChannelSelectObject(radio, ApplicationConstants.LAST_7_DAYS_HOUR, equipment, channelDataLast7Days));
                channelSelectDistributionList.add(genChannelSelectObject(radio, ApplicationConstants.LAST_30_DAYS_HOUR, equipment, channelDataLast30Days));
                channelSelectDistributionList.add(genChannelSelectObject(radio, ApplicationConstants.LAST_90_DAYS_HOUR, equipment, channelDataLast90Days));
            }
        }

        return channelSelectDistributionList;
    }

    private AirTimeBusy genAirTimeBusyObject(String radioKey, Integer duration, Float occupancy) {

        AirTimeBusy airTimeBusy = new AirTimeBusy();
        airTimeBusy.setRadioKey(radioKey);
        airTimeBusy.setDuration(duration);
        airTimeBusy.setValue(occupancy);

        return airTimeBusy;
    }

    public List<AirTimeBusy> getAirTimeBusy(String scope, String scopeId, String radios) {

        List<String> radioKeys = new ArrayList<String>(Arrays.asList(radios.split(",")));

        List<AirTimeBusy> airTimeBusyList = new ArrayList<>();

        String userId = null;
        String ispName = null;

        Float airTimeBusyDataToday = 0.0f;
        Float airTimeBusyDataLast7Days = 0.0f;
        Float airTimeBusyDataLast30Days = 0.0f;
        Float airTimeBusyDataLast90Days = 0.0f;

        if(scope.equals(ApplicationConstants.CLUSTER)) {
            if(!scopeId.equals(String.valueOf(ApplicationConstants.ZERO))) {
                String groupId = at3Adapter.getGroupIdByClusterId(scopeId);
                ispName = at3Adapter.getIspNameByGroupId(groupId);
            }
        } else if(scope.equals(ApplicationConstants.GROUP)) {
            ispName = at3Adapter.getIspNameByGroupId(scopeId);
        } else if(scope.equals(ApplicationConstants.ISP)) {
            ispName = at3Adapter.getIspNameById(scopeId);
        } else {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get Steering Logs Count Failed");
        }

        for(String radio : radioKeys) {
            Optional<RadioEnum> radioEnumOptional = RadioEnum.parseByRadioKey(radio);

            if(scopeId.equals(String.valueOf(ApplicationConstants.ZERO)) && userId == null) {
                airTimeBusyDataToday = statsAggregateDao.getAirTimeBusyData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerIspPerHour, statsAggregateDao.OPERATION_AVG);
                airTimeBusyDataLast7Days = statsAggregateDao.getAirTimeBusyData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                airTimeBusyDataLast30Days = statsAggregateDao.getAirTimeBusyData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                airTimeBusyDataLast90Days = statsAggregateDao.getAirTimeBusyData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
            } else {
                if (userId != null) {
                    airTimeBusyDataToday = statsAggregateDao.getAirTimeBusyData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerUserPerHour, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDataLast7Days = statsAggregateDao.getAirTimeBusyData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDataLast30Days = statsAggregateDao.getAirTimeBusyData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDataLast90Days = statsAggregateDao.getAirTimeBusyData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_AVG);
                } else {
                    airTimeBusyDataToday = statsAggregateDao.getAirTimeBusyData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerIspPerHour, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDataLast7Days = statsAggregateDao.getAirTimeBusyData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDataLast30Days = statsAggregateDao.getAirTimeBusyData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDataLast90Days = statsAggregateDao.getAirTimeBusyData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                }
            }

            airTimeBusyList.add(genAirTimeBusyObject(radio, ApplicationConstants.LAST_DAY_HOUR, airTimeBusyDataToday));
            airTimeBusyList.add(genAirTimeBusyObject(radio, ApplicationConstants.LAST_7_DAYS_HOUR, airTimeBusyDataLast7Days));
            airTimeBusyList.add(genAirTimeBusyObject(radio, ApplicationConstants.LAST_30_DAYS_HOUR, airTimeBusyDataLast30Days));
            airTimeBusyList.add(genAirTimeBusyObject(radio, ApplicationConstants.LAST_90_DAYS_HOUR, airTimeBusyDataLast90Days));
        }

        return airTimeBusyList;
    }

    private AirTimeBusyDistribution genAirTimeBusyDistributionObject(String radioKey, Integer duration, Map<String, Float> airTimeDistributionData) {

        AirTimeBusyDistribution airTimeBusyDistribution = new AirTimeBusyDistribution();
        airTimeBusyDistribution.setRadioKey(radioKey);
        airTimeBusyDistribution.setDuration(duration);
        airTimeBusyDistribution.setValues(airTimeDistributionData);

        return airTimeBusyDistribution;
    }

    public List<AirTimeBusyDistribution> getAirTimeBusyDistribution(String scope, String scopeId, String radios) {

        List<String> radioKeys = new ArrayList<String>(Arrays.asList(radios.split(",")));

        List<AirTimeBusyDistribution> airTimeBusyDistributionList = new ArrayList<>();

        String userId = null;
        String ispName = null;

        Map<String, Float> airTimeBusyDistributionDataToday = null;
        Map<String, Float> airTimeBusyDistributionDataLast7Days = null;
        Map<String, Float> airTimeBusyDistributionDataLast30Days = null;
        Map<String, Float> airTimeBusyDistributionDataLast90Days = null;

        if(scope.equals(ApplicationConstants.CLUSTER)) {
            if(!scopeId.equals(String.valueOf(ApplicationConstants.ZERO))) {
                String groupId = at3Adapter.getGroupIdByClusterId(scopeId);
                ispName = at3Adapter.getIspNameByGroupId(groupId);
            }
        } else if(scope.equals(ApplicationConstants.GROUP)) {
            ispName = at3Adapter.getIspNameByGroupId(scopeId);
        } else if(scope.equals(ApplicationConstants.ISP)) {
            ispName = at3Adapter.getIspNameById(scopeId);
        } else {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get Steering Logs Count Failed");
        }

        for(String radio : radioKeys) {
            Optional<RadioEnum> radioEnumOptional = RadioEnum.parseByRadioKey(radio);

            if(scopeId.equals(String.valueOf(ApplicationConstants.ZERO)) && userId == null) {
                airTimeBusyDistributionDataToday = statsAggregateDao.getAirTimeBusyDistributionData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerIspPerHour, statsAggregateDao.OPERATION_AVG);
                airTimeBusyDistributionDataLast7Days = statsAggregateDao.getAirTimeBusyDistributionData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                airTimeBusyDistributionDataLast30Days = statsAggregateDao.getAirTimeBusyDistributionData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                airTimeBusyDistributionDataLast90Days = statsAggregateDao.getAirTimeBusyDistributionData(null, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
            } else {
                if (userId != null) {
                    airTimeBusyDistributionDataToday = statsAggregateDao.getAirTimeBusyDistributionData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerUserPerHour, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDistributionDataLast7Days = statsAggregateDao.getAirTimeBusyDistributionData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDistributionDataLast30Days = statsAggregateDao.getAirTimeBusyDistributionData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDistributionDataLast90Days = statsAggregateDao.getAirTimeBusyDistributionData(ispName, userId, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerUserPerDay, statsAggregateDao.OPERATION_AVG);
                } else {
                    airTimeBusyDistributionDataToday = statsAggregateDao.getAirTimeBusyDistributionData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_DAY), statsAggregateDao.COLLECTION_statsPerIspPerHour, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDistributionDataLast7Days = statsAggregateDao.getAirTimeBusyDistributionData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_7_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDistributionDataLast30Days = statsAggregateDao.getAirTimeBusyDistributionData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_30_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                    airTimeBusyDistributionDataLast90Days = statsAggregateDao.getAirTimeBusyDistributionData(ispName, null, radioEnumOptional.get(), getDateTimeForCriteria(ApplicationConstants.LAST_90_DAYS), statsAggregateDao.COLLECTION_statsPerIspPerDay, statsAggregateDao.OPERATION_AVG);
                }
            }

            airTimeBusyDistributionList.add(genAirTimeBusyDistributionObject(radio, ApplicationConstants.LAST_DAY_HOUR, airTimeBusyDistributionDataToday));
            airTimeBusyDistributionList.add(genAirTimeBusyDistributionObject(radio, ApplicationConstants.LAST_7_DAYS_HOUR, airTimeBusyDistributionDataLast7Days));
            airTimeBusyDistributionList.add(genAirTimeBusyDistributionObject(radio, ApplicationConstants.LAST_30_DAYS_HOUR, airTimeBusyDistributionDataLast30Days));
            airTimeBusyDistributionList.add(genAirTimeBusyDistributionObject(radio, ApplicationConstants.LAST_90_DAYS_HOUR, airTimeBusyDistributionDataLast90Days));
        }

        return airTimeBusyDistributionList;
    }
}
