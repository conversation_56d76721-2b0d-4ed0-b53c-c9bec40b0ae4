package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.NetworkTraffic;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.EXTENDER;

@Service
public class NetworkTrafficService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private At3Adapter at3Adapter;


    public List<NetworkTraffic> getNetworkTraffic(String userId, long startTime, long endTime, String type) {
        HashMap<String, HashMap<String, Object>> allDeviceSum = new HashMap<>();

        Instant now = Instant.now();
        long timeInMilli = now.toEpochMilli();
        long begin = (startTime != 0? startTime: (timeInMilli-3600000));
        long end = endTime != 0? endTime: timeInMilli;

        Instant instant = Instant.ofEpochMilli(begin);
        long dateInSecond = LocalDateTime.of(LocalDateTime.ofInstant(instant, ZoneId.systemDefault()).toLocalDate(), LocalTime.MIN).toEpochSecond(ZoneOffset.systemDefault().getRules().getOffset(now));

        String collection = DS_HOST_INSIGHTS;
        String txField = INTERNET_TX;
        String rxField = INTERNET_RX;
        if (!StringUtils.equals(type, "Internet")) {
            collection = USER_STATION_SLIDING_DATA;
            txField = "tx";
            rxField = "rx";
        }

        Set<String> extMacAddrSet = new HashSet<>();
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userId);
        query.put("type", EXTENDER);
        DBCursor cursor = at3Adapter.getMongoDbCollection(AP_DETAIL).find(query, new BasicDBObject().append("macAddress", 1));
        while (cursor.hasNext()) {
            DBObject dbObject = cursor.next();
            Optional.ofNullable(dbObject.get("macAddress")).ifPresent(p->extMacAddrSet.add(String.valueOf(p)));
        }

        query.clear();
        query.put("userId", userId);
        query.put("dateHour",  new BasicDBObject("$gte", dateInSecond).append("$lte", end/1000));
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);
        cursor = at3Adapter.getMongoDbCollection(collection).find(query, fieldsToRemove);

        while(cursor.hasNext()) {
            DBObject dbObject = cursor.next();
            String today = String.valueOf(dbObject.get("date"));
            Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
            if (Objects.nonNull(dataByDay)) {
                for (String key : dataByDay.keySet()) {
                    Map<String, Map<String, Object>> insightData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                    HashMap<String, Object> devMap = (HashMap<String, Object>) insightData.get("devMap");

                    for (Map.Entry<String, Object> entry : devMap.entrySet()) {
                        if (extMacAddrSet.contains(entry.getKey())) {
                            continue;
                        }

                        HashMap<String, Object> devTraffic = allDeviceSum.get(entry.getKey());
                        List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                        if (Objects.nonNull(dataList)) {
                            dataList = dataList.parallelStream().filter(element -> ((Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > begin) && (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) <= end).collect(Collectors.toList());

                            if (Objects.isNull(devTraffic)) {
                               devTraffic = new HashMap<>();
                               devTraffic.put("mac", entry.getKey());
                               devTraffic.put("tx", 0.0);
                               devTraffic.put("rx", 0.0);
                               allDeviceSum.put(entry.getKey(), devTraffic);
                            }
                            try {

                                String finalTxField = txField;
                                String finalRxField = rxField;
                                devTraffic.put("tx", (double)devTraffic.get("tx") + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(finalTxField)) ? p.get(finalTxField).toString() : "0.0")).sum());
                                devTraffic.put("rx", (double)devTraffic.get("rx") + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(finalRxField)) ? p.get(finalRxField).toString() : "0.0")).sum());
                            } catch (Exception e) {
                                logger.error("Get error.", e);
                            }
                        }
                    }
                }
            }
        }

        return allDeviceSum.values().parallelStream().map(p->new NetworkTraffic((String) p.get("mac"), new Double((Double) p.get("tx")).longValue(), new Double((Double) p.get("rx")).longValue())).sorted(Comparator.comparing(NetworkTraffic::getBytesReceived).reversed()).collect(Collectors.toList());
    }
}