package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.*;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.HostDto;
import com.actiontec.optim.mongodb.dto.StationSpeedTestDto;
import com.actiontec.optim.mongodb.dto.WifiStationDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.actiontec.optim.platform.api.v5.model.DeviceActionRequest;
import com.actiontec.optim.platform.api.v5.model.DeviceRequest;
import com.actiontec.optim.platform.api.v6.dto.ConnectionDTO;
import com.actiontec.optim.platform.api.v6.dto.DeviceDTO;
import com.actiontec.optim.platform.api.v6.dto.IPAddressDTO;
import com.actiontec.optim.platform.api.v6.dto.WirelessDTO;
import com.actiontec.optim.platform.mapper.StationSpeedTestMapper;
import com.actiontec.optim.platform.mapper.UserDeviceAddressMapper;
import com.actiontec.optim.platform.mapper.UserDeviceWirelessMapper;
import com.actiontec.optim.platform.model.*;
import com.actiontec.optim.platform.model.enums.*;
import com.actiontec.optim.service.CpeRpcService;
import com.actiontec.optim.util.MacOuiUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.EXECUTE_FAIL;

@Service
public class UserDeviceService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    public static final long DEVICE_VALID_MILLIS = 3 * 24 * 60 * 60 * 1000;

    @Value("${device.internetAccess.defaultBlockDurationInSec:900}")
    private long defaultBlockDuration = 900;

    @Autowired
    private CpeTopologyDao cpeTopologyDao;

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private HostDao hostDao;

    @Autowired
    private WifiStationDao wifiStationDao;

    @Autowired
    private DsHostDao dsHostDao;

    @Autowired
    private StationSpeedTestDao stationSpeedTestDao;

    @Autowired
    private UserDeviceAddressMapper userDeviceAddressMapper;

    @Autowired
    private UserDeviceWirelessMapper userDeviceWirelessMapper;

    @Autowired
    private StationSpeedTestMapper stationSpeedTestMapper;

    @Autowired
    private ModelSpecificService modelSpecificService;

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private HazelcastService hazelcastService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private CpeRpcService cpeRpcService;

    @Autowired
    private ObjectMapper objectMapper;

    public List<UserDevice> listByStn(String stn) {
        logger.debug("listByStn [{}]", stn);
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return listByUserId(userId);
    }

    public List<UserDevice> listByNetworkId(String networkId) throws Exception {
        logger.info("listByNetworkId [{}]", networkId);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(networkId);

        List<DeviceDTO> deviceDtos = cpeTopologyDao.getTopologyDevices(userEquipment.getRgwSerial(), userEquipment.getServiceTelephoneNo(), null);
        Map<String, HostDto> hostDtos = hostDao.listByNetworkId(networkId);
        Map<String, WifiStationDto> wifiStationDtos = wifiStationDao.listByNetworkId(networkId);
        Map<String, StationSpeedTestDto> stationSpeedTestMap = stationSpeedTestDao.listLatestByUserId(networkId);

        List<UserDevice> userDevices = buildUserDevices(deviceDtos, hostDtos, wifiStationDtos, stationSpeedTestMap);

        logger.info("listByNetworkId networkId:[{}] size:[{}]", networkId, userDevices.size());
        return userDevices;
    }

    public Optional<UserDevice> findByNetworkIdAndDeviceMac(String networkId, String deviceMac) throws Exception {
        logger.info("findByNetworkIdAndDeviceMac [{}]", networkId);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(networkId);

        List<DeviceDTO> deviceDtos = cpeTopologyDao.getTopologyDevices(userEquipment.getRgwSerial(), userEquipment.getServiceTelephoneNo(), deviceMac);
        if (deviceDtos != null && !deviceDtos.isEmpty()) {
            Map<String, HostDto> hostDtos = hostDao.findByNetworkIdAndDeviceMac(networkId, deviceMac);
            Map<String, WifiStationDto> wifiStationDtos = wifiStationDao.listByNetworkId(networkId);
            Map<String, StationSpeedTestDto> stationSpeedTestMap = stationSpeedTestDao.listLatestByUserId(networkId);

            List<UserDevice> userDevices = buildUserDevices(deviceDtos, hostDtos, wifiStationDtos, stationSpeedTestMap);

            logger.info("findByNetworkIdAndDeviceMac networkId:[{}] size:[{}]", networkId, userDevices.size());

            if (!userDevices.isEmpty()) {
                return Optional.of(userDevices.get(0));
            }
        }

        return Optional.empty();
    }

    public List<UserDevice> listByUserId(String userId) {
        logger.debug("listByUserId [{}]", userId);
        long currentMillis = System.currentTimeMillis();
        long onlineBaseMillis = currentMillis - at3Adapter.getEquipmentOfflineMins() * 60000;
        long validBaseMillis = currentMillis - DEVICE_VALID_MILLIS;

        // 1. list ApDetail
        List<ApDetailDto> apDetailDtos = apDetailDao.listByUserId(userId);
        ApDetailDto rgwApDetailDto = pickGateway(apDetailDtos);
        Map<String, ApDetailDto> serialToApDetailMap = getSerialToApDetailMap(apDetailDtos);

        // 2. dsHostList
        String serial = manageCommonService.getGatewaySerialByUserId(userId).orElseGet(()->userId);
        List<HostDto> hostDtos = hostDao.listByUserIdAndSerial(userId, serial, validBaseMillis);
        hostDtos = excludeExtenderWifiMac(hostDtos);

        // 3. wifi
        List<WifiStationDto> wifiStationDtos = wifiStationDao.listByUserId(userId, validBaseMillis);
        wifiStationDtos = excludeDuplicatedStation(wifiStationDtos); // not necessary, etl update wifi station by userId + mac

        // 4. moca
        // XXX: only fetch RG's mocaStaList
        List<ApDetailDto.MocaDeviceDto> mocaDeviceDtos = rgwApDetailDto.getMocaDevices();

        // speedTest
        Map<String, StationSpeedTestDto> stationSpeedTestMap = stationSpeedTestDao.listLatestByUserId(userId);

        List<UserDevice> userDevices = buildUserDevices(rgwApDetailDto, serialToApDetailMap, hostDtos, wifiStationDtos, mocaDeviceDtos, stationSpeedTestMap, onlineBaseMillis);

        logger.debug("listByUserId userId:[{}] size:[{}]", userId, userDevices.size());
        return userDevices;
    }

    public Optional<UserDevice> findByStnAndDeviceMac(String stn, String deviceMac) {
        logger.info("listByStnAndDeviceMac stn:[{}] deviceMac:[{}]", stn, deviceMac);
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return findByUserIdAndDeviceMac(userId, deviceMac);
    }

    public Optional<UserDevice> findByUserIdAndDeviceMac(String userId, String deviceMac) {
        long currentMillis = System.currentTimeMillis();
        long onlineBaseMillis = currentMillis - at3Adapter.getEquipmentOfflineMins() * 60000;
        long validBaseMillis = currentMillis - DEVICE_VALID_MILLIS;

        // 1. list ApDetail
        List<ApDetailDto> apDetailDtos = apDetailDao.listByUserId(userId);
        ApDetailDto rgwApDetailDto = pickGateway(apDetailDtos);
        Map<String, ApDetailDto> serialToApDetailMap = getSerialToApDetailMap(apDetailDtos);

        ApDetailDto extenderDetalDto = apDetailDtos.stream().filter(item-> {
            return StringUtils.equals(item.getMacAddress(),deviceMac) && StringUtils.equals(item.getType(), EXTENDER);
        }).findFirst().orElse(null);

        String queryMac = extenderDetalDto != null && Objects.nonNull(extenderDetalDto.getBaseMacAddress())? extenderDetalDto.getBaseMacAddress(): deviceMac;
        // 2.
        String serial = manageCommonService.getGatewaySerialByUserId(userId).orElseGet(()->userId);
        // OC-5662: check baseMacAddress is always lower case.
        Optional<HostDto> optionalHostDto = hostDao.findByUserIdAndSerialAndDeviceMac(userId, serial, StringUtils.lowerCase(queryMac), validBaseMillis);
        if (extenderDetalDto != null && !optionalHostDto.isPresent()) {
            optionalHostDto = hostDao.findByUserIdAndSerialAndDeviceMac(userId, serial, StringUtils.lowerCase(deviceMac), validBaseMillis);
        }

        Optional<UserDevice> optionalUserDevice = optionalHostDto.map(hostDto -> {

            List<WifiStationDto> wifiStationDtos = wifiStationDao.listByUserId(userId, validBaseMillis);
            wifiStationDtos = excludeDuplicatedStation(wifiStationDtos); // not necessary, etl update wifi station by userId + mac

            // XXX: only fetch RG's mocaStaList
            List<ApDetailDto.MocaDeviceDto> mocaDeviceDtos = rgwApDetailDto.getMocaDevices();

            // speedTest
            Map<String, StationSpeedTestDto> stationSpeedTestMap = stationSpeedTestDao.listLatestByUserId(userId);

            UserDevice userDevice = buildUserDevice(rgwApDetailDto, serialToApDetailMap, hostDto, wifiStationDtos, mocaDeviceDtos, stationSpeedTestMap, onlineBaseMillis);
            return userDevice;
        });

        return optionalUserDevice;
    }

    private ApDetailDto pickGateway(List<ApDetailDto> apDetailDtos) {
        ApDetailDto gateway = null;
        for (ApDetailDto apDetailDto : apDetailDtos) {
            if (StringUtils.equals(apDetailDto.getType(), EquipmentType.GATEWAY.name())) {
                if (gateway == null) {
                    gateway = apDetailDto;
                } else {
                    // multiple gateways, log error, throw exception
                }
            }
        }

        if (gateway == null) {
            // no gateway, log error, throw exception
        }
        return gateway;
    }

    private Map<String, ApDetailDto> getSerialToApDetailMap(List<ApDetailDto> apDetailDtos) {
        HashMap<String, ApDetailDto> apDetailMap = new HashMap<>();
        for (ApDetailDto apDetailDto : apDetailDtos) {
            if (apDetailDto.getSendingTime() != 0
                    && StringUtils.isNotBlank(apDetailDto.getSerialNumber())
                    && StringUtils.isNotBlank(apDetailDto.getMacAddress())
                //&& StringUtils.isNotBlank(apDetailDto.getBaseMacAddress())
            ) {
                apDetailMap.put(apDetailDto.getSerialNumber(), apDetailDto);
            }
        }
        return apDetailMap;
    }

    private List<HostDto> excludeExtenderWifiMac(List<HostDto> hostDtos) {
        List<HostDto> result = new ArrayList<>();
        List<HostDto> extenders = new ArrayList<>();

        for (HostDto hostDto : hostDtos) {
            if (StringUtils.equals(hostDto.getDeviceType(), UserDeviceType.Extender.name())) {
                extenders.add(hostDto);
            }
        }

        for (HostDto hostDto : hostDtos) {
            boolean isExtenderWifiMac = false;
            if (!StringUtils.equals(hostDto.getDeviceType(), UserDeviceType.Extender.name())) {
                for (HostDto extender : extenders) {
                    if (modelSpecificService.isSameEquipment(extender.getMacAddress(), hostDto.getMacAddress())) {
                        isExtenderWifiMac = true;
                        break;
                    }   
                }
            }
            if (!isExtenderWifiMac) {
                result.add(hostDto);
            }
        }

        return result;
    }

    private List<WifiStationDto> excludeDuplicatedStation(List<WifiStationDto> wifiStationDtos) {
        // newer first
        wifiStationDtos.sort((w1, w2) -> new Long(w2.getNtTimestamp()).compareTo(w1.getNtTimestamp()));

        HashSet<String> deviceMacs = new HashSet<>();
        ArrayList<WifiStationDto> result = new ArrayList<>();
        for (WifiStationDto wifiStationDto : wifiStationDtos) {
            if (!deviceMacs.contains(wifiStationDto.getMacAddress())) {
                deviceMacs.add(wifiStationDto.getMacAddress());
                result.add(wifiStationDto);
            }
        }
        return result;
    }

    private List<MarkedAttributes> getMarkedAttributes(String mac) {
        List<MarkedAttributes> markedAttributes = new ArrayList<>();

        if (StringUtils.isBlank(mac))
            return markedAttributes;

        if ((Integer.parseInt(mac.split(":")[0], 16) & 0x2 ) != 0) {
            markedAttributes.add(new MarkedAttributes("MAC", "Virtual MAC", "Warning"));
        }
        return markedAttributes;
    }

    private List<UserDevice> buildUserDevices(List<DeviceDTO> deviceDtos, Map<String, HostDto> hostDtoMap,
                                              Map<String, WifiStationDto> wifiStationDtoMap, Map<String, StationSpeedTestDto> stationSpeedTestMap) {
        ArrayList<UserDevice> userDevices = new ArrayList<>();
        for (DeviceDTO deviceDto : deviceDtos) {
            UserDevice userDevice = buildUserDevice(deviceDto, hostDtoMap, wifiStationDtoMap, stationSpeedTestMap);
            if (userDevice != null) userDevices.add(userDevice);
        }
        return userDevices;
    }

    private String getStationMac(HostDto hostDto, String mac) {
        if (hostDto == null) {
            return mac;
        }
        return hostDto.getIsExtender() && StringUtils.isNotBlank(hostDto.getL2MacAddress())
                ? hostDto.getL2MacAddress()
                : mac;
    }

    private Optional<IPAddressDTO> selectBestIpv4(List<IPAddressDTO> ipAddresses) {
        if (ipAddresses == null || ipAddresses.isEmpty()) {
            return Optional.empty();
        }

        Comparator<String> statePriority = Comparator.comparingInt(state -> {
            switch (state) {
                case "Infinite": return 0;
                case "Valid":    return 1;
                case "Unknown":  return 2;
                default:         return 3;
            }
        });

        return ipAddresses.stream()
                .filter(ip -> ip.getVersion() == 4)
                .min(Comparator.comparing(
                        IPAddressDTO::getLifetimeState,
                        statePriority
                ));
    }

    private UserDevice buildUserDevice(DeviceDTO deviceDto,
                                       Map<String, HostDto> hostDtoMap,
                                       Map<String, WifiStationDto> wifiStationDtoMap,
                                       Map<String, StationSpeedTestDto> stationSpeedTestMap) {

        if (deviceDto.getConnections().isEmpty()) return null;

        final String mac = deviceDto.getMacAddress();
        final HostDto hostDto = hostDtoMap.get(mac);
        final WifiStationDto wifiStationDto = wifiStationDtoMap.get(getStationMac(hostDto, mac));
        final ConnectionDTO connectionDto = deviceDto.getConnections().get(0);
        final WirelessDTO wirelessDto = connectionDto.getWireless();

        UserDevice userDevice = new UserDevice();
        userDevice.setMacAddress(mac);
        userDevice.setMarkedAttributes(getMarkedAttributes(mac));
        userDevice.setHostName(deviceDto.getHostname());
        userDevice.setStatus(ConnectionStatus.valueOf(deviceDto.getStatus()));
        userDevice.setDeviceType(
                CpeDeviceType.Extender.name().equals(deviceDto.getType()) ?
                        UserDeviceType.Extender :
                        UserDeviceType.Other
        );
        String vendor = getVendorName(mac);
        userDevice.setVendor(vendor != null ? vendor : hostDto.getAeiDto().getVendor());

        String MARK_EXTENDER = CpeDeviceType.Extender.name().equals(deviceDto.getType()) ? "(EXT)" : "";

        // IP Addresses
        List<UserDeviceAddress> userDeviceAddresses = Optional.ofNullable(hostDto)
            .map(h -> userDeviceAddressMapper.toUserDeviceAddresses(
                h.getAddresses().stream()
                    .filter(address -> "IPv6".equals(address.getVersion()))
                    .collect(Collectors.toList())
            ))
            .orElse(new ArrayList<>());

        Optional<IPAddressDTO> bestIp = selectBestIpv4(deviceDto.getIpAddresses());
        UserDeviceAddress addr = new UserDeviceAddress();

        if (bestIp.isPresent()) {
            IPAddressDTO ip = bestIp.get();
            addr.setAddress(ip.getAddress());
            addr.setVersion(ip.getVersion() == 4 ? "IPv4" : "IPv6");
            addr.setType(ip.getType() == null ? "" : ip.getType());
        } else {
            addr.setAddress(hostDto != null ? hostDto.getIp() : "0.0.0.0");
            addr.setVersion("IPv4");
            addr.setType("");
        }
        userDeviceAddresses.add(addr);
//        List<UserDeviceAddress> userDeviceAddresses = deviceDto.getIpAddresses().stream()
//                .map(ip -> {
//                    UserDeviceAddress addr = new UserDeviceAddress();
//                    addr.setAddress(ip.getAddress());
//                    addr.setVersion(ip.getVersion() == 4 ? "IPv4" : "IPv6");
//                    addr.setType(ip.getType() == null ? "" : ip.getType());
//                    return addr;
//                })
//                .collect(Collectors.toList());
        userDevice.setAddresses(userDeviceAddresses);

        // Internet block info
        if (hostDto != null) {
            userDevice.setInternetAccessBlocked(
                    hostDto.getNtTimestamp() > hostDto.getRpcTimestamp() ?
                            hostDto.isInternetAccessBlocked() : hostDto.isRpcBlocked());
            userDevice.setInternetAccessBlockStartTime(hostDto.getInternetAccessBlockStartTime() * 1000L);
            userDevice.setInternetAccessBlockDuration(hostDto.getInternetAccessBlockDuration());
        } else {
            userDevice.setInternetAccessBlocked(false);
            userDevice.setInternetAccessBlockStartTime(0);
            userDevice.setInternetAccessBlockDuration(0);
        }

        // Set PHY Type and type-specific info
        String connType = connectionDto.getType();
        if (CpeConnectionType.Wireless.name().equals(connType)) {
            userDevice.setPhyType(UserDevicePhyType.Wifi);
            userDevice.setWanMacAddress(mac);

            UserDeviceWireless wireless;
            if (wifiStationDto != null) {
                wireless = userDeviceWirelessMapper.toUserDeviceWireless(wifiStationDto);
                if (deviceDto.getConnections() != null) {
                    for (ConnectionDTO connectionDTO : deviceDto.getConnections()) {
                        if (CpeConnectionType.Wireless.name().equals(connectionDTO.getType())) {
                            WirelessDTO wirelessConn = connectionDTO.getWireless();
                            if (wirelessConn != null && wirelessConn.getRadio().equals(wireless.getRadioKey())) {
                                wireless.setSsid(wirelessConn.getSsid());
                                wireless.setRssi(wirelessConn.getRssi());
//                                wireless.setLastDataUplinkRate(wirelessConn.getUplinkPhyRate() / 1000L);
//                                wireless.setLastDataDownlinkRate(wirelessConn.getDownlinkPhyRate() / 1000L);
                                break;
                            }
                        }
                    }
                }
            } else {
                wireless = new UserDeviceWireless();
                wireless.setMacAddress(deviceDto.getMacAddress());
                wireless.setOperatingStandards("");
                wireless.setSsidKey("");
                wireless.setRadioKey(wirelessDto.getRadio());
                wireless.setSsid(wirelessDto.getSsid());
                wireless.setRssi(wirelessDto.getRssi());
                wireless.setLastDataUplinkRate(wirelessDto.getUplinkPhyRate() / 1000L);
                wireless.setLastDataDownlinkRate(wirelessDto.getDownlinkPhyRate() / 1000L);
            }
            // set serial number (upstream)
            String upstreamSerial = apDetailDao.getSerialByMacAddress(deviceDto.getUpstreamId());
            wireless.setSerialNumber(upstreamSerial);

            userDevice.setUserDeviceWireless(wireless);
            userDevice.setFriendlyName(
                (wifiStationDto != null && wifiStationDto.getFriendlyName() != null)
                    ? wifiStationDto.getFriendlyName()
                    : deviceDto.getHostname() + MARK_EXTENDER
            );

            StationSpeedTestDto testDto = stationSpeedTestMap.get(mac);
            if (testDto != null) {
                userDevice.setStationSpeedTest(stationSpeedTestMapper.toStationSpeedTest(testDto));
            }
        } else if (CpeConnectionType.Ethernet.name().equals(connType)) {
            userDevice.setPhyType(UserDevicePhyType.Ethernet);
            userDevice.setWanMacAddress(mac);
            long byteRate = connectionDto.getEthernet().getBitRate() / 1000;
            userDevice.setDownlinkPhyRate(byteRate);
            userDevice.setUplinkPhyRate(byteRate);
            if (hostDto != null) {
                userDevice.setFriendlyName(
                    (hostDto.getFriendlyName() != null)
                        ? hostDto.getFriendlyName()
                        : deviceDto.getHostname() + MARK_EXTENDER
                );
                userDevice.setUserDeviceEthernet(dsHostDao.fetchInternetUsage(hostDto.getUserId(), mac));
            }
        }

        userDevice.setLastReportTime(deviceDto.getLastChangeTime());
        return userDevice;
    }

    private List<UserDevice> buildUserDevices(ApDetailDto rgwApDetailDto, Map<String, ApDetailDto> serialToApDetailDtoMap,
                                              List<HostDto> hostDtos, List<WifiStationDto> wifiStationDtos, List<ApDetailDto.MocaDeviceDto> mocaDeviceDtos,
                                              Map<String, StationSpeedTestDto> stationSpeedTestMap, long onlineBaseMillis) {
        ArrayList<UserDevice> userDevices = new ArrayList<>();
        for (HostDto hostDto : hostDtos) {
            UserDevice userDevice = buildUserDevice(rgwApDetailDto, serialToApDetailDtoMap, hostDto, wifiStationDtos, mocaDeviceDtos, stationSpeedTestMap, onlineBaseMillis);
            userDevices.add(userDevice);
        }
        return userDevices;
    }

    private UserDevice buildUserDevice(ApDetailDto rgwApDetailDto, Map<String, ApDetailDto> serialToApDetailDtoMap,
                                       HostDto hostDto, List<WifiStationDto> wifiStationDtos, List<ApDetailDto.MocaDeviceDto> mocaDeviceDtos,
                                       Map<String, StationSpeedTestDto> stationSpeedTestMap, long onlineBaseMillis) {
        UserDevice userDevice = new UserDevice();
        userDevice.setMacAddress(hostDto.getMacAddress());
        userDevice.setMarkedAttributes(getMarkedAttributes(hostDto.getMacAddress()));
        userDevice.setHostName(hostDto.getHostName());
        boolean hostOnline;
        if (hostDto.getNtTimestamp() >= onlineBaseMillis) {
            hostOnline = true;
            userDevice.setStatus(ConnectionStatus.Up);
        } else {
            hostOnline = false;
            userDevice.setStatus(ConnectionStatus.Down);
        }
        UserDeviceType.fromName(hostDto.getDeviceType()).ifPresent(userDevice::setDeviceType);
        UserDeviceType.getCustomType(Optional.ofNullable(userDevice.getDeviceType()), hostDto.getCustomType()).ifPresent(userDevice::setDeviceType);
        userDevice.setVendor(getVendorName(hostDto.getMacAddress()));

        List<UserDeviceAddress> userDeviceAddresses = userDeviceAddressMapper.toUserDeviceAddresses(hostDto.getAddresses());
        if (userDeviceAddresses == null) {
            userDeviceAddresses = new ArrayList<>();
        }
        userDeviceAddresses = addIpv4ToUserDeviceAddresses(userDeviceAddresses, hostDto.getIp());
        userDevice.setAddresses(userDeviceAddresses);

        userDevice.setInternetAccessBlocked(hostDto.isInternetAccessBlocked());
        userDevice.setInternetAccessBlockStartTime(hostDto.getInternetAccessBlockStartTime()*1000L);
        userDevice.setInternetAccessBlockDuration(hostDto.getInternetAccessBlockDuration());

        userDevice.setLastReportTime(hostDto.getNtTimestamp());

        // pick wifi station
        WifiStationDto wifiStationDto = null;
        for (WifiStationDto tmpWifiStationDto : wifiStationDtos) {
            boolean online = (tmpWifiStationDto.getNtTimestamp() >= onlineBaseMillis);
            if (hostOnline == online) {
                if (UserDeviceType.Extender == userDevice.getDeviceType()) {
                    if (modelSpecificService.isSameEquipment(hostDto.getMacAddress(), tmpWifiStationDto.getMacAddress())) {
                        wifiStationDto = tmpWifiStationDto;
                        break;
                    }
                } else {
                    if (StringUtils.equals(hostDto.getMacAddress(), tmpWifiStationDto.getMacAddress())) {
                        wifiStationDto = tmpWifiStationDto;
                        break;
                    }
                }
            }
        }

        // pick moca device
        ApDetailDto.MocaDeviceDto mocaDeviceDto = null;
        for (ApDetailDto.MocaDeviceDto tmpMocaDeviceDto : mocaDeviceDtos) {
            if (UserDeviceType.Extender == userDevice.getDeviceType()) {
                if (modelSpecificService.isSameEquipment(hostDto.getMacAddress(), tmpMocaDeviceDto.getMacAddress())) {
                    mocaDeviceDto = tmpMocaDeviceDto;
                    break;
                }
            } else {
                if (StringUtils.equals(hostDto.getMacAddress(), tmpMocaDeviceDto.getMacAddress())) {
                    mocaDeviceDto = tmpMocaDeviceDto;
                    break;
                }
            }
        }

        // order matters, in current implementation,
        // moca devices will only contains latest devices, no historical devices
        if (mocaDeviceDto != null) {
            userDevice.setPhyType(UserDevicePhyType.Moca);
            userDevice.setWanMacAddress(mocaDeviceDto.getMacAddress());
            Optional.ofNullable(hostDto.getFriendlyName()).ifPresent(userDevice::setFriendlyName);
        } else if (wifiStationDto != null) {
            userDevice.setPhyType(UserDevicePhyType.Wifi);
            userDevice.setWanMacAddress(wifiStationDto.getMacAddress());

            UserDeviceWireless userDeviceWireless = userDeviceWirelessMapper.toUserDeviceWireless(wifiStationDto);

            // fill channel and ssid
            ApDetailDto wifiHostApDetailDto = serialToApDetailDtoMap.get(wifiStationDto.getSerialNumber());
            if(wifiHostApDetailDto != null) {
                for (ApDetailDto.SsidDto ssidDto : wifiHostApDetailDto.getSsids()) {
                    if (StringUtils.equals(wifiStationDto.getBssid(), ssidDto.getBssid())) {
                        userDeviceWireless.setSsid(ssidDto.getSsid());
                    }
                }
                for (ApDetailDto.RadioDto radioDto : wifiHostApDetailDto.getWifiRadios()) {
                    if (StringUtils.equals(wifiStationDto.getRadioKey(), radioDto.getRadioKey().getRadioKey())) {
                        userDeviceWireless.setChannel(radioDto.getChannel());
                    }
                }
            } else {
                logger.warn("Couldn't get WiFi data of " + wifiStationDto.getSerialNumber());
            }

            userDevice.setUserDeviceWireless(userDeviceWireless);
            userDevice.setFriendlyName(wifiStationDto.getFriendlyName());

            if (stationSpeedTestMap.get(wifiStationDto.getMacAddress()) != null) {
                userDevice.setStationSpeedTest(stationSpeedTestMapper.toStationSpeedTest(stationSpeedTestMap.get(wifiStationDto.getMacAddress())));
            }
        } else {
            userDevice.setPhyType(UserDevicePhyType.Ethernet);
            userDevice.setWanMacAddress(userDevice.getMacAddress());
            Optional.ofNullable(hostDto.getFriendlyName()).ifPresent(userDevice::setFriendlyName);

            UserDeviceEthernet userDeviceEthernet = dsHostDao.fetchInternetUsage(hostDto.getUserId(), hostDto.getMacAddress());
            userDevice.setUserDeviceEthernet(userDeviceEthernet);
        }

        if (UserDeviceType.Extender == userDevice.getDeviceType())
            userDevice.setFriendlyName(manageCommonService.getDisplayNameByUserIdMacAddr(rgwApDetailDto.getUserId(), userDevice.getMacAddress()));

        if (StringUtils.isBlank(userDevice.getFriendlyName())) {
            userDevice.setFriendlyName(getDeviceName(hostDto));
        }

        return userDevice;
    }

    public String getDeviceName(HostDto hostDto) {
        String name = null;
        String part1 = "";
        String part2 = "";

        if (!StringUtils.isBlank(hostDto.getHostName())) {
            name = hostDto.getHostName();
        } else {

            if (Objects.nonNull(hostDto.getAeiDto())) {
                if (!StringUtils.isBlank(hostDto.getAeiDto().getVendor())) {
                    if (!StringUtils.containsIgnoreCase(hostDto.getAeiDto().getVendor(), "Apple")) {
                        part1 = hostDto.getAeiDto().getVendor();
                    }
                } else if (!StringUtils.isBlank(hostDto.getAeiDto().getOs())) {
                    part1 = hostDto.getAeiDto().getOs();
                }

                if (!StringUtils.isBlank(hostDto.getAeiDto().getModel())) {
                    part2 = hostDto.getAeiDto().getModel();
                } else if (!StringUtils.isBlank(hostDto.getAeiDto().getType())) {
                    part2 = hostDto.getAeiDto().getType();
                }
            }

            name = String.format("%s %s", part1, part2);
        }

        if (StringUtils.isBlank(name)) {
            if (!StringUtils.isBlank(hostDto.getIp())) {
                name = hostDto.getIp();
            } else {
                name = getVendorName(hostDto.getMacAddress());
            }
        }

        return name;
    }

    private String getVendorName(String key) {
        String vendorName = null;
        String maskedKey = MacOuiUtil.maskLocallyAdministeredBit(key.trim());
        String[] macIdOctetArray = maskedKey.split(COLON);
        if (macIdOctetArray.length >= 3) {
            String macOctet = (macIdOctetArray[0] + COLON + macIdOctetArray[1] + COLON + macIdOctetArray[2]).toLowerCase();
            Object vName = hazelcastService.read(macOctet.toLowerCase(), MAC_VENDOR_DETAIL);
            if (Objects.nonNull(vName)) {
                vendorName = String.valueOf(vName);
            } else {
                BasicDBObject query = new BasicDBObject();
                query.put("macIdOctet", macOctet);

                BasicDBObject sort = new BasicDBObject();
                sort.put("date", -1);

                DBObject dbObject = at3Adapter.getMongoDbCollection("macIdParent").findOne(query, new BasicDBObject(), sort);
                if (Objects.nonNull(dbObject)) {
                    vendorName = String.valueOf(dbObject.get("vendor"));
                }
            }
        }
        return vendorName;
    }

    private List<UserDeviceAddress> addIpv4ToUserDeviceAddresses(List<UserDeviceAddress> userDeviceAddresses, String ipv4Address) {
        boolean ipv4Included = false;
        for (UserDeviceAddress userDeviceAddress : userDeviceAddresses) {
            if (StringUtils.equals("IPv4", userDeviceAddress.getVersion())) {
                ipv4Included = true;
                break;
            }
        }

        if (!ipv4Included) {
            UserDeviceAddress ipv4UserDeviceAddress = new UserDeviceAddress();
            ipv4UserDeviceAddress.setAddress(ipv4Address);
            ipv4UserDeviceAddress.setVersion("IPv4");
            ipv4UserDeviceAddress.setType("");
            userDeviceAddresses.add(ipv4UserDeviceAddress);
        }
        return userDeviceAddresses;
    }

    private DBObject getDeviceDetails(Equipment userEquipment, String macAddr) throws Exception {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);

        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        if (Objects.isNull(deviceDetails))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device not found with macAddress :: " + macAddr);

        return deviceDetails;
    }

    public void put(String stn, String deviceId, DeviceRequest request) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        manageCommonService.subscriberDataExistInMongo(userEquipment);

        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        BasicDBObject updateFields = new BasicDBObject();
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("macAddress", deviceId);

        DBObject stationObj = at3Adapter.getMongoDbCollection(STATION_DETAIL).findOne(query,  new BasicDBObject().append("_id", ZERO));

        if (Objects.nonNull(request.getDeviceType())) {
            Optional<UserDeviceType> userDeviceType = UserDeviceType.fromName(request.getDeviceType());
            if (!userDeviceType.isPresent() || userDeviceType.get().ordinal() < UserDeviceType.Other.ordinal()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Type");
            } else {
                updateFields.put(HostDto.Fields.customType.name(), userDeviceType.get().name());
                if (Objects.isNull(stationObj) && Objects.nonNull(request.getName()))
                    updateFields.put("friendlyName", request.getName());

                basicDBObjectUpdate.put("$set", updateFields);
                mongoService.update(query, basicDBObjectUpdate, true, false, HOSTNAME_DETAIL);
            }
        }

        if (Objects.nonNull(stationObj) && Objects.nonNull(request.getName())) {
            updateFields.clear();
            basicDBObjectUpdate.clear();

            updateFields.put("friendlyName", request.getName());
            basicDBObjectUpdate.put("$set", updateFields);
            mongoService.update(query, basicDBObjectUpdate, true, false, STATION_DETAIL);
        }
    }

    @PermissionHandle(checkSubscriber=false)
    public Object postActions(String stn, String id, DeviceActionRequest request) throws Exception {
        String deviceId = id;
        String action = request.getAction();
        String ipAddr = "";
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);

        long validBaseMillis = System.currentTimeMillis() - DEVICE_VALID_MILLIS;
        Optional<HostDto> host = hostDao.findByUserIdAndSerialAndDeviceMac(
                userEquipment.getRgwSerial(),
                userEquipment.getServiceTelephoneNo(),
                id,
                validBaseMillis
        );
        if (host.isPresent() && host.get().getIsExtender() && host.get().getL2MacAddress() != null) {
            deviceId = host.get().getL2MacAddress();
        }
        ipAddr = host.isPresent() ? host.get().getIp() : "";

        String rpcUri;
        HashMap<String, Object> payloadMap = new HashMap<>();
        String userId = userEquipment.getRgwSerial();
        BasicDBObject query = new BasicDBObject();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("_id", 0);
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));
        DBObject deviceDetailData = getDeviceDetails(userEquipment, deviceId);

        if (StringUtils.equals("SpeedTest", action)) {
            String band = String.valueOf(deviceDetailData.get("band"));
            rpcUri = String.format(RpcConstants.DEVICE_SPEEDTEST_URI, deviceId);
            Date sendRpcTime = new Date();
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userEquipment.getRgwSerial(), String.valueOf(deviceDetailData.get("serialNumber")), rpcUri, "GET", "\"\"", maxTries, THREAD_TO_SLEEP);

            BasicDBObject stationSpeedTest = new BasicDBObject();
            Map<String, Object> speedTestData = new HashMap<>();
            speedTestData.put("userId", userId);
            speedTestData.put("band", band);
            speedTestData.put("ipAddr", ipAddr);
            speedTestData.put("macAddr", deviceId);
            speedTestData.put("date", sendRpcTime);

            stationSpeedTest.put("_id", CommonUtils.generateUUID());
            stationSpeedTest.put("userId", userId);
            stationSpeedTest.put("band", band);
            stationSpeedTest.put("ipAddr", ipAddr);
            stationSpeedTest.put("macAddr", id);
            stationSpeedTest.put("date", sendRpcTime);

            if (rpcResult.get("payload") != null) {
                List<Object> respPayloadList = (List) rpcResult.get("payload");
                Map<String, Object> respPayloadMap = (Map) respPayloadList.get(0);
                Long txRate = Long.valueOf(respPayloadMap.get("tx").toString());
                Long rxRate = Long.valueOf(respPayloadMap.get("rx").toString());
                Long dataRate = (txRate+rxRate) * 3/10;

                speedTestData.put("txRate", txRate);
                speedTestData.put("rxRate", rxRate);
                speedTestData.put("dataRate", dataRate);
                speedTestData.put("result", EXECUTE_OK);

                stationSpeedTest.put("rxRate", rxRate);
                stationSpeedTest.put("txRate", txRate);
                stationSpeedTest.put("dataRate", dataRate);
                stationSpeedTest.put("result", EXECUTE_OK);
            } else {
                speedTestData.put("txRate", 0);
                speedTestData.put("rxRate", 0);
                speedTestData.put("dataRate", 0);
                speedTestData.put("result", EXECUTE_FAIL);

                stationSpeedTest.put("rxRate", 0);
                stationSpeedTest.put("txRate", 0);
                stationSpeedTest.put("dataRate", 0);
                stationSpeedTest.put("result", EXECUTE_FAIL);
            }
            mongoService.create(STATION_SPEED_TEST_INFO, stationSpeedTest);
            return speedTestData;
        } else if (StringUtils.equals("InternetAccess", action)) {
            rpcUri = String.format(RpcConstants.DEVICE_INTERETACCESS_ACTION_URI, deviceId);
            String accessAction = request.getInternetAccessAction().toUpperCase();
            payloadMap.put("action", accessAction);
            if (Objects.nonNull(request.getStartTime()) && 0 != request.getStartTime())
                payloadMap.put("blockStartTime", request.getStartTime());
            if (request.getDuration() > 0) {
                payloadMap.put("blockDuration", request.getDuration());
            } else if (StringUtils.equals(accessAction, "BLOCK")) {
                logger.warn("no block duration");
                payloadMap.put("blockDuration", defaultBlockDuration);
            }

            String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);
            String payload = objectMapper.writeValueAsString(payloadMap);
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userEquipment.getRgwSerial(), serial, rpcUri, "POST", payload, maxTries, THREAD_TO_SLEEP);

            if (StringUtils.equals(String.valueOf(rpcResult.get("code")), "200")) {
                Map<String, Object> respPayloadMap = null;
                try {
                    if (rpcResult.get("payload") != null) {
                        List<Object> respPayloadList = (List) rpcResult.get("payload");
                        respPayloadMap = (Map) respPayloadList.get(0);
                        logger.warn("internetAccessBlocked userId[{}] blocked[{}] blockStartTime[{}] blockDuration[{}]",
                                userId, respPayloadMap.get("blocked"), respPayloadMap.get("blockStartTime"), respPayloadMap.get("blockDuration"));
                    }
                } catch (Exception e) {
                    logger.error("updateInternetAccess error. userId:[{}]", serial, e);
                }

                DBObject queryDbObj = new BasicDBObject();
                queryDbObj.put("userId", userId);
//                queryDbObj.put("serialNumber", serial);
                queryDbObj.put("macAddress", deviceId);

                BasicDBObject dataToUpdate = new BasicDBObject();
                BasicDBObject updateDbObj = new BasicDBObject();
                updateDbObj.put("$set", dataToUpdate);

                //TODO: parse rpc result and fetch action from it.
                if (StringUtils.equals(accessAction, "ALLOW")) {
                    dataToUpdate.put("internetAccessBlocked", false);
                    dataToUpdate.put("internetAccessBlockStartTime", 0);
                    dataToUpdate.put("internetAccessBlockDuration", 0);
                    dataToUpdate.put("rpcBlocked", false);
                    dataToUpdate.put("rpcTimestamp", System.currentTimeMillis());
                    mongoService.update(queryDbObj, updateDbObj, true, false, HOSTNAME_DETAIL);
                } else if (StringUtils.equals(accessAction, "BLOCK")) {
                    dataToUpdate.put("internetAccessBlocked", true);
                    long blockStartTime = Objects.nonNull(respPayloadMap.get("blockStartTime"))? Long.valueOf(respPayloadMap.get("blockStartTime").toString()): 0;
                    long blockDuration = Objects.nonNull(respPayloadMap.get("blockDuration"))? Long.valueOf(respPayloadMap.get("blockDuration").toString()): 0;
                    dataToUpdate.put("internetAccessBlockStartTime", blockStartTime);
                    dataToUpdate.put("internetAccessBlockDuration", blockDuration);
                    dataToUpdate.put("rpcBlocked", true);
                    dataToUpdate.put("rpcTimestamp", System.currentTimeMillis());
                    mongoService.update(queryDbObj, updateDbObj, true, false, HOSTNAME_DETAIL);
                } else {
                    // should not happen
                    throw new ApiException("invalid accessAction [" + accessAction + "]");
                }

                return rpcResult.get("payload");
            } else {
                throw new ApiException("internet access rpc failed");
            }
        } else if (StringUtils.equals("Reconnect", action)) {
            if (Objects.isNull(deviceDetailData.get("serialNumber")) || deviceDetailData.get("serialNumber").toString().trim().isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device is not connected to any RG/EXT");

            if (Objects.isNull(deviceDetailData.get("band")) || deviceDetailData.get("band").toString().trim().equalsIgnoreCase(EMPTY_STRING)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Band Found for MAC :: " + deviceId);
            }

            boolean isForgetDevice = Objects.isNull(deviceDetailData.get("isForgetDevice")) ? false : Boolean.valueOf(deviceDetailData.get("isForgetDevice").toString());
            if (isForgetDevice) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device is already Disconnected");
            }

            if (Objects.isNull(deviceDetailData.get("bssid")))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device is not connected to any RG/EXT");

            query.clear();
            query.put("userId", userId);
            BasicDBObject sort = new BasicDBObject();
            sort.put("type", -1);

            DBCollection dbCollection = at3Adapter.getMongoDbCollection(AP_DETAIL);
            DBCursor dbCursor = dbCollection.find(query, mongoFieldOptions).sort(sort);

            rpcUri = String.format(RpcConstants.DEVICE_ACTION_URI, deviceId);
            payloadMap.put("action", RpcConstants.ACTION_KICK_WIFI_STA);
            String payload = objectMapper.writeValueAsString(payloadMap);

            while (dbCursor.hasNext()) {
                BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
                if (manageCommonService.getCurrentDevStatus(resultObject)) {
                    continue;
                }
                String type = resultObject.getString("type", "");
                String sn = resultObject.getString("serialNumber", "");
                if (StringUtils.equals("GATEWAY", type)) {
                    try {
                        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, sn, rpcUri, "POST", payload, maxTries, THREAD_TO_SLEEP);
                    } catch (Exception e) {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RPC fail, network:[ " + userId + " ]");
                    }
                } else {
                    cpeRpcService.sendRpc(userId, sn, rpcUri, "POST", payload);
                }
            }
        } else {
            throw new ApiException("invalid action [" + action+ "]");
        }

        return null;
    }
}
