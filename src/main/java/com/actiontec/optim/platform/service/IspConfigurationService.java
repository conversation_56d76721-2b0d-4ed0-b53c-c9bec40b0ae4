package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.IspConfigurationDao;
import com.actiontec.optim.mongodb.dto.IspConfigurationDto;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.model.IspConfigDto;
import com.actiontec.optim.platform.model.IspConfiguration;
import com.actiontec.optim.platform.model.IspConfigurationManagementServer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Service
public class IspConfigurationService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IspConfigurationDao ispConfigurationDao;

    @Autowired
    private ManageCommonService manageCommonService;

    private IspConfiguration getIspConfiguration(IspConfigurationDto ispConfigurationDto) {

        IspConfigurationManagementServer ispConfigurationManagementServer = new IspConfigurationManagementServer();
        ispConfigurationManagementServer.setEnabled(ispConfigurationDto.getConfiguration().getManagementServer().getEnabled());
        ispConfigurationManagementServer.setUrl(ispConfigurationDto.getConfiguration().getManagementServer().getUrl());
        ispConfigurationManagementServer.setUsername(ispConfigurationDto.getConfiguration().getManagementServer().getUsername());
        ispConfigurationManagementServer.setPassword(ispConfigurationDto.getConfiguration().getManagementServer().getPassword());
        ispConfigurationManagementServer.setPeriodicInformEnabled(ispConfigurationDto.getConfiguration().getManagementServer().getPeriodicInformEnabled());
        ispConfigurationManagementServer.setPeriodicInformInterval(ispConfigurationDto.getConfiguration().getManagementServer().getPeriodicInformInterval());
        ispConfigurationManagementServer.setRetryInterval(ispConfigurationDto.getConfiguration().getManagementServer().getRetryInterval());
        ispConfigurationManagementServer.setConnectionRequestUrl(ispConfigurationDto.getConfiguration().getManagementServer().getConnectionRequestUrl());
        ispConfigurationManagementServer.setConnectionRequestUsername(ispConfigurationDto.getConfiguration().getManagementServer().getConnectionRequestUsername());
        ispConfigurationManagementServer.setConnectionRequestPassword(ispConfigurationDto.getConfiguration().getManagementServer().getConnectionRequestPassword());

        IspConfigDto ispConfigDto = new IspConfigDto();
        ispConfigDto.setManagementServer(ispConfigurationManagementServer);

        IspConfiguration ispConfiguration = new IspConfiguration();
        ispConfiguration.setId(ispConfigurationDto.getId());
        ispConfiguration.setIspId(ispConfigurationDto.getIspId());
        ispConfiguration.setEquipmentTypeIds(ispConfigurationDto.getEquipmentTypeIds());
        ispConfiguration.setConfiguration(ispConfigDto);

        return ispConfiguration;
    }

    public List<IspConfiguration> getConfigurations() {

        List<IspConfiguration> result = new ArrayList<>();

        List<IspConfigurationDto> ispConfigurationDtoList = ispConfigurationDao.listAllIspConfigurationDto();
        for(IspConfigurationDto ispConfigurationDto : ispConfigurationDtoList) {
            IspConfiguration ispConfiguration = getIspConfiguration(ispConfigurationDto);
            result.add(ispConfiguration);
        }

        return result;
    }

    public String addConfiguration(IspConfiguration ispConfiguration) throws Exception {

        String id = CommonUtils.generateUUID();
        HashMap<String, Object> confMap = objectMapper.convertValue(ispConfiguration, HashMap.class);
        confMap.put("_id", id);
        confMap.remove("id");

        ispConfigurationDao.addIspConfiguration(confMap);
        return id;
    }

    public List<IspConfiguration> getConfigurationById(String configId) throws Exception {

        List<IspConfiguration> result = new ArrayList<>();

        List<IspConfigurationDto> ispConfigurationDtoList = ispConfigurationDao.findById(configId);
        for(IspConfigurationDto ispConfigurationDto : ispConfigurationDtoList) {
            IspConfiguration ispConfiguration = getIspConfiguration(ispConfigurationDto);
            result.add(ispConfiguration);
        }

        return result;
    }

    public void updateConfigurationById(String configId, IspConfiguration ispConfiguration) throws Exception {

        HashMap<String, Object> ispConfMap = objectMapper.convertValue(ispConfiguration, HashMap.class);
        ispConfMap.remove("_id");

        ispConfigurationDao.updateById(configId, ispConfMap);
    }

    public void deleteConfigurationById(String configId) throws Exception {
        String ispId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());

        List<IspConfigurationDto> ispConfigurationDtoList = ispConfigurationDao.findById(configId);
        for(IspConfigurationDto ispConfigurationDto : ispConfigurationDtoList) {
            if(CommonUtils.isGroupAdmin()) {
                if(!ispConfigurationDto.getIspId().equals(ispId)) {
                    throw new OptimApiException(HttpStatus.METHOD_NOT_ALLOWED, "RESOURCE_NOT_ALLOWED");
                }
            }
            ispConfigurationDao.removeById(configId);
        }
    }
}
