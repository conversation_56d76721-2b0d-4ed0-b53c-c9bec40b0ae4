package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.CpeSmmAppDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.CpeSmmAppDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.model.SmmLifeCycleActionRequest;
import com.actiontec.optim.platform.exception.EntityOverlapException;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.mapper.SmmContainerMapper;
import com.actiontec.optim.platform.model.SmmLifeCycle;
import com.actiontec.optim.platform.model.SmmServiceBody;
import com.actiontec.optim.platform.model.SmmContainerPayloadService;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.actiontec.optim.platform.service.SmmLifeCycleService.CpeSmmAppDesiredState.*;

@Service
public class SmmLifeCycleService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private SmmContainerService smmContainerService;
    @Autowired
    private CpeSmmAppDao cpeSmmAppDao;
    @Autowired
    private SmmContainerMapper smmContainerMapper;
    @Autowired
    private ManageCommonService manageCommonService;

    public enum CpeSmmAppDesiredState {
        Active,
        Paused,
        Inactive,
        Manual;
        public static CpeSmmAppDesiredState getDesiredState(String state) {
            for (CpeSmmAppDesiredState item: values()) {
                if (StringUtils.equals(item.name(), state))
                    return item;
            }
            return null;
        }
    }

    public enum CpeSmmAppActualState {
        Active,
        InProgress,
        Inactive,
        Deleted,
        Error
    }
    public enum CpeSmmAppActionState {
        Init,
        Done,
        Failed,
        Manual
    }
    public enum CpeSmmAppActions {
        Activate,
        Deactivate,
        Pause,
        Resume,
        Upgrade,
        EnableManualMode
    }

    private CpeSmmAppDesiredState getDesiredState(String action, String preDesiredState) {
        CpeSmmAppDesiredState state = null;
        try {
            switch (CpeSmmAppDesiredState.getDesiredState(preDesiredState)) {
                case Active:
                    if (action.equals(CpeSmmAppActions.Deactivate.name())) {
                        state = Inactive;
                    }
                    if (action.equals(CpeSmmAppActions.Pause.name())) {
                        state = Paused;
                    }
                    if (action.equals(CpeSmmAppActions.EnableManualMode.name())) {
                        state = Manual;
                    }
                    if (action.equals(CpeSmmAppActions.Activate.name()) || action.equals(CpeSmmAppActions.Upgrade.name())) {
                        state = Active;
                    }
                    break;
                case Paused:
                    if (action.equals(CpeSmmAppActions.Resume.name())) {
                        state = Active;
                    }
                    if (action.equals(CpeSmmAppActions.Deactivate.name())) {
                        state = Inactive;
                    }
                    if (action.equals(CpeSmmAppActions.EnableManualMode.name())) {
                        state = Manual;
                    }
                    break;
                case Inactive:
                    if (action.equals(CpeSmmAppActions.Activate.name())) {
                        state = Active;
                    }
                    if (action.equals(CpeSmmAppActions.EnableManualMode.name())) {
                        state = Manual;
                    }
                    break;
                case Manual:
                    if (action.equals(CpeSmmAppActions.Activate.name())) {
                        state = Active;
                    }
                    if (action.equals(CpeSmmAppActions.Deactivate.name())) {
                        state = Inactive;
                    }
                    else if (action.equals(CpeSmmAppActions.Pause.name())) {
                        state = Paused;
                    }
                    break;
                default:
                    logger.info("Action:[{}] is conflicted with desiredState:[{}].", action, preDesiredState);
            }
        }
        catch (Exception e) {
            logger.info("Action:[{}] is conflicted with desiredState:[{}].", action, preDesiredState);
        }

        if (Objects.isNull(state))
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Action is conflicted with desiredState.");

        return state;
    }

    public List<SmmLifeCycle> getAll(String stn) {
        List<CpeSmmAppDto> cpeSmmAppDtoList = cpeSmmAppDao.findByStn(stn);
        List<SmmLifeCycle> smmLifeCycleList = new ArrayList<>();
        for(CpeSmmAppDto item: cpeSmmAppDtoList) {
            smmLifeCycleList.add(smmContainerMapper.toSmmLifeCycle(item));
        }
        return smmLifeCycleList;
    }

    public List<SmmLifeCycle> getByStnAndServiceId(String stn, String serviceId) {
        List<CpeSmmAppDto> cpeSmmAppDtoList = cpeSmmAppDao.findByStnAndServiceId(stn, serviceId);
        List<SmmLifeCycle> smmLifeCycleList = new ArrayList<>();
        smmLifeCycleList.add(smmContainerMapper.toSmmLifeCycle(cpeSmmAppDtoList.get(0)));
        return smmLifeCycleList;
    }

    public String createOne(String stn, SmmServiceBody serviceBody) throws Exception {
        List<CpeSmmAppDto>  cpeSmmAppDtoList = cpeSmmAppDao.findByStnAndServiceId(stn, serviceBody.getId());
        if (cpeSmmAppDtoList.size() != 0) {
            throw new EntityOverlapException();
        }

        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getGatewaySerialByUserId(userId).orElseGet(()->userId);
        DBObject param = new BasicDBObject();
        param.put("userId", userId);
        param.put("serialNumber", serial);
        DBObject  apDetail = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail).findOne(param);
        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userId);
        query.put("serialNumber", serial);
        query.put("serviceId", serviceBody.getId());

        BasicDBObject insertField = new BasicDBObject();
        if (Objects.nonNull(apDetail))
            insertField.put("isp", String.valueOf(apDetail.get("isp")));
        insertField.put("serviceTelephoneNumber", stn);
        insertField.put("userId", userId);
        insertField.put("serialNumber", serial);
        insertField.put("serviceId", serviceBody.getId());
        insertField.put("actionId", CommonUtils.generateUUID());
        insertField.put("desiredState", Active.name());
        insertField.put("actionState", CpeSmmAppActionState.Init.name());
        insertField.put("attemptCount", 0);
        if (Objects.nonNull(serviceBody.getTargetVersion())) {
            insertField.put("serviceVer", serviceBody.getTargetVersion());

            SmmContainerPayloadService smmContainerPayloadService = smmContainerService.getPayloadService(userId, serial, smmContainerMapper.toSmmRequestService(serviceBody));
            insertField.put("applicationId", smmContainerPayloadService.getApplicationId());
        }
        insertField.put("provisionData", serviceBody.getProvisionData());
        insertField.put("createdAt", CommonUtils.getCurrentTimeInMillis());
        at3Adapter.findAndModifyCollection(query, CpeSmmAppDto.COLLECTION_cpeSmmApp, null, insertField, true);

        return serviceBody.getId();
    }

    public String postActions(String stn, String serviceId, SmmLifeCycleActionRequest actionRequest) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        List<CpeSmmAppDto>  cpeSmmAppDtoList = cpeSmmAppDao.findByStnAndServiceId(stn, serviceId);
        if (cpeSmmAppDtoList.size() == 0) {
            throw new NoSuchEntityException();
        }

        String serial = manageCommonService.getGatewaySerialByUserId(userId).orElseGet(()->userId);
        BasicDBObject insertField = new BasicDBObject();
        insertField.put("attemptCount", 0);
        insertField.put("actionId", CommonUtils.generateUUID());
        insertField.put("desiredState", getDesiredState(actionRequest.getAction(), cpeSmmAppDtoList.get(0).getDesiredState()).name());
        insertField.put("actionState", actionRequest.getAction().equals(CpeSmmAppActions.EnableManualMode.name())? CpeSmmAppActionState.Manual.name(): CpeSmmAppActionState.Init.name());
        if (actionRequest.getAction().equals(CpeSmmAppActions.Activate.name()) || actionRequest.getAction().equals(CpeSmmAppActions.Resume.name()) ||
                actionRequest.getAction().equals(CpeSmmAppActions.Upgrade.name())) {

            insertField.put("provisionData", actionRequest.getProvisionData());
            if (Objects.nonNull(actionRequest.getTargetVersion())) {
                SmmContainerPayloadService smmContainerPayloadService = smmContainerService.getPayloadService(userId, serial, smmContainerMapper.toSmmRequestService(serviceId, actionRequest));
                insertField.put("applicationId", smmContainerPayloadService.getApplicationId());
                insertField.put("serviceVer", actionRequest.getTargetVersion());
            }
        }
        if (actionRequest.getAction().equals(CpeSmmAppActions.Deactivate.name())) {
            insertField.put("provisionData", "");
        }
        insertField.put("updatedAt", CommonUtils.getCurrentTimeInMillis());
        BasicDBObject updatedDBObject = new BasicDBObject();
        updatedDBObject.put("$set", insertField);
        updatedDBObject.put("$unset", new BasicDBObject("jobId", 1));
        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userId);
        query.put("serialNumber", serial);
        query.put("serviceId", serviceId);
        at3Adapter.findAndModifyCollection(query, CpeSmmAppDto.COLLECTION_cpeSmmApp, null, updatedDBObject, false);

        return serviceId;
    }

}
