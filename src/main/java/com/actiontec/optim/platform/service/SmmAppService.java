package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.mapper.SmmApplicationMapper;
import com.actiontec.optim.platform.model.SmmApplication;
import com.actiontec.optim.platform.model.SmmUsage;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.constants.queries.EquipmentSQL;
import com.incs83.app.entities.OptimFile;
import com.incs83.app.entities.SmmApp;
import com.incs83.app.entities.SmmService;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.CPE_SMM_APP;

@Service
public class SmmAppService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private AwsS3Service awsS3Service;

    @Autowired
    private SmmApplicationMapper smmApplicationMapper;

    private SmmApplication getApp(SmmApp smmApp) throws Exception {

        SmmApplication smmApplication = new SmmApplication();

        smmApplication.setId(smmApp.getId());
        smmApplication.setServiceId(smmApp.getSmmService().getId());
        smmApplication.setVersion(smmApp.getVersion());
        smmApplication.setDescription(smmApp.getDescription());
        smmApplication.setDependencies((List<SmmApplication.Dependency>) new ObjectMapper().readValue(smmApp.getDependencies(), new TypeReference<List<SmmApplication.Dependency>>(){}));
        smmApplication.setFileName(smmApp.getOptimFile().getFileName());
        smmApplication.setFileSize(smmApp.getOptimFile().getFileSize());
        smmApplication.setFileEntry("/actiontec/api/v5/files/" + smmApp.getOptimFile().getId());
        smmApplication.setFileStatus(smmApp.getOptimFile().getFileStatus());
        smmApplication.setFileUrl(smmApp.getOptimFile().getSecureUrl());
        smmApplication.setFileUploadedTime(smmApp.getOptimFile().getFileUploadedTime() == null? 0: smmApp.getOptimFile().getFileUploadedTime().getTime());

        return smmApplication;
    }

    public List<SmmApplication> getAllApps() throws Exception {

        List<SmmApplication> smmApplicationList = new ArrayList<>();

        List<SmmApp> smmApps = (List<SmmApp>) dataAccessService.read(SmmApp.class);

        for(SmmApp smmApp : smmApps) {
            smmApplicationList.add(getApp(smmApp));
        }

        return smmApplicationList;
    }

    public SmmApplication findAppById(String applicationId) throws Exception {

        SmmApp smmApp = (SmmApp) dataAccessService.read(SmmApp.class, applicationId);
        if(smmApp == null) {
            throw new NoSuchEntityException();
        }

        SmmApplication smmApplication = getApp(smmApp);
        return smmApplication;
    }

    public SmmApplication createApp(SmmApplication smmApplication) throws Exception {

        smmApplication.setId(CommonUtils.generateUUID());
        SmmApp smmApp = smmApplicationMapper.toSmmApp(smmApplication);

        SmmService smmService = (SmmService) dataAccessService.read(SmmService.class, smmApplication.getServiceId());
        if(smmService == null) {
            throw new NoSuchEntityException();
        }

        OptimFile optimFile = new OptimFile();
        optimFile.setId(CommonUtils.generateUUID());
        optimFile.setType(ApplicationConstants.OptimFileType.smm.name());
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);
        optimFile.setFileName(smmApplication.getFileName());
        optimFile.setFileSize(smmApplication.getFileSize());

        smmApp.setSmmService(smmService);
        smmApp.setOptimFile(optimFile);
        dataAccessService.create(SmmApp.class, smmApp);

        return getApp(smmApp);
    }

    public void updateAppById(String applicationId, SmmApplication smmApplication) throws Exception {

        SmmApp smmApp = (SmmApp) dataAccessService.read(SmmApp.class, applicationId);
        if(smmApp == null) {
            throw new NoSuchEntityException();
        }

        SmmService smmService = (SmmService) dataAccessService.read(SmmService.class, smmApplication.getServiceId());
        if(smmService == null) {
            throw new NoSuchEntityException();
        }

        OptimFile optimFile = smmApp.getOptimFile();
        if(!smmApplication.getFileName().equals(optimFile.getFileName()) || smmApplication.getFileSize() != optimFile.getFileSize()) {
            awsS3Service.deleteFile(optimFile);
            smmApp.getOptimFile().setFileName(smmApplication.getFileName());
            smmApp.getOptimFile().setFileSize(smmApplication.getFileSize());
            smmApp.getOptimFile().setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);
        }

        smmApp.setVersion((smmApplication.getVersion() != null) ? smmApplication.getVersion() : smmApp.getVersion());
        smmApp.setDescription((smmApplication.getDescription() != null) ? smmApplication.getDescription() : smmApp.getDescription());
        smmApp.setDependencies(new ObjectMapper().writeValueAsString(smmApplication.getDependencies()));
        smmApp.setSmmService(smmService);
        smmApp.setOptimFile(optimFile);

        dataAccessService.update(SmmApp.class, smmApp);
    }

    public void deleteAppById(String applicationId) throws Exception {

        SmmApp smmApp = (SmmApp) dataAccessService.read(SmmApp.class, applicationId);
        if(smmApp == null) {
            throw new NoSuchEntityException();
        }

        awsS3Service.deleteFile(smmApp.getOptimFile());
        dataAccessService.delete(SmmApp.class, applicationId);
    }

    public List<SmmApplication> findAppByVersion(String serviceId, String version) throws Exception {

        List<SmmApplication> smmApplicationList = new ArrayList<>();

        HashMap<String, String> query = new HashMap<>();
        query.clear();
        query.put("serviceId", serviceId);
        query.put("version", version);

        List<SmmApp> smmApps = (List<SmmApp>) dataAccessService.read(SmmApp.class, EquipmentSQL.GET_SMMAPP_LIST_BY_VERSION, query);

        for(SmmApp smmApp : smmApps) {
            smmApplicationList.add(getApp(smmApp));
        }

        logger.debug("findAppByVersion serviceId:[{}] version:[{}] size:[{}]", serviceId, version, smmApplicationList.size());
        return smmApplicationList;
    }

    public Set<String> getAvailableVersions(String serviceId) throws Exception {

        Set<String> versions = new HashSet<>();

        HashMap<String, String> query = new HashMap<>();
        query.clear();
        query.put("serviceId", serviceId);

        List<SmmApp> smmApps = (List<SmmApp>) dataAccessService.read(SmmApp.class, EquipmentSQL.GET_SMMAPP_LIST_BY_SERVICE_ID, query);

        for(SmmApp smmApp : smmApps) {
            versions.add(smmApp.getVersion());
        }

        logger.debug("getAvailableVersions serviceId:[{}] size:[{}]", serviceId, versions.size());
        return versions;
    }

    public SmmUsage getUsage(String serial, String fwVer) throws Exception {
        HashMap<String, Object> query = new HashMap<>();
        query.put("serialNumber", serial);
        query.put("rptFwVer", fwVer);

        int memoryUsed = 0;
        int flashUsed = 0;

        BasicDBObject projection = new BasicDBObject("system.memoryUsed", 1)
                .append("system.fsUsed", 1);

        List<BasicDBObject> documents = mongoService.findList(query, CPE_SMM_APP, projection);
        for (BasicDBObject doc : documents) {
            Object systemObj = doc.get("system");
            if (!(systemObj instanceof BasicDBObject)) {
                continue;
            }

            BasicDBObject system = (BasicDBObject) systemObj;
            memoryUsed += system.getInt("memoryUsed", 0);

            Object fsUsedObj = system.get("fsUsed");
            if (fsUsedObj instanceof BasicDBList) {
                BasicDBList fsUsedList = (BasicDBList) fsUsedObj;
                for (Object entry : fsUsedList) {
                    if (entry instanceof BasicDBObject) {
                        Number used = (Number) ((BasicDBObject) entry).get("used");
                        if (used != null) {
                            flashUsed += used.intValue();
                        }
                    }
                }
            }
        }

        return new SmmUsage(memoryUsed, flashUsed);
    }
}
