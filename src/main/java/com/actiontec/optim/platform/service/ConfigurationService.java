package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.model.*;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.OptimFile;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.MongoService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.actiontec.optim.platform.constant.ApplicationConstants.PUBLIC_FILE_PATH;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.ISP_DEFAULT_CONFIG;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;

@Service
public class ConfigurationService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private AwsS3Service awsS3Service;

    private void validateUserAccessToIsp(String ispId) throws Exception {
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            String userIspId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());

            if (!StringUtils.equalsIgnoreCase(ispId, userIspId)) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
            }
        }
    }

    private String generateFileId(String fileName, int fileSize, String fileType) throws Exception {

        OptimFile optimFile = new OptimFile();
        optimFile.setId(CommonUtils.generateUUID());
        optimFile.setType(fileType);
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);
        optimFile.setFileName(fileName);
        optimFile.setFileSize(fileSize);

        dataAccessService.create(optimFile);

        return optimFile.getId();
    }

    private OptimFile getOptimFile(String fileId) throws Exception {
        return (OptimFile) dataAccessService.read(OptimFile.class, fileId);
    }

    private BasicDBObject createVersionDocument() {
        final Integer MAJOR_VERSION = 1;
        final Integer MINOR_VERSION = 0;

        return new BasicDBObject("major", MAJOR_VERSION)
                .append("minor", MINOR_VERSION);
    }

    private BasicDBObject createIspDocument(ConfigurationRequest configurationRequest) throws Exception {
        Isp isp = configurationRequest.getIsp();
        return new BasicDBObject("name", isp.getName())
                .append("message", isp.getMessage())
                .append("logo", new BasicDBObject("fileId", generateFileId(isp.getLogo().getFileName(), isp.getLogo().getFileSize(), ApplicationConstants.OptimFileType.logo.name())));
    }

    private BasicDBObject createAcsDocument(ConfigurationRequest configurationRequest) {
        Acs acs = configurationRequest.getAcs();
        List<BasicDBObject> certificates = Optional.ofNullable(acs.getCertificates())
                .orElse(Collections.emptyList())
                .stream()
                .map(cert -> {
                    try {
                        return new BasicDBObject("fileId", generateFileId(
                                cert.getFileName(),
                                cert.getFileSize(),
                                ApplicationConstants.OptimFileType.certification.name()
                        ));
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to generate fileId for certificate with fileName: " + cert.getFileName(), e);
                    }
                })
                .collect(Collectors.toList());


        return new BasicDBObject("username", acs.getUsername())
                .append("password", acs.getPassword())
                .append("url", acs.getUrl())
                .append("certificates", certificates);
    }

    private BasicDBObject createWirelessDocument(ConfigurationRequest configurationRequest) {
        Wireless wireless = configurationRequest.getWireless();

        List<BasicDBObject> ssids = wireless.getSsids().stream()
                .map(ssid -> new BasicDBObject("type", ssid.getType())
                        .append("namePrefix", ssid.getNamePrefix())
                        .append("nameSuffixType", ssid.getNameSuffixType())
                        .append("mloEnabled", ssid.isMloEnabled()))
                .collect(Collectors.toList());

        List<BasicDBObject> radios = wireless.getRadios().stream()
                .map(radio -> new BasicDBObject("band", radio.getBand())
                        .append("bandwidth", radio.getBandwidth())
                        .append("dfsEnabled", radio.isDfsEnabled())
                        .append("activeAcs", new BasicDBObject("enabled", radio.getActiveAcs().isEnabled())
                                .append("interval", radio.getActiveAcs().getInterval())))
                .collect(Collectors.toList());

        BasicDBObject backhaulSteering = new BasicDBObject("enabled", wireless.getBackhualSteering().isEnabled())
                .append("interval", wireless.getBackhualSteering().getInterval());

        BasicDBObject smartHome = new BasicDBObject("enabled", wireless.getSmartHome().isEnabled());

        return new BasicDBObject("ssids", ssids)
                .append("radios", radios)
                .append("backhualSteering", backhaulSteering)
                .append("smartHome", smartHome);
    }

    private BasicDBObject createNetworkDocument(ConfigurationRequest configurationRequest) {
        Network network = configurationRequest.getNetwork();

        BasicDBObject wan = new BasicDBObject("ipv4Settings", new BasicDBObject("protocol", network.getWan().getIpv4Settings().getProtocol()))
                .append("ipv6Settings", new BasicDBObject("enabled", network.getWan().getIpv6Settings().isEnabled())
                        .append("protocol", network.getWan().getIpv6Settings().getProtocol()));

        BasicDBObject lan = new BasicDBObject("ipv4Settings", new BasicDBObject("ipAddress", network.getLan().getIpv4Settings().getIpAddress())
                .append("mask", network.getLan().getIpv4Settings().getMask()))
                .append("ipv6Settings", new BasicDBObject("enabled", network.getLan().getIpv6Settings().isEnabled()));

        return new BasicDBObject("wan", wan)
                .append("lan", lan);
    }

    private BasicDBObject createAccessDocument(ConfigurationRequest configurationRequest) {
        Access access = configurationRequest.getAccess();
        Ssh ssh = access.getSsh();
        return new BasicDBObject("ssh", new BasicDBObject("enabled", ssh.isEnabled())
                .append("port", ssh.getPort()));
    }

    private ConfigurationResponse mapDocumentToResponse(DBObject dbObject) throws Exception {
        ConfigurationResponse response = new ConfigurationResponse();

        // Map top-level fields
        response.setId((String) dbObject.get("configId"));
        response.setModelId((String) dbObject.get("modelId"));

        // Map version
        DBObject version = (DBObject) dbObject.get("version");
        Version versionObj = new Version();
        versionObj.setMajor(((Number) version.get("major")).intValue());
        versionObj.setMinor(((Number) version.get("minor")).intValue());
        response.setVersion(versionObj);

        // Map ISP
        DBObject isp = (DBObject) dbObject.get("isp");
        Isp ispObj = new Isp();
        ispObj.setName((String) isp.get("name"));
        ispObj.setMessage((String) isp.get("message"));

        DBObject logo = (DBObject) isp.get("logo");
        OptimFile optimFile = getOptimFile((String) logo.get("fileId"));
        if (optimFile != null) {
            Logo logoObj = new Logo();
            logoObj.setFileName(optimFile.getFileName());
            logoObj.setFileSize(optimFile.getFileSize());
            logoObj.setFileEntry(PUBLIC_FILE_PATH + optimFile.getId());
            ispObj.setLogo(logoObj);
        }

        response.setIsp(ispObj);

        // Map ACS
        DBObject acs = (DBObject) dbObject.get("acs");
        Acs acsObj = new Acs();
        acsObj.setUsername((String) acs.get("username"));
        acsObj.setPassword((String) acs.get("password"));
        acsObj.setUrl((String) acs.get("url"));

        List<DBObject> certificates = (List<DBObject>) acs.get("certificates");
        List<Certificate> certificateObjs = certificates.stream()
                .map(cert -> {
                    String fileId = (String) cert.get("fileId");
                    try {
                        OptimFile fileDetails = getOptimFile(fileId);

                        Certificate certificate = new Certificate();
                        certificate.setFileName(fileDetails.getFileName());
                        certificate.setFileSize(fileDetails.getFileSize());
                        certificate.setFileEntry(PUBLIC_FILE_PATH + fileDetails.getId());
                        return certificate;
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to fetch file details for fileId: " + fileId, e);
                    }
                })
                .collect(Collectors.toList());
        acsObj.setCertificates(certificateObjs);
        response.setAcs(acsObj);

        // Map Wireless
        DBObject wireless = (DBObject) dbObject.get("wireless");
        Wireless wirelessObj = new Wireless();

        List<DBObject> ssids = (List<DBObject>) wireless.get("ssids");
        List<Ssid> ssidObjs = ssids.stream().map(ssid -> {
            Ssid ssidObj = new Ssid();
            ssidObj.setType((String) ssid.get("type"));
            ssidObj.setNamePrefix((String) ssid.get("namePrefix"));
            ssidObj.setNameSuffixType((String) ssid.get("nameSuffixType"));
            ssidObj.setMloEnabled((Boolean) ssid.get("mloEnabled"));
            return ssidObj;
        }).collect(Collectors.toList());
        wirelessObj.setSsids(ssidObjs);

        List<DBObject> radios = (List<DBObject>) wireless.get("radios");
        List<Radio> radioObjs = radios.stream().map(radio -> {
            Radio radioObj = new Radio();
            radioObj.setBand((String) radio.get("band"));
            radioObj.setBandwidth((String) radio.get("bandwidth"));
            radioObj.setDfsEnabled((Boolean) radio.get("dfsEnabled"));

            DBObject activeAcs = (DBObject) radio.get("activeAcs");
            ActiveAcs activeAcsObj = new ActiveAcs();
            activeAcsObj.setEnabled((Boolean) activeAcs.get("enabled"));
            activeAcsObj.setInterval(((Number) activeAcs.get("interval")).intValue());
            radioObj.setActiveAcs(activeAcsObj);
            return radioObj;
        }).collect(Collectors.toList());
        wirelessObj.setRadios(radioObjs);

        DBObject backhaulSteering = (DBObject) wireless.get("backhualSteering");
        BackhaulSteering backhaulSteeringObj = new BackhaulSteering();
        backhaulSteeringObj.setEnabled((Boolean) backhaulSteering.get("enabled"));
        backhaulSteeringObj.setInterval(((Number) backhaulSteering.get("interval")).intValue());
        wirelessObj.setBackhualSteering(backhaulSteeringObj);

        DBObject smartHome = (DBObject) wireless.get("smartHome");
        SmartHome smartHomeObj = new SmartHome();
        if (smartHome != null) {
            smartHomeObj.setEnabled((Boolean) smartHome.get("enabled"));
        }
        wirelessObj.setSmartHome(smartHomeObj);

        response.setWireless(wirelessObj);

        // Map Network
        DBObject network = (DBObject) dbObject.get("network");
        Network networkObj = new Network();

        DBObject wan = (DBObject) network.get("wan");
        Wan wanObj = new Wan();
        DBObject ipv4Settings = (DBObject) wan.get("ipv4Settings");
        Ipv4Settings ipv4SettingsObj = new Ipv4Settings();
        ipv4SettingsObj.setProtocol((String) ipv4Settings.get("protocol"));
        wanObj.setIpv4Settings(ipv4SettingsObj);

        DBObject ipv6Settings = (DBObject) wan.get("ipv6Settings");
        Ipv6Settings ipv6SettingsObj = new Ipv6Settings();
        ipv6SettingsObj.setEnabled((Boolean) ipv6Settings.get("enabled"));
        ipv6SettingsObj.setProtocol((String) ipv6Settings.get("protocol"));
        wanObj.setIpv6Settings(ipv6SettingsObj);
        networkObj.setWan(wanObj);

        DBObject lan = (DBObject) network.get("lan");
        Lan lanObj = new Lan();
        DBObject lanIpv4Settings = (DBObject) lan.get("ipv4Settings");
        LanIpv4Settings lanIpv4SettingsObj = new LanIpv4Settings();
        lanIpv4SettingsObj.setIpAddress((String) lanIpv4Settings.get("ipAddress"));
        lanIpv4SettingsObj.setMask((String) lanIpv4Settings.get("mask"));
        lanObj.setIpv4Settings(lanIpv4SettingsObj);

        DBObject lanIpv6Settings = (DBObject) lan.get("ipv6Settings");
        LanIpv6Settings lanIpv6SettingsObj = new LanIpv6Settings();
        lanIpv6SettingsObj.setEnabled((Boolean) lanIpv6Settings.get("enabled"));
        lanObj.setIpv6Settings(lanIpv6SettingsObj);
        networkObj.setLan(lanObj);

        response.setNetwork(networkObj);

        // Map Access
        DBObject access = (DBObject) dbObject.get("access");
        Access accessObj = new Access();
        DBObject ssh = (DBObject) access.get("ssh");
        Ssh sshObj = new Ssh();
        sshObj.setEnabled((Boolean) ssh.get("enabled"));
        sshObj.setPort(((Number) ssh.get("port")).intValue());
        accessObj.setSsh(sshObj);

        response.setAccess(accessObj);

        return response;
    }

    private BasicDBObject buildIspUpdate(Isp isp, BasicDBObject existingIspData) throws Exception {
        BasicDBObject ispUpdate = new BasicDBObject();
        if (isp.getName() != null) {
            ispUpdate.put("name", isp.getName());
        }
        if (isp.getMessage() != null) {
            ispUpdate.put("message", isp.getMessage());
        }
        if (isp.getLogo() != null) {
            BasicDBObject logoUpdate = new BasicDBObject("fileId", generateFileId(isp.getLogo().getFileName(), isp.getLogo().getFileSize(), ApplicationConstants.OptimFileType.logo.name()));
            ispUpdate.put("logo", logoUpdate);
        } else {
            ispUpdate.put("logo", existingIspData.get("logo"));
        }
        return ispUpdate;
    }

    private BasicDBObject buildAcsUpdate(Acs acs, BasicDBObject existingAcsData) {
        BasicDBObject acsUpdate = new BasicDBObject();
        if (acs.getUsername() != null) {
            acsUpdate.put("username", acs.getUsername());
        }
        if (acs.getPassword() != null) {
            acsUpdate.put("password", acs.getPassword());
        }
        if (acs.getUrl() != null) {
            acsUpdate.put("url", acs.getUrl());
        }
        if (acs.getCertificates() != null) {
            List<BasicDBObject> certificates = acs.getCertificates().stream()
                .map(cert -> {
                    try {
                        return new BasicDBObject("fileId", generateFileId(
                                cert.getFileName(),
                                cert.getFileSize(),
                                ApplicationConstants.OptimFileType.certification.name()
                        ));
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to generate fileId for certificate with fileName: " + cert.getFileName(), e);
                    }
                })
                .collect(Collectors.toList());
            acsUpdate.put("certificates", certificates);
        } else {
            acsUpdate.put("certificates", existingAcsData.get("certificates"));
        }
        return acsUpdate;
    }

    private BasicDBObject buildWirelessUpdate(Wireless wireless) {
        BasicDBObject wirelessUpdate = new BasicDBObject();

        // Convert ssids
        if (wireless.getSsids() != null) {
            List<BasicDBObject> ssidObjects = wireless.getSsids().stream()
                    .map(ssid -> {
                        BasicDBObject ssidObject = new BasicDBObject();
                        ssidObject.put("type", ssid.getType());
                        ssidObject.put("namePrefix", ssid.getNamePrefix());
                        ssidObject.put("nameSuffixType", ssid.getNameSuffixType());
                        ssidObject.put("mloEnabled", ssid.isMloEnabled());
                        return ssidObject;
                    })
                    .collect(Collectors.toList());
            wirelessUpdate.put("ssids", ssidObjects);
        }

        // Convert radios
        if (wireless.getRadios() != null) {
            List<BasicDBObject> radioObjects = wireless.getRadios().stream()
                    .map(radio -> {
                        BasicDBObject radioObject = new BasicDBObject();
                        radioObject.put("band", radio.getBand());
                        radioObject.put("bandwidth", radio.getBandwidth());
                        radioObject.put("dfsEnabled", radio.isDfsEnabled());
                        if (radio.getActiveAcs() != null) {
                            BasicDBObject activeAcsObject = new BasicDBObject();
                            activeAcsObject.put("enabled", radio.getActiveAcs().isEnabled());
                            activeAcsObject.put("interval", radio.getActiveAcs().getInterval());
                            radioObject.put("activeAcs", activeAcsObject);
                        }
                        return radioObject;
                    })
                    .collect(Collectors.toList());
            wirelessUpdate.put("radios", radioObjects);
        }

        // Convert backhualSteering
        if (wireless.getBackhualSteering() != null) {
            BasicDBObject backhualSteeringObject = new BasicDBObject();
            backhualSteeringObject.put("enabled", wireless.getBackhualSteering().isEnabled());
            backhualSteeringObject.put("interval", wireless.getBackhualSteering().getInterval());
            wirelessUpdate.put("backhualSteering", backhualSteeringObject);
        }

        if (wireless.getSmartHome() != null) {
            BasicDBObject smartHomeObject = new BasicDBObject();
            smartHomeObject.put("enabled", wireless.getSmartHome().isEnabled());
            wirelessUpdate.put("smartHome", smartHomeObject);
        }

        return wirelessUpdate;
    }

    private BasicDBObject buildNetworkUpdate(Network network) {
        BasicDBObject networkUpdate = new BasicDBObject();

        // Convert WAN
        if (network.getWan() != null) {
            BasicDBObject wanObject = new BasicDBObject();

            if (network.getWan().getIpv4Settings() != null) {
                Ipv4Settings ipv4Settings = network.getWan().getIpv4Settings();
                BasicDBObject ipv4Object = new BasicDBObject();
                ipv4Object.put("protocol", ipv4Settings.getProtocol());
                wanObject.put("ipv4Settings", ipv4Object);
            }

            if (network.getWan().getIpv6Settings() != null) {
                Ipv6Settings ipv6Settings = network.getWan().getIpv6Settings();
                BasicDBObject ipv6Object = new BasicDBObject();
                ipv6Object.put("enabled", ipv6Settings.isEnabled());
                ipv6Object.put("protocol", ipv6Settings.getProtocol());
                wanObject.put("ipv6Settings", ipv6Object);
            }

            networkUpdate.put("wan", wanObject);
        }

        // Convert LAN
        if (network.getLan() != null) {
            BasicDBObject lanObject = new BasicDBObject();

            if (network.getLan().getIpv4Settings() != null) {
                LanIpv4Settings ipv4Settings = network.getLan().getIpv4Settings();
                BasicDBObject ipv4Object = new BasicDBObject();
                ipv4Object.put("ipAddress", ipv4Settings.getIpAddress());
                ipv4Object.put("mask", ipv4Settings.getMask());
                lanObject.put("ipv4Settings", ipv4Object);
            }

            if (network.getLan().getIpv6Settings() != null) {
                LanIpv6Settings ipv6Settings = network.getLan().getIpv6Settings();
                BasicDBObject ipv6Object = new BasicDBObject();
                ipv6Object.put("enabled", ipv6Settings.isEnabled());
                lanObject.put("ipv6Settings", ipv6Object);
            }

            networkUpdate.put("lan", lanObject);
        }

        return networkUpdate;
    }

    private BasicDBObject buildAccessUpdate(Access access) {
        BasicDBObject accessUpdate = new BasicDBObject();

        // Convert SSH settings
        if (access.getSsh() != null) {
            Ssh ssh = access.getSsh();
            BasicDBObject sshObject = new BasicDBObject();
            sshObject.put("enabled", ssh.isEnabled());
            sshObject.put("port", ssh.getPort());
            accessUpdate.put("ssh", sshObject);
        }

        return accessUpdate;
    }

    private void deleteFile(String fileId) {
        try {
            OptimFile optimFile = (OptimFile) dataAccessService.read(OptimFile.class, fileId);
            awsS3Service.deleteFile(optimFile);
        } catch (Exception e) {
            System.err.println("Failed to delete file with fileId: " + fileId + ", error: " + e.getMessage());
        }
    }


    private void deleteAssociatedFiles(DBObject configObject) {
        // Delete logo file
        if (configObject.containsField("isp")) {
            DBObject isp = (DBObject) configObject.get("isp");
            if (isp.containsField("logo")) {
                DBObject logo = (DBObject) isp.get("logo");
                String logoFileId = (String) logo.get("fileId");
                if (logoFileId != null) {
                    deleteFile(logoFileId);
                }
            }
        }

        // Delete ACS certificates
        if (configObject.containsField("acs")) {
            DBObject acs = (DBObject) configObject.get("acs");
            if (acs.containsField("certificate")) {
                List<DBObject> certificates = (List<DBObject>) acs.get("certificate");
                for (DBObject certificate : certificates) {
                    String certificateFileId = (String) certificate.get("fileId");
                    if (certificateFileId != null) {
                        deleteFile(certificateFileId);
                    }
                }
            }
        }
    }


    public String createDefaultConfiguration(String ispId, ConfigurationRequest configurationRequest) throws Exception {

        validateUserAccessToIsp(ispId);

        String id = CommonUtils.generateUUID();
        BasicDBObject document = new BasicDBObject("configId", id)
                .append("ispId", ispId)
                .append("modelId", configurationRequest.getModelId())
                .append("version", createVersionDocument())
                .append("isp", createIspDocument(configurationRequest))
                .append("acs", createAcsDocument(configurationRequest))
                .append("wireless", createWirelessDocument(configurationRequest))
                .append("network", createNetworkDocument(configurationRequest))
                .append("access", createAccessDocument(configurationRequest));

        mongoService.create(ISP_DEFAULT_CONFIG, document);

        return id;
    }

    public List<ConfigurationResponse> getAllConfigurations(String ispId) throws Exception {

        validateUserAccessToIsp(ispId);

        List<ConfigurationResponse> configurationResponses = new ArrayList<>();

        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);

        List<BasicDBObject> configObjectList = mongoService.findList(params, ISP_DEFAULT_CONFIG, fieldsToRemove);
        if (Objects.nonNull(configObjectList) && !configObjectList.isEmpty()) {
            for (DBObject dbObject : configObjectList) {
                configurationResponses.add(mapDocumentToResponse(dbObject));
            }
        }

        return configurationResponses;
    }

    public ConfigurationResponse getConfiguration(String ispId, String configId) throws Exception {

        validateUserAccessToIsp(ispId);

        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);
        params.put("configId", configId);

        DBObject configObject = mongoService.findOne(ISP_DEFAULT_CONFIG, params);
        if (configObject == null) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Config not found with id: " + configId);
        }

        return mapDocumentToResponse(configObject);
    }

    public void updateConfiguration(String ispId, String configId, ConfigurationRequest configurationRequest) throws Exception {

        validateUserAccessToIsp(ispId);

        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);
        params.put("configId", configId);

        DBObject configObject = mongoService.findOne(ISP_DEFAULT_CONFIG, params);
        if (configObject == null) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Config not found with id: " + configId);
        }

        // Build the update fields
        BasicDBObject updateFields = new BasicDBObject();
        if (configurationRequest.getModelId() != null) {
            updateFields.put("modelId", configurationRequest.getModelId());
        }
        if (configurationRequest.getIsp() != null) {
            updateFields.put("isp", buildIspUpdate(configurationRequest.getIsp(), (BasicDBObject) configObject.get("isp")));
        }
        if (configurationRequest.getAcs() != null) {
            updateFields.put("acs", buildAcsUpdate(configurationRequest.getAcs(), (BasicDBObject) configObject.get("acs")));
        }
        if (configurationRequest.getWireless() != null) {
            updateFields.put("wireless", buildWirelessUpdate(configurationRequest.getWireless()));
        }
        if (configurationRequest.getNetwork() != null) {
            updateFields.put("network", buildNetworkUpdate(configurationRequest.getNetwork()));
        }
        if (configurationRequest.getAccess() != null) {
            updateFields.put("access", buildAccessUpdate(configurationRequest.getAccess()));
        }


        BasicDBObject query = new BasicDBObject();
        query.put("ispId", ispId);
        query.put("configId", configId);

        BasicDBObject dataToUpdate = new BasicDBObject();
        dataToUpdate.put("$set", updateFields);

        mongoService.update(query, dataToUpdate, false, false, ISP_DEFAULT_CONFIG);
    }

    public void deleteConfiguration(String ispId, String configId) throws Exception {

        validateUserAccessToIsp(ispId);

        HashMap<String, Object> params = new HashMap<>();
        params.put("ispId", ispId);
        params.put("configId", configId);

        DBObject configObject = mongoService.findOne(ISP_DEFAULT_CONFIG, params);

        deleteAssociatedFiles(configObject);

        mongoService.deleteOne(params, ISP_DEFAULT_CONFIG);
    }
}
