package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.repository.EquipmentRedirectRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EquipmentRedirectService {

    @Autowired
    private EquipmentRedirectRepo equipmentRedirectRepo;

    public boolean hasRedirectSetting(String brokerId) throws Exception {
        return equipmentRedirectRepo.existsByBrokerId(brokerId);
    }
} 