package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.mapper.FwFileMapper;
import com.actiontec.optim.platform.model.FwFile;
import com.incs83.app.entities.OptimFile;
import com.incs83.mt.DataAccessService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;

@Service
public class FwFileService extends FileService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private FwFileMapper fwFileMapper;

    @Autowired
    private AwsS3Service awsS3Service;

    public FwFile findFwFileById(String fileId) throws Exception {

        OptimFile optimFile = (OptimFile) dataAccessService.read(OptimFile.class, fileId);
        return fwFileMapper.toFwFile(optimFile);
    }

    public Boolean updateFwFile(String fileId, MultipartFile file) throws Exception {

        OptimFile optimFile = (OptimFile) dataAccessService.read(OptimFile.class, fileId);
        if(optimFile == null) {
            logger.warn("File not found: {}", fileId);
            return false;
        }

        if(!optimFile.getFileName().equals(file.getOriginalFilename())) {
            logger.warn("Uploaded file name does not match expected file name.");
            return false;
        }

        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_UPLOADING);
        dataAccessService.update(OptimFile.class, optimFile);

        Boolean upload = awsS3Service.updateFile(optimFile, file);
        if(!upload) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_ERROR);
            dataAccessService.update(OptimFile.class, optimFile);
            return false;
        }

        // Calculate MD5 checksum
        String md5 = calculateMd5(file.getInputStream());
        if (md5 == null) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_ERROR);
            dataAccessService.update(OptimFile.class, optimFile);
            return false;
        }
        optimFile.setMd5(md5);

        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_SUCCESS);
        optimFile.setFileUploadedTime(new Date());
        dataAccessService.update(OptimFile.class, optimFile);
        return true;
    }

    public boolean updateFile(String fileId, String path) throws Exception {
        boolean ret = false;

        OptimFile optimFile = (OptimFile) dataAccessService.read(OptimFile.class, fileId);
        if(optimFile == null) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_ERROR);
            dataAccessService.update(OptimFile.class, optimFile);
            return ret;
        }

        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_UPLOADING);
        dataAccessService.update(OptimFile.class, optimFile);

        File fileOnDisk = new File(path);

        Boolean upload = awsS3Service.uploadFile(optimFile, fileOnDisk);
        if(!upload) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_ERROR);
            dataAccessService.update(OptimFile.class, optimFile);
            return ret;
        }

        // Calculate MD5 checksum
        String md5 = calculateMd5(fileOnDisk);
        if (md5 == null) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_ERROR);
            dataAccessService.update(OptimFile.class, optimFile);
            return ret;
        }
        optimFile.setMd5(md5);

        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_SUCCESS);
        optimFile.setFileUploadedTime(new Date());
        dataAccessService.update(OptimFile.class, optimFile);
        return ret;
    }
}
