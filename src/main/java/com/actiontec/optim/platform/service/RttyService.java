package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.internal.model.RttyLogRequest;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.incs83.app.entities.OptimFile;
import com.incs83.app.entities.RttyLog;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;

@Service
public class RttyService {

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private FwFileService fileService;

    public void createLog(String deviceId, String sessionId, String fileName, int fileSize, MultipartFile file) throws Exception {
        RttyLog rttyLog = new RttyLog();

        rttyLog.setId(CommonUtils.generateUUID());
        rttyLog.setDeviceId(deviceId);
        rttyLog.setSessionId(sessionId);

        OptimFile optimFile = new OptimFile();
        optimFile.setId(CommonUtils.generateUUID());
        optimFile.setType(ApplicationConstants.OptimFileType.rtty.name());
        optimFile.setFileName(fileName);
        optimFile.setFileSize(fileSize);
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);

        dataAccessService.create(optimFile);
        rttyLog.setOptimFile(optimFile);

        rttyLog.setCreatedAt(new Date());
        dataAccessService.create(rttyLog);

        fileService.updateFwFile(optimFile.getId(), file);
    }
}
