package com.actiontec.optim.platform.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

@Service
public class SimpleAesEcbCryptoService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    public String encrypt(String src, String key) {
        Cipher cipher = getCipher(Cipher.ENCRYPT_MODE, key);

        byte[] encrypted;
        try {
            encrypted = cipher.doFinal(src.getBytes("UTF-8"));
        } catch (UnsupportedEncodingException e) {
            // should not happen
            throw new RuntimeException(e);
        } catch (BadPaddingException e) {
            // should not happen
            throw new RuntimeException(e);
        } catch (IllegalBlockSizeException e) {
            // should not happen
            throw new RuntimeException(e);
        }
        return Base64.getUrlEncoder().withoutPadding().encodeToString(encrypted);
    }

    public String decrypt(String src, String key) {
        Cipher cipher = getCipher(Cipher.DECRYPT_MODE, key);

        byte[] encrypted1 = Base64.getUrlDecoder().decode(src);
        try {
            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original);
            return originalString;
        } catch (BadPaddingException e) {
            // should not happen
            throw new RuntimeException(e);
        } catch (IllegalBlockSizeException e) {
            // should not happen
            throw new RuntimeException(e);
        }

    }

    private Cipher getCipher(int opmode, String key) {
        if (key == null || key.length() != 16) {
            throw new RuntimeException("encrypt failed. no key or key length is not enough");
        }

        byte[] rawKey;
        try {
            rawKey = key.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            // should not happen
            throw new RuntimeException(e);
        }

        SecretKeySpec skeySpec = new SecretKeySpec(rawKey, "AES");
        try {
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(opmode, skeySpec);
            //Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            //IvParameterSpec iv = new IvParameterSpec("0102030405060708".getBytes());
            //cipher.init(Cipher.ENCRYPT_MODE, skeySpec, iv);
            return cipher;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (NoSuchPaddingException e) {
            throw new RuntimeException(e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e);
        }
    }
}
