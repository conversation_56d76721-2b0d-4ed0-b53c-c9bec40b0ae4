package com.actiontec.optim.platform.service;

import com.incs83.app.constants.queries.SubscriberSQL;
import com.incs83.mt.DataAccessService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class SubscriberService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public Long getSubscriberCountByGroupId(String groupId) throws Exception {

        HashMap<String, Object> param = new HashMap<>();
        param.put("id", groupId);

        return Long.valueOf(dataAccessService.readNative(SubscriberSQL.GET_SUBSCRIBER_COUNT_BY_COMPARTMENT, param).iterator().next().toString());
    }
}
