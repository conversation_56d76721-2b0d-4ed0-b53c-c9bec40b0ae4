package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.BatchEquipmentUploadFileDetailDTO;
import com.actiontec.optim.platform.api.v6.dto.BatchEquipmentUploadLogQueryDTO;
import com.actiontec.optim.platform.api.v6.dto.BatchEquipmentUploadLogDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.api.v6.enums.BatchEquipmentStatus;
import com.actiontec.optim.platform.repository.BatchEquipmentUploadLogRepo;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.BatchEquipmentUploadLog;
import com.incs83.app.entities.OptimFile;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.MongoTenantTemplate;
import com.incs83.service.MongoService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants._FILE_TYPE;
import static com.incs83.constants.ApplicationCommonConstants.FORWARD_SLASH;

@Service
public class BatchEquipmentService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private MongoTenantTemplate mongoTenantTemplate;

    @Autowired
    private NCSIspService ispService;

    @Autowired
    private BatchEquipmentFileService batchEquipmentFileService;

    @Autowired
    BatchEquipmentUploadLogRepo uploadLogRepo;

    public PaginationResponse<BatchEquipmentUploadLogDTO> getAllBatchEquipmentUploadLogs(
            BatchEquipmentUploadLogQueryDTO queryDTO) throws Exception {
        PaginationResponse<BatchEquipmentUploadLogDTO> response = uploadLogRepo
                .getAllBatchEquipmentUploadLogs(queryDTO);
        List<BatchEquipmentUploadLogDTO> uploadLogDTOList = populateUploadDetailsResult(response.getData(),
                queryDTO.getOffset(), queryDTO.getLimit());
        response.setData(uploadLogDTOList);
        return response;
    }

    public BatchEquipmentUploadLogDTO getBatchEquipmentUploadLogById(String id) throws Exception {
        BatchEquipmentUploadLogDTO result = new BatchEquipmentUploadLogDTO();
        BatchEquipmentUploadLogDTO uploadLogDTO = uploadLogRepo.getBatchEquipmentUploadDTOById(id);
        List<BatchEquipmentUploadLogDTO> uploadLogDTOList = new ArrayList<>();
        uploadLogDTOList.add(uploadLogDTO);
        uploadLogDTOList = populateUploadDetailsResult(uploadLogDTOList, 0, 1);
        if (CollectionUtils.isNotEmpty(uploadLogDTOList)) {
            result = uploadLogDTOList.get(0);
        }

        return result;
    }

    public BatchEquipmentUploadLog getBatchEquipmentUploadLogByFileId(String fileId) throws Exception {
        return uploadLogRepo.getBatchEquipmentUploadByFileId(fileId);
    }

    public BatchEquipmentUploadLog getBatchEquipmentUploadLogByReportFileId(String reportFileId) throws Exception {
        return uploadLogRepo.getBatchEquipmentUploadByReportFileId(reportFileId);
    }

    public String createUploadLog(String ispId, String fileId, String action) throws Exception {
        return uploadLogRepo.createLog(ispId, fileId, action);
    }

    public void batchEquipmentFileUpload(String userIspId, String fileId, MultipartFile file) throws Exception {
        BatchEquipmentUploadLog batchUploadLog = uploadLogRepo.getBatchEquipmentUploadByFileId(fileId);
        OptimFile optimFile = batchEquipmentFileService.getOptimFileById(batchUploadLog.getFileId());

        ispService.checkIspIdPresent(userIspId);

        if (ObjectUtils.isEmpty(optimFile)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "FileId does not exist.");
        }

        if (ObjectUtils.isEmpty(file)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File cannot be empty.");
        }

        if (!optimFile.getFileName().equals(file.getOriginalFilename())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File unexpected.");
        }

        if (!manageCommonService.isCSVFile(file)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Content is not valid CSV");
        }

        // upload file to aws s3
        String s3FileName = fileId + ".csv";
        String filePath = EQUIPMENT_BATCH_UPLOAD_DIR + FORWARD_SLASH + s3FileName;
        // optimFile path is s3 filePath, not equal to fileEntry
        optimFile.setFilePath(filePath);
        optimFile = batchEquipmentFileService.uploadFile(optimFile, file);

        // sync upload log to mysql
        String uploadLogId = batchUploadLog.getId();
        if (StringUtils.equals(batchUploadLog.getIspId(), userIspId) || CommonUtils.isSysAdmin()) {
            batchUploadLog.setStatus(BatchEquipmentStatus.Pending);
            batchUploadLog.setUpdatedAt(LocalDateTime.now());
            uploadLogRepo.updateUploadLog(batchUploadLog);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The file does not belong to this ispId.");
        }

        // sync upload log to mongo
        createUploadFileDetails(userIspId, uploadLogId, fileId, file, optimFile.getFileName(), optimFile.getSecureUrl());
    }

    private void createUploadFileDetails(String ispId, String uploadLogId, String fileId, MultipartFile file,
            String fileName, String secureUrl) throws Exception {
        // upload status saved in mongo could be expanded
        HashMap<String, Object> uploadFileDetail = new HashMap<>();
        uploadFileDetail.put("id", CommonUtils.generateUUID());
        uploadFileDetail.put("ispId", ispId);
        uploadFileDetail.put("uploadLogId", uploadLogId);
        uploadFileDetail.put("fileId", fileId);
        uploadFileDetail.put("fileName", fileName);
        uploadFileDetail.put("type", _FILE_TYPE);
        uploadFileDetail.put("url", secureUrl);
        uploadFileDetail.put("size", file.getSize());
        uploadFileDetail.put("createdAt", CommonUtils.getCurrentTimeInMillis());
        uploadFileDetail.put("createdBy", CommonUtils.getUserIdOfLoggedInUser());
        uploadFileDetail.put("totalRecords", manageCommonService.countNoOfRecord(file));
        uploadFileDetail.put("successRecords", 0L);
        uploadFileDetail.put("failedRecords", 0L);
        mongoService.create(EQUIPMENT_UPLOAD_FILE_DETAILS, uploadFileDetail);
    }

    public void checkQueryParameter(BatchEquipmentUploadLogQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            ispService.checkIspIdPresent(queryDTO.getIspId());
        }
    }

    public void deleteBatchEquipmentUploadLog(String actionId) throws Exception {
        if (!CommonUtils.isSysAdmin() && !CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
        String userIspId = manageCommonService.getIspByGroupId(userGroupId);

        BatchEquipmentUploadLog batchEquipmentUploadLog = uploadLogRepo.getBatchEquipmentUploadById(actionId);
        if (Objects.isNull(batchEquipmentUploadLog)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Batch equipment upload log doesn't exist");
        }

        if (CommonUtils.isGroupAdmin() && !StringUtils.equals(userIspId, batchEquipmentUploadLog.getIspId())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This batch equipment upload log does not belong to the current ispId");
        }

        if (!BatchEquipmentStatus.Initial.equals(batchEquipmentUploadLog.getStatus()) &&
                !BatchEquipmentStatus.Pending.equals(batchEquipmentUploadLog.getStatus())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                    "Cannot delete log with status: " + batchEquipmentUploadLog.getStatus().name());
        }

        batchEquipmentFileService.deleteFileById(batchEquipmentUploadLog.getFileId());

        uploadLogRepo.deleteById(actionId);
    }

    private List<BatchEquipmentUploadLogDTO> populateUploadDetailsResult(
            List<BatchEquipmentUploadLogDTO> uploadLogDTOList, Integer offset, Integer limit) {
        List<String> fileIdList = uploadLogDTOList.stream()
                .map(BatchEquipmentUploadLogDTO::getFileId)
                .collect(Collectors.toList());
        ;
        DBCursor cursor = null;
        DBCollection collection = mongoTenantTemplate.getCollection(ActiontecConstants.EQUIPMENT_UPLOAD_FILE_DETAILS);
        BasicDBObject query = new BasicDBObject();
        query.put("fileId", new BasicDBObject("$in", fileIdList));

        try {
            cursor = collection.find(query).skip(offset).limit(limit);
            List<BatchEquipmentUploadFileDetailDTO> uploadDetailDTOList = new ArrayList<>();
            while (cursor.hasNext()) {
                DBObject doc = cursor.next();
                BatchEquipmentUploadFileDetailDTO detailDTO = new BatchEquipmentUploadFileDetailDTO();
                detailDTO.setId(doc.get("id").toString());
                detailDTO.setIspId(doc.get("ispId").toString());
                detailDTO.setUploadLogId(doc.get("uploadLogId").toString());
                detailDTO.setFileId(doc.get("fileId").toString());
                detailDTO.setTotalRecords(Long.parseLong(doc.get("totalRecords").toString()));
                detailDTO.setSuccessRecords(Long.parseLong(doc.get("successRecords").toString()));
                detailDTO.setFailedRecords(Long.parseLong(doc.get("failedRecords").toString()));
                uploadDetailDTOList.add(detailDTO);
            }

            for (BatchEquipmentUploadLogDTO uploadLogDTO : uploadLogDTOList) {
                BatchEquipmentUploadLogDTO.Result result = new BatchEquipmentUploadLogDTO.Result();
                Optional<BatchEquipmentUploadFileDetailDTO> detailDTOOptional = uploadDetailDTOList.stream()
                        .filter(detailDTO -> detailDTO.getUploadLogId().equals(uploadLogDTO.getId())
                                && detailDTO.getFileId().equals(uploadLogDTO.getFileId())
                                && detailDTO.getIspId().equals(uploadLogDTO.getCreator().getIspId()))
                        .findFirst();
                if (detailDTOOptional.isPresent()) {
                    BatchEquipmentUploadFileDetailDTO detailDTO = detailDTOOptional.get();
                    result.setTotal((int) detailDTO.getTotalRecords());
                    result.setComplete((int) detailDTO.getSuccessRecords());
                    result.setFailed((int) detailDTO.getFailedRecords());
                }
                uploadLogDTO.setResult(result);
            }

        } catch (Exception e) {
            logger.error("Failed to get BatchEquipmentUploadLogDetails: ", e.getMessage());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(),
                    "Failed to get BatchEquipmentUploadLogDetails");
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return uploadLogDTOList;
    }
}
