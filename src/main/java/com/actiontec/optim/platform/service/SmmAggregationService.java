package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.model.SmmAppStats;
import com.actiontec.optim.platform.repository.SmmAppStatsRepo;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class SmmAggregationService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private SmmAppStatsRepo smmAppStatsRepo;

    public List<SmmAppStats> getSmmAppStats(String scope, String scopeId) {

        String isp = null;

        if(scope.equals(ApplicationConstants.CLUSTER)) {
            if(!scopeId.equals(String.valueOf(ApplicationConstants.ZERO))) {
                String groupId = at3Adapter.getGroupIdByClusterId(scopeId);
                isp = at3Adapter.getIspNameByGroupId(groupId);
            }
        } else if(scope.equals(ApplicationConstants.GROUP)) {
            isp = at3Adapter.getIspNameByGroupId(scopeId);
        } else if(scope.equals(ApplicationConstants.ISP)) {
            isp = at3Adapter.getIspNameById(scopeId);
        } else {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get Smm App Stats Failed");
        }

        List<SmmAppStats> smmAppStatsList = smmAppStatsRepo.findSmmAppStatsByIsp(isp);
        smmAppStatsList = smmAppStatsList.stream()
                .filter(smmAppStat -> StringUtils.isNotEmpty(smmAppStat.getServiceId()))
                .collect(Collectors.toList());
        if (!smmAppStatsList.isEmpty()) {
            Collections.sort(smmAppStatsList, new Comparator<SmmAppStats>() {
                @Override
                public int compare(SmmAppStats o1, SmmAppStats o2) {
                    return o1.getServiceId().compareTo(o2.getServiceId());
                }
            });
        }

        return smmAppStatsList;
    }
}
