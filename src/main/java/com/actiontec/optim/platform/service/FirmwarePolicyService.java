package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.InternetServiceProviderDao;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.model.ActionRequest;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.model.firmware.FwPolicyBaseRequest;
import com.actiontec.optim.platform.model.firmware.FwPolicyInfo;
import com.actiontec.optim.platform.model.firmware.FwPolicyRequest;
import com.actiontec.optim.platform.model.firmware.PolicyBase;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.bson.types.ObjectId;

import java.util.*;
import java.util.stream.Collectors;

import static com.actiontec.optim.platform.constant.ApplicationConstants.FW_UPGRADE_RETRY_INTERVAL;
import static com.actiontec.optim.platform.constant.ApplicationConstants.FW_UPGRADE_RETRY_TIMES;

@Service
public class FirmwarePolicyService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private InternetServiceProviderDao internetServiceProviderDao;

    private static final String FIRMWAREUPGRADEPOLICY = "firmwareUpgradePolicy";
    private static final String FIRMWAREUPGRADEREQ = "firmwareUpgradeReq";

    private enum FirmwareJobFields {
        _id,
        status,
        executionTime,
        count,
        rate,
        sourceIspId,
        sourceModelId,
        sourceVersions,
        targetImageId,
        initCount,
        rpcSentCount,
        downloadingCount,
        updatingCount,
        completedCount,
        failedCount,
        timeoutCount,
        upgradeRoundHour,
        insertionState,
        invokingState,
        countingState,
        logs,
        retryTimes,
        retryInterval
    }

    private FwPolicyInfo toFwPolicyInfo(BasicDBObject dbObj) {
        FwPolicyInfo fwPolicyInfo = new FwPolicyInfo();
        fwPolicyInfo.setId(dbObj.getString(FirmwareJobFields._id.name()));
        fwPolicyInfo.setStatus(dbObj.getString(FirmwareJobFields.status.name(), ""));
        fwPolicyInfo.getSource().setIspId(dbObj.getString(FirmwareJobFields.sourceIspId.name()));
        fwPolicyInfo.getSource().setModelId(dbObj.getString(FirmwareJobFields.sourceModelId.name()));
        fwPolicyInfo.getSource().setVersions(objectMapper.convertValue(dbObj.get(FirmwareJobFields.sourceVersions.name()), at3Adapter.getCollectionType(List.class, String.class)));
        fwPolicyInfo.setTargetImageId(dbObj.getString(FirmwareJobFields.targetImageId.name()));
        fwPolicyInfo.setCount(dbObj.getInt(FirmwareJobFields.count.name(), 0));
        fwPolicyInfo.setRate(dbObj.getInt(FirmwareJobFields.rate.name(), 0));
        fwPolicyInfo.setExecutionTime(objectMapper.convertValue(dbObj.get(FirmwareJobFields.executionTime.name()), new TypeReference<PolicyBase.ExecutionTime>(){}));
        fwPolicyInfo.getUnitStats().setComplete(dbObj.getInt(FirmwareJobFields.completedCount.name(), 0));
        fwPolicyInfo.getUnitStats().setFailed(dbObj.getInt(FirmwareJobFields.failedCount.name(), 0) + dbObj.getInt(FirmwareJobFields.timeoutCount.name(), 0));
        int initCount = dbObj.getInt(FirmwareJobFields.initCount.name(), 0);
        int rpcSentCount = dbObj.getInt(FirmwareJobFields.rpcSentCount.name(), 0);
        int downloadingCount = dbObj.getInt(FirmwareJobFields.rpcSentCount.name(), 0);
        int updatingCount = dbObj.getInt(FirmwareJobFields.rpcSentCount.name(), 0);
        fwPolicyInfo.getUnitStats().setProcessing(initCount+rpcSentCount+downloadingCount+updatingCount);
        fwPolicyInfo.getRetry().setCount(dbObj.getInt(FirmwareJobFields.retryTimes.name(), FW_UPGRADE_RETRY_TIMES));
        fwPolicyInfo.getRetry().setInterval(dbObj.getInt(FirmwareJobFields.retryInterval.name(), FW_UPGRADE_RETRY_INTERVAL));

        return fwPolicyInfo;
    }

    private void checkPermission(String sourceIspId, String policyId) {
        boolean pass = false;
        if (CommonUtils.isSysAdmin() == true) {
            pass = true;
        } else if (CommonUtils.isGroupAdmin() == true) {
            String ispId = sourceIspId;
            String allIspId = internetServiceProviderDao.getIspIdByName(ApplicationConstants.ALL_ISP);
            if (Objects.nonNull(policyId)) {
                DBObject query = new BasicDBObject();
                query.put(FirmwareJobFields._id.name(), new ObjectId(policyId));
                BasicDBObject firmwarePolicy = (BasicDBObject) at3Adapter.getMongoDbCollection(FIRMWAREUPGRADEPOLICY).findOne(query);
                if (Objects.isNull(firmwarePolicy))
                    throw new OptimApiException(HttpStatus.BAD_REQUEST, "No such id for Policy.");
                else
                    ispId = firmwarePolicy.getString(FirmwareJobFields.sourceIspId.name(), "");

            }

            if (StringUtils.equals(ispId, at3Adapter.getIspId()) || StringUtils.equals(ispId, allIspId)) {
                pass = true;
            }
        }

        if (pass == false)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The user can't handle this policy.");
    }

    public List<FwPolicyInfo> getPolicy(String policyId) throws Exception {
        Map<String, FwPolicyInfo> policyInfoMap = new HashMap<>();
        List<DBObject> aggregatePipeline = new ArrayList<>();
        BasicDBObject query = new BasicDBObject();
        if (Objects.isNull(policyId)) {
            if (CommonUtils.isGroupAdmin() == true) {
                String userIspId = at3Adapter.getIspId();
                String allIspId = internetServiceProviderDao.getIspIdByName(ApplicationConstants.ALL_ISP);
                query.put("$or", Arrays.asList(
                        new BasicDBObject(FirmwareJobFields.sourceIspId.name(), userIspId),
                        new BasicDBObject(FirmwareJobFields.sourceIspId.name(), allIspId)
                ));
            } else if (CommonUtils.isSysAdmin() == false) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The user can't handle policy any operation.");
            }
            query.put(FirmwareJobFields._id.name(), new BasicDBObject("$exists", true));
        }
        else {
            checkPermission(null, policyId);
            query.put(FirmwareJobFields._id.name(), new ObjectId(policyId));
        }
        DBCursor dbCursor = at3Adapter.getMongoDbCollection(FIRMWAREUPGRADEPOLICY).find(query, new BasicDBObject());
        while (dbCursor.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) dbCursor.next();
            policyInfoMap.put(resultObject.getString(FirmwareJobFields._id.name()), toFwPolicyInfo(resultObject));
        }

        if (Objects.isNull(policyId)) {
            String pipelineQuery0 = "    { " +
                    "        $match: {" +
                    "            'policyId': { " +
                    "                '$exists': true " +
                    "            }" +
                    "        }    " +
                    "    }";
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery0)));
        } else {
            String pipelineQuery0 = " {$match:{policyId: '%s'}}";
            aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery0, policyId)));
        }
        String pipelineQuery1 = "    { " +
                "        $match: {" +
                "            'faultCode': { " +
                "                '$exists': true " +
                "            }" +
                "        }    " +
                "    }";
        String pipelineQuery2 = " { $group: " +
                "   {" +
                " _id: {policyId:'$policyId', code:'$faultCode'}," +
                "   count: {$sum: 1}" +
                "   }" +
                "  }";
        String pipelineQuery3 = "{$project: " +
                " {_id:0, policyId:'$_id.policyId',code:'$_id.code', count:1}" +
                "  }";
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery1)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery2)));
        aggregatePipeline.add(BasicDBObject.parse(String.format(pipelineQuery3)));
        Iterator iterator = at3Adapter.getMongoDbCollection(FIRMWAREUPGRADEREQ).aggregate(aggregatePipeline).results().iterator();
        while (iterator.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) iterator.next();
            FwPolicyInfo fwPolicyInfo = policyInfoMap.get(resultObject.getString("policyId"));
            if (Objects.nonNull(fwPolicyInfo)) {
                fwPolicyInfo.getFailureStats().add(new FwPolicyInfo.FailureStats(resultObject.getInt("code"), resultObject.getInt("count")));
                policyInfoMap.put(resultObject.getString("policyId"), fwPolicyInfo);
            }
        }

        return policyInfoMap.values().stream().sorted(Comparator.comparing(FwPolicyInfo::getUpdatedAt).reversed()).collect(Collectors.toList());
    }

    public Map<String, String> postPolicy(FwPolicyRequest request) throws Exception {
        checkPermission(request.getSource().getIspId(), null);

        Map<String, String> responseMap = new HashMap<>();
        BasicDBObject params = new BasicDBObject();
        params.put(FirmwareJobFields._id.name(), new ObjectId());
        params.put(FirmwareJobFields.status.name(), "Stopped");
        params.put(FirmwareJobFields.count.name(), request.getCount());
        params.put(FirmwareJobFields.rate.name(), request.getRate());
        if (Objects.nonNull(request.getExecutionTime()))
            params.put(FirmwareJobFields.executionTime.name(), at3Adapter.getDbObject(request.getExecutionTime()));
        params.put(FirmwareJobFields.targetImageId.name(), request.getTargetImageId());
        if (Objects.nonNull(request.getSource()))
        {
            if (Objects.nonNull(request.getSource().getIspId()))
                params.put(FirmwareJobFields.sourceIspId.name(), request.getSource().getIspId());
            if (Objects.nonNull(request.getSource().getModelId()))
                params.put(FirmwareJobFields.sourceModelId.name(), request.getSource().getModelId());
            if (Objects.nonNull(request.getSource().getVersions()))
                params.put(FirmwareJobFields.sourceVersions.name(), request.getSource().getVersions());
        }
        if (Objects.nonNull(request.getRetry())) {
            params.put(FirmwareJobFields.retryTimes.name(), request.getRetry().getCount());
            params.put(FirmwareJobFields.retryInterval.name(), request.getRetry().getInterval());
        }
        params.put(FirmwareJobFields.insertionState.name(), "Inactive");
        params.put(FirmwareJobFields.invokingState.name(), "Inactive");
        params.put(FirmwareJobFields.countingState.name(), "Inactive");
        params.put(FirmwareJobFields.upgradeRoundHour.name(), 0);
        final BasicDBObject logObject = new BasicDBObject();
        logObject.put("action", "Init");
        logObject.put("requestTime", CommonUtils.getCurrentTimeInMillis());
        ArrayList< DBObject > array = new ArrayList< DBObject >();
        array.add(logObject);
        params.put(FirmwareJobFields.logs.name(), array);

        at3Adapter.insertDataInCollection(FIRMWAREUPGRADEPOLICY, params);
        responseMap.put("id", params.get(FirmwareJobFields._id.name()).toString());
        return responseMap;
    }

    public void putPolicy(String policyId, FwPolicyBaseRequest request) throws Exception {
        DBObject query = new BasicDBObject();
        checkPermission(null, policyId);
        query.put(FirmwareJobFields._id.name(), new ObjectId(policyId));

        BasicDBObject projection = new BasicDBObject();
        projection.put(FirmwareJobFields.status.name(), 1);
        BasicDBObject firmwarePolicy = (BasicDBObject) at3Adapter.getMongoDbCollection(FIRMWAREUPGRADEPOLICY).findOne(query, projection);
        String status = firmwarePolicy.getString(FirmwareJobFields.status.name(), "");
        if (status.equals("Complete") || status.equals("Canceled")) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid action to handle the Complete or Canceled policy.");
        }

        BasicDBObject params = new BasicDBObject();
        params.put(FirmwareJobFields.count.name(), request.getCount());
        params.put(FirmwareJobFields.rate.name(), request.getRate());
        params.put(FirmwareJobFields.upgradeRoundHour.name(), 0);
        if (Objects.nonNull(request.getExecutionTime()))
            params.put(FirmwareJobFields.executionTime.name(), at3Adapter.getDbObject(request.getExecutionTime()));
        if (Objects.nonNull(request.getRetry())) {
            params.put(FirmwareJobFields.retryTimes.name(), request.getRetry().getCount());
            params.put(FirmwareJobFields.retryInterval.name(), request.getRetry().getInterval());
        }
        DBObject logItem = new BasicDBObject(FirmwareJobFields.logs.name(), new BasicDBObject("action","Update").append("requestTime",CommonUtils.getCurrentTimeInMillis()));
        BasicDBObject updatedDBObject = new BasicDBObject();
        updatedDBObject.put("$push", logItem);
        updatedDBObject.put("$set", params);

        at3Adapter.findAndModifyCollection((HashMap<String, Object>) query, FIRMWAREUPGRADEPOLICY, null, updatedDBObject, false);
    }

    public void removePolicy(String policyId) throws Exception{
        checkPermission(null, policyId);

        DBObject query = new BasicDBObject();
        query.put(FirmwareJobFields._id.name(), new ObjectId(policyId));
        at3Adapter.getMongoDbCollection(FIRMWAREUPGRADEPOLICY).remove(query);
    }

    public String postActions(String policyId, ActionRequest request) throws Exception {
        DBObject query = new BasicDBObject();
        checkPermission(null, policyId);
        query.put(FirmwareJobFields._id.name(), new ObjectId(policyId));

        BasicDBObject projection = new BasicDBObject();
        projection.put(FirmwareJobFields.status.name(), 1);
        BasicDBObject firmwarePolicy = (BasicDBObject) at3Adapter.getMongoDbCollection(FIRMWAREUPGRADEPOLICY).findOne(query, projection);
        String status = firmwarePolicy.getString(FirmwareJobFields.status.name(), "");
        if (status.equals("Complete") || status.equals("Canceled")) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid action to handle the Complete or Canceled policy.");
        }

        String action = request.getAction();
        String dediredStatus = null;
        if (StringUtils.equals(status, "Active")) {
            if (StringUtils.equals(action, "Stop"))
                dediredStatus = "Stopped";
            else if (StringUtils.equals(action, "Cancel"))
                dediredStatus = "Canceled";
        }
        else if (StringUtils.equals(status, "Stopped")) {
            if (StringUtils.equals(action, "Cancel"))
                dediredStatus = "Canceled";
            else if (StringUtils.equals(action, "Start"))
                dediredStatus = "Active";
        }

        if (StringUtils.isBlank(dediredStatus))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid action to handle FW policy.");

        BasicDBObject params = new BasicDBObject();
        params.put(FirmwareJobFields.status.name(), dediredStatus);
        DBObject logItem = new BasicDBObject(FirmwareJobFields.logs.name(), new BasicDBObject("action", action).append("requestTime",CommonUtils.getCurrentTimeInMillis()));
        BasicDBObject updatedDBObject = new BasicDBObject();
        updatedDBObject.put("$push", logItem);
        updatedDBObject.put("$set", params);

        at3Adapter.findAndModifyCollection((HashMap<String, Object>) query, FIRMWAREUPGRADEPOLICY, null, updatedDBObject, false);
        return policyId;
    }

    public List<Map<String, Object>> getLogs(String policyId) {
        DBObject query = new BasicDBObject();
        checkPermission(null, policyId);
        query.put(FirmwareJobFields._id.name(), new ObjectId(policyId));

        BasicDBObject projection = new BasicDBObject();
        projection.put(FirmwareJobFields.logs.name(), 1);
        BasicDBObject firmwarePolicy = (BasicDBObject) at3Adapter.getMongoDbCollection(FIRMWAREUPGRADEPOLICY).findOne(query, projection);

        return Objects.nonNull(firmwarePolicy.get(FirmwareJobFields.logs.name())) ? (List<Map<String, Object>>) firmwarePolicy.get(FirmwareJobFields.logs.name()) : new ArrayList<>();
    }
}
