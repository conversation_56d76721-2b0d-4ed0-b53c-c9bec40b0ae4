package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.repository.AppCloudRepo;
import com.incs83.app.entities.AppCloudConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AppCloudService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private AppCloudRepo appCloudRepo;

    /**
     * Get App Cloud configuration by kid
     */
    public AppCloudConfig getAppCloudConfigByKid(String kid) throws Exception {
        return appCloudRepo.getAppCloudConfigByKid(kid);
    }

    /**
     * Create App Cloud configuration
     */
    public boolean createAppCloudConfig(AppCloudConfig appCloudConfig) throws Exception {
        return appCloudRepo.create(appCloudConfig);
    }

    /**
     * Update App Cloud configuration
     */
    public void updateAppCloudConfig(AppCloudConfig appCloudConfig) throws Exception {
        appCloudRepo.update(appCloudConfig);
    }
}
