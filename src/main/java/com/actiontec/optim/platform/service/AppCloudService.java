package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.repository.AppCloudRepo;
import com.incs83.app.entities.AppCloudConfig;
import com.incs83.exceptions.handler.ValidationException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Date;

@Service
public class AppCloudService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private AppCloudRepo appCloudRepo;

    // 測試用的硬編碼RSA Public Key
    private static final String TEST_RSA_PUBLIC_KEY = 
        "-----BEGIN PUBLIC KEY-----\n" +
        "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4f5wg5l2hKsTeNem/V41\n" +
        "fGnJm6gOdrj8ym3rFkEjWT2btf+FxKaGZSoOq8qKwvXDsOLrJhVhsxzxe8KZqNQ5\n" +
        "qjpqUHdwBzWP/31Oqf5Lg5p2hKsTeNem/V41fGnJm6gOdrj8ym3rFkEjWT2btf+F\n" +
        "xKaGZSoOq8qKwvXDsOLrJhVhsxzxe8KZqNQ5qjpqUHdwBzWP/31Oqf5Lg5p2hKsT\n" +
        "eNem/V41fGnJm6gOdrj8ym3rFkEjWT2btf+FxKaGZSoOq8qKwvXDsOLrJhVhsxzx\n" +
        "e8KZqNQ5qjpqUHdwBzWP/31Oqf5Lg5p2hKsTeNem/V41fGnJm6gOdrj8ym3rFkEj\n" +
        "WT2btf+FxKaGZSoOq8qKwvXDsOLrJhVhsxzxe8KZqNQ5qjpqUHdwBzWP/31Oqf5L\n" +
        "QIDAQAB\n" +
        "-----END PUBLIC KEY-----";

    /**
     * 驗證App Cloud JWT Token
     * @param authorizationHeader Authorization header值
     * @return 驗證是否成功
     */
    public boolean validateAppCloudJwtToken(String authorizationHeader) {
        try {
            // 1. 檢查Authorization header格式
            String token = extractTokenFromHeader(authorizationHeader);
            if (StringUtils.isBlank(token)) {
                logger.debug("Invalid authorization header format");
                return false;
            }

            // 2. 解析JWT header獲取kid
            String kid = extractKidFromToken(token);
            if (StringUtils.isBlank(kid)) {
                logger.debug("No kid found in JWT header");
                return false;
            }

            // 3. 根據kid獲取公鑰配置（目前使用測試公鑰）
            PublicKey publicKey = getPublicKeyForValidation(kid);
            if (publicKey == null) {
                logger.debug("No public key found for kid: {}", kid);
                return false;
            }

            // 4. 驗證JWT token
            Jws<Claims> jwsClaims = Jwts.parserBuilder()
                    .setSigningKey(publicKey)
                    .build()
                    .parseClaimsJws(token);

            // 5. 檢查token是否過期
            if (isTokenExpired(jwsClaims.getBody().getExpiration())) {
                logger.debug("Token expired, token: {}, expiration: {}", token, jwsClaims.getBody().getExpiration());
                return false;
            }

            logger.info("App Cloud JWT token validation successful for kid: {}", kid);
            return true;

        } catch (Exception e) {
            logger.debug("App Cloud JWT token validation failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 從Authorization header中提取token
     */
    private String extractTokenFromHeader(String authorizationHeader) {
        if (StringUtils.isBlank(authorizationHeader)) {
            return null;
        }

        String[] parts = authorizationHeader.split(" ");
        if (parts.length == 2 && "Bearer".equalsIgnoreCase(parts[0])) {
            return parts[1];
        }

        return null;
    }

    /**
     * 從JWT token中提取kid
     */
    private String extractKidFromToken(String token) {
        try {
            // 解析JWT header (不驗證簽名)
            String[] chunks = token.split("\\.");
            if (chunks.length < 2) {
                return null;
            }

            String header = new String(Base64.getUrlDecoder().decode(chunks[0]));
            // 簡單的JSON解析獲取kid (實際項目中可能需要使用JSON庫)
            if (header.contains("\"kid\"")) {
                int kidStart = header.indexOf("\"kid\"") + 6;
                int valueStart = header.indexOf("\"", kidStart) + 1;
                int valueEnd = header.indexOf("\"", valueStart);
                if (valueEnd > valueStart) {
                    return header.substring(valueStart, valueEnd);
                }
            }
        } catch (Exception e) {
            logger.debug("Failed to extract kid from token: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 獲取用於驗證的公鑰
     * 目前使用測試公鑰，未來可以從數據庫獲取
     */
    private PublicKey getPublicKeyForValidation(String kid) {
        try {
            // TODO: 未來從數據庫獲取
            // AppCloudConfig config = appCloudRepo.getAppCloudConfigByKid(kid);
            // return parsePublicKey(config.getPublicKeyPem());

            // 目前使用測試公鑰
            return parsePublicKey(TEST_RSA_PUBLIC_KEY);
        } catch (Exception e) {
            logger.debug("Failed to get public key for kid: {}, error: {}", kid, e.getMessage());
            return null;
        }
    }

    /**
     * 解析PEM格式的公鑰
     */
    private PublicKey parsePublicKey(String publicKeyPem) throws Exception {
        String publicKeyContent = publicKeyPem
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(publicKeyContent);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return keyFactory.generatePublic(spec);
    }

    /**
     * 檢查token是否過期
     */
    private boolean isTokenExpired(Date expiration) {
        if (expiration == null) {
            return true;
        }
        return expiration.before(new Date());
    }

    /**
     * 根據kid獲取App Cloud配置
     */
    public AppCloudConfig getAppCloudConfigByKid(String kid) throws Exception {
        return appCloudRepo.getAppCloudConfigByKid(kid);
    }
}
