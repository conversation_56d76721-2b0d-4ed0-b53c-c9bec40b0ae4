package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.AppCloudTokenDTO;
import com.actiontec.optim.platform.api.v6.tokenFactory.AppCloudJwtTokenFactory;
import com.actiontec.optim.platform.api.v6.validator.AppCloudJwtTokenValidator;
import com.actiontec.optim.platform.repository.AppCloudRepo;
import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.entities.AppCloudConfig;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.security.auth.jwt.extractor.TokenExtractor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Service
public class AppCloudService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private AppCloudJwtTokenValidator appCloudJwtValidator;

    @Autowired
    private AppCloudJwtTokenFactory appCloudJwtTokenFactory;

    @Autowired
    private TokenExtractor tokenExtractor;

    @Autowired
    private AppCloudRepo appCloudRepo;

    @Autowired
    private ObjectMapper objectMapper;

    public AppCloudTokenDTO exchangeToken(String accessToken) throws Exception {
        AppCloudTokenDTO response;
        String tokenValue;
        try {
            // Extract token from request using TokenExtractor
            tokenValue = tokenExtractor.extract(accessToken);
        } catch (AuthenticationServiceException e) {
            // Return 400 if Authorization header is missing or malformed
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Malformed or missing Authorization header.");
        }

        // Check if token is empty or null
        if (CustomStringUtils.isEmpty(tokenValue)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(),"Token is missing or empty.");
        }

        // Extract 'kid' from JWT token header
        String kid = AppCloudJwtTokenUtils.extractKid(tokenValue);
        if (CustomStringUtils.isEmpty(kid)) {
            throw new ValidationException(HttpStatus.UNAUTHORIZED.value(), "Missing kid in JWT header.");
        }

        // Retrieve the public key corresponding to the 'kid'
        AppCloudConfig appCloudConfig = getAppCloudConfigByKid(kid);
        if (CustomStringUtils.isNotEmpty(appCloudConfig.getExchangeToken()) &&
                appCloudConfig.getExpiredAt().isAfter(LocalDateTime.now(ZoneOffset.UTC))) {
            response = new AppCloudTokenDTO(appCloudConfig.getExchangeToken());
        } else {
            // Validate the JWT token with the public key
            if (!appCloudJwtValidator.verifyUnAuthorizationToken(tokenValue, appCloudConfig)) {
                // Return 401 if token validation fails
                throw new ValidationException(HttpStatus.UNAUTHORIZED.value(),"JWT validation failed.");
            }

            // Token validated successfully, update app_cloud_config
            response = appCloudJwtTokenFactory.createAppCloudToken(appCloudConfig);
            appCloudConfig.setExchangeToken(response.getExchangeToken());
            appCloudConfig.setExpiredAt(LocalDateTime.now(ZoneOffset.UTC).plusYears(1));
            appCloudRepo.update(appCloudConfig);
        }

        return response;
    }




    /**
     * Get App Cloud configuration by kid
     */
    public AppCloudConfig getAppCloudConfigByKid(String kid) throws Exception {
        return appCloudRepo.getAppCloudConfigByKid(kid);
    }

    /**
     * Create App Cloud configuration
     */
    public boolean createAppCloudConfig(AppCloudConfig appCloudConfig) throws Exception {
        return appCloudRepo.create(appCloudConfig);
    }

    /**
     * Update App Cloud configuration
     */
    public void updateAppCloudConfig(AppCloudConfig appCloudConfig) throws Exception {
        appCloudRepo.update(appCloudConfig);
    }
}
