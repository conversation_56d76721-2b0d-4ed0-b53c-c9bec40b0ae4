package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.NCSSubscriberQueryDTO;
import com.actiontec.optim.platform.repository.*;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.NewManageEquipmentService;
import com.incs83.app.business.v2.NewManageSubscriberService;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.*;
import com.actiontec.optim.platform.api.v6.dto.NCSSubscriberDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.business.ESService;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.AP_DETAIL;
import static com.incs83.constants.ApplicationCommonConstants.SUBSCRIBER_INDEX;

@Service
public class NCSSubscriberService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    ManageCommonService manageCommonService;

    @Autowired
    NewManageSubscriberService newManageSubscriberService;

    @Autowired
    NewManageEquipmentService newManageEquipmentService;

    @Autowired
    private NCSIspService ispService;

    @Autowired
    CompartmentRepo compartmentRepo;

    @Autowired
    NCSSubscriberRepo ncsSubscriberRepo;

    @Autowired
    NCSEquipmentService ncsEquipmentService;

    @Autowired
    SubscriberNetworkRepo subscriberNetworkRepo;

    @Autowired
    LoginDetailsRepo loginDetailsRepo;

    @Autowired
    private MongoServiceImpl mongoServiceImpl;

    @Autowired
    private ESService esService;

    @Value("${elastic-search.enable}")
    Boolean elasticSearchEnable;


    public PaginationResponse<NCSSubscriberDTO> getAllSubscribers(NCSSubscriberQueryDTO queryDTO) throws Exception {
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if(CommonUtils.isGroupAdmin()) {
            manageCommonService.checkIspIdForGroupAdmin(queryDTO.getIspId());

            // group admin may not bring ispId parameter, default set query ispId by login user ispId.
            if (StringUtils.isEmpty(queryDTO.getIspId())) {
                String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
                String userIspId = manageCommonService.getIspByGroupId(userGroupId);
                queryDTO.setIspId(userIspId);
            }
        }
        checkQueryParameter(queryDTO);

        return ncsSubscriberRepo.getAllSubscribers(queryDTO);
    }

    public NCSSubscriberDTO getSubscriber(String subscriberId) throws Exception {
        if (!CommonUtils.isSysAdmin() && !CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        return ncsSubscriberRepo.getSubscriberDTOById(subscriberId);
    }

    public Map<String, String> createSubscriber(NCSSubscriberDTO subscriberData) throws Exception {
        String userIspId;
        if (CommonUtils.isSysAdmin()) {
            if (CustomStringUtils.isEmpty(subscriberData.getIspId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP ID cannot be empty");
            }
            userIspId = subscriberData.getIspId();
        } else if (CommonUtils.isGroupAdmin()) {
            String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
            userIspId = manageCommonService.getIspByGroupId(userGroupId);
            if(CustomStringUtils.isEmpty(userGroupId) || CustomStringUtils.isEmpty(userIspId)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot find group_id or isp_id for logged in user");
            }
        } else {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        Set<Compartment> compartmentSet = new HashSet<>();
        List<Compartment> compartmentList = compartmentRepo.getCompartmentByIspId(userIspId);
        compartmentSet.add(compartmentList.get(0));

        Subscriber subscriber = generateSubscriberInstance(subscriberData, compartmentSet);
        subscriber.setActive(true);
        HashMap<String, String> result = new HashMap<>();
        try {
            boolean isCreated = ncsSubscriberRepo.create(subscriber);
            if(isCreated) {
                if (elasticSearchEnable) {
                    try {
                        HashMap<String, Object> subscriberMap = prepareDataForESSubscriber(subscriber);
                        esService.insert(SUBSCRIBER_INDEX, subscriberMap, String.valueOf(subscriberMap.get("id")), null);
                    } catch (Exception e) {
                        logger.error("Error in inserting subscriber in ES");
                    }
                }

                LoginDetails loginDetails = new LoginDetails();
                loginDetails.setId(subscriber.getId());
                loginDetails.setSubscriber(true);
                loginDetails.setActive(true);
                loginDetails.setToken(null);
                loginDetails.setTokenCreatedAt(null);
                loginDetails.setTokenUpdatedAt(null);
                loginDetails.setTokenType(SUBSCRIBER);
                CommonUtils.setCreateEntityFields(loginDetails);
                loginDetailsRepo.create(loginDetails);

                result.put("id", subscriber.getId());
            }

        } catch (Exception e) {
            logger.error("Error in creating subscriber (insertToMysqlAndElastic) :: " + e.getMessage());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in creating subscriber");
        }

        return result;
    }

    private Subscriber generateSubscriberInstance(NCSSubscriberDTO createSubscriberRequest, Set<Compartment> compartmentSet) throws Exception {
        Subscriber subscriber = new Subscriber();
        subscriber.setId(CommonUtils.generateUUID());
        subscriber.setFirstName(createSubscriberRequest.getFirstName());
        subscriber.setLastName(createSubscriberRequest.getLastName());
        subscriber.setCompartment(compartmentSet);
        subscriber.setSubscriberRole(newManageSubscriberService.getEndSubscriberRole());
        subscriber.setPassword(USER_PASSWORD);
        CommonUtils.setCreateEntityFields(subscriber);
        return subscriber;
    }

    private HashMap<String, Object> prepareDataForESSubscriber(Subscriber subscriber) {
        HashMap<String, Object> subscriberMap = new HashMap<>();
        subscriberMap.put("id", subscriber.getId());
        subscriberMap.put("createdAt", subscriber.getCreatedAt());
        subscriberMap.put("createdBy", subscriber.getCreatedBy());
        subscriberMap.put("name", subscriber.getFirstName() + " " + subscriber.getLastName());
        subscriberMap.put("groupId", subscriber.getCompartment().iterator().next().getId());
        subscriberMap.put("groupName", subscriber.getCompartment().iterator().next().getName());
        subscriberMap.put("isActive", subscriber.isActive());
        return subscriberMap;
    }

    public void updateSubscriber(NCSSubscriberDTO subscriberData) throws Exception {
        String userIspId;
        if (CommonUtils.isSysAdmin()) {
            if (CustomStringUtils.isEmpty(subscriberData.getIspId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP ID cannot be empty");
            }
            userIspId = subscriberData.getIspId();
        } else if (CommonUtils.isGroupAdmin()) {
            String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
            userIspId = manageCommonService.getIspByGroupId(userGroupId);
            if(CustomStringUtils.isEmpty(userGroupId) || CustomStringUtils.isEmpty(userIspId)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot find group_id or isp_id for logged in user");
            }
        } else {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        if (StringUtils.isEmpty(subscriberData.getFirstName()) || StringUtils.isEmpty(subscriberData.getLastName())
                || StringUtils.isEmpty(subscriberData.getIspId())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Subscriber request is invalid");
        }

        if (StringUtils.isEmpty(subscriberData.getFirstName()) || StringUtils.isEmpty(subscriberData.getLastName())
                || StringUtils.isEmpty(subscriberData.getIspId())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Subscriber request is invalid");
        }

        Subscriber subscriber = ncsSubscriberRepo.getSubscriberById(subscriberData.getId());
        if (Objects.isNull(subscriber) || !subscriber.getId().equals(subscriberData.getId())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Subscriber doesn't exists");
        }

        Set<Compartment> compartmentSet = new HashSet<>();
        List<Compartment> compartmentList = compartmentRepo.getCompartmentByIspId(userIspId);
        compartmentSet.add(compartmentList.get(0));

        subscriber.setFirstName(subscriberData.getFirstName());
        subscriber.setLastName(subscriberData.getLastName());
        subscriber.setCompartment(compartmentSet);
        CommonUtils.setUpdateEntityFields(subscriber);
        ncsSubscriberRepo.update(subscriber);

        // update es cache
        esService.updateById(SUBSCRIBER_INDEX, prepareDataForESSubscriber(subscriber), subscriber.getId(), null);
    }

    // need to delete old equipment table data, clean ncs_equipment subscriber and network mapping, clean mongo apDetail
    public void deleteSubscriber(String subscriberId) throws Exception {
        if (!CommonUtils.isSysAdmin() && !CommonUtils.isGroupAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        NCSSubscriberDTO subscriberDTO = ncsSubscriberRepo.getSubscriberDTOById(subscriberId);

        if (CommonUtils.isGroupAdmin()) {
            String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
            String userIspId = manageCommonService.getIspByGroupId(userGroupId);
            if (!CustomStringUtils.equals(userIspId, subscriberDTO.getIspId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group admins can only delete their own subscriber.");
            }
        }

        if(Objects.nonNull(subscriberDTO)) {
            ncsEquipmentService.deleteOldEquipmentBySubscriberId(subscriberId);
            ncsEquipmentService.clearSubscriberMappingBySubscriberId(subscriberId);
            List<SubscriberNetwork> subscriberNetworkList = subscriberNetworkRepo.getSubscriberNetworkBySubscriberId(subscriberId);
            if(CollectionUtils.isNotEmpty(subscriberNetworkList)) {
                for (SubscriberNetwork subscriberNetwork: subscriberNetworkList) {
                    subscriberNetworkRepo.deleteBySubscriberId(subscriberNetwork.getSubscriberId());
                    ncsEquipmentService.clearNetworkMappingByNetworkId(subscriberNetwork.getNetworkId());
                }
            }

            ncsSubscriberRepo.deleteById(subscriberId);

            // clean login data and cache
            try {
                LoginDetails loginDetails = loginDetailsRepo.getLoginDetailsById(subscriberId);
                if(Objects.nonNull(loginDetails)) {
                    loginDetailsRepo.deleteById(subscriberId);
                    esService.delete(SUBSCRIBER_INDEX, subscriberId);
                }
            } catch (Exception ex) {
                // do not need to throw exception
                logger.error("delete login details fail or delete from es failed.");
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Subscriber doesn't exists");
        }
    }

    public void checkQueryParameter(NCSSubscriberQueryDTO queryDTO) {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            ispService.checkIspIdPresent(queryDTO.getIspId());
        }
    }
}
