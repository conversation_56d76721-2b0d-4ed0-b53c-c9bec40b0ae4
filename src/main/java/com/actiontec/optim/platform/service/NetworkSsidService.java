package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.OperationHandle;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.model.RadioRequest;
import com.actiontec.optim.platform.api.v5.model.SsidRequest;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.constant.OperationConstants;
import com.actiontec.optim.platform.exception.CpeRpcResultException;
import com.actiontec.optim.platform.model.NetworkSsid;
import com.actiontec.optim.platform.model.NetworkSsidVirtualKey;
import com.actiontec.optim.platform.model.SsidCompositeKey;
import com.actiontec.optim.platform.repository.NetworkSsidRepo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.SimpleRpcService;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.THREAD_TO_SLEEP;

@Service
public class NetworkSsidService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private NetworkSsidRepo networkSsidRepo;

    @Autowired
    private SimpleAesEcbCryptoService simpleAesEcbCryptoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SimpleRpcService cpeRpcService;

    public List<NetworkSsid> findByStnAndEquipmentId(String stn, String equipmentId, boolean alignment) throws Exception {
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, equipment);

        List<NetworkSsid> networkSsids = networkSsidRepo.findByUserIdAndSerialNumber(equipment.getRgwSerial(),equipmentId);

        networkSsids = excludeUndefined(networkSsids);
        Boolean smartSteeringEnable = manageCommonService.isSmartSteeringEnabledForDeviceMac(equipment.getRgwSerial(), equipmentId);
        if (smartSteeringEnable && alignment) {
            networkSsids = mergeSsids(networkSsids);
        }

        for (NetworkSsid networkSsid : networkSsids) {
            networkSsid.genVirtualKey(simpleAesEcbCryptoService);
            networkSsid.genRadioVirtualKeys(simpleAesEcbCryptoService);
        }

        return networkSsids;
    }

    public NetworkSsid findByStnEquipmentIdAndVirtualkey(String stn, String equitmentId, String virtualKey) throws Exception {
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        NetworkSsidVirtualKey networkSsidVirtualKey = NetworkSsidVirtualKey.fromVirtualKeyString(simpleAesEcbCryptoService, virtualKey);
        String serialNumber = networkSsidVirtualKey.getSerial();

        manageCommonService.checkAPSerialNumBelongsToSubscriber(equitmentId, equipment);
        if (!StringUtils.equals(equitmentId, serialNumber)) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Invalid ssid Id for the equipment.");
        }

        List<NetworkSsid> matchedNetworkSsids = new ArrayList<>();
        SsidCompositeKey[] ssidCompositeKeys = networkSsidVirtualKey.getSsidCompositeKeys();
        List<NetworkSsid> networkSsids = networkSsidRepo.findByUserIdAndSerialNumber(equipment.getRgwSerial(), serialNumber);
        for (NetworkSsid networkSsid : networkSsids) {
            for (SsidCompositeKey ssidCompositeKey : ssidCompositeKeys) {
                if (StringUtils.equals(ssidCompositeKey.getRadioKey(), networkSsid.getRadioEnums().get(0).getRadioKey())
                        && StringUtils.equals(ssidCompositeKey.getSsidKey(), networkSsid.getSsidKey())) {
                    matchedNetworkSsids.add(networkSsid);
                }
            }
        }

        Boolean smartSteeringEnable = manageCommonService.isSmartSteeringEnabledForDeviceMac(equipment.getRgwSerial(), equitmentId);
        List<NetworkSsid> mergedNetworkSsids = matchedNetworkSsids;
        if (smartSteeringEnable)
            mergedNetworkSsids = mergeSsids(mergedNetworkSsids);
        NetworkSsid result = null;
        if (mergedNetworkSsids.size() == 1) {
            result = mergedNetworkSsids.get(0);
            result.genVirtualKey(simpleAesEcbCryptoService);
            result.genRadioVirtualKeys(simpleAesEcbCryptoService);
        }
        return result;
    }

    @PermissionHandle
    @OperationHandle(operation = OperationConstants.WIFI_OP)
    public void putSsidByEquipimentId(String stn, String equipmentId, String virtualKey, SsidRequest request) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        NetworkSsidVirtualKey networkSsidVirtualKey = NetworkSsidVirtualKey.fromVirtualKeyString(simpleAesEcbCryptoService, virtualKey);

        if (!StringUtils.equals(equipmentId, networkSsidVirtualKey.getSerial())) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Invalid ssidId.");
        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        String isp = at3Adapter.getIspNameByGroupId(equipment.getGroupId());

        List<Object> requestList = new ArrayList<>();
        SsidCompositeKey[] ssidCompositeKeys = networkSsidVirtualKey.getSsidCompositeKeys();
        for (SsidCompositeKey ssidCompositeKey : ssidCompositeKeys) {
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("enabled", request.isEnabled());
            payloadMap.put("ssid", request.getName());
            payloadMap.put("password", request.getPassword());

            HashMap<String, Object> data = new HashMap<>();
            data.put("uri", String.format(RpcConstants.SSID_ACTION_URI, ssidCompositeKey.getRadioKey(), ssidCompositeKey.getSsidKey()));
            data.put("method", "PUT");
            data.put("payload", payloadMap);

            requestList.add(data);
        }

        String payload = objectMapper.writeValueAsString(requestList);
        String tid = CommonUtils.generateUUID();

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(LONG_RPC_POLL_COUNT));
        Map<String, Object> rpcResult = cpeRpcService.sendRpc31AndWaitResult(tid, userId, networkSsidVirtualKey.getSerial(), isp, payload, max_Tries, THREAD_TO_SLEEP);
        List<HashMap<String, Object>> responseList = null;
        if (rpcResult.get("response") == null) {
            throw new CpeRpcResultException(CpeRpcResultException.FailedReason.FAILED_RESPONSE);

        } else {
            responseList = objectMapper.convertValue(rpcResult.get("response"), new TypeReference<List<HashMap<String, Object>>>() {});
        }

        for (HashMap<String, Object> response: responseList) {
            if (Integer.valueOf(String.valueOf(response.get("code"))) != 200)
                throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get bad response from response.");
        }
    }

    public List<NetworkSsid> findByStn(String stn, boolean alignment) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(stn);

        List<NetworkSsid> networkSsids = networkSsidRepo.findByUserId(userId);
        networkSsids = excludeUndefined(networkSsids);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);

        Boolean smartSteeringEnable = manageCommonService.isSmartSteeringEnabledForDeviceMac(userId, serial);
        if (smartSteeringEnable && alignment) {
            networkSsids = mergeSsids(networkSsids);
        }

        for (NetworkSsid networkSsid : networkSsids) {
            networkSsid.genVirtualKey(simpleAesEcbCryptoService);
            networkSsid.genRadioVirtualKeys(simpleAesEcbCryptoService);
        }

        return networkSsids;
    }

    private List<NetworkSsid> excludeUndefined(List<NetworkSsid> networkSsids) {
        List<NetworkSsid> result = new ArrayList<>();
        for (NetworkSsid networkSsid : networkSsids) {
            if (!StringUtils.equals(ApplicationConstants.SSID_KEY_UNDEFINED, networkSsid.getSsidKey())) {
                result.add(networkSsid);
            }
        }
        return result;
    }

    private List<NetworkSsid> mergeSsids(List<NetworkSsid> networkSsids) {
        List<NetworkSsid> mergedSsids = new ArrayList<>();

        for (NetworkSsid networkSsid : networkSsids) {
            boolean merged = false;
            for (NetworkSsid mergedSsid : mergedSsids) {
                if (StringUtils.equals(networkSsid.getSerial(), mergedSsid.getSerial()) &&
                        (networkSsid.getEnabled() == mergedSsid.getEnabled()) &&
                        StringUtils.equals(networkSsid.getSsidKey(), mergedSsid.getSsidKey()) &&
                        StringUtils.equals(networkSsid.getName(), mergedSsid.getName()) &&
                        StringUtils.equals(networkSsid.getPassword(), mergedSsid.getPassword()) &&
                        StringUtils.equals(networkSsid.getSecurityMode(), mergedSsid.getSecurityMode())
                ) {
                    mergedSsid.getSsidCompositeKeys().addAll(networkSsid.getSsidCompositeKeys());
                    mergedSsid.getBssids().addAll(networkSsid.getBssids());
                    mergedSsid.getRadioEnums().addAll(networkSsid.getRadioEnums());
                    merged = true;
                    break;
                }
            }

            if (!merged) {
                mergedSsids.add(networkSsid);
            }
        }
        return mergedSsids;
    }

    public NetworkSsid findByStnAndVirtualkey(String stn, String virtualKey) throws Exception {
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        NetworkSsidVirtualKey networkSsidVirtualKey = NetworkSsidVirtualKey.fromVirtualKeyString(simpleAesEcbCryptoService, virtualKey);
        String serialNumber = networkSsidVirtualKey.getSerial();

        manageCommonService.checkAPSerialNumBelongsToSubscriber(serialNumber, equipment);

        List<NetworkSsid> matchedNetworkSsids = new ArrayList<>();
        SsidCompositeKey[] ssidCompositeKeys = networkSsidVirtualKey.getSsidCompositeKeys();
        List<NetworkSsid> networkSsids = networkSsidRepo.findByUserIdAndSerialNumber(equipment.getRgwSerial(),serialNumber);
        for (NetworkSsid networkSsid : networkSsids) {
            for (SsidCompositeKey ssidCompositeKey : ssidCompositeKeys) {
                if (StringUtils.equals(ssidCompositeKey.getRadioKey(), networkSsid.getRadioEnums().get(0).getRadioKey())
                        && StringUtils.equals(ssidCompositeKey.getSsidKey(), networkSsid.getSsidKey())) {
                    matchedNetworkSsids.add(networkSsid);
                }
            }
        }

        Boolean smartSteeringEnable = manageCommonService.isSmartSteeringEnabledForDeviceMac(equipment.getRgwSerial(), serialNumber);
        List<NetworkSsid> mergedNetworkSsids = matchedNetworkSsids;
        if (smartSteeringEnable)
            mergedNetworkSsids = mergeSsids(matchedNetworkSsids);
        NetworkSsid result = null;
        if (mergedNetworkSsids.size() == 1) {
            result = mergedNetworkSsids.get(0);
            result.genVirtualKey(simpleAesEcbCryptoService);
            result.genRadioVirtualKeys(simpleAesEcbCryptoService);
        }
        return result;
    }

    @PermissionHandle(checkSubscriber = false)
    public void putSsid(String stn, String virtualKey, SsidRequest request) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        NetworkSsidVirtualKey networkSsidVirtualKey = NetworkSsidVirtualKey.fromVirtualKeyString(simpleAesEcbCryptoService, virtualKey);

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(networkSsidVirtualKey.getSerial(), equipment);

        BasicDBObject query = new BasicDBObject();
        query.put("userId", equipment.getRgwSerial());
        query.put("serialNumber", networkSsidVirtualKey.getSerial());

        ArrayList<DBObject> list = new ArrayList<DBObject>();
        BasicDBObject or1 = new BasicDBObject("controller", true);
        BasicDBObject or2 = new BasicDBObject().append("controller", new BasicDBObject().append("$exists", false));
        list.add(or1);
        list.add(or2);
        query.put("$or", list);

        DBObject dbObject = at3Adapter.getMongoDbCollection(AP_DETAIL).findOne(query, new BasicDBObject().append("_id", 0));
        if (Objects.isNull(dbObject))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "WiFi operation will be send to controller only.");

        String isp = at3Adapter.getIspNameByGroupId(equipment.getGroupId());

        List<Object> requestList = new ArrayList<>();
        SsidCompositeKey[] ssidCompositeKeys = networkSsidVirtualKey.getSsidCompositeKeys();
        for (SsidCompositeKey ssidCompositeKey : ssidCompositeKeys) {
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("enabled", request.isEnabled());
            payloadMap.put("ssid", request.getName());
            payloadMap.put("password", request.getPassword());

            HashMap<String, Object> data = new HashMap<>();
            data.put("uri", String.format(RpcConstants.SSID_ACTION_URI, ssidCompositeKey.getRadioKey(), ssidCompositeKey.getSsidKey()));
            data.put("method", "PUT");
            data.put("payload", payloadMap);

            requestList.add(data);
        }

        String payload = objectMapper.writeValueAsString(requestList);
        String tid = CommonUtils.generateUUID();

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(LONG_RPC_POLL_COUNT));
        Map<String, Object> rpcResult = cpeRpcService.sendRpc31AndWaitResult(tid, userId, networkSsidVirtualKey.getSerial(), isp, payload, max_Tries, THREAD_TO_SLEEP);
        List<HashMap<String, Object>> responseList = null;
        if (rpcResult.get("response") == null) {
            throw new CpeRpcResultException(CpeRpcResultException.FailedReason.FAILED_RESPONSE);

        } else {
            responseList = objectMapper.convertValue(rpcResult.get("response"), new TypeReference<List<HashMap<String, Object>>>() {});
        }

        for (HashMap<String, Object> response: responseList) {
            if (Integer.valueOf(String.valueOf(response.get("code"))) != 200)
                throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get bad response from response.");
        }
    }

}
