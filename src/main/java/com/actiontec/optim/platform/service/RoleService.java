package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.repository.RoleRepo;
import com.incs83.app.entities.Role;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RoleService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    RoleRepo roleRepo;

    public Role getRoleById(String roleId) throws Exception {
        return roleRepo.getRoleById(roleId);
    }
}
