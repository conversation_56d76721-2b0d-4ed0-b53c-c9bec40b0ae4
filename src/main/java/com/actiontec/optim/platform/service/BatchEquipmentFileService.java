package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.BatchUploadActionRequest;
import com.actiontec.optim.platform.api.v6.enums.BatchEquipmentAction;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.repository.FileRepo;
import com.incs83.app.entities.OptimFile;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.S3Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.HashMap;

import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_BATCH_UPLOAD_DIR;
import static com.incs83.constants.ApplicationCommonConstants.FORWARD_SLASH;

@Service
public class BatchEquipmentFileService extends FileService {
    @Autowired
    FileRepo fileRepo;

    @Autowired
    S3Service s3Service;

    public String createFile(BatchUploadActionRequest batchUploadActionRequest, String fileType) throws Exception {
        if (!BatchEquipmentAction.checkAction(batchUploadActionRequest.getAction())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Action illegal.");
        }

        return fileRepo.createFile(batchUploadActionRequest.getFileName(), fileType);
    }

    public OptimFile uploadFile(OptimFile optimFile, MultipartFile file) throws Exception {
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_UPLOADING);

        String fileName = optimFile.getId() + ".csv";
        String filePath = EQUIPMENT_BATCH_UPLOAD_DIR + FORWARD_SLASH + fileName;
        optimFile.setFilePath(filePath);
        optimFile = fileRepo.updateFile(optimFile);

        // upload to aws s3
        HashMap<String, String> uploadResult = s3Service.uploadFile(file, filePath);
        if(ObjectUtils.isEmpty(uploadResult)) {
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_ERROR);
            fileRepo.updateFile(optimFile);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Upload file failed.");
        }

        String md5 = calculateMd5(file.getInputStream());
        optimFile.setMd5(md5);
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_SUCCESS);
        optimFile.setSecureUrl(uploadResult.get("secure_url"));
        optimFile.setFileUploadedTime(new Date());
        return fileRepo.updateFile(optimFile);
    }

    public OptimFile getOptimFileById(String fileId) throws Exception {
        return fileRepo.getOptimFileById(fileId);
    }

    public void deleteFileById(String fileId) throws Exception {
        fileRepo.deleteById(fileId);
    }
}
