package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentDTO;
import com.actiontec.optim.platform.api.v6.dto.NCSEquipmentQueryDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.repository.EquipmentRepo;
import com.actiontec.optim.platform.repository.NCSEquipmentRepo;
import com.incs83.app.business.v2.NewManageEquipmentService;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.NCSEquipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.ValidationException;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.AP_DETAIL;

@Service
public class NCSEquipmentService {
    @Autowired
    private NCSIspService ispService;

    @Autowired
    NewManageEquipmentService newManageEquipmentService;

    @Autowired
    private MongoServiceImpl mongoServiceImpl;

    @Autowired
    EquipmentRepo equipmentRepo;

    @Autowired
    private NCSEquipmentRepo ncsEquipmentRepo;

    @Autowired
    private BrokerInfoService brokerInfoService;

    private final List<String> csvTitle;

    public NCSEquipmentService() {
        csvTitle = Arrays.asList("serial", "mac", "modelName", "productId");
    }

    public PaginationResponse<NCSEquipmentDTO> getAllEquipment(NCSEquipmentQueryDTO queryDTO) throws Exception {
        String defaultBrokerId = brokerInfoService.getDefaultBrokerId();
        PaginationResponse<NCSEquipmentDTO> response = ncsEquipmentRepo.getAllEquipment(queryDTO);

        // Set default broker_info_id when equipment has no redirect setting in equipment_redirect table
        if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
            response.getData().forEach(equipment -> {
                if (equipment.getBrokerInfoId() == null) {
                    equipment.setBrokerInfoId(defaultBrokerId);
                }
            });
        }

        return response;
    }

    public void downloadTemplate(HttpServletResponse response) throws IOException {
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"equipment_template.csv\"");
        PrintWriter writer = response.getWriter();
        writer.println(String.join(COMMA, csvTitle));
        writer.flush();
    }

    public void checkQueryParameter(NCSEquipmentQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            ispService.checkIspIdPresent(queryDTO.getIspId());
        }

        if (StringUtils.isNotEmpty(queryDTO.getNetworkId())) {
            checkQueryNetworkId(queryDTO);
        }
    }

    private void checkQueryNetworkId(NCSEquipmentQueryDTO queryDTO) throws Exception {
        if (StringUtils.isNotEmpty(queryDTO.getIspId())) {
            List<String> ispNetworkIdList = ncsEquipmentRepo.getNCSEquipmentListByIspId(queryDTO.getIspId())
                    .stream()
                    .map(NCSEquipment::getNetworkId)
                    .collect(Collectors.toList());
            if (StringUtils.isNotEmpty(queryDTO.getNetworkId()) && !ispNetworkIdList.contains(queryDTO.getNetworkId())) {
                throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Query networkId is not belongs to the user's isp.");
            }
        }
    }

    public void updateNetworkAndSubscriberByIdList(String ispId, List<String> equipmentIdList, String networkId, String subscriberId) throws Exception {
        ncsEquipmentRepo.updateNetworkAndSubscriberByIdList(ispId, equipmentIdList, networkId, subscriberId);
    }

    // delete old equipment table
    public void deleteOldEquipmentBySubscriberId(String subscriberId) throws Exception {
        List<Equipment> equipmentList = equipmentRepo.getEquipmentListBySubscriberId(subscriberId);
        if (CollectionUtils.isNotEmpty(equipmentList)) {
            for (Equipment equipment : equipmentList) {
                // this method contains clean mongo apDetail data.
                newManageEquipmentService.deleteEquipmentById(equipment.getId());
            }
        }
    }

    public List<NCSEquipment> getNCSEquipmentListBySerialList(List<String> serialList) throws Exception {
        return ncsEquipmentRepo.getNCSEquipmentListBySerialList(serialList);
    }

    public List<NCSEquipment> getNCSEquipmentListByNetworkId(String networkId) throws Exception {
        return ncsEquipmentRepo.getNCSEquipmentListByNetworkId(networkId);
    }

    public NCSEquipment getNCSEquipmentById(String id) throws Exception {
        return ncsEquipmentRepo.getNCSEquipmentById(id);
    }

    // clear ncs_equipment table subscriber_id
    public void clearSubscriberMappingBySubscriberId(String subscriberId) throws Exception {
        ncsEquipmentRepo.clearSubscriberMappingBySubscriberId(subscriberId);
    }

    public void clearNetworkMappingByNetworkId(String networkId) throws Exception {
        List<NCSEquipment> networkEquipmentList = getNCSEquipmentListByNetworkId(networkId);
        if(CollectionUtils.isNotEmpty(networkEquipmentList)) {
            for (NCSEquipment ncsEquipment: networkEquipmentList) {
                deleteMongoApDetail(ncsEquipment);
            }
            ncsEquipmentRepo.clearNetworkMappingByNetworkId(networkId);
        }
    }

    private void deleteMongoApDetail(NCSEquipment ncsEquipment) {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", ncsEquipment.getSerial());
        params.put("serialNumber", ncsEquipment.getSerial());
        DBObject apDetails = mongoServiceImpl.findOne(params, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(apDetails)) {
            String[] rgwCleanUp = ActiontecConstants.cleanupRGW.split(COMMA);
            newManageEquipmentService.cleanUpData(params, rgwCleanUp);
        }
    }

    public boolean checkEquipmentListByIspId(String ispId, List<String> equipmentIdList) throws Exception {
        return ncsEquipmentRepo.checkEquipmentListByIspId(ispId, equipmentIdList);
    }
}
