package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.OperationHandle;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.actiontec.optim.platform.api.v5.model.SmartSteeringConfig;
import com.actiontec.optim.platform.constant.OperationConstants;
import com.actiontec.optim.platform.model.SteeringLog;
import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.UtilityServices;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.cassandra.*;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.service.repository.CassandraRepository;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.RpcConstants.STEERING_URI;
import static com.incs83.constants.ApplicationCommonConstants.COMMON_CONFIG;

@Service
public class SmartSteeringConfigService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private CpeRpcService cpeRpcService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private CassandraRepository cassandraRepository;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private UtilityServices utilityServices;
    private final String AppleSampleMacAddress = "b8:09:8a:c2:47:47";

    private enum BandType {
        BAND_5G_HIGH(0, "5GHi"),
        BAND_5G(1, "5G"),
        BAND_24G(2, "2.4G"),
        BAND_6G(4, "6G");

        private int idx;
        private String name;

        BandType(int idx, String name) {
            this.idx = idx;
            this.name = name;
        }

        public static String getBandByIndex(int bandIdx) {
            for(BandType bandType : BandType.values()) {
                if(bandType.idx == bandIdx) {
                    return bandType.name;
                }
            }

            return null;
        }
    }

    private List<String> bssidList = new ArrayList<String>(){
            {
                add("bssid24G");
                add("bssid5G");
                add("bssid5GLo");
                add("bssid5GHi");
                add("bssid6G");
            }
    };

    public enum SteeringLogType {
        Assoc,
        Disassoc,
        Steer,
        Roam,
        Join,
        Leave,
        Log,
        Diag,
        Unknown;

        public static SteeringLogType getType(String in) {
           for(SteeringLogType item: values()) {
               if (StringUtils.equals(item.name(), in))
                   return item;
           }
           return null;
        }
    }

    public enum ResultTypeEnum {
        Target("TARGET", "Target"),
        Other("OTHER", "Other"),
        Original("ORIGINAL", "Original"),
        None("NONE", "None"),
        Stay("STAY", "Stay"),
        NA("N/A", "N/A");

        public static String parse(String name) {
            for(ResultTypeEnum item: values()) {
                if (StringUtils.equals(item.name, name))
                    return item.getValue();
            }

            return NA.getValue();
        }

        private String name;
        private String value;

        ResultTypeEnum(String name, String type) {
            this.name = name;
            this.value = type;
        }

        public String getName() {
            return name;
        }

        public String getValue() {
            return value;
        }
    }

    public SmartSteeringConfig getSteering(String stn) throws Exception {
        logger.info("getSteering equipmentId:[{}]", stn);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);

        DBObject queryParam = new BasicDBObject();
        queryParam.put("userId", userId);
        queryParam.put("serialNumber", serial);
        BasicDBObject projection = new BasicDBObject();
        projection.put("wifiConf", 1);
        DBCollection dbCollection = at3Adapter.getMongoDbCollection(ApDetailDto.COLLECTION_apDetail);
        DBObject apDetail = dbCollection.findOne(queryParam, projection);

        if (apDetail == null) {
            logger.error("getSteering apDetail not found, equipmentId:[{}]", stn);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
        }

        DBObject wifiConf = (DBObject) apDetail.get("wifiConf");
        if (wifiConf == null) {
            logger.error("getSteering wifiConf not found, equipmentId:[{}]", stn);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
        }

        DBObject smartSteering = (DBObject) wifiConf.get("SmartSteering");
        if (smartSteering == null) {
            logger.error("getSteering smartSteering not found, equipmentId:[{}]", stn);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
        }

        smartSteering.removeField("steeringOptions");
        return objectMapper.convertValue(smartSteering, SmartSteeringConfig.class);
    }

    @OperationHandle(operation = OperationConstants.STEERING_OP)
    public void putSteering(String stn, SmartSteeringConfig requestBody) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);
        if (userId.isEmpty()) {
            logger.error("putSteering equipment.rgwSerial is empty, equipmentId:[{}]", stn);
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "internal error");
        }

        boolean steeringEnabled = requestBody.isEnabled();
        HashMap<String, Boolean> steeringOptionsMap = new HashMap<>();
        steeringOptionsMap.put("staticEnable", steeringEnabled);
        steeringOptionsMap.put("dynamicEnable", steeringEnabled);
        steeringOptionsMap.put("deviceSteerEnable", steeringEnabled);
        steeringOptionsMap.put("deviceSteerStaticEnable", steeringEnabled);
        steeringOptionsMap.put("deviceSteerDynamicEnable", steeringEnabled);

        HashMap<String, Object> payloadMap = objectMapper.convertValue(requestBody, HashMap.class);
        payloadMap.put("steeringOptions", steeringOptionsMap);

        String payload = objectMapper.writeValueAsString(payloadMap);
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, serial, STEERING_URI, "PUT", payload);

        if (rpcResult == null || (int)rpcResult.get("code")!= 200)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "bad request");
    }

    @PermissionHandle(checkSubscriber = false)
    public List<SteeringLog> getSteeringLogs(String stn, String type, String macAddress, int minutes) throws Exception {
        if (minutes < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "duration cannot be negative value " + minutes);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if (Objects.nonNull(macAddress))
            manageCommonService.checkDeviceMACBelongsToSubscriber(macAddress, userEquipment);

        HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
        boolean flag = (Objects.nonNull(commonProps.get(ABS_DATABASE)) && commonProps.get(ABS_DATABASE).equalsIgnoreCase("MONGO")) ? true : false;
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        HashSet<String> serialNumbers;
        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject projection = new BasicDBObject();
        projection.put("macAddress", 1);
        projection.put("serialNumber", 1);
        projection.put("friendlyName", 1);
        projection.put("modelName", 1);
        projection.put("ssids", 1);
        projection.put("type", 1);

        List<BasicDBObject> equipmentList = mongoService.findList(query, null, AP_DETAIL, projection);
        serialNumbers = (HashSet<String>) equipmentList.stream().map(ap -> String.valueOf(ap.get("serialNumber"))).collect(Collectors.toSet());
        HashMap<String, Object> bssidDetails = new HashMap<>();

        HashSet<String> bssidSets = new HashSet<>();
        for (BasicDBObject equipment : equipmentList) {
            manageCommonService.processBSSIDDetails(bssidDetails, bssidSets, equipment);
        }

        StringJoiner bssIdCsv = new StringJoiner(COMMA);
        bssidSets.forEach(item -> {
            bssIdCsv.add(item);
        });

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("_id", 0);

        StringJoiner serialNumberCsv;
        HashMap<String, Object> rangeParam = new HashMap<>();
        rangeParam.put("key", TIMESTAMP);
        rangeParam.put("operand", "gt");

        List<String> cassandraProjection = new ArrayList<>();
        List<String> macAddressList = null;
        if (Objects.isNull(macAddress))
            macAddressList = manageCommonService.getMacAddressFromUserId(userEquipment.getRgwSerial());
        else {
            macAddressList = new ArrayList<>(1);
            macAddressList.add(macAddress);
        }

        String vendor = manageCommonService.getVendorNameFromCache(AppleSampleMacAddress);
        if (type.equals(SteeringLogType.Assoc.name()) && Objects.isNull(vendor))
        {
            utilityServices.updateOui();
        }

        HashMap<String, List<String>> inparams = new HashMap<>();
        inparams.put("macAddress", macAddressList);
        List<SteeringLog> steeringLogs = null;

        switch (SteeringLogType.getType(type)) {
            case Assoc:
                List<EvAssociation> association;
                if (flag) {
                    List<DBObject> associationFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.BSID, DEVICE_ASSOCIATION, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    association = manageCommonService.convertToAssocEvent(associationFromMongo);
                } else {
                    association = (List<EvAssociation>) cassandraRepository.read(EvAssociation.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }

                steeringLogs = new ArrayList<>(association.size());
                List<SteeringLog> assocLogs = steeringLogs;
                association.stream().forEach(i -> {
                    HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));
                    SteeringLog log = new SteeringLog();
                    log.setType(type);
                    log.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : String.valueOf(i.getMacAddress()));
                    log.setTimestamp(i.getTimestamp());

                    SteeringLog.Dest dest = new SteeringLog.Dest();
                    dest.setRadioKey(BandType.getBandByIndex(i.getBand()));
                    dest.setSerialNumber(Objects.nonNull(details) ? details.get("serialNumber") : null);
                    log.setDest(dest);
                    assocLogs.add(log);
                });
                break;
            case Disassoc:
                List<EvDisassociation> disAssociation;

                if (flag) {
                    List<DBObject> disAssociationFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.BSID, DEVICE_DISASSOCIATION, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    disAssociation = manageCommonService.convertToDisAssocEvent(disAssociationFromMongo);
                } else {
                    inparams.put("assocType", ASSOC_TYPE_LIST);
                    disAssociation = (List<EvDisassociation>) cassandraRepository.read(EvDisassociation.class, query, rangeParam, inparams, cassandraProjection, minutes);
                    inparams.remove("assocType");
                }

                steeringLogs = new ArrayList<>(disAssociation.size());
                List<SteeringLog> disassocLogs = steeringLogs;

                disAssociation.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && i.getTimestamp() <= currentTimestamp) {

                        HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));
                        SteeringLog log = new SteeringLog();
                        log.setType(type);
                        log.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : String.valueOf(i.getMacAddress()));
                        log.setTimestamp(i.getTimestamp());
                        SteeringLog.Origin origin = new SteeringLog.Origin();
                        origin.setSerialNumber(Objects.nonNull(details) ? details.get("serialNumber") : null);
                        origin.setRadioKey(BandType.getBandByIndex(i.getBand()));
                        log.setOrigin(origin);
                        disassocLogs.add(log);
                    }
                });
                break;
            case Steer:
                List<EvSteeringLogs> steer;
                if (flag) {
                    List<DBObject> steerFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.OBSS_BSSID, DEVICE_STEERING, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    steer = manageCommonService.convertToEVSteeringLog(steerFromMongo);
                } else {
                    steer = (List<EvSteeringLogs>) cassandraRepository.read(EvSteeringLogs.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }
                steer = steer.stream().filter(p -> (!p.getSteeringend().equals(EMPTY_STRING))).collect(Collectors.toList());
                steeringLogs = new ArrayList<>(steer.size());
                List<SteeringLog> steerLogs = steeringLogs;

                steer.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && i.getTimestamp() <= currentTimestamp) {
                        SteeringLog log = new SteeringLog();
                        log.setType(type);
                        log.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        log.setTimestamp(i.getTimestamp());
                        SteeringLog.Origin origin = new SteeringLog.Origin();
                        if (Objects.nonNull(i.getObssbssid())) {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getObssbssid());
                            origin.setSerialNumber(details == null ? null : details.get("serialNumber"));
                            origin.setRadioKey(details == null ? null : details.get("band"));
                        }
                        origin.setRssi(i.getObssrssi());
                        origin.setChannel(i.getOchannel());
                        origin.setPhyRate(i.getOphyrate());
                        origin.setAirTimeUsage(i.getOairusage());
                        origin.setFat(i.getOfat());
                        log.setOrigin(origin);

                        SteeringLog.Dest dest = new SteeringLog.Dest();
                        if (Objects.nonNull(i.getIbssbssid())) {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getIbssbssid());
                            dest.setSerialNumber(details == null ? null : details.get("serialNumber"));
                            dest.setRadioKey(details == null ? null : details.get("band"));
                        }
                        dest.setRssi(i.getIbssrssi());
                        dest.setChannel(i.getIchannel());
                        dest.setFat(i.getIfat());
                        log.setDest(dest);

                        SteeringLog.Result result = new SteeringLog.Result();
                        if (Objects.nonNull(i.getTbssbssid())) {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getTbssbssid());
                            result.setSerialNumber(details == null ? null : details.get("serialNumber"));
                            result.setRadioKey(details == null ? null : details.get("band"));
                        }
                        result.setChannel(i.getTchannel());
                        result.setRssi(i.getTbssrssi());
                        result.setPhyRate(i.getTphyrate());
                        result.setAirTimeUsage(i.getTairusage());
                        result.setFat(i.getTfat());
                        log.setResult(result);

                        SteeringLog.SteeringData steeringData = new SteeringLog.SteeringData();
                        steeringData.setType(i.getSteeringtype());
                        steeringData.setResult(ResultTypeEnum.parse(i.getSteeringend()));
                        steeringData.setTime(i.getSteeringtime());
                        log.setSteeringData(steeringData);
                        steerLogs.add(log);
                    }
                });
                break;
            case Roam:
                List<EvRoaming> roam;
                if (flag) {
                    List<DBObject> roamFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.OBSSID, DEVICE_ROAMING, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    roam = manageCommonService.convertToRoamEvent(roamFromMongo);
                } else {
                    roam = (List<EvRoaming>) cassandraRepository.read(EvRoaming.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }
                steeringLogs = new ArrayList<>(roam.size());
                List<SteeringLog> roamLogs = steeringLogs;

                roam.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && i.getTimestamp() <= currentTimestamp) {
                        SteeringLog log = new SteeringLog();
                        log.setType(type);
                        log.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        log.setTimestamp(i.getTimestamp());
                        SteeringLog.Origin origin = new SteeringLog.Origin();
                        if (Objects.nonNull(i.getObssid())) {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(i.getObssid());
                            origin.setSerialNumber(details == null ? null : details.get("serialNumber"));
                        }

                        origin.setRadioKey(BandType.getBandByIndex(i.getOband()));
                        origin.setRssi((long) i.getOrssi());
                        log.setOrigin(origin);

                        SteeringLog.Result result = new SteeringLog.Result();
                        if (Objects.nonNull(i.getNbssid())) {
                            HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getNbssid()));
                            result.setSerialNumber(details == null ? null : details.get("serialNumber"));
                        }
                        result.setRadioKey(BandType.getBandByIndex(i.getNband()));
                        result.setRssi((long) i.getNrssi());
                        log.setResult(result);
                        roamLogs.add(log);
                    }
                });
                break;
            case Log:
                serialNumberCsv = new StringJoiner(COMMA);
                serialNumbers.forEach(item -> {
                    serialNumberCsv.add(item);
                });
                List<EvLogs> eventLog;

                if (flag) {
                    List<DBObject> eventLogFromMongo = mongoService.findListByTimestamp(query, serialNumberCsv.toString(), ActiontecConstants.SERIAL_NUMBER, DEVICE_EVENT_LOG, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    eventLog = manageCommonService.convertToEvLogs(eventLogFromMongo);
                } else {
                    eventLog = (List<EvLogs>) cassandraRepository.read(EvLogs.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }
                steeringLogs = new ArrayList<>(eventLog.size());
                List<SteeringLog> logs = steeringLogs;

                eventLog.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && Long.valueOf(String.valueOf(i.getTimestamp())) <= currentTimestamp) {
                        SteeringLog log = new SteeringLog();
                        log.setType(type);
                        log.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        log.setTimestamp(i.getTimestamp());
                        log.setLog(Objects.isNull(i.getLog()) ? null : i.getLog());
                        logs.add(log);
                    }
                });
                break;
            case Diag:
                List<EvTargetApVictim> diagnosticLogs;
                diagnosticLogs = (List<EvTargetApVictim>) cassandraRepository.read(EvTargetApVictim.class, query, rangeParam, inparams, cassandraProjection, minutes);

                steeringLogs = new ArrayList<>(diagnosticLogs.size());
                List<SteeringLog> diagLogs = steeringLogs;
                diagnosticLogs.forEach( i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getTimestamp()) && Long.valueOf(String.valueOf(i.getTimestamp())) <= currentTimestamp) {
                        HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));
                        SteeringLog log = new SteeringLog();
                        log.setType(type);
                        log.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        log.setTimestamp(i.getTimestamp());
                        SteeringLog.DiagnosticData diagnosticData = new SteeringLog.DiagnosticData();
                        diagnosticData.setSerialNumber(details == null ? null : details.get("serialNumber"));
                        diagnosticData.setRadioKey(details == null ? null : details.get("band"));
                        diagnosticData.setTrigger(i.getTrigger());
                        diagnosticData.setTriggerDescription(ABS_TRIGGER_STRING.get(Long.valueOf(i.getTrigger()).intValue()));
                        diagnosticData.setReasonCode(i.getReasoncode());
                        if (EV_TARGET_AP_SELECTION.equals(i.getEvent())) {
                            diagnosticData.setReasonDescription(ABS_TARGET_REASON_STRING.get(Long.valueOf(i.getReasoncode()).intValue()));
                        } else {
                            diagnosticData.setReasonDescription(ABS_VICTIM_REASON_STRING.get(Long.valueOf(i.getReasoncode()).intValue()));
                        }
                        log.setDiagnosticData(diagnosticData);
                        diagLogs.add(log);
                    }
                });
                break;
            case Join:
            case Leave:
            case Unknown:
                List<EvStaConnectLogs> evStaConnectLogs;
                if (flag) {
                    List<DBObject> evStaConnectLogsFromMongo = mongoService.findListByTimestamp(query, bssIdCsv.toString(), ActiontecConstants.BSID, DEVICE_STATION_CONNECT_LOGS, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    evStaConnectLogs = manageCommonService.convertToEvStaConnectLogs(evStaConnectLogsFromMongo);
                } else {
                    evStaConnectLogs = (List<EvStaConnectLogs>) cassandraRepository.read(EvStaConnectLogs.class, query, rangeParam, inparams, cassandraProjection, minutes);
                }
                if (type.equals(SteeringLogType.Unknown.name())) {
                    evStaConnectLogs = evStaConnectLogs.stream().filter(i-> !(i.getStatus().equals("JOIN") || i.getStatus().equals("QUIT"))).collect(Collectors.toList());
                } else {
                    String status = type.equals("JOIN")? "JOIN": "QUIT";
                    evStaConnectLogs = evStaConnectLogs.stream().filter(i->status.toUpperCase().equals(i.getStatus())).collect(Collectors.toList());
                }

                steeringLogs = new ArrayList<>(evStaConnectLogs.size());
                List<SteeringLog> staLogs = steeringLogs;
                evStaConnectLogs.forEach(i -> {
                    long currentTimestamp = Calendar.getInstance().getTimeInMillis();
                    if (Objects.nonNull(i.getStatus()) && Objects.nonNull(i.getTimestamp()) && Long.valueOf(String.valueOf(i.getTimestamp())) <= currentTimestamp) {
                        HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(i.getBssid()));
                        SteeringLog log = new SteeringLog();
                        log.setType(type);
                        log.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(i.getMacaddress()) ? null : i.getMacaddress());
                        log.setTimestamp(i.getTimestamp());
                        if ("QUIT".equals(String.valueOf(i.getStatus()))) {
                            SteeringLog.Origin origin = new SteeringLog.Origin();
                            origin.setSerialNumber(details == null ? null : details.get("serialNumber"));
                            origin.setRadioKey(BandType.getBandByIndex(i.getBand()));

                            log.setOrigin(origin);
                        } else if ("JOIN".equals(String.valueOf(i.getStatus()))) {
                            SteeringLog.Dest dest = new SteeringLog.Dest();
                            dest.setRadioKey(BandType.getBandByIndex(i.getBand()));
                            dest.setSerialNumber(details == null ? null : details.get("serialNumber"));
                            log.setDest(dest);
                        }
                        staLogs.add(log);
                    }
                });
                break;
            default:
                break;
        }

        return Objects.nonNull(steeringLogs) && steeringLogs.size()>0?steeringLogs.stream().sorted((o1,o2)->Long.compare(o2.getTimestamp(),o1.getTimestamp())).collect(Collectors.toList()) : steeringLogs;
    }
}