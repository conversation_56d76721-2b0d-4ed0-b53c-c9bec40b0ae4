package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.OperationHandle;
import com.actiontec.optim.platform.constant.OperationConstants;
import com.actiontec.optim.platform.exception.CpeFirmwareNotSupportException;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.model.SmartSteeringRule;
import com.actiontec.optim.service.CpeRpcService;
import com.actiontec.optim.util.SimpleVersion;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class SmartSteeringRuleService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    private static final String STEER_RULE_RPC_URI = "/cpe-api/steering/rules";
    private static final SimpleVersion STEER_RULE_SUPPORTED_VER = new SimpleVersion("3.20.1");

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private CpeRpcService cpeRpcService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private ManageCommonService manageCommonService;

    @OperationHandle(operation = OperationConstants.STEERING_OP)
    public String save(String stn, SmartSteeringRule smartSteeringRule) throws InterruptedException {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);

        checkFirmwareSupport(userId);

        Map<String, String> payloadMap = new HashMap<>();
        payloadMap.put("deviceMacAddress", smartSteeringRule.getMacAddress());
        payloadMap.put("targetBand", smartSteeringRule.getTargetBand());

        if (smartSteeringRule.getTargetSerialNumber().equals("Any")) {
            payloadMap.put("targetApMacAddress", smartSteeringRule.getTargetSerialNumber());
        } else {
            Optional<ApDetailDto> optTargetApDetailDto = apDetailDao.findByUserIdAndSerial(userId, smartSteeringRule.getTargetSerialNumber());
            if (!optTargetApDetailDto.isPresent()) {
                throw new NoSuchEntityException();
            }
            payloadMap.put("targetApMacAddress", optTargetApDetailDto.get().getMacAddress());
        }

        try {
            String payload = objectMapper.writeValueAsString(payloadMap);
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, serial, STEER_RULE_RPC_URI, "POST", payload);
        } catch (JsonProcessingException e) {
            logger.error("covert rpc payload failed. userId:[{}]", userId, e);
            throw new RuntimeException(e);  // should not happen
        }

        return smartSteeringRule.getMacAddress();
    }

    @OperationHandle(operation = OperationConstants.STEERING_OP)
    public void update(String stn, String deviceMacAddress, SmartSteeringRule smartSteeringRule) throws InterruptedException {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);

        checkFirmwareSupport(userId);

        String rpcUri = STEER_RULE_RPC_URI + "/" + deviceMacAddress;

        Map<String, String> payloadMap = new HashMap<>();
        payloadMap.put("targetBand", smartSteeringRule.getTargetBand());

        if (smartSteeringRule.getTargetSerialNumber().equals("Any")) {
            payloadMap.put("targetApMacAddress", smartSteeringRule.getTargetSerialNumber());
        } else {
            Optional<ApDetailDto> optTargetApDetailDto = apDetailDao.findByUserIdAndSerial(userId, smartSteeringRule.getTargetSerialNumber());
            if (!optTargetApDetailDto.isPresent()) {
                throw new NoSuchEntityException();
            }
            payloadMap.put("targetApMacAddress", optTargetApDetailDto.get().getMacAddress());
        }

        try {
            String payload = objectMapper.writeValueAsString(payloadMap);
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, serial, rpcUri, "PUT", payload);
        } catch (JsonProcessingException e) {
            logger.error("covert rpc payload failed. userId:[{}]", userId, e);
            throw new RuntimeException(e);  // should not happen
        }
    }

    @OperationHandle(operation = OperationConstants.STEERING_OP)
    public void deleteByDeviceMacAddress(String stn, String deviceMacAddress) throws InterruptedException {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);

        checkFirmwareSupport(userId);

        String rpcUri = STEER_RULE_RPC_URI + "/" + deviceMacAddress;

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, serial, rpcUri, "DELETE", "{}");
    }

    @OperationHandle(operation = OperationConstants.STEERING_OP)
    public List<SmartSteeringRule> findAll(String stn) throws InterruptedException {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);

        checkFirmwareSupport(userId);

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, serial, STEER_RULE_RPC_URI, "GET", "{}");

        List<SmartSteeringRule> smartSteeringRuleList;
        if (rpcResult.get("payload") != null) {
            Map<String, String> macToSerialMap = apDetailDao.getMacToSerialMap(userId);
            smartSteeringRuleList = convertToSteerRule((List<Map<String, Object>>) rpcResult.get("payload"), macToSerialMap);
        } else {
            smartSteeringRuleList = new ArrayList<>();
        }

        return smartSteeringRuleList;
    }

    @OperationHandle(operation = OperationConstants.STEERING_OP)
    public SmartSteeringRule findByDeviceMacAddress(String stn, String deviceMacAddress) throws InterruptedException {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        String serial = manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId);

        checkFirmwareSupport(userId);

        String rpcUri = STEER_RULE_RPC_URI + "/" + deviceMacAddress;
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(userId, serial, rpcUri, "GET", "{}");

        SmartSteeringRule smartSteeringRule = null;
        if (rpcResult.get("payload") != null) {
            List<Map<String, Object>> respPayloadList = (List<Map<String, Object>>) rpcResult.get("payload");
            for (Map<String, Object> respPayloadMap : respPayloadList) {
                if (StringUtils.equals(deviceMacAddress, String.valueOf(respPayloadMap.get("deviceMacAddress")))) {
                    Map<String, String> macToSerialMap = apDetailDao.getMacToSerialMap(userId);
                    smartSteeringRule = convertToSteerRule(respPayloadMap, macToSerialMap);
                }
            }
        }

        return smartSteeringRule;
    }

    private void checkFirmwareSupport(String userId) {
        Optional<ApDetailDto> optApDetailDto = apDetailDao.findByUserIdAndSerial(userId, userId);
        if (!optApDetailDto.isPresent()) {
            throw new NoSuchEntityException();
        } else {
            String buildVer = optApDetailDto.get().getBuildVersion();
            if (StringUtils.isBlank(buildVer) || STEER_RULE_SUPPORTED_VER.isNewerThan(buildVer)) {
                throw new CpeFirmwareNotSupportException();
            }
        }
    }

    private List<SmartSteeringRule> convertToSteerRule(List<Map<String, Object>> respPayloadList, Map<String, String> macToSerialMap) {
        List<SmartSteeringRule> smartSteeringRuleList = new ArrayList<>();
        for (Map<String, Object> respPayloadMap : respPayloadList) {
            SmartSteeringRule smartSteeringRule = convertToSteerRule(respPayloadMap, macToSerialMap);
            smartSteeringRuleList.add(smartSteeringRule);
        }
        return smartSteeringRuleList;
    }

    private SmartSteeringRule convertToSteerRule(Map<String, Object> respPayloadMap, Map<String, String> macToSerialMap) {
        SmartSteeringRule smartSteeringRule = new SmartSteeringRule();

        String apMac = String.valueOf(respPayloadMap.get("targetApMacAddress"));
        if (apMac.equals("Any")) {
            smartSteeringRule.setTargetSerialNumber(apMac);
        } else {
            smartSteeringRule.setTargetSerialNumber(macToSerialMap.get(apMac));
        }
        smartSteeringRule.setId(String.valueOf(respPayloadMap.get("deviceMacAddress")));
        smartSteeringRule.setMacAddress(String.valueOf(respPayloadMap.get("deviceMacAddress")));
        smartSteeringRule.setTargetBand(String.valueOf(respPayloadMap.get("targetBand")));

        return smartSteeringRule;
    }
}