package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.mapper.FwFileMapper;
import com.actiontec.optim.platform.model.FwFile;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.util.IOUtils;
import com.incs83.app.entities.OptimFile;
import com.incs83.mt.DataAccessService;
import com.incs83.service.S3Service;

import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import javax.annotation.PostConstruct;

@Service
public class AwsS3Service {

    private final Logger logger = LogManager.getLogger(this.getClass());

    private AmazonS3 amazonS3;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private FwFileMapper fwFileMapper;

    @Value("${s3.bucket}")
    private String bucketName;

    @PostConstruct
    public void init() {
        // use common s3 client to access ceph 
        amazonS3 = s3Service.getS3Client();
    }

    private File convertMultiPartFileToFile(OptimFile optimFile, final MultipartFile multipartFile) {
        final File file = new File(optimFile.getFileName());
        try (final FileOutputStream outputStream = new FileOutputStream(file)) {
            outputStream.write(multipartFile.getBytes());
        } catch (final IOException ex) {
            logger.error("Error converting the multi-part file to file", ex);
        }
        return file;
    }

    private void uploadFileToS3Bucket(OptimFile optimFile, final File file) {
        logger.info("Uploading file with name [{}]", optimFile.getFilePath());
        final PutObjectRequest putObjectRequest = new PutObjectRequest(optimFile.getS3Bucket(), optimFile.getFilePath(), file);
        putObjectRequest.setCannedAcl(CannedAccessControlList.PublicRead);
        amazonS3.putObject(putObjectRequest);
        optimFile.setSecureUrl(amazonS3.getUrl(optimFile.getS3Bucket(), optimFile.getFilePath()).toExternalForm());
    }

    public Boolean uploadFile(OptimFile optimFile, File file) {

        logger.info("File upload in progress.");
        Boolean ret = false;

        try {
            optimFile.setS3Bucket(bucketName);
            optimFile.setFilePath(ApplicationConstants.S3_PUBLIC_FILE_FOLDER + optimFile.getType() + "/" + optimFile.getId());

            uploadFileToS3Bucket(optimFile, file);

            ret = true;

            logger.info("File upload is completed.");
        } catch (Exception ex) {
            logger.error("Error while uploading file.", ex);
        }

        return ret;
    }

    public Boolean updateFile(OptimFile optimFile, MultipartFile multipartFile) {

        logger.info("File upload in progress.");
        Boolean ret = false;

        try {
            optimFile.setS3Bucket(bucketName);

            optimFile.setFilePath(ApplicationConstants.S3_PUBLIC_FILE_FOLDER + optimFile.getType() + "/" + optimFile.getId());

            final File file = convertMultiPartFileToFile(optimFile, multipartFile);
            uploadFileToS3Bucket(optimFile, file);

            ret = true;

            logger.info("File upload is completed.");
        } catch (Exception ex) {
            logger.error("Error while uploading file.", ex);
        }

        return ret;
    }

    public byte[] getFile(FwFile fwFile) throws IOException {

        byte[] content = null;

        try (
            S3Object s3Object = amazonS3.getObject(fwFile.getS3Bucket(), fwFile.getFilePath());
            S3ObjectInputStream stream = s3Object.getObjectContent()
        ) {
            content = IOUtils.toByteArray(stream);
            logger.info("File [{}] download is completed.", fwFile.getFilePath());
        } catch (IOException ex) {
            logger.error("IO Error Message", ex);
        }

        return content;
    }

    public void deleteFile(OptimFile optimFile) throws Exception {

        if(!StringUtils.isEmpty(optimFile.getS3Bucket()) && !StringUtils.isEmpty(optimFile.getFilePath())) {
            amazonS3.deleteObject(optimFile.getS3Bucket(), optimFile.getFilePath());
        }
    }

    public void copyFile(OptimFile sourceFile, OptimFile targetFile) {

        try {
            amazonS3.copyObject(sourceFile.getS3Bucket(), sourceFile.getFilePath(), targetFile.getS3Bucket(), targetFile.getFilePath());
        } catch (Exception ex) {
            logger.error("Copy S3 Object Fail src:[{}], target:[{}]", sourceFile.getFilePath(), targetFile.getFilePath(), ex);
        }
    }
}
