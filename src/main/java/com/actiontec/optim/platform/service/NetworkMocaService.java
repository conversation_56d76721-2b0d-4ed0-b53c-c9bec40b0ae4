package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.model.NetworkMoca;
import com.actiontec.optim.platform.repository.NetworkMocaRepo;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class NetworkMocaService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private NetworkMocaRepo networkMocaRepo;

    public List<NetworkMoca> findByStnAndSerial(String stn, String serial) {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return networkMocaRepo.findByUserIdAndSerial(userId, serial);
    }

    public Optional<NetworkMoca> findByStnAndSerialAndPortNo(String stn, String serial, int portNo) {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return networkMocaRepo.findByUserIdAndSerialAndPortNo(userId, serial, portNo);
    }
}
