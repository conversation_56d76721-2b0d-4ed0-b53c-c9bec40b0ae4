package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.mapper.SmmContainerMapper;
import com.actiontec.optim.platform.model.SmmApplication;
import com.actiontec.optim.platform.model.SmmContainer;
import com.actiontec.optim.platform.api.v5.model.SmmContainerRequest;
import com.actiontec.optim.platform.model.SmmContainerPayloadService;
import com.actiontec.optim.platform.repository.SmmContainerRepo;
import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.Equipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;

@Service
public class SmmContainerService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private  CpeRpcService cpeRpcService;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    SmmAppService smmAppService;
    @Autowired
    SmmServicesService smmServicesService;
    @Autowired
    SmmContainerMapper smmContainerMapper;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private SmmContainerRepo smmContainerRepo;

    public SmmContainerPayloadService getPayloadService(String userId, String equipmentId, SmmContainerRequest.Service service) throws Exception {
        String version = Objects.nonNull(service.getVersion())? service.getVersion(): smmServicesService.getSmmSeviceById(service.getServiceId()).getProductionVersion();
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("serialNumber", equipmentId);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        DBObject aPDetails = mongoService.findOne(params, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        if (Objects.isNull(aPDetails) || Objects.isNull(aPDetails.get("modelName")))
            throw new NoSuchEntityException();

        String modelName = aPDetails.get("modelName").toString();
        List<SmmApplication> smmApplicationList = smmAppService.findAppByVersion(service.getServiceId(), version);
        smmApplicationList = smmApplicationList.stream().filter(p -> p.getDependencies().stream().anyMatch(dependency -> dependency.getEquipmentTypeId().equals(modelName))).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(smmApplicationList)) {
            throw new NoSuchEntityException();
        }

        return smmContainerMapper.toPayloadService(smmApplicationList.get(0), service);
    }

    private Map<String, Object> getPayLoad(String userId, String equipmentId, SmmContainerRequest smmContainerRequest) throws Exception {
        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("actionId", CommonUtils.generateUUID());

        if (smmContainerRequest.getActions().getStatusAction().equals("None") || smmContainerRequest.getActions().getActionFlagsValue()) {
            payloadMap.put("actions", objectMapper.convertValue(smmContainerRequest.getActions(), HashMap.class));
            payloadMap.put("service", objectMapper.convertValue(getPayloadService(userId, equipmentId, smmContainerRequest.getService()), HashMap.class));
        } else {
            HashMap<String, Object> actions = new HashMap<>();
            actions.put("statusAction", smmContainerRequest.getActions().getStatusAction());
            payloadMap.put("actions", objectMapper.convertValue(actions, HashMap.class));
        }

        return  payloadMap;
    }

    public List<SmmContainer> getAllContainers(String stn, String equipmentId, boolean rpcRequest) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        List<SmmContainer> smmContainerList = new ArrayList<>();
        String userId = at3Adapter.getRgwSerialByStn(stn);

        if(rpcRequest) {
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, "/cpe-container/containers", "GET", "{}", ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

            if (rpcResult.get("payload") != null) {
                smmContainerList = objectMapper.convertValue(rpcResult.get("payload"), at3Adapter.getCollectionType(List.class, SmmContainer.class));
            }
        } else {
            smmContainerList = smmContainerRepo.findByUserIdAndSerial(userId, equipmentId);
            smmContainerList = smmContainerList.size() != 0? smmContainerList.stream().filter(p->p.getStatus().equals("Deleted") == false).collect(Collectors.toList()) : smmContainerList;
        }

        return  smmContainerList;
    }

    public String createContainer(String stn, String equipmentId, SmmContainerRequest smmContainerRequest) throws Exception{
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String userId = at3Adapter.getRgwSerialByStn(stn);
        String id = null;

        HashMap<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("id", smmContainerRequest.getService().getServiceId());
        payloadMap.put("actions", objectMapper.convertValue(smmContainerRequest.getActions(), HashMap.class));
        payloadMap.put("service", objectMapper.convertValue(getPayloadService(userId, equipmentId, smmContainerRequest.getService()), HashMap.class));
        payloadMap.put("actionId", CommonUtils.generateUUID());
        String jsonString = objectMapper.writeValueAsString(payloadMap);

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, "/cpe-container/containers", "POST", jsonString, ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

        if (rpcResult.get("payload") != null) {
            List<HashMap<String, String>> payloadList = objectMapper.convertValue(rpcResult.get("payload"), new TypeReference<List<HashMap<String, String>>>(){});
            id = String.valueOf(payloadList.get(0).get("id"));
        }
        return id;
    }

    public List<SmmContainer> getContainerById(String stn, String equipmentId, String containerId, boolean rpcRequest) throws Exception {
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String userId = at3Adapter.getRgwSerialByStn(stn);

        List<SmmContainer> smmContainerList = new ArrayList<>();

        if(rpcRequest) {
            Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, "/cpe-container/containers/" + containerId, "GET", "{}",  ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

            if (rpcResult.get("payload") != null) {
                smmContainerList = objectMapper.convertValue(rpcResult.get("payload"), at3Adapter.getCollectionType(List.class, SmmContainer.class));
            }
        } else {
            smmContainerList = smmContainerRepo.findByUserIdAndSerialAndContainerId(userId, equipmentId, containerId);
            smmContainerList = smmContainerList.size() != 0? smmContainerList.stream().filter(p->p.getStatus().equals("Deleted") == false).collect(Collectors.toList()) : smmContainerList;
        }

        return  smmContainerList;
    }

    public void removeContainer(String stn, String equipmentId, String containerId) throws  Exception{
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String userId = at3Adapter.getRgwSerialByStn(stn);

        cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, "/cpe-container/containers/" + containerId, "DELETE", "{}",  ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
    }

    public String postContainer(String stn, String equipmentId, String containerId, SmmContainerRequest smmContainerRequest) throws Exception{
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
        String userId = at3Adapter.getRgwSerialByStn(stn);

        String id = null;
        String jsonString = objectMapper.writeValueAsString(getPayLoad(userId, equipmentId, smmContainerRequest));
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, "/cpe-container/containers/" + containerId + "/actions", "POST", jsonString,  ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);

        if (rpcResult.get("payload") != null) {
            List<HashMap<String, String>> payloadList = objectMapper.convertValue(rpcResult.get("payload"), new TypeReference<List<HashMap<String, String>>>(){});
            id = String.valueOf(payloadList.get(0).get("id"));
        }

        return  id;
    }
}
