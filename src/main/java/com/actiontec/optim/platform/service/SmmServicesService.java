package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v5.model.SmmServiceRequest;
import com.actiontec.optim.platform.api.v5.model.SmmServiceVo;
import com.actiontec.optim.platform.exception.EntityOverlapException;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.exception.PermissionNotSupportException;
import com.actiontec.optim.platform.mapper.SmmServiceMapper;
import com.incs83.app.constants.queries.EquipmentSQL;
import com.incs83.app.entities.SmmService;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
public class SmmServicesService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private SmmServiceMapper smmServiceMapper;

    @Autowired
    private SmmAppService smmAppService;

    private List<String> getVersionsByServiceId(String serviceId) throws Exception {
        Set<String> versions = smmAppService.getAvailableVersions(serviceId);
        List<String> versionList = new ArrayList<String>(versions);

        return versionList;
    }

    public List<SmmServiceVo> getSmmServices() throws Exception {
        List<SmmServiceVo> smmServicesVoList = new ArrayList<>();
        List<SmmService> smmServices = (List<SmmService>) dataAccessService.read(SmmService.class);
        SmmServiceVo smmServiceVo = null;

        for(SmmService service : smmServices) {
            smmServiceVo = smmServiceMapper.toSmmServiceVo(service, getVersionsByServiceId(service.getId()));

            smmServicesVoList.add(smmServiceVo);
        }

        return smmServicesVoList;
    }

    public String createSmmService(SmmServiceRequest serviceRequest) throws Exception {
        HashMap<String, Object> query = new HashMap<>();
        query.clear();
        query.put("name", serviceRequest.getName());
        long count = Long.valueOf(dataAccessService.readNative(EquipmentSQL.GET_SMMSERVICE_COUNT_BY_NAME, query).iterator().next().toString());

        if (count != 0)
        {
            throw new EntityOverlapException();
        }

        SmmService smmService = new SmmService();
        smmService.setId(CommonUtils.generateUUID());
        smmService.setName(serviceRequest.getName());
        smmService.setDeletable(true);
        dataAccessService.create(SmmService.class, smmService);

        return smmService.getId();
    }

    public SmmServiceVo getSmmSeviceById(String serviceId) throws Exception {
        SmmServiceVo smmServiceVo = null;
        SmmService smmService = (SmmService) dataAccessService.read(SmmService.class, serviceId);

        if (Objects.isNull(smmService)) {
            throw new NoSuchEntityException();
        }

        smmServiceVo = smmServiceMapper.toSmmServiceVo(smmService, getVersionsByServiceId(smmService.getId()));

        return smmServiceVo;
    }

    public Boolean updateSmmService(String serviceId, SmmServiceRequest serviceRequest) throws Exception {
        SmmService smmService  = (SmmService) dataAccessService.read(SmmService.class, serviceId);

        if (smmService == null) {
            throw new NoSuchEntityException();
        }

        smmService.setName(serviceRequest.getName());
        smmService.setProductionVersion(serviceRequest.getProductionVersion());
        dataAccessService.update(SmmService.class, smmService);

        return true;
    }

    public Boolean deleteSmmServiceById(String serviceId) throws Exception {
        SmmService smmService = (SmmService) dataAccessService.read(SmmService.class, serviceId);

        if (smmService == null) {
            throw new NoSuchEntityException();
        }

        if (smmService.getDeletable() == false) {
            throw new PermissionNotSupportException();
        }

        dataAccessService.delete(SmmService.class, serviceId);
        return  true;
    }
}
