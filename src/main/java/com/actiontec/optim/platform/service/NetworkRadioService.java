package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.OperationHandle;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.actiontec.optim.platform.api.v5.model.RadioRequest;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.constant.OperationConstants;
import com.actiontec.optim.platform.exception.CpeRpcResultException;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.model.NetworkRadio;
import com.actiontec.optim.platform.model.NetworkRadioSeries;
import com.actiontec.optim.platform.model.NetworkRadioVirtualKey;
import com.actiontec.optim.platform.model.UserDevice;
import com.actiontec.optim.platform.repository.NetworkRadioRepo;
import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.business.v2.SimpleRpcService;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ActiontecConstants.NEIGHBOR_DETAIL;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.THREAD_TO_SLEEP;

@Service
public class NetworkRadioService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private NetworkRadioRepo networkRadioRepo;

    @Autowired
    private SimpleAesEcbCryptoService cryptoService;

    @Autowired
    private UserDeviceService userDeviceService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SimpleRpcService simpleRpcService;
    @Autowired
    private CpeRpcService cpeRpcService;

    public List<NetworkRadio> findByStnAndSerial(String stn, String serial) {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return networkRadioRepo.findByUserIdAndSerial(userId, serial);
    }

    private Optional<NetworkRadio> findByStnAndSerialAndRadio(String stn, String serial, RadioEnum radioEnum) {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        return networkRadioRepo.findByUserIdAndSerialAndRadio(userId, serial, radioEnum);
    }

    public Optional<NetworkRadio> findByVirtualKey(String stn, String serial, String encryptedVirtualKey) {
        String radioKeyJson = null;
        try {
            radioKeyJson = cryptoService.decrypt(encryptedVirtualKey, ApplicationConstants.ENCRYPT_KEY);
        } catch (Exception e) {
            logger.debug("failed to decrypt virtualKey, stn:[{}], serial:[{}], encryptedVirtualKey:[{}] errMsg:[{}]", stn, serial, encryptedVirtualKey, e.getMessage());
            Optional.empty();
        }

        NetworkRadioVirtualKey radioVirtualKey = null;
        try {
            radioVirtualKey = NetworkRadioVirtualKey.fromJsonString(radioKeyJson);
        } catch (Exception e) {
            logger.debug("failed to parse json, stn:[{}], serial:[{}], encryptedVirtualKey:[{}] errMsg:[{}]", stn, serial, encryptedVirtualKey, e.getMessage());
            Optional.empty();
        }

        if (!StringUtils.equals(serial, radioVirtualKey.getSerial())) {
            logger.debug("serial is not identical with virtualKey, stn:[{}], serial:[{}], encryptedVirtualKey:[{}]", stn, serial, encryptedVirtualKey);
            return Optional.empty();
        }

        return findByStnAndSerialAndRadio(stn, serial, radioVirtualKey.getRadioKey());
    }

    private RadioEnum findRadioKeyByVirtualKey(String serial, String encryptedVirtualKey) {

        String radioKeyJson = null;

        try {
            radioKeyJson = cryptoService.decrypt(encryptedVirtualKey, ApplicationConstants.ENCRYPT_KEY);
        } catch (Exception e) {
            logger.debug("failed to decrypt virtualKey, serial:[{}], encryptedVirtualKey:[{}] errMsg:[{}]", serial, encryptedVirtualKey, e.getMessage());
        }

        NetworkRadioVirtualKey radioVirtualKey = null;
        try {
            radioVirtualKey = NetworkRadioVirtualKey.fromJsonString(radioKeyJson);
        } catch (Exception e) {
            logger.debug("failed to parse json, serial:[{}], encryptedVirtualKey:[{}] errMsg:[{}]", serial, encryptedVirtualKey, e.getMessage());
        }

        if (!StringUtils.equals(serial, radioVirtualKey.getSerial())) {
            logger.debug("serial is not identical with virtualKey, serial:[{}], encryptedVirtualKey:[{}]", serial, encryptedVirtualKey);
            return null;
        }

        return radioVirtualKey.getRadioKey();
    }

    private boolean isExtender(UserDevice userDevice) {

        if(userDevice.getDeviceType() == null) {
            return false;
        }

        if(userDevice.getDeviceType().equals("Extender")) {
            return true;
        }

        return false;
    }

    @PermissionHandle
    @OperationHandle(operation = OperationConstants.WIFI_OP)
    public List<HashMap<String, Object>> putRadio(String stn, String serial, String encryptedVirtualKey, RadioRequest request) throws Exception {
        List<HashMap<String, Object>> responseList = new LinkedList<>();
        String userId = at3Adapter.getRgwSerialByStn(stn);
        RadioEnum radioEnum = findRadioKeyByVirtualKey(serial, encryptedVirtualKey);
        if(radioEnum == null) {
            throw new NoSuchEntityException();
        }

        Map<String, Object> radioData = new HashMap<>();
        radioData.put("enable", request.isEnabled());
        radioData.put("autoChannelEnable", request.isAutoChannelEnabled());
        radioData.put("DFSEnable", request.isDfsEnabled());
        if (request.isAutoChannelEnabled() == false)
            radioData.put("ChannelsInSet", request.getChannel());
        //logger.info("channelBandwidth:" + (int)request.get("channelBandwidth"));
        //((List<String>)request.get("operatingStandards")).stream().forEach(p->logger.info("p:[{}]", p));

        String radioId = radioEnum.getRadioKey();
        String payLoad = objectMapper.writeValueAsString(radioData);
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndExpectSucceeded(userId, serial, String.format(RpcConstants.RADIO_URI, radioId), "PUT", payLoad,  ApplicationConstants.RPC_DEFAULT_MAX_TRIES, ApplicationConstants.RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        if (rpcResult.get("payload") != null) {
            List<HashMap<String, Object>> payloadList = objectMapper.convertValue(rpcResult.get("payload"), new TypeReference<List<HashMap<String, String>>>(){});
            return payloadList;
        }

        return responseList;
    }

    public List<NetworkRadioSeries> findSeriesByStnAndSerialAndRadio(String stn, String serial, String encryptedVirtualKey, long duration) {

        String userId = at3Adapter.getRgwSerialByStn(stn);
        RadioEnum radioEnum = findRadioKeyByVirtualKey(serial, encryptedVirtualKey);
        if(radioEnum == null) {
            throw new NoSuchEntityException();
        }

        List<NetworkRadioSeries> networkRadioSeriesList = networkRadioRepo.findSeriesByUserIdAndSerialAndRadio(userId, serial, radioEnum.getRadioKey(), duration);
        List<UserDevice> userDeviceList = userDeviceService.listByUserId(userId);

//        for(UserDevice userDevice : userDeviceList) {
//            logger.info("MAC: {}, WAN MAC: {}, TYPE: {}", userDevice.getMacAddress(), userDevice.getWanMacAddress(), userDevice.getDeviceType());
//        }

//        List<UserDevice> extenderList = userDeviceList.stream().filter(userDevice -> isExtender(userDevice)).collect(Collectors.toList());

        for(NetworkRadioSeries networkRadioSeries : networkRadioSeriesList) {
            List<NetworkRadioSeries.AirTimeBusyDeviceDistributionDto> airTimeBusyDeviceDistributionDtoList = networkRadioSeries.getAirTimeBusyDeviceDistribution();

//            airTimeBusyDeviceDistributionDtoList = airTimeBusyDeviceDistributionDtoList.stream().sorted(Comparator.comparing(e -> e.getAirTimeBusy(), Comparator.reverseOrder())).limit(5).collect(Collectors.toList());

            for(NetworkRadioSeries.AirTimeBusyDeviceDistributionDto airTimeBusyDeviceDistributionDto : airTimeBusyDeviceDistributionDtoList) {
                for(UserDevice userDevice : userDeviceList) {
                    if(airTimeBusyDeviceDistributionDto.getMacAddress().equals(userDevice.getMacAddress())) {
                        airTimeBusyDeviceDistributionDto.setName(userDevice.getFriendlyName() == null ? userDevice.getHostName(): userDevice.getFriendlyName());
                        airTimeBusyDeviceDistributionDto.setDeviceType(userDevice.getDeviceType().name());
                        break;
                    } else {
                        if(airTimeBusyDeviceDistributionDto.getMacAddress().equals(userDevice.getWanMacAddress())) {
                            airTimeBusyDeviceDistributionDto.setMacAddress(userDevice.getMacAddress());
                            airTimeBusyDeviceDistributionDto.setName(userDevice.getFriendlyName() == null ? userDevice.getHostName(): userDevice.getFriendlyName());
                            airTimeBusyDeviceDistributionDto.setDeviceType(userDevice.getDeviceType().name());
                            break;
                        }
                    }
                }
            }

//            if(extenderList != null) {
//                for(UserDevice extender : extenderList) {
//                    NetworkRadioSeries.AirTimeBusyDeviceDistributionDto airTimeBusyDeviceDistributionDto = new NetworkRadioSeries.AirTimeBusyDeviceDistributionDto();
//                    airTimeBusyDeviceDistributionDto.setMacAddress(extender.getMacAddress());
//                    airTimeBusyDeviceDistributionDto.setName(extender.getFriendlyName() == null ? extender.getHostName(): extender.getFriendlyName());
//                    airTimeBusyDeviceDistributionDto.setDeviceType(extender.getDeviceType());
//                    airTimeBusyDeviceDistributionDto.setAirTimeBusy(0);
//
//                    airTimeBusyDeviceDistributionDtoList.add(airTimeBusyDeviceDistributionDto);
//                }
//            }

            networkRadioSeries.setAirTimeBusyDeviceDistribution(airTimeBusyDeviceDistributionDtoList);
        }
        return networkRadioSeriesList;
    }

    @PermissionHandle
    @OperationHandle(operation = OperationConstants.WIFI_OP)
    public void postAction(String stn, String equipmentId, String radioId, String action) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        RadioEnum radioEnum = findRadioKeyByVirtualKey(equipmentId, radioId);
        if(radioEnum == null) {
            throw new NoSuchEntityException();
        }
        String band = radioEnum.getRadioKey();

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(LONG_RPC_POLL_COUNT));
        if (action.equals("RescanNeighbor")) {
            HashMap<String, String> queryParams = new HashMap<>();
            HashMap<String, String> appendableParams = new HashMap<>();
            queryParams.put("serialNumber", equipmentId);

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
            String isp = String.valueOf(aPDetails.get("isp"));

            List<Object> requestList = new ArrayList<>();
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("action", RpcConstants.ACTION_NEIGHBOR_SCAN);

            HashMap<String, Object> param = new HashMap<>();
            param.put("uri", String.format(RpcConstants.RADIO_ACTION_URI, band));
            param.put("method", "POST");
            param.put("payload", payloadMap);
            requestList.add(param);
            if (requestList.isEmpty()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Don't support band of " + band);
            }
            String request = null;
            request = objectMapper.writeValueAsString(requestList);
            String tid = CommonUtils.generateUUID();

            Map<String, Object> rpcResult = simpleRpcService.sendRpc31AndWaitResult(tid, userId, equipmentId, isp, request, max_Tries, THREAD_TO_SLEEP);
            List<Map<String, Object>> responses = (List<Map<String, Object>>) rpcResult.get("response");
            if (request == null || responses.isEmpty())
                throw new CpeRpcResultException(CpeRpcResultException.FailedReason.FAILED_RESPONSE);

            ZonedDateTime ldtNow = ZonedDateTime.now();
            BasicDBList neighborList = new BasicDBList();
            for (Map<String, Object> response : responses) {
                List<Map<String, Object>> datas = (List<Map<String, Object>>) response.get("data");
                for (Map<String, Object> data : datas) {
                    List<Map<String, Object>> neighbors = (List<Map<String, Object>>) data.get("List");
                    for (Map<String, Object> neighbor : neighbors) {
                        BasicDBObject n = new BasicDBObject();
                        n.put("ssid", String.valueOf(neighbor.get("ssid")));
                        n.put("bssid", String.valueOf(neighbor.get("bssid")));
                        n.put("mode", String.valueOf(neighbor.get("mode")));
                        n.put("channel", Integer.valueOf(neighbor.get("channel").toString()));
                        n.put("bandwidth", Integer.valueOf(neighbor.get("bandwidth").toString()));
                        n.put("DTIMPeriod", Integer.valueOf(neighbor.get("DTIMPeriod").toString()));
                        n.put("beaconPeriod", Integer.valueOf(neighbor.get("beaconPeriod").toString()));
                        n.put("signalStrength", Integer.valueOf(neighbor.get("signalStrength").toString()));
                        n.put("noise", Integer.valueOf(neighbor.get("noise").toString()));
                        n.put("RadioIdx", String.valueOf(neighbor.get("RadioIdx")));
                        n.put("SupportedStandards", neighbor.get("SupportedStandards"));
                        neighborList.add(n);
                    }
                }
            }

            if (!neighborList.isEmpty()) {
                BasicDBObject neighborObj = new BasicDBObject();
                neighborObj.put("dateCreated", new Date());
                neighborObj.put("dateHour", ldtNow.truncatedTo(ChronoUnit.HOURS).toEpochSecond());
                neighborObj.put("timestamp", ldtNow.toInstant().toEpochMilli());
                neighborObj.put("userId", userId);
                neighborObj.put("serialNumber", equipmentId);
                neighborObj.put("triggerBy", "Manually");
                neighborObj.put("neighbors", neighborList);

                mongoService.create(NEIGHBOR_DETAIL, neighborObj);
            }
        } else if (action.equals("Reset") || action.equals("RescanChannel")) {
            String rpcUri = String.format(RpcConstants.RADIO_ACTION_URI, band);
            Map<String, Object> payloadMap = new HashMap<>();
            if (action.equals("Reset"))
                payloadMap.put("action", RpcConstants.ACTION_WIFI_RESET);
            else
                payloadMap.put("action", RpcConstants.ACTION_DO_ACS);
            String payload = objectMapper.writeValueAsString(payloadMap);
            cpeRpcService.sendRpcAndExpectSucceeded(userId, equipmentId, rpcUri, "POST", payload, max_Tries, THREAD_TO_SLEEP);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Serial: %s %s don't support", equipmentId, action));
        }
    }
}