package com.actiontec.optim.platform.service;

import org.apache.commons.io.FileUtils;
import org.springframework.stereotype.Service;
import java.nio.file.Files;
import java.nio.file.Paths;
import com.actiontec.optim.platform.model.EquipmentTrackingRecord;
import org.apache.commons.csv.QuoteMode;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import java.io.*;
import java.util.Arrays;
import java.util.List;

@Service
public class CSVService {

    public long converArray2CSV(List<String> input, String path) {
        BufferedWriter writer = null;
        int i = 0;
        long len = 0;
        try {
            writer = Files.newBufferedWriter(Paths.get(path));
            CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT);
            csvPrinter.printRecord("serial");

            for (String e : input) {
                csvPrinter.printRecord(e);
            }
            csvPrinter.flush();
            csvPrinter.close();

            len = FileUtils.sizeOf(new File(path));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return len;
    }

    public ByteArrayInputStream recordsToCvs(List<EquipmentTrackingRecord> equipmentTrackingRecords) {
        final CSVFormat format = CSVFormat.DEFAULT.withQuoteMode(QuoteMode.MINIMAL);

        try (ByteArrayOutputStream out = new ByteArrayOutputStream();
             CSVPrinter csvPrinter = new CSVPrinter(new PrintWriter(out), format);) {

            for(EquipmentTrackingRecord equipmentTrackingRecord : equipmentTrackingRecords) {
                List<String> data = Arrays.asList(
                        equipmentTrackingRecord.getId(),
                        equipmentTrackingRecord.getSerialNumber(),
                        equipmentTrackingRecord.getIspId(),
                        equipmentTrackingRecord.getEquipmentStatus(),
                        String.valueOf(equipmentTrackingRecord.getProvisionedTime()),
                        String.valueOf(equipmentTrackingRecord.getOnboardTime()),
                        String.valueOf(equipmentTrackingRecord.getFirstReportTime()),
                        String.valueOf(equipmentTrackingRecord.getLastReportTime())
                );

                csvPrinter.printRecord(data);
            }

            csvPrinter.flush();
            return new ByteArrayInputStream(out.toByteArray());

        } catch (IOException ex) {
            throw new RuntimeException("fail to import data to CSV file: " + ex.getMessage());
        }
    }
}
