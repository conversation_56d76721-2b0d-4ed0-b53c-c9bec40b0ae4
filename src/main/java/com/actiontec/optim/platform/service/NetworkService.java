package com.actiontec.optim.platform.service;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.model.NetworkRequest;
import com.actiontec.optim.platform.model.NetworkContract;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.queries.EquipmentSQL;
import com.incs83.app.entities.ClusterInfo;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.Subscriber;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.business.ESService;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.Constants.ESConstants.EXT_MAC;
import static com.incs83.Constants.ESConstants.EXT_SERIAL;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.INTERNET_SERVICE_PROVIDER;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.queries.ClusterInfoSQL.DELETE_EQUIPMENT_FROM_CLUSTER_BY_EQUIPMENT_ID;
import static com.incs83.app.constants.queries.ClusterInfoSQL.MAPPING_OF_CLUSTER_AND_EQUIPMENT;
import static com.incs83.constants.ApplicationCommonConstants.DEFAULT;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_INDEX;

@Service
public class NetworkService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private ESService esService;

    @Value("${elastic-search.enable}")
    private Boolean elasticSearchEnable;

    private String getSubscriberIdByLoggedInUser(String subscriberId) throws Exception {
        String _subscriberId = ExecutionContext.get().getUsercontext().getId();
        if(!subscriberId.equals("") && !_subscriberId.equals(subscriberId)) {
            throw new OptimApiException(HttpStatus.FORBIDDEN, "");
        }
        return _subscriberId;
    }

    private HashMap<String, Object> prepareDataForES2(Equipment equipment, Subscriber subscriber) {
        List<String> clusters = new ArrayList<>();
        HashMap<String, Object> userAPMap = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());
            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster() && cluster.getName().equals(DEFAULT + compartment.getName())).findAny().orElse(null);
            if (Objects.nonNull(clusterInfo)) {
                clusters.add(clusterInfo.getId());
            }

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", equipment.getRgwSerial());
            HashMap<String, Object> projection = new HashMap<>();
            projection.put("type", 1);
            projection.put("serialNumber", 1);
            projection.put("macAddress", 1);
            List<HashMap<String, String>> apDetails = mongoService.findList(queryParams, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, projection);
            List<Map<String, Object>> extInfo = new ArrayList<>();
            apDetails.stream().filter(ap -> !GATEWAY.equals(ap.get("type"))).forEach(elem -> {
                Map<String, Object> extender = new HashMap<>();
                extender.put(EXT_MAC, elem.get("macAddress"));
                extender.put(EXT_SERIAL, elem.get("serialNumber"));
                extInfo.add(extender);
            });

            userAPMap = new HashMap<>();
            userAPMap.put("id", equipment.getId());
            userAPMap.put("name", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getFirstName()) ? subscriber.getFirstName() + " " + subscriber.getLastName() : equipment.getRgwSerial() + " " + equipment.getRgwMAC());
            userAPMap.put("createdAt", equipment.getCreatedAt());
            userAPMap.put("createdBy", equipment.getCreatedBy());
            userAPMap.put("email", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getEmail()) ? subscriber.getEmail() : equipment.getRgwSerial() + "@optim.com");
            userAPMap.put("subscriberId", equipment.getSubscriber().getId());
            userAPMap.put("mobileSubscribed", false);
            userAPMap.put("downLinkRate", equipment.getDownLinkRate());
            userAPMap.put("upLinkRate", equipment.getUpLinkRate());
            userAPMap.put("rgwMAC", equipment.getRgwMAC());
            userAPMap.put("apId", equipment.getRgwSerial());
            userAPMap.put("serialNumber", equipment.getRgwSerial());
            userAPMap.put("groupId", equipment.getGroupId());
            userAPMap.put("phoneNo", equipment.getServiceTelephoneNo());
            userAPMap.put("globalAccountNo", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getGlobalAccountNo()) ? subscriber.getGlobalAccountNo() : null);
            userAPMap.put("camsAccountNo", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getCamsAccountNo()) ? subscriber.getCamsAccountNo() : null);
            userAPMap.put("extInfo", extInfo);
            userAPMap.put("clusters", clusters);
            userAPMap.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
        } catch (Exception e) {
            logger.error("ERROR in prepare data for ES.");
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }
        return userAPMap;
    }

    public List<NetworkContract> getNetworkContractByUserId(String subscriberId) throws Exception {

        String userId = CommonUtils.getUserIdOfLoggedInUser();
        List<NetworkContract> networkContractList = new ArrayList<>();

        if(CommonUtils.isEndUser()) {
            subscriberId = getSubscriberIdByLoggedInUser(subscriberId);
        }

        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("subscriberId", subscriberId);
        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_FOR_SUBSCRIBER, queryParams);

        for(Equipment equipment : equipmentList) {

            NetworkContract networkContract = new NetworkContract();
            networkContract.setId(equipment.getId());
            networkContract.setServiceTelephoneNumber(equipment.getServiceTelephoneNo());
            networkContract.setName(equipment.getFriendlyName());

            logger.info("Debug userId:[{}] equipment:[{}]", userId, equipment.getCreatedBy());
            if(equipment.getCreatedBy().equals(userId)) {
                networkContract.setIspManaged(false);
            } else {
                networkContract.setIspManaged(true);
            }

            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());

            BasicDBObject query = new BasicDBObject();
            query.put("id", compartment.getIspId());
            DBObject ispObject = mongoService.findOne(INTERNET_SERVICE_PROVIDER, query);

            NetworkContract.ispDto ispDto = new NetworkContract.ispDto();
            ispDto.setId(compartment.getIspId());
            ispDto.setName(String.valueOf(ispObject.get("displayName")));

            NetworkContract.subscriptionDto subscriptionDto = new NetworkContract.subscriptionDto();
            subscriptionDto.setDownlinkRate(equipment.getDownLinkRate());
            subscriptionDto.setUplinkRate(equipment.getUpLinkRate());

            networkContract.setIsp(ispDto);
            networkContract.setSubscription(subscriptionDto);

            networkContractList.add(networkContract);
        }

        return networkContractList;
    }

    public List<NetworkContract> getNetworkContractByStn(String stn) throws Exception {

        String userId = CommonUtils.getUserIdOfLoggedInUser();
        List<NetworkContract> networkContractList = new ArrayList<>();

        String loggedInUserSubscriberId = getSubscriberIdByLoggedInUser("");

        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("id", stn);

        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_LIST_BY_EQUIPMENT_ID_OR_RGW_SERIAL_OR_STN, queryParams);

        for(Equipment equipment : equipmentList) {

            NetworkContract networkContract = new NetworkContract();
            networkContract.setId(equipment.getId());
            networkContract.setServiceTelephoneNumber(equipment.getServiceTelephoneNo());
            networkContract.setName(equipment.getFriendlyName());

            if(equipment.getCreatedBy().equals(userId)) {
                networkContract.setIspManaged(false);
            } else {
                networkContract.setIspManaged(true);
            }

            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());

            BasicDBObject query = new BasicDBObject();
            query.put("id", compartment.getIspId());
            DBObject ispObject = mongoService.findOne(INTERNET_SERVICE_PROVIDER, query);

            NetworkContract.ispDto ispDto = new NetworkContract.ispDto();
            ispDto.setId(compartment.getIspId());
            ispDto.setName(String.valueOf(ispObject.get("displayName")));

            NetworkContract.subscriptionDto subscriptionDto = new NetworkContract.subscriptionDto();
            subscriptionDto.setDownlinkRate(equipment.getDownLinkRate());
            subscriptionDto.setUplinkRate(equipment.getUpLinkRate());

            networkContract.setIsp(ispDto);
            networkContract.setSubscription(subscriptionDto);

            if(CommonUtils.isSysAdmin()) {
                networkContractList.add(networkContract);
            } else if(CommonUtils.isEndUser()) {
                if (loggedInUserSubscriberId.equals(equipment.getSubscriber().getId())) {
                    networkContractList.add(networkContract);
                }
            } else {
                if(compartment.getId() == CommonUtils.getGroupIdOfLoggedInUser()) {
                    networkContractList.add(networkContract);
                }
            }
        }

        return networkContractList;
    }

    public Map<String, String> createNetworkContract(NetworkRequest networkRequest) throws Exception {

        Map<String, String> retVal = new HashMap<>();

        Equipment equipment = new Equipment();
        String id = CommonUtils.generateUUID();
        Subscriber subscriber;

        if(CommonUtils.isEndUser()) {
            String subscriberId = ExecutionContext.get().getUsercontext().getId();
            subscriber = (Subscriber) dataAccessService.read(Subscriber.class, subscriberId);

            if(networkRequest.getSubscription() != null) {
                equipment.setDownLinkRate(networkRequest.getSubscription().getDownlinkRate());
                equipment.setUpLinkRate(networkRequest.getSubscription().getUplinkRate());
            } else {
                equipment.setDownLinkRate(0);
                equipment.setUpLinkRate(0);
            }
        } else {
            subscriber = (Subscriber) dataAccessService.read(Subscriber.class, networkRequest.getSubscriberId());
        }

        if(subscriber == null) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The user don't match any subscriber.");
        }

        equipment.setId(id);
        equipment.setFriendlyName(networkRequest.getName());
        equipment.setGroupId(subscriber.getCompartment().iterator().next().getId());
        equipment.setServiceTelephoneNo(networkRequest.getServiceTelephoneNumber() != null ? networkRequest.getServiceTelephoneNumber() : id);
        equipment.setSubscriber(subscriber);
        equipment.setRgwSerial(id);
        CommonUtils.setCreateEntityFields(equipment);

        dataAccessService.create(Equipment.class, equipment);

        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, subscriber.getCompartment().iterator().next().getId());

        ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster()).findAny().orElse(null);
        if (Objects.nonNull(clusterInfo)) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("clusterId", clusterInfo.getId());
            param.put("equipmentId", equipment.getId());
            dataAccessService.deleteUpdateNative(MAPPING_OF_CLUSTER_AND_EQUIPMENT, param);
        }

        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("serialNumber", equipment.getRgwSerial());
        queryParam.put("userId", equipment.getRgwSerial());
        DBObject dbObject = mongoService.findOne(AP_DETAIL, queryParam);

        if (Objects.isNull(dbObject)) {
            HashMap<String, Object> apDetail = new HashMap<>();
            apDetail.put("userId", equipment.getRgwSerial());
            apDetail.put("serialNumber", equipment.getRgwSerial());
            apDetail.put("macAddress", equipment.getRgwMAC());
            apDetail.put("type", GATEWAY);
//            apDetail.put("isp", createEquipmentRequest.getIsp());
            apDetail.put("processed", true);
            apDetail.put("mysqlProcessed", true);
            mongoService.create(AP_DETAIL, apDetail);
        }

        BasicDBObject record;
        DBObject update;
        DBObject query;

        if (elasticSearchEnable) {
            try {
                HashMap<String, Object> equipmentMap = prepareDataForES2(equipment, subscriber);
                esService.insert(EQUIPMENT_INDEX, equipmentMap, String.valueOf(equipmentMap.get("id")), null);
                record = new BasicDBObject();
                record.put("esProcessed", true);
                update = new BasicDBObject("$set", record);
                query = new BasicDBObject("userId", equipment.getRgwSerial());
                mongoService.update(query, update, true, false, ApplicationCommonConstants.AP_DETAIL);
            } catch (Exception e) {
                logger.error("Error in updating esProcessed true. AP ID ::" + equipment.getRgwSerial());
            }
        }

        retVal.put("id", id);
        return retVal;
    }

    public void editNetworkContract(String stn, NetworkRequest networkRequest) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);

        equipment.setFriendlyName(networkRequest.getName());
        if(!CommonUtils.isEndUser()) {
            equipment.setDownLinkRate(networkRequest.getSubscription().getDownlinkRate());
            equipment.setUpLinkRate(networkRequest.getSubscription().getUplinkRate());
        }
        CommonUtils.setUpdateEntityFields(equipment);

        dataAccessService.update(Equipment.class, equipment);
        esService.updateById(EQUIPMENT_INDEX, prepareDataForES2(equipment, equipment.getSubscriber()), equipment.getId(), null);

    }

    public void deleteNetworkContract(String stn) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if(CommonUtils.isEndUser()) {
            if(!equipment.getCreatedBy().equals(ExecutionContext.get().getUsercontext().getId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The user can't delete STN: " + stn);
            }
        }

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", equipment.getRgwSerial());
        query.put("onBoarding", true);
        long totalEquipment = mongoService.count(query, null, ApplicationCommonConstants.AP_DETAIL);
        if(totalEquipment > 0) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This network is in use");
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("equipmentId", equipment.getId());
        dataAccessService.deleteUpdateNative(DELETE_EQUIPMENT_FROM_CLUSTER_BY_EQUIPMENT_ID, param);

        dataAccessService.delete(Equipment.class, equipment.getId());
        esService.delete(EQUIPMENT_INDEX, equipment.getId());
    }
}
