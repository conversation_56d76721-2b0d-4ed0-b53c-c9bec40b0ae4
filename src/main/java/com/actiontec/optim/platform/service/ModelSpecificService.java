package com.actiontec.optim.platform.service;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ModelSpecificService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    private static Map<String, Integer> MODEL_MAC_NUMBER_MAP;

    static {
        MODEL_MAC_NUMBER_MAP = new HashMap<>();
        MODEL_MAC_NUMBER_MAP.put("WEB7200", 11);
        MODEL_MAC_NUMBER_MAP.put("WCB6200Q", 11);
        MODEL_MAC_NUMBER_MAP.put("WEB8500TB", 11);
    }

    public boolean isSameEquipment(String modelName, String mac1, String mac2) {
        long macValue1 = Long.parseLong(mac1.replaceAll(":", ""), 16);
        long macValue2 = Long.parseLong(mac2.replaceAll(":", ""), 16);

        int macNumber = 0;
        if (MODEL_MAC_NUMBER_MAP.get(modelName) == null) {
            logger.warn("no match extender model:[{}]", modelName);
        } else {
            macNumber = MODEL_MAC_NUMBER_MAP.get(modelName);
        }

        if (macValue1 <= macValue2 && macValue1 + macNumber >= macValue2) {
            return true;
        }
        return false;
    }

    public boolean isSameEquipment(String mac1, String mac2) {
        // ignore the first byte
        mac1 = mac1.substring(3);
        mac2 = mac2.substring(3);

        long macValue1 = Long.parseLong(mac1.replaceAll(":", ""), 16);
        long macValue2 = Long.parseLong(mac2.replaceAll(":", ""), 16);

        long macDelta = macValue1 - macValue2;

        // XXX: hard to tell if is same equipment with base mac, wan mac, wireless mac,
        //      just take a simple workaround, mac +- 15 will be treat as same equipment
        if (macDelta <= 15 && macDelta >= -15) {
            return true;
        }
        return false;
    }
}
