package com.actiontec.optim.platform.service;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dao.HostDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.HostDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.AlertHistory;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

import static com.actiontec.optim.platform.constant.ApplicationConstants.SECONDS_OF_DAY;

@Service
public class AlertService {
    private final Logger logger = LogManager.getLogger(this.getClass());
    public static final String COLLECTION_EQUIPMENT_EVENTS = "equipmentEvents";
    public static final String COLLECTION_EQUIPMENT_KPI = "equipmentKpi";
    public static final String COLLECTION_DEVICE_EVENTS = "deviceEvents";
    public static final String COLLECTION_DEVICE_KPI = "deviceKpi";
    public static final String COLLECTION_RADIO_EVENTS = "radioEvents";
    public static final String COLLECTION_RADIO_KPI = "radioKpi";
    private HashSet<String> equipmentDeviceInstIds;
    private HashSet<String> equipmentRadioInstIds;
    private HashMap<String, Long> reportTimeMap;

    private EnumMap<EventType, EventNode>  typeEventNodeEnumMap;
    private EnumMap<EventType, String>  reasonTypeEnumMap;
    private List<AlertHistory> alertHistoryResponses;

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private HostDao hostDao;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private ObjectMapper objectMapper;
    @PostConstruct
    private void init() {
        equipmentDeviceInstIds = new HashSet<>();
        equipmentRadioInstIds = new HashSet<>();
        reportTimeMap = new HashMap<>();
        typeEventNodeEnumMap = new EnumMap<>(EventType.class);
        reasonTypeEnumMap =  new EnumMap<>(EventType.class);

        for (int i = 0; i< EventType.values().length; i++) {
            typeEventNodeEnumMap.put(EventType.values()[i], new EventNode());
            reasonTypeEnumMap.put(EventType.values()[i],  ReasonType.values()[i].getValue());
        }
    }

    private void reset() {
        equipmentDeviceInstIds.clear();
        equipmentRadioInstIds.clear();
        reportTimeMap.clear();
        alertHistoryResponses = new ArrayList<>();

        typeEventNodeEnumMap.forEach((key, value) ->{
            value.reset();
        });
    }

    public enum EventType {
        LastSeen, MemUsage, RadioEnabled, AirtimeUsage, InternetUsage, InternetUpload, BootCount, Rssi, Standard, DfsSupport
    }

    public enum LevelType {
        Equipment, Device, Network
    }

    public enum SeverityType {
        Info, Warning, Error;

        public static String getName(String input) {
            for(SeverityType item: SeverityType.values()) {
                if (StringUtils.equals(item.name(), input))
                    return item.name();
            }
           return null;
        }
    }

    public enum ReasonType {
        LastSeen("No report"),
        MemUsage("High memory usage"),
        RadioEnabled("Radios disabled"),
        AirtimeUsage("High airtime usage"),
        InternetUsage("High downlink usage"),
        InternetUpload("High uplink usage"),
        BootCount("Reboot"),
        Rssi("Low signal"),
        Standard("Legacy operating standard"),
        DfsSupport("No DFS");

        String value;

        ReasonType(String value) {
            this.value = value;
        }
        public String getValue() {
            return value;
        }
    }

    private class EventNode {
        private int lastColor;
        private int curColor;
        private boolean sustained;
        private AlertHistory history;

        public void reset() {
            lastColor = 0;
            curColor = 0;
            sustained = false;
        }

        public int getLastColor() {
            return lastColor;
        }

        public void setLastColor(int lastColor) {
            this.lastColor = lastColor;
        }

        public int getCurColor() {
            return curColor;
        }

        public void setCurColor(int curColor) {
            this.curColor = curColor;
        }

        public boolean getSustained() {
            return sustained;
        }

        public void setSustained(boolean sustained) {
            this.sustained = sustained;
        }

        public AlertHistory getHistory() {
            return history;
        }

        public void setHistory(AlertHistory history) {
            this.history = history;
        }
    }

    public static class KPIsData {
        private String instId;
        private String radioKey;
        private long eventTime;
        private Kpi currentKpi;
        private Kpi lastKpi;
        private CurrentColor currentColor;

        public String getInstId() {
            return instId;
        }

        public void setInstId(String instId) {
            this.instId = instId;
        }

        public  String getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(String radioKey) {
            this.radioKey = radioKey;
        }

        public long getEventTime() {
            return eventTime;
        }

        public void setEventTime(long eventTime) {
            this.eventTime = eventTime;
        }

        public Kpi getCurrentKpi() {
            return currentKpi;
        }

        public void setCurrentKpi(Kpi currentKpi) {
            this.currentKpi = currentKpi;
        }

        public Kpi getLastKpi() {
            return lastKpi;
        }

        public void setLastKpi(Kpi lastKpi) {
            this.lastKpi = lastKpi;
        }

        public CurrentColor getCurrentColor() {
            return currentColor;
        }

        public void setCurrentColor(CurrentColor currentColor) {
            this.currentColor = currentColor;
        }
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class Kpi {
            private long timestamp;
            private int bootCount;

            public long getTimestamp() {
                return timestamp;
            }

            public void setTimestamp(long timestamp) {
                this.timestamp = timestamp;
            }

            public int getBootCount() {
                return bootCount;
            }

            public void setBootCount(int bootCount) {
                this.bootCount = bootCount;
            }
        }

        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class CurrentColor {
            private int lastSeen;
            private int memUsage;
            private int radioEnabled;
            private int airtimeColor;
            private int internetUsage;
            private int internetUpload;
            private int bootCount;
            private int rssi;
            private int standard;
            private int dfsSupport;

            public int getLastSeen() {
                return lastSeen;
            }

            public void setLastSeen(int lastSeen) {
                this.lastSeen = lastSeen;
            }

            public int getMemUsage() {
                return memUsage;
            }

            public void setMemUsage(int memUsage) {
                this.memUsage = memUsage;
            }

            public int getRadioEnabled() {
                return radioEnabled;
            }

            public void setRadioEnabled(int radioEnabled) {
                this.radioEnabled = radioEnabled;
            }

            public int getAirtimeColor() {
                return airtimeColor;
            }

            public void setAirtimeColor(int airtimeColor) {
                this.airtimeColor = airtimeColor;
            }

            public int getInternetUsage() {
                return internetUsage;
            }

            public void setInternetUsage(int internetUsage) {
                this.internetUsage = internetUsage;
            }

            public int getInternetUpload() {
                return internetUpload;
            }

            public void setInternetUpload(int internetUpload) {
                this.internetUpload = internetUpload;
            }

            public int getBootCount() {
                return bootCount;
            }

            public void setBootCount(int bootCount) {
                this.bootCount = bootCount;
            }

            public int getRssi() {
                return rssi;
            }

            public void setRssi(int rssi) {
                this.rssi = rssi;
            }

            public int getStandard() {
                return standard;
            }

            public void setStandard(int standard) {
                this.standard = standard;
            }

            public int getDfsSupport() {
                return dfsSupport;
            }

            public void setDfsSupport(int dfsSupport) {
                this.dfsSupport = dfsSupport;
            }
        }
    }

    private void fetchReportTime(String userId) {
        List<ApDetailDto> apDetailDtos = apDetailDao.listByUserId(userId);
        apDetailDtos.forEach(p-> {
            if (Objects.nonNull(p.getSerialNumber()))
                reportTimeMap.put(p.getSerialNumber(), p.getTimestamp());
        });

        List<HostDto> hostDtos = hostDao.listByUserId(userId, 0);
        hostDtos.forEach(p->{
            if (Objects.nonNull(p.getMacAddress()))
                reportTimeMap.put(p.getMacAddress(), p.getTimestamp());
        });
    }


    private long getLatestEndTime(EventType eventType, int color, boolean end, long eventTime, String instId) {
        long endTime = eventTime;
        if (end && color>0) {
            if (eventType == EventType.LastSeen) {
                endTime = CommonUtils.getCurrentTimeInMillis();
            } else {
                if(reportTimeMap.get(instId) != null) {
                    endTime = reportTimeMap.get(instId);
                }
            }
        }

        return endTime;
    }

    private void processKpiDataToHistory(EventType eventType, int color, long eventTime, String levelType, String instId, boolean end,  Object indicator) {
        EventNode eventNodeBuf = typeEventNodeEnumMap.get(eventType);
        eventNodeBuf.setCurColor(color);

        if (eventNodeBuf.getSustained() == true) {
            if (eventNodeBuf.getLastColor() != eventNodeBuf.getCurColor()) {
                eventNodeBuf.getHistory().setEndTime(eventTime);
                alertHistoryResponses.add(eventNodeBuf.getHistory());
                if (color > 0) {
                    AlertHistory history = new AlertHistory();
                    history.setInstId(instId);
                    history.setType(levelType);
                    history.setSeverity(SeverityType.values()[color].name());
                    history.setReason(reasonTypeEnumMap.get(eventType));
                    history.setStartTime(eventTime);

                    if (EventType.AirtimeUsage == eventType) {
                        history.getIndicators().setRadioKey(String.valueOf(indicator));
                    } else if (EventType.BootCount == eventType) {
                        history.getIndicators().setRebootTimes((int)indicator);
                    }

                    eventNodeBuf.setHistory(history);
                }
            }
        } else if (color > 0){
            eventNodeBuf.setSustained(true);
            AlertHistory history = new AlertHistory();
            history.setInstId(instId);
            history.setType(levelType);
            history.setSeverity(SeverityType.values()[color].name());
            history.setReason(reasonTypeEnumMap.get(eventType));
            history.setStartTime(eventTime);

            if (EventType.AirtimeUsage == eventType) {
                history.getIndicators().setRadioKey(String.valueOf(indicator));
            } else if (EventType.BootCount == eventType) {
                history.getIndicators().setRebootTimes((int)indicator);
            }

            eventNodeBuf.setHistory(history);
        }

        eventNodeBuf.setLastColor(color);

        if (color > 0 && end == true) {
            long endTime = getLatestEndTime(eventType, color, end, eventTime, instId);
            eventNodeBuf.getHistory().setEndTime(endTime);
            if (eventType == EventType.BootCount)
                eventNodeBuf.getHistory().setActive(false);
            else
                eventNodeBuf.getHistory().setActive((endTime > System.currentTimeMillis()- 5*60000)? true: false);

            alertHistoryResponses.add(eventNodeBuf.getHistory());
            eventNodeBuf.reset();
        }
        if (color == 0 || end == true ){
            eventNodeBuf.reset();
        }
    }


    private static final String matchQuery = "{$match: {userId:'%s'}}";
    private static final String outPutQuery = "{$project: {_id:0, KPIs:'$KPIs'}}";
    private int processEventData(String userId, String collection, Map<String, String> opsMap) {
        boolean end;
        int size = 0;

        List<DBObject> aggregatePipeline = new ArrayList<>();
        aggregatePipeline.add(BasicDBObject.parse(String.format(matchQuery, userId)));
        aggregatePipeline.add(BasicDBObject.parse(opsMap.get("project")));

        if (Objects.nonNull(opsMap.get("sort"))) {
            aggregatePipeline.add(BasicDBObject.parse(opsMap.get("sort")));
        }

        aggregatePipeline.add(BasicDBObject.parse(opsMap.get("group")));
        aggregatePipeline.add(BasicDBObject.parse(String.format(outPutQuery)));
        Iterator iterator =  at3Adapter.getMongoDbCollection(collection).aggregate(aggregatePipeline).results().iterator();
        while (iterator.hasNext()) {
            BasicDBObject resultObject = (BasicDBObject) iterator.next();
            DBObject kpi = (DBObject) resultObject.get("KPIs");
            if (Objects.nonNull(kpi)) {
               List<KPIsData> kpisDataList = objectMapper.convertValue(kpi, at3Adapter.getCollectionType(List.class, KPIsData.class));

                if (Objects.nonNull(kpisDataList) && !kpisDataList.isEmpty()) {
                   size = kpisDataList.size();
                   for(int i= 0; i < size; i++) {
                       KPIsData kpisData = kpisDataList.get(i);
                       if (Objects.isNull(kpisData) || Objects.isNull(kpisData.getCurrentColor())) {
                           continue;
                       }

                       if (collection.equals(COLLECTION_RADIO_EVENTS)) {
                           equipmentRadioInstIds.add(kpisData.getInstId());
                       } else if (StringUtils.endsWith(collection, "Events")) {
                           equipmentDeviceInstIds.add(kpisData.getInstId());
                       }

                       end = (size==i+1);
                       long eventTime = 0;
                       if (StringUtils.endsWith(collection, "Events")) {
                           eventTime = kpisData.getEventTime();
                       }

                       if (collection.equals(COLLECTION_EQUIPMENT_EVENTS) || (collection.equals(COLLECTION_EQUIPMENT_KPI) && !equipmentDeviceInstIds.contains(kpisData.getInstId()))) {
                           processKpiDataToHistory(EventType.LastSeen, kpisData.getCurrentColor().getLastSeen(), eventTime, LevelType.Equipment.name(), kpisData.getInstId(), end, null);//active should be true for the last LastSeen event
                           if (collection.equals(COLLECTION_EQUIPMENT_EVENTS) || (collection.equals(COLLECTION_EQUIPMENT_KPI) && kpisData.getCurrentColor().getLastSeen() == 0)) {
                               processKpiDataToHistory(EventType.MemUsage, kpisData.getCurrentColor().getMemUsage(), eventTime, LevelType.Equipment.name(), kpisData.getInstId(), end, null);
                               processKpiDataToHistory(EventType.RadioEnabled, kpisData.getCurrentColor().getRadioEnabled(), eventTime, LevelType.Equipment.name(), kpisData.getInstId(), end, null);
                               processKpiDataToHistory(EventType.InternetUsage, kpisData.getCurrentColor().getInternetUsage(), eventTime, LevelType.Network.name(), kpisData.getInstId(), end, null);
                               processKpiDataToHistory(EventType.InternetUpload, kpisData.getCurrentColor().getInternetUpload(), eventTime, LevelType.Network.name(), kpisData.getInstId(), end, null);
                               processKpiDataToHistory(EventType.BootCount, kpisData.getCurrentColor().getBootCount(), eventTime, LevelType.Equipment.name(), kpisData.getInstId(), end,kpisData.getCurrentKpi().getBootCount() - kpisData.getLastKpi().getBootCount());//active should be false for all BootCount event
                           }
                       } else if (collection.equals(COLLECTION_DEVICE_EVENTS) || (collection.equals(COLLECTION_DEVICE_KPI) && !equipmentDeviceInstIds.contains(kpisData.getInstId()))){
                           processKpiDataToHistory(EventType.Rssi, kpisData.getCurrentColor().getRssi(), eventTime, LevelType.Device.name(), kpisData.getInstId(), end,null);
                           processKpiDataToHistory(EventType.Standard, kpisData.getCurrentColor().getStandard(), eventTime, LevelType.Device.name(), kpisData.getInstId(), end, null);
                           processKpiDataToHistory(EventType.DfsSupport, kpisData.getCurrentColor().getDfsSupport(), eventTime, LevelType.Device.name(), kpisData.getInstId(), end, null);
                       } else if (collection.equals(COLLECTION_RADIO_EVENTS) || (collection.equals(COLLECTION_RADIO_KPI) && !equipmentRadioInstIds.contains(kpisData.getInstId()))) {
                           processKpiDataToHistory(EventType.AirtimeUsage, kpisData.getCurrentColor().getAirtimeColor(), eventTime, LevelType.Equipment.name(), kpisData.getInstId(), end, kpisData.getRadioKey());
                       }
                   }
               }
            }
        }

        return size;
    }

    void checkOfflineEquipments(String userId) {
        List<ApDetailDto> apDetailDtos = apDetailDao.listByUserId(userId);
        long currentMillis = System.currentTimeMillis();
        long onlineBaseMillis = currentMillis - at3Adapter.getEquipmentOfflineMins() * 60000;
        apDetailDtos.forEach(p-> {
          if ((onlineBaseMillis > p.getTimestamp()) && alertHistoryResponses.stream().filter(a->StringUtils.equals(a.getInstId(), p.getSerialNumber())).count() == 0) {
              AlertHistory history = new AlertHistory();
              history.setActive(true);
              history.setStartTime(reportTimeMap.get(p.getSerialNumber()));
              history.setEndTime(System.currentTimeMillis());
              history.setReason(ReasonType.LastSeen.value);
              history.setInstId(p.getSerialNumber());
              history.setType(LevelType.Equipment.name());
              history.setSeverity(SeverityType.values()[(currentMillis-p.getTimestamp())/1000 > 2100?2 :1].name());
              alertHistoryResponses.add(history);
          }
        });
    }

    public List<AlertHistory> getAlerts(String stn, boolean active, String severity, long duration) throws Exception {
        long t0 = CommonUtils.getCurrentTimeInMillis();
        if (Objects.nonNull(severity) && (SeverityType.getName(severity) == null))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid severity.");
        if (duration < 0 || duration > 7*SECONDS_OF_DAY)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration.");

        reset();
        String userId = at3Adapter.getRgwSerialByStn(stn);
        fetchReportTime(userId);

        String projectEventQuery = "{$project: {_id:0, instId:'$%s', currentColor:'$%s', eventTime:{ $multiply: [ '$eventTime', NumberLong(1000)] }, currentKpi: '$currentKpi', lastKpi:'$lastKpi'}}";
        String projectKpiQuery = "{$project: {_id:0, instId:'$%s', currentColor:'$%s', currentKpi: '$currentKpi', lastKpi:'$lastKpi'}}";
        String sortQuery = "{$sort:{eventTime: 1}}";
        String groupQuery = "{$group: { _id:'$instId', KPIs: { $push: '$$ROOT' } }}";
        Map<String, String> opsMap = new HashMap<>();

        opsMap.put("project", String.format(projectEventQuery, "serialNumber", "currentColor"));
        opsMap.put("sort", sortQuery);
        opsMap.put("group", groupQuery);
        processEventData(userId, COLLECTION_EQUIPMENT_EVENTS, opsMap);

        opsMap.clear();
        opsMap.put("project", String.format(projectKpiQuery, "serialNumber", "lastColor"));
        opsMap.put("group", groupQuery);
        processEventData(userId, COLLECTION_EQUIPMENT_KPI, opsMap);

        String projectRadioEventQuery = "{$project: {_id:0, instId:'$%s', currentColor:'$%s', radioKey:'$radioKey', eventTime:{ $multiply: [ '$eventTime', NumberLong(1000)] }, currentKpi: '$currentKpi'}}";
        String groupRadioQuery = "{$group: { _id:{instId:'$instId', radioKey:'$radioKey'}, KPIs: { $push: '$$ROOT' } }}";
        opsMap.clear();
        opsMap.put("project", String.format(projectRadioEventQuery, "serialNumber", "currentColor"));
        opsMap.put("sort", sortQuery);
        opsMap.put("group", groupRadioQuery);
        processEventData(userId, COLLECTION_RADIO_EVENTS, opsMap);

        String projectRadioKpiQuery = "{$project: {_id:0, instId:'$%s', currentColor:'$%s', radioKey:'$radioKey', currentKpi: '$currentKpi'}}";
        opsMap.clear();
        opsMap.put("project", String.format(projectRadioKpiQuery, "serialNumber", "lastColor"));
        opsMap.put("group", groupRadioQuery);
        processEventData(userId, COLLECTION_RADIO_KPI, opsMap);

        opsMap.clear();
        opsMap.put("project", String.format(projectEventQuery, "stationMac", "currentColor"));
        opsMap.put("sort", sortQuery);
        opsMap.put("group", groupQuery);
        processEventData(userId, COLLECTION_DEVICE_EVENTS, opsMap);

        opsMap.clear();
        opsMap.put("project", String.format(projectKpiQuery, "stationMac", "lastColor"));
        opsMap.put("group", groupQuery);
        processEventData(userId, COLLECTION_DEVICE_KPI, opsMap);

        checkOfflineEquipments(userId);
        long startTime = t0 - duration*1000L;
        //logger.info("startTime:[{}]", startTime);
        alertHistoryResponses = alertHistoryResponses.stream().filter(p->p.getEndTime()>= startTime).collect(Collectors.toList());
        if (Objects.nonNull(severity)) {
            alertHistoryResponses = alertHistoryResponses.stream().filter(p->p.getSeverity().equals(SeverityType.getName(severity))).collect(Collectors.toList());
        }
        if (active == true)
            alertHistoryResponses = alertHistoryResponses.stream().filter(p->p.getActive() == true).collect(Collectors.toList());

        long t1 = CommonUtils.getCurrentTimeInMillis();
        logger.info("getAlerts takes [{}] ms", t1-t0);
        return alertHistoryResponses.stream().sorted(
                Comparator.comparing(AlertHistory::getType, Comparator.reverseOrder()).thenComparing(AlertHistory::getInstId).thenComparing(AlertHistory::getReason).thenComparing(AlertHistory::getStartTime))
                .collect(Collectors.toList());
    }
}
