package com.actiontec.optim.platform.api.v6.dto;

import java.util.List;

public class DeviceDTO {
    private String id;
    private String hostname;
    private String macAddress;
    private String status;
    private String type;
    private String upstreamId;
    private long lastChangeTime;
    private List<IPAddressDTO> ipAddresses;
    private List<ConnectionDTO> connections;
    private List<CapabilityDTO> capabilities;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUpstreamId() {
        return upstreamId;
    }

    public void setUpstreamId(String upstreamId) {
        this.upstreamId = upstreamId;
    }

    public long getLastChangeTime() {
        return lastChangeTime;
    }

    public void setLastChangeTime(long lastChangeTime) {
        this.lastChangeTime = lastChangeTime;
    }

    public List<IPAddressDTO> getIpAddresses() {
        return ipAddresses;
    }

    public void setIpAddresses(List<IPAddressDTO> ipAddresses) {
        this.ipAddresses = ipAddresses;
    }

    public List<ConnectionDTO> getConnections() {
        return connections;
    }

    public void setConnections(List<ConnectionDTO> connections) {
        this.connections = connections;
    }

    public List<CapabilityDTO> getCapabilities() {
        return capabilities;
    }

    public void setCapabilities(List<CapabilityDTO> capabilities) {
        this.capabilities = capabilities;
    }
}
