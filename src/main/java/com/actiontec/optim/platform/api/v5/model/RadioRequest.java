package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class RadioRequest {
    private boolean enabled;
    private boolean autoChannelEnabled;
    private int channel;
    private int channelBandwidth;
    private List<String> operatingStandards;
    private boolean dfsEnabled;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isAutoChannelEnabled() {
        return autoChannelEnabled;
    }

    public void setAutoChannelEnabled(boolean autoChannelEnabled) {
        this.autoChannelEnabled = autoChannelEnabled;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public int getChannelBandwidth() {
        return channelBandwidth;
    }

    public void setChannelBandwidth(int channelBandwidth) {
        this.channelBandwidth = channelBandwidth;
    }

    public List<String> getOperatingStandards() {
        return operatingStandards;
    }

    public void setOperatingStandards(List<String> operatingStandards) {
        this.operatingStandards = operatingStandards;
    }

    public boolean isDfsEnabled() {
        return dfsEnabled;
    }

    public void setDfsEnabled(boolean dfsEnabled) {
        this.dfsEnabled = dfsEnabled;
    }
}
