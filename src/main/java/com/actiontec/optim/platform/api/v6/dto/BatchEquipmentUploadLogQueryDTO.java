package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

@ApiModel(description = "Batch Equipment Upload Log query parameters")
public class BatchEquipmentUploadLogQueryDTO extends PaginationRequest {

    @ApiModelProperty(value = "Specified ISP ID")
    @Column(name = "isp_id")
    private String ispId;

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }
} 