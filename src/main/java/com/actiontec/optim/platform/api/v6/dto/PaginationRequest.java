package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.ObjectUtils;

@ApiModel(description = "Base Pagination parameters")
public class PaginationRequest {
    @ApiModelProperty(value = "Number of records to skip, default value: 0")
    private Integer offset = 0;

    @ApiModelProperty(value = "Max number of records, default value: 50")
    private Integer limit = 50;

    public Integer getOffset() {
        return ObjectUtils.defaultIfNull(offset, 0);
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getLimit() {
        return ObjectUtils.defaultIfNull(limit, 50);
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }
}