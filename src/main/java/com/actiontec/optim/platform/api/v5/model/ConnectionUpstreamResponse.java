package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class ConnectionUpstreamResponse {
    private String id;
    private String status;
    private String phyType;
    private long phyTransmitRate;
    private long phyReceiveRate;
    private long lastChangeTime;
    private long lastReportTime;
    private Ipv6Response ipv6;

    public static class Ipv6Response {
        private boolean enabled;
        private List<Ipv6AddressResponse> addresses;
        private String protocol;
        private String defaultGateway;
        private List<DnsServerResponse> dnsServers;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public List<Ipv6AddressResponse> getAddresses() {
            return addresses;
        }

        public void setAddresses(List<Ipv6AddressResponse> addresses) {
            this.addresses = addresses;
        }

        public String getProtocol() {
            return protocol;
        }

        public void setProtocol(String protocol) {
            this.protocol = protocol;
        }

        public String getDefaultGateway() {
            return defaultGateway;
        }

        public void setDefaultGateway(String defaultGateway) {
            this.defaultGateway = defaultGateway;
        }

        public List<DnsServerResponse> getDnsServers() {
            return dnsServers;
        }

        public void setDnsServers(List<DnsServerResponse> dnsServers) {
            this.dnsServers = dnsServers;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPhyType() {
        return phyType;
    }

    public void setPhyType(String phyType) {
        this.phyType = phyType;
    }

    public long getPhyTransmitRate() {
        return phyTransmitRate;
    }

    public void setPhyTransmitRate(long phyTransmitRate) {
        this.phyTransmitRate = phyTransmitRate;
    }

    public long getPhyReceiveRate() {
        return phyReceiveRate;
    }

    public void setPhyReceiveRate(long phyReceiveRate) {
        this.phyReceiveRate = phyReceiveRate;
    }

    public long getLastChangeTime() {
        return lastChangeTime;
    }

    public void setLastChangeTime(long lastChangeTime) {
        this.lastChangeTime = lastChangeTime;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public Ipv6Response getIpv6() {
        return ipv6;
    }

    public void setIpv6(Ipv6Response ipv6) {
        this.ipv6 = ipv6;
    }
}
