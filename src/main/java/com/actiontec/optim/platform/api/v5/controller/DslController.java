package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.DslResponseMapper;
import com.actiontec.optim.platform.api.v5.model.DslResponse;
import com.actiontec.optim.platform.model.NetworkDsl;
import com.actiontec.optim.platform.service.NetworkDslService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@Api(value = "(V5) DSL Statistics", description = "API's for DSL Statistics",tags = { "Optim (V5) - DSL Statistics" })
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}/dsls")
public class DslController {

    @Autowired
    private NetworkDslService networkDslService;

    @Autowired
    private DslResponseMapper dslResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<DslResponse> getAllDsl(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkDsl networkDsl = networkDslService.findByEquipmentId(equipmentId);

        List<NetworkDsl> networkDslList = new ArrayList<>();
        if(networkDsl != null) {
            networkDslList.add(networkDsl);
        }
        List<DslResponse> dslResponse = dslResponseMapper.toDslResponse(networkDslList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "DSL Resource", null, "200", dslResponse, httpServletRequest);
        return dslResponse;
    }

    @RequestMapping(value = "/{dslId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<DslResponse> getDsl(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "dslId", required = true) @PathVariable(name = "dslId") String dslId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkDsl networkDsl = networkDslService.findByEquipmentId(equipmentId);

        List<NetworkDsl> networkDslList = new ArrayList<>();
        if(networkDsl != null) {
            networkDslList.add(networkDsl);
        }
        List<DslResponse> dslResponse = dslResponseMapper.toDslResponse(networkDslList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "DSL Resource", null, "200", dslResponse, httpServletRequest);
        return dslResponse;
    }
}
