package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.IspConfigurationRequest;
import com.actiontec.optim.platform.api.v5.model.IspConfigurationResponse;
import com.actiontec.optim.platform.model.IspConfiguration;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface IspConfigurationMapper {

//    public List<IspConfigurationRequest> toIspConfigurationRequest(List<IspConfiguration> ispConfigurationList);
//    public List<IspConfiguration> toIspConfiguration(List<IspConfigurationRequest> ispConfigurationRequestList);

//    public IspConfigurationRequest toIspConfigurationRequest(IspConfiguration ispConfigurationList);
    public IspConfiguration toIspConfiguration(IspConfigurationRequest ispConfigurationRequestList);

    public List<IspConfigurationResponse> toIspConfigurationResponse(List<IspConfiguration> ispConfigurationList);

}
