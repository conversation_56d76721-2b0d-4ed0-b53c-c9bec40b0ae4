package com.actiontec.optim.platform.api.v5.model;

import com.actiontec.optim.platform.model.MarkedAttributes;

import java.util.List;

public class DeviceResponse {
    private String id;
    private String macAddress;
    private String name = "";
    private String hostName = "";
    private String status;
    private String deviceType;
    private String phyType;
    private String vendor = "";
    private long downlinkPhyRate;
    private long uplinkPhyRate;
    private long lastReportTime;
    private List<DeviceAddressResponse> addresses;
    private DeviceWirelessResponse wireless;
    private DeviceInternetAccessResponse internetAccess;
    private DeviceSpeedTestResponse speedTest;
    private DeviceStatResponse stats;
    private List<MarkedAttributes> markedAttributes;

    public static class DeviceAddressResponse {
        private String version;
        private String type;
        private String address;

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getAddress() {
            return address;
        }

        public void setAddress(String address) {
            this.address = address;
        }
    }

    public static class DeviceWirelessResponse {
        private String macAddress;
        private String upstream;
        private String operatingStandards;
        private int channel;
        private long rssi;
        private double airTimeUsage;
        private boolean dualBandSupported;
        private boolean dfsSupported;
        private boolean ieee80211kSupported;
        private boolean ieee80211vSupported;
        private boolean ieee80211rSupported;
        private String ssid;
        private String radioKey;
        private String ssidKey;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getUpstream() {
            return upstream;
        }

        public void setUpstream(String upstream) {
            this.upstream = upstream;
        }

        public String getOperatingStandards() {
            return operatingStandards;
        }

        public void setOperatingStandards(String operatingStandards) {
            this.operatingStandards = operatingStandards;
        }

        public int getChannel() {
            return channel;
        }

        public void setChannel(int channel) {
            this.channel = channel;
        }

        public long getRssi() {
            return rssi;
        }

        public void setRssi(long rssi) {
            this.rssi = rssi;
        }

        public double getAirTimeUsage() {
            return airTimeUsage;
        }

        public void setAirTimeUsage(double airTimeUsage) {
            this.airTimeUsage = airTimeUsage;
        }

        public boolean isDualBandSupported() {
            return dualBandSupported;
        }

        public void setDualBandSupported(boolean dualBandSupported) {
            this.dualBandSupported = dualBandSupported;
        }

        public boolean isDfsSupported() {
            return dfsSupported;
        }

        public void setDfsSupported(boolean dfsSupported) {
            this.dfsSupported = dfsSupported;
        }

        public boolean isIeee80211kSupported() {
            return ieee80211kSupported;
        }

        public void setIeee80211kSupported(boolean ieee80211kSupported) {
            this.ieee80211kSupported = ieee80211kSupported;
        }

        public boolean isIeee80211vSupported() {
            return ieee80211vSupported;
        }

        public void setIeee80211vSupported(boolean ieee80211vSupported) {
            this.ieee80211vSupported = ieee80211vSupported;
        }

        public boolean isIeee80211rSupported() {
            return ieee80211rSupported;
        }

        public void setIeee80211rSupported(boolean ieee80211rSupported) {
            this.ieee80211rSupported = ieee80211rSupported;
        }

        public String getSsid() {
            return ssid;
        }

        public void setSsid(String ssid) {
            this.ssid = ssid;
        }

        public String getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(String radioKey) {
            this.radioKey = radioKey;
        }

        public String getSsidKey() {
            return ssidKey;
        }

        public void setSsidKey(String ssidKey) {
            this.ssidKey = ssidKey;
        }
    }

    public static class DeviceInternetAccessResponse {
        private boolean blocked;
        private long startTime;
        private long duration;

        public boolean isBlocked() {
            return blocked;
        }

        public void setBlocked(boolean blocked) {
            this.blocked = blocked;
        }

        public long getStartTime() {
            return startTime;
        }

        public void setStartTime(long startTime) {
            this.startTime = startTime;
        }

        public long getDuration() {
            return duration;
        }

        public void setDuration(long duration) {
            this.duration = duration;
        }
    }

    public static class DeviceSpeedTestResponse {
        private double rate;
        private long lastTestTime;

        public double getRate() {
            return rate;
        }

        public void setRate(double rate) {
            this.rate = rate;
        }

        public long getLastTestTime() {
            return lastTestTime;
        }

        public void setLastTestTime(long lastTestTime) {
            this.lastTestTime = lastTestTime;
        }
    }

    public static class DeviceStatResponse {
        private long bytesSent;
        private long bytesReceived;
        private double avgBytesSent;
        private double avgBytesReceived;
        private long errorsSent;
        private long retransSent;

        public long getBytesSent() {
            return bytesSent;
        }

        public void setBytesSent(long bytesSent) {
            this.bytesSent = bytesSent;
        }

        public long getBytesReceived() {
            return bytesReceived;
        }

        public void setBytesReceived(long bytesReceived) {
            this.bytesReceived = bytesReceived;
        }

        public double getAvgBytesSent() {
            return avgBytesSent;
        }

        public void setAvgBytesSent(double avgBytesSent) {
            this.avgBytesSent = avgBytesSent;
        }

        public double getAvgBytesReceived() {
            return avgBytesReceived;
        }

        public void setAvgBytesReceived(double avgBytesReceived) {
            this.avgBytesReceived = avgBytesReceived;
        }

        public long getErrorsSent() {
            return errorsSent;
        }

        public void setErrorsSent(long errorsSent) {
            this.errorsSent = errorsSent;
        }

        public long getRetransSent() {
            return retransSent;
        }

        public void setRetransSent(long retransSent) {
            this.retransSent = retransSent;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getPhyType() {
        return phyType;
    }

    public void setPhyType(String phyType) {
        this.phyType = phyType;
    }

    public String getVendor() {
        return vendor;
    }

    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    public long getDownlinkPhyRate() {
        return downlinkPhyRate;
    }

    public void setDownlinkPhyRate(long downlinkPhyRate) {
        this.downlinkPhyRate = downlinkPhyRate;
    }

    public long getUplinkPhyRate() {
        return uplinkPhyRate;
    }

    public void setUplinkPhyRate(long uplinkPhyRate) {
        this.uplinkPhyRate = uplinkPhyRate;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public List<DeviceAddressResponse> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<DeviceAddressResponse> addresses) {
        this.addresses = addresses;
    }

    public DeviceWirelessResponse getWireless() {
        return wireless;
    }

    public void setWireless(DeviceWirelessResponse wireless) {
        this.wireless = wireless;
    }

    public DeviceInternetAccessResponse getInternetAccess() {
        return internetAccess;
    }

    public void setInternetAccess(DeviceInternetAccessResponse internetAccess) {
        this.internetAccess = internetAccess;
    }

    public DeviceSpeedTestResponse getSpeedTest() {
        return speedTest;
    }

    public void setSpeedTest(DeviceSpeedTestResponse speedTest) {
        this.speedTest = speedTest;
    }

    public DeviceStatResponse getStats() {
        return stats;
    }

    public void setStats(DeviceStatResponse stats) {
        this.stats = stats;
    }

    public List<MarkedAttributes> getMarkedAttributes() {
        return markedAttributes;
    }

    public void setMarkedAttributes(List<MarkedAttributes> markedAttributes) {
        this.markedAttributes = markedAttributes;
    }
}
