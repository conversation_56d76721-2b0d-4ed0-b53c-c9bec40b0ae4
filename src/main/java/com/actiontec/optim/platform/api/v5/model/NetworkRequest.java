package com.actiontec.optim.platform.api.v5.model;

public class NetworkRequest {
    String serviceTelephoneNumber;
    SubscriptionDto subscription;
    String name;
    String subscriberId;

    public String getServiceTelephoneNumber() {
        return serviceTelephoneNumber;
    }

    public void setServiceTelephoneNumber(String serviceTelephoneNumber) {
        this.serviceTelephoneNumber = serviceTelephoneNumber;
    }

    public SubscriptionDto getSubscription() {
        return subscription;
    }

    public void setSubscription(SubscriptionDto subscription) {
        this.subscription = subscription;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public static class SubscriptionDto {
        double downlinkRate;
        double uplinkRate;

        public double getDownlinkRate() {
            return downlinkRate;
        }

        public void setDownlinkRate(double downlinkRate) {
            this.downlinkRate = downlinkRate;
        }

        public double getUplinkRate() {
            return uplinkRate;
        }

        public void setUplinkRate(double uplinkRate) {
            this.uplinkRate = uplinkRate;
        }
    }
}
