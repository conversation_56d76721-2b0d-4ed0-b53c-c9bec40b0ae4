package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.mapper.MocaResponseMapper;
import com.actiontec.optim.platform.api.v5.model.MocaResponse;
import com.actiontec.optim.platform.model.NetworkMoca;
import com.actiontec.optim.platform.service.NetworkMocaService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}/mocaPorts")
public class MocaController {

    @Autowired
    private NetworkMocaService networkMocaService;

    @Autowired
    private MocaResponseMapper mocaResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<MocaResponse> getAll(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkMoca> networkMocas = networkMocaService.findByStnAndSerial(stn, equipmentId);
        List<MocaResponse> mocaResponses = mocaResponseMapper.toMocaResponse(networkMocas);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "MoCA Resource", null, "200", mocaResponses, httpServletRequest);
        return mocaResponses;
    }

    @RequestMapping(value = "/{mocaId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public MocaResponse get(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "mocaId", required = true) @PathVariable(name = "mocaId")
                    String mocaId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        int portNo = 0;
        try {
            portNo = Integer.parseInt(mocaId);
        } catch (Exception e){
            throw new OptimApiException(HttpStatus.NOT_FOUND, "");
        }

        Optional<NetworkMoca> mocaOptional = networkMocaService.findByStnAndSerialAndPortNo(stn, equipmentId, portNo);
        if (!mocaOptional.isPresent()) {
            throw new OptimApiException(HttpStatus.NOT_FOUND, "");
        }
        MocaResponse mocaResponse = mocaResponseMapper.toMocaResponse(mocaOptional.get());
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "MoCA Resource", null, "200", mocaResponse, httpServletRequest);
        return mocaResponse;
    }
}
