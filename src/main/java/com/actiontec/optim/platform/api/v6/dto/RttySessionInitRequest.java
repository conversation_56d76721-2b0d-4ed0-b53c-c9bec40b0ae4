package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Remote Debug Session Request")
public class RttySessionInitRequest {

    @ApiModelProperty(value = "Equipment serial number", required = true)
    private String serialNumber;

    @ApiModelProperty(value = "Session duration in hours", required = false, example = "24")
    private Integer durationHours = 24;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getDurationHours() {
        return durationHours;
    }

    public void setDurationHours(Integer durationHours) {
        this.durationHours = durationHours;
    }
}
