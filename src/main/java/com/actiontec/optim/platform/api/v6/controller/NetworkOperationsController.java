package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.service.NCSNetworkService;
import com.actiontec.optim.platform.service.TopologyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;

@RestController
@Api(value = "(V6) Network", description = "API's for Network Statistics", tags = {"Optim - (V6) Network"})
@RequestMapping(value = "/actiontec/api/v6/networks")
public class NetworkOperationsController {

    @Autowired
    private TopologyService topologyService;

    @Autowired
    private NCSNetworkService ncsNetworkService;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<NetworkOperationsDTO> getNetworkByKeyword(@ModelAttribute NetworkOperationsQueryDTO queryDTO) throws Exception {
        return ncsNetworkService.getNetworksByKeyword(queryDTO);
    }

    @RequestMapping(value = "/{networkId}/topology/history", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<SnapshotDataDTO> getTopologyHistory(
            @ApiParam(value = "Network Id", required = true) @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "startTime") @RequestParam(required = false, name = "startTime") Long startTime,
            @ApiParam(value = "endTime") @RequestParam(required = false, name = "endTime") Long endTime,
            @ApiParam(value = "deviceId") @RequestParam(required = false, name = "deviceId") String deviceId) throws Exception {

        if (startTime == null) {
            startTime = Instant.now().minus(1, ChronoUnit.DAYS).toEpochMilli();
        }

        if (endTime == null) {
            endTime = Instant.now().toEpochMilli();
        }

        return topologyService.getHistory(networkId, startTime, endTime, deviceId);
    }

    @RequestMapping(value = "/{networkId}/proxy", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public CpeApiResponse getCpeApiProxy(
            @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "Proxy request details", required = true) @RequestBody CpeApiRequest proxyRequest) throws Exception {
        return ncsNetworkService.getCpeApiProxy(networkId, proxyRequest);
    }
}
