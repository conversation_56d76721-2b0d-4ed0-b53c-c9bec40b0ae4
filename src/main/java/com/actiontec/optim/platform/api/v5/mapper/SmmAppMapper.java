package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.SmmAppRequest;
import com.actiontec.optim.platform.api.v5.model.SmmAppResponse;
import com.actiontec.optim.platform.model.SmmApplication;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.Arrays;

@Mapper(componentModel = "spring")
public interface SmmAppMapper {
    public SmmApplication toSmmApplication(SmmAppRequest smmAppRequest);
    public SmmAppResponse toSmmAppResponse(SmmApplication smmApplication);
}
