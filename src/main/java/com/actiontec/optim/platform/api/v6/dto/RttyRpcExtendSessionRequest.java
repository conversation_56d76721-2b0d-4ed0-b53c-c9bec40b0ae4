package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Response DTO for RTTY RPC operations
 */
@ApiModel(description = "RTTY RPC Request")
public class RttyRpcExtendSessionRequest {
    @ApiModelProperty(value = "Additional duration in seconds")
    private Long additionalDuration;

    public Long getAdditionalDuration() {
        return additionalDuration;
    }

    public void setAdditionalDuration(Long additionalDuration) {
        this.additionalDuration = additionalDuration;
    }
}
