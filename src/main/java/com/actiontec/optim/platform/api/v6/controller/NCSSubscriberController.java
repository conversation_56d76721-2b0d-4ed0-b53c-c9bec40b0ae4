package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.NCSSubscriberQueryDTO;
import com.actiontec.optim.platform.service.NCSSubscriberService;
import com.incs83.app.business.v2.ManageCommonService;
import com.actiontec.optim.platform.api.v6.dto.NCSSubscriberDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@Api(value = "(V6) Subscribers", description = "API's for Subscribers Operations", tags = {"Optim - (V6) Subscribers"})
@RequestMapping(value = "/actiontec/api/v6/iam/subscribers")
public class NCSSubscriberController {
    @Autowired
    NCSSubscriberService ncsSubscriberService;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<NCSSubscriberDTO> getAllSubscribers(
            @ModelAttribute NCSSubscriberQueryDTO queryDTO) throws Exception  {
        return ncsSubscriberService.getAllSubscribers(queryDTO);
    }

    @RequestMapping(value = "/{subscriberId}",method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public NCSSubscriberDTO getSubscriber(@ApiParam(value = "subscriberId") @PathVariable String subscriberId) throws Exception {
        return ncsSubscriberService.getSubscriber(subscriberId);
    }

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, String> createSubscriber(@ApiParam(value = "subscriber data")
                                                @RequestBody NCSSubscriberDTO subscriberData) throws Exception {
        return ncsSubscriberService.createSubscriber(subscriberData);
    }

    @RequestMapping(value = "/{subscriberId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateSubscriber(@ApiParam(value = "subscriber") @PathVariable String subscriberId,
                               @RequestBody NCSSubscriberDTO subscriberData) throws Exception {
        subscriberData.setId(subscriberId);
        ncsSubscriberService.updateSubscriber(subscriberData);
    }

    @RequestMapping(value = "/{subscriberId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public void deleteSubscriber(@ApiParam(value = "subscriber") @PathVariable String subscriberId) throws Exception {
        ncsSubscriberService.deleteSubscriber(subscriberId);
    }
}
