package com.actiontec.optim.platform.api.v6.model;

public class ConfigurationRequest {
    private String modelId;
    private Isp isp;
    private Acs acs;
    private Wireless wireless;
    private Network network;
    private Access access;

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public Isp getIsp() {
        return isp;
    }

    public void setIsp(Isp isp) {
        this.isp = isp;
    }

    public Acs getAcs() {
        return acs;
    }

    public void setAcs(Acs acs) {
        this.acs = acs;
    }

    public Wireless getWireless() {
        return wireless;
    }

    public void setWireless(Wireless wireless) {
        this.wireless = wireless;
    }

    public Network getNetwork() {
        return network;
    }

    public void setNetwork(Network network) {
        this.network = network;
    }

    public Access getAccess() {
        return access;
    }

    public void setAccess(Access access) {
        this.access = access;
    }
}

