package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.DeviceResponse;
import com.actiontec.optim.platform.model.UserDevice;
import com.actiontec.optim.platform.model.UserDeviceAddress;
import com.actiontec.optim.platform.model.UserDeviceEthernet;
import com.actiontec.optim.platform.model.UserDeviceWireless;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class DeviceResponseMapper {
    @Mapping(target = "name", source = "friendlyName")
    @Mapping(target = "phyType", source = "phyType.formalName")
    @Mapping(target = "wireless", source = "userDeviceWireless")
    public abstract DeviceResponse toDeviceResponse(UserDevice userDevice);

    public abstract List<DeviceResponse> toDeviceResponse(List<UserDevice> userDevices);

    public abstract DeviceResponse.DeviceAddressResponse toDeviceAddressResponse(UserDeviceAddress userDeviceAddress);

    public abstract List<DeviceResponse.DeviceAddressResponse> toDeviceAddressResponse(List<UserDeviceAddress> userDeviceAddresses);

    @Mapping(target = "upstream", source = "serialNumber")
    @Mapping(target = "airTimeUsage", source = "airTimePercentage")
    public abstract DeviceResponse.DeviceWirelessResponse toDeviceWirelessResponse(UserDeviceWireless userDeviceWireless);

    //public abstract DeviceResponse.DeviceSpeedTestResponse toDeviceSpeedTestResponse();

    @AfterMapping
    protected void afterMapping(UserDevice userDevice, @MappingTarget DeviceResponse deviceResponse) {
        deviceResponse.setId(userDevice.getMacAddress());

        // XXX: hack for extender mac
//        if (UserDeviceType.Extender == userDevice.getDeviceType()) {
//            deviceResponse.setId(userDevice.getWanMacAddress());
//            deviceResponse.setMacAddress(userDevice.getWanMacAddress());
//        }

        DeviceResponse.DeviceInternetAccessResponse internetAccessResponse = new DeviceResponse.DeviceInternetAccessResponse();
        internetAccessResponse.setBlocked(userDevice.isInternetAccessBlocked());
        internetAccessResponse.setStartTime(userDevice.getInternetAccessBlockStartTime());
        internetAccessResponse.setDuration(userDevice.getInternetAccessBlockDuration());
        deviceResponse.setInternetAccess(internetAccessResponse);

        if (userDevice.getUserDeviceWireless() != null) {
            UserDeviceWireless userDeviceWireless = userDevice.getUserDeviceWireless();
            deviceResponse.setDownlinkPhyRate(userDeviceWireless.getLastDataUplinkRate());
            deviceResponse.setUplinkPhyRate(userDeviceWireless.getLastDataDownlinkRate());

            DeviceResponse.DeviceStatResponse deviceStatResponse = new DeviceResponse.DeviceStatResponse();
            deviceStatResponse.setBytesSent(userDeviceWireless.getBytesSent());
            deviceStatResponse.setBytesReceived(userDeviceWireless.getBytesReceived());
            deviceStatResponse.setAvgBytesSent(userDeviceWireless.getAvgBytesSent());
            deviceStatResponse.setAvgBytesReceived(userDeviceWireless.getAvgBytesReceived());
            deviceStatResponse.setErrorsSent(userDeviceWireless.getErrorsSent());
            deviceStatResponse.setRetransSent(userDeviceWireless.getRetransSent());
            deviceResponse.setStats(deviceStatResponse);
        } else {
            if (userDevice.getUserDeviceEthernet() != null) {
                UserDeviceEthernet userDeviceEthernet = userDevice.getUserDeviceEthernet();

                DeviceResponse.DeviceStatResponse deviceStatResponse = new DeviceResponse.DeviceStatResponse();
                deviceStatResponse.setBytesSent(userDeviceEthernet.getBytesSent());
                deviceStatResponse.setBytesReceived(userDeviceEthernet.getBytesReceived());
                deviceResponse.setStats(deviceStatResponse);
            }
        }

        if (userDevice.getStationSpeedTest() != null) {
            DeviceResponse.DeviceSpeedTestResponse speedTestResponse = new DeviceResponse.DeviceSpeedTestResponse();
            speedTestResponse.setRate(userDevice.getStationSpeedTest().getDataRate());
            speedTestResponse.setLastTestTime(userDevice.getStationSpeedTest().getDate().getTime());
            deviceResponse.setSpeedTest(speedTestResponse);
        }

        if (deviceResponse.getName() == null) {
            deviceResponse.setName("");
        }
    }
}
