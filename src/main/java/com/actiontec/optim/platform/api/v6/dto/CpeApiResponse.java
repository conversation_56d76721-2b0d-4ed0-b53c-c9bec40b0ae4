package com.actiontec.optim.platform.api.v6.dto;

import java.time.Instant;
import java.util.List;

public class CpeApiResponse {
    private long requestTime;
    private List<ResponseEndpoint> endpoints;

    // Getter 和 Setter
    public long getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(long requestTime) {
        this.requestTime = requestTime;
    }

    public List<ResponseEndpoint> getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(List<ResponseEndpoint> endpoints) {
        this.endpoints = endpoints;
    }

    public static class ResponseEndpoint extends CpeApiEndpoint {
        private List<Object> response;

        public List<Object> getResponse() {
            return response;
        }

        public void setResponse(List<Object> response) {
            this.response = response;
        }
    }
}
