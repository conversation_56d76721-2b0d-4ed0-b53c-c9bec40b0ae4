package com.actiontec.optim.platform.api.v5.controller;


import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.SmartSteeringConfig;
import com.actiontec.optim.platform.model.SteeringLog;
import com.actiontec.optim.platform.service.SmartSteeringConfigService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/smartSteering")
public class SmartSteeringController {

    @Autowired
    private SmartSteeringConfigService smartSteeringConfigService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public SmartSteeringConfig get(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        SmartSteeringConfig response = smartSteeringConfigService.getSteering(stn);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", null, "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.CREATED)
    public void put(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Change SmartSteering", required = true) @RequestBody SmartSteeringConfig request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        smartSteeringConfigService.putSteering(stn, request);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", request, "201", null, httpServletRequest);
    }

    @RequestMapping(value = "/logs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<SteeringLog> getLogs(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Steering Log type.")
            @RequestParam(name = "type", required = true) String type,
            @ApiParam(value = "macAddress", required = false)
            @RequestParam(required = false, name = "macAddress") String macAddress,
            @RequestParam(required = false, name = "duration", defaultValue = "180") int duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SteeringLog> steeringLogs = smartSteeringConfigService.getSteeringLogs(stn, type, macAddress, duration);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Steering Logs", null, "200", steeringLogs, httpServletRequest);
        return steeringLogs;
    }
}