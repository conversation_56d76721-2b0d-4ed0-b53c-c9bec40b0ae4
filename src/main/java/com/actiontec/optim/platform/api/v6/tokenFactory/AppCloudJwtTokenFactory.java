package com.actiontec.optim.platform.api.v6.tokenFactory;

import com.actiontec.optim.platform.api.v6.dto.AppCloudTokenDTO;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.AppCloudConfig;
import com.incs83.config.JwtConfig;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Date;

@Component
public class AppCloudJwtTokenFactory {

    private final JwtConfig jwtConfig;

    public AppCloudJwtTokenFactory(JwtConfig jwtConfig) {
        this.jwtConfig = jwtConfig;
    }

    public AppCloudTokenDTO createAppCloudToken(AppCloudConfig config) {
        if (CustomStringUtils.isEmpty(config.getId())) {
            throw new IllegalArgumentException("AppCloudConfig ID must not be empty");
        }

        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);

        Date issuedAt = Date.from(now.toInstant());
        Date expiration = Date.from(now.plusYears(1).toInstant());


        Claims claims = Jwts.claims().setSubject(config.getId());
        claims.put("name", config.getName());
        claims.put("groupId", config.getGroup().getId());
        claims.put("roleId", config.getRole().getId());
        claims.put("kid", config.getKid());

        String token = Jwts.builder()
                .setClaims(claims)
                .setIssuer(ActiontecConstants.ACTIONTEC)
                .setIssuedAt(issuedAt)
                .setExpiration(expiration)
                .signWith(SignatureAlgorithm.HS512, jwtConfig.getTokenSigningKey())
                .compact();
        return new AppCloudTokenDTO(token);
    }
}
