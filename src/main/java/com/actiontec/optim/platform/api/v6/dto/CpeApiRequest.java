package com.actiontec.optim.platform.api.v6.dto;

import java.util.List;

public class CpeApiRequest {
    private List<RequestEndpoint> endpoints;

    public List<RequestEndpoint> getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(List<RequestEndpoint> endpoints) {
        this.endpoints = endpoints;
    }

    public static class RequestEndpoint extends CpeApiEndpoint {
        private Object requestBody;

        public Object getRequestBody() {
            return requestBody;
        }

        public void setRequestBody(Object payload) {
            this.requestBody = payload;
        }
    }
}
