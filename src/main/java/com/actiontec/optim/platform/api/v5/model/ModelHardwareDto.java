package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

public class ModelHardwareDto {

    private long memorySize;
    private long flashSpaceSize;
    private int cpuSpeed;
    private int cpuCores;
    private long smmMemorySize;
    private long smmFlashSpaceSize;
    private int smmCpuMhz;
    private ArrayList<String> availableRadioKeys;
    private ArrayList<String> availableWanTypes;
    private ArrayList<String> availableLanTypes;

    public long getMemorySize() {
        return memorySize;
    }

    public void setMemorySize(long memorySize) {
        this.memorySize = memorySize;
    }

    public long getFlashSpaceSize() {
        return flashSpaceSize;
    }

    public void setFlashSpaceSize(long flashSpaceSize) {
        this.flashSpaceSize = flashSpaceSize;
    }

    public int getCpuSpeed() {
        return cpuSpeed;
    }

    public void setCpuSpeed(int cpuSpeed) {
        this.cpuSpeed = cpuSpeed;
    }

    public int getCpuCores() {
        return cpuCores;
    }

    public void setCpuCores(int cpuCores) {
        this.cpuCores = cpuCores;
    }

    public long getSmmMemorySize() {
        return smmMemorySize;
    }

    public void setSmmMemorySize(long smmMemorySize) {
        this.smmMemorySize = smmMemorySize;
    }

    public long getSmmFlashSpaceSize() {
        return smmFlashSpaceSize;
    }

    public void setSmmFlashSpaceSize(long smmFlashSpaceSize) {
        this.smmFlashSpaceSize = smmFlashSpaceSize;
    }

    public int getSmmCpuMhz() {
        return smmCpuMhz;
    }

    public void setSmmCpuMhz(int smmCpuMhz) {
        this.smmCpuMhz = smmCpuMhz;
    }

    public ArrayList<String> getAvailableRadioKeys() {
        return availableRadioKeys;
    }

    public void setAvailableRadioKeys(ArrayList<String> availableRadioKeys) {
        this.availableRadioKeys = availableRadioKeys;
    }

    public ArrayList<String> getAvailableWanTypes() {
        return availableWanTypes;
    }

    public void setAvailableWanTypes(ArrayList<String> availableWanTypes) {
        this.availableWanTypes = availableWanTypes;
    }

    public ArrayList<String> getAvailableLanTypes() {
        return availableLanTypes;
    }

    public void setAvailableLanTypes(ArrayList<String> availableLanTypes) {
        this.availableLanTypes = availableLanTypes;
    }
}
