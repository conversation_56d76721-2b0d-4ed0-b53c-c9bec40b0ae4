package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.TopologyResponseMapper;
import com.actiontec.optim.platform.api.v5.model.TopologyResponse;
import com.actiontec.optim.platform.service.NetworkTopologyService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/topology")
public class TopologyController {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private NetworkTopologyService networkTopologyService;

    @Autowired
    private TopologyResponseMapper topologyResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public TopologyResponse getTopology(
            @ApiParam(value = "STN", required = true)
            @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        logger.info("getTopologyByStn [{}]", stn);

        TopologyResponse topologyResponse = topologyResponseMapper.toTopologyResponse(networkTopologyService.buildTopologyByNetwork(stn));
        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Equipment Resource", null, "200", topologyResponse, httpServletRequest);
        return topologyResponse;
    }
}
