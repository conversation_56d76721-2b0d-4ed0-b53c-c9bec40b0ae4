package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

@ApiModel(description = "Network query parameters")
public class NCSNetworkQueryDTO extends PaginationRequest {
    @ApiModelProperty(value = "Specified ISP ID")
    @Column(name = "isp_id")
    private String ispId;

    @ApiModelProperty(value = "Specified subscriber  ID")
    @Column(name = "subscriber_id")
    private String subscriberId;

    @ApiModelProperty(value = "Filter of network name, support fuzzy search")
    private String name;

    @ApiModelProperty(value = "Specified ISP ID")
    @Column(name = "gen_type")
    private Boolean isAuto;

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getIsAuto() {
        return isAuto;
    }

    public void setIsAuto(Boolean auto) {
        isAuto = auto;
    }
}
