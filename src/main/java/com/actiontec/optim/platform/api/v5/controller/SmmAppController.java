package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.mapper.SmmAppMapper;
import com.actiontec.optim.platform.api.v5.model.SmmAppRequest;
import com.actiontec.optim.platform.api.v5.model.SmmAppResponse;
import com.actiontec.optim.platform.model.SmmApplication;
import com.actiontec.optim.platform.service.SmmAppService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Smm;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/smm/applications")
public class SmmAppController {

    @Autowired
    private SmmAppService smmAppService;

    @Autowired
    private SmmAppMapper smmAppMapper;

    @Autowired
    private AuditService auditService;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmAppResponse> getAllApp(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SmmApplication> smmApplicationList = smmAppService.getAllApps();

        List<SmmAppResponse> smmAppResponseList = new ArrayList<>();
        for(SmmApplication smmApplication : smmApplicationList) {
            smmAppResponseList.add(smmAppMapper.toSmmAppResponse(smmApplication));
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Resource", null, "200", smmAppResponseList, httpServletRequest);
        return smmAppResponseList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    public Map<String, String> createApp(
            @ApiParam(value = "App properties", required = true) @RequestBody SmmAppRequest smmAppRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Map<String, String> response = new HashMap<>();

        SmmApplication smmApplication = smmAppService.createApp(smmAppMapper.toSmmApplication(smmAppRequest));

        response.put("id", smmApplication.getId());
        response.put("fileEntry", smmApplication.getFileEntry());
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Resource", smmAppRequest, "201", response, httpServletRequest);
        return response;
    }

    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    @RequestMapping(value = "/{applicationId}", method = RequestMethod.GET, produces = "application/json")
    public List<SmmAppResponse> getApp(
            @ApiParam(value = "applicationId", required = true) @PathVariable(name = "applicationId") String applicationId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SmmAppResponse> smmAppResponseList = new ArrayList<>();

        SmmApplication smmApplication = smmAppService.findAppById(applicationId);
        smmAppResponseList.add(smmAppMapper.toSmmAppResponse(smmApplication));
        auditService.createAuditLog(null, null, "SMM Resource", null, "200", smmAppResponseList, httpServletRequest);
        return smmAppResponseList;
    }

    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Smm.class)
    @RequestMapping(value = "/{applicationId}", method = RequestMethod.PUT, produces = "application/json")
    public void updateApp(
            @ApiParam(value = "applicationId", required = true) @PathVariable(name = "applicationId") String applicationId,
            @ApiParam(value = "App properties", required = true) @RequestBody SmmAppRequest smmAppRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        SmmApplication smmApplication = smmAppMapper.toSmmApplication(smmAppRequest);
        smmAppService.updateAppById(applicationId, smmApplication);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Resource", smmAppRequest, "200", null, httpServletRequest);
    }

    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Smm.class)
    @RequestMapping(value = "/{applicationId}", method = RequestMethod.DELETE, produces = "application/json")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteApp(
            @ApiParam(value = "applicationId", required = true) @PathVariable(name = "applicationId") String applicationId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        smmAppService.deleteAppById(applicationId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Resource", null, "204", null, httpServletRequest);
    }
}
