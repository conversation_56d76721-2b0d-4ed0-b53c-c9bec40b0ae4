package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.mapper.IspConfigurationMapper;
import com.actiontec.optim.platform.api.v5.model.IspConfigurationRequest;
import com.actiontec.optim.platform.api.v5.model.IspConfigurationResponse;
import com.actiontec.optim.platform.model.IspConfiguration;
import com.actiontec.optim.platform.service.IspConfigurationService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/initialConfigurations")
public class IspConfigurationController {

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private IspConfigurationService ispConfigurationService;

    @Autowired
    private IspConfigurationMapper ispConfigurationMapper;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<IspConfigurationResponse> getIspConfigurations(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<IspConfiguration> ispConfigurationList = ispConfigurationService.getConfigurations();
        List<IspConfigurationResponse> ispConfigurationResponses = ispConfigurationMapper.toIspConfigurationResponse(ispConfigurationList);

        return ispConfigurationResponses;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> addIspConfiguration(
            @ApiParam(value = "Isp configuration properties", required = true) @RequestBody IspConfigurationRequest ispConfigurationRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        if(CommonUtils.isEndUser()) {
            throw new OptimApiException(HttpStatus.METHOD_NOT_ALLOWED, "RESOURCE_NOT_ALLOWED");
        } else if(CommonUtils.isGroupAdmin()) {
            String ispId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            if(!ispConfigurationRequest.getIspId().equals(ispId)) {
                throw new OptimApiException(HttpStatus.METHOD_NOT_ALLOWED, "RESOURCE_NOT_ALLOWED");
            }
        }

        Map<String, String> retMap = new HashMap<>();
        IspConfiguration ispConfiguration = ispConfigurationMapper.toIspConfiguration(ispConfigurationRequest);
        String id = ispConfigurationService.addConfiguration(ispConfiguration);

        retMap.put("id", id);
        return retMap;
    }

    @RequestMapping(value="/{configId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<IspConfigurationResponse> getIspConfiguration(
            @ApiParam(value = "configId", required = true) @PathVariable(name = "configId")  String configId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<IspConfiguration> ispConfigurationList = ispConfigurationService.getConfigurationById(configId);
        List<IspConfigurationResponse> ispConfigurationResponses = ispConfigurationMapper.toIspConfigurationResponse(ispConfigurationList);

        return ispConfigurationResponses;
    }

    @RequestMapping(value="/{configId}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    public void updateIspConfigurations(
            @ApiParam(value = "Config id", required = true) @PathVariable(name = "configId")  String configId,
            @ApiParam(value = "Isp configuration properties", required = true) @RequestBody IspConfigurationRequest ispConfigurationRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        if(CommonUtils.isEndUser()) {
            throw new OptimApiException(HttpStatus.METHOD_NOT_ALLOWED, "RESOURCE_NOT_ALLOWED");
        } else if(CommonUtils.isGroupAdmin()) {
            String ispId = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            if(!ispConfigurationRequest.getIspId().equals(ispId)) {
                throw new OptimApiException(HttpStatus.METHOD_NOT_ALLOWED, "RESOURCE_NOT_ALLOWED");
            }
        }

        IspConfiguration ispConfiguration = ispConfigurationMapper.toIspConfiguration(ispConfigurationRequest);
        ispConfigurationService.updateConfigurationById(configId, ispConfiguration);

    }

    @RequestMapping(value="/{configId}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeIspConfigurations(
            @ApiParam(value = "configId", required = true) @PathVariable(name = "configId")  String configId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        if(CommonUtils.isEndUser()) {
            throw new OptimApiException(HttpStatus.METHOD_NOT_ALLOWED, "RESOURCE_NOT_ALLOWED");
        }

        ispConfigurationService.deleteConfigurationById(configId);
    }
}
