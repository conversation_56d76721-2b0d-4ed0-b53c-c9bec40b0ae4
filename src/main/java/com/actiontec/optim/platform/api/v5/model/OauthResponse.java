package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

public class OauthResponse extends OauthConfigBase {
    private String id;
    private ArrayList<OauthConfigIspDetail> isps;
    private OauthConfigRoleDetail role;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public ArrayList<OauthConfigIspDetail> getIsps() {
        return isps;
    }

    public void setIsps(ArrayList<OauthConfigIspDetail> isps) {
        this.isps = isps;
    }

    public OauthConfigRoleDetail getRole() {
        return role;
    }

    public void setRole(OauthConfigRoleDetail role) {
        this.role = role;
    }
}
