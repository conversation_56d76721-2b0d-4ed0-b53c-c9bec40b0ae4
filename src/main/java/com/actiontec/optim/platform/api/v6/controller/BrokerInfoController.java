package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.service.BrokerInfoService;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@Api(value = "(V6) BrokerInfo", description = "API's for BrokerInfo Operations", tags = {"Optim - (V6) BrokerInfo"})
@RequestMapping(value = "/actiontec/api/v6/iam/brokers")
public class BrokerInfoController {
    @Autowired
    private BrokerInfoService brokerInfoService;

    @Value("${onboarding.twostage.enabled:false}")
    private boolean twoStageEnabled;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<BrokerInfoDTO> getAllBrokerInfos(
            @ModelAttribute BrokerInfoQueryDTO queryDTO) throws Exception {
        if (!twoStageEnabled) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Two stage onboarding is not enabled");
        }

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        return brokerInfoService.getAllBrokerInfos(queryDTO);
    }

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> createBrokerInfo(
            @ApiParam(value = "Create BrokerInfo properties", required = true) @RequestBody BrokerInfoRequest brokerInfoRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        if (!twoStageEnabled) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Two stage onboarding is not enabled");
        }

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        return brokerInfoService.createBrokerInfo(brokerInfoRequest);
    }

    @RequestMapping(value = "/{brokerId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BrokerInfoDTO getBrokerInfo(
            @ApiParam(value = "Broker Id", required = true)
            @PathVariable(name = "brokerId") String brokerId) throws Exception {
        if (!twoStageEnabled) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Two stage onboarding is not enabled");
        }

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        return brokerInfoService.getBrokerInfo(brokerId);
    }

    @RequestMapping(value = "/{brokerId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateBrokerInfo(
            @PathVariable(name = "brokerId") String brokerId,
            @ApiParam(value = "Update BrokerInfo properties", required = true) @RequestBody BrokerInfoRequest brokerInfoRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        if (!twoStageEnabled) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Two stage onboarding is not enabled");
        }

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        brokerInfoService.updateBrokerInfo(brokerId, brokerInfoRequest);
    }

    @RequestMapping(value = "/{brokerId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteBrokerInfo(
            @ApiParam(value = "Broker Id", required = true)
            @PathVariable String brokerId) throws Exception {
        if (!twoStageEnabled) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Two stage onboarding is not enabled");
        }

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }
        brokerInfoService.deleteBrokerInfo(brokerId);
    }
}
