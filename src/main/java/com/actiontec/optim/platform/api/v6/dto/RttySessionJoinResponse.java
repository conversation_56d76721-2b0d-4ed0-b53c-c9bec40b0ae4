package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.platform.api.v6.enums.RttySessionOperationType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.incs83.app.entities.RttySession;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Response DTO for joining a remote debug session
 */
@ApiModel(description = "Remote Debug Session Join data Response")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RttySessionJoinResponse {
    @ApiModelProperty(value = "SSH URL for the session")
    private String sshUrl;

    @ApiModelProperty(value = "HTTP URL for the session")
    private String httpUrl;

    public String getSshUrl() {
        return sshUrl;
    }

    public void setSshUrl(String sshUrl) {
        this.sshUrl = sshUrl;
    }

    public String getHttpUrl() {
        return httpUrl;
    }

    public void setHttpUrl(String httpUrl) {
        this.httpUrl = httpUrl;
    }
}
