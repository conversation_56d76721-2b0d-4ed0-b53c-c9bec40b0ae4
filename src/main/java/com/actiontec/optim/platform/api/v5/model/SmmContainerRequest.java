package com.actiontec.optim.platform.api.v5.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

public class SmmContainerRequest {
    private Actions actions;
    private Service service;

    public Actions getActions() {
        return actions;
    }

    public void setActions(Actions actions) {
        this.actions = actions;
    }

    public Service getService() {
        return service;
    }

    public void setService(Service service) {
        this.service = service;
    }

    public static class Actions {
        private String statusAction;
        private boolean serviceProvisioning;
        private boolean forceDownload;
        private boolean reInstall;
        private boolean autoStart = true;
        private boolean cleanInstall;

        public String getStatusAction() {
            return statusAction;
        }

        public void setStatusAction(String statusAction) {
            this.statusAction = statusAction;
        }

        public boolean isServiceProvisioning() {
            return serviceProvisioning;
        }

        public void setServiceProvisioning(boolean serviceProvisioning) {
            this.serviceProvisioning = serviceProvisioning;
        }

        public boolean isForceDownload() {
            return forceDownload;
        }

        public void setForceDownload(boolean forceDownload) {
            this.forceDownload = forceDownload;
        }

        public boolean isReInstall() {
            return reInstall;
        }

        public void setReInstall(boolean reInstall) {
            this.reInstall = reInstall;
        }

        public boolean isAutoStart() {
            return autoStart;
        }

        public void setAutoStart(boolean autoStart) {
            this.autoStart = autoStart;
        }

        public boolean isCleanInstall() {
            return cleanInstall;
        }

        public void setCleanInstall(boolean cleanInstall) {
            this.cleanInstall = cleanInstall;
        }

        @JsonIgnore
        public boolean getActionFlagsValue() {
            return this.serviceProvisioning || this.forceDownload || this.reInstall || this.autoStart;
        }
    }

    public static class Service {

        private String serviceId;

        private String version;

        private Object provisionData;

        public String getServiceId() {

            return serviceId;

        }

        public void setServiceId(String serviceId) {

            this.serviceId = serviceId;

        }

        public String getVersion() {

            return version;

        }

        public void setVersion(String version) {

            this.version = version;

        }

        public Object getProvisionData() {

            return provisionData;

        }

        public void setProvisionData(Object provisionData) {

            this.provisionData = provisionData;

        }

    }
}