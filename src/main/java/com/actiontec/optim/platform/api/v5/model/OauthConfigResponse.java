package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

public class OauthConfigResponse extends OauthConfigBase {
    private String id;
    private ArrayList<String> ispIds;
    private String defaultIspId;
    private String roleId;

    @Override
    public String getId() {
        return id;
    }

    @Override
    public void setId(String id) {
        this.id = id;
    }

    public ArrayList<String> getIspIds() {
        return ispIds;
    }

    public void setIspIds(ArrayList<String> ispIds) {
        this.ispIds = ispIds;
    }

    public String getDefaultIspId() {
        return defaultIspId;
    }

    public void setDefaultIspId(String defaultIspId) {
        this.defaultIspId = defaultIspId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
