package com.actiontec.optim.platform.api.v5.model;

import com.actiontec.optim.platform.model.Source;

public class SmmJobRequest {
    private String action;
    private String serviceId;
    private Source source;
    private String targetVersion;
    private int count = 0;
    private int rate = 0;
    private boolean resourceCheck;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public Source getSource() {
        return source;
    }

    public void setSource(Source source) {
        this.source = source;
    }

    public String getTargetVersion() {
        return targetVersion;
    }

    public void setTargetVersion(String targetVersion) {
        this.targetVersion = targetVersion;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getRate() {
        return rate;
    }

    public void setRate(int rate) {
        this.rate = rate;
    }

    public boolean isResourceCheck() {
        return resourceCheck;
    }

    public void setResourceCheck(boolean resourceCheck) {
        this.resourceCheck = resourceCheck;
    }
}
