package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.SmmLifeCycleActionRequest;
import com.actiontec.optim.platform.model.SmmLifeCycle;
import com.actiontec.optim.platform.model.SmmServiceBody;
import com.actiontec.optim.platform.service.SmmLifeCycleService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Smm;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping(value = "/actiontec/api/v5/smm/lifecycle/network/{stn}/services")
public class SmmLifeCycleController {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private SmmLifeCycleService smmLifeCycleService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmLifeCycle> getAll(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<SmmLifeCycle> smmLifeCycleList = smmLifeCycleService.getAll(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "SmmLifeCycleResource", null, "200", smmLifeCycleList, httpServletRequest);
        return smmLifeCycleList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    public Map<String, String>  createCpeSmmApp(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "SmmCpeApp properties", required = true) @RequestBody SmmServiceBody smmServiceRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> response = new HashMap<>();

        String id = smmLifeCycleService.createOne(stn, smmServiceRequest);
        if (id != null) {
            response.put("id", id);
        }
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "SmmLifeCycleResource", smmServiceRequest, "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/{serviceId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmLifeCycle> getOne(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "serviceId", required = true) @PathVariable(name = "serviceId")
                    String serviceId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SmmLifeCycle> smmLifeCycleList = smmLifeCycleService.getByStnAndServiceId(stn, serviceId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "SmmLifeCycleResource", null, "200", smmLifeCycleList, httpServletRequest);

        return smmLifeCycleList;
    }

    @RequestMapping(value = "/{serviceId}/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    public Map<String, String> postSmmCpeApp(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "serviceId", required = true) @PathVariable(name = "serviceId")
                    String serviceId,
            @ApiParam(value = "Container properties", required = true) @RequestBody SmmLifeCycleActionRequest smmLifeCycleActionRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Map<String, String> response = new HashMap<>();
        String id = smmLifeCycleService.postActions(stn, serviceId, smmLifeCycleActionRequest);
        if (Objects.nonNull(id)) {
            response.put("id", id);
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "SmmLifeCycleResource", smmLifeCycleActionRequest, "200", response, httpServletRequest);
        return response;
    }
}
