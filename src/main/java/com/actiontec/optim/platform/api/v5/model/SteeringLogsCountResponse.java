package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

public class SteeringLogsCountResponse {

    private String eventType;
    private int duration;
    private ArrayList<ValueDto> values;

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public ArrayList<ValueDto> getValues() {
        return values;
    }

    public void setValues(ArrayList<ValueDto> values) {
        this.values = values;
    }

    public static class ValueDto {

        private String steeringEnd;
        private String originRadioKey;
        private String destRadioKey;
        private long count;

        public String getSteeringEnd() {
            return steeringEnd;
        }

        public void setSteeringEnd(String steeringEnd) {
            this.steeringEnd = steeringEnd;
        }

        public String getOriginRadioKey() {
            return originRadioKey;
        }

        public void setOriginRadioKey(String originRadioKey) {
            this.originRadioKey = originRadioKey;
        }

        public String getDestRadioKey() {
            return destRadioKey;
        }

        public void setDestRadioKey(String destRadioKey) {
            this.destRadioKey = destRadioKey;
        }

        public long getCount() {
            return count;
        }

        public void setCount(long count) {
            this.count = count;
        }
    }
}
