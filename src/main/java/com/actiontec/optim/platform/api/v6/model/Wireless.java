package com.actiontec.optim.platform.api.v6.model;

import java.util.List;

public class Wireless {
    private List<Ssid> ssids;
    private List<Radio> radios;
    private BackhaulSteering backhualSteering;
    private SmartHome smartHome;

    public List<Ssid> getSsids() {
        return ssids;
    }

    public void setSsids(List<Ssid> ssids) {
        this.ssids = ssids;
    }

    public List<Radio> getRadios() {
        return radios;
    }

    public void setRadios(List<Radio> radios) {
        this.radios = radios;
    }

    public BackhaulSteering getBackhualSteering() {
        return backhualSteering;
    }

    public void setBackhualSteering(BackhaulSteering backhualSteering) {
        this.backhualSteering = backhualSteering;
    }

    public SmartHome getSmartHome() {
        return smartHome;
    }

    public void setSmartHome(SmartHome smartHome) {
        this.smartHome = smartHome;
    }
}
