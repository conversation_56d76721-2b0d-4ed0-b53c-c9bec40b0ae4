package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.model.ActionRequest;
import com.actiontec.optim.platform.model.firmware.FwPolicyBaseRequest;
import com.actiontec.optim.platform.model.firmware.FwPolicyInfo;
import com.actiontec.optim.platform.model.firmware.FwPolicyRequest;
import com.actiontec.optim.platform.service.FirmwarePolicyService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Acs;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/fwupgrade/upgradePolicies")
public class FirmwarePolicyController {
    @Autowired
    private AuditService auditService;
    @Autowired
    FirmwarePolicyService firmwarePolicyService;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    public List<FwPolicyInfo> getPolicies(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<FwPolicyInfo> response = firmwarePolicyService.getPolicy(null);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.GET_FIRMWARE_POLICY, null, "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Acs.class)
    public Map<String, String> postPolicy(
            @ApiParam(value = "Policy properties", required = true) @RequestBody @Valid FwPolicyRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> reponse = firmwarePolicyService.postPolicy(request);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.POST_FIRMWARE_POLICY, request, "204", reponse, httpServletRequest);
        return reponse;
    }

    @RequestMapping(value = "/{policyId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    public List<FwPolicyInfo> getPolicy(
            @ApiParam(value = "policyId", required = true) @PathVariable(name = "policyId")
                    String policyId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<FwPolicyInfo> response = firmwarePolicyService.getPolicy(policyId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.GET_FIRMWARE_POLICY, null, "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/{policyId}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Acs.class)
    public void putPolicy(
            @ApiParam(value = "policyId", required = true) @PathVariable(name = "policyId")
                    String policyId,
            @ApiParam(value = "Policy base properties", required = true) @RequestBody @Valid FwPolicyBaseRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        firmwarePolicyService.putPolicy(policyId, request);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.PUT_FIRMWARE_POLICY, request, "204", null, httpServletRequest);
    }

    @RequestMapping(value = "/{policyId}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Acs.class)
    public void removePolicy(
            @ApiParam(value = "policyId", required = true) @PathVariable(name = "policyId")
                    String policyId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        firmwarePolicyService.removePolicy(policyId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.DELETE_FIRMWARE_POLICY, null, "204", null, httpServletRequest);

    }

    @RequestMapping(value = "/{policyId}/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Acs.class)
    public void postActions(
            @ApiParam(value = "policyId", required = true) @PathVariable(name = "policyId")
                    String policyId,
            @ApiParam(value = "Policy base properties", required = true) @RequestBody ActionRequest actionRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> response = new HashMap<>();
        String id = firmwarePolicyService.postActions(policyId, actionRequest);
        if (id != null)
            response.put("id", id);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.POST_FIRMWARE_POLICY_ACTION, actionRequest, "200", response, httpServletRequest);
    }

    @RequestMapping(value = "/{policyId}/logs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    public List<Map<String, Object>> getPolicyLogs(
            @ApiParam(value = "policyId", required = true) @PathVariable(name = "policyId")
                    String policyId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<Map<String, Object>> response = firmwarePolicyService.getLogs(policyId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.GET_FIRMWARE_POLICY_LOGS, null, "200", response, httpServletRequest);
        return response;
    }
}
