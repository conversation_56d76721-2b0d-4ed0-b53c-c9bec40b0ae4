package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.ChannelSelectDistributionResponse;
import com.actiontec.optim.platform.model.ChannelSelectDistribution;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ChannelSelectDistributionMapper {

    public List<ChannelSelectDistributionResponse> toChannelSelectDistributionResponse(List<ChannelSelectDistribution> channelSelectDistributionList);
}
