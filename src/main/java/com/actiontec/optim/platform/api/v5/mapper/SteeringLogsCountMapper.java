package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.SteeringLogsCountResponse;
import com.actiontec.optim.platform.model.SteeringLogsCount;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SteeringLogsCountMapper {

    public List<SteeringLogsCountResponse> toSteeringLogsCountResponse(List<SteeringLogsCount> steeringLogsCountList);
}
