package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.mapper.EquipmentTrackingMapper;
import com.actiontec.optim.platform.api.v5.model.EquipmentTrackingResponse;
import com.actiontec.optim.platform.api.v5.model.EquipmentTransactionResponse;
import com.actiontec.optim.platform.api.v5.model.TransactionRequest;
import com.actiontec.optim.platform.model.EquipmentTrackingRecord;
import com.actiontec.optim.platform.model.EquipmentTransaction;
import com.actiontec.optim.platform.service.CSVService;
import com.actiontec.optim.platform.service.EquipmentTrackingService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Acs;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/equipmenttracking/")
public class EquipmentTrackingController {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private EquipmentTrackingService trackingService;
    @Autowired
    private CSVService csvService;
    @Autowired
    private EquipmentTrackingMapper equipmentTrackingMapper;
    @Autowired
    private AuditService auditService;


    @RequestMapping(value = "records", method = RequestMethod.GET, headers = "Accept=application/json", produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<EquipmentTrackingResponse> jsonRecords(
            @ApiParam(value = "Pagination")  @RequestParam(name = "pagination", required = false, defaultValue = "false") Boolean pagination,
            @ApiParam(value = "Offset")  @RequestParam(name = "offset", required = false, defaultValue = "0") Integer offset,
            @ApiParam(value = "limit")  @RequestParam(name = "limit", required = false, defaultValue = "100") Integer limit,
            @ApiParam(value = "filters.serialNumber")  @RequestParam(name = "filters.serialNumber", required = false) String serialNumber,
            @ApiParam(value = "filters.ispId")  @RequestParam(name = "filters.ispId", required = false) String ispId,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<EquipmentTrackingRecord> equipmentTrackingRecords = trackingService.getEquipmentTrackingRecords(pagination, offset, limit, serialNumber, ispId);
        List<EquipmentTrackingResponse> equipmentTrackingResponses = equipmentTrackingMapper.toEquipmentTrackingResponse(equipmentTrackingRecords);
        return equipmentTrackingResponses;
    }

    @RequestMapping(value = "records", method = RequestMethod.GET, headers = "Accept=text/csv", produces = "text/csv")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseEntity<Resource> csvRecords(
            @ApiParam(value = "Pagination")  @RequestParam(name = "pagination", required = false, defaultValue = "false") Boolean pagination,
            @ApiParam(value = "Offset")  @RequestParam(name = "offset", required = false, defaultValue = "0") Integer offset,
            @ApiParam(value = "limit")  @RequestParam(name = "limit", required = false, defaultValue = "100") Integer limit,
            @ApiParam(value = "filters.serialNumber")  @RequestParam(name = "filters.serialNumber", required = false) String serialNumber,
            @ApiParam(value = "filters.ispId")  @RequestParam(name = "filters.ispId", required = false) String ispId,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        String filename = "tracking.csv";
        List<EquipmentTrackingRecord> equipmentTrackingRecords = trackingService.getEquipmentTrackingRecords(pagination, offset, limit, serialNumber, ispId);
        InputStreamResource file = new InputStreamResource(csvService.recordsToCvs(equipmentTrackingRecords));

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/csv"))
                .body(file);
    }

    @RequestMapping(value = "records/{recordId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<EquipmentTrackingResponse> getRecord(
            @ApiParam(value = "recordId", required = true) @PathVariable(name = "recordId") String recordId,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<EquipmentTrackingRecord> equipmentTrackingRecords = trackingService.getEquipmentTrackingRecordById(recordId);
        List<EquipmentTrackingResponse> equipmentTrackingResponses = equipmentTrackingMapper.toEquipmentTrackingResponse(equipmentTrackingRecords);
        return equipmentTrackingResponses;
    }

    @RequestMapping(value = "transactions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Acs.class)
    @ResponseStatus(HttpStatus.OK)
    public Map<String, String> postTransaction(
            @ApiParam(value = "Transaction Properties", required = true) @RequestBody TransactionRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> response = null;
        response = trackingService.postTransaction(request);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, AuditorConstants.POST_TRACKING_TRANSACTION, request, String.valueOf(200), response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "transactions", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<EquipmentTransactionResponse> getTransactions(
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<EquipmentTransaction> equipmentTransactions = trackingService.getEquipmentTransactions();
        List<EquipmentTransactionResponse> equipmentTransactionResponses = equipmentTrackingMapper.toEquipmentTransactionResponse(equipmentTransactions);

        return equipmentTransactionResponses;
    }

    @RequestMapping(value = "transactions/{transactionId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<EquipmentTransactionResponse> getTransaction(
            @ApiParam(value = "transactionId", required = true) @PathVariable(name = "transactionId") String transactionId,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<EquipmentTransaction> equipmentTransactions = trackingService.getEquipmentTransactionById(transactionId);
        List<EquipmentTransactionResponse> equipmentTransactionResponses = equipmentTrackingMapper.toEquipmentTransactionResponse(equipmentTransactions);

        return equipmentTransactionResponses;
    }
}
