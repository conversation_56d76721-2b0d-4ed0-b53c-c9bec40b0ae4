package com.actiontec.optim.platform.api.v6.security.auth.jwt.provider;

import com.actiontec.optim.platform.api.v6.security.auth.jwt.validator.AppCloudJwtTokenValidator;
import com.actiontec.optim.platform.api.v6.security.tokenFactory.AppCloudJwtAuthenticationToken;
import com.actiontec.optim.platform.service.AppCloudService;
import com.actiontec.optim.platform.service.NCSNetworkService;
import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.entities.AppCloudConfig;
import com.incs83.app.entities.NCSNetwork;
import com.incs83.config.JwtConfig;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.xml.bind.ValidationException;

@Component
public class AppCloudAuthenticationProvider implements AuthenticationProvider {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private NCSNetworkService ncsNetworkService;

    @Autowired
    private AppCloudService appCloudService;

    @Autowired
    private AppCloudJwtTokenValidator appCloudJwtTokenValidator;

    @Autowired
    private JwtConfig jwtConfig;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        AppCloudJwtAuthenticationToken authToken = (AppCloudJwtAuthenticationToken) authentication;
        String exchangeToken = (String) authToken.getCredentials();
        String networkId = authToken.getNetworkId();
        String networkTokenId = authToken.getNetworkTokenId();

        logger.info("exchange token: {}, signing key: {}", exchangeToken, jwtConfig.getTokenSigningKey());
        if (CustomStringUtils.isEmpty(exchangeToken)) {
            throw new InsufficientAuthenticationException("Missing exchange token");
        }

        if (!appCloudJwtTokenValidator.verifyExchangeToken(exchangeToken, jwtConfig.getTokenSigningKey())) {
            throw new BadCredentialsException("Invalid exchange token");
        }

        String kid;
        AppCloudConfig appCloudConfig;
        try {
            kid = AppCloudJwtTokenUtils.extractKid(exchangeToken);
            appCloudConfig = appCloudService.getAppCloudConfigByKid(kid);
        } catch (Exception e) {
            throw new AuthenticationServiceException("Failed to extract kid from token: " + e.getMessage(), e);
        }

        if (ObjectUtils.isEmpty(appCloudConfig)) {
            throw new AuthenticationServiceException("AppCloudConfig not found for kid: " + kid);
        }

        // validate network token id
        if (CustomStringUtils.isNotEmpty(networkId)) {
            try {
                NCSNetwork network = ncsNetworkService.getNetworkById(networkId);
                if (!CustomStringUtils.equals(networkTokenId, network.getNetworkTokenId())) {
                    logger.info("networkTokenId not match, request networkTokenId:[{}], db networkTokenId:[{}]",
                            networkTokenId, network.getNetworkTokenId());
                    throw new AuthenticationServiceException("Invalid network token id");
                }
            } catch (Exception e) {
                logger.error("Error fetching network or validating token: {}", e.getMessage(), e);
                throw new AuthenticationServiceException("Network not found for networkId: " + networkId);
            }
        }

        return new AppCloudJwtAuthenticationToken(exchangeToken, networkId, networkTokenId, appCloudConfig);
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return AppCloudJwtAuthenticationToken.class.isAssignableFrom(authentication);
    }
}
