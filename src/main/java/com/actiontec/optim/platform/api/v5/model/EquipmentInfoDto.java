package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

public class EquipmentInfoDto {
    private String id;
    private String status;
    private String serialNumber;
    private String macAddress;
    private String modelName;
    private String name;
    private String fwVersion;
    private String agentVersion;
    private int cpuUsage;
    private int memoryUsage;
    private long totalMemory;
    private long availableMemory;
    private long totalFlashSpace;
    private long availableFlashSpace;
    private String equipmentType;
    private long lastRebootTime;
    private long lastDataProcessedTime;
    private long lastReportTime;
    private ModelSnapshotResponse snapshot;
    private ModelHardwareDto hardware;
    private ArrayList<String> features;
    private String equipmentTypeId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFwVersion() {
        return fwVersion;
    }

    public void setFwVersion(String fwVersion) {
        this.fwVersion = fwVersion;
    }

    public String getAgentVersion() {
        return agentVersion;
    }

    public void setAgentVersion(String agentVersion) {
        this.agentVersion = agentVersion;
    }

    public int getCpuUsage() {
        return cpuUsage;
    }

    public void setCpuUsage(int cpuUsage) {
        this.cpuUsage = cpuUsage;
    }

    public int getMemoryUsage() {
        return memoryUsage;
    }

    public void setMemoryUsage(int memoryUsage) {
        this.memoryUsage = memoryUsage;
    }

    public long getTotalMemory() {
        return totalMemory;
    }

    public void setTotalMemory(long totalMemory) {
        this.totalMemory = totalMemory;
    }

    public long getAvailableMemory() {
        return availableMemory;
    }

    public void setAvailableMemory(long availableMemory) {
        this.availableMemory = availableMemory;
    }

    public long getTotalFlashSpace() {
        return totalFlashSpace;
    }

    public void setTotalFlashSpace(long totalFlashSpace) {
        this.totalFlashSpace = totalFlashSpace;
    }

    public long getAvailableFlashSpace() {
        return availableFlashSpace;
    }

    public void setAvailableFlashSpace(long availableFlashSpace) {
        this.availableFlashSpace = availableFlashSpace;
    }

    public String getEquipmentType() {
        return equipmentType;
    }

    public void setEquipmentType(String equipmentType) {
        this.equipmentType = equipmentType;
    }

    public long getLastRebootTime() {
        return lastRebootTime;
    }

    public void setLastRebootTime(long lastRebootTime) {
        this.lastRebootTime = lastRebootTime;
    }

    public long getLastDataProcessedTime() {
        return lastDataProcessedTime;
    }

    public void setLastDataProcessedTime(long lastDataProcessedTime) {
        this.lastDataProcessedTime = lastDataProcessedTime;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public ModelSnapshotResponse getSnapshot() {
        return snapshot;
    }

    public void setSnapshot(ModelSnapshotResponse snapshot) {
        this.snapshot = snapshot;
    }

    public ModelHardwareDto getHardware() {
        return hardware;
    }

    public void setHardware(ModelHardwareDto hardware) {
        this.hardware = hardware;
    }

    public ArrayList<String> getFeatures() {
        return features;
    }

    public void setFeatures(ArrayList<String> features) {
        this.features = features;
    }

    public String getEquipmentTypeId() {
        return equipmentTypeId;
    }

    public void setEquipmentTypeId(String equipmentTypeId) {
        this.equipmentTypeId = equipmentTypeId;
    }
}
