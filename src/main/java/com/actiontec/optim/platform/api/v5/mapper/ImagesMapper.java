package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.FwImageResponse;
import com.actiontec.optim.platform.api.v5.model.ImageRequest;
import com.actiontec.optim.platform.api.v5.model.ImageResponse;
import com.actiontec.optim.platform.model.FwImage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.ArrayList;
import java.util.Arrays;

@Mapper(componentModel = "spring")
public abstract class ImagesMapper {

    @Mapping(target = "locationType", source = "type")
    @Mapping(target = "fileLocation", source = "location")
    public abstract FwImage toFwImage(ImageRequest imageRequest);

    public abstract ImageResponse toImagesResponse(FwImage fwImage);

    @Mapping(target = "type", source = "locationType")
    @Mapping(target = "location", source = "fileLocation")
    public abstract FwImageResponse toFwImageResponse(FwImage fwImage);

}
