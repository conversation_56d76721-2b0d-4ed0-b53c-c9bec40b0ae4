package com.actiontec.optim.platform.api.v5.controller;


import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.NetworkTraffic;
import com.actiontec.optim.platform.service.NetworkTrafficService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/")
public class NetworkTrafficController {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private ResponseUtil responseUtil;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private NetworkTrafficService networkTrafficService;
    @Autowired
    private AuditService auditService;

    @RequestMapping(value = "traffic", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<NetworkTraffic> getNetworkTraffic(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Traffic type", required = false)  @RequestParam(name = "type", defaultValue = "Internet", required = false) String type,
            @RequestParam(required = false, name = "startTime", defaultValue = "0") Long startTime,
            @RequestParam(required = false, name = "endTime", defaultValue = "0") Long endTime,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest
    ) throws Exception {
        String userId = at3Adapter.getRgwSerialByStn(stn);
        List<NetworkTraffic> response = networkTrafficService.getNetworkTraffic(userId, startTime, endTime, type);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Network Traffic", type, HttpStatus.OK.toString(), response, httpServletRequest);
        return response;
    }

}
