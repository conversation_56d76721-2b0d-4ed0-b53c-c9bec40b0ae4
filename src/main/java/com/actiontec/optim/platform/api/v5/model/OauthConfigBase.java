package com.actiontec.optim.platform.api.v5.model;

import javax.validation.constraints.NotBlank;

public class OauthConfigBase {
    private String id;

    @NotBlank(message = "Name cannot be empty.")
    private String name;

    @NotBlank(message = "Provider cannot be empty")
    private String provider;

    @NotBlank(message = "ClientId cannot be empty")
    private String clientId;

    @NotBlank(message = "RedirectUri cannot be empty")
    private String redirectUri;

    @NotBlank(message = "ResponseType cannot be empty")
    private String responseType;

    @NotBlank(message = "ResponseType cannot be empty")
    private String scope;

    private Boolean openIdSupported;

    @NotBlank(message = "WellKnownConfig cannot be empty.")
    private String wellKnownConfig;

    private Boolean isDefaultProvider;

    private OauthConfigEndpointDetail endpoints;

    private OauthConfigExtraClientDataDetail extraClientData;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getResponseType() {
        return responseType;
    }

    public void setResponseType(String responseType) {
        this.responseType = responseType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public Boolean getOpenIdSupported() {
        return openIdSupported;
    }

    public void setOpenIdSupported(Boolean openIdSupported) {
        this.openIdSupported = openIdSupported;
    }

    public String getWellKnownConfig() {
        return wellKnownConfig;
    }

    public void setWellKnownConfig(String wellKnownConfig) {
        this.wellKnownConfig = wellKnownConfig;
    }

    public Boolean getIsDefaultProvider() {
        return isDefaultProvider;
    }

    public void setIsDefaultProvider(Boolean isDefaultProvider) {
        this.isDefaultProvider = isDefaultProvider;
    }

    public OauthConfigEndpointDetail getEndpoints() {
        return endpoints;
    }

    public void setEndpoints(OauthConfigEndpointDetail endpoints) {
        this.endpoints = endpoints;
    }

    public OauthConfigExtraClientDataDetail getExtraClientData() {
        return extraClientData;
    }

    public void setExtraClientData(OauthConfigExtraClientDataDetail extraClientData) {
        this.extraClientData = extraClientData;
    }
}
