package com.actiontec.optim.platform.api.v6.security.auth.jwt.filter;

import com.actiontec.optim.platform.api.v6.security.auth.jwt.validator.AppCloudJwtTokenValidator;
import com.actiontec.optim.platform.api.v6.security.tokenFactory.AppCloudJwtAuthenticationToken;
import com.actiontec.optim.platform.service.AppCloudService;
import com.actiontec.optim.platform.service.NCSNetworkService;
import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.config.JwtConfig;
import com.incs83.security.auth.jwt.extractor.TokenExtractor;
import com.incs83.security.config.WebSecurityConfig;
import io.jsonwebtoken.JwtException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ObjectUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@Component
public class AppCloudJwtTokenFilter extends OncePerRequestFilter {
    private final Logger logger = LogManager.getLogger(this.getClass());

    private final ObjectProvider<AuthenticationManager> authenticationManagerProvider;
    private final AppCloudService appCloudService;
    private final AppCloudJwtTokenValidator appCloudJwtTokenValidator;
    private final JwtConfig jwtConfig;
    private final TokenExtractor tokenExtractor;
    private final AntPathMatcher pathMatcher;

    @Autowired
    public AppCloudJwtTokenFilter(
            ObjectProvider<AuthenticationManager> authenticationManagerProvider, AppCloudService appCloudService,
            AppCloudJwtTokenValidator appCloudJwtTokenValidator, JwtConfig jwtConfig, TokenExtractor tokenExtractor,
            AntPathMatcher pathMatcher) {
        this.authenticationManagerProvider = authenticationManagerProvider;
        this.appCloudService = appCloudService;
        this.appCloudJwtTokenValidator = appCloudJwtTokenValidator;
        this.jwtConfig = jwtConfig;
        this.tokenExtractor = tokenExtractor;
        this.pathMatcher = pathMatcher;
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        // Only filter App Cloud API paths
        return !pathMatcher.match("/**/api/v6/networks/**", requestUri);
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        logger.info("AppCloudJwtTokenFilter triggered: {}", request.getRequestURI());
        String networkApiPattern = "/**/api/v6/networks/{networkId}/**";
        String requestUri = request.getRequestURI();
        String networkId = null;
        String networkTokenId = null;

        // prevent duplicate authentication
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!ObjectUtils.isEmpty(authentication) && authentication.isAuthenticated()) {
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // Extract the JWT token payload from the header using tokenExtractor
            String rawHeader = request.getHeader(WebSecurityConfig.JWT_TOKEN_HEADER_PARAM);
            String token = tokenExtractor.extract(rawHeader);

            if (CustomStringUtils.isEmpty(token)) {
                sendUnauthorizedResponse(response, "Authentication failed: token is missing or empty.");
                return;
            }

            // only app cloud admin need to verify network_token_id
            try {
                String roleId = AppCloudJwtTokenUtils.extractRoleId(token, jwtConfig.getTokenSigningKey());
                if (!CustomStringUtils.equals(roleId, ApplicationConstants.APP_CLOUD_ADMIN_ROLE_ID)) {
                    filterChain.doFilter(request, response);
                    return;
                }
            } catch (JwtException e) {
                logger.error("Error extracting role ID from token: {}", e.getMessage(), e);
                sendUnauthorizedResponse(response, "Authentication failed: invalid or expired token.");
                return;
            }

            // Validate JWT token
//            if (!appCloudJwtTokenValidator.verifyExchangeToken(token, jwtConfig.getTokenSigningKey())) {
//                sendUnauthorizedResponse(response, "Authentication failed: invalid or expired token.");
//                return;
//            }

            // extract networkId from pathUri
            if (pathMatcher.match(networkApiPattern, requestUri)) {
                Map<String, String> variables = pathMatcher.extractUriTemplateVariables(networkApiPattern, requestUri);
                networkId = variables.get("networkId");
            }

            if (CustomStringUtils.isNotEmpty(networkId)) {
                networkTokenId = request.getHeader("networkTokenId");
            }

            Authentication authenticated = authenticationManagerProvider.getObject()
                    .authenticate(new AppCloudJwtAuthenticationToken(token, networkId, networkTokenId));
            SecurityContextHolder.getContext().setAuthentication(authenticated);

            filterChain.doFilter(request, response);
        } catch (Exception e) {
            logger.error("Error in AppCloudJwtFilter: {}", e.getMessage(), e);
            sendUnauthorizedResponse(response, "Authentication failed due to internal error.");
        }
    }

    private void sendUnauthorizedResponse(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write("{\"error\":\"" + message + "\"}");
    }
}
