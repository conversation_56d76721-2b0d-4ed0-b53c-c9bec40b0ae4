package com.actiontec.optim.platform.api.v6.dto;

public abstract class CpeApiEndpoint {
    private String path;
    private String method;
    private Integer httpStatusCode;

    // Getter 和 Setter
    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Integer getHttpStatusCode() {
        return httpStatusCode;
    }

    public void setHttpStatusCode(Integer httpStatusCode) {
        this.httpStatusCode = httpStatusCode;
    }
}
