package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.ConnectionDownstreamResponse;
import com.actiontec.optim.platform.model.equipment.NetworkDownstreamInterface;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ConnectionDownstreamResponseMapper {

    @Mapping(target = "ipv6.enabled", source = "ipv6Enabled")
    @Mapping(target = "ipv6.addresses", source = "ipv6Addresses")
    public ConnectionDownstreamResponse toConnectionDownstreamResponse(NetworkDownstreamInterface downstreamInterface);

    public List<ConnectionDownstreamResponse> toConnectionDownstreamResponse(List<NetworkDownstreamInterface> downstreamInterfaces);
}
