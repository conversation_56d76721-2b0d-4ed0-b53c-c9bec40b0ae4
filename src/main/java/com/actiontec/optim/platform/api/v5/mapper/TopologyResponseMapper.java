package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.TopologyResponse;
import com.actiontec.optim.platform.model.UserDevice;
import com.actiontec.optim.platform.model.enums.UserDeviceType;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class TopologyResponseMapper {
    private final Logger logger = LogManager.getLogger(this.getClass());
    public TopologyResponse toTopologyResponse(Map<String, UserDevice> userDeviceMap) {
        TopologyResponse topologyResponse = new TopologyResponse();

        List<TopologyResponse.TopologyEquipmentResponse> equipmentResponseList = new ArrayList<>();
        topologyResponse.setEquipmentList(equipmentResponseList);
        List<TopologyResponse.TopologyDeviceResponse> deviceResponseList = new ArrayList<>();
        topologyResponse.setDeviceList(deviceResponseList);

        for (UserDevice userDevice : userDeviceMap.values()) {

            if (UserDeviceType.Gateway == userDevice.getDeviceType()
                    || UserDeviceType.Extender == userDevice.getDeviceType()) {

                if (!StringUtils.isEmpty(userDevice.getSerialNumber())) {
                    // no corresponding record in apDetail
                    // means extender never send a valid report
                    // not put this entity into equipment list
                    //continue;

                    TopologyResponse.TopologyEquipmentResponse equipmentResponse = new TopologyResponse.TopologyEquipmentResponse();
                    equipmentResponse.setId(userDevice.getSerialNumber());
                    equipmentResponse.setSerialNumber(userDevice.getSerialNumber());
                    equipmentResponse.setMacAddress(userDevice.getMacAddress());
                    equipmentResponse.setStatus(userDevice.getStatus().name());
                    if (userDevice.getDeviceType() != null) {
                        equipmentResponse.setEquipmentType(userDevice.getDeviceType().name());
                    }
                    equipmentResponse.setUpstream(userDevice.getUpstream());
                    if (userDevice.getPhyType() != null) {
                        equipmentResponse.setPhyType(userDevice.getPhyType().getFormalName());
                    }

                    if (StringUtils.isNotBlank(userDevice.getFriendlyName())) {
                        equipmentResponse.setName(userDevice.getFriendlyName());
                    } else {
                        equipmentResponse.setName(userDevice.getHostName());
                    }

                    equipmentResponseList.add(equipmentResponse);

                    if (UserDeviceType.Gateway == userDevice.getDeviceType()) {
                        topologyResponse.setLastReportTime(userDevice.getNtTimestamp());
                    }
                }
            }

            if (UserDeviceType.Gateway != userDevice.getDeviceType()) {
                TopologyResponse.TopologyDeviceResponse deviceResponse = new TopologyResponse.TopologyDeviceResponse();
                deviceResponse.setId(userDevice.getMacAddress());
                deviceResponse.setMacAddress(userDevice.getMacAddress());

                // XXX: hack for extender mac
//                if (UserDeviceType.Extender == userDevice.getDeviceType()) {
//                    deviceResponse.setId(userDevice.getWanMacAddress());
//                    deviceResponse.setMacAddress(userDevice.getWanMacAddress());
//                }

                deviceResponse.setStatus(userDevice.getStatus().name());
                if (userDevice.getDeviceType() != null) {
                    deviceResponse.setDeviceType(userDevice.getDeviceType().name());
                }
                deviceResponse.setUpstream(userDevice.getUpstream());
                if (userDevice.getPhyType() != null) {
                    deviceResponse.setPhyType(userDevice.getPhyType().getFormalName());
                }

                if (StringUtils.isNotBlank(userDevice.getFriendlyName())) {
                    deviceResponse.setName(userDevice.getFriendlyName());
                }
                deviceResponseList.add(deviceResponse);
            }
        }

        return topologyResponse;
    }
}
