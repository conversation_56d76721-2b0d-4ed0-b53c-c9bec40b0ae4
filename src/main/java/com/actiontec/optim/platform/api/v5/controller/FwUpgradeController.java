package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.mapper.FwVerDistributionResponseMapper;
import com.actiontec.optim.platform.api.v5.mapper.ImagesMapper;
import com.actiontec.optim.platform.api.v5.model.FwImageResponse;
import com.actiontec.optim.platform.api.v5.model.FwVerDistributionResponse;
import com.actiontec.optim.platform.api.v5.model.ImageRequest;
import com.actiontec.optim.platform.api.v5.model.ImageResponse;
import com.actiontec.optim.platform.model.FwImage;
import com.actiontec.optim.platform.service.FwImageService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Acs;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v5/fwupgrade")
public class FwUpgradeController {

    @Autowired
    private FwImageService fwImageService;

    @Autowired
    private ImagesMapper imagesMapper;

    @Autowired
    private FwVerDistributionResponseMapper fwVerDistributionResponseMapper;

    @Autowired
    private AuditService auditService;

    @RequestMapping(value = "/images", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    public List<FwImageResponse> getAllImages(
            @RequestParam(name = "equipmentTypeId", required = false) String equipmentTypeIds,
            @RequestParam(name = "groupId", required = false) String groupIds,
            @RequestParam(name = "ispId",  required = false) String ispId,
            HttpServletRequest httpServletRequest) throws Exception {

        List<FwImage> fwImageList = fwImageService.findFwImagesByIds(equipmentTypeIds, groupIds, ispId);

        List<FwImageResponse> fwImageResponseList = new ArrayList<>();
        for(FwImage fwImage : fwImageList) {
            fwImageResponseList.add(imagesMapper.toFwImageResponse(fwImage));
        }
        fwImageService.populateResponseIspId(fwImageResponseList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "FW Upgrade Resource", null, "200", fwImageResponseList, httpServletRequest);
        return fwImageResponseList;
    }

    @RequestMapping(value = "/images", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Acs.class)
    @ResponseStatus(HttpStatus.CREATED)
    public ImageResponse createImage(
            @ApiParam(value = "Image properties", required = true) @RequestBody ImageRequest imageRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        FwImage fwImage = imagesMapper.toFwImage(imageRequest);
        Boolean ret = fwImageService.createFwImage(fwImage, imageRequest.getEquipmentTypeIds());
        if(ret) {
            ImageResponse imageResponse = imagesMapper.toImagesResponse(fwImage);
            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "FW Upgrade Resource", imageRequest, "201", imageResponse, httpServletRequest);
            return imageResponse;
        }

        throw new OptimApiException(HttpStatus.BAD_REQUEST, "Fail to create wrong image");
    }

    @RequestMapping(value = "/images/{imageId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    public List<FwImageResponse> getImage(
            @ApiParam(value = "imageId", required = true) @PathVariable(name = "imageId")  String imageId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<FwImageResponse> fwImageResponseList = new ArrayList<>();

        FwImage fwImage = fwImageService.findFwImageById(imageId);

        if(fwImage != null) {
            FwImageResponse fwImageResponse = imagesMapper.toFwImageResponse(fwImage);
            if(fwImageResponse != null) {
                fwImageResponseList.add(fwImageResponse);
            }
            fwImageService.populateResponseIspId(fwImageResponseList);
            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "FW Upgrade Resource", null, "200", fwImageResponseList, httpServletRequest);
            return fwImageResponseList;
        }

        throw new OptimApiException(HttpStatus.BAD_REQUEST, "Image configuration not found");
    }

    @RequestMapping(value = "/images/{imageId}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Acs.class)
    public void updateImage(
            @ApiParam(value = "imageId", required = true) @PathVariable(name = "imageId")  String imageId,
            @ApiParam(value = "Image properties", required = true) @RequestBody ImageRequest imageRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        FwImage fwImage = imagesMapper.toFwImage(imageRequest);
        Boolean ret = fwImageService.updateFwImageById(imageId, fwImage, imageRequest.getEquipmentTypeIds());

        if(!ret) {
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Image configuration not found");
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "FW Upgrade Resource", imageRequest, "200", null, httpServletRequest);
    }

    @RequestMapping(value = "/images/{imageId}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Acs.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeImage(
            @ApiParam(value = "imageId", required = true) @PathVariable(name = "imageId")  String imageId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        fwImageService.deleteFwImageById(imageId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "FW Upgrade Resource", null, "204", null, httpServletRequest);
    }

    @RequestMapping(value = "/aggregation/firmwareVersionDistribution", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    public List<FwVerDistributionResponse> getFwDistribution(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<FwVerDistributionResponse> fwVerDistributionResponseList = fwVerDistributionResponseMapper.toFwVerDistributionResponse(fwImageService.calculateFwVerDistribution());
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "FW Upgrade Resource", null, "200", fwVerDistributionResponseList, httpServletRequest);
        return fwVerDistributionResponseList;
    }
}
