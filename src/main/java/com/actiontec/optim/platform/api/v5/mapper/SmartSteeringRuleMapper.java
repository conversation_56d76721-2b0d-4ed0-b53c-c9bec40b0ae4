package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.SmartSteeringRuleRequest;
import com.actiontec.optim.platform.api.v5.model.SmartSteeringRuleResponse;
import com.actiontec.optim.platform.model.SmartSteeringRule;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface SmartSteeringRuleMapper {

    public SmartSteeringRule toSmartSteeringRule(SmartSteeringRuleRequest smartSteeringRuleRequest);
    public SmartSteeringRuleResponse toSmartSteeringRuleResponse(SmartSteeringRule smartSteeringRule);
    public List<SmartSteeringRuleResponse> toSmartSteeringRuleResponseList(List<SmartSteeringRule> smartSteeringRuleList);

}
