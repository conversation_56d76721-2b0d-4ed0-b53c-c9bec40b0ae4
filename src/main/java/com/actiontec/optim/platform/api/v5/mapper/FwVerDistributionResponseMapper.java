package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.FwVerDistributionResponse;
import com.actiontec.optim.platform.model.FwVerDistribution;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class FwVerDistributionResponseMapper {

    @Mapping(target="version", source="fwVersion")
    public abstract FwVerDistributionResponse toFwVerDistributionResponse(FwVerDistribution fwVerDistribution);

    public abstract List<FwVerDistributionResponse> toFwVerDistributionResponse(List<FwVerDistribution> fwVerDistributions);

    @AfterMapping
    protected void afterMapping(FwVerDistribution fwVerDistribution, @MappingTarget FwVerDistributionResponse fwVerDistributionResponse){
        FwVerDistributionResponse.DistributionMap distributionMap = new FwVerDistributionResponse.DistributionMap();
        distributionMap.setOnlineUnits(fwVerDistribution.getOnlineUnits());
        distributionMap.setOfflineUnits(fwVerDistribution.getOfflineUnits());
        fwVerDistributionResponse.setValues(distributionMap);
    }
}