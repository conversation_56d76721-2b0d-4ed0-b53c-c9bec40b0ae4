package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.api.v5.model.ConnectionUpstreamResponse;
import com.actiontec.optim.platform.model.DnsServer;
import com.actiontec.optim.platform.model.equipment.NetworkUpstreamInterface;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.ArrayList;
import java.util.List;

@Mapper(componentModel = "spring")
public abstract class ConnectionUpstreamResponseMapper {
    @Mapping(target = "ipv6.enabled", source = "upstreamInterface.ipv6Enabled")
    @Mapping(target = "ipv6.addresses", source = "upstreamInterface.ipv6Addresses")
    @Mapping(target = "ipv6.protocol", source = "upstreamInterface.ipv6Protocol")
    @Mapping(target = "ipv6.defaultGateway", source = "upstreamInterface.ipv6DefaultGateway")
    @Mapping(target = "ipv6.dnsServers", source = "ipv6DnsServers")
    @Mapping(target = "phyType", source = "wan.phyType")
    @Mapping(target = "lastChangeTime", source = "wan.lastChange")
    @Mapping(target = "lastReportTime", source = "lastReportTime")
    public abstract ConnectionUpstreamResponse toConnectionUpstreamResponse(NetworkUpstreamInterface upstreamInterface, List<DnsServer> ipv6DnsServers, ApDetailDto.WanDto wan, long lastReportTime);

    public List<ConnectionUpstreamResponse> toConnectionUpstreamResponse(List<NetworkUpstreamInterface> upstreamInterfaces, List<DnsServer> ipv6DnsServers, ApDetailDto.WanDto wan, long lastReportTime) {
        ArrayList<ConnectionUpstreamResponse> result = new ArrayList<>();
        if (upstreamInterfaces != null) {
            for (NetworkUpstreamInterface upstreamInterface : upstreamInterfaces) {
                result.add(toConnectionUpstreamResponse(upstreamInterface, ipv6DnsServers, wan, lastReportTime));
            }
        } else {
            result.add(toConnectionUpstreamResponse((NetworkUpstreamInterface) null, ipv6DnsServers, wan, lastReportTime));
        }
        return result;
    }

    @AfterMapping
    protected  void afterMapping(ApDetailDto.WanDto wan, @MappingTarget ConnectionUpstreamResponse connectionUpstreamResponse) {
        connectionUpstreamResponse.setStatus(StringUtils.equals(wan.getStatus(), "1")? "Up": "Down");
    }
}
