package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.model.ConfigurationRequest;
import com.actiontec.optim.platform.api.v6.model.ConfigurationResponse;
import com.actiontec.optim.platform.service.ConfigurationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(value = "(V6) Configuration", description = "API's for Configuration Operations", tags = {"Optim - (V6) Configuration"})
@RequestMapping(value = "/actiontec/api/v6/iam/isps/{ispId}/configurations")
public class ConfigurationController {

    @Autowired
    private ConfigurationService configurationService;

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> createConfiguration(
            @ApiParam(value = "ISP id", required = true) @PathVariable String ispId,
            @ApiParam(value = "Create Configuration properties", required = true) @RequestBody ConfigurationRequest configurationRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

            String id = configurationService.createDefaultConfiguration(ispId, configurationRequest);

            Map<String, String> response = new HashMap<>();
            response.put("id", id);

            return response;
    }

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<ConfigurationResponse> getAllConfigurations(
            @ApiParam(value = "ISP id", required = true) @PathVariable String ispId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return configurationService.getAllConfigurations(ispId);
    }

    @RequestMapping(value = "/{configId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public ConfigurationResponse getConfiguration(
            @ApiParam(value = "ISP id", required = true) @PathVariable String ispId,
            @ApiParam(value = "Config id", required = true) @PathVariable String configId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return configurationService.getConfiguration(ispId, configId);
    }

    @RequestMapping(value = "/{configId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateConfiguration(
            @ApiParam(value = "ISP id", required = true) @PathVariable String ispId,
            @ApiParam(value = "Config id", required = true) @PathVariable String configId,
            @ApiParam(value = "Configuration properties", required = true) @RequestBody ConfigurationRequest configurationRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        configurationService.updateConfiguration(ispId, configId, configurationRequest);

    }

    @RequestMapping(value = "/{configId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteConfiguration(
            @ApiParam(value = "ISP id", required = true) @PathVariable String ispId,
            @ApiParam(value = "Config id", required = true) @PathVariable String configId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        configurationService.deleteConfiguration(ispId, configId);
    }
}
