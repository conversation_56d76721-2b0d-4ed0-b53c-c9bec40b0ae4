package com.actiontec.optim.platform.api.v5.model;

public class AlertHistory {
    private String type;
    private String instId;
    private String severity;
    private String reason;
    private long startTime;
    private long endTime;
    private boolean active;
    private Indicators indicators;

    public AlertHistory() {
        this.indicators = new Indicators();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getInstId() {
        return instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean getActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public Indicators getIndicators() {
        return indicators;
    }

    public void setIndicators(Indicators indicators) {
        this.indicators = indicators;
    }

    public  static class Indicators {
        private String radioKey;
        private int rebootTimes;

        public Indicators() {
        }

        public Indicators(String radioKey, int rebootTimes) {
            this.radioKey = radioKey;
            this.rebootTimes = rebootTimes;
        }

        public String getRadioKey() {
            return radioKey;
        }

        public void setRadioKey(String radioKey) {
            this.radioKey = radioKey;
        }

        public int getRebootTimes() {
            return rebootTimes;
        }

        public void setRebootTimes(int rebootTimes) {
            this.rebootTimes = rebootTimes;
        }
    }

}

