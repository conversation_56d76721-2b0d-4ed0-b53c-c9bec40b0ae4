package com.actiontec.optim.platform.api.v5.model;

import io.swagger.annotations.ApiModel;

import java.util.List;

public class VoiceResponse {
    private Boolean enabled;
    private String status;
    private String ipAddress;
    private List<LinesDto> lines;

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public List<LinesDto> getLines() {
        return lines;
    }

    public void setLines(List<LinesDto> lines) {
        this.lines = lines;
    }

    @ApiModel(value="VoiceLinesDto")
    public static class LinesDto {
        private Boolean enabled;
        private int lineNumber;
        private String hookStatus;
        private int callDuration;
        private String clientId;

        public Boolean getEnabled() {
            return enabled;
        }

        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        public int getLineNumber() {
            return lineNumber;
        }

        public void setLineNumber(int lineNumber) {
            this.lineNumber = lineNumber;
        }

        public String getHookStatus() {
            return hookStatus;
        }

        public void setHookStatus(String hookStatus) {
            this.hookStatus = hookStatus;
        }

        public int getCallDuration() {
            return callDuration;
        }

        public void setCallDuration(int callDuration) {
            this.callDuration = callDuration;
        }

        public String getClientId() {
            return clientId;
        }

        public void setClientId(String clientId) {
            this.clientId = clientId;
        }
    }
}
