package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

public class ImageBase {

    private String name;
    private String description;
    private String version;
    private String type;
    private String sequenceVersion;
    private Boolean isIsProduction;
    private String fileName;
    private int fileSize;
    private String location;
    private String fileStatus;
    private String username;
    private String password;
    private ArrayList<String> equipmentTypeIds;
    private String groupId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSequenceVersion() {
        return sequenceVersion;
    }

    public void setSequenceVersion(String sequenceVersion) {
        this.sequenceVersion = sequenceVersion;
    }

    public Boolean getIsProduction() {
        return isIsProduction;
    }

    public void setIsProduction(Boolean isProduction) {
        isIsProduction = isProduction;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public ArrayList<String> getEquipmentTypeIds() {
        return equipmentTypeIds;
    }

    public void setEquipmentTypeIds(ArrayList<String> equipmentTypeIds) {
        this.equipmentTypeIds = equipmentTypeIds;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
}
