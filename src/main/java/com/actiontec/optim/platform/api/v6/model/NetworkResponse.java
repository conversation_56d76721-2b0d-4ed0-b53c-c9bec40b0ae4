package com.actiontec.optim.platform.api.v6.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class NetworkResponse {

    private String id;

    @JsonProperty("isAuto")
    private boolean autoFlag;

    private String autoNetworkId;
    private String name;
    private List<String> equipment;
    private String subscriberId;
    private long lastChangedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(boolean autoFlag) {
        this.autoFlag = autoFlag;
    }

    public String getAutoNetworkId() {
        return autoNetworkId;
    }

    public void setAutoNetworkId(String autoNetworkId) {
        this.autoNetworkId = autoNetworkId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getEquipment() {
        return equipment;
    }

    public void setEquipment(List<String> equipment) {
        this.equipment = equipment;
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public long getLastChangedTime() {
        return lastChangedTime;
    }

    public void setLastChangedTime(long lastChangedTime) {
        this.lastChangedTime = lastChangedTime;
    }
}
