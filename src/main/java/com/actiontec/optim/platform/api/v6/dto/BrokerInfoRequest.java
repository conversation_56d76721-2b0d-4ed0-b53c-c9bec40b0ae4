package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(description = "Broker Info Request")
public class BrokerInfoRequest {

    @ApiModelProperty(value = "Environment name", required = true)
    @JsonProperty("name")
    private String envName;

    @ApiModelProperty(value = "Broker URL", required = true)
    @JsonProperty("brokerAddress")
    private String brokerUrl;

    @ApiModelProperty(value = "Broker port", required = true)
    private Integer port;

    @ApiModelProperty(value = "Username for broker authentication", required = true)
    private String userName;

    @ApiModelProperty(value = "Password for broker authentication", required = true)
    private String password;

    @ApiModelProperty(value = "Is default broker")
    @JsonProperty("isDefault")
    private Boolean isDefault = false;

    public String getEnvName() {
        return envName;
    }

    public void setEnvName(String envName) {
        this.envName = envName;
    }

    public String getBrokerUrl() {
        return brokerUrl;
    }

    public void setBrokerUrl(String brokerUrl) {
        this.brokerUrl = brokerUrl;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
}