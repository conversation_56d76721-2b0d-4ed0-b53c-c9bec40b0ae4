package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.platform.api.v6.enums.RttySessionOperationType;
import com.incs83.app.entities.RttySession;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.ZoneOffset;

/**
 * Response DTO for extending a remote debug session
 */
@ApiModel(description = "Remote Debug Session Extend Response")
public class RttySessionExtendResponse {
    private Long expiredAt;

    public Long getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Long expiredAt) {
        this.expiredAt = expiredAt;
    }
}
