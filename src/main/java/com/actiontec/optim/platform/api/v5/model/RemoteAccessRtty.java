package com.actiontec.optim.platform.api.v5.model;

import com.fasterxml.jackson.annotation.JsonAlias;

import java.io.Serializable;

public class RemoteAccessRtty  implements Serializable {
    @JsonAlias("enable")
    private boolean enabled;
    private int remainingTime;
    private boolean persistent;
    private String url;
    private int port;
    @JsonAlias("id")
    private String connectionId;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getRemainingTime() {
        return remainingTime;
    }

    public void setRemainingTime(int remainingTime) {
        this.remainingTime = remainingTime;
    }

    public boolean isPersistent() {
        return persistent;
    }

    public void setPersistent(boolean persistent) {
        this.persistent = persistent;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getConnectionId() {
        return connectionId;
    }

    public void setConnectionId(String connectionId) {
        this.connectionId = connectionId;
    }


}
