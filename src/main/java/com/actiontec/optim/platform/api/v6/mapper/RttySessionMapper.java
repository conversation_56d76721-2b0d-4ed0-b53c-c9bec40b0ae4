package com.actiontec.optim.platform.api.v6.mapper;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.incs83.app.entities.RttyServerConfig;
import com.incs83.app.entities.RttySession;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.LocalDateTime;
import java.time.ZoneOffset;

@Mapper(componentModel = "spring")
public interface RttySessionMapper {
    @Mapping(source = "rttySession.createdAt", target = "createdAt", qualifiedByName = "toTimestamp")
    @Mapping(source = "rttySession.expiredAt", target = "expiredAt", qualifiedByName = "toTimestamp")
    @Mapping(source = "userName", target = "username")
    @Mapping(source = "password", target = "password")
    RttySessionInitResponse toRttySessionInitResponse(RttySession rttySession, String userName, String password);

    @Mapping(target = "httpUrl", expression = "java(hasHttpPermission ? rttySession.getHttpUrl() : null)")
    @Mapping(target = "sshUrl", expression = "java(hasSshPermission ? rttySession.getSshUrl() : null)")
    RttySessionJoinResponse toRttySessionJoinResponse(RttySession rttySession, boolean hasHttpPermission, boolean hasSshPermission);

    @Mapping(source = "expiredAt", target = "expiredAt", qualifiedByName = "toTimestamp")
    RttySessionExtendResponse toRttySessionExtendResponse(RttySession entity);

    @Mapping(source = "rttySession.equipmentId", target = "id")
    @Mapping(source = "rttySession.token", target = "token")
    @Mapping(source = "rttyServerConfig.host", target = "url")
    @Mapping(source = "rttyServerConfig.port", target = "port")
    @Mapping(source = "userName", target = "username")
    @Mapping(source = "password", target = "password")
    @Mapping(source = "durationHours", target = "duration", qualifiedByName = "hoursToSeconds")
    @Mapping(source = "maxConnections", target = "maxConnections")
    RttyRpcInitSessionRequest toRttyRpcInitSessionRequest(
            RttySession rttySession,
            String userName,
            String password,
            RttyServerConfig rttyServerConfig,
            Integer durationHours,
            Integer maxConnections
    );

    @Mapping(source = "extendHours", target = "additionalDuration", qualifiedByName = "hoursToSeconds")
    RttyRpcExtendSessionRequest toRttyRpcExtendSessionRequest(Integer extendHours);

    @Named("hoursToSeconds")
    static Integer hoursToSeconds(Integer hours) {
        return hours != null ? hours * 3600 : null;
    }

    @Named("toTimestamp")
    static Long toTimestamp(LocalDateTime time) {
        return time != null ? time.toInstant(ZoneOffset.UTC).toEpochMilli() : null;
    }
}
