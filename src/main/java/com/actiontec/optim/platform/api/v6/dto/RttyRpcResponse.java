package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Response DTO for RTTY RPC operations
 */
@ApiModel(description = "RTTY RPC Response")
@JsonIgnoreProperties(ignoreUnknown = true)
public class RttyRpcResponse {
    @ApiModelProperty(value = "Session ID")
    private String id;

    @ApiModelProperty(value = "RTTY server URL")
    private String url;

    @ApiModelProperty(value = "RTTY server port")
    private int port;

    @ApiModelProperty(value = "Total duration of RTTY session in seconds")
    private Long duration;

    @ApiModelProperty(value = "Start time in seconds")
    @JsonProperty("creationTime")
    private Long createdAt;

    @ApiModelProperty(value = "End time in seconds")
    @JsonProperty("endTime")
    private Long expiredAt;

    @JsonProperty("username")
    private String userName;

    @ApiModelProperty(value = "Maximum allowed connections")
    private Integer maxConnections;

    public RttyRpcResponse() {
    }

    public RttyRpcResponse(String id, String url, int port, Long createdAt, Long expiredAt, Long duration, String userName, Integer maxConnections) {
        this.id = id;
        this.url = url;
        this.port = port;
        this.createdAt = createdAt;
        this.expiredAt = expiredAt;
        this.duration = duration;
        this.userName = userName;
        this.maxConnections = maxConnections;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Long expiredAt) {
        this.expiredAt = expiredAt;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getMaxConnections() {
        return maxConnections;
    }

    public void setMaxConnections(Integer maxConnections) {
        this.maxConnections = maxConnections;
    }
}
