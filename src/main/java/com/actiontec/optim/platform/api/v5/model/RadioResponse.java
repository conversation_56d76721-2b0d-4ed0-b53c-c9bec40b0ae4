package com.actiontec.optim.platform.api.v5.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class RadioResponse extends  RadioRequest{
    private String id;
    private String status;
    private String radioKey;

    private boolean isBackhaul;
    private int devicesAssociated;
    private long lastReportTime;
    private RadioStatResponse stats;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRadioKey() {
        return radioKey;
    }

    public void setRadioKey(String radioKey) {
        this.radioKey = radioKey;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @JsonProperty("isBackhaul")
    public boolean isBackhaul() {
        return isBackhaul;
    }

    public void setBackhaul(boolean backhaul) {
        isBackhaul = backhaul;
    }

    public int getDevicesAssociated() {
        return devicesAssociated;
    }

    public void setDevicesAssociated(int devicesAssociated) {
        this.devicesAssociated = devicesAssociated;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public RadioStatResponse getStats() {
        return stats;
    }

    public void setStats(RadioStatResponse stats) {
        this.stats = stats;
    }

    public static class RadioStatResponse {
        private long bytesSent;
        private long bytesReceived;
        private double avgBytesSent;
        private double avgBytesReceived;

        public long getBytesSent() {
            return bytesSent;
        }

        public void setBytesSent(long bytesSent) {
            this.bytesSent = bytesSent;
        }

        public long getBytesReceived() {
            return bytesReceived;
        }

        public void setBytesReceived(long bytesReceived) {
            this.bytesReceived = bytesReceived;
        }

        public double getAvgBytesSent() {
            return avgBytesSent;
        }

        public void setAvgBytesSent(double avgBytesSent) {
            this.avgBytesSent = avgBytesSent;
        }

        public double getAvgBytesReceived() {
            return avgBytesReceived;
        }

        public void setAvgBytesReceived(double avgBytesReceived) {
            this.avgBytesReceived = avgBytesReceived;
        }
    }

}
