package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.mapper.SmmAggregationMapper;
import com.actiontec.optim.platform.api.v5.model.SmmAppStatsResponse;
import com.actiontec.optim.platform.model.SmmAppStats;
import com.actiontec.optim.platform.service.SmmAggregationService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Smm;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v5/smm/aggregation")
public class SmmAggregationController {

    @Autowired
    private SmmAggregationService smmAggregationService;

    @Autowired
    private SmmAggregationMapper smmAggregationMapper;

    @Autowired
    private AuditService auditService;

    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    @RequestMapping(value = "/serviceDistribution", method = RequestMethod.GET, produces = "application/json")
    public List<SmmAppStatsResponse> getSmmAppDistribution(
            @ApiParam(value = "The scope of aggregated data") @RequestParam(name = "scope", defaultValue = "cluster") String scope,
            @ApiParam(value = "Unique key to identify a scope") @RequestParam(name = "scopeId", defaultValue = "0") String scopeId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SmmAppStatsResponse> smmAppStatsResponseList = new ArrayList<>();

        List<SmmAppStats> smmAppStatsList = smmAggregationService.getSmmAppStats(scope, scopeId);
        for(SmmAppStats smmAppStats : smmAppStatsList) {
            smmAppStatsResponseList.add(smmAggregationMapper.toSmmAppStatsResponse(smmAppStats));
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM resource", null, "200", smmAppStatsResponseList, httpServletRequest);
        return smmAppStatsResponseList;
    }
}
