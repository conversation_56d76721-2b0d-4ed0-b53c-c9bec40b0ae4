package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

public class BatchUploadActionResponse {
    @JsonProperty("id")
    private String batchUploadLogId;
    private String fileEntry;

    public String getBatchUploadLogId() {
        return batchUploadLogId;
    }

    public void setBatchUploadLogId(String batchUploadLogId) {
        this.batchUploadLogId = batchUploadLogId;
    }

    public String getFileEntry() {
        return fileEntry;
    }

    public void setFileEntry(String fileEntry) {
        this.fileEntry = fileEntry;
    }
}
