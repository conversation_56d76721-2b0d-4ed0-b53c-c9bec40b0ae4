package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class SmmAppBase {

    private String serviceId;
    private String version;
    private String description;
    private List<Dependency> dependencies;
    private String fileName;
    private int fileSize;

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<Dependency> getDependencies() {
        return dependencies;
    }

    public void setDependencies(List<Dependency> dependencies) {
        this.dependencies = dependencies;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public static class Dependency {

        private String equipmentTypeId;
        private String fwVersion;

        public String getEquipmentTypeId() {
            return equipmentTypeId;
        }

        public void setEquipmentTypeId(String equipmentTypeId) {
            this.equipmentTypeId = equipmentTypeId;
        }

        public String getFwVersion() {
            return fwVersion;
        }

        public void setFwVersion(String fwVersion) {
            this.fwVersion = fwVersion;
        }
    }
}
