package com.actiontec.optim.platform.api.v6.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class NCSSubscriberDTO {
    private String id;
    @NotNull(message = "First name can not be null")
    @NotEmpty(message = "First name can not be empty")
    private String firstName;
    @NotNull(message = "Last name can not be null")
    @NotEmpty(message = "Last name can not be empty")
    private String lastName;
    private String ispId;
    private String userId;

    public NCSSubscriberDTO() {

    }

    public NCSSubscriberDTO(Object[] object, String userId) {
        this.id = String.valueOf(object[0]);
        this.firstName = String.valueOf(object[1]);
        this.lastName = String.valueOf(object[2]);
        this.ispId = String.valueOf(object[3]);
        this.userId = userId;
    }
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }
}
