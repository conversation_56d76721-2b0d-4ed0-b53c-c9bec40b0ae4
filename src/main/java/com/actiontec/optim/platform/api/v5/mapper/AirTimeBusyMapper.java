package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.AirTimeBusyDistributionResponse;
import com.actiontec.optim.platform.api.v5.model.AirTimeBusyResponse;
import com.actiontec.optim.platform.model.AirTimeBusy;
import com.actiontec.optim.platform.model.AirTimeBusyDistribution;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface AirTimeBusyMapper {

    public List<AirTimeBusyResponse> toAirTimeBusyResponse(List<AirTimeBusy> airTimeBusyList);
    public List<AirTimeBusyDistributionResponse> toAirTimeBusyDistributionResponse(List<AirTimeBusyDistribution> airTimeBusyDistributionList);
}
