package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

@ApiModel(description = "Broker Info query parameters")
public class BrokerInfoQueryDTO extends PaginationRequest {
    
    @ApiModelProperty(value = "Filter of broker name, support fuzzy search")
    @Column(name = "env_name")
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
} 