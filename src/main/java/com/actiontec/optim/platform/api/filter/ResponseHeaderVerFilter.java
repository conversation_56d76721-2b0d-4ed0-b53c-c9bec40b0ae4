package com.actiontec.optim.platform.api.filter;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class ResponseHeaderVerFilter extends OncePerRequestFilter {
    @Override
    protected void doFilterInternal(HttpServletRequest httpServletRequest,
                                    HttpServletResponse httpServletResponse,
                                    FilterChain filterChain)
            throws ServletException, IOException {
        String uri = httpServletRequest.getRequestURI();
        if (uri.contains("/actiontec/api/v5/")) {
            httpServletResponse.setIntHeader("X-API-Version", 5);
        }
        filterChain.doFilter(httpServletRequest, httpServletResponse);
    }
}
