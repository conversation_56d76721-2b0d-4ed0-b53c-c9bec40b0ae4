package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.EquipmentTrackingResponse;
import com.actiontec.optim.platform.api.v5.model.EquipmentTransactionResponse;
import com.actiontec.optim.platform.model.EquipmentTrackingRecord;
import com.actiontec.optim.platform.model.EquipmentTransaction;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface EquipmentTrackingMapper {

    public List<EquipmentTrackingResponse> toEquipmentTrackingResponse(List<EquipmentTrackingRecord> equipmentTrackingRecordList);
    public List<EquipmentTransactionResponse> toEquipmentTransactionResponse(List<EquipmentTransaction> equipmentTransactionList);
}
