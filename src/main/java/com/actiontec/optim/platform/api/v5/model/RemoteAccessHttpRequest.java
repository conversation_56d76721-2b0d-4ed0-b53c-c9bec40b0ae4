package com.actiontec.optim.platform.api.v5.model;

public class RemoteAccessHttpRequest {
    private boolean enabled;
    private int duration = -1;
    private int port;

    public boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }
}
