package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.OpticalModuleResponse;
import com.actiontec.optim.platform.api.v5.model.OpticalResponse;
import com.actiontec.optim.platform.api.v5.model.OpticalStatsResponse;
import com.actiontec.optim.platform.model.NetworkOptical;
import com.actiontec.optim.platform.model.NetworkOpticalModule;
import com.actiontec.optim.platform.model.NetworkOpticalStats;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OpticalResponseMapper {

    public List<OpticalResponse> toOpticalResponse(List<NetworkOptical> networkOpticalList);
    public OpticalModuleResponse toOpticalModuleResponse(NetworkOpticalModule networkOpticalModule);
    public List<OpticalStatsResponse> toOpticalStatsResponse(List<NetworkOpticalStats> networkOpticalStatsList);
}
