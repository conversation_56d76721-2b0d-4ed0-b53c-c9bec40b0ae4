package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.service.NCSNetworkService;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;


@RestController
@Api(value = "(V6) Network", description = "API's for Network Operations", tags = {"Optim - (V6) Network"})
@RequestMapping(value = "/actiontec/api/v6/iam/networks")
public class NCSNetworkController {
    @Autowired
    NCSNetworkService ncsNetworkService;

    @Autowired
    ManageCommonService manageCommonService;

    // IAM network api
    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<NCSNetworkDTO> getAllNetworks(
            @ModelAttribute NCSNetworkQueryDTO queryDTO) throws Exception  {
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if(CommonUtils.isGroupAdmin()) {
            manageCommonService.checkIspIdForGroupAdmin(queryDTO.getIspId());

            // group admin may not bring ispId parameter, default set query ispId by login user ispId.
            if (StringUtils.isEmpty(queryDTO.getIspId())) {
                String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
                String userIspId = manageCommonService.getIspByGroupId(userGroupId);
                queryDTO.setIspId(userIspId);
            }
        }

        ncsNetworkService.checkQueryParameter(queryDTO);
        return ncsNetworkService.getAllNetworks(queryDTO);
    }

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> createNCSNetwork(
            @ApiParam(value = "Create NCSNetwork properties", required = true) @RequestBody NCSNetworkRequest networkRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> result = ncsNetworkService.createNetwork(networkRequest);

        return result;
    }

    @RequestMapping(value = "/{networkId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public NCSNetworkDetailDTO getNCSNetwork(@ApiParam(value = "Network Id", required = true)
                                    @PathVariable(name = "networkId") String networkId) throws Exception {
        return ncsNetworkService.getNetworkDetailDTO(networkId);
    }

    @RequestMapping(value = "/{networkId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateNCSNetwork(
            @PathVariable(name = "networkId") String networkId,
            @ApiParam(value = "Assign equipment to Network properties", required = true) @RequestBody NCSNetworkRequest networkRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        ncsNetworkService.updateNetwork(networkId, networkRequest);
    }

    @RequestMapping(value = "/{networkId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteNetwork(
            @ApiParam(value = "Network Id", required = true)
            @PathVariable String networkId) throws Exception {
        ncsNetworkService.deleteNetwork(networkId);
    }
}
