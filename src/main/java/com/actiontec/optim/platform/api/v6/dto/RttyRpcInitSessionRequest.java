package com.actiontec.optim.platform.api.v6.dto;

import com.incs83.app.entities.RttyServerConfig;
import com.incs83.app.entities.RttySession;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Response DTO for RTTY RPC operations
 */
@ApiModel(description = "RTTY RPC Request")
public class RttyRpcInitSessionRequest {
    @ApiModelProperty(value = "Session ID")
    private String id;

    @ApiModelProperty(value = "Session token")
    private String token;

    @ApiModelProperty(value = "RTTY server URL")
    private String url;

    @ApiModelProperty(value = "RTTY server port")
    private int port;

    @ApiModelProperty(value = "The length of time that RTTY client last, Unit is second.")
    private Integer duration;

    @ApiModelProperty(value = "Username for authentication")
    private String username;

    @ApiModelProperty(value = "Password for authentication")
    private String password;

    @ApiModelProperty(value = "Maximum allowed connections")
    private Integer maxConnections;

    public RttyRpcInitSessionRequest() {
    }

    public RttyRpcInitSessionRequest(String id, String token, String url, int port, Integer duration, String username, String password, Integer maxConnections) {
        this.id = id;
        this.token = token;
        this.url = url;
        this.port = port;
        this.duration = duration;
        this.username = username;
        this.password = password;
        this.maxConnections = maxConnections;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getMaxConnections() {
        return maxConnections;
    }

    public void setMaxConnections(Integer maxConnections) {
        this.maxConnections = maxConnections;
    }
}
