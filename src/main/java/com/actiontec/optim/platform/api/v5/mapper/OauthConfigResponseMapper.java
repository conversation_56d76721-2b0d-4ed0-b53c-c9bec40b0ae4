package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.OauthConfigRequest;
import com.actiontec.optim.platform.api.v5.model.OauthConfigResponse;
import com.actiontec.optim.platform.api.v5.model.OauthResponse;
import com.actiontec.optim.platform.model.OauthConfig;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface OauthConfigResponseMapper {

    public OauthConfig toOauthConfig(OauthConfigRequest oauthConfigRequest);
    public OauthConfigResponse toOauthConfigResponse(OauthConfig oauthConfig);
    public List<OauthConfigResponse> toOauthConfigResponses(List<OauthConfig> oauthConfigs);
    public OauthResponse toOauthResponse(OauthConfig oauthConfig);
}
