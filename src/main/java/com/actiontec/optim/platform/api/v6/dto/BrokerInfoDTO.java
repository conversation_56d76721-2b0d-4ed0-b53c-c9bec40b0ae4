package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;

public class BrokerInfoDTO {
    private String id;
    @JsonProperty("name")
    private String envName;
    @JsonProperty("brokerAddress")
    private String brokerUrl;
    private Integer port;
    private String userName;

    public BrokerInfoDTO() {

    }

    public BrokerInfoDTO(Object[] object) {
        this.id = CustomStringUtils.toStringOrNull(object[0]);
        this.envName = CustomStringUtils.toStringOrNull(object[1]);
        this.brokerUrl = CustomStringUtils.toStringOrNull(object[2]);
        this.port = object[3] != null ? Integer.parseInt(CustomStringUtils.toStringOrNull(object[3])) : null;
        this.userName = CustomStringUtils.toStringOrNull(object[4]);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEnvName() {
        return envName;
    }

    public void setEnvName(String envName) {
        this.envName = envName;
    }

    public String getBrokerUrl() {
        return brokerUrl;
    }

    public void setBrokerUrl(String brokerUrl) {
        this.brokerUrl = brokerUrl;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
