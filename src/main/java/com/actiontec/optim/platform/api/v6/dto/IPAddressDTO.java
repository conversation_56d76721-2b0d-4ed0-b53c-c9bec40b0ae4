package com.actiontec.optim.platform.api.v6.dto;

public class IPAddressDTO {
    private int version;
    private String address;
    private String type;
    private String lifetimeState;
    private long lifetime;

    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getLifetimeState() {
        return lifetimeState;
    }

    public void setLifetimeState(String lifetimeState) {
        this.lifetimeState = lifetimeState;
    }

    public long getLifetime() {
        return lifetime;
    }

    public void setLifetime(long lifetime) {
        this.lifetime = lifetime;
    }
}
