package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.AuditLog;
import com.actiontec.optim.platform.api.v5.mapper.SsidResponseMapper;
import com.actiontec.optim.platform.api.v5.model.SsidRequest;
import com.actiontec.optim.platform.api.v5.model.SsidResponse;
import com.actiontec.optim.platform.model.NetworkSsid;
import com.actiontec.optim.platform.service.NetworkSsidService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}/ssids")
public class SsidController {

    @Autowired
    private NetworkSsidService networkSsidService;

    @Autowired
    private SsidResponseMapper ssidResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<SsidResponse> get(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "id", required = true) @PathVariable(name = "id")
                    String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkSsid networkSsid = networkSsidService.findByStnEquipmentIdAndVirtualkey(stn, equipmentId, id);
        List<NetworkSsid> networkSsids = new ArrayList<>();
        networkSsids.add(networkSsid);
        List<SsidResponse> ssidResponses = ssidResponseMapper.toSsidResponse(networkSsids);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", null, "200", ssidResponses, httpServletRequest);
        return ssidResponses;
    }

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<SsidResponse> getAll(
            @ApiParam(value = "STN", required = true)
            @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "alignment", required = false)
            @RequestParam(required = false, name = "alignment", defaultValue = "true")
                    boolean alignment,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkSsid> networkSsids = networkSsidService.findByStnAndEquipmentId(stn, equipmentId, alignment);
        List<SsidResponse> ssidResponses = ssidResponseMapper.toSsidResponse(networkSsids);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", null, "200", ssidResponses, httpServletRequest);
        return ssidResponses;
    }

    @RequestMapping(value = "/{ssidId}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void put(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "ssidId", required = true) @PathVariable(name = "ssidId")
                    String ssidId,
            @ApiParam(value = "Put radio properties", required = true) @RequestBody SsidRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        networkSsidService.putSsidByEquipimentId(stn, equipmentId, ssidId, request);
        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", request, "204", null, httpServletRequest);
    }
}
