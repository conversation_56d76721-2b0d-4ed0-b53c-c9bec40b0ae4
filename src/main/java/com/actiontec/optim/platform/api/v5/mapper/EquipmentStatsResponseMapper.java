package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.EquipmentStatsResponse;
import com.actiontec.optim.platform.model.EquipmentStats;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface EquipmentStatsResponseMapper {

    public List<EquipmentStatsResponse> toEquipmentStatsResponse(List<EquipmentStats> equipmentStatsList);
}
