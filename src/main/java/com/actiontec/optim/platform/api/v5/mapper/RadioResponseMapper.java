package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.RadioResponse;
import com.actiontec.optim.platform.model.NetworkRadio;
import com.actiontec.optim.platform.model.NetworkRadioVirtualKey;
import com.actiontec.optim.platform.service.SimpleAesEcbCryptoService;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring")
public abstract class RadioResponseMapper {

    @Autowired
    private SimpleAesEcbCryptoService cryptoService;

    @Mapping(target = "channelBandwidth", source = "channelWidth")
    public abstract RadioResponse toRadioResponse(NetworkRadio networkRadio);

    public abstract List<RadioResponse> toRadioResponse(List<NetworkRadio> networkRadios);

    @AfterMapping
    protected void afterMapping(NetworkRadio networkRadio, @MappingTarget RadioResponse radioResponse) {
        Optional.ofNullable(networkRadio.getRadioKey()).ifPresent(s->radioResponse.setRadioKey(s.getRadioKey()));

        RadioResponse.RadioStatResponse radioStatResponse = new RadioResponse.RadioStatResponse();
        radioStatResponse.setBytesSent(networkRadio.getBytesSent());
        radioStatResponse.setBytesReceived(networkRadio.getBytesReceived());
        radioStatResponse.setAvgBytesSent(networkRadio.getAvgBytesSent());
        radioStatResponse.setAvgBytesReceived(networkRadio.getAvgBytesReceived());
        radioResponse.setStats(radioStatResponse);

        NetworkRadioVirtualKey networkRadioVirtualKey = new NetworkRadioVirtualKey(networkRadio.getSerialNumber(), networkRadio.getRadioKey());
        String id = networkRadioVirtualKey.getEncryptedString(cryptoService);
        radioResponse.setId(id);
    }
}