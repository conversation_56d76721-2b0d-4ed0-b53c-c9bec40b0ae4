package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.OpticalResponseMapper;
import com.actiontec.optim.platform.api.v5.model.OpticalModuleResponse;
import com.actiontec.optim.platform.api.v5.model.OpticalResponse;
import com.actiontec.optim.platform.api.v5.model.OpticalStatsResponse;
import com.actiontec.optim.platform.model.NetworkOptical;
import com.actiontec.optim.platform.model.NetworkOpticalModule;
import com.actiontec.optim.platform.model.NetworkOpticalStats;
import com.actiontec.optim.platform.service.OpticalService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}")
public class OpticalController {

    @Autowired
    private OpticalService opticalService;

    @Autowired
    private OpticalResponseMapper opticalResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(value = "/opticalPorts", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<OpticalResponse> getOpticalPorts(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkOptical> networkOpticalList = opticalService.findOpticalByEquipmentId(equipmentId);
        List<OpticalResponse> opticalResponseList = opticalResponseMapper.toOpticalResponse(networkOpticalList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Optical Resource", null, "200", opticalResponseList, httpServletRequest);
        return opticalResponseList;
    }

    @RequestMapping(value = "/opticalPorts/{opticsId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<OpticalResponse> getOpticalPort(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "opticsId", required = true) @PathVariable(name = "opticsId") String opticsId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkOptical> networkOpticalList = opticalService.findOpticalByEquipmentIdAndOpticsId(equipmentId, opticsId);
        List<OpticalResponse> opticalResponseList = opticalResponseMapper.toOpticalResponse(networkOpticalList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Optical Resource", null, "200", opticalResponseList, httpServletRequest);
        return opticalResponseList;
    }

    @RequestMapping(value = "/opticalPorts/{opticsId}/module", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public OpticalModuleResponse getOpticalModule(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "opticsId", required = true) @PathVariable(name = "opticsId") String opticsId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkOpticalModule networkOpticalModule = opticalService.findOpticalModuleByEquipmentId(equipmentId);
        OpticalModuleResponse opticalModuleResponse = opticalResponseMapper.toOpticalModuleResponse(networkOpticalModule);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Optical Resource", null, "200", opticalModuleResponse, httpServletRequest);
        return opticalModuleResponse;
    }

    @RequestMapping(value = "/opticalPorts/{opticsId}/module/series", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<OpticalStatsResponse> getOpticalStats(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "opticsId", required = true) @PathVariable(name = "opticsId") String opticsId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 3600 seconds")
            @RequestParam(required = false, name = "duration", defaultValue = "3600") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkOpticalStats> networkOpticalStatsList = opticalService.findOpticalStatsByEquipmentIdAndDuration(equipmentId, duration);
        List<OpticalStatsResponse> opticalStatsResponseList = opticalResponseMapper.toOpticalStatsResponse(networkOpticalStatsList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Optical Resource", null, "200", opticalStatsResponseList, httpServletRequest);
        return opticalStatsResponseList;
    }

}
