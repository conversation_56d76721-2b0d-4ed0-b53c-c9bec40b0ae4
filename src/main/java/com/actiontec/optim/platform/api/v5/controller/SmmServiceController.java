package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.model.SmmServiceRequest;
import com.actiontec.optim.platform.api.v5.model.SmmServiceVo;
import com.actiontec.optim.platform.service.SmmServicesService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.Smm;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/smm/services")
public class SmmServiceController {
    @Autowired
    private AuditService auditService;
    @Autowired
    private  SmmServicesService smmServicesService;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmServiceVo> getAllServices(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<SmmServiceVo> smmServiceVoList = smmServicesService.getSmmServices();
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Service Resource", null, "200", smmServiceVoList, httpServletRequest);

        return  smmServiceVoList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> createService(
            @ApiParam(value = "Service Properties", required = true) @RequestBody SmmServiceRequest serviceRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        String id = smmServicesService.createSmmService(serviceRequest);
        Map<String, String> response = new HashMap<>();
        if (id != null) {
            response.put("id", id);
        }
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Service Resource", serviceRequest, "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/{serviceId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmServiceVo> getService(
            @ApiParam(value = "serviceId", required = true) @PathVariable(name = "serviceId")  String serviceId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<SmmServiceVo> smmServiceVoList =  new ArrayList<>();
        SmmServiceVo smmServiceVo = smmServicesService.getSmmSeviceById(serviceId);

        if (smmServiceVo != null) {
            smmServiceVoList.add(smmServiceVo);
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Service Resource", null, "200", smmServiceVoList, httpServletRequest);
        return smmServiceVoList;
    }

    @RequestMapping(value = "/{serviceId}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Smm.class)
    public void updateService(
            @ApiParam(value = "serviceId", required = true) @PathVariable(name = "serviceId")  String serviceId,
            @ApiParam(value = "Service Properties", required = true) @RequestBody SmmServiceRequest serviceRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        smmServicesService.updateSmmService(serviceId, serviceRequest);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Service Resource", serviceRequest, "204", null, httpServletRequest);
    }

    @RequestMapping(value = "/{serviceId}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Smm.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeService(
            @ApiParam(value = "serviceId", required = true) @PathVariable(name = "serviceId")  String serviceId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        smmServicesService.deleteSmmServiceById(serviceId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "SMM Service Resource", null, "204", null, httpServletRequest);
    }
}
