package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConnectionDTO {
    private String type;
    private long uptime;
    private WirelessDTO wireless;
    private EthernetDTO ethernet;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getUptime() {
        return uptime;
    }

    public void setUptime(long uptime) {
        this.uptime = uptime;
    }

    public WirelessDTO getWireless() {
        return wireless;
    }

    public void setWireless(WirelessDTO wireless) {
        this.wireless = wireless;
    }

    public EthernetDTO getEthernet() {
        return ethernet;
    }

    public void setEthernet(EthernetDTO ethernet) {
        this.ethernet = ethernet;
    }
}
