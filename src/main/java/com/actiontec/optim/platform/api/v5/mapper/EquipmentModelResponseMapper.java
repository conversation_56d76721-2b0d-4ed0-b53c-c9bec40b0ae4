package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.ModelRequest;
import com.actiontec.optim.platform.api.v5.model.ModelResponse;
import com.actiontec.optim.platform.model.EquipmentModel;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface EquipmentModelResponseMapper {

    public List<ModelResponse> toModelResponseList(List<EquipmentModel> equipmentModels);
    public ModelResponse toModelResponse(EquipmentModel equipmentModel);
    public EquipmentModel toModel(ModelRequest modelRequest);
}
