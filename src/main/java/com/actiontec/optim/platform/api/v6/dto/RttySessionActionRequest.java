package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.platform.api.v6.enums.RttySessionOperationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Request DTO for RTTY session actions (join or extend)
 */
@ApiModel(description = "Remote Debug Session Action Request")
public class RttySessionActionRequest {

    @ApiModelProperty(value = "Operation type (Join or Extend)", required = true, example = "Join")
    private RttySessionOperationType type;

    @ApiModelProperty(value = "Session token for authentication", required = true)
    private String token;

    @ApiModelProperty(value = "Hours to extend the session (optional for Extend operation)", required = false)
    private Integer extendHours;

    public RttySessionActionRequest() {
    }

    public RttySessionOperationType getType() {
        return type;
    }

    public void setType(RttySessionOperationType type) {
        this.type = type;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getExtendHours() {
        return extendHours;
    }

    public void setExtendHours(Integer extendHours) {
        this.extendHours = extendHours;
    }
}
