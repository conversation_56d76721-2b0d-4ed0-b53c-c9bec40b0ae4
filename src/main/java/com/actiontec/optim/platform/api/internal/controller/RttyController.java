package com.actiontec.optim.platform.api.internal.controller;

import com.actiontec.optim.platform.service.RttyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/internal/rtty")
public class RttyController {

    @Autowired
    private RttyService rttyService;

    @PostMapping(value = "/log/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<?> postAction(
            @RequestParam("deviceId") String deviceId,
            @RequestParam("sessionId") String sessionId,
            @RequestParam("fileName") String fileName,
            @RequestParam("fileSize") int fileSize,
            @RequestParam("file") MultipartFile file,
            @RequestHeader(value = "X-INTERNAL-NODE", required = false) String internalNode) throws Exception {

        if (!"rttys".equals(internalNode)) {
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("error", "unauth");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(errorResponse);
        }

        rttyService.createLog(deviceId, sessionId, fileName, fileSize, file);

        return ResponseEntity.status(HttpStatus.CREATED).build();
    }

}
