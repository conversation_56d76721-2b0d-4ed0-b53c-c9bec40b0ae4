package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class ConnectionDownstreamResponse {
    private String id;
    private long lastReportTime;
    private Ipv6Response ipv6;

    public static class Ipv6Response {
        private boolean enabled;
        private List<Ipv6AddressResponse> addresses;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public List<Ipv6AddressResponse> getAddresses() {
            return addresses;
        }

        public void setAddresses(List<Ipv6AddressResponse> addresses) {
            this.addresses = addresses;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public Ipv6Response getIpv6() {
        return ipv6;
    }

    public void setIpv6(Ipv6Response ipv6) {
        this.ipv6 = ipv6;
    }
}
