
package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

@ApiModel(description = "Subscriber query parameters")
public class NCSSubscriberQueryDTO extends PaginationRequest {

    // compartment table ispId column original name
    @ApiModelProperty(value = "Specified ISP ID")
    private String ispId;

    @ApiModelProperty(value = "Filter of subscriber first name, support fuzzy search")
    private String firstName;

    @ApiModelProperty(value = "Filter of subscriber last name, support fuzzy search")
    private String lastName;

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
}
