package com.actiontec.optim.platform.api.v6.dto;

import java.sql.Timestamp;

public class BatchEquipmentUploadFileDetailDTO {
    private String id;
    private String ispId;
    private String uploadLogId;
    private String fileId;
    private String fileName;
    private String type;
    private String url;
    private long totalRecords;
    private long successRecords;
    private long failedRecords;
    private Timestamp createdAt;
    private Timestamp updatedAt;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getUploadLogId() {
        return uploadLogId;
    }

    public void setUploadLogId(String uploadLogId) {
        this.uploadLogId = uploadLogId;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(long totalRecords) {
        this.totalRecords = totalRecords;
    }

    public long getSuccessRecords() {
        return successRecords;
    }

    public void setSuccessRecords(long successRecords) {
        this.successRecords = successRecords;
    }

    public long getFailedRecords() {
        return failedRecords;
    }

    public void setFailedRecords(long failedRecords) {
        this.failedRecords = failedRecords;
    }

    public Timestamp getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public Timestamp getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Timestamp updatedAt) {
        this.updatedAt = updatedAt;
    }
}