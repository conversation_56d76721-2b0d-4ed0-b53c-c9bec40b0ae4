package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.mapper.MeResponseMapper;
import com.actiontec.optim.platform.api.v5.model.MeRequest;
import com.actiontec.optim.platform.api.v5.model.MeResponse;
import com.actiontec.optim.platform.model.Me;
import com.actiontec.optim.platform.service.MeService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping(value = "/actiontec/api/v5/me")
public class MeController {

    @Autowired
    private MeService meService;

    @Autowired
    private MeResponseMapper meResponseMapper;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public MeResponse getMeInfo(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Me me = meService.getMeInfo();
        MeResponse meResponse = meResponseMapper.toMeResponse(me);
        return meResponse;
    }

    @RequestMapping(method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    public void updateMeInfo(
            @ApiParam(value = "User properties", required = true) @RequestBody MeRequest meRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        meService.putMeInfo(meRequest);
    }

}
