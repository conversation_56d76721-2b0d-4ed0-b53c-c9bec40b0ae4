package com.actiontec.optim.platform.api.v6.model;

public class Radio {
    private String band;
    private String bandwidth;
    private boolean dfsEnabled;
    private ActiveAcs activeAcs;

    public String getBand() {
        return band;
    }

    public void setBand(String band) {
        this.band = band;
    }

    public String getBandwidth() {
        return bandwidth;
    }

    public void setBandwidth(String bandwidth) {
        this.bandwidth = bandwidth;
    }

    public boolean isDfsEnabled() {
        return dfsEnabled;
    }

    public void setDfsEnabled(boolean dfsEnabled) {
        this.dfsEnabled = dfsEnabled;
    }

    public ActiveAcs getActiveAcs() {
        return activeAcs;
    }

    public void setActiveAcs(ActiveAcs activeAcs) {
        this.activeAcs = activeAcs;
    }
}
