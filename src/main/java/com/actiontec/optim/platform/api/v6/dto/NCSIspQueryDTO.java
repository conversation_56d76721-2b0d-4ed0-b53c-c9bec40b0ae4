package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

@ApiModel(description = "Equipment query parameters")
public class NCSIspQueryDTO extends PaginationRequest {

    @ApiModelProperty(value = "Specified ISP ID")
    private String name;

    @ApiModelProperty(value = "Specified network ID")
    private String alias;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
}
