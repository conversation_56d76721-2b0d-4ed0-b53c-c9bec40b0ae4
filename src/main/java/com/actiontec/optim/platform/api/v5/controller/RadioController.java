package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.AuditLog;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.mapper.RadioResponseMapper;
import com.actiontec.optim.platform.api.v5.model.ActionRequest;
import com.actiontec.optim.platform.api.v5.model.RadioRequest;
import com.actiontec.optim.platform.api.v5.model.RadioResponse;
import com.actiontec.optim.platform.model.NetworkRadio;
import com.actiontec.optim.platform.model.NetworkRadioSeries;
import com.actiontec.optim.platform.service.NetworkRadioService;
import com.actiontec.optim.platform.service.SimpleAesEcbCryptoService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.task.DelegatingSecurityContextAsyncTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}/radios")
public class RadioController {
    @Autowired
    private NetworkRadioService networkRadioService;

    @Autowired
    private RadioResponseMapper radioResponseMapper;

    @Autowired
    private SimpleAesEcbCryptoService cryptoService;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<RadioResponse> getAll(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkRadio> networkRadios = networkRadioService.findByStnAndSerial(stn, equipmentId);
        List<RadioResponse> radioResponses = radioResponseMapper.toRadioResponse(networkRadios);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "WiFi Resource", null, "200", radioResponses, httpServletRequest);
        return radioResponses;
    }

    @RequestMapping(value = "/{radioId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public RadioResponse get(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "radioId", required = true) @PathVariable(name = "radioId")
                    String radioId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Optional<NetworkRadio> radioOptional = networkRadioService.findByVirtualKey(stn, equipmentId, radioId);
        if (!radioOptional.isPresent()) {
            throw new OptimApiException(HttpStatus.NOT_FOUND, "");
        }

        RadioResponse radioResponse = radioResponseMapper.toRadioResponse(radioOptional.get());
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "WiFi Resource", null, "200", radioResponse, httpServletRequest);
        return radioResponse;
    }

    @RequestMapping(value = "/{radioId}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    @AuditLog(operation = AuditorConstants.PUT_RADIO)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void put(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "radioId", required = true) @PathVariable(name = "radioId")
                    String radioId,
            @ApiParam(value = "Put radio properties", required = true) @RequestBody RadioRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        networkRadioService.putRadio(stn, equipmentId, radioId, request);
    }

    @RequestMapping(value = "/{radioId}/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    @AuditLog(operation = AuditorConstants.POST_TRIBAND)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void postActions(@ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                                    String stn,
                            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                                    String equipmentId,
                            @ApiParam(value = "radioId", required = true) @PathVariable(name = "radioId")
                                    String radioId,
                            @ApiParam(value = "Actions properties", required = true) @RequestBody ActionRequest actionRequest,
                            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                            @RequestHeader(name = "X-Authorization")
                                    String accessToken,
                            HttpServletRequest httpServletRequest) throws Exception {
        networkRadioService.postAction(stn, equipmentId, radioId, actionRequest.getAction());
    }

    @RequestMapping(value = "/{radioId}/series", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<Map<String, Object>> getSeries(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "radioId", required = true) @PathVariable(name = "radioId") String radioId,
            @ApiParam(value = "Time duration in seconds") @RequestParam(name = "duration", defaultValue = "3600") Long duration,
            @ApiParam(value = "Fields") @RequestParam(name = "fields", defaultValue = "airTimeBusy,channel,bytesSent,bytesReceived,deltaBytesSent,deltaBytesReceived,airTimeBusyDeviceDistribution") String fields,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<Map<String, Object>> result = new ArrayList<>();

        List<String> fieldList = new ArrayList<String>(Arrays.asList(fields.split(",")));
        Map<String, Boolean> fieldMap = new HashMap<String, Boolean>();
        for(String field : fieldList) {
            fieldMap.put(field, true);
        }

        List<NetworkRadioSeries> networkRadioSeriesList = networkRadioService.findSeriesByStnAndSerialAndRadio(stn, equipmentId, radioId, duration);
        for(NetworkRadioSeries networkRadioSeries : networkRadioSeriesList) {

            Map<String, Object> output = new HashMap<>();
            output.put("timestamp", networkRadioSeries.getTimestamp());

            if(fieldMap.get("airTimeBusy") != null) {
                output.put("airTimeBusy", networkRadioSeries.getAirTimeBusy());
            }

            if(fieldMap.get("channel") != null) {
                output.put("channel", networkRadioSeries.getChannel());
            }

            if(fieldMap.get("bytesSent") != null) {
                output.put("bytesSent", networkRadioSeries.getBytesSent());
            }

            if(fieldMap.get("bytesReceived") != null) {
                output.put("bytesReceived", networkRadioSeries.getBytesReceived());
            }

            if(fieldMap.get("deltaBytesSent") != null) {
                output.put("deltaBytesSent", networkRadioSeries.getDeltaBytesSent());
            }

            if(fieldMap.get("deltaBytesReceived") != null) {
                output.put("deltaBytesReceived", networkRadioSeries.getDeltaBytesReceived());
            }

            if(fieldMap.get("airTimeBusyDeviceDistribution") != null) {
                output.put("airTimeBusyDeviceDistribution", networkRadioSeries.getAirTimeBusyDeviceDistribution());
            }

            result.add(output);
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "WiFi Resource", null, "200", result, httpServletRequest);
        return result;
    }
}