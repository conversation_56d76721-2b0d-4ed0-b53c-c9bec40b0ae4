package com.actiontec.optim.platform.api.v5.model;

import java.util.ArrayList;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

public class OauthConfigRequest extends OauthConfigBase {

    @NotBlank(message = "ClientSecret cannot be empty.")
    private String clientSecret;

    @NotEmpty(message = "IspIds cannot be empty.")
    private ArrayList<String> ispIds;

    private String defaultIspId;

    @NotBlank(message = "RoleId cannot be empty.")
    private String roleId;

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public ArrayList<String> getIspIds() {
        return ispIds;
    }

    public void setIspIds(ArrayList<String> ispIds) {
        this.ispIds = ispIds;
    }

    public String getDefaultIspId() {
        return defaultIspId;
    }

    public void setDefaultIspId(String defaultIspId) {
        this.defaultIspId = defaultIspId;
    }

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }
}
