package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.service.UtilsService;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@RequestMapping(value = "/actiontec/api/v5/utils/")
public class utilsController {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ResponseUtil responseUtil;
    @Autowired
    UtilsService utilsService;

    @RequestMapping(value = "decryptId", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?>  decryptId(
            @RequestParam(name = "id") String id,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return  responseUtil.ok(utilsService.getByVirtualKey(id), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
