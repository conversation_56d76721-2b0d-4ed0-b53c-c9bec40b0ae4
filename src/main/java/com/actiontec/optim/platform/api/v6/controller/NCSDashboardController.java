package com.actiontec.optim.platform.api.v6.controller;

import com.incs83.annotation.PreHandle;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.actiontec.optim.platform.service.NCSDashboardService;
import com.actiontec.optim.platform.api.v6.dto.NCSDashboardDTO;

@RestController
@Api(value = "(V6) Network", description = "API's for Dashboard Operations", tags = {"Optim - (V6) Dashboard"})
@RequestMapping(value = "/actiontec/api/v6/aggregation/clusters/")
public class NCSDashboardController {

    @Autowired
    private NCSDashboardService dashboardService;

    @GetMapping(value = "/{clusterId}/counters")
    public NCSDashboardDTO getDashboardStats(@PathVariable String clusterId) throws Exception {
        return dashboardService.getDashboardStats(clusterId);
    }

}
