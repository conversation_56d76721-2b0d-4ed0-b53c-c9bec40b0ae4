package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.platform.api.v6.enums.RttySessionOperationType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * Base response DTO for RTTY session actions
 * @param <T> Type of data contained in the response
 */
@ApiModel(description = "Remote Debug Session Action Response")
public class RttySessionActionResponse<T> {

    @ApiModelProperty(value = "Operation type (Join or Extend)")
    private RttySessionOperationType type;

    @ApiModelProperty(value = "Response data")
    private T data;

    public RttySessionActionResponse() {
    }

    public RttySessionActionResponse(RttySessionOperationType type, T data) {
        this.type = type;
        this.data = data;
    }

    public RttySessionOperationType getType() {
        return type;
    }

    public void setType(RttySessionOperationType type) {
        this.type = type;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
