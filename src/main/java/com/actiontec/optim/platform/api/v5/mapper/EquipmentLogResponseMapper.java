package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.EquipmentLogResponse;
import com.actiontec.optim.platform.model.EquipmentLog;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface EquipmentLogResponseMapper {

    public List<EquipmentLogResponse> toEquipmentLogResponse(List<EquipmentLog> equipmentLogList);
}
