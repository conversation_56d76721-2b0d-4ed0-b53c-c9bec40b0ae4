package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import java.util.List;

@ApiModel(description = "Remote Session Query DTO")
public class RttySessionQueryDTO extends PaginationRequest {

    @ApiModelProperty(value = "List of user IDs who created the sessions")
    @Column(name = "fullName")
    private String creatorName;
    
    @ApiModelProperty(value = "ISP ID")
    @Column(name = "isp_id")
    private String ispId;
    
    @ApiModelProperty(value = "Equipment serial number")
    @Column(name = "serial")
    private String serialNumber;

    private boolean createdByMe;

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public boolean isCreatedByMe() {
        return createdByMe;
    }

    public void setCreatedByMe(boolean createdByMe) {
        this.createdByMe = createdByMe;
    }
}
