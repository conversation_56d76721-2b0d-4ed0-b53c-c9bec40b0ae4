package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.sql.Timestamp;

public class BatchEquipmentUploadLogDTO {
    private String id;
    @JsonIgnore
    private String fileId;
    private String fileEntry;
    private String fileName;
    private String action;
    private String status;
    private Result result;

    @JsonProperty("createdTime")
    private Long createdAt;
    
    @JsonProperty("lastChangedTime")
    private Long updatedAt;

    private String reportEntry;

    private Creator creator;

    public BatchEquipmentUploadLogDTO() {

    }

    public BatchEquipmentUploadLogDTO(Object[] object) {
        this.id = CustomStringUtils.toStringOrNull(object[0]); // beul.id
        this.fileEntry = CustomStringUtils.toBatchEquipmentFileEntry(CustomStringUtils.toStringOrNull(object[0])); // beul.id
        this.fileId = CustomStringUtils.toStringOrNull(object[2]); // beul.fileId
        this.fileName = CustomStringUtils.toStringOrNull(object[3]); // beul.fileName
        this.action = CustomStringUtils.toStringOrNull(object[4]); // beul.action
        this.status = CustomStringUtils.toStringOrNull(object[5]); // beul.status
        this.result = null; // 先不處理 result，留空
        this.createdAt = object[6] != null ? ((Timestamp) object[6]).getTime() : null; // beul.createdTime
        this.updatedAt = object[8] != null ? ((Timestamp) object[8]).getTime() : null; // beul.updatedAt
        this.reportEntry = CustomStringUtils.toBatchEquipmentFileEntry(CustomStringUtils.toStringOrNull(object[9])); // beul.errorFileId

        // 將 ispId 和 userId 塞進 CreatorInfo
        this.creator = new Creator();
        this.creator.setIspId(CustomStringUtils.toStringOrNull(object[1])); // beul.ispId
        this.creator.setUserId(CustomStringUtils.toStringOrNull(object[7])); // beul.userId
    }

    public static class Result {
        private int total = 0;
        private int complete = 0;
        private int failed = 0;

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getComplete() {
            return complete;
        }

        public void setComplete(int complete) {
            this.complete = complete;
        }

        public int getFailed() {
            return failed;
        }

        public void setFailed(int failed) {
            this.failed = failed;
        }
    }

    public static class Creator {
        private String ispId;
        private String userId;

        public String getIspId() {
            return ispId;
        }

        public void setIspId(String ispId) {
            this.ispId = ispId;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getFileEntry() {
        return fileEntry;
    }

    public void setFileEntry(String fileEntry) {
        this.fileEntry = fileEntry;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Result getResult() {
        return result;
    }

    public void setResult(Result result) {
        this.result = result;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getReportEntry() {
        return reportEntry;
    }

    public void setReportEntry(String reportEntry) {
        this.reportEntry = reportEntry;
    }

    public Creator getCreator() {
        return creator;
    }

    public void setCreator(Creator creator) {
        this.creator = creator;
    }
}