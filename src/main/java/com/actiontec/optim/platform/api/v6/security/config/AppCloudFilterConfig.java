package com.actiontec.optim.platform.api.v6.security.config;

import com.actiontec.optim.platform.api.v6.security.auth.jwt.provider.AppCloudAuthenticationProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.util.AntPathMatcher;

@Configuration
public class AppCloudFilterConfig {

    private final AppCloudAuthenticationProvider appCloudAuthenticationProvider;

    public AppCloudFilterConfig(AppCloudAuthenticationProvider appCloudAuthenticationProvider) {
        this.appCloudAuthenticationProvider = appCloudAuthenticationProvider;
    }

    @Autowired
    public void configureGlobal(AuthenticationManagerBuilder auth) {
        auth.authenticationProvider(appCloudAuthenticationProvider);
    }

    @Bean("appCloudAntPathMatcher")
    public AntPathMatcher antPathMatcher() {
        return new AntPathMatcher();
    }
}


