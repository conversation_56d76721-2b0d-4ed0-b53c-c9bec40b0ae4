package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.service.DmAccessService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Acs;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/dmaccess/equipment")
public class DmAccessController {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DmAccessService dmAccessService;

    @RequestMapping(value = "/{equipmentId}/actions", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Acs.class)
    public Map<String, Object> doDmAccess(
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "request properties", required = true) @RequestBody Map<String, Object> requestBodyMap,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return dmAccessService.doDmAccess(equipmentId, requestBodyMap);
    }
}
