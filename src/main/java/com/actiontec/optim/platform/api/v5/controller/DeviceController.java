package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v5.mapper.DeviceResponseMapper;
import com.actiontec.optim.platform.api.v5.model.DeviceActionRequest;
import com.actiontec.optim.platform.api.v5.model.DeviceRequest;
import com.actiontec.optim.platform.api.v5.model.DeviceResponse;
import com.actiontec.optim.platform.model.UserDevice;
import com.actiontec.optim.platform.service.UserDeviceService;
import com.actiontec.optim.service.AuditService;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@RestController("OptimDeviceController")
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/devices")
public class DeviceController {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private UserDeviceService userDeviceService;

    @Autowired
    private DeviceResponseMapper deviceResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @Autowired
    private ResponseUtil responseUtil;

    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<DeviceResponse> get(
            @ApiParam(value = "STN", required = true)
            @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "ID", required = true)
            @PathVariable(name = "id")
                    String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest
    ) throws Exception {
        Optional<UserDevice> optionalUserDevice = userDeviceService.findByNetworkIdAndDeviceMac(stn, id);
        if (!optionalUserDevice.isPresent()) {
            throw new OptimApiException(HttpStatus.NOT_FOUND, "");
        }
        List<UserDevice> result = new ArrayList<>();
        result.add(optionalUserDevice.get());
        List<DeviceResponse> deviceResponseList = deviceResponseMapper.toDeviceResponse(result);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "Device Resource", null, "200", deviceResponseList, httpServletRequest);
        return deviceResponseList;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void put(@ApiParam(value = "STN", required = true)
                        @PathVariable(name = "stn")
                        String stn,
                        @PathVariable(name = "id")
                        String id,
                        @ApiParam(value = "Request Properties", required = true) @RequestBody
                        DeviceRequest request,
                        @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                        @RequestHeader(name = "X-Authorization")
                        String accessToken,
                    HttpServletRequest httpServletRequest) throws Exception {

        userDeviceService.put(stn, id, request);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "Device Resource", request, "204", null, httpServletRequest);
    }

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<DeviceResponse> getAll(
            @ApiParam(value = "STN", required = true)
            @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        logger.info("listByStn [{}]", stn);
        List<DeviceResponse> deviceResponseList = deviceResponseMapper.toDeviceResponse(userDeviceService.listByNetworkId(stn));

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "Device Resource", null, "200", deviceResponseList, httpServletRequest);
        return deviceResponseList;
    }

    @RequestMapping(value = "/{deviceId}/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> post(@ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "stn") String stn,
                               @ApiParam(value = "Device Id of the device whose details are requested", required = true)
                               @PathVariable(name = "deviceId") String deviceId,
                               @ApiParam(value = "Actions Properties", required = true) @RequestBody DeviceActionRequest request,
                               @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                               @RequestHeader(name = "X-Authorization") String accessToken,
                               HttpServletRequest httpServletRequest) throws Exception {
        Object response = userDeviceService.postActions(stn, deviceId, request);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "Device Resource", request, "200", response, httpServletRequest);
        return responseUtil.ok(response, ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
