package com.actiontec.optim.platform.api.v6.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(value = "(V6) Batch Equipment", description = "API's for Batch Equipment Operations", tags = {"Optim - (V6) Batch Equipment"})
@RequestMapping(value = "/actiontec/api/v6/appCloud")
public class AppCloudController {
    





}
