package com.actiontec.optim.platform.api.v6.controller;

import io.swagger.annotations.Api;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(value = "(V6) App Cloud", description = "API's for App Cloud Operations", tags = {"Optim - (V6) App Cloud"})
@RequestMapping(value = "/actiontec/api/v6")
public class AppCloudController {
    private static final Logger logger = LogManager.getLogger(AppCloudController.class);

    @RequestMapping(value = "/test", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> test() {
        logger.info("App Cloud test API called successfully");

        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "OK");

        return response;
    }






}
