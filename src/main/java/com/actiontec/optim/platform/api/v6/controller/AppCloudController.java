package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.AppCloudTokenDTO;
import com.actiontec.optim.platform.service.AppCloudService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(value = "(V6) App Cloud", description = "API's for App Cloud Operations", tags = {"Optim - (V6) App Cloud"})
@RequestMapping(value = "/actiontec/api/v6")
public class AppCloudController {
    private static final Logger logger = LogManager.getLogger(AppCloudController.class);

    @Autowired
    private AppCloudService appCloudService;

    @RequestMapping(value = "/exchangeToken", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppCloudTokenDTO tokenExchange(@ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        logger.info("App Cloud token exchange requested.");

        return appCloudService.exchangeToken(accessToken);
    }






}
