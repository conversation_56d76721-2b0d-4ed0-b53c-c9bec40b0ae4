package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.platform.api.v6.enums.BatchEquipmentAction;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.service.BatchEquipmentService;
import com.actiontec.optim.platform.service.BatchEquipmentFileService;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@Api(value = "(V6) Batch Equipment", description = "API's for Batch Equipment Operations", tags = {"Optim - (V6) Batch Equipment"})
@RequestMapping(value = "/actiontec/api/v6/iam/equipment/actions")
public class BatchEquipmentController {
    @Autowired
    ManageCommonService manageCommonService;

    @Autowired
    BatchEquipmentService batchEquipmentService;

    @Autowired
    BatchEquipmentFileService batchEquipmentFileService;

    @Value("${onboarding.twostage.enabled:false}")
    private boolean twoStageEnabled;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<BatchEquipmentUploadLogDTO> getAllBatchEquipmentUploadLogs(
            @ModelAttribute BatchEquipmentUploadLogQueryDTO queryDTO) throws Exception {
        if (CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        } else if (CommonUtils.isGroupAdmin()) {
            manageCommonService.checkIspIdForGroupAdmin(queryDTO.getIspId());

            if (StringUtils.isEmpty(queryDTO.getIspId())) {
                String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
                String userIspId = manageCommonService.getIspByGroupId(userGroupId);
                queryDTO.setIspId(userIspId);
            }
        }
        batchEquipmentService.checkQueryParameter(queryDTO);
        return batchEquipmentService.getAllBatchEquipmentUploadLogs(queryDTO);
    }

    @RequestMapping(value = "/{actionId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BatchEquipmentUploadLogDTO getBatchEquipmentUploadLog(@ApiParam(value = "actionId") @PathVariable String actionId) throws Exception {
        // actionId is as same as batchEquipmentUploadLog id
        return batchEquipmentService.getBatchEquipmentUploadLogById(actionId);
    }

    @RequestMapping(method = RequestMethod.POST)
    public BatchUploadActionResponse createBatchEquipmentUploadLog(
            @RequestBody BatchUploadActionRequest batchUploadActionRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        if (CustomStringUtils.equals(BatchEquipmentAction.Redirect.name(), batchUploadActionRequest.getAction()) && !twoStageEnabled) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "Two stage onboarding is not enabled");
        }

        // only system admin can create
        if ((CustomStringUtils.equals(BatchEquipmentAction.Create.name(), batchUploadActionRequest.getAction())
                && !CommonUtils.isSysAdmin()) || CommonUtils.isEndUser()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource, only the system admin can add equipment.");
        }

        BatchUploadActionResponse response = new BatchUploadActionResponse();
        String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
        String userIspId = manageCommonService.getIspByGroupId(userGroupId);
        if (StringUtils.isEmpty(userIspId)) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "user ispId is not exist.");
        }

        String fileId = batchEquipmentFileService.createFile(batchUploadActionRequest, ApplicationConstants.OptimFileType.batchEquipment.name());
        String batchUploadLogId = batchEquipmentService.createUploadLog(userIspId, fileId, batchUploadActionRequest.getAction());
        response.setBatchUploadLogId(batchUploadLogId);
        response.setFileEntry(CustomStringUtils.toBatchEquipmentFileEntry(fileId));
        return response;
    }

    @RequestMapping(value = "/{actionId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public void deleteBatchEquipmentUploadLog(@ApiParam(value = "actionId") @PathVariable String actionId) throws Exception {
        batchEquipmentService.deleteBatchEquipmentUploadLog(actionId);
    }

}
