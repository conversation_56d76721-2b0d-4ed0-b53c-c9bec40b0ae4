package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class TopologyResponse {
    private List<TopologyEquipmentResponse> equipmentList;
    private List<TopologyDeviceResponse> deviceList;
    private long lastReportTime;

    public List<TopologyEquipmentResponse> getEquipmentList() {
        return equipmentList;
    }

    public void setEquipmentList(List<TopologyEquipmentResponse> equipmentList) {
        this.equipmentList = equipmentList;
    }

    public List<TopologyDeviceResponse> getDeviceList() {
        return deviceList;
    }

    public void setDeviceList(List<TopologyDeviceResponse> deviceList) {
        this.deviceList = deviceList;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public static class TopologyEntityResponse {
        private String id;
        private String macAddress;
        private String name;
        private String status;
        private String upstream;
        private String phyType;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getUpstream() {
            return upstream;
        }

        public void setUpstream(String upstream) {
            this.upstream = upstream;
        }

        public String getPhyType() {
            return phyType;
        }

        public void setPhyType(String phyType) {
            this.phyType = phyType;
        }
    }

    public static class TopologyEquipmentResponse extends TopologyEntityResponse{
        private String serialNumber;
        private String equipmentType;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getEquipmentType() {
            return equipmentType;
        }

        public void setEquipmentType(String equipmentType) {
            this.equipmentType = equipmentType;
        }
    }

    public static class TopologyDeviceResponse extends TopologyEntityResponse{
        private String deviceType;

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }
    }
}
