package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;

@ApiModel(description = "Network query parameters")
public class NetworkOperationsQueryDTO extends PaginationRequest {
    @ApiModelProperty(value = "The keyword is used for searching networks. It can be a partial match from the following fields: Subscriber Name or Serial Number.")
    @Column(name = "keyword")
    private String keyword;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }
}
