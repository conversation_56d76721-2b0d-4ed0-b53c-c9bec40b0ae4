package com.actiontec.optim.platform.api.v5.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class MocaResponse {
    private String id;
    private String macAddress;
    private int portNumber;
    private boolean isUpstream;
    private String version;
    private String status;
    private long operatingFrequency;
    private long lastChangeTime;
    private long lastReportTime;
    private MocaStatsResponse stats;
    private List<MocaNodeResponse> nodes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public int getPortNumber() {
        return portNumber;
    }

    public void setPortNumber(int portNumber) {
        this.portNumber = portNumber;
    }

    @JsonProperty("isUpstream")
    public boolean isUpstream() {
        return isUpstream;
    }

    public void setUpstream(boolean upstream) {
        isUpstream = upstream;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getOperatingFrequency() {
        return operatingFrequency;
    }

    public void setOperatingFrequency(long operatingFrequency) {
        this.operatingFrequency = operatingFrequency;
    }

    public long getLastChangeTime() {
        return lastChangeTime;
    }

    public void setLastChangeTime(long lastChangeTime) {
        this.lastChangeTime = lastChangeTime;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }

    public MocaStatsResponse getStats() {
        return stats;
    }

    public void setStats(MocaStatsResponse stats) {
        this.stats = stats;
    }

    public List<MocaNodeResponse> getNodes() {
        return nodes;
    }

    public void setNodes(List<MocaNodeResponse> nodes) {
        this.nodes = nodes;
    }

    public static class MocaStatsResponse {
        private long bytesSent;
        private long bytesReceived;

        public long getBytesSent() {
            return bytesSent;
        }

        public void setBytesSent(long bytesSent) {
            this.bytesSent = bytesSent;
        }

        public long getBytesReceived() {
            return bytesReceived;
        }

        public void setBytesReceived(long bytesReceived) {
            this.bytesReceived = bytesReceived;
        }
    }

    public static class MocaNodeResponse {
        private String macAddress;
        private String mode;
        private long uptime;
        private long attenuation;
        private long transmitPhyRate;
        private long receivePhyRate;
        private long transmitPowerLevel;
        private long receivePowerLevel;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }

        public long getAttenuation() {
            return attenuation;
        }

        public void setAttenuation(long attenuation) {
            this.attenuation = attenuation;
        }

        public long getTransmitPhyRate() {
            return transmitPhyRate;
        }

        public void setTransmitPhyRate(long transmitPhyRate) {
            this.transmitPhyRate = transmitPhyRate;
        }

        public long getReceivePhyRate() {
            return receivePhyRate;
        }

        public void setReceivePhyRate(long receivePhyRate) {
            this.receivePhyRate = receivePhyRate;
        }

        public long getTransmitPowerLevel() {
            return transmitPowerLevel;
        }

        public void setTransmitPowerLevel(long transmitPowerLevel) {
            this.transmitPowerLevel = transmitPowerLevel;
        }

        public long getReceivePowerLevel() {
            return receivePowerLevel;
        }

        public void setReceivePowerLevel(long receivePowerLevel) {
            this.receivePowerLevel = receivePowerLevel;
        }
    }
}
