package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.sql.Timestamp;

public class NCSEquipmentDTO {
    private String id;
    @JsonProperty("serialNumber")
    private String serial;
    @JsonProperty("macAddress")
    private String mac;
    private String modelName;
    private String productId;
    private String ispId;
    private String ispName;
    private boolean inUse;
    private String networkId;
    @JsonProperty("lastChangedTime")
    private Long updatedAt;
    @JsonProperty("brokerId")
    private String brokerInfoId;

    public NCSEquipmentDTO() {
    }

    public NCSEquipmentDTO(Object[] object) {
        this.id = CustomStringUtils.toStringOrNull(object[0]);
        this.serial = CustomStringUtils.toStringOrNull(object[1]);
        this.mac = CustomStringUtils.toStringOrNull(object[2]);
        this.modelName = CustomStringUtils.toStringOrNull(object[3]);
        this.productId = CustomStringUtils.toStringOrNull(object[4]);
        this.inUse = Boolean.parseBoolean(String.valueOf(object[5]));
        this.updatedAt = object[6] != null ? ((Timestamp) object[6]).getTime() : null;
        this.ispId = CustomStringUtils.toStringOrNull(object[7]);
        this.networkId = CustomStringUtils.toStringOrNull(object[8]);
        this.brokerInfoId = CustomStringUtils.toStringOrNull(object[9]);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getIspName() {
        return ispName;
    }

    public void setIspName(String ispName) {
        this.ispName = ispName;
    }

    public boolean isInUse() {
        return inUse;
    }

    public void setInUse(boolean inUse) {
        this.inUse = inUse;
    }

    public String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(String networkId) {
        this.networkId = networkId;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getBrokerInfoId() {
        return brokerInfoId;
    }

    public void setBrokerInfoId(String brokerInfoId) {
        this.brokerInfoId = brokerInfoId;
    }
}
