package com.actiontec.optim.platform.api.v5.controller;


import com.actiontec.optim.platform.api.v5.model.ActionRequest;
import com.actiontec.optim.platform.api.v5.model.SmmJobRequest;
import com.actiontec.optim.platform.model.SmmJobStatus;
import com.actiontec.optim.platform.service.SmmJobService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.Smm;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/smm/lifecycle/serviceJobs")
public class SmmJobController {
    @Autowired
    private AuditService auditService;
    @Autowired
    private SmmJobService smmJobService;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmJobStatus> getAllJobs(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<SmmJobStatus> smmJobStatusList = smmJobService.findSmmJobFromJobs(null);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "GET", null, "200", smmJobStatusList, httpServletRequest);
        return smmJobStatusList;
    }

    @RequestMapping(value = "/{jobId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmJobStatus> getJob(
            @ApiParam(value = "jobId", required = true) @PathVariable(name = "jobId")  String jobId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<SmmJobStatus> smmJobStatusList = smmJobService.findSmmJobFromJobs(jobId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "GET", jobId, "200", smmJobStatusList, httpServletRequest);
        return smmJobStatusList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> postJob(
            @ApiParam(value = "Job Properties", required = true) @RequestBody SmmJobRequest smmJobRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> response = new HashMap<>();
        String id = smmJobService.postJob(smmJobRequest);
        if (id != null)
            response.put("id", id);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "POST", smmJobRequest, "200", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/{jobId}/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    public Map<String,String> postActions(
            @ApiParam(value = "jobId", required = true) @PathVariable(name = "jobId") String jobId,
            @ApiParam(value = "Job Actions properties", required = true) @RequestBody ActionRequest actionRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> response = new HashMap<>();
        String id = smmJobService.postActions(jobId, actionRequest);
        if (id != null)
            response.put("id", id);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "POST", actionRequest, "200", response, httpServletRequest);

        return response;
    }

}
