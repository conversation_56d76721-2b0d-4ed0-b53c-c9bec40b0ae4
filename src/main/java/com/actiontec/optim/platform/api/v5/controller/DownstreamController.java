package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.ConnectionDownstreamResponseMapper;
import com.actiontec.optim.platform.api.v5.model.ConnectionDownstreamResponse;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.model.equipment.NetworkDownstreamContext;
import com.actiontec.optim.platform.model.equipment.NetworkDownstreamInterface;
import com.actiontec.optim.platform.service.NetworkStreamService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}/downstreamConnections")
public class DownstreamController {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private NetworkStreamService networkStreamService;

    @Autowired
    private ConnectionDownstreamResponseMapper downstreamResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<ConnectionDownstreamResponse> getAll(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkDownstreamContext downstreamContext = networkStreamService.findDownstreamContextByStnAndSerial(stn, equipmentId);
        List<ConnectionDownstreamResponse> downstreamResponses = downstreamResponseMapper.toConnectionDownstreamResponse(downstreamContext.getInterfaces());

        if (downstreamResponses == null) {
            downstreamResponses = new ArrayList<>();
        }
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Network Connection Resource", null, "200", downstreamResponses, httpServletRequest);
        return downstreamResponses;
    }

    @RequestMapping(value = "/{downstreamId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<ConnectionDownstreamResponse> get(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "id", required = true) @PathVariable(name = "downstreamId")
                    String downstreamId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkDownstreamContext downstreamContext = networkStreamService.findDownstreamContextByStnAndSerial(stn, equipmentId);
        if(downstreamContext.getInterfaces() == null) {
            throw new NoSuchEntityException();
        }

        Optional<NetworkDownstreamInterface> downstreamInterfaceOpt = downstreamContext.getInterfaces().stream()
                .filter(x -> StringUtils.equals(x.getId(), downstreamId))
                .findAny();

        if (downstreamInterfaceOpt.isPresent()) {
            ConnectionDownstreamResponse connectionDownstreamResponse = downstreamResponseMapper.toConnectionDownstreamResponse(downstreamInterfaceOpt.get());
            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Network Connection Resource", null, "200", connectionDownstreamResponse, httpServletRequest);
            return Stream.of(connectionDownstreamResponse).collect(Collectors.toList());
        } else {
            throw new NoSuchEntityException();
        }

    }

}
