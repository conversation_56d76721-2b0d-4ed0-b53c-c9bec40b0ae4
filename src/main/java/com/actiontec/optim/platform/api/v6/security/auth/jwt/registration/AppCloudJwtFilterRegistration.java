package com.actiontec.optim.platform.api.v6.security.auth.jwt.registration;

import com.actiontec.optim.platform.api.v6.security.auth.jwt.filter.AppCloudJwtTokenFilter;
import com.incs83.security.auth.jwt.filter.FilterPosition;
import com.incs83.security.auth.jwt.filter.JwtTokenAuthenticationProcessingFilter;
import com.incs83.security.auth.jwt.filter.SecurityFilterRegistration;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.PostConstruct;
import javax.servlet.Filter;

@Component
public class AppCloudJwtFilterRegistration implements SecurityFilterRegistration {
    private final Logger logger = LogManager.getLogger(this.getClass());

    private final AppCloudJwtTokenFilter appCloudJwtTokenFilter;

    public AppCloudJwtFilterRegistration(AppCloudJwtTokenFilter appCloudJwtTokenFilter) {
        this.appCloudJwtTokenFilter = appCloudJwtTokenFilter;
    }

    @PostConstruct
    public void init() {
        logger.info("AppCloudJwtFilterRegistration initialized");
    }

    @Override
    public Filter getFilter() {
        return appCloudJwtTokenFilter;
    }

    @Override
    public FilterPosition<? extends Filter> getPosition() {
        // 這邊指定插在 JwtTokenAuthenticationProcessingFilter 之前
        return FilterPosition.before(JwtTokenAuthenticationProcessingFilter.class);
    }
}
