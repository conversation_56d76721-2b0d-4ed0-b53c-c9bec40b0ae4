package com.actiontec.optim.platform.api.v6.controller;

import com.actiontec.optim.platform.api.v6.dto.NCSIspQueryDTO;
import com.actiontec.optim.platform.api.v6.model.IspRequest;
import com.actiontec.optim.platform.api.v6.model.IspUpdateRequest;
import com.actiontec.optim.platform.service.NCSIspService;
import com.actiontec.optim.platform.api.v6.dto.NCSIspDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api(value = "(V6) Internet Service Provider", description = "REST API's for Managing ISP",tags = {"Optim - (V6) Internet Service Provider"})
@RequestMapping(value = "/actiontec/api/v6/iam/isps")
public class NCSIspController {
    @Autowired
    private NCSIspService ispService;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PaginationResponse<NCSIspDTO> getAllIsps(
            @ModelAttribute NCSIspQueryDTO queryDTO) throws Exception  {
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Insufficient permissions to access this resource");
        }

        return ispService.getAllIsps(queryDTO);
    }

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> createIsp(
            @ApiParam(value = "ISP data", required = true)
            @Valid @RequestBody IspRequest ispRequest) throws Exception {
        Map<String, String> response = new HashMap<>();
        String id = ispService.createIsp(ispRequest);
        response.put("id", id);
        return response;
    }

    @RequestMapping(value = "/{ispId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public NCSIspDTO getIsp(
            @ApiParam(value = "ISP ID", required = true)
            @PathVariable String ispId) throws Exception {
        return ispService.getIsp(ispId);
    }

    @RequestMapping(value = "/{ispId}", method = RequestMethod.PATCH, produces = MediaType.APPLICATION_JSON_VALUE)
    public void updateIsp(
            @ApiParam(value = "ISP ID", required = true)
            @PathVariable String ispId,
            @ApiParam(value = "ISP data", required = true)
            @Valid @RequestBody IspUpdateRequest ispRequest) throws Exception {
        ispService.updateIsp(ispId, ispRequest);
    }

    @RequestMapping(value = "/{ispId}", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteIsp(
            @ApiParam(value = "ISP ID", required = true)
            @PathVariable String ispId) throws Exception {
        ispService.deleteIsp(ispId);
    }
}
