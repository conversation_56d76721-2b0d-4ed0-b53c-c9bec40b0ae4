package com.actiontec.optim.platform.api.v6.security.tokenFactory;

import com.incs83.app.entities.AppCloudConfig;
import org.springframework.security.authentication.AbstractAuthenticationToken;

public class AppCloudJwtAuthenticationToken extends AbstractAuthenticationToken {
    private final String exchangeToken;
    private final String networkId;
    private final String networkTokenId;
    private final AppCloudConfig appCloudConfig;

    public AppCloudJwtAuthenticationToken(String exchangeToken, String networkId, String networkTokenId) {
        super(null);
        this.exchangeToken = exchangeToken;
        this.networkId = networkId;
        this.networkTokenId = networkTokenId;
        this.appCloudConfig = null;
        setAuthenticated(false);
    }

    public AppCloudJwtAuthenticationToken(String exchangeToken, String networkId, String networkTokenId,
                                          AppCloudConfig appCloudConfig) {
        super(null);
        this.exchangeToken = exchangeToken;
        this.networkId = networkId;
        this.networkTokenId = networkTokenId;
        this.appCloudConfig = appCloudConfig;
        setAuthenticated(true);
    }

    @Override
    public Object getCredentials() {
        return exchangeToken;
    }

    @Override
    public Object getPrincipal() {
        return appCloudConfig;
    }

    public String getNetworkId() {
        return this.networkId;
    }

    public String getNetworkTokenId() {
        return this.networkTokenId;
    }
}