package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.VoiceClientResponse;
import com.actiontec.optim.platform.api.v5.model.VoiceNetworkResponse;
import com.actiontec.optim.platform.api.v5.model.VoiceResponse;
import com.actiontec.optim.platform.api.v5.model.VoiceStatsResponse;
import com.actiontec.optim.platform.model.NetworkVoice;
import com.actiontec.optim.platform.model.NetworkVoiceClient;
import com.actiontec.optim.platform.model.NetworkVoiceNet;
import com.actiontec.optim.platform.model.NetworkVoiceStats;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface VoiceResponseMapper {

    public VoiceResponse toVoiceResponse(NetworkVoice networkVoice);
    public List<VoiceClientResponse> toVoiceClientResponse(List<NetworkVoiceClient> networkVoiceClientList);
    public VoiceNetworkResponse toVoiceNetworkResponse(NetworkVoiceNet networkVoiceNet);
    public List<VoiceStatsResponse> toVoiceStatsResponse(List<NetworkVoiceStats> networkVoiceStatsList);
}
