package com.actiontec.optim.platform.api.v5.model;

public class OpticalModuleResponse {

    private String serialNumber;
    private String fwVersion;
    private String status;
    private Long lastChangeTime;
    private int temperature;
    private int temperatureWarningThreshold;
    private int temperatureAlarmThreshold;
    private Long lastReportTime;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getFwVersion() {
        return fwVersion;
    }

    public void setFwVersion(String fwVersion) {
        this.fwVersion = fwVersion;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getLastChangeTime() {
        return lastChangeTime;
    }

    public void setLastChangeTime(Long lastChangeTime) {
        this.lastChangeTime = lastChangeTime;
    }

    public int getTemperature() {
        return temperature;
    }

    public void setTemperature(int temperature) {
        this.temperature = temperature;
    }

    public int getTemperatureWarningThreshold() {
        return temperatureWarningThreshold;
    }

    public void setTemperatureWarningThreshold(int temperatureWarningThreshold) {
        this.temperatureWarningThreshold = temperatureWarningThreshold;
    }

    public int getTemperatureAlarmThreshold() {
        return temperatureAlarmThreshold;
    }

    public void setTemperatureAlarmThreshold(int temperatureAlarmThreshold) {
        this.temperatureAlarmThreshold = temperatureAlarmThreshold;
    }

    public Long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(Long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }
}
