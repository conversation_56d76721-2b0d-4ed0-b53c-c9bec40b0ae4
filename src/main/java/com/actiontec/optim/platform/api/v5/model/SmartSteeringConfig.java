package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class SmartSteeringConfig {
    private boolean enabled;
    private DiagnosticLoggingOptions diagnosticLoggingOptions;

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public DiagnosticLoggingOptions getDiagnosticLoggingOptions() {
        return diagnosticLoggingOptions;
    }

    public void setDiagnosticLoggingOptions(DiagnosticLoggingOptions diagnosticLoggingOptions) {
        this.diagnosticLoggingOptions = diagnosticLoggingOptions;
    }

    public static class DiagnosticLoggingOptions {
        private LogLevelSteering logLevelSteering;
        private LogLevelVictimSelection logLevelVictimSelection;
        private List<String> logLevelStation;

        public LogLevelSteering getLogLevelSteering() {
            return logLevelSteering;
        }

        public void setLogLevelSteering(LogLevelSteering logLevelSteering) {
            this.logLevelSteering = logLevelSteering;
        }

        public LogLevelVictimSelection getLogLevelVictimSelection() {
            return logLevelVictimSelection;
        }

        public void setLogLevelVictimSelection(LogLevelVictimSelection logLevelVictimSelection) {
            this.logLevelVictimSelection = logLevelVictimSelection;
        }

        public List<String> getLogLevelStation() {
            return logLevelStation;
        }

        public void setLogLevelStation(List<String> logLevelStation) {
            this.logLevelStation = logLevelStation;
        }

        public static class LogLevelVictimSelection {
            private boolean reachedMaxSteering;
            private boolean notDualbandStation;
            private boolean stationForManualSteering;
            private boolean stationBeingSteered;
            private boolean reachedMaxFailure;
            private boolean roamedStation;
            private boolean notStationaryStation;
            private boolean legacyStation;
            private boolean stationCooldownTimer;
            private boolean frequentBtmReqFailures;
            private boolean duplicatedStation;
            private boolean overloadedOriginalAp;
            private boolean isStationRepeater;
            private boolean trafficRateAboveThreshold;
            private boolean stationRssiStrongerThanThreshold;
            private boolean projectedTargetApRssiWeakerThanCurrent;
            private boolean stationCandidateTimer;
            private boolean noBetterApFound;
            private boolean stationOnVideoSsid;

            public boolean isReachedMaxSteering() {
                return reachedMaxSteering;
            }

            public void setReachedMaxSteering(boolean reachedMaxSteering) {
                this.reachedMaxSteering = reachedMaxSteering;
            }

            public boolean isNotDualbandStation() {
                return notDualbandStation;
            }

            public void setNotDualbandStation(boolean notDualbandStation) {
                this.notDualbandStation = notDualbandStation;
            }

            public boolean isStationForManualSteering() {
                return stationForManualSteering;
            }

            public void setStationForManualSteering(boolean stationForManualSteering) {
                this.stationForManualSteering = stationForManualSteering;
            }

            public boolean isStationBeingSteered() {
                return stationBeingSteered;
            }

            public void setStationBeingSteered(boolean stationBeingSteered) {
                this.stationBeingSteered = stationBeingSteered;
            }

            public boolean isReachedMaxFailure() {
                return reachedMaxFailure;
            }

            public void setReachedMaxFailure(boolean reachedMaxFailure) {
                this.reachedMaxFailure = reachedMaxFailure;
            }

            public boolean isRoamedStation() {
                return roamedStation;
            }

            public void setRoamedStation(boolean roamedStation) {
                this.roamedStation = roamedStation;
            }

            public boolean isNotStationaryStation() {
                return notStationaryStation;
            }

            public void setNotStationaryStation(boolean notStationaryStation) {
                this.notStationaryStation = notStationaryStation;
            }

            public boolean isLegacyStation() {
                return legacyStation;
            }

            public void setLegacyStation(boolean legacyStation) {
                this.legacyStation = legacyStation;
            }

            public boolean isStationCooldownTimer() {
                return stationCooldownTimer;
            }

            public void setStationCooldownTimer(boolean stationCooldownTimer) {
                this.stationCooldownTimer = stationCooldownTimer;
            }

            public boolean isFrequentBtmReqFailures() {
                return frequentBtmReqFailures;
            }

            public void setFrequentBtmReqFailures(boolean frequentBtmReqFailures) {
                this.frequentBtmReqFailures = frequentBtmReqFailures;
            }

            public boolean isDuplicatedStation() {
                return duplicatedStation;
            }

            public void setDuplicatedStation(boolean duplicatedStation) {
                this.duplicatedStation = duplicatedStation;
            }

            public boolean isOverloadedOriginalAp() {
                return overloadedOriginalAp;
            }

            public void setOverloadedOriginalAp(boolean overloadedOriginalAp) {
                this.overloadedOriginalAp = overloadedOriginalAp;
            }

            public boolean isStationRepeater() {
                return isStationRepeater;
            }

            public void setStationRepeater(boolean stationRepeater) {
                isStationRepeater = stationRepeater;
            }

            public boolean isTrafficRateAboveThreshold() {
                return trafficRateAboveThreshold;
            }

            public void setTrafficRateAboveThreshold(boolean trafficRateAboveThreshold) {
                this.trafficRateAboveThreshold = trafficRateAboveThreshold;
            }

            public boolean isStationRssiStrongerThanThreshold() {
                return stationRssiStrongerThanThreshold;
            }

            public void setStationRssiStrongerThanThreshold(boolean stationRssiStrongerThanThreshold) {
                this.stationRssiStrongerThanThreshold = stationRssiStrongerThanThreshold;
            }

            public boolean isProjectedTargetApRssiWeakerThanCurrent() {
                return projectedTargetApRssiWeakerThanCurrent;
            }

            public void setProjectedTargetApRssiWeakerThanCurrent(boolean projectedTargetApRssiWeakerThanCurrent) {
                this.projectedTargetApRssiWeakerThanCurrent = projectedTargetApRssiWeakerThanCurrent;
            }

            public boolean isStationCandidateTimer() {
                return stationCandidateTimer;
            }

            public void setStationCandidateTimer(boolean stationCandidateTimer) {
                this.stationCandidateTimer = stationCandidateTimer;
            }

            public boolean isNoBetterApFound() {
                return noBetterApFound;
            }

            public void setNoBetterApFound(boolean noBetterApFound) {
                this.noBetterApFound = noBetterApFound;
            }

            public boolean isStationOnVideoSsid() {
                return stationOnVideoSsid;
            }

            public void setStationOnVideoSsid(boolean stationOnVideoSsid) {
                this.stationOnVideoSsid = stationOnVideoSsid;
            }
        }

        public static class LogLevelSteering {
            private boolean apOnDfsChannel;
            private boolean blacklistedAp;
            private boolean stationTransittedBeforeBestApDetermined;
            private boolean targetApOverloaded;
            private boolean targetApInBsDown;
            private boolean preventingSteeringLoop;
            private boolean projectedTargetApCuAboveThreshold;
            private boolean isBestRssiOnCurrentAP;
            private boolean noIdleApFound;
            private boolean no11kResponded;
            private boolean no11vTransitionObserved;

            public boolean isApOnDfsChannel() {
                return apOnDfsChannel;
            }

            public void setApOnDfsChannel(boolean apOnDfsChannel) {
                this.apOnDfsChannel = apOnDfsChannel;
            }

            public boolean isBlacklistedAp() {
                return blacklistedAp;
            }

            public void setBlacklistedAp(boolean blacklistedAp) {
                this.blacklistedAp = blacklistedAp;
            }

            public boolean isStationTransittedBeforeBestApDetermined() {
                return stationTransittedBeforeBestApDetermined;
            }

            public void setStationTransittedBeforeBestApDetermined(boolean stationTransittedBeforeBestApDetermined) {
                this.stationTransittedBeforeBestApDetermined = stationTransittedBeforeBestApDetermined;
            }

            public boolean isTargetApOverloaded() {
                return targetApOverloaded;
            }

            public void setTargetApOverloaded(boolean targetApOverloaded) {
                this.targetApOverloaded = targetApOverloaded;
            }

            public boolean isTargetApInBsDown() {
                return targetApInBsDown;
            }

            public void setTargetApInBsDown(boolean targetApInBsDown) {
                this.targetApInBsDown = targetApInBsDown;
            }

            public boolean isPreventingSteeringLoop() {
                return preventingSteeringLoop;
            }

            public void setPreventingSteeringLoop(boolean preventingSteeringLoop) {
                this.preventingSteeringLoop = preventingSteeringLoop;
            }

            public boolean isProjectedTargetApCuAboveThreshold() {
                return projectedTargetApCuAboveThreshold;
            }

            public void setProjectedTargetApCuAboveThreshold(boolean projectedTargetApCuAboveThreshold) {
                this.projectedTargetApCuAboveThreshold = projectedTargetApCuAboveThreshold;
            }

            public boolean isBestRssiOnCurrentAP() {
                return isBestRssiOnCurrentAP;
            }

            public void setBestRssiOnCurrentAP(boolean bestRssiOnCurrentAP) {
                isBestRssiOnCurrentAP = bestRssiOnCurrentAP;
            }

            public boolean isNoIdleApFound() {
                return noIdleApFound;
            }

            public void setNoIdleApFound(boolean noIdleApFound) {
                this.noIdleApFound = noIdleApFound;
            }

            public boolean isNo11kResponded() {
                return no11kResponded;
            }

            public void setNo11kResponded(boolean no11kResponded) {
                this.no11kResponded = no11kResponded;
            }

            public boolean isNo11vTransitionObserved() {
                return no11vTransitionObserved;
            }

            public void setNo11vTransitionObserved(boolean no11vTransitionObserved) {
                this.no11vTransitionObserved = no11vTransitionObserved;
            }
        }

    }
}
