package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class NCSDashboardDTO {

    @JsonProperty("networks")
    private NetworkStats networkStats;

    @JsonProperty("equipment")
    private EquipmentStats equipmentStats;

    @JsonProperty("devices")
    private DeviceStats deviceStats;

    public NetworkStats getNetworkStats() {
        return networkStats;
    }

    public void setNetworkStats(NetworkStats networkStats) {
        this.networkStats = networkStats;
    }

    public EquipmentStats getEquipmentStats() {
        return equipmentStats;
    }

    public void setEquipmentStats(EquipmentStats equipmentStats) {
        this.equipmentStats = equipmentStats;
    }

    public DeviceStats getDeviceStats() {
        return deviceStats;
    }

    public void setDeviceStats(DeviceStats deviceStats) {
        this.deviceStats = deviceStats;
    }

    public static class BaseStats {
        private Long online = 0L;
        private Long offline = 0L;

        public Long getOnline() {
            return online;
        }

        public void setOnline(Long online) {
            this.online = online;
        }

        public Long getOffline() {
            return offline;
        }

        public void setOffline(Long offline) {
            this.offline = offline;
        }
    }

    public static class NetworkStats extends BaseStats {
        private Long autoCreated = 0L;
        private Long manuallyCreated = 0L;

        @JsonIgnore
        @Override
        public Long getOffline() {
            return null;
        }

        public Long getAutoCreated() {
            return autoCreated;
        }

        public void setAutoCreated(Long autoCreated) {
            this.autoCreated = autoCreated;
        }

        public Long getManuallyCreated() {
            return manuallyCreated;
        }

        public void setManuallyCreated(Long manuallyCreated) {
            this.manuallyCreated = manuallyCreated;
        }
    }

    public static class EquipmentStats extends BaseStats {
    }

    public static class DeviceStats extends BaseStats {
    }
} 