package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.platform.api.v5.model.MocaResponse;
import com.actiontec.optim.platform.model.NetworkMoca;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface MocaResponseMapper {

    @Mapping(target = "portNumber", source = "portNo")
    @Mapping(target = "version", source = "mocaVer")
    public MocaResponse toMocaResponse(NetworkMoca networkMoca);

    public MocaResponse.MocaStatsResponse toMocaStatsResponse(NetworkMoca.MocaStats mocaStats);

    public MocaResponse.MocaNodeResponse toMocaNodeResponse(NetworkMoca.MocaNode mocaNode);

    public List<MocaResponse> toMocaResponse(List<NetworkMoca> networkMocas);

    public List<MocaResponse.MocaNodeResponse> toMocaNodeResponse(List<NetworkMoca.MocaNode> mocaNodes);

}
