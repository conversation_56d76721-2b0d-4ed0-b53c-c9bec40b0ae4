package com.actiontec.optim.platform.api.v5.model;

public class NetworkTraffic {
    private String deviceId;
    private long bytesSent;
    private long bytesReceived;

    public NetworkTraffic() {
    }

    public NetworkTraffic(String deviceId, long bytesSent, long bytesReceived) {
        this.deviceId = deviceId;
        this.bytesSent = bytesSent;
        this.bytesReceived = bytesReceived;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public long getBytesSent() {
        return bytesSent;
    }

    public void setBytesSent(long bytesSent) {
        this.bytesSent = bytesSent;
    }

    public long getBytesReceived() {
        return bytesReceived;
    }

    public void setBytesReceived(long bytesReceived) {
        this.bytesReceived = bytesReceived;
    }
}
