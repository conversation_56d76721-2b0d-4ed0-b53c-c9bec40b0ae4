package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.mapper.NetworkResponseMapper;
import com.actiontec.optim.platform.api.v5.model.NetworkRequest;
import com.actiontec.optim.platform.api.v5.model.NetworkResponse;
import com.actiontec.optim.platform.model.NetworkContract;
import com.actiontec.optim.platform.service.NetworkService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network")
public class EquipmentNetworkController {

    @Autowired
    NetworkService networkService;

    @Autowired
    NetworkResponseMapper networkResponseMapper;

    @Autowired
    private AuditService auditService;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<NetworkResponse> getNetworkContracts(
            @ApiParam(value = "subscriberId")  @RequestParam(name = "subscriberId", required = false, defaultValue = "") String subscriberId,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkContract> networkContractList = networkService.getNetworkContractByUserId(subscriberId);
        List<NetworkResponse> networkResponseList = networkResponseMapper.toNetworkResponse(networkContractList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "", "Network Resource", null, "200", networkResponseList, httpServletRequest);

        return networkResponseList;
    }

    @RequestMapping(value = "/{stn}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<NetworkResponse> getNetwork(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkContract> networkContractList = networkService.getNetworkContractByStn(stn);
        List<NetworkResponse> networkResponseList = networkResponseMapper.toNetworkResponse(networkContractList);

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "", "Network Resource", null, "200", networkResponseList, httpServletRequest);

        return networkResponseList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> createNetworkContracts(
            @ApiParam(value = "Network properties", required = true) @RequestBody NetworkRequest networkRequest,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Map<String, String> retVal = networkService.createNetworkContract(networkRequest);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "", "Network Resource", null, "201", null, httpServletRequest);

        return retVal;
    }

    @RequestMapping(value = "/{stn}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    public void editNetworkContracts(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Network properties", required = true) @RequestBody NetworkRequest networkRequest,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        networkService.editNetworkContract(stn, networkRequest);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "", "Network Resource", null, "201", null, httpServletRequest);
    }

    @RequestMapping(value = "/{stn}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteNetworkContracts(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        networkService.deleteNetworkContract(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), "", "Network Resource", null, "204", null, httpServletRequest);
    }
}
