package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.model.SmmContainer;
import com.actiontec.optim.platform.api.v5.model.SmmContainerRequest;
import com.actiontec.optim.platform.service.SmmContainerService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Smm;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping(value = "/actiontec/api/v5/smm/network/{stn}/equipment/{equipmentId}/containers")
public class SmmContainerController {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private SmmContainerService smmContainerService;

    @Autowired
    private AuditService auditService;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmContainer> getAllContainers(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "To get data from CPE directly or not.")
            @RequestParam(name = "rpcRequest", required = false, defaultValue = "false") boolean rpcRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        List<SmmContainer> smmContainerList = smmContainerService.getAllContainers(stn, equipmentId, rpcRequest);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Container Resource", equipmentId, "200", smmContainerList, httpServletRequest);
        return smmContainerList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    public Map<String, String>  createContainer(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Container properties", required = true) @RequestBody SmmContainerRequest smmContainerRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> response = new HashMap<>();

        String id = smmContainerService.createContainer(stn, equipmentId, smmContainerRequest);
        if (id != null) {
            response.put("id", id);
        }
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Container Resource", smmContainerRequest, "201", response, httpServletRequest);
        return response;
    }

    @RequestMapping(value = "/{containerId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Smm.class)
    public List<SmmContainer> getContainer(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "containerId", required = true) @PathVariable(name = "containerId")
                    String containerId,
            @ApiParam(value = "To get data from CPE directly or not.")
            @RequestParam(name = "rpcRequest", required = false, defaultValue = "false") boolean rpcRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SmmContainer> smmContainer = smmContainerService.getContainerById(stn, equipmentId, containerId, rpcRequest);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Container Resource", containerId, "200", smmContainer, httpServletRequest);

        return smmContainer;
    }

    @RequestMapping(value = "/{containerId}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Smm.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void removeContainer(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "containerId", required = true) @PathVariable(name = "containerId")
                    String containerId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        smmContainerService.removeContainer(stn, equipmentId, containerId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Container Resource", null, "204", null, httpServletRequest);
    }

    @RequestMapping(value = "/{containerId}/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Smm.class)
    public Map<String, String> postContainer(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "containerId", required = true) @PathVariable(name = "containerId")
                    String containerId,
            @ApiParam(value = "Container properties", required = true) @RequestBody SmmContainerRequest smmContainerRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        Map<String, String> response = new HashMap<>();
        String id = smmContainerService.postContainer(stn, equipmentId, containerId, smmContainerRequest);
        if (Objects.nonNull(id)) {
            response.put("id", id);
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Container Resource", smmContainerRequest, "200", response, httpServletRequest);
        return response;
    }
}
