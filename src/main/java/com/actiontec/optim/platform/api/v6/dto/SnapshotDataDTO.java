package com.actiontec.optim.platform.api.v6.dto;

import java.util.List;

public class SnapshotDataDTO {
    private List<DeviceDTO> devices;
    private List<RadioDTO> radios;
    private long timeOfSnapshot;

    public List<DeviceDTO> getDevices() {
        return devices;
    }

    public void setDevices(List<DeviceDTO> devices) {
        this.devices = devices;
    }

    public List<RadioDTO> getRadios() {
        return radios;
    }

    public void setRadios(List<RadioDTO> radios) {
        this.radios = radios;
    }

    public long getTimeOfSnapshot() {
        return timeOfSnapshot;
    }

    public void setTimeOfSnapshot(long timeOfSnapshot) {
        this.timeOfSnapshot = timeOfSnapshot;
    }
}
