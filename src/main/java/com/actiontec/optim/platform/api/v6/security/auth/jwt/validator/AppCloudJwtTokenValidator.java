package com.actiontec.optim.platform.api.v6.security.auth.jwt.validator;

import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.incs83.app.entities.AppCloudConfig;
import io.jsonwebtoken.Jwts;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.PublicKey;

@Component
public class AppCloudJwtTokenValidator {
    private static final Logger logger = LogManager.getLogger(AppCloudJwtTokenValidator.class);

    /**
     * Validate JWT token with token, appCloudConfig
     */
    public boolean verifyUnAuthorizationToken(String token, AppCloudConfig appCloudConfig) {
        try {
            PublicKey publicKey = AppCloudJwtTokenUtils.parsePublicKey(appCloudConfig.getPublicKeyPem(), appCloudConfig.getPublicKeyAlgorithm());
            Jwts.parser()
                .setSigningKey(publicKey)
                .parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            logger.debug("JWT token validation failed, public key cannot verify token: {}", e.getMessage());
            return false;
        }
    }

    public boolean verifyExchangeToken(String token, String signingKey) {
        try {
            Jwts.parser()
                .setSigningKey(signingKey.getBytes(StandardCharsets.UTF_8))
                .parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            logger.debug("JWT exchange token validation failed: {}", e.getMessage());
            return false;
        }
    }
}
