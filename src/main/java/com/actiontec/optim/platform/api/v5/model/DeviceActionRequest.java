package com.actiontec.optim.platform.api.v5.model;

public class DeviceActionRe<PERSON> extends ActionRequest{
    private String internetAccessAction;
    private int startTime;
    private int duration = 0;

    public String getInternetAccessAction() {
        return internetAccessAction;
    }

    public void setInternetAccessAction(String internetAccessAction) {
        this.internetAccessAction = internetAccessAction;
    }

    public int getStartTime() {
        return startTime;
    }

    public void setStartTime(int startTime) {
        this.startTime = startTime;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }
}
