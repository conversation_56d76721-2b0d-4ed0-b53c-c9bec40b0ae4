package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.sql.Timestamp;

public class NCSNetworkDTO {
    private String id;
    @JsonProperty("isAuto")
    private boolean autoFlag;
    private String autoNetworkId;
    private String name;
    private String ispId;
    private String subscriberId;
    @JsonProperty("lastChangedTime")
    private Long updatedAt;

    public NCSNetworkDTO() {

    }

    public NCSNetworkDTO(Object[] object) {
        this.id = CustomStringUtils.toStringOrNull(object[0]);
        this.autoFlag = Boolean.valueOf((String) object[1]);
        this.autoNetworkId = CustomStringUtils.toStringOrNull(object[2]);
        this.name = CustomStringUtils.toStringOrNull(object[3]);
        this.ispId = CustomStringUtils.toStringOrNull(object[4]);
        this.subscriberId = CustomStringUtils.toStringOrNull(object[5]);
        this.updatedAt = object[6] != null ? ((Timestamp) object[6]).getTime() : null;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public boolean isAutoFlag() {
        return autoFlag;
    }

    public void setAutoFlag(boolean autoFlag) {
        this.autoFlag = autoFlag;
    }

    public String getAutoNetworkId() {
        return autoNetworkId;
    }

    public void setAutoNetworkId(String autoNetworkId) {
        this.autoNetworkId = autoNetworkId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Long updatedAt) {
        this.updatedAt = updatedAt;
    }
}
