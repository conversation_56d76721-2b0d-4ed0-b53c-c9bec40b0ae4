package com.actiontec.optim.platform.api.v5.controller;


import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.SmartSteeringRuleMapper;
import com.actiontec.optim.platform.api.v5.model.SmartSteeringRuleRequest;
import com.actiontec.optim.platform.api.v5.model.SmartSteeringRuleResponse;
import com.actiontec.optim.platform.model.SmartSteeringRule;
import com.actiontec.optim.platform.service.SmartSteeringRuleService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/smartSteering/rules")
public class SmartSteeringRuleController {

    @Autowired
    private SmartSteeringRuleService smartSteeringRuleService;

    @Autowired
    private SmartSteeringRuleMapper smartSteeringRuleMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<SmartSteeringRuleResponse> getAll(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SmartSteeringRuleResponse> smartSteeringRuleResponseList = null;

        List<SmartSteeringRule> smartSteeringRuleList = smartSteeringRuleService.findAll(stn);
        if(!smartSteeringRuleList.isEmpty()) {
            smartSteeringRuleResponseList = smartSteeringRuleMapper.toSmartSteeringRuleResponseList(smartSteeringRuleList);
        } else {
            smartSteeringRuleResponseList = new ArrayList<>();
        }

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", null, "200", smartSteeringRuleResponseList, httpServletRequest);
        return smartSteeringRuleResponseList;
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.CREATED)
    public List<Map<String, String>> post (
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Request Body", required = true) @RequestBody SmartSteeringRuleRequest smartSteeringRuleRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<Map<String, String>> postResponse = new ArrayList<>();

        SmartSteeringRule smartSteeringRule = smartSteeringRuleMapper.toSmartSteeringRule(smartSteeringRuleRequest);
        String macAddress = smartSteeringRuleService.save(stn, smartSteeringRule);

        Map<String, String> idResponse = new HashMap<>();
        idResponse.put("id", macAddress);

        postResponse.add(idResponse);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", smartSteeringRuleRequest, "201", postResponse, httpServletRequest);
        return postResponse;
    }

    @RequestMapping(value = "/{ruleId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<SmartSteeringRuleResponse> get(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "ruleId", required = true) @PathVariable(name = "ruleId") String ruleId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<SmartSteeringRuleResponse> smartSteeringRuleResponseList = new ArrayList<>();

        SmartSteeringRule smartSteeringRule = smartSteeringRuleService.findByDeviceMacAddress(stn, ruleId);
        if(smartSteeringRule != null) {
            SmartSteeringRuleResponse smartSteeringRuleResponse = smartSteeringRuleMapper.toSmartSteeringRuleResponse(smartSteeringRule);
            smartSteeringRuleResponseList.add(smartSteeringRuleResponse);
        }

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", null, "200", smartSteeringRuleResponseList, httpServletRequest);
        return smartSteeringRuleResponseList;
    }

    @RequestMapping(value = "/{ruleId}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    public void put (
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "ruleId", required = true) @PathVariable(name = "ruleId") String ruleId,
            @ApiParam(value = "Request Body", required = true) @RequestBody SmartSteeringRuleRequest smartSteeringRuleRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        SmartSteeringRule smartSteeringRule = smartSteeringRuleMapper.toSmartSteeringRule(smartSteeringRuleRequest);
        smartSteeringRuleService.update(stn, ruleId, smartSteeringRule);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", smartSteeringRuleRequest, "200", null, httpServletRequest);
    }

    @RequestMapping(value = "/{ruleId}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void delete (
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "ruleId", required = true) @PathVariable(name = "ruleId") String ruleId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        smartSteeringRuleService.deleteByDeviceMacAddress(stn, ruleId);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "WiFi Resource", null, "204", null, httpServletRequest);
    }
}