package com.actiontec.optim.platform.api.v5.model;

import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

public class SsidResponse extends SsidRequest {
    @ApiModelProperty
    private String id;

    @ApiModelProperty
    private String status;

    @ApiModelProperty
    private String bssid;

    @ApiModelProperty
    private Set<String> radioKeys;

    @ApiModelProperty
    private Set<String> radioIds;

    @ApiModelProperty
    private String ssidKey;

    @ApiModelProperty
    private String serialNumber;

    @ApiModelProperty
    private long lastReportTime;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }

    public Set<String> getRadioKeys() {
        return radioKeys;
    }

    public void setRadioKeys(Set<String> radioKeys) {
        this.radioKeys = radioKeys;
    }

    public Set<String> getRadioIds() {
        return radioIds;
    }

    public void setRadioIds(Set<String> radioIds) {
        this.radioIds = radioIds;
    }

    public String getSsidKey() {
        return ssidKey;
    }

    public void setSsidKey(String ssidKey) {
        this.ssidKey = ssidKey;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public long getLastReportTime() {
        return lastReportTime;
    }

    public void setLastReportTime(long lastReportTime) {
        this.lastReportTime = lastReportTime;
    }
}
