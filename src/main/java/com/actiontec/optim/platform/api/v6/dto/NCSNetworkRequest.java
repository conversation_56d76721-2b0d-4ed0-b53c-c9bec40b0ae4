package com.actiontec.optim.platform.api.v6.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.List;

public class NCSNetworkRequest {
    private String name;
    private String ispId;
    @NotNull
    private String subscriberId;
    @JsonProperty("equipment")
    @NotNull
    private List<String> equipmentSerialList;


    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getEquipmentSerialList() {
        return equipmentSerialList;
    }

    public void setEquipmentSerialList(List<String> equipmentSerialList) {
        this.equipmentSerialList = equipmentSerialList;
    }
}
