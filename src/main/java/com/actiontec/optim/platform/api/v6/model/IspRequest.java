package com.actiontec.optim.platform.api.v6.model;

import javax.validation.constraints.NotBlank;

public class IspRequest {

    private String alias;
    @NotBlank(message = "Name cannot be empty.")
    private String name;
    private String description;

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
