package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.annotation.AuditLog;
import com.actiontec.optim.platform.api.v5.mapper.EquipmentLogResponseMapper;
import com.actiontec.optim.platform.api.v5.mapper.EquipmentStatsResponseMapper;
import com.actiontec.optim.platform.api.v5.model.*;
import com.actiontec.optim.platform.model.EquipmentLog;
import com.actiontec.optim.platform.model.EquipmentStats;
import com.actiontec.optim.service.AuditService;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import com.actiontec.optim.platform.service.EquipmentService;


import javax.servlet.http.HttpServletRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.incs83.app.constants.misc.ActiontecConstants.*;

@RestController
@Api(value = "(V5) Equipment", description = "API's for Equipment Operations", tags = {"Optim - (V5) Equipment"})
@RequestMapping(value = "/actiontec/api/v5/network/{equipmentIdOrSerialOrSTN}/equipment")
@SuppressWarnings("rawtypes")
public class EquipmentController {
    @Autowired
    private AuditService auditService;

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private EquipmentLogResponseMapper equipmentLogResponseMapper;

    @Autowired
    private EquipmentStatsResponseMapper equipmentStatsResponseMapper;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getAllEquipments(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(equipmentService.getEquipmentInfo(equipmentIdOrSerialOrSTN, null, all), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(equipmentService.getEquipmentInfo(equipmentIdOrSerialOrSTN, serialNumber, system), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/{serialNumber}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @AuditLog(operation = AuditorConstants.PUT_EQUIPMENT)
    public void putEquipment(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Put epuipment properties", required = true) @RequestBody NameBase request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        equipmentService.updateFriendlyName(equipmentIdOrSerialOrSTN, serialNumber, request);
    }

    @RequestMapping(value = "/{serialNumber}/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @AuditLog(operation = AuditorConstants.POST_EQUIPMENT_ACTION)
    public void postEquipment(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Post epuipment actions properties", required = true) @RequestBody EquipmentActionRequest request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        equipmentService.postActionsForEquipment(equipmentIdOrSerialOrSTN, serialNumber, request);
    }

    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.CREATED)
    public Map<String, String> postEquipment(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Post epuipment properties", required = true) @RequestBody EquipmentBase request,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, String> result = new HashMap<>();

        String id = equipmentService.postEquipment(equipmentIdOrSerialOrSTN, request);
        result.put("id", id);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), request.getSerialNumber(), AuditorConstants.POST_EQUIPMENT, null, "201", "", httpServletRequest);
        return result;
    }

    @RequestMapping(value = "/{equipmentId}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = TechnicianDashboard.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteEquipment(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "equipmentId of Equipment", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        equipmentService.deleteEquipment(equipmentIdOrSerialOrSTN, equipmentId);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, AuditorConstants.DELETE_EQUIPMENT, null, "204", "", httpServletRequest);

    }

    @RequestMapping(value = "/{equipmentId}/systemLogs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<EquipmentLogResponse> getEquipmentLogs(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "equipmentId of Equipment", required = true) @PathVariable(name = "equipmentId") String equipmentId,
            @RequestParam(name = "duration", defaultValue = "3600", required = false) Long duration,
            @RequestParam(name = "type", defaultValue = "", required = false) String type,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<EquipmentLog> equipmentLogList = equipmentService.getEquipmentLogs(equipmentIdOrSerialOrSTN, equipmentId, duration, type);
        List<EquipmentLogResponse> equipmentLogResponseList = equipmentLogResponseMapper.toEquipmentLogResponse(equipmentLogList);
        return equipmentLogResponseList;
    }

    @RequestMapping(value = "/{serialNumber}/series", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<EquipmentStatsResponse> getEquipmentStats(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @RequestParam(name = "duration", defaultValue = "3600", required = false) Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<EquipmentStats> equipmentStatsList = equipmentService.getEquipmentStats(equipmentIdOrSerialOrSTN, serialNumber, duration);
        List<EquipmentStatsResponse> equipmentStatsResponseList = equipmentStatsResponseMapper.toEquipmentStatsResponse(equipmentStatsList);
        return equipmentStatsResponseList;
    }
}