package com.actiontec.optim.platform.api.v6.validator;

import com.actiontec.optim.util.AppCloudJwtTokenUtils;
import com.incs83.app.entities.AppCloudConfig;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import java.security.PublicKey;

@Component
public class AppCloudJwtTokenValidator {
    private static final Logger logger = LogManager.getLogger(AppCloudJwtTokenValidator.class);

    /**
     * Validate JWT token with token, appCloudConfig
     */
    public boolean verifyUnAuthorizationToken(String token, AppCloudConfig appCloudConfig) {
        try {
            PublicKey publicKey = AppCloudJwtTokenUtils.parsePublicKey(appCloudConfig.getPublicKeyPem(), appCloudConfig.getPublicKeyAlgorithm());
            Jws<Claims> jwsClaims = Jwts.parser()
                    .setSigningKey(publicKey)
                    .parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            logger.debug("JWT token validation failed, public key cannot verify token: {}", e.getMessage());
            return false;
        }
    }

    public boolean verifyExchangeToken(String token, AppCloudConfig appCloudConfig) {
        return false;
    }
}
