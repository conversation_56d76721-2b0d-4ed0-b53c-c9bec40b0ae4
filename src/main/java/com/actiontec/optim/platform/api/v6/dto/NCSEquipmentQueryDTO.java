package com.actiontec.optim.platform.api.v6.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;

@ApiModel(description = "Equipment query parameters")
public class NCSEquipmentQueryDTO extends PaginationRequest {

    @ApiModelProperty(value = "Specified ISP ID")
    @Column(name = "isp_id")
    private String ispId;

    @ApiModelProperty(value = "Specified network ID")
    @Column(name = "network_id")
    private String networkId;

    @ApiModelProperty(value = "Filter of equipment serial number, support fuzzy search")
    @Column(name = "serial")
    private String serialNumber;

    @ApiModelProperty(value = "Filter of equipment model name, support fuzzy search")
    @Column(name = "model_name")
    private String modelName;

    @ApiModelProperty(value = "Filter of equipment inUse state")
    @Column(name = "in_use")
    private Boolean inUse;

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(String networkId) {
        this.networkId = networkId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public Boolean getInUse() {
        return inUse;
    }

    public void setInUse(Boolean inUse) {
        this.inUse = inUse;
    }
}
