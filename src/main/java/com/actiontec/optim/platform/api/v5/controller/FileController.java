package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.api.v6.enums.FileType;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.model.FwFile;
import com.actiontec.optim.platform.service.AwsS3Service;
import com.actiontec.optim.platform.service.BatchEquipmentService;
import com.actiontec.optim.platform.service.BatchEquipmentFileService;
import com.actiontec.optim.platform.service.FwFileService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Acs;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.BatchEquipmentUploadLog;
import com.incs83.app.entities.OptimFile;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@RequestMapping(value = "/actiontec/api/v5/files")
public class FileController {
    private final Logger logger = LogManager.getLogger(this.getClass());
    @Autowired
    private FwFileService fwFileService;

    @Autowired
    private BatchEquipmentFileService batchEquipmentFileService;

    @Autowired
    BatchEquipmentService batchEquipmentService;

    @Autowired
    private AwsS3Service awsS3Service;

    @Autowired
    private AuditService auditService;

    @Autowired
    ManageCommonService manageCommonService;

    @RequestMapping(value = "/{fileId}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    public ResponseEntity getFile(
            @ApiParam(value = "fileId", required = true) @PathVariable(name = "fileId") String fileId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        FwFile fwFile = fwFileService.findFwFileById(fileId);
        if (fwFile == null) {
            logger.debug("failed to find file, fileId:[{}]", fileId);
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get file fail id=" + fileId);
        }

        if (!CommonUtils.isSysAdmin()) {
            String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
            String userIspId = manageCommonService.getIspByGroupId(userGroupId);
            if (StringUtils.equalsIgnoreCase(fwFile.getType(), FileType.batchEquipment.name())) {
                // OC-7490: batch equipment always download report file, not upload file, so just check report file id
                BatchEquipmentUploadLog batchEquipmentUploadLog = batchEquipmentService.getBatchEquipmentUploadLogByReportFileId(fileId);
                if (ObjectUtils.isEmpty(batchEquipmentUploadLog)) {
                    String errorMsg = "failed to find batch Equipment upload log for fileId: " + fileId;
                    logger.error(errorMsg);
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), errorMsg);
                }

                if (!StringUtils.equalsIgnoreCase(userIspId, batchEquipmentUploadLog.getIspId())) {
                    String errorMsg = "The file's ispId " + batchEquipmentUploadLog.getIspId()  + " does not belong to the current user ispId " + userIspId;
                    logger.error(errorMsg);
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), errorMsg);
                }
            }
        }

        try {
            byte[] data = awsS3Service.getFile(fwFile);
            ByteArrayResource resource = new ByteArrayResource(data);

            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "File Data", null, "200", null, httpServletRequest);
            return ResponseEntity.status(HttpStatus.OK)
                    .contentType(MediaType.parseMediaType("application/octet-stream"))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fwFile.getFileName() + "\"")
                    .contentLength(data.length)
                    .body(data);
        } catch (IOException ex) {
            logger.error("failed to get file from aws, fileId:[{}]", fileId);
            throw new OptimApiException(HttpStatus.BAD_REQUEST, "Get file fail id=" + fileId);
        }
    }

    @RequestMapping(value = "/{fileId}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Acs.class)
    public void updateFile(
            @ApiParam(value = "fileId", required = true) @PathVariable(name = "fileId") String fileId,
            @RequestParam("file") MultipartFile file,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        OptimFile optimFile = batchEquipmentFileService.getOptimFileById(fileId);
        if (StringUtils.equals(ApplicationConstants.OptimFileType.batchEquipment.name(), optimFile.getType())) {
            String userGroupId = CommonUtils.getGroupIdOfLoggedInUser();
            String userIspId = manageCommonService.getIspByGroupId(userGroupId);
            batchEquipmentService.batchEquipmentFileUpload(userIspId, fileId, file);
        } else {
            Boolean ret = fwFileService.updateFwFile(fileId, file);
            if (!ret) {
                throw new OptimApiException(HttpStatus.BAD_REQUEST, "PUT file fail id=" + fileId);
            }
        }
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), null, "File Data", null, "200", null, httpServletRequest);
    }
}
