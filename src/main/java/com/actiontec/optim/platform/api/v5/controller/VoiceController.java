package com.actiontec.optim.platform.api.v5.controller;


import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.VoiceResponseMapper;
import com.actiontec.optim.platform.api.v5.model.VoiceClientResponse;
import com.actiontec.optim.platform.api.v5.model.VoiceNetworkResponse;
import com.actiontec.optim.platform.api.v5.model.VoiceResponse;
import com.actiontec.optim.platform.api.v5.model.VoiceStatsResponse;
import com.actiontec.optim.platform.model.NetworkVoice;
import com.actiontec.optim.platform.model.NetworkVoiceClient;
import com.actiontec.optim.platform.model.NetworkVoiceNet;
import com.actiontec.optim.platform.model.NetworkVoiceStats;
import com.actiontec.optim.platform.service.NetworkVoiceService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/services/voice")
public class VoiceController {

    @Autowired
    private NetworkVoiceService networkVoiceService;

    @Autowired
    private VoiceResponseMapper voiceResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public VoiceResponse getVoice(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkVoice networkVoice = networkVoiceService.findVoiceByStn(stn);
        VoiceResponse voiceResponse = voiceResponseMapper.toVoiceResponse(networkVoice);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Service Resource", null, "200", voiceResponse, httpServletRequest);
        return voiceResponse;
    }

    @RequestMapping(value = "/series", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<VoiceStatsResponse> getVoiceStats(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 3600 seconds")
            @RequestParam(required = false, name = "duration", defaultValue = "3600") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkVoiceStats> networkVoiceStatsList = networkVoiceService.findVoiceStatsByStnAndDuration(stn, duration);
        List<VoiceStatsResponse> voiceStatsResponseList = voiceResponseMapper.toVoiceStatsResponse(networkVoiceStatsList);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Service Resource", null, "200", voiceStatsResponseList, httpServletRequest);
        return voiceStatsResponseList;
    }

    @RequestMapping(value = "/clients", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<VoiceClientResponse> getVoiceClients(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkVoiceClient> networkVoiceClientList = networkVoiceService.findVoiceClientByStn(stn);
        List<VoiceClientResponse> voiceClientResponseList = voiceResponseMapper.toVoiceClientResponse(networkVoiceClientList);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Service Resource", null, "200", voiceClientResponseList, httpServletRequest);
        return voiceClientResponseList;
    }

    @RequestMapping(value = "/clients/{clientId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<VoiceClientResponse> getVoiceClient(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "clientId", required = true) @PathVariable(name = "clientId") int clientId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        List<NetworkVoiceClient> networkVoiceClientList = networkVoiceService.findVoiceClientByStnAndClientId(stn, clientId);
        List<VoiceClientResponse> voiceClientResponseList = voiceResponseMapper.toVoiceClientResponse(networkVoiceClientList);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Service Resource", null, "200", voiceClientResponseList, httpServletRequest);
        return voiceClientResponseList;
    }

    @RequestMapping(value = "/network", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public VoiceNetworkResponse getVoiceNetwork(
            @ApiParam(value = "stn", required = true) @PathVariable(name = "stn") String stn,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkVoiceNet networkVoiceNet = networkVoiceService.findVoiceNetByStn(stn);
        VoiceNetworkResponse voiceNetworkResponse = voiceResponseMapper.toVoiceNetworkResponse(networkVoiceNet);

        String userId = at3Adapter.getRgwSerialByStn(stn);
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Service Resource", null, "200", voiceNetworkResponse, httpServletRequest);
        return voiceNetworkResponse;
    }
}
