package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.annotation.AuditLog;
import com.actiontec.optim.platform.api.v5.model.FirmwareActionRequest;
import com.actiontec.optim.platform.model.firmware.FwInfo;
import com.actiontec.optim.platform.model.firmware.FwLog;
import com.actiontec.optim.platform.service.FirmwareService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.Acs;
import com.incs83.app.constants.misc.AuditorConstants;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}/firmwares")
public class FirmwareController {

    @Autowired
    private FirmwareService firmwareService;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    @AuditLog(operation = AuditorConstants.GET_FIRMWARE)
    public List<FwInfo> getFirmwares(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return firmwareService.getFirmwares(stn, equipmentId);
    }

    @RequestMapping(value = "/logs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    @AuditLog(operation = AuditorConstants.GET_FIRMWARE)
    public List<FwLog> getFirmwareLog(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return firmwareService.getFirmwareLogs(stn, equipmentId, null);
    }

    @RequestMapping(value = "/logs/{actionId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Acs.class)
    @AuditLog(operation = AuditorConstants.GET_FIRMWARE)
    public List<FwLog> getFirmwareLog(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "actionId", required = true) @PathVariable(name = "actionId")
                    String actionId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return firmwareService.getFirmwareLogs(stn, equipmentId, actionId);
    }

    @RequestMapping(value = "/actions", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Acs.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @AuditLog(operation = AuditorConstants.POST_FIRMWARE)
    public Map<String, String> postAction(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Actions properties", required = true) @RequestBody FirmwareActionRequest actionRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception{
        return firmwareService.postAction(stn, equipmentId, actionRequest);
    }
}
