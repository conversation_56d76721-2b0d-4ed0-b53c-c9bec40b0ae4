package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.ConnectionUpstreamResponseMapper;
import com.actiontec.optim.platform.api.v5.model.ConnectionUpstreamResponse;
import com.actiontec.optim.platform.exception.NoSuchEntityException;
import com.actiontec.optim.platform.model.equipment.NetworkUpstreamContext;
import com.actiontec.optim.platform.model.equipment.NetworkUpstreamInterface;
import com.actiontec.optim.platform.service.NetworkStreamService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/equipment/{equipmentId}/upstreamConnections")
public class UpstreamController {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private NetworkStreamService networkStreamService;

    @Autowired
    private ConnectionUpstreamResponseMapper upstreamResponseMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<ConnectionUpstreamResponse> getAll(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkUpstreamContext upstreamContext = networkStreamService.findUpstreamContextByStnAndSerial(stn, equipmentId);
        List<ConnectionUpstreamResponse> upstreamResponses = upstreamResponseMapper.toConnectionUpstreamResponse(upstreamContext.getInterfaces(), upstreamContext.getIpv6DnsServers(), upstreamContext.getWanDto(), upstreamContext.getLastReportTime());

        if (upstreamResponses == null) {
            upstreamResponses = new ArrayList<>();
        }

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Network Connection Resource", null, "200", upstreamResponses, httpServletRequest);
        return upstreamResponses;
    }

    @RequestMapping(value = "/{upstreamId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<ConnectionUpstreamResponse> get(
            @ApiParam(value = "STN", required = true) @PathVariable(name = "stn")
                    String stn,
            @ApiParam(value = "equipmentId", required = true) @PathVariable(name = "equipmentId")
                    String equipmentId,
            @ApiParam(value = "id", required = true) @PathVariable(name = "upstreamId")
                    String upstreamId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        NetworkUpstreamContext upstreamContext = networkStreamService.findUpstreamContextByStnAndSerial(stn, equipmentId);
        if(upstreamContext.getInterfaces() == null) {
            throw new NoSuchEntityException();
        }

        Optional<NetworkUpstreamInterface> upstreamInterfaceOpt = upstreamContext.getInterfaces().stream()
                .filter(x -> StringUtils.equals(x.getId(), upstreamId))
                .findAny();

        if (upstreamInterfaceOpt.isPresent()) {
            ConnectionUpstreamResponse connectionUpstreamResponse = upstreamResponseMapper.toConnectionUpstreamResponse(upstreamInterfaceOpt.get(), upstreamContext.getIpv6DnsServers(), upstreamContext.getWanDto(), upstreamContext.getLastReportTime());
            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), equipmentId, "Network Connection Resource", null, "200", connectionUpstreamResponse, httpServletRequest);
            return Stream.of(connectionUpstreamResponse).collect(Collectors.toList());
        } else {
            throw new NoSuchEntityException();
        }

    }

}
