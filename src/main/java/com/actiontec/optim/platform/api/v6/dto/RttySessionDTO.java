package com.actiontec.optim.platform.api.v6.dto;

import com.actiontec.optim.util.CustomStringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.incs83.app.entities.RttySession;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import java.sql.Timestamp;

public class RttySessionDTO {
    private String id;
    private String equipmentId;
    private String serialNumber;
    private String ispId;
    @JsonProperty("createdBy")
    private Creator creator;
    @ApiModelProperty(value = "Session creation time (timestamp)")
    private Long createdAt;
    @ApiModelProperty(value = "Session expiration time (timestamp)")
    private Long expiredAt;

    public RttySessionDTO() {
    }

    public RttySessionDTO(Object[] object) {
        this.id = CustomStringUtils.toStringOrNull(object[0]);
        this.equipmentId = CustomStringUtils.toStringOrNull(object[1]);
        this.serialNumber = CustomStringUtils.toStringOrNull(object[2]);
        this.ispId = CustomStringUtils.toStringOrNull(object[3]);
        this.creator = new Creator();
        this.creator.setId(CustomStringUtils.toStringOrNull(object[4]));
        this.creator.setName(CustomStringUtils.toStringOrNull(object[5]));
        this.createdAt = object[6] != null ? ((Timestamp) object[6]).getTime() : null;
        this.expiredAt = object[7] != null ? ((Timestamp) object[7]).getTime() : null;
    }

    public static class Creator {
        private String id;
        private String name;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEquipmentId() {
        return equipmentId;
    }

    public void setEquipmentId(String equipmentId) {
        this.equipmentId = equipmentId;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public Creator getCreator() {
        return creator;
    }

    public void setCreator(Creator creator) {
        this.creator = creator;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Long expiredAt) {
        this.expiredAt = expiredAt;
    }
}
