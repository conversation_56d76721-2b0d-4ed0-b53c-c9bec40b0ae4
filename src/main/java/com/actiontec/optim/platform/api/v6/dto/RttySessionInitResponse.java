package com.actiontec.optim.platform.api.v6.dto;

import com.incs83.app.entities.RttySession;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.ZoneOffset;

/**
 * Response DTO for remote debug session creation
 */
@ApiModel(description = "Remote Debug Session Response")
public class RttySessionInitResponse {

    @ApiModelProperty(value = "Session ID")
    private String id;

    @ApiModelProperty(value = "Session token")
    private String token;

    @ApiModelProperty(value = "Username for authentication")
    private String username;

    @ApiModelProperty(value = "Password for authentication")
    private String password;

    @ApiModelProperty(value = "Session creation timestamp (UTC)")
    private Long createdAt;

    @ApiModelProperty(value = "Session expiration timestamp (UTC)")
    private Long expiredAt;

    public RttySessionInitResponse() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public Long getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Long expiredAt) {
        this.expiredAt = expiredAt;
    }
}
