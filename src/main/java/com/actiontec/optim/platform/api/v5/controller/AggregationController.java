package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.AirTimeBusyMapper;
import com.actiontec.optim.platform.api.v5.mapper.ChannelSelectDistributionMapper;
import com.actiontec.optim.platform.api.v5.mapper.SteeringLogsCountMapper;
import com.actiontec.optim.platform.api.v5.model.AirTimeBusyDistributionResponse;
import com.actiontec.optim.platform.api.v5.model.AirTimeBusyResponse;
import com.actiontec.optim.platform.api.v5.model.ChannelSelectDistributionResponse;
import com.actiontec.optim.platform.api.v5.model.SteeringLogsCountResponse;
import com.actiontec.optim.platform.model.AirTimeBusy;
import com.actiontec.optim.platform.model.AirTimeBusyDistribution;
import com.actiontec.optim.platform.model.ChannelSelectDistribution;
import com.actiontec.optim.platform.model.SteeringLogsCount;
import com.actiontec.optim.platform.service.AggregationService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.NetworkDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v5/aggregation")
public class AggregationController {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    AggregationService aggregationService;

    @Autowired
    SteeringLogsCountMapper steeringLogsCountMapper;

    @Autowired
    ChannelSelectDistributionMapper channelSelectDistributionMapper;

    @Autowired
    AirTimeBusyMapper airTimeBusyMapper;

    @Autowired
    private AuditService auditService;

    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(value = "/steeringLogsCount", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public List<SteeringLogsCountResponse> getSteeringLogsCount(
        @ApiParam(value = "The scope of aggregated data") @RequestParam(name = "scope", defaultValue = "cluster") String scope,
        @ApiParam(value = "Unique key to identify a scope") @RequestParam(name = "scopeId", defaultValue = "0") String scopeId,
        @ApiParam(value = "Unique key to identify a home network.") @RequestParam(name = "stn", defaultValue = "") String stn,
        @ApiParam(value = "Event type projection.") @RequestParam(name = "eventType", defaultValue = "Steer,Roam") String eventType,
        @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
        @RequestHeader(name = "X-Authorization") String accessToken,
        HttpServletRequest httpServletRequest) throws Exception
    {
        List<SteeringLogsCount> steeringLogsCountList = aggregationService.getSteeringLogsCount(scope, scopeId, stn, eventType);
        List<SteeringLogsCountResponse> steeringLogsCountResponseList = steeringLogsCountMapper.toSteeringLogsCountResponse(steeringLogsCountList);

        String userId = (!stn.isEmpty()) ? at3Adapter.getRgwSerialByStn(stn) : null;
        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), userId, "Device Resource", null, "200", steeringLogsCountResponseList, httpServletRequest);
        return steeringLogsCountResponseList;
    }

    @RequestMapping(value = "/channelSelectDistribution", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public List<ChannelSelectDistributionResponse> getChannelSelectDistribution(
        @ApiParam(value = "The scope of aggregated data") @RequestParam(name = "scope", defaultValue = "cluster") String scope,
        @ApiParam(value = "Unique key to identify a scope") @RequestParam(name = "scopeId", defaultValue = "0") String scopeId,
        @ApiParam(value = "Radios") @RequestParam(name = "radios", defaultValue = "2.4G,5G,6G") String radios,
        @ApiParam(value = "Equipment type") @RequestParam(name = "equipmentType", defaultValue = "RG,EXT") String equipmentType,
        @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
        @RequestHeader(name = "X-Authorization") String accessToken,
        HttpServletRequest httpServletRequest) throws Exception
    {
        List<ChannelSelectDistribution> channelSelectDistributionList = aggregationService.getChannelSelectDistribution(scope, scopeId, radios, equipmentType);
        List<ChannelSelectDistributionResponse> channelSelectDistributionResponseList = channelSelectDistributionMapper.toChannelSelectDistributionResponse(channelSelectDistributionList);

        return channelSelectDistributionResponseList;
    }

    @RequestMapping(value = "/airTimeBusy", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public List<AirTimeBusyResponse> getAirTimeBusy(
            @ApiParam(value = "The scope of aggregated data") @RequestParam(name = "scope", defaultValue = "cluster") String scope,
            @ApiParam(value = "Unique key to identify a scope") @RequestParam(name = "scopeId", defaultValue = "0") String scopeId,
            @ApiParam(value = "Radios") @RequestParam(name = "radios", defaultValue = "2.4G,5G,6G") String radios,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception
    {
        List<AirTimeBusy> airTimeBusyList = aggregationService.getAirTimeBusy(scope, scopeId, radios);
        List<AirTimeBusyResponse> airTimeBusyResponseList = airTimeBusyMapper.toAirTimeBusyResponse(airTimeBusyList);

        return airTimeBusyResponseList;
    }

    @RequestMapping(value = "/airTimeBusyDistribution", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public List<AirTimeBusyDistributionResponse> getAirTimeBusyDistribution(
            @ApiParam(value = "The scope of aggregated data") @RequestParam(name = "scope", defaultValue = "cluster") String scope,
            @ApiParam(value = "Unique key to identify a scope") @RequestParam(name = "scopeId", defaultValue = "0") String scopeId,
            @ApiParam(value = "Radios") @RequestParam(name = "radios", defaultValue = "2.4G,5G,6G") String radios,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception
    {
        List<AirTimeBusyDistribution> airTimeBusyDistributionList = aggregationService.getAirTimeBusyDistribution(scope, scopeId, radios);
        List<AirTimeBusyDistributionResponse> airTimeBusyDistributionResponseList = airTimeBusyMapper.toAirTimeBusyDistributionResponse(airTimeBusyDistributionList);

        return airTimeBusyDistributionResponseList;
    }
}
