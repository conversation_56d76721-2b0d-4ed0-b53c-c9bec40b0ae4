package com.actiontec.optim.platform.api.v5.model;

import java.util.List;

public class SmmServiceVo extends SmmServiceBase {
    private String id;
    private List<String> versions;
    private Boolean deletable;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<String> getVersions() {
        return versions;
    }

    public void setVersions(List<String> versions) {
        this.versions = versions;
    }
    public Boolean getDeletable() {
        return deletable;
    }

    public void setDeletable(Boolean deletable) {
        this.deletable = deletable;
    }
}
