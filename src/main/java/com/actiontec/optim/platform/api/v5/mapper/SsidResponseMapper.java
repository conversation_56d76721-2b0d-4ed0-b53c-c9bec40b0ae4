package com.actiontec.optim.platform.api.v5.mapper;

import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.platform.api.v5.model.SsidResponse;
import com.actiontec.optim.platform.model.NetworkSsid;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public abstract class SsidResponseMapper {

    @Mapping(target = "id", source = "virtualKey")
    public abstract SsidResponse toSsidResponse(NetworkSsid networkSsid);

    public abstract List<SsidResponse> toSsidResponse(List<NetworkSsid> networkSsids);

    @AfterMapping
    protected void afterMapping(NetworkSsid networkSsid, @MappingTarget SsidResponse ssidResponse) {
        if (networkSsid.getBssids().size() == 1) {
            ssidResponse.setBssid(networkSsid.getBssids().get(0));
        }

        ssidResponse.setStatus(networkSsid.getEnabled()==true?"Up":"Down");
        Set<String> radioKeys = new LinkedHashSet<>();
        for (RadioEnum radioEnum : networkSsid.getRadioEnums()) {
            if (radioEnum != null) {
                radioKeys.add(radioEnum.getRadioKey());
            }
        }
        ssidResponse.setRadioKeys(radioKeys);

        Set<String> radioIds = new LinkedHashSet<>();
        for (String radioVirtualKey : networkSsid.getRadioVirtualKeys()) {
            radioIds.add(radioVirtualKey);
        }
        ssidResponse.setRadioIds(radioIds);
        ssidResponse.setSerialNumber(networkSsid.getSerial());
    }
}