package com.actiontec.optim.platform.api.v5.controller;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.model.AlertHistory;
import com.actiontec.optim.platform.service.AlertService;
import com.actiontec.optim.service.AuditService;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.util.CommonUtils;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v5/network/{stn}/alerts")
public class AlertController {
    @Autowired
    private AlertService alertService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private At3Adapter at3Adapter;

    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public List<AlertHistory> getAlerts(
             @ApiParam(value = "STN", required = true) @PathVariable(name = "stn") String stn,
             @ApiParam(value = "Fetch active alerts only or not", required = false)  @RequestParam(name = "active", required = false) boolean active,
             @ApiParam(value = "Severity filter", required = false)  @RequestParam(name = "severity", required = false) String severity,
             @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
             @RequestParam(required = false, name = "duration", defaultValue = "3600") Long duration,
             @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
             @RequestHeader(name = "X-Authorization") String accessToken,
             HttpServletRequest httpServletRequest) throws Exception {
            List<AlertHistory> responseList = alertService.getAlerts(stn, active, severity, duration);
            auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), "Equipment and Device Alarms", null, "200", responseList, httpServletRequest);

            return responseList;
            }
}
