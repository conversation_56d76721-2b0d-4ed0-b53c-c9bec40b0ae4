package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.queries.BrokerInfoSQL;
import com.incs83.app.entities.BrokerInfo;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.ObjectUtils;
import com.actiontec.optim.util.SensitiveDataCrypto;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;

import static com.incs83.app.utils.SqlStatementUtils.*;

@Repository
public class BrokerInfoRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public PaginationResponse<BrokerInfoDTO> getAllBrokerInfos(BrokerInfoQueryDTO queryDTO) throws Exception {
        StringBuilder query = new StringBuilder(BrokerInfoSQL.GET_BROKER_INFOS);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(Condition.AND, Condition.WHERE, StringUtils.EMPTY);
        
        // use reflection to generate dynamic query string.
        Field[] fields = queryDTO.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldValue = field.get(queryDTO);
            if (field.getDeclaringClass().equals(PaginationRequest.class) || ObjectUtils.isEmpty(fieldValue)) {
                continue;
            }

            String columnNativeName = getNativeColumnName(field);

            if (StringUtils.equals(field.getName(), "name")) {
                fieldValue = fieldValue + "%";
                whereClause.add(buildLikeStatement(Alias.BrokerInfo, columnNativeName, field.getName()));
            }

            logger.info("field name: {}, field value: {}", field.getName(), fieldValue.toString());
            params.put(field.getName(), fieldValue);
        }

        // query without any parameters
        if (params.isEmpty()) {
            whereClause = new StringJoiner(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        }

        String page = buildPageStatement(queryDTO.getOffset(), queryDTO.getLimit());

        String finalQuery = query.append(whereClause).append(page).toString();
        logger.info("getAllBrokerInfos finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<BrokerInfoDTO> paginationResponse = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<BrokerInfoDTO> brokerInfoDTOList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(rawDataList)) {
            for (Object[] rawData : rawDataList) {
                BrokerInfoDTO brokerInfoDTO = new BrokerInfoDTO(rawData);
                brokerInfoDTOList.add(brokerInfoDTO);
            }

            paginationDTO.setTotal(countBrokerInfos(BrokerInfoSQL.BROKER_COUNT, whereClause, params));
            paginationDTO.setListSize(brokerInfoDTOList.size());
        }
        paginationDTO.setOffset(queryDTO.getOffset());
        paginationDTO.setLimit(queryDTO.getLimit());

        paginationResponse.setPagination(paginationDTO);
        paginationResponse.setData(brokerInfoDTOList);

        return paginationResponse;
    }

    public Integer countBrokerInfos(String countQuery, StringJoiner whereClause, HashMap<String, Object> params) throws Exception {
        StringBuilder query = new StringBuilder(countQuery);
        String finalQuery = query.append(whereClause).toString();
        return Integer.valueOf(dataAccessService.readNative(finalQuery, params).iterator().next().toString());
    }

    public BrokerInfoDTO getBrokerInfoById(String brokerId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", brokerId);
        
        List<Object[]> rawDataList = dataAccessService.readNative(BrokerInfoSQL.GET_BROKER_INFO_BY_ID, params);
        BrokerInfoDTO brokerInfoDTO = null;

        if (CollectionUtils.isNotEmpty(rawDataList)) {
            brokerInfoDTO = new BrokerInfoDTO(rawDataList.get(0)); // 假設 BrokerInfoDTO 有一個適當的構造函數
        }

        return brokerInfoDTO;
    }

    public boolean existsByEnvName(String envName) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("envName", envName);
        List<Object> result = dataAccessService.readNative(BrokerInfoSQL.CHECK_ENV_NAME_EXISTS, params);
        
        if (CollectionUtils.isNotEmpty(result)) {
            Object count = result.get(0);
            return count != null && ((Number) count).longValue() > 0;
        }
        return false;
    }

    public String createBrokerInfo(BrokerInfoRequest request) throws Exception {
        BrokerInfo brokerInfo = new BrokerInfo();
        brokerInfo.setId(CommonUtils.generateUUID());
        brokerInfo.setEnvName(request.getEnvName());
        brokerInfo.setBrokerUrl(request.getBrokerUrl());
        brokerInfo.setPort(request.getPort());
        brokerInfo.setUsername(request.getUserName());
        // Store password with AES encryption
        brokerInfo.setPassword(SensitiveDataCrypto.encrypt(request.getPassword()));
        brokerInfo.setIsDefault(request.getIsDefault());
        brokerInfo.setCreatedAt(LocalDateTime.now());
        brokerInfo.setCreatedBy(CommonUtils.getUserIdOfLoggedInUser());

        dataAccessService.create(BrokerInfo.class, brokerInfo);
        return brokerInfo.getId();
    }

    public void updateBrokerInfo(String brokerId, BrokerInfoRequest request) throws Exception {
        BrokerInfo brokerInfo = (BrokerInfo) dataAccessService.read(BrokerInfo.class, brokerId);
        brokerInfo.setEnvName(request.getEnvName());
        brokerInfo.setBrokerUrl(request.getBrokerUrl());
        brokerInfo.setPort(request.getPort());
        brokerInfo.setUsername(request.getUserName());
        // Update password with AES encryption
        if (CustomStringUtils.isNotEmpty(request.getPassword())) {
            brokerInfo.setPassword(SensitiveDataCrypto.encrypt(request.getPassword()));
        }
        brokerInfo.setUpdatedAt(LocalDateTime.now());
        brokerInfo.setUpdatedBy(CommonUtils.getUserIdOfLoggedInUser());
        
        dataAccessService.update(BrokerInfo.class, brokerInfo);
    }

    public void clearDefaultStatus() throws Exception {
        dataAccessService.readNative(BrokerInfoSQL.CLEAR_DEFAULT_STATUS, new HashMap<>());
    }

    public void delete(String brokerId) throws Exception {
        dataAccessService.delete(BrokerInfo.class, brokerId);
    }

    public String getDefaultBrokerId() throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        List<Object> results = dataAccessService.readNative(BrokerInfoSQL.GET_DEFAULT_BROKER_INFO_ID, params);
        return results.isEmpty() ? CustomStringUtils.EMPTY : results.get(0).toString();
    }
}