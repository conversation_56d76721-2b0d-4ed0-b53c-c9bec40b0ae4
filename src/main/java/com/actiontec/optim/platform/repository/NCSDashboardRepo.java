package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dto.CpeSmmAppStatsDto;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v6.dto.NCSDashboardDTO;
import com.incs83.service.MongoService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Component
public class NCSDashboardRepo {

    @Autowired
    private MongoService mongoService;

    @Autowired
    private At3Adapter at3Adapter;

    public NCSDashboardDTO.NetworkStats getNetworkStats(String ispId, String clusterId) {

        DBObject latestNetworkState = null;

        if (clusterId != null && clusterId.equals("0")) {
            List<DBObject> aggregatePipeline = new ArrayList<>();

            aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject("ispId", new BasicDBObject("$ne", null))));

            aggregatePipeline.add(new BasicDBObject("$sort", new BasicDBObject("createdAt", -1)));

            BasicDBObject groupByIspId = new BasicDBObject();
            groupByIspId.put("_id", "$ispId");
            groupByIspId.put("latestRecord", new BasicDBObject("$first", "$$ROOT"));
            aggregatePipeline.add(new BasicDBObject("$group", groupByIspId));

            aggregatePipeline.add(new BasicDBObject("$replaceRoot", new BasicDBObject("newRoot", "$latestRecord")));

            BasicDBObject groupTotal = new BasicDBObject();
            groupTotal.put("_id", null);
            groupTotal.put("autoCreated", new BasicDBObject("$sum", "$autoCreated"));
            groupTotal.put("manuallyCreated", new BasicDBObject("$sum", "$manuallyCreated"));
            groupTotal.put("online", new BasicDBObject("$sum", "$online"));
            aggregatePipeline.add(new BasicDBObject("$group", groupTotal));

            DBCollection dbCollection = at3Adapter.getMongoDbCollection(NETWORK_CONNECTIVITY_STATS);
            Iterator iterator = dbCollection.aggregate(aggregatePipeline).results().iterator();

            while (iterator.hasNext()) {
                latestNetworkState = (DBObject) iterator.next();
            }
        } else {
            HashMap<String, Object> queryParams = new HashMap<>();
            HashMap<String, Object> appendableParams = new HashMap<>();

            if (StringUtils.isNotEmpty(ispId)) {
                queryParams.put("ispId", ispId);
            } else {
                queryParams.put("ispId", new BasicDBObject("$ne", null));
            }

            if (StringUtils.isNotEmpty(clusterId)) {
                queryParams.put("clusterId", clusterId);
            } else {
                queryParams.put("clusterId", new BasicDBObject("$ne", null));
            }

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.put("_id", 0);

            latestNetworkState = mongoService.findOne(queryParams, appendableParams, NETWORK_CONNECTIVITY_STATS, CREATEDAT, DESC, mongoFieldOptions);
        }

        if (latestNetworkState != null) {
            return convertToNetworkStats(latestNetworkState);
        }

        return new NCSDashboardDTO.NetworkStats();
    }

    public NCSDashboardDTO.EquipmentStats getEquipmentStats(String ispName) {

        DBObject latestEquipmentState = null;

        if (StringUtils.isEmpty(ispName)) {
            List<DBObject> aggregatePipeline = new ArrayList<>();

            aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject("isp", new BasicDBObject("$ne", null))));

            aggregatePipeline.add(new BasicDBObject("$sort", new BasicDBObject("dateCreated", -1)));

            BasicDBObject groupByIsp = new BasicDBObject();
            groupByIsp.put("_id", "$isp");
            groupByIsp.put("latestRecord", new BasicDBObject("$first", "$$ROOT"));
            aggregatePipeline.add(new BasicDBObject("$group", groupByIsp));

            aggregatePipeline.add(new BasicDBObject("$replaceRoot", new BasicDBObject("newRoot", "$latestRecord")));

            BasicDBObject groupTotal = new BasicDBObject();
            groupTotal.put("_id", null);
            groupTotal.put("connected", new BasicDBObject("$sum", "$connected"));
            groupTotal.put("disconnected", new BasicDBObject("$sum", "$disconnected"));
            aggregatePipeline.add(new BasicDBObject("$group", groupTotal));

            DBCollection dbCollection = at3Adapter.getMongoDbCollection(RGW_CONNECTIVITY_STATS);
            Iterator iterator = dbCollection.aggregate(aggregatePipeline).results().iterator();

            while (iterator.hasNext()) {
                latestEquipmentState = (DBObject) iterator.next();
            }
        } else {
            HashMap<String, String> queryParams = new HashMap<>();
            HashMap<String, String> appendableParams = new HashMap<>();

            queryParams.put("isp", ispName);

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.put("_id", 0);

            latestEquipmentState = mongoService.findOne(queryParams, appendableParams, RGW_CONNECTIVITY_STATS, TIMESTAMP, DESC, mongoFieldOptions);
        }

        if (latestEquipmentState != null) {
            return convertToEquipmentStats(latestEquipmentState);
        }

        return new NCSDashboardDTO.EquipmentStats();
    }

    public NCSDashboardDTO.DeviceStats getDeviceStats(String ispName) {

        DBObject latestDeviceState = null;

        if (StringUtils.isEmpty(ispName)) {
            List<DBObject> aggregatePipeline = new ArrayList<>();

            aggregatePipeline.add(new BasicDBObject("$match", new BasicDBObject("isp", new BasicDBObject("$ne", null))));

            aggregatePipeline.add(new BasicDBObject("$sort", new BasicDBObject("dateCreated", -1)));

            BasicDBObject groupByIsp = new BasicDBObject();
            groupByIsp.put("_id", "$isp");
            groupByIsp.put("latestRecord", new BasicDBObject("$first", "$$ROOT"));
            aggregatePipeline.add(new BasicDBObject("$group", groupByIsp));

            aggregatePipeline.add(new BasicDBObject("$replaceRoot", new BasicDBObject("newRoot", "$latestRecord")));

            BasicDBObject groupTotal = new BasicDBObject();
            groupTotal.put("_id", null);
            groupTotal.put("connected", new BasicDBObject("$sum", "$connected"));
            groupTotal.put("disconnected", new BasicDBObject("$sum", "$disconnected"));
            aggregatePipeline.add(new BasicDBObject("$group", groupTotal));

            DBCollection dbCollection = at3Adapter.getMongoDbCollection(DEVICE_CONNECTIVITY_STATS);
            Iterator iterator = dbCollection.aggregate(aggregatePipeline).results().iterator();

            while (iterator.hasNext()) {
                latestDeviceState = (DBObject) iterator.next();
            }
        } else {
            HashMap<String, String> queryParams = new HashMap<>();
            HashMap<String, String> appendableParams = new HashMap<>();

            queryParams.put("isp", ispName);

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.put("_id", 0);

            latestDeviceState = mongoService.findOne(queryParams, appendableParams, DEVICE_CONNECTIVITY_STATS, TIMESTAMP, DESC, mongoFieldOptions);
        }

        if (latestDeviceState != null) {
            return convertToDeviceStats(latestDeviceState);
        }

        return new NCSDashboardDTO.DeviceStats();
    }

    private NCSDashboardDTO.NetworkStats convertToNetworkStats(DBObject dbObject) {
        NCSDashboardDTO.NetworkStats networkStats = new NCSDashboardDTO.NetworkStats();
        networkStats.setAutoCreated(ObjectUtils.isEmpty(dbObject.get("autoCreated")) ? 0L : (Long) dbObject.get("autoCreated"));
        networkStats.setManuallyCreated(ObjectUtils.isEmpty(dbObject.get("manuallyCreated")) ? 0L : (Long) dbObject.get("manuallyCreated"));
        networkStats.setOnline(ObjectUtils.isEmpty(dbObject.get("online")) ? 0L : (Long) dbObject.get("online"));
        return networkStats;
    }

    private NCSDashboardDTO.EquipmentStats convertToEquipmentStats(DBObject dbObject) {
        NCSDashboardDTO.EquipmentStats equipmentStats = new NCSDashboardDTO.EquipmentStats();
        equipmentStats.setOnline(ObjectUtils.isEmpty(dbObject.get("connected")) ? 0L : (Long) dbObject.get("connected"));
        equipmentStats.setOffline(ObjectUtils.isEmpty(dbObject.get("disconnected")) ? 0L : (Long) dbObject.get("disconnected"));
        return equipmentStats;
    }


    private NCSDashboardDTO.DeviceStats convertToDeviceStats(DBObject dbObject) {
        NCSDashboardDTO.DeviceStats deviceStats = new NCSDashboardDTO.DeviceStats();
        deviceStats.setOnline(ObjectUtils.isEmpty(dbObject.get("connected")) ? 0L : (Long) dbObject.get("connected"));
        deviceStats.setOffline(ObjectUtils.isEmpty(dbObject.get("disconnected")) ? 0L : (Long) dbObject.get("disconnected"));
        return deviceStats;
    }
} 