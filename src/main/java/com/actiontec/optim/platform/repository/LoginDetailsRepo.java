package com.actiontec.optim.platform.repository;

import com.incs83.app.entities.LoginDetails;
import com.incs83.mt.DataAccessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
public class LoginDetailsRepo {
    @Autowired
    DataAccessService dataAccessService;

    public boolean create(LoginDetails loginDetails) throws Exception {
        return dataAccessService.create(LoginDetails.class, loginDetails);
    }

    public void deleteById(String id) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        dataAccessService.delete(LoginDetails.class, id);
    }

    public LoginDetails getLoginDetailsById(String id) {
        return (LoginDetails) dataAccessService.read(LoginDetails.class, id);
    }
}
