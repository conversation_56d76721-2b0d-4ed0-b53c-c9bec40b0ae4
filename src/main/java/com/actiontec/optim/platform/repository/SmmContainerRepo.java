package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.CpeSmmAppDao;
import com.actiontec.optim.mongodb.dto.CpeSmmAppDto;
import com.actiontec.optim.platform.mapper.SmmContainerMapper;
import com.actiontec.optim.platform.model.SmmContainer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class SmmContainerRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    CpeSmmAppDao cpeSmmAppDao;

    @Autowired
    SmmContainerMapper smmContainerMapper;

    private List<SmmContainer> getSmmContainerList(List<CpeSmmAppDto> cpeSmmAppDtoList) {
        List<SmmContainer> smmContainerList = new ArrayList<>();
        for(CpeSmmAppDto cpeSmmAppDto : cpeSmmAppDtoList) {
            SmmContainer smmContainer = new SmmContainer();

            smmContainer.setId(cpeSmmAppDto.getServiceId());
            smmContainer.setStatus(cpeSmmAppDto.getRptStatus());
            smmContainer.setAutoStart(cpeSmmAppDto.getRptAutoStart());

            SmmContainer.Service service = new SmmContainer.Service();
            service.setServiceId(cpeSmmAppDto.getServiceId());
            service.setApplicationId(cpeSmmAppDto.getRptApplicationId());
            service.setVersion(cpeSmmAppDto.getRptVersion());
            service.setProvisionStatus(cpeSmmAppDto.getRptProvisionStatus());
            smmContainer.setService(service);

            smmContainer.setSystem(smmContainerMapper.toSmmContainerSystem(cpeSmmAppDto.getSystem()));
            smmContainer.setForbiddenOperations(cpeSmmAppDto.getForbiddenOperations());
            smmContainerList.add(smmContainer);
        }
        return smmContainerList;
    }

    public List<SmmContainer> findByUserIdAndSerial(String userId, String serial) {
        List<CpeSmmAppDto> cpeSmmAppDtoList = cpeSmmAppDao.findByUserIdAndSerial(userId, serial);
        List<SmmContainer> smmContainerList = getSmmContainerList(cpeSmmAppDtoList);
        return smmContainerList;
    }

    public List<SmmContainer> findByUserIdAndSerialAndContainerId(String userId, String serial, String containerId) {
        List<CpeSmmAppDto> cpeSmmAppDtoList = cpeSmmAppDao.findByUserIdAndSerialAndContainerId(userId, serial, containerId);
        List<SmmContainer> smmContainerList = getSmmContainerList(cpeSmmAppDtoList);
        return smmContainerList;
    }
}
