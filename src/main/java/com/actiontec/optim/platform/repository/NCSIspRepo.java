package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.api.v6.dto.NCSIspDTO;
import com.actiontec.optim.platform.api.v6.dto.NCSIspQueryDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.MongoTenantTemplate;
import com.mongodb.BasicDBObject;
import com.mongodb.DBCollection;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.INTERNET_SERVICE_PROVIDER;

@Component
public class NCSIspRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private MongoTenantTemplate mongoTenantTemplate;

    public PaginationResponse<NCSIspDTO> getAllIsps(NCSIspQueryDTO queryDTO) {
        PaginationResponse<NCSIspDTO> result = new PaginationResponse<>();
        List<NCSIspDTO> dataList = new ArrayList<>();

        DBCursor cursor = null;

        DBCollection collection = mongoTenantTemplate.getCollection(INTERNET_SERVICE_PROVIDER);

        long total = 0;

        BasicDBObject query = new BasicDBObject();
        if(StringUtils.isNotEmpty(queryDTO.getName())) {
            query.put("name", new BasicDBObject("$regex", "^" + queryDTO.getName() + ".*"));
        }

        if(StringUtils.isNotEmpty(queryDTO.getAlias())) {
            query.put("displayName", new BasicDBObject("$regex", "^" + queryDTO.getAlias() + ".*"));
        }

        total = collection.count(query);

        try {
            cursor = collection.find(query).skip(queryDTO.getOffset()).limit(queryDTO.getLimit());

            while (cursor.hasNext()) {
                DBObject doc = cursor.next();

                NCSIspDTO ispDTO = new NCSIspDTO();
                ispDTO.setId(doc.get("id").toString());
                ispDTO.setAlias(Objects.nonNull(doc.get("displayName")) ? doc.get("displayName").toString() : StringUtils.EMPTY);
                ispDTO.setName(Objects.nonNull(doc.get("name")) ? doc.get("name").toString() : StringUtils.EMPTY);
                ispDTO.setDescription(Objects.nonNull(doc.get("description")) ? doc.get("description").toString() : StringUtils.EMPTY);

                dataList.add(ispDTO);
            }

            PaginationDTO pagination = new PaginationDTO();
            pagination.setTotal((int) total);
            pagination.setListSize(dataList.size());
            pagination.setOffset(queryDTO.getOffset());
            pagination.setLimit(queryDTO.getLimit());

            result.setPagination(pagination);
            result.setData(dataList);

            return result;

        } catch (Exception e) {
            logger.error("Failed to get ISP list: ", e);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Failed to get ISP list");
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }


}
