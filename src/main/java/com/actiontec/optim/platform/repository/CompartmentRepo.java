package com.actiontec.optim.platform.repository;

import com.incs83.app.constants.queries.GroupISPSQL;
import com.incs83.app.entities.Compartment;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Component
public class CompartmentRepo {
    @Autowired
    DataAccessService dataAccessService;

    public List<Compartment> getCompartmentByIspId(String ispId) throws Exception {
        HashMap<String, String> params= new HashMap<>();
        params.put("ispId", ispId);
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);
        if (CollectionUtils.isEmpty(compartmentList)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "IspId is invalid : " + ispId);
        }

        return compartmentList;
    }

}
