package com.actiontec.optim.platform.repository;

import com.incs83.app.entities.RttyServerConfig;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

import static com.incs83.app.constants.queries.RttyServerConfigSQL.GET_RTTY_SERVER_CONFIG;

@Component
public class RttyServerConfigRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public RttyServerConfig getServerConfig() throws Exception {
        HashMap<String, String> params = new HashMap<>();
        List<RttyServerConfig> rttyServerConfigList = (List<RttyServerConfig>) dataAccessService.read(RttyServerConfig.class, GET_RTTY_SERVER_CONFIG, params);
        if (CollectionUtils.isEmpty(rttyServerConfigList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No RTTY server config found");
        }

        return rttyServerConfigList.get(0);
    }
}
