package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.mapper.NetworkMocaMapper;
import com.actiontec.optim.platform.model.NetworkMoca;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class NetworkMocaRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private NetworkMocaMapper networkMocaMapper;

    public List<NetworkMoca> findByUserIdAndSerial(String userId, String serial) {
        ApDetailDto apDetail = apDetailDao.findMocaByUserIdAndSerial(userId, serial);

        List<NetworkMoca> networkMocas = networkMocaMapper.toNetworkMoca(apDetail.getMocas());

        for (NetworkMoca networkMoca : networkMocas) {
            networkMoca.setLastReportTime(apDetail.getReceivedTime());

            List<ApDetailDto.MocaDeviceDto> mocaDeviceDtos = apDetail.getMocaDevices().stream()
                    .filter(e -> networkMoca.getPortNo() == e.getPortNo().intValue())
                    .collect(Collectors.toList());
            networkMoca.setNodes(networkMocaMapper.toNetworkMocaNodes(mocaDeviceDtos, apDetail.getSendingTime()).toArray(new NetworkMoca.MocaNode[0]));
        }

        return networkMocas;
    }

    public Optional<NetworkMoca> findByUserIdAndSerialAndPortNo(String userId, String serial, int portNo) {
        ApDetailDto apDetail = apDetailDao.findMocaByUserIdAndSerial(userId, serial);

        List<NetworkMoca> networkMocas = networkMocaMapper.toNetworkMoca(apDetail.getMocas());


        for (NetworkMoca networkMoca : networkMocas) {
            if(networkMoca.getPortNo() == portNo) {
                networkMoca.setLastReportTime(apDetail.getReceivedTime());

                List<ApDetailDto.MocaDeviceDto> mocaDeviceDtos = apDetail.getMocaDevices().stream()
                        .filter(e -> networkMoca.getPortNo() == e.getPortNo().intValue()).collect(Collectors.toList());
                networkMoca.setNodes(networkMocaMapper.toNetworkMocaNodes(mocaDeviceDtos, apDetail.getSendingTime()).toArray(new NetworkMoca.MocaNode[0]));
                return Optional.of(networkMoca);
            }
        }

        return Optional.empty();
    }
}