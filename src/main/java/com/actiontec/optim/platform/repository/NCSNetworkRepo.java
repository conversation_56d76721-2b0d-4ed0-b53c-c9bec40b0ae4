package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.incs83.app.constants.queries.NCSNetworkSQL;
import com.incs83.app.entities.NCSNetwork;
import com.incs83.app.utils.SqlStatementUtils;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;

import static com.incs83.app.constants.misc.ApplicationConstants.GEN_TYPE_AUTO;
import static com.incs83.app.constants.misc.ApplicationConstants.GEN_TYPE_MANUAL;
import static com.incs83.app.utils.SqlStatementUtils.*;

@Component
public class NCSNetworkRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public String createNetwork(NCSNetworkRequest networkRequest) throws Exception {
        NCSNetwork network = new NCSNetwork();
        String networkId = CommonUtils.generateUUID();
        network.setId(networkId);
        network.setName(networkRequest.getName());
        network.setCreatedAt(LocalDateTime.now());
        network.setCreatedBy(CommonUtils.getUserIdOfLoggedInUser());
        network.setGenType(GEN_TYPE_MANUAL);
        network.setIspId(networkRequest.getIspId());
        dataAccessService.create(NCSNetwork.class, network);
        return networkId;
    }

    public PaginationResponse<NCSNetworkDTO> getAllNetwork(NCSNetworkQueryDTO queryDTO) throws Exception {
        StringBuilder query = new StringBuilder(NCSNetworkSQL.GET_NETWORKS);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(SqlStatementUtils.Condition.AND, SqlStatementUtils.Condition.WHERE, StringUtils.EMPTY);
        // use reflection to generate dynamic query string.
        Field[] fields = queryDTO.getClass().getDeclaredFields();
        for (Field field : fields) {
            // allow access private attribute.
            field.setAccessible(true);
            Object fieldValue = field.get(queryDTO);
            if (field.getDeclaringClass().equals(PaginationRequest.class) || ObjectUtils.isEmpty(fieldValue)) {
                continue;
            }

            String columnNativeName =  SqlStatementUtils.getNativeColumnName(field);

            if (StringUtils.equals(field.getName(), "name")) {
                fieldValue = fieldValue + "%";
                whereClause.add(buildLikeStatement(Alias.Network, columnNativeName, field.getName()));
            } else if (StringUtils.equals(field.getName(), "ispId")) {
                whereClause.add(buildEqualStatement(Alias.Network, columnNativeName, field.getName()));
            } else if (StringUtils.equals(field.getName(), "subscriberId")) {
                whereClause.add(buildEqualStatement(Alias.SubscriberNetwork, columnNativeName, field.getName()));
            } else if (StringUtils.equals(field.getName(), "isAuto")){
                whereClause.add(buildEqualStatement(SqlStatementUtils.Alias.Network, columnNativeName, field.getName()));
                params.put(field.getName(), queryDTO.getIsAuto() ? GEN_TYPE_AUTO : GEN_TYPE_MANUAL);
                continue;
            }

            logger.info("field name: {}, field value: {}", field.getName(), fieldValue.toString());

            params.put(field.getName(), fieldValue);
        }

        // query without any parameters
        if(params.isEmpty()) {
            whereClause = new StringJoiner(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        }

        String page = buildPageStatement(queryDTO.getOffset(), queryDTO.getLimit());

        String finalQuery = query.append(whereClause).append(page).toString();
        logger.info("getAllEquipments finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<NCSNetworkDTO> paginationResponse = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<NCSNetworkDTO>  networkDTOList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(rawDataList)) {
            for(Object[] rawData: rawDataList) {
                NCSNetworkDTO networkDTO = new NCSNetworkDTO(rawData);
                networkDTOList.add(networkDTO);
            }

            paginationDTO.setTotal(countNetworks(NCSNetworkSQL.NETWORK_COUNT, whereClause, params));
            paginationDTO.setListSize(networkDTOList.size());
        }
        paginationDTO.setOffset(queryDTO.getOffset());
        paginationDTO.setLimit(queryDTO.getLimit());

        paginationResponse.setPagination(paginationDTO);
        paginationResponse.setData(networkDTOList);

        return paginationResponse;
    }

    public Integer countNetworks(String countQuery, StringJoiner whereClause, HashMap<String, Object> params) throws Exception {
        StringBuilder query = new StringBuilder(countQuery);
        String finalQuery = query.append(whereClause).toString();
        return Integer.valueOf(dataAccessService.readNative(finalQuery, params).iterator().next().toString());
    }

    public NCSNetworkDetailDTO getNetworkDetailDTOById(String networkId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("id", networkId);
        // OC-5927: auto network flow will not create subscriber_network data, need to modify sql with left join.
        List<Object[]> rawDataList = dataAccessService.readNative(NCSNetworkSQL.GET_NETWORK_BY_ID, params);
        NCSNetworkDTO networkDTO = new NCSNetworkDTO();
        if(CollectionUtils.isNotEmpty(rawDataList)) {
            networkDTO = new NCSNetworkDTO(rawDataList.get(0));
        }

        NCSNetworkDetailDTO networkDetailDTO = new NCSNetworkDetailDTO();
        BeanUtils.copyProperties(networkDTO, networkDetailDTO);

        return networkDetailDTO;
    }

    public NCSNetwork getNetworkById(String networkId) throws Exception {
        return (NCSNetwork) dataAccessService.read(NCSNetwork.class, networkId);
    }

    // network only can modify name
    public void updateNetworkName(String networkId, NCSNetworkRequest networkRequest) throws Exception {
        NCSNetwork network= (NCSNetwork) dataAccessService.read(NCSNetwork.class, networkId);
        network.setName(networkRequest.getName());
        network.setUpdatedAt(LocalDateTime.now());
        network.setUpdatedBy(CommonUtils.getUserIdOfLoggedInUser());
        dataAccessService.update(NCSNetwork.class, network);
    }

    public void deleteNetwork(String networkId) throws Exception {
        dataAccessService.delete(NCSNetwork.class, networkId);
    }

    public PaginationResponse<NetworkOperationsDTO> getNetworksByKeyword(NetworkOperationsQueryDTO queryDTO) throws Exception {
        StringBuilder query = new StringBuilder(NCSNetworkSQL.GET_NETWORK_BY_KEYWORD);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        String page = buildPageStatement(queryDTO.getOffset(), queryDTO.getLimit());

        // since query is too complicated, need to replace keyword manually.
        String finalQuery = query.toString().replaceAll(":keyword", "'" + queryDTO.getKeyword() + "'") + page;
        logger.info("getNetworksByKeyword finalQuery: {}", finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<NetworkOperationsDTO> paginationResponse = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<NetworkOperationsDTO> networkOperationsDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rawDataList)) {
            for (Object[] rawData: rawDataList) {
                NetworkOperationsDTO networkOperationsDTO = new NetworkOperationsDTO(rawData);
                networkOperationsDTOList.add(networkOperationsDTO);
            }

            String countFinalQuery = NCSNetworkSQL.NETWORK_BY_KEYBOARD_COUNT.replaceAll(":keyword", "'" + queryDTO.getKeyword() + "'");
            paginationDTO.setTotal(countNetworks(countFinalQuery, whereClause, params));
            paginationDTO.setListSize(networkOperationsDTOList.size());
            paginationDTO.setOffset(queryDTO.getOffset());
            paginationDTO.setLimit(queryDTO.getLimit());
        }

        paginationResponse.setPagination(paginationDTO);
        paginationResponse.setData(networkOperationsDTOList);

        return paginationResponse;
    }

    public PaginationResponse<NetworkOperationsDTO> getNetworksByKeyword(NetworkOperationsQueryDTO queryDTO, String ispId) throws Exception {
        StringBuilder query = new StringBuilder(NCSNetworkSQL.GET_NETWORK_BY_KEYWORD_V2);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        String page = buildPageStatement(queryDTO.getOffset(), queryDTO.getLimit());

        // since query is too complicated, need to replace keyword manually.
        String finalQuery = query.toString().replaceAll(":keyword", "'" + queryDTO.getKeyword() + "'") + page;
        if (ispId != null) {
            finalQuery = finalQuery.replace(":EXTRA_CONDITION", "AND ne.isp_id = '" + ispId + "'");
        } else {
            finalQuery = finalQuery.replace(":EXTRA_CONDITION", "");
        }
        logger.info("getNetworksByKeyword finalQuery: {}", finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<NetworkOperationsDTO> paginationResponse = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<NetworkOperationsDTO> networkOperationsDTOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rawDataList)) {
            for (Object[] rawData: rawDataList) {
                NetworkOperationsDTO networkOperationsDTO = new NetworkOperationsDTO(rawData);
                networkOperationsDTOList.add(networkOperationsDTO);
            }

            String countFinalQuery = NCSNetworkSQL.NETWORK_BY_KEYBOARD_COUNT_V2.replaceAll(":keyword", "'" + queryDTO.getKeyword() + "'");
            if (ispId != null) {
                countFinalQuery = countFinalQuery.replace(":EXTRA_CONDITION", "AND ne.isp_id = '" + ispId + "'");
            } else {
                countFinalQuery = countFinalQuery.replace(":EXTRA_CONDITION", "");
            }
            paginationDTO.setTotal(countNetworks(countFinalQuery, whereClause, params));
            paginationDTO.setListSize(networkOperationsDTOList.size());
            paginationDTO.setOffset(queryDTO.getOffset());
            paginationDTO.setLimit(queryDTO.getLimit());
        }

        paginationResponse.setPagination(paginationDTO);
        paginationResponse.setData(networkOperationsDTOList);

        return paginationResponse;
    }
}
