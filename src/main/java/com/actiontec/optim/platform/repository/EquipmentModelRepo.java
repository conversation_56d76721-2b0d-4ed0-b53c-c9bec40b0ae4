package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.api.v5.exception.OptimApiException;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.platform.model.ModelHardware;
import com.actiontec.optim.platform.model.ModelSnapshot;
import com.actiontec.optim.platform.service.AwsS3Service;
import com.incs83.app.entities.EquipmentModel;
import com.incs83.app.entities.OptimFile;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import static com.incs83.app.constants.queries.ModelSQL.COUNT_MODEL_BY_IDS;

@Component
public class EquipmentModelRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private AwsS3Service awsS3Service;

    private List<com.actiontec.optim.platform.model.EquipmentModel> toEquipmentModelList(List<EquipmentModel> modelList) {
        List<com.actiontec.optim.platform.model.EquipmentModel> equipmentEquipmentModelList = new ArrayList<>();

        for(EquipmentModel model : modelList) {
            com.actiontec.optim.platform.model.EquipmentModel equipmentModel = toEquipmentModel(model);
            equipmentEquipmentModelList.add(equipmentModel);
        }

        return equipmentEquipmentModelList;
    }

    private com.actiontec.optim.platform.model.EquipmentModel toEquipmentModel(EquipmentModel model) {
        com.actiontec.optim.platform.model.EquipmentModel equipmentModel = new com.actiontec.optim.platform.model.EquipmentModel();
        ModelSnapshot modelSnapshot = new ModelSnapshot();
        ModelHardware modelHardware = new ModelHardware();
        ArrayList<String> ispIds;
        ArrayList<String> radioKeys;
        ArrayList<String> wanTypes;
        ArrayList<String> lanTypes;
        ArrayList<String> features;

        equipmentModel.setId(model.getId());
        equipmentModel.setName(model.getName());
        equipmentModel.setEquipmentType(model.getType());

        if(model.getIspIds() != null && !model.getIspIds().isEmpty()) {
            ispIds = new ArrayList<String>(Arrays.asList(model.getIspIds().split(",")));
        } else {
            ispIds = new ArrayList<>();
        }
        equipmentModel.setIspIds(ispIds);

        modelSnapshot.setFileName(model.getOptimFile().getFileName());
        modelSnapshot.setFileSize(model.getOptimFile().getFileSize());
        modelSnapshot.setFileEntry("/actiontec/api/v5/files/" + model.getOptimFile().getId());
        modelSnapshot.setFileStatus(model.getOptimFile().getFileStatus());
        equipmentModel.setSnapshot(modelSnapshot);

        if(model.getRadioKeys() != null && !model.getRadioKeys().isEmpty()) {
            radioKeys = new ArrayList<String>(Arrays.asList(model.getRadioKeys().split(",")));
        } else {
            radioKeys = new ArrayList<>();
        }

        if(model.getWanTypes() != null && !model.getWanTypes().isEmpty()) {
            wanTypes = new ArrayList<String>(Arrays.asList(model.getWanTypes().split(",")));
        } else {
            wanTypes = new ArrayList<>();
        }

        if(model.getLanTypes() != null && !model.getLanTypes().isEmpty()) {
            lanTypes = new ArrayList<String>(Arrays.asList(model.getLanTypes().split(",")));
        } else {
            lanTypes = new ArrayList<>();
        }

        modelHardware.setMemorySize(model.getMemorySize());
        modelHardware.setFlashSpaceSize(model.getFlashSize());
        modelHardware.setCpuSpeed(model.getCpuSpeed());
        modelHardware.setCpuCores(model.getCpuCores());
        modelHardware.setSmmFlashSpaceSize(model.getSmmFlashSize());
        modelHardware.setSmmMemorySize(model.getSmmMemorySize());
        modelHardware.setSmmCpuMhz(model.getSmmCpuMhz());
        modelHardware.setAvailableRadioKeys(radioKeys);
        modelHardware.setAvailableWanTypes(wanTypes);
        modelHardware.setAvailableLanTypes(lanTypes);
        equipmentModel.setHardware(modelHardware);

        if(model.getFeatures() != null && !model.getFeatures().isEmpty()) {
            features = new ArrayList<String>(Arrays.asList(model.getFeatures().split(",")));
        } else {
            features = new ArrayList<>();
        }
        equipmentModel.setFeatures(features);

        return equipmentModel;
    }

    public List<com.actiontec.optim.platform.model.EquipmentModel> findEquipmentModels(String groupId) throws Exception {

        List<EquipmentModel> equipmentModelList = (List<EquipmentModel>) dataAccessService.read(EquipmentModel.class);
        List<com.actiontec.optim.platform.model.EquipmentModel> equipmentEquipmentModels = toEquipmentModelList(equipmentModelList);
        if(groupId != null) {
            List<com.actiontec.optim.platform.model.EquipmentModel> groupEquipmentModels = equipmentEquipmentModels.stream().filter(model -> model.getIspIds().contains(groupId)).collect(Collectors.toList());
            return groupEquipmentModels;
        }
        return equipmentEquipmentModels;
    }

    public com.actiontec.optim.platform.model.EquipmentModel addEquipmentModel(com.actiontec.optim.platform.model.EquipmentModel equipmentModel) throws Exception {

        EquipmentModel model = new EquipmentModel();
        OptimFile optimFile = new OptimFile();;

        model.setId(CommonUtils.generateUUID());
        model.setName(equipmentModel.getName());
        model.setType(equipmentModel.getEquipmentType());
        model.setMemorySize(equipmentModel.getHardware().getMemorySize());
        model.setFlashSize(equipmentModel.getHardware().getFlashSpaceSize());
        model.setCpuSpeed(equipmentModel.getHardware().getCpuSpeed());
        model.setCpuCores(equipmentModel.getHardware().getCpuCores());
        model.setSmmFlashSize(equipmentModel.getHardware().getSmmFlashSpaceSize());
        model.setSmmMemorySize(equipmentModel.getHardware().getSmmMemorySize());
        model.setSmmCpuMhz(equipmentModel.getHardware().getSmmCpuMhz());

        String ispIds = String.join(",", equipmentModel.getIspIds());
        model.setIspIds(ispIds);

        String radioKeys = String.join(",", equipmentModel.getHardware().getAvailableRadioKeys());
        model.setRadioKeys(radioKeys);

        String wanTypes = String.join(",", equipmentModel.getHardware().getAvailableWanTypes());
        model.setWanTypes(wanTypes);

        String lanTypes = String.join(",", equipmentModel.getHardware().getAvailableLanTypes());
        model.setLanTypes(lanTypes);

        String features = String.join(",", equipmentModel.getFeatures());
        model.setFeatures(features);

        optimFile.setId(CommonUtils.generateUUID());
        String fileLocation = equipmentModel.getSnapshot().getFileLocation();
        if(fileLocation == null) {
            optimFile.setFileName(equipmentModel.getSnapshot().getFileName());
            optimFile.setFileSize(equipmentModel.getSnapshot().getFileSize());
            optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);
            optimFile.setType(ApplicationConstants.OptimFileType.firmware.name());
        } else {
            String fileId = fileLocation.substring(fileLocation.lastIndexOf('/') + 1);
            OptimFile existOptimFile = (OptimFile) dataAccessService.read(OptimFile.class, fileId);
            if(existOptimFile != null) {
                optimFile.setFileName(existOptimFile.getFileName());
                optimFile.setFileSize(existOptimFile.getFileSize());
                optimFile.setFileStatus(existOptimFile.getFileStatus());
                optimFile.setType(existOptimFile.getType());
                optimFile.setS3Bucket(existOptimFile.getS3Bucket());
                optimFile.setFilePath(ApplicationConstants.S3_PUBLIC_FILE_FOLDER + optimFile.getId());
                awsS3Service.copyFile(existOptimFile, optimFile);
            } else {
                new OptimApiException(HttpStatus.BAD_REQUEST, "File [" + fileLocation + "] not exist");
            }
        }
        model.setOptimFile(optimFile);
        dataAccessService.create(EquipmentModel.class, model);

        equipmentModel.setId(model.getId());
        equipmentModel.getSnapshot().setFileEntry("/actiontec/api/v5/files/" + optimFile.getId());

        return equipmentModel;
    }

    public com.actiontec.optim.platform.model.EquipmentModel findEquipmentModelById(String id, String groupId) throws Exception {

        com.actiontec.optim.platform.model.EquipmentModel equipmentModel = null;

        EquipmentModel model = (EquipmentModel) dataAccessService.read(EquipmentModel.class, id);
        if(model != null) {
            equipmentModel = toEquipmentModel(model);
        }
        return equipmentModel;
    }

    public Boolean updateEquipmentModelById(String id, com.actiontec.optim.platform.model.EquipmentModel equipmentModel) throws Exception {

        EquipmentModel model = (EquipmentModel) dataAccessService.read(EquipmentModel.class, id);

        model.setName(equipmentModel.getName());
        model.setType(equipmentModel.getEquipmentType());
        model.setMemorySize(equipmentModel.getHardware().getMemorySize());
        model.setFlashSize(equipmentModel.getHardware().getFlashSpaceSize());
        model.setCpuSpeed(equipmentModel.getHardware().getCpuSpeed());
        model.setCpuCores(equipmentModel.getHardware().getCpuCores());
        model.setSmmFlashSize(equipmentModel.getHardware().getSmmFlashSpaceSize());
        model.setSmmMemorySize(equipmentModel.getHardware().getSmmMemorySize());
        model.setSmmCpuMhz(equipmentModel.getHardware().getSmmCpuMhz());

        String ispIds = String.join(",", equipmentModel.getIspIds());
        model.setIspIds(ispIds);

        String radioKeys = String.join(",", equipmentModel.getHardware().getAvailableRadioKeys());
        model.setRadioKeys(radioKeys);

        String wanTypes = String.join(",", equipmentModel.getHardware().getAvailableWanTypes());
        model.setWanTypes(wanTypes);

        String lanTypes = String.join(",", equipmentModel.getHardware().getAvailableLanTypes());
        model.setLanTypes(lanTypes);

        String features = String.join(",", equipmentModel.getFeatures());
        model.setFeatures(features);

        String fileLocation = equipmentModel.getSnapshot().getFileLocation();
        if(fileLocation == null) {
            model.getOptimFile().setFileName(equipmentModel.getSnapshot().getFileName());
            model.getOptimFile().setFileSize(equipmentModel.getSnapshot().getFileSize());
        } else {
            String fileId = fileLocation.substring(fileLocation.lastIndexOf('/') + 1);
            OptimFile existOptimFile = (OptimFile) dataAccessService.read(OptimFile.class, fileId);
            if(existOptimFile != null) {
                model.getOptimFile().setFileName(existOptimFile.getFileName());
                model.getOptimFile().setFileSize(existOptimFile.getFileSize());
                model.getOptimFile().setFileStatus(existOptimFile.getFileStatus());
                model.getOptimFile().setType(existOptimFile.getType());
                model.getOptimFile().setS3Bucket(existOptimFile.getS3Bucket());
                model.getOptimFile().setFilePath(ApplicationConstants.S3_PUBLIC_FILE_FOLDER + model.getOptimFile().getId());
                awsS3Service.copyFile(existOptimFile, model.getOptimFile());
            } else {
                new OptimApiException(HttpStatus.BAD_REQUEST, "File [" + fileLocation + "] not exist");
            }
        }

        dataAccessService.update(EquipmentModel.class, model);
        return true;
    }

    public Boolean deleteEquipmentModelById(String id) throws Exception {

        EquipmentModel equipmentModel = (EquipmentModel) dataAccessService.read(EquipmentModel.class, id);

        awsS3Service.deleteFile(equipmentModel.getOptimFile());
        dataAccessService.delete(EquipmentModel.class, id);
        return true;
    }

    public com.actiontec.optim.platform.model.EquipmentModel findEquipmentModelByModelName(String modelName) throws Exception {

        com.actiontec.optim.platform.model.EquipmentModel equipmentModel = null;

        List<EquipmentModel> equipmentModels = (List<EquipmentModel>) dataAccessService.read(EquipmentModel.class);
        List<EquipmentModel> eqpModels = equipmentModels.stream().filter(e -> e.getName().equals(modelName)).collect(Collectors.toList());

        if(!eqpModels.isEmpty()) {
            equipmentModel = toEquipmentModel(eqpModels.get(0));
        } else {
            for(EquipmentModel eqpModel : equipmentModels) {
                if(modelName.contains(eqpModel.getName())) {
                    equipmentModel = toEquipmentModel(eqpModel);
                }
            }
        }
        return equipmentModel;
    }

    public int countEquipmentModelByIds(ArrayList<String> equipmentTypeIds) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("equipmentTypeIds", equipmentTypeIds);
        logger.info("equipmentTypeIds: " + equipmentTypeIds.toString());

        List<Object> count = dataAccessService.readNative(COUNT_MODEL_BY_IDS, params);
        return ((BigInteger) count.get(0)).intValue();
    }
}
