package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.incs83.app.entities.OptimFile;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class FileRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    @Value("${s3.bucket}")
    private String bucketName;

    public String createFile(String fileName, String fileType) throws Exception {
        OptimFile optimFile = new OptimFile();
        String fileId = CommonUtils.generateUUID();
        optimFile.setId(fileId);
        optimFile.setType(fileType);
        optimFile.setFileName(fileName);
        optimFile.setFileStatus(ApplicationConstants.FILE_STATUS_INITIAL);
        optimFile.setS3Bucket(bucketName);

        dataAccessService.create(OptimFile.class, optimFile);
        return fileId;
    }

    public OptimFile updateFile(OptimFile optimFile) throws Exception {
        return (OptimFile) dataAccessService.update(OptimFile.class, optimFile);
    }

    public OptimFile getOptimFileById(String fileId) throws Exception {
        return (OptimFile) dataAccessService.read(OptimFile.class, fileId);
    }

    public void deleteById(String fileId) throws Exception {
        dataAccessService.delete(OptimFile.class, fileId);
    }
}
