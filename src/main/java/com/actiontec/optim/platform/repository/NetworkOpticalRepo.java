package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dao.ApWiFiInsightsPerMinuteDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.PonDataDto;
import com.actiontec.optim.platform.mapper.NetworkOpticalMapper;
import com.actiontec.optim.platform.model.NetworkOptical;
import com.actiontec.optim.platform.model.NetworkOpticalModule;
import com.actiontec.optim.platform.model.NetworkOpticalStats;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Component
public class NetworkOpticalRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private ApWiFiInsightsPerMinuteDao apWiFiInsightsPerMinuteDao;

    @Autowired
    private NetworkOpticalMapper networkOpticalMapper;

    public List<NetworkOptical> findOpticalBySerial(String serial) {

        HashMap<String, Object> ponData = apDetailDao.findPonBySerialNumber(serial);
        ApDetailDto.PonDto pon = (ApDetailDto.PonDto) ponData.get("pon");
        if(pon == null) {
            return null;
        }

        Long timestamp = (Long) ponData.get("timestamp");

        List<NetworkOptical> networkOpticalList = new ArrayList<>();
        NetworkOptical networkOptical = new NetworkOptical();
        networkOptical.setId("1");
        networkOptical.setEnabled(true);
        networkOptical.setStatus(pon.getStatus());
        networkOptical.setLastChangeTime(pon.getLastChange());
        networkOptical.setLastReportTime(timestamp);

        networkOpticalList.add(networkOptical);
        return networkOpticalList;
    }

    public List<NetworkOptical> findOpticalBySerialAndOpticsId(String serial, String opticsId) {

        HashMap<String, Object> ponData = apDetailDao.findPonBySerialNumber(serial);
        ApDetailDto.PonDto pon = (ApDetailDto.PonDto) ponData.get("pon");
        if(pon == null) {
            return null;
        }

        Long timestamp = (Long) ponData.get("timestamp");

        List<NetworkOptical> networkOpticalList = new ArrayList<>();
        NetworkOptical networkOptical = new NetworkOptical();
        networkOptical.setId("1");
        networkOptical.setEnabled(true);
        networkOptical.setStatus(pon.getStatus());
        networkOptical.setLastChangeTime(pon.getLastChange());
        networkOptical.setLastReportTime(timestamp);

        if(!networkOptical.getId().equals(opticsId)) {
            return null;
        }

        networkOpticalList.add(networkOptical);
        return networkOpticalList;
    }

    public NetworkOpticalModule findOpticalModuleBySerial(String serial) {

        HashMap<String, Object> ponData = apDetailDao.findPonBySerialNumber(serial);
        ApDetailDto.PonDto pon = (ApDetailDto.PonDto) ponData.get("pon");
        if(pon == null) {
            return null;
        }

        Long timestamp = (Long) ponData.get("timestamp");

        NetworkOpticalModule networkOpticalModule = new NetworkOpticalModule();
        networkOpticalModule.setSerialNumber(pon.getSerialNumber());
        networkOpticalModule.setFwVersion(pon.getFwVersion());
        networkOpticalModule.setStatus(pon.getStatus());
        networkOpticalModule.setLastChangeTime(pon.getLastChange());
        networkOpticalModule.setTemperature(pon.getTemperature());
        networkOpticalModule.setTemperatureWarningThreshold(pon.getTemperatureWarningThreshold());
        networkOpticalModule.setTemperatureAlarmThreshold(pon.getTemperatureAlarmThreshold());
        networkOpticalModule.setLastReportTime(timestamp);

        return networkOpticalModule;
    }

    public List<NetworkOpticalStats> findOpticalStatsBySerialAndDuration(String serial, Long duration) {

        List<PonDataDto> ponDataDtoList = apWiFiInsightsPerMinuteDao.getPonpDataList(serial, duration);
        List<NetworkOpticalStats> networkOpticalStatsList = networkOpticalMapper.toNetworkOpticalStats(ponDataDtoList);

        return networkOpticalStatsList;
    }
}
