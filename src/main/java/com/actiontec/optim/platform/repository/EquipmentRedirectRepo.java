package com.actiontec.optim.platform.repository;

import com.incs83.app.constants.queries.EquipmentRedirectSQL;
import com.incs83.mt.DataAccessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

@Repository
public class EquipmentRedirectRepo {

    @Autowired
    private DataAccessService dataAccessService;

    public boolean existsByBrokerId(String brokerId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("brokerId", brokerId);
        List<Object> result = dataAccessService.readNative(EquipmentRedirectSQL.REDIRECT_IS_EXIST_BY_BROKER_ID, params);
        
        if (CollectionUtils.isNotEmpty(result)) {
            Object count = result.get(0);
            return count != null && ((Number) count).longValue() > 0;
        }
        return false;
    }
} 