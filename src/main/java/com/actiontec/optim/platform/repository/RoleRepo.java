package com.actiontec.optim.platform.repository;

import com.incs83.app.entities.Role;
import com.incs83.mt.DataAccessService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
public class RoleRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public Role getRoleById(String roleId) throws Exception {
        return (Role) dataAccessService.read(Role.class, roleId);
    }







}
