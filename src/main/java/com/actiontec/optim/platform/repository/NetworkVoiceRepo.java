package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dao.ApWiFiInsightsPerMinuteDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.VoipDataDto;
import com.actiontec.optim.platform.mapper.NetworkVoiceMapper;
import com.actiontec.optim.platform.model.NetworkVoice;
import com.actiontec.optim.platform.model.NetworkVoiceClient;
import com.actiontec.optim.platform.model.NetworkVoiceNet;
import com.actiontec.optim.platform.model.NetworkVoiceStats;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class NetworkVoiceRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private ApWiFiInsightsPerMinuteDao apWiFiInsightsPerMinuteDao;

    @Autowired
    private NetworkVoiceMapper networkVoiceMapper;

    public NetworkVoice findVoiceBySerial(String serial) {

        ApDetailDto.VoipDto voipData = apDetailDao.findVoipBySerialNumber(serial);
        if(voipData == null) {
            return null;
        }

        NetworkVoice networkVoice = new NetworkVoice();

        networkVoice.setEnabled(voipData.getEnable());
        networkVoice.setStatus(voipData.getNetwork().getStatus());
        networkVoice.setIpAddress(voipData.getBoundIpAddr());

        List<NetworkVoice.LinesDto> linesDtoList = new ArrayList<>();
        for(ApDetailDto.VoipDto.LineDto line : voipData.getLines()) {
            NetworkVoice.LinesDto linesDto = new NetworkVoice.LinesDto();
            linesDto.setEnabled(line.getClient().getEnable());
            linesDto.setLineNumber(line.getLineNumber());
            linesDto.setHookStatus(line.getCurrentStatus().getHookStatus());
            linesDto.setCallDuration(line.getCurrentStatus().getCallDuration());
            linesDto.setClientId(String.valueOf(line.getClient().getClientId()));
            linesDtoList.add(linesDto);
        }
        networkVoice.setLines(linesDtoList);

        return networkVoice;
    }

    public List<NetworkVoiceClient> findVoiceClientBySerial(String serial) {

        ApDetailDto.VoipDto voipData = apDetailDao.findVoipBySerialNumber(serial);
        if(voipData == null) {
            return null;
        }

        List<NetworkVoiceClient> networkVoiceClientList = new ArrayList<>();

        for(ApDetailDto.VoipDto.LineDto line : voipData.getLines()) {
            NetworkVoiceClient networkVoiceClient = new NetworkVoiceClient();
            networkVoiceClient.setId(String.valueOf(line.getClient().getClientId()));
            networkVoiceClient.setEnabled(line.getClient().getEnable());
            networkVoiceClient.setStatus(line.getClient().getStatus());
            networkVoiceClient.setUsername(line.getClient().getAuthUserName());
            networkVoiceClient.setPassword(line.getClient().getAuthPassword());
            networkVoiceClient.setRegisterUri(line.getClient().getRegisterUri());
            networkVoiceClientList.add(networkVoiceClient);
        }

        return networkVoiceClientList;
    }

    public List<NetworkVoiceStats> findVoiceStatsBySerialAndDuration(String serial, Long duration) {

        List<VoipDataDto> voipDataDtoList = apWiFiInsightsPerMinuteDao.getVoipDataList(serial, duration);
        List<NetworkVoiceStats> networkVoiceStatsList = networkVoiceMapper.toNetworkVoiceStats(voipDataDtoList);

        return networkVoiceStatsList;
    }

    public List<NetworkVoiceClient> findVoiceClientBySerialAndClientId(String serial, int clientId) {

        ApDetailDto.VoipDto voipData = apDetailDao.findVoipBySerialNumber(serial);
        if(voipData == null) {
            return null;
        }

        List<NetworkVoiceClient> networkVoiceClientList = new ArrayList<>();

        for(ApDetailDto.VoipDto.LineDto line : voipData.getLines()) {
            if(clientId == line.getClient().getClientId()) {
                NetworkVoiceClient networkVoiceClient = new NetworkVoiceClient();
                networkVoiceClient.setId(String.valueOf(line.getClient().getClientId()));
                networkVoiceClient.setEnabled(line.getClient().getEnable());
                networkVoiceClient.setStatus(line.getClient().getStatus());
                networkVoiceClient.setUsername(line.getClient().getAuthUserName());
                networkVoiceClient.setPassword(line.getClient().getAuthPassword());
                networkVoiceClient.setRegisterUri(line.getClient().getRegisterUri());
                networkVoiceClientList.add(networkVoiceClient);
            }
        }

        return networkVoiceClientList;
    }

    public NetworkVoiceNet findVoiceNetBySerial(String serial) {

        ApDetailDto.VoipDto voipData = apDetailDao.findVoipBySerialNumber(serial);
        if(voipData == null) {
            return null;
        }

        NetworkVoiceNet networkVoiceNet = new NetworkVoiceNet();
        networkVoiceNet.setEnabled(voipData.getNetwork().getEnable());
        networkVoiceNet.setStatus(voipData.getNetwork().getStatus());
        networkVoiceNet.setProxyServer(voipData.getNetwork().getProxyServer());
        networkVoiceNet.setProxyServerPort(voipData.getNetwork().getProxyServerPort());
        networkVoiceNet.setProxyServerTransport(voipData.getNetwork().getProxyServerTransport());
        networkVoiceNet.setRegistrarServer(voipData.getNetwork().getRegistrarServer());
        networkVoiceNet.setRegistrarServerPort(voipData.getNetwork().getRegistrarServerPort());
        networkVoiceNet.setRegistrarServerTransport(voipData.getNetwork().getRegistrarServerTransport());
        networkVoiceNet.setRegistrationPeriod(voipData.getNetwork().getRegistrationPeriod());
        networkVoiceNet.setRegisterRetryInterval(voipData.getNetwork().getRegisterRetryInterval());
        networkVoiceNet.setOutboundProxy(voipData.getNetwork().getOutboundProxy());
        networkVoiceNet.setOutboundProxyPort(voipData.getNetwork().getOutboundProxyPort());

        return networkVoiceNet;
    }
}
