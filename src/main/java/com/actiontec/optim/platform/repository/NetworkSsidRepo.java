package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.mapper.NetworkSsidMapper;
import com.actiontec.optim.platform.model.NetworkSsid;
import com.actiontec.optim.platform.service.SimpleAesEcbCryptoService;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NetworkSsidRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private NetworkSsidMapper networkSsidMapper;

    @Autowired
    private SimpleAesEcbCryptoService simpleAesEcbCryptoService;

    public List<NetworkSsid> findByUserIdAndSerialNumber(String userId, String serial) {
        List<ApDetailDto.SsidDto> ssids = apDetailDao.findSsidsByUserIdAndSerialNumber(userId, serial);
        List<NetworkSsid> networkSsids = networkSsidMapper.toNetworkSsid(ssids);
        for (NetworkSsid networkSsid : networkSsids) {
            networkSsid.setSerial(serial);
        }

        return networkSsids;
    }

    public List<NetworkSsid> findByUserId(String userId) {
        List<ApDetailDto.SsidDto> ssids = apDetailDao.findSsidsByUserId(userId);
        List<NetworkSsid> networkSsids = networkSsidMapper.toNetworkSsid(ssids);
        return networkSsids;
    }

}
