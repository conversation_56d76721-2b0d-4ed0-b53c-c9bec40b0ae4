package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.api.v6.dto.BatchEquipmentUploadLogDTO;
import com.actiontec.optim.platform.api.v6.dto.BatchEquipmentUploadLogQueryDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationDTO;
import com.actiontec.optim.platform.api.v6.dto.PaginationResponse;
import com.actiontec.optim.platform.api.v6.enums.BatchEquipmentAction;
import com.actiontec.optim.platform.api.v6.enums.BatchEquipmentStatus;
import com.incs83.app.constants.queries.BatchEquipmentUploadLogSQL;
import com.incs83.app.entities.BatchEquipmentUploadLog;
import com.incs83.app.utils.SqlStatementUtils;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;

import static com.incs83.app.utils.SqlStatementUtils.*;

@Component
public class BatchEquipmentUploadLogRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public String createLog(String ispId, String fileId, String action) throws Exception {
        BatchEquipmentUploadLog uploadLog = new BatchEquipmentUploadLog();
        uploadLog.setId(CommonUtils.generateUUID());
        uploadLog.setIspId(ispId);
        uploadLog.setFileId(fileId);
        uploadLog.setProcessed(false);
        uploadLog.setAction(BatchEquipmentAction.valueOf(action));
        uploadLog.setStatus(BatchEquipmentStatus.Initial);
        uploadLog.setCreatedAt(LocalDateTime.now());
        uploadLog.setCreatedBy(CommonUtils.getUserIdOfLoggedInUser());

        dataAccessService.create(BatchEquipmentUploadLog.class, uploadLog);
        return uploadLog.getId();
    }

    public PaginationResponse<BatchEquipmentUploadLogDTO> getAllBatchEquipmentUploadLogs(BatchEquipmentUploadLogQueryDTO queryDTO) throws Exception {
        StringBuilder query = new StringBuilder(BatchEquipmentUploadLogSQL.GET_ALL_UPLOAD_LOGS);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(SqlStatementUtils.Condition.AND, SqlStatementUtils.Condition.WHERE, StringUtils.EMPTY);
        // use reflection to generate dynamic query string.
        Field[] fields = queryDTO.getClass().getDeclaredFields();
        for (Field field : fields) {
            // allow access private attribute.
            field.setAccessible(true);
            Object fieldValue = field.get(queryDTO);
            if (ObjectUtils.isEmpty(fieldValue)) {
                continue;
            }

            String columnNativeName = SqlStatementUtils.getNativeColumnName(field);
            whereClause.add(buildEqualStatement(Alias.BatchEquipmentUploadLog, columnNativeName, field.getName()));
            params.put(field.getName(), fieldValue);
            logger.info("field name: {}, field value: {}", field.getName(), fieldValue);
        }

        // query without any parameters
        if(params.isEmpty()) {
            whereClause = new StringJoiner(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        }

        String order = buildOrderStatement(SORT_DESC, Alias.BatchEquipmentUploadLog + "createdAt");
        String page = buildPageStatement((Objects.nonNull(queryDTO.getOffset()) ? queryDTO.getOffset() : 0),
                Objects.nonNull(queryDTO.getLimit()) ? queryDTO.getLimit() : 50);

        String finalQuery = query.append(whereClause).append(order).append(page).toString();
        logger.info("getAllBatchEquipmentUploadLogs finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<BatchEquipmentUploadLogDTO> paginationResponseDTO = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<BatchEquipmentUploadLogDTO> uploadLogDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rawDataList)) {
            for (Object[] rawData : rawDataList) {
                BatchEquipmentUploadLogDTO uploadLogDTO = new BatchEquipmentUploadLogDTO(rawData);
                uploadLogDataList.add(uploadLogDTO);
            }

            paginationDTO.setTotal(countUploadLogs(queryDTO, whereClause, params));
            paginationDTO.setListSize(uploadLogDataList.size());
            paginationDTO.setOffset(queryDTO.getOffset());
            paginationDTO.setLimit(queryDTO.getLimit());

        }

        paginationResponseDTO.setPagination(paginationDTO);
        paginationResponseDTO.setData(uploadLogDataList);
        
        return paginationResponseDTO;
    }

    public Integer countUploadLogs(BatchEquipmentUploadLogQueryDTO queryDTO, StringJoiner whereClause, HashMap<String, Object> params) throws Exception {
        StringBuilder query = new StringBuilder(BatchEquipmentUploadLogSQL.COUNT_ALL_UPLOAD_LOGS);
        String finalQuery = query.append(whereClause).toString();
        return Integer.valueOf(dataAccessService.readNative(finalQuery, params).iterator().next().toString());
    }
    
    public BatchEquipmentUploadLogDTO getBatchEquipmentUploadDTOById(String id) throws Exception {
        StringBuilder query = new StringBuilder(BatchEquipmentUploadLogSQL.GET_ALL_UPLOAD_LOGS);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(SqlStatementUtils.Condition.AND, SqlStatementUtils.Condition.WHERE, StringUtils.EMPTY);
        whereClause.add(buildEqualStatement(Alias.BatchEquipmentUploadLog, "id", "id"));
        params.put("id", id);

        String finalQuery = query.append(whereClause).toString();
        logger.info("getBatchEquipmentsUploadDTOById finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        BatchEquipmentUploadLogDTO batchEquipmentUploadLogDTO = new BatchEquipmentUploadLogDTO();
        if(CollectionUtils.isNotEmpty(rawDataList)) {
            batchEquipmentUploadLogDTO = new BatchEquipmentUploadLogDTO(rawDataList.get(0));
        }

        return batchEquipmentUploadLogDTO;
    }

    public BatchEquipmentUploadLog getBatchEquipmentUploadById(String id) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("id", id);
        List<BatchEquipmentUploadLog> batchEquipmentUploadLogList = (List<BatchEquipmentUploadLog>) dataAccessService.read(BatchEquipmentUploadLog.class, BatchEquipmentUploadLogSQL.GET_BATCH_LOG_BY_ID, params);
        if (CollectionUtils.isEmpty(batchEquipmentUploadLogList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No upload log be found with id " + id);
        }
        return batchEquipmentUploadLogList.get(0);
    }

    public BatchEquipmentUploadLog getBatchEquipmentUploadByFileId(String fileId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("fileId", fileId);
        List<BatchEquipmentUploadLog> batchEquipmentUploadLogList = (List<BatchEquipmentUploadLog>) dataAccessService.read(BatchEquipmentUploadLog.class, BatchEquipmentUploadLogSQL.GET_BATCH_LOG_BY_FILE_ID, params);
        if (CollectionUtils.isEmpty(batchEquipmentUploadLogList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No upload log be found with fileId " + fileId);
        }
        return batchEquipmentUploadLogList.get(0);
    }

    public BatchEquipmentUploadLog getBatchEquipmentUploadByReportFileId(String reportFileId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("reportFileId", reportFileId);
        List<BatchEquipmentUploadLog> batchEquipmentUploadLogList = (List<BatchEquipmentUploadLog>) dataAccessService.read(BatchEquipmentUploadLog.class, BatchEquipmentUploadLogSQL.GET_BATCH_LOG_BY_REPORT_FILE_ID, params);
        if (CollectionUtils.isEmpty(batchEquipmentUploadLogList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No upload log be found with fileId " + reportFileId);
        }
        return batchEquipmentUploadLogList.get(0);
    }

    public void updateUploadLog(BatchEquipmentUploadLog batchEquipmentUploadLog) throws Exception {
        dataAccessService.update(BatchEquipmentUploadLog.class, batchEquipmentUploadLog);
    }

    public void deleteById(String id) throws Exception {
        dataAccessService.delete(BatchEquipmentUploadLog.class, id);
    }
}
