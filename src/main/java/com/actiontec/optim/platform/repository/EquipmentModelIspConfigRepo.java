package com.actiontec.optim.platform.repository;

import com.incs83.app.constants.queries.EquipmentModelFirmwareSQL;
import com.incs83.app.constants.queries.EquipmentModelIspConfigSQL;
import com.incs83.mt.DataAccessService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Component
public class EquipmentModelIspConfigRepo {

    @Autowired
    private DataAccessService dataAccessService;

    public boolean existsByEquipmentModelId(String equipmentModelId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("equipmentModelId", equipmentModelId);
        List<Object> result = dataAccessService.readNative(EquipmentModelIspConfigSQL.EXISTS_BY_EQUIPMENT_MODEL_ID, params);

        if (CollectionUtils.isNotEmpty(result)) {
            Object count = result.get(0);
            return count != null && ((Number) count).longValue() > 0;
        }
        return false;
    }

    public boolean existsByFirmwareId(String firmwareId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("firmwareId", firmwareId);
        List<Object> result = dataAccessService.readNative(EquipmentModelIspConfigSQL.EXISTS_BY_FIRMWARE_ID, params);

        if (CollectionUtils.isNotEmpty(result)) {
            Object count = result.get(0);
            return count != null && ((Number) count).longValue() > 0;
        }
        return false;
    }

}
