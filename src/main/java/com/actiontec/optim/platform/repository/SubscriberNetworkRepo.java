package com.actiontec.optim.platform.repository;

import com.incs83.app.entities.NCSEquipment;
import com.incs83.app.entities.SubscriberNetwork;
import com.incs83.mt.DataAccessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import parquet.it.unimi.dsi.fastutil.Hash;

import java.util.HashMap;
import java.util.List;

import static com.incs83.app.constants.queries.SubscriberNetworkSQL.*;

@Component
public class SubscriberNetworkRepo {
    @Autowired
    DataAccessService dataAccessService;

    public List<SubscriberNetwork> getSubscriberNetworkBySubscriberId(String subscriberId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("subscriberId", subscriberId);
        return (List<SubscriberNetwork>) dataAccessService.read(SubscriberNetwork.class, GET_SUBSCRIBER_NETWORK_BY_SUBSCRIBER_ID, params);
    }

    public void deleteBySubscriberId(String subscriberId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("subscriberId", subscriberId);
        dataAccessService.deleteUpdateNative(DELETE_SUBSCRIBER_NETWORK_BY_SUBSCRIBER_ID, params);
    }

    public List<SubscriberNetwork> getSubscriberNetworkByNetworkId(String networkId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("networkId", networkId);
        return (List<SubscriberNetwork>) dataAccessService.read(SubscriberNetwork.class, GET_SUBSCRIBER_NETWORK_BY_NETWORK_ID, params);
    }

    public void create(String subscriberId, String networkId) throws Exception {
        SubscriberNetwork subscriberNetwork = new SubscriberNetwork();
        subscriberNetwork.setSubscriberId(subscriberId);
        subscriberNetwork.setNetworkId(networkId);
        dataAccessService.create(SubscriberNetwork.class,subscriberNetwork);
    }

    public void deleteByNetworkId(String networkId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("networkId", networkId);
        dataAccessService.deleteUpdateNative(DELETE_SUBSCRIBER_NETWORK_BY_NETWORK_ID, params);
    }
}
