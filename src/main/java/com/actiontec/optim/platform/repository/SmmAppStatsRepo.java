package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.CpeSmmAppStatsDao;
import com.actiontec.optim.mongodb.dto.CpeSmmAppStatsDto;
import com.actiontec.optim.platform.model.SmmAppStats;
import com.actiontec.optim.platform.model.SmmAppStatsUsage;
import com.actiontec.optim.platform.model.SmmAppStatsValues;
import com.actiontec.optim.platform.model.enums.SmmAppStateEnum;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class SmmAppStatsRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private CpeSmmAppStatsDao cpeSmmAppStatsDao;

    private SmmAppStats toSmmAppStats(CpeSmmAppStatsDto cpeSmmAppStatsDto) {

        SmmAppStatsUsage cpuUsage = new SmmAppStatsUsage();
        cpuUsage.setAvg(cpeSmmAppStatsDto.getAvgCpu());;
        cpuUsage.setMax(cpeSmmAppStatsDto.getMaxCpu());
        cpuUsage.setMin(cpeSmmAppStatsDto.getMinCpu());
        cpuUsage.setTotal(cpeSmmAppStatsDto.getTotalCpu());

        SmmAppStatsUsage memUsed = new SmmAppStatsUsage();
        memUsed.setAvg(cpeSmmAppStatsDto.getAvgMem());
        memUsed.setMax(cpeSmmAppStatsDto.getMaxMem());
        memUsed.setMin(cpeSmmAppStatsDto.getMinMem());

        SmmAppStatsUsage fsUsed = new SmmAppStatsUsage();
        fsUsed.setAvg(cpeSmmAppStatsDto.getAvgFsUsed());
        fsUsed.setMax(cpeSmmAppStatsDto.getMaxFsUsed());
        fsUsed.setMin(cpeSmmAppStatsDto.getMinFsUsed());

        SmmAppStatsValues smmAppStatsValues = new SmmAppStatsValues();
        smmAppStatsValues.setCount(cpeSmmAppStatsDto.getCount());
        smmAppStatsValues.setCpuUsage(cpuUsage);
        smmAppStatsValues.setMemoryUsed(memUsed);
        smmAppStatsValues.setFsUsed(fsUsed);

        SmmAppStats smmAppStats = new SmmAppStats();
        smmAppStats.setServiceId(cpeSmmAppStatsDto.getServiceId());
        smmAppStats.setServiceVersion(cpeSmmAppStatsDto.getVersion());
        smmAppStats.setActionState(cpeSmmAppStatsDto.getActionState());
        smmAppStats.setDesiredState(cpeSmmAppStatsDto.getDesiredState());
        smmAppStats.setDuration(0L);
        smmAppStats.setValues(smmAppStatsValues);

        return smmAppStats;
    }

    public List<SmmAppStats> findSmmAppStatsByIsp(String isp) {

        List<SmmAppStats> smmAppStatsList = new ArrayList<>();

        Long timestamp = cpeSmmAppStatsDao.getLastTimestamp(isp);
        List<CpeSmmAppStatsDto> cpeSmmAppStatsDtoList = cpeSmmAppStatsDao.findCpeSmmAppStatsByIspAndTimestamp(isp, timestamp);

        for(CpeSmmAppStatsDto cpeSmmAppStatsDto : cpeSmmAppStatsDtoList) {
            if(SmmAppStateEnum.checkState(cpeSmmAppStatsDto.getDesiredState(), cpeSmmAppStatsDto.getActionState())) {
                smmAppStatsList.add(toSmmAppStats(cpeSmmAppStatsDto));
            } else {
                logger.warn("Unexpected state: isp:[{}], serviceId:[{}], actionState:[{}], desiredState:[{}], timestamp:[{}]",
                        cpeSmmAppStatsDto.getIsp(),
                        cpeSmmAppStatsDto.getServiceId(),
                        cpeSmmAppStatsDto.getActionState(),
                        cpeSmmAppStatsDto.getDesiredState(),
                        timestamp);
            }
        }

        return smmAppStatsList;
    }
}
