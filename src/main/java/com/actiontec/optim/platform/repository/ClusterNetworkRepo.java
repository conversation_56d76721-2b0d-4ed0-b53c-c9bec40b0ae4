package com.actiontec.optim.platform.repository;

import com.incs83.app.entities.ClusterNetwork;
import com.incs83.mt.DataAccessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;

import static com.incs83.app.constants.queries.ClusterNetworkSQL.DELETE_CLUSTER_NETWORK_BY_NETWORK_ID;

@Component
public class ClusterNetworkRepo {
    @Autowired
    DataAccessService dataAccessService;

    public void create(String defaultClusterId, String networkId) throws Exception {
        ClusterNetwork clusterNetwork = new ClusterNetwork();
        clusterNetwork.setClusterId(defaultClusterId);
        clusterNetwork.setNetworkId(networkId);
        dataAccessService.create(ClusterNetwork.class,clusterNetwork);
    }

    public void deleteByNetworkId(String networkId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("networkId", networkId);
        dataAccessService.deleteUpdateNative(DELETE_CLUSTER_NETWORK_BY_NETWORK_ID, params);
    }
}
