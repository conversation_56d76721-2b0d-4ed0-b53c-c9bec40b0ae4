package com.actiontec.optim.platform.repository;

import com.incs83.app.constants.queries.AppCloudConfigSQL;
import com.incs83.app.entities.AppCloudConfig;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;

@Repository
public class AppCloudRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    public AppCloudConfig getAppCloudConfigByKid(String kid) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("kid", kid);
        
        List<AppCloudConfig> configList = (List<AppCloudConfig>) dataAccessService.read(
            AppCloudConfig.class, 
            AppCloudConfigSQL.GET_APP_CLOUD_CONFIG_BY_KID, 
            params
        );
        
        if (CollectionUtils.isEmpty(configList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No App Cloud Config found with kid: " + kid);
        }
        
        return configList.get(0);
    }

    public boolean create(AppCloudConfig appCloudConfig) throws Exception {
        return dataAccessService.create(AppCloudConfig.class, appCloudConfig);
    }

    public void update(AppCloudConfig appCloudConfig) throws Exception {
        dataAccessService.update(AppCloudConfig.class, appCloudConfig);
    }
}
