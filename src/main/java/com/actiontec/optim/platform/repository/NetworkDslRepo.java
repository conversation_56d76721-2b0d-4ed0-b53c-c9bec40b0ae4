package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.platform.mapper.NetworkDslMapper;
import com.actiontec.optim.platform.model.NetworkDsl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import parquet.it.unimi.dsi.fastutil.Hash;

import java.util.HashMap;
import java.util.List;

@Component
public class NetworkDslRepo {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private NetworkDslMapper networkDslMapper;

    public NetworkDsl findDslBySerialNumber(String serial) {

        HashMap<String, Object> dslData = apDetailDao.findDslBySerialNumber(serial);
        List<ApDetailDto.DslDto> dslList = (List<ApDetailDto.DslDto>) dslData.get("dsls");
        if(dslList.isEmpty()) {
            return null;
        }
        Long timestamp = (Long) dslData.get("timestamp");

        NetworkDsl networkDsl = new NetworkDsl();
        networkDsl.setId("1");
        networkDsl.setLines(networkDslMapper.toLinesDto(dslList));
        networkDsl.setLastReportTime(timestamp);

        return networkDsl;
    }
}
