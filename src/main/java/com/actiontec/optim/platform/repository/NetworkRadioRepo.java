package com.actiontec.optim.platform.repository;

import com.actiontec.optim.mongodb.dao.ApDetailDao;
import com.actiontec.optim.mongodb.dao.ApWiFiInsightsPerMinuteDao;
import com.actiontec.optim.mongodb.dto.ApDetailDto;
import com.actiontec.optim.mongodb.dto.RadioDataDto;
import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.actiontec.optim.platform.mapper.NetworkRadioMapper;
import com.actiontec.optim.platform.model.NetworkRadio;
import com.actiontec.optim.platform.model.NetworkRadioSeries;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
public class NetworkRadioRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ApDetailDao apDetailDao;

    @Autowired
    private ApWiFiInsightsPerMinuteDao apWiFiInsightsPerMinuteDao;

    @Autowired
    private NetworkRadioMapper networkRadioMapper;

    public List<NetworkRadio> findByUserIdAndSerial(String userId, String serial) {
        ApDetailDto apDetailDto = apDetailDao.findRadioByUserIdAndSerial(userId, serial);
        List<NetworkRadio> networkRadios;
        if (apDetailDto != null) {
            networkRadios = networkRadioMapper.toNetworkRadio(apDetailDto.getWifiRadios());
            for (NetworkRadio networkRadio : networkRadios) {
                networkRadio.setSerialNumber(apDetailDto.getSerialNumber());
                networkRadio.setLastReportTime(apDetailDto.getNtReportTimestamp());
            }
        } else {
            networkRadios = new ArrayList<>();
        }
        return networkRadios;
    }

    public Optional<NetworkRadio> findByUserIdAndSerialAndRadio(String userId, String serial, RadioEnum radioEnum) {
        NetworkRadio result = null;
        ApDetailDto apDetailDto = apDetailDao.findRadioByUserIdAndSerial(userId, serial);

        for (ApDetailDto.RadioDto radioDto : apDetailDto.getWifiRadios()) {
            if (radioEnum == radioDto.getRadioKey()) {
                if (result == null) {
                    // XXX: set serial in mapper
                    result = networkRadioMapper.toNetworkRadio(radioDto);
                    result.setSerialNumber(apDetailDto.getSerialNumber());
                    result.setLastReportTime(apDetailDto.getNtReportTimestamp());
                } else {
                    //should not happen, 2 radios have same radioKey
                    logger.error("duplicate radio in ApDetail.wifiRadios userId:[{}] serial:[{}] radio:[{}]", userId, serial, radioEnum.getRadioKey());
                    throw new RuntimeException("Duplicate radio in ApDetail.wifiRadios");
                }
            }
        }
        return Optional.ofNullable(result);
    }

    public List<NetworkRadioSeries> findSeriesByUserIdAndSerialAndRadio(String userId, String serial, String radioKey, long duration) {

        List<NetworkRadioSeries> networkRadioSeriesList;

        List<RadioDataDto> radioDataDtoList = apWiFiInsightsPerMinuteDao.getHourlyDataList(userId, serial, radioKey, duration);
        if(radioDataDtoList != null) {
            networkRadioSeriesList = networkRadioMapper.toNetworkRadioSeries(radioDataDtoList);
        } else {
            networkRadioSeriesList = new ArrayList<>();
        }

        return networkRadioSeriesList;
    }
}
