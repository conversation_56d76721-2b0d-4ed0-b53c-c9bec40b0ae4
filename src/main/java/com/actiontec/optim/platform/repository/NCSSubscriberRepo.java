package com.actiontec.optim.platform.repository;

import com.actiontec.optim.platform.api.v6.dto.*;
import com.actiontec.optim.util.CustomStringUtils;
import com.incs83.app.constants.queries.SubscriberSQL;
import com.incs83.app.entities.Subscriber;
import com.incs83.app.utils.SqlStatementUtils;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.*;

import static com.incs83.app.constants.queries.SubscriberSQL.GET_SUBSCRIBER_LIST_BY_SUBSCRIBER_ID;
import static com.incs83.app.utils.SqlStatementUtils.*;

@Component
public class NCSSubscriberRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    DataAccessService dataAccessService;

    public PaginationResponse<NCSSubscriberDTO> getAllSubscribers(NCSSubscriberQueryDTO queryDTO) throws Exception {
        StringBuilder query = new StringBuilder(SubscriberSQL.GET_SUBSCRIBER_V6);
        HashMap<String, Object> params = new HashMap<>();

        StringJoiner whereClause = new StringJoiner(SqlStatementUtils.Condition.AND, SqlStatementUtils.Condition.WHERE, StringUtils.EMPTY);

        // use reflection to generate dynamic query string.
        Field[] fields = queryDTO.getClass().getDeclaredFields();
        for (Field field : fields) {
            // allow access private attribute.
            field.setAccessible(true);
            Object fieldValue = field.get(queryDTO);
            if (field.getDeclaringClass().equals(PaginationRequest.class) || ObjectUtils.isEmpty(fieldValue)) {
                continue;
            }

            String columnNativeName = SqlStatementUtils.getNativeColumnName(field);

            if (StringUtils.equals(field.getName(), "ispId")) {
                whereClause.add(buildEqualStatement(Alias.Compartment, columnNativeName, field.getName()));
            } else if (StringUtils.equals(field.getName(), "firstName") || StringUtils.equals(field.getName(), "lastName")) {
                fieldValue = fieldValue + "%";
                whereClause.add(buildLikeStatement(Alias.Subscriber, columnNativeName, field.getName()));
            }
            logger.info("field name: {}, field value: {}", field.getName(), fieldValue.toString());

            params.put(field.getName(), fieldValue);
        }

        // groupAdmin can only search within their own group
        if (CommonUtils.isGroupAdmin()) {
            whereClause.add(buildEqualStatement(Alias.SubscriberCompartment, "compartment_id", "groupId"));
            params.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
        }

        // query without any parameters
        if (params.isEmpty()) {
            whereClause = new StringJoiner(StringUtils.EMPTY, StringUtils.EMPTY, StringUtils.EMPTY);
        }

        String page = buildPageStatement((Objects.nonNull(queryDTO.getOffset()) ? queryDTO.getOffset() : 0),
                Objects.nonNull(queryDTO.getLimit()) ? queryDTO.getLimit() : 50);

        String finalQuery = query.append(whereClause).append(page).toString();
        logger.info("getAllSubscribers finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        PaginationResponse<NCSSubscriberDTO> paginationResponseDTO = new PaginationResponse<>();
        PaginationDTO paginationDTO = new PaginationDTO();
        List<NCSSubscriberDTO> subscriberDataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rawDataList)) {
            for (Object[] rawData : rawDataList) {
                // todo: v6 user will be refactor, subscriber cannot be mapped to an user currently, so userId be save as empty string temporarily.
                NCSSubscriberDTO subscriberDTO = new NCSSubscriberDTO(rawData, StringUtils.EMPTY);
                subscriberDataList.add(subscriberDTO);
            }

            paginationDTO.setTotal(countSubscribers(queryDTO, whereClause, params));
            paginationDTO.setListSize(subscriberDataList.size());
            paginationDTO.setOffset(queryDTO.getOffset());
            paginationDTO.setLimit(queryDTO.getLimit());

        }
        paginationResponseDTO.setPagination(paginationDTO);
        paginationResponseDTO.setData(subscriberDataList);

        return paginationResponseDTO;
    }

    private Integer countSubscribers(NCSSubscriberQueryDTO queryDTO, StringJoiner whereClause, HashMap<String, Object> params) throws Exception {
        StringBuilder query = new StringBuilder(SubscriberSQL.COUNT_ALL_SUBSCRIBER_V6);
        String finalQuery = query.append(whereClause).toString();
        return Integer.valueOf(dataAccessService.readNative(finalQuery, params).iterator().next().toString());
    }

    public NCSSubscriberDTO getSubscriberDTOById(String id) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        StringBuilder query = new StringBuilder(SubscriberSQL.GET_SUBSCRIBER_V6);
        StringJoiner whereClause = new StringJoiner(Condition.AND, Condition.WHERE, StringUtils.EMPTY);
        whereClause.add(buildEqualStatement(Alias.Subscriber, "id", "id"));

        params.put("id", id);

        // groupAdmin can only search within their own group
        if (CommonUtils.isGroupAdmin()) {
            whereClause.add(buildEqualStatement(Alias.SubscriberCompartment, "compartment_id", "groupId"));
            params.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
        }

        String finalQuery = query.append(whereClause).toString();
        logger.info("getEquipment finalQuery: " + finalQuery);

        List<Object[]> rawDataList = dataAccessService.readNative(finalQuery, params);
        NCSSubscriberDTO ncsSubscriberDTO = new NCSSubscriberDTO();
        if (CollectionUtils.isNotEmpty(rawDataList)) {
            // note: v6 user will be refactor, subscriber cannot be mapped to an user currently, so userId be saved as empty string temporarily.
            ncsSubscriberDTO = new NCSSubscriberDTO(rawDataList.get(0), StringUtils.EMPTY);
        }
        return ncsSubscriberDTO;
    }

    public Subscriber getSubscriberById(String id) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("id", id);
        List<Subscriber> subscriberList = (List<Subscriber>) dataAccessService.read(Subscriber.class, GET_SUBSCRIBER_LIST_BY_SUBSCRIBER_ID, params);
        if (CollectionUtils.isEmpty(subscriberList)) {
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "No Subscriber found with id " + id);
        }
        return subscriberList.get(0);
    }

    public boolean create(Subscriber subscriber) throws Exception {
        return dataAccessService.create(Subscriber.class, subscriber);
    }

    public void update(Subscriber subscriber) throws Exception {
        dataAccessService.update(Subscriber.class, subscriber);
    }

    public void deleteById(String id) throws Exception {
        dataAccessService.delete(Subscriber.class, id);
    }
}
