package com.actiontec.optim.platform.repository;

import com.incs83.app.entities.Equipment;
import com.incs83.mt.DataAccessService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_FOR_SUBSCRIBER;

@Component
public class EquipmentRepo {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private DataAccessService dataAccessService;

    // old equipment table, only contains rg
    public List<Equipment> getEquipmentListBySubscriberId(String subscriberId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("subscriberId", subscriberId);
        return (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_FOR_SUBSCRIBER, params);
    }
}
