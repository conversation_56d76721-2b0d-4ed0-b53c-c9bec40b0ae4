package com.actiontec.optim.platform.exception;

public class CpeRpcResultException extends RuntimeException {
    public enum FailedReason {
        NO_CORRELATED_RECORD,
        INVALID_RESPONSE,
        FAILED_RESPONSE,
        TIMEOUT
    }

    private FailedReason failedReason;

    public CpeRpcResultException(FailedReason failedReason, Throwable cause) {
        super(cause);
        this.failedReason = failedReason;
    }

    public CpeRpcResultException(FailedReason failedReason) {
        this.failedReason = failedReason;
    }
}
