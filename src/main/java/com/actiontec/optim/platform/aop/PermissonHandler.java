package com.actiontec.optim.platform.aop;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.PermissionHandle;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.Equipment;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.Objects;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;


@Aspect
@Component
@Order(2)
public class PermissonHandler {
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private At3Adapter at3Adapter;

    private static final Logger logger = LogManager.getLogger("PermissionHandler");

    @Pointcut("@annotation(com.actiontec.optim.platform.annotation.PermissionHandle)")
    public void handlePointCut() {
    }

    @Before("handlePointCut()")
    public void before(JoinPoint jp) throws Throwable {
        String stn = jp.getArgs()[0].toString();
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(stn);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        MethodSignature signature = (MethodSignature) jp.getSignature();
        Method method = signature.getMethod();
        PermissionHandle permissionHandle = method.getAnnotation(PermissionHandle.class);

        if ( permissionHandle != null) {
           if (permissionHandle.checkSubscriber() == true ) {
                String equipmentId = jp.getArgs()[1].toString();
                manageCommonService.checkAPSerialNumBelongsToSubscriber(equipmentId, userEquipment);
            }

            if (permissionHandle.checkStatus() == true) {
                BasicDBObject query = new BasicDBObject();
                String equipmentId = jp.getArgs()[1].toString();
                query.put("userId", userEquipment.getRgwSerial());
                query.put("serialNumber", equipmentId);

                BasicDBObject mongoFieldOptions = new BasicDBObject();
                mongoFieldOptions.put("_id", 0);

                DBObject apDetail = at3Adapter.getMongoDbCollection(AP_DETAIL).findOne(query, mongoFieldOptions);
                if (true == manageCommonService.getCurrentDevStatus(apDetail))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The equipment is offline.");
                }
        }
    }
}