package com.actiontec.optim.platform.aop;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.OperationHandle;
import com.actiontec.optim.platform.constant.OperationConstants;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.Equipment;
import com.incs83.exceptions.handler.ValidationException;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;


import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Objects;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;


@Aspect
@Component
@Order(5)
public class OperationHandler {
    private  final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private ManageCommonService manageCommonService;
    @Value("${mesh.enable:false}")
    private Boolean meshEnable;


    @Pointcut("@annotation(com.actiontec.optim.platform.annotation.OperationHandle)")
    public void handlePointCut() {
    }

    @Before("handlePointCut()")
    public void before(JoinPoint jp) throws Throwable {
        if (meshEnable == false) {
            return;
        }
        MethodSignature signature = (MethodSignature) jp.getSignature();
        Method method = signature.getMethod();
        String stn = jp.getArgs()[0].toString();

        OperationHandle operationHandle = method.getAnnotation(OperationHandle.class);
        String op = operationHandle.operation();
        BasicDBObject query = new BasicDBObject();
        DBObject dbObject = null;

        Equipment equipment = manageCommonService.getUserAPWithoutAccessChecking(stn);
        if (StringUtils.equals(op, OperationConstants.WIFI_OP)) {
            String equipmentId = jp.getArgs()[1].toString();

            query.put("userId", equipment.getRgwSerial());
            query.put("serialNumber", equipmentId);

            ArrayList<DBObject> list = new ArrayList<DBObject>();
            BasicDBObject or1 = new BasicDBObject("controller", true);
            BasicDBObject or2 = new BasicDBObject().append("controller", new BasicDBObject().append("$exists", false));
            list.add(or1);
            list.add(or2);

            query.put("$or", list);

            dbObject = at3Adapter.getMongoDbCollection(AP_DETAIL).findOne(query, new BasicDBObject().append("_id", 0));
            if (Objects.isNull(dbObject))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "WiFi operation will be send to controller only.");
        } else if (StringUtils.equals(op, OperationConstants.STEERING_OP)) {
            query.put("userId", equipment.getRgwSerial());
            ArrayList<DBObject> list = new ArrayList<DBObject>();
            BasicDBObject or1 = new BasicDBObject("controller", true);
            BasicDBObject or2 = new BasicDBObject().append("controller", new BasicDBObject().append("$exists", false));
            list.add(or1);
            list.add(or2);

            query.put("$or", list);
            dbObject = at3Adapter.getMongoDbCollection(AP_DETAIL).findOne(query, new BasicDBObject().append("_id", 0));
            if (Objects.isNull(dbObject))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Steering operation will be send to controller only.");
        } else if (StringUtils.equals(op, OperationConstants.FSECURE_OP)) {
            query.put("userId", equipment.getRgwSerial());
            query.put("type", GATEWAY);
            dbObject = at3Adapter.getMongoDbCollection(AP_DETAIL).findOne(query, new BasicDBObject().append("_id", 0));
            if (Objects.isNull(dbObject))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Fsecure operation will be send to Gateway only.");
        }
    }

}