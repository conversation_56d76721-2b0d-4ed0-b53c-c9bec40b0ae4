package com.actiontec.optim.platform.aop;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.annotation.AuditLog;
import com.actiontec.optim.service.AuditService;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Objects;


@Aspect
@Component
@Order(2)
public class AuditLogger {
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private At3Adapter at3Adapter;

    private static final Logger logger = LogManager.getLogger("rpcHandler");

    @AfterReturning(pointcut="@annotation(com.actiontec.optim.platform.annotation.AuditLog)", returning="retVal")
    public void afterReturning(JoinPoint jp, Object retVal) throws Throwable {
        MethodSignature signature = (MethodSignature) jp.getSignature();
        Method method = signature.getMethod();
        AuditLog auditLog = method.getAnnotation(AuditLog.class);
        int size = jp.getArgs().length;
        String stn = jp.getArgs()[0].toString();
        String equipmentId = jp.getArgs()[1].toString();

        Object reqContent = jp.getArgs()[2];
        HttpServletRequest httpServletRequest = (HttpServletRequest) jp.getArgs()[size - 1];
        String op = auditLog.operation();

        auditService.createAuditLog(CommonUtils.getUserIdOfLoggedInUser(), at3Adapter.getRgwSerialByStn(stn), op, reqContent, Objects.isNull(retVal)?"204":"200", retVal, httpServletRequest);
    }

}