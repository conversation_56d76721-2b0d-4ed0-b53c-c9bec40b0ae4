package com.actiontec.optim.service;

import com.datastax.driver.core.utils.UUIDs;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.config.KafkaConfig;
import com.incs83.pubsub.KafkaPublisher;
import com.incs83.util.ClientInfoUtils;
import com.incs83.util.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.HashMap;

import static com.incs83.app.constants.misc.ActiontecConstants.PLATFORM_LOGS;
import static com.incs83.app.constants.misc.ActiontecConstants.SUG_ACT_TYPE;

@Service
public class AuditService {

    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private ClientInfoUtils clientInfoUtils;

    @Autowired
    private KafkaPublisher kafkaPublisher;

    @Autowired
    private KafkaConfig kafkaConfig;

    @Autowired
    private ObjectMapper objectMapper;

    public void createAuditLog(String userId, String serial, String operation, Object payload, String responseCode, Object responseMessage, HttpServletRequest request) throws Exception {
        HashMap<String, Object> auditLogs = new HashMap();
        auditLogs.put(SUG_ACT_TYPE, PLATFORM_LOGS);
        auditLogs.put("id", UUIDs.timeBased().toString().replaceAll("-", ""));
        auditLogs.put("userId", userId != null ? userId : "");
        auditLogs.put("ip", String.valueOf(clientInfoUtils.getClientIpAddr(request)));
        auditLogs.put("url", request.getRequestURI());
        auditLogs.put("apId", serial != null ? serial : "");
        auditLogs.put("time", Calendar.getInstance().getTimeInMillis());
        auditLogs.put("method",  request.getMethod());
        auditLogs.put("operation", operation);
        auditLogs.put("payload", payload != null ? StringUtils.left(objectMapper.writeValueAsString(payload), 8000) : "");
        auditLogs.put("responseCode", responseCode);
        auditLogs.put("responseMessage", responseMessage != null ? StringUtils.left(objectMapper.writeValueAsString(responseMessage), 8000) : "");
        kafkaPublisher.publishAuditLogsToKafka(auditLogs, kafkaConfig.getTopic());
    }
}
