package com.actiontec.optim.service;

import com.actiontec.optim.platform.exception.CpeRpcResultException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.pubsub.KafkaPublisher;
import com.incs83.pubsub.MQTTService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.incs83.app.constants.misc.ActiontecConstants.JSON_RPC_V3_INFO;
import static com.incs83.app.constants.misc.ApplicationConstants.UC_CONTROL_TOPIC;
import static com.incs83.constants.ApplicationCommonConstants.AP_DETAIL;

@Service
public class CpeRpcService {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Value("${uCentralEnabled:false}")
    private Boolean uCentralEnabled;

    private static final int DEFAULT_MAX_TRIES = 3;
    private static final long DEFUALT_RETRY_INTERVAL_MILLIS = 5000L;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private MQTTService mqttService;

    @Autowired
    private KafkaPublisher kafkaPublisher;

    @Autowired
    private ObjectMapper objectMapper;

    private Boolean isUcentralEquipment(String userId, String serial) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("serialNumber", serial);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("srcAgent", 1);
        BasicDBObject apDetailDbObj = (BasicDBObject) mongoService.findOne(params, new HashMap<>(), AP_DETAIL, mongoFieldOptions);
        if(apDetailDbObj != null) {
            String srcAgent = apDetailDbObj.getString("srcAgent");
            if(srcAgent != null) {
                if (srcAgent.equals("uCentral")) {
                    return true;
                }
            }
        }
        return false;
    }

    private void sendRpcToKafka(String serial, HashMap<String, String> publishParams, String pubMsgTemplate, String topic) {
        try {
            Map.Entry entry;
            Iterator itr = publishParams.entrySet().iterator();
            while(itr.hasNext()) {
                entry = (Map.Entry) itr.next();
                if (Objects.nonNull(entry.getValue())) {
                    pubMsgTemplate = pubMsgTemplate.replaceAll((String) entry.getKey(), ((String) entry.getValue()).replaceAll("\\$", "\\\\\\$").replaceAll(Pattern.quote("\\\\"), Matcher.quoteReplacement("\\\\\\\\")).replaceAll("\"", "\\\\\""));
                } else {
                    pubMsgTemplate = pubMsgTemplate.replaceAll((String) entry.getKey(), (String) entry.getValue());
                }
            }

            HashMap<String, Object> data = new HashMap<>();
            data.put("serial", serial);
            data.put("rpc", pubMsgTemplate);
            kafkaPublisher.publishToKafka(data, UC_CONTROL_TOPIC);
        } catch (Exception ex) {
            logger.error("Thread InterruptedException\n" + ex.getMessage());
        }
    }

    public Map<String, Object> sendRpcAndExpectSucceeded(String userId, String serial, String uri, String method, String payload) throws InterruptedException {
        String tid = UUID.randomUUID().toString();
        Map<String, Object> rpcResult = sendRpcAndWaitResult(userId, serial, uri, method, payload);
        if (rpcResult == null || !StringUtils.equals(String.valueOf(rpcResult.get("code")), "200")) {
            logger.debug("rpc return failed. userId:[{}] serial:[{}] uri:[{}] method:[{}]", userId, serial, uri, method);
            throw new CpeRpcResultException(CpeRpcResultException.FailedReason.FAILED_RESPONSE);
        }
        return rpcResult;
    }

    public Map<String, Object> sendRpcAndExpectSucceeded(String userId, String serial, String uri, String method, String payload, int maxTries, long threadToSleep) throws InterruptedException {
        String tid = UUID.randomUUID().toString();
        Map<String, Object> rpcResult = sendRpcAndWaitResult(userId, serial, uri, method, payload, maxTries, threadToSleep, tid);
        if (rpcResult == null || !StringUtils.equals(String.valueOf(rpcResult.get("code")), "200")) {
            logger.debug("rpc return failed. userId:[{}] serial:[{}] uri:[{}] method:[{}]", userId, serial, uri, method);
            throw new CpeRpcResultException(CpeRpcResultException.FailedReason.FAILED_RESPONSE);
        }
        return rpcResult;
    }

    public Map<String, Object> sendRpcAndWaitResult(String userId, String serial, String uri, String method, String payload) throws InterruptedException {
        String tid = UUID.randomUUID().toString();
        return sendRpcAndWaitResult(userId, serial, uri, method, payload, DEFAULT_MAX_TRIES, DEFUALT_RETRY_INTERVAL_MILLIS, tid);
    }

    public Map<String, Object> sendRpcAndWaitResult(String userId, String serial, String uri, String method, String payload, int maxTries, long threadToSleep) throws InterruptedException {
        String tid = UUID.randomUUID().toString();
        return sendRpcAndWaitResult(userId, serial, uri, method, payload, maxTries, threadToSleep, tid);
    }

    public Map<String, Object> sendRpcAndWaitResult(String userId, String serial, String uri, String method, String payload, int maxTries, long threadToSleep, String tid) throws InterruptedException {
        sendRpc(userId, serial, uri, method, payload, tid);
        return waitRpcResult(tid, maxTries, threadToSleep);
    }

    public String sendRpc(String userId, String serial, String uri, String method, String payload) {
        String tid = UUID.randomUUID().toString();
        sendRpc(userId, serial, uri, method, payload, tid);
        return tid;
    }

    public void sendRpc(String userId, String serial, String uri, String method, String payload, String tid) {
        logger.debug("sendRpc begins. tid:[{}] userId:[{}] sId:[{}] uri:[{}] method:[{}], payload:[{}]", tid, userId, serial, uri, method, payload);

        if (StringUtils.isBlank(tid)) {
            tid = UUID.randomUUID().toString();
            logger.debug("sendRpc but no tid provided, generated tid:[{}]", tid);
        }

        HashMap<String, Object> rpcEntityInMongo = new HashMap<>();
        rpcEntityInMongo.put("_id", tid);
        rpcEntityInMongo.put("userId", userId);
        rpcEntityInMongo.put("sId", serial);
        rpcEntityInMongo.put("method", method);
        rpcEntityInMongo.put("timestamp", System.currentTimeMillis());
        rpcEntityInMongo.put("dateCreated", new Date());
        rpcEntityInMongo.put("payload", payload);
        mongoService.create(JSON_RPC_V3_INFO, rpcEntityInMongo);

        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-USER_ID-", userId);
        publishParam.put("-S_ID-", serial);
        publishParam.put("-URI-", uri);
        publishParam.put("-METHOD-", method);
        publishParam.put("-PAYLOAD-", payload);

        if(uCentralEnabled) {
            if(isUcentralEquipment(userId, serial)) {
                sendRpcToKafka(serial, publishParam, MqttTemplate.MESSAGE_FOR_RPC_30, UC_CONTROL_TOPIC);
            } else {
                mqttService.publishToMqtt(publishParam, MqttTemplate.MESSAGE_FOR_RPC_30, MqttTemplate.TOPIC_FOR_RPC_CALL);
            }
        } else {
            mqttService.publishToMqtt(publishParam, MqttTemplate.MESSAGE_FOR_RPC_30, MqttTemplate.TOPIC_FOR_RPC_CALL);
        }

        logger.debug("sendRpc ended. tid:[{}] userId:[{}] sId:[{}] uri:[{}] method:[{}], payload:[{}]", tid, userId, serial, uri, method, payload);
    }

    public Map<String, Object> readRpcResult(String tid) {
        logger.debug("readRpcResult. tid:[{}]", tid);

        BasicDBObject query = new BasicDBObject();
        query.put("_id", tid);
        DBObject rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);

        if (rpcResult == null) {
            logger.error("readRpcResult failed. no record in mongo. tid:[{}]", tid);
            throw new CpeRpcResultException(CpeRpcResultException.FailedReason.NO_CORRELATED_RECORD);
        }

        if (rpcResult.get("rawResponse") != null && StringUtils.isNotEmpty(String.valueOf(rpcResult.get("rawResponse")))) {
            String responseJsonString = String.valueOf(rpcResult.get("rawResponse"));
            logger.debug("readRpcResult. tid:[{}] response:[{}]", tid, responseJsonString);
            Map<String, Object> rpcRawResponse = null;
            try {
                rpcRawResponse = objectMapper.readValue(responseJsonString, HashMap.class);

                if (StringUtils.equals("900", String.valueOf(rpcRawResponse.get("code")))) {
                    return null;
                }

            } catch (Exception e) {
                throw new CpeRpcResultException(CpeRpcResultException.FailedReason.INVALID_RESPONSE, e);
            }
            return rpcRawResponse;
        } else {
            // no response yet
            return null;
        }
    }

    public Map<String, Object> waitRpcResult(String tid, int maxTries, long threadToSleep) throws InterruptedException {
        Map<String, Object> result = null;
        for (int i = 0; i < maxTries; i++) {
            try {
                Thread.sleep(threadToSleep);
            } catch (InterruptedException e) {
                logger.error("waitingRpcResult interrupted. tid:[ " + tid + "]");
                throw e;
            }

            result = readRpcResult(tid);
            if (result != null) {
                break;
            }
        }

        if (result != null) {
            return result;
        } else {
            throw new CpeRpcResultException(CpeRpcResultException.FailedReason.TIMEOUT);
        }
    }
}