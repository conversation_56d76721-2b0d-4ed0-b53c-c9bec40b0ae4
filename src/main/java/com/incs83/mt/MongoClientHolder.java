package com.incs83.mt;

import com.incs83.config.MongoConfig;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MongoClientHolder {
    @Autowired
    private MongoConfig mongoConfig;

    @Autowired
    private MongoTenantTemplate mongoTenantTemplate;

    public MongoClient getMongoClient() {
        return mongoTenantTemplate.getTenantMongoTemplate(this.mongoConfig.getDatabase());
    }

    public MongoDatabase getDatabase() {
        return this.getMongoClient().getDatabase(this.mongoConfig.getDatabase());
    }

    public MongoCollection getCollection(String collectionName) {
        return this.getDatabase().getCollection(collectionName);
    }
}
