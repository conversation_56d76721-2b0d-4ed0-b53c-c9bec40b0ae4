package com.incs83.app.common.v3;

import com.incs83.auditor.AuditableData;
import com.incs83.enums.sql.Order;

public class TechnicianDashboardDTO implements AuditableData {
    private String serialNumber;
    private String rgwMAC;
    private String name;
    private String email;
    private String phoneNo;
    private String globalAccountNo;
    private String camsAccountNo;
    private String serviceTelephoneNo;
    private String clusterId;
    private Integer offset = Integer.valueOf(0);
    private Integer max = Integer.valueOf(10);
    private Order order = Order.ASC;;
    private String sortBy;
    private String tableName;

    public String getServiceTelephoneNo() {
        return serviceTelephoneNo;
    }

    public void setServiceTelephoneNo(String serviceTelephoneNo) {
        this.serviceTelephoneNo = serviceTelephoneNo;
    }

    public TechnicianDashboardDTO() {
        this.sortBy = "name";
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getRgwMAC() {
        return rgwMAC;
    }

    public void setRgwMAC(String rgwMAC) {
        this.rgwMAC = rgwMAC;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getGlobalAccountNo() {
        return globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public String getCamsAccountNo() {
        return camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getClusterId() {
        return clusterId;
    }

    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }
}
