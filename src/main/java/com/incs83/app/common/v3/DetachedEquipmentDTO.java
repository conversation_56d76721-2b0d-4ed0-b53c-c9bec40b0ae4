package com.incs83.app.common.v3;

import com.incs83.auditor.AuditableData;
import com.incs83.enums.sql.Order;

public class DetachedEquipmentDTO implements AuditableData {
    private String serialNumber;
    private String macAddress;
    private String serviceTelephoneNo;
    private String clusterId;
    private String groupId;
    private Integer offset = Integer.valueOf(0);
    private Integer max = Integer.valueOf(10);
    private Order order = Order.DESC;;
    private String sortBy;
    private String tableName;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getClusterId() {
        return clusterId;
    }

    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    public String getServiceTelephoneNo() {
        return serviceTelephoneNo;
    }

    public void setServiceTelephoneNo(String serviceTelephoneNo) {
        this.serviceTelephoneNo = serviceTelephoneNo;
    }

    public DetachedEquipmentDTO() {
        this.sortBy = "name";
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

}
