package com.incs83.app.common.v3;

public class SteeringData {
    private boolean assocEvent;
    private boolean disAssocEvent;
    private boolean steeringEvent;
    private boolean roamEvent;
    private boolean steeringLog;
    private boolean staConnectState;

    public boolean isAssocEvent() {
        return assocEvent;
    }

    public void setAssocEvent(boolean assocEvent) {
        this.assocEvent = assocEvent;
    }

    public boolean isDisAssocEvent() {
        return disAssocEvent;
    }

    public void setDisAssocEvent(boolean disAssocEvent) {
        this.disAssocEvent = disAssocEvent;
    }

    public boolean isSteeringEvent() {
        return steeringEvent;
    }

    public void setSteeringEvent(boolean steeringEvent) {
        this.steeringEvent = steeringEvent;
    }

    public boolean isRoamEvent() {
        return roamEvent;
    }

    public void setRoamEvent(boolean roamEvent) {
        this.roamEvent = roamEvent;
    }

    public boolean isSteeringLog() {
        return steeringLog;
    }

    public void setSteeringLog(boolean steeringLog) {
        this.steeringLog = steeringLog;
    }

    public boolean isStaConnectState() {
        return staConnectState;
    }

    public void setStaConnectState(boolean staConnectState) {
        this.staConnectState = staConnectState;
    }
}
