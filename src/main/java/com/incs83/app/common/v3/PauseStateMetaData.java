package com.incs83.app.common.v3;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

public class PauseStateMetaData implements AuditableData {

    @ApiModelProperty(notes = "Redirection Page for Pause State")
    private String redirectionPage;

    @ApiModelProperty(notes = "Whitelist URL")
    private String whiteListURL;

    public String getRedirectionPage() {
        return redirectionPage.trim();
    }

    public PauseStateMetaData setRedirectionPage(String redirectionPage) {
        this.redirectionPage = redirectionPage;
        return this;
    }

    public String getWhiteListURL() {
        return whiteListURL.trim();
    }

    public PauseStateMetaData setWhiteListURL(String whiteListURL) {
        this.whiteListURL = whiteListURL;
        return this;
    }

    @Override
    public String toString() {
        return "PauseStateMetaData{" +
                "redirectionPage='" + redirectionPage + '\'' +
                ", whiteListURL='" + whiteListURL + '\'' +
                '}';
    }
}
