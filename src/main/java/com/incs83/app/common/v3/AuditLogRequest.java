package com.incs83.app.common.v3;

import java.util.List;

public class AuditLogRequest {
    private Integer max;
    private Integer offset;
    private String order;
    private String sortBy;
    private String email;
    private String from;
    private String to;
    private Boolean getCalls = false;
    private List<String> operation;
    private String ip;
    private String rgwSerial;
    private List<Integer> responseCode;
    private String firstName ;
    private String lastName ;

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public List<String> getOperation() {
        return operation;
    }

    public void setOperation(List<String> operation) {
        this.operation = operation;
    }

    public Boolean getGetCalls() {
        return getCalls;
    }

    public void setGetCalls(Boolean getCalls) {
        this.getCalls = getCalls;
    }

    public String getRgwSerial() {
        return rgwSerial;
    }

    public void setRgwSerial(String rgwSerial) {
        this.rgwSerial = rgwSerial;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public List<Integer> getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(List<Integer> responseCode) {
        this.responseCode = responseCode;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
}
