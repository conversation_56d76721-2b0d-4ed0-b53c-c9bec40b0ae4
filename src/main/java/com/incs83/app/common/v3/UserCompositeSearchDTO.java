package com.incs83.app.common.v3;

import com.incs83.auditor.AuditableData;
import com.incs83.enums.sql.Order;

import javax.validation.constraints.Pattern;

public class UserCompositeSearchDTO implements AuditableData {
    private String name;
    private String email;
    private String groupName;
    private String roleName;
    private Boolean internalUser;
    private Integer offset = Integer.valueOf(0);
    private Integer max = Integer.valueOf(10);
    private Order order = Order.ASC;
    @Pattern(regexp = "name|email|groupName|roleName|internalUser|createdAt", message = "Invalid Sort By value")
    private String sortBy = "createdAt";
    private String tableName;

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Boolean isInternalUser() {
        return internalUser;
    }

    public void setInternalUser(Boolean internalUser) {
        this.internalUser = internalUser;
    }
}
