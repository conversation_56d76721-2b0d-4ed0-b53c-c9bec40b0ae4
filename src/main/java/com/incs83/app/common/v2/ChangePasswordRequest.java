package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Created 11-Jan-2018
 */
public class ChangePasswordRequest implements AuditableData {
    @NotNull(message = "Old Password Cannot be empty")
    @NotBlank(message = "Old Password Cannot be empty")
//    @ApiModelProperty(notes = "Old Password", required = true)
    @Size(min = 1, message = "Old Password size must be between 1 to 255 character", max = 255)
    private String oldPassword;

    @NotNull(message = "New Password Cannot be empty")
    @Pattern(regexp = "((?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%?=*&]).{10,64})", message = "Password, Invalid characters Found. Rule, At least one lowercase, At least one uppercase, At least one digit, At least one special character, At least it should have 10-64 characters long.")
    @Size(min = 10, max = 64, message = "Input can be min 10 and max 64 characters long")
    private String newPassword;

    private String portalUrl;

    public String getPortalUrl() {
        return portalUrl;
    }

    public void setPortalUrl(String portalUrl) {
        this.portalUrl = portalUrl;
    }

    public String getOldPassword() {
        return Objects.nonNull(oldPassword) ? oldPassword.trim() : oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getNewPassword() {
        return Objects.nonNull(newPassword) ? newPassword.trim() : newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }
}
