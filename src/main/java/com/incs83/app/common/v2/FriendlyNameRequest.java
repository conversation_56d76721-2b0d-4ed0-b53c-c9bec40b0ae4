package com.incs83.app.common.v2;

public class FriendlyNameRequest {

    private String macAddress;

    private String friendlyName;

    private String serialNumber;

    public String getMacAddress() {
        return macAddress == null ? null : macAddress.trim();
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getFriendlyName() {
        return friendlyName == null ? null : friendlyName.trim();
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public String getSerialNumber() {
        return serialNumber == null ? null : serialNumber.trim();
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }
}
