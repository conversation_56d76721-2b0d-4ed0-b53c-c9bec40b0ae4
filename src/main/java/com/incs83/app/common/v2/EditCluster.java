package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;

public class EditCluster implements AuditableData {
    @ApiModelProperty(notes = "Cluster name")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String clusterName;

    @ApiModelProperty(notes = "Cluster description")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String description;

    @ApiModelProperty(notes = "Add Subscribers to the cluster")
    private List<String> addedAps;

    @ApiModelProperty(notes = "Remove Subscribers from cluster")
    private List<String> removedAps;

    public String getClusterName() {
        return Objects.nonNull(clusterName) ? clusterName.trim() : clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getDescription() {
        return Objects.nonNull(description) ? description.trim() : description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getAddedAps() {
        return addedAps;
    }

    public void setAddedAps(List<String> addedAps) {
        this.addedAps = addedAps;
    }

    public List<String> getRemovedAps() {
        return removedAps;
    }

    public void setRemovedAps(List<String> removedAps) {
        this.removedAps = removedAps;
    }
}
