package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Objects;

/**
 * Created by ha<PERSON> on 22/11/17.
 */
public class ChangeNetworkCredsRequest implements AuditableData {

    @ApiModelProperty(notes = "Type can be any one of the combinations of 1,2,5,6 or individual 1,2,5 & 6. They represent the 2.4G & 5G Guest and Primary Networks. 1 - 2.4G Primary, 2 - 2.4G Guest, 5 - 5G Primary & 6 - 5G Guest.\nExample: If the intended is primary and guest 2.4G, supply  1,2", required = true)
    @Pattern(regexp = "1|2|5|6|1,2|1,5|1,6|2,5|2,6|5,6|1,2,5|1,2,6|1,2,5,6", message = "validValues.NetworkType")
    @NotNull(message = "Type cannot be null")
    @Size(min = 1, max = 255, message = "Type size must be between 1 to 255 character")
    private String type;

    @NotNull(message = "Password cannot be null")
    @ApiModelProperty(notes = "Password for the network", required = true)
    @Size(min = 8, max = 63, message = "Password should be between 8-63 characters")
    private String password;

    @NotNull(message = "SSID cannot be null")
    @Size(min = 1, max = 32, message = "SSID should be between 1-32 characters")
    @ApiModelProperty(notes = "SSID for the Network", required = true)
    private String ssid;

    @ApiModelProperty(notes = "SSID serialNumber for the Network", required = true)
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String serialNumber;

    public String getType() {
        return Objects.nonNull(type) ? type.trim() : type;
    }

    public ChangeNetworkCredsRequest setType(String type) {
        this.type = type;
        return this;
    }

    public String getPassword() {
        return Objects.nonNull(password) ? password.trim() : password;
    }

    public ChangeNetworkCredsRequest setPassword(String password) {
        this.password = password;
        return this;
    }

    public String getSsid() {
        return Objects.nonNull(ssid) ? ssid.trim() : ssid;
    }

    public ChangeNetworkCredsRequest setSsid(String ssid) {
        this.ssid = ssid;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    @Override
    public String toString() {
        return "ChangeNetworkCredsRequest{" +
                "type='" + type + '\'' +
                ", password='" + password + '\'' +
                ", ssid='" + ssid + '\'' +
                '}';
    }
}
