package com.incs83.app.common.v2;


import com.incs83.auditor.AuditableData;

import javax.validation.constraints.NotNull;
import java.util.Objects;

public class IndexTTLRequest implements AuditableData {

    @NotNull(message = "Collection cannot be NULL")
    private String collection;

    @NotNull(message = "indexField cannot be NULL")
    private String indexField;
    private long ttl;

    public String getCollection() {
        return Objects.isNull(collection) ? collection : collection.trim();
    }

    public void setCollection(String collection) {
        this.collection = collection;
    }

    public String getIndexField() {
        return Objects.isNull(indexField) ? indexField : indexField.trim();
    }

    public void setIndexField(String indexField) {
        this.indexField = indexField;
    }

    public long getTtl() {
        return ttl;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }

}
