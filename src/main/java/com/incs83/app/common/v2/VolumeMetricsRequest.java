package com.incs83.app.common.v2;

import com.incs83.app.enums.MetricsStatistics;

import javax.validation.constraints.Pattern;
import java.util.List;
import java.util.Objects;

public class VolumeMetricsRequest {
    private List<String> volumeIds;
    private Long startTime;
    private Integer period;
    @Pattern(regexp = "Average|Sum|Minimum|Maximum", message = "Invalid Statistics selected")
    private MetricsStatistics statistics;

    public List<String> getVolumeIds() {
        return volumeIds;
    }

    public void setVolumeIds(List<String> volumeIds) {
        this.volumeIds = volumeIds;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public MetricsStatistics getStatistics() {
        return statistics;
    }

    public void setStatistics(MetricsStatistics statistics) {
        this.statistics = statistics;
    }
}
