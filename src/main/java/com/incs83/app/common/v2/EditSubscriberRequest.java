package com.incs83.app.common.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Email;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Objects;

/**
 * Created by hari on 1/2/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class EditSubscriberRequest implements AuditableData {
    @ApiModelProperty(notes = "Email of the Subscriber")
    @Size(max = 255, message = "Email can be max 254 characters long")
    private String email;

    @ApiModelProperty(notes = "Global Account Number of the Subscriber")
    @Size(max = 255, message = "Global Account Number can be max 254 characters long")
    private String globalAccountNo;

    @ApiModelProperty(notes = "Cams Account Number of the Subscriber")
    @Size(max = 255, message = "Cams Account Number can be max 254 characters long")
    private String camsAccountNo;

    @ApiModelProperty(notes = "Phone Number of the Subscriber")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String phoneNo;

    @ApiModelProperty(notes = "Allocated downLink Bandwidth to Subscriber")
    private Double downLinkRate;

    @ApiModelProperty(notes = "Allocated upLink Bandwidth to Subscriber")
    private Double upLinkRate;

    @ApiModelProperty(notes = "Subscriber's First Name")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String firstName;

    @ApiModelProperty(notes = "Subscriber's Last Name")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String lastName;

    public String getGlobalAccountNo() {
        return globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public String getCamsAccountNo() {
        return camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }

    public String getPhoneNo() {
        return Objects.nonNull(phoneNo) ? phoneNo.trim() : phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public Double getDownLinkRate() {
        return downLinkRate;
    }

    public void setDownLinkRate(Double downLinkRate) {
        this.downLinkRate = downLinkRate;
    }

    public Double getUpLinkRate() {
        return upLinkRate;
    }

    public void setUpLinkRate(Double upLinkRate) {
        this.upLinkRate = upLinkRate;
    }

    public String getEmail() {
        return Objects.nonNull(email)?email.trim():email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
}
