package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Objects;

public class IS<PERSON><PERSON>quest implements AuditableData {
    @ApiModelProperty(notes = "ISP name", required = true)
    @NotNull(message = "Name cannot be empty")
    @Size(min = 1, max = 20, message = "Name must be between 1 and 20 characters")
    @Pattern(regexp="^[a-zA-Z\\d\\-_()\\s]+$", message="name, Invalid characters Found")
    private String name;
    @ApiModelProperty(notes = "ISP Display Name", required = true)
    @NotNull(message = "Display name cannot be empty")
    @Size(min = 1, max = 20, message = "Display Name must be between 1 and 20 characters")
    @Pattern(regexp="^[a-zA-Z\\d\\-_()\\s]+$", message="display Name, Invalid characters Found")
    private String displayName;
    @ApiModelProperty(notes = "ISP Description")
    private String description;

    public String getName() {
        return Objects.isNull(name) ? name : name.trim();
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return Objects.isNull(displayName) ? displayName : displayName.trim();
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return Objects.isNull(description) ? description : description.trim();
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
