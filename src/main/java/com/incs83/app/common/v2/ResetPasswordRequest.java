package com.incs83.app.common.v2;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @Created 11-Jan-2018
 */
public class ResetPasswordRequest {
    @NotNull(message = "Username Cannot be empty")
    @NotBlank(message = "Username Cannot be empty")
    @Size(min = 1, message = "Username Cannot be empty")
    private String username;

    private String baseUrl;

    public String getUsername() {
        return username == null ? null : username.trim();
    }

    public String getBaseUrl() {
        return baseUrl == null ? null : baseUrl.trim();
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public ResetPasswordRequest setUsername(String username) {
        this.username = username;
        return this;
    }
}
