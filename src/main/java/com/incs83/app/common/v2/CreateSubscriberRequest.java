package com.incs83.app.common.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Objects;

/**
 * Created by hari on 1/2/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateSubscriberRequest implements AuditableData {
    @ApiModelProperty(notes = "Email of the Subscriber")
    @Size(max = 255, message = "Email can be max 254 Characters long")
    private String email;

    @NotNull(message = "Serial Number cannot be Null")
    @ApiModelProperty(notes = "Serial Number of Subscriber", required = true)
    @Size(min = 1, max = 255, message = "Serial Number size must be between 1 to 255 character")
    private String serialNumber;

    @NotNull(message = "MAC address cannot be Null")
    @ApiModelProperty(notes = "MAC address of Subscriber", required = true)
    @Size(min = 1, max = 255, message = "MAC address size must be between 1 to 255 character")
    private String rgwMAC;

    @ApiModelProperty(notes = "Subscriber's First Name")
    @Size(max = 255, message = "First Name can be max 254 Characters long")
    private String firstName;

    @ApiModelProperty(notes = "Subscriber's Last Name")
    @Size(max = 255, message = "Last Name can be max 254 Characters long")
    private String lastName;

    @NotNull(message = "PhoneNo cannot be Null")
    @ApiModelProperty(notes = "Phone Number of the Subscriber")
    @Size(max = 255, message = "Phone Number can be max 254 Characters long")
    private String phoneNo;

    @NotNull(message = "ISP cannot be Null")
    @ApiModelProperty(notes = "Subscriber ISP")
    @Size(min = 1, max = 255, message = "ISP size must be between 1 to 255 character")
    private String isp;

    @ApiModelProperty(notes = "Global Account Number of the Subscriber")
    @Size(max = 255, message = "Global Account Number can be max 254 Characters long")
    private String globalAccountNo;

    @ApiModelProperty(notes = "Cams Account Number of the Subscriber")
    @Size(max = 255, message = "Cams Account Number can be max 254 Characters long")
    private String camsAccountNo;

    @ApiModelProperty(notes = "Allocated downLink Bandwidth to Subscriber")
    private Double downLinkRate;

    @ApiModelProperty(notes = "Allocated upLink Bandwidth to Subscriber")
    private Double upLinkRate;

    public String getEmail() {
        return Objects.nonNull(email) ? email.trim() : email;
    }

    public CreateSubscriberRequest setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getSerialNumber() {
        return Objects.nonNull(serialNumber) ? serialNumber.trim() : serialNumber;
    }

    public CreateSubscriberRequest setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getPhoneNo() {
        return Objects.nonNull(phoneNo) ? phoneNo.trim() : phoneNo;
    }

    public CreateSubscriberRequest setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
        return this;
    }

    public String getGlobalAccountNo() {
        return Objects.nonNull(globalAccountNo) ? globalAccountNo.trim() : globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public Double getDownLinkRate() {
        return downLinkRate;
    }

    public void setDownLinkRate(Double downLinkRate) {
        this.downLinkRate = downLinkRate;
    }

    public Double getUpLinkRate() {
        return upLinkRate;
    }

    public void setUpLinkRate(Double upLinkRate) {
        this.upLinkRate = upLinkRate;
    }

    public String getCamsAccountNo() {
        return camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }

    public String getRgwMAC() {
        return rgwMAC;
    }

    public void setRgwMAC(String rgwMAC) {
        this.rgwMAC = rgwMAC;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
}
