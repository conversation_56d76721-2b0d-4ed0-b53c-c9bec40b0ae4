package com.incs83.app.common.v2;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;

import java.io.IOException;
import java.io.Serializable;

public class RadioRequestDTO implements Serializable, DataSerializable {

    private boolean dfsEnable;

    public boolean isDfsEnable() {
        return dfsEnable;
    }

    public void setDfsEnable(boolean dfsEnable) {
        this.dfsEnable = dfsEnable;
    }

    @Override
    public void writeData(ObjectDataOutput out) throws IOException {
        out.writeBoolean(dfsEnable);
    }

    @Override
    public void readData(ObjectDataInput in) throws IOException {
        dfsEnable = in.readBoolean();
    }

    @Override
    public String toString() {
        return "RadioRequestDTO{" +
                "dfsEnable=" + dfsEnable +
                '}';
    }
}
