package com.incs83.app.common.v2;

import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.entities.User;
import com.incs83.auditor.AuditableData;
import com.incs83.enums.Gender;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.*;
import java.util.List;
import java.util.Objects;

public class UserRequestDTO implements AuditableData {
    @ApiModelProperty(notes = "First Name, Character Range [1-255]", required = true)
    @NotBlank(message = "UserRequest.NotBlank.firstName")
    @Size(min = 1, max = 255, message = "First Name size must be between 1 to 255 character")
    @Pattern(regexp = "^[a-zA-Z\\d\\-_()\\s]+$", message = "First Name, Invalid characters Found")
    private String firstName;

    @ApiModelProperty(notes = "Last Name, Character Range [1-255]")
    @Pattern(regexp = "^[a-zA-Z\\d\\-_:\\s]+$|^$", message = "Last Name, Invalid characters Found")
    @Size(max = 255, message = "Last Name size must be between 1 to 255 character")
    private String lastName;

    @ApiModelProperty(notes = "Password, At least one lowercase, At least one uppercase, At least one digit, At least one special character, At least it should have 10-64 characters long.", required = false)
    private String password;

    @ApiModelProperty(notes = "Email Address, Character Range [1-255]", required = true)
    @NotEmpty(message = "Email cannot be empty")
    @Email(message = "UserRequest.Invalid.email")
    @Size(min = 1, message = "Email size must be between 1 to 255 character", max = 255)
    private String email;


    @ApiModelProperty(notes = "Role Id", required = true)
    private List<String> roleIds;

    @ApiModelProperty(notes = "Compartment Id", required = true)
    private List<String> compartmentIds;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public List<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }

    public List<String> getCompartmentIds() {
        return compartmentIds;
    }

    public void setCompartmentIds(List<String> compartmentIds) {
        this.compartmentIds = compartmentIds;
    }

    public static User generateFrom(UserRequestDTO userRequest, User u) {
        if (userRequest.getFirstName() != null) {
            u.setFirstName(userRequest.getFirstName());
        }

        if (userRequest.getLastName() != null) {
            u.setLastName(userRequest.getLastName());
        } else if (Objects.nonNull(userRequest.getLastName()) && ApplicationConstants.EMPTY_STRING.equals(userRequest.getLastName())) {
            u.setLastName(null);
        }

        u.setGender(Gender.valueOf("MALE"));
        if (userRequest.getEmail() != null) {
            u.setEmail(userRequest.getEmail());
        }
        return u;
    }
}
