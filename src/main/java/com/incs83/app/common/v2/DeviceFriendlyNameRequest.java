package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Objects;

public class DeviceFriendlyNameRequest implements AuditableData {
    @NotNull(message = "MAC Address cannot be Null")
    @NotBlank(message = "MAC Address cannot be empty")
    @Size(min = 1, message = "MAC Address cannot be empty")
    @ApiModelProperty(notes = "Device MAC Address", required = true)
    private String macAddress;

    @NotNull(message = "Friendly Name cannot be Null")
    @NotBlank(message = "Friendly Name cannot be empty")
    @Size(min = 1, max = 255, message = "Friendly Name must be between 1 and 255 characters")
    @ApiModelProperty(notes = "Device Friendly Name", required = true)
    @Pattern(regexp="^[a-zA-Z\\d\\-_()\\s]+$", message="friendlyName, Invalid characters Found")
    private String friendlyName;

    public String getMacAddress() {
        return Objects.nonNull(macAddress) ? macAddress.trim() : macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getFriendlyName() {
        return Objects.nonNull(friendlyName) ? friendlyName.trim() : friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }
}
