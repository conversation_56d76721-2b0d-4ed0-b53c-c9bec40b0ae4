package com.incs83.app.common.v2;

import java.util.ArrayList;
import java.util.Arrays;

public class AgentVersion {
    private int majorVersion;
    private int minorVersion;
    private int patchVersion;

    public AgentVersion(String agentVersion) {
        ArrayList<String> agentVersions = new ArrayList<String>(Arrays.asList(agentVersion.split("\\.")));
        if(agentVersions.size() >= 3) {
            this.majorVersion = Integer.valueOf(agentVersions.get(0));
            this.minorVersion = Integer.valueOf(agentVersions.get(1));
            this.patchVersion = Integer.valueOf(agentVersions.get(2));
        } else {
            this.majorVersion = 0;
            this.minorVersion = 0;
            this.patchVersion = 0;
        }
    }

    public int getMajorVersion() {
        return majorVersion;
    }

    public void setMajorVersion(int majorVersion) {
        this.majorVersion = majorVersion;
    }

    public int getMinorVersion() {
        return minorVersion;
    }

    public void setMinorVersion(int minorVersion) {
        this.minorVersion = minorVersion;
    }

    public int getPatchVersion() {
        return patchVersion;
    }

    public void setPatchVersion(int patchVersion) {
        this.patchVersion = patchVersion;
    }
}
