package com.incs83.app.common.v2;

import java.util.List;
import java.util.StringJoiner;

/**
 * Created by hari on 22/11/17.
 */
public class EditProfileRequest {
    private List<Device> devices;
    private Long pauseDuration;

    public List<Device> getDevices() {
        return devices;
    }

    public EditProfileRequest setDevices(List<Device> devices) {
        this.devices = devices;
        return this;
    }

    public Long getPauseDuration() {
        return pauseDuration;
    }

    public EditProfileRequest setPauseDuration(Long pauseDuration) {
        this.pauseDuration = pauseDuration;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", "EditProfileRequest{", "}")
                .add("devices=" + devices)
                .add("pauseDuration=" + pauseDuration)
                .toString();
    }
}
