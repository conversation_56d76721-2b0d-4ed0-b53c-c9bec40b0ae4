package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class WanSpeedTestRequest implements AuditableData {
    @NotBlank(message = "NotBlank.name")
    @Size(min = 1, max = 255, message = "Name cannot be blank, must be between 1 and 255 characters")
    @Pattern(regexp="^[a-zA-Z\\d\\-_()\\s]+$", message="Name, Invalid characters Found")
    private String name;
    private String urls;
    private long scheduledEvent;

    public String getName() {
        return name == null ? null : name.trim();
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrls() {
        return urls == null ? null : urls.trim();
    }

    public void setUrls(String urls) {
        this.urls = urls;
    }

    public long getScheduledEvent() {
        return scheduledEvent;
    }

    public void setScheduledEvent(long scheduledEvent) {
        this.scheduledEvent = scheduledEvent;
    }
}
