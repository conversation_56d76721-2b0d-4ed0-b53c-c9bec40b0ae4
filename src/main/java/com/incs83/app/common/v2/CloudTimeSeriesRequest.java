package com.incs83.app.common.v2;

import com.incs83.app.enums.MetricsStatistics;

import javax.validation.constraints.Pattern;
import java.util.List;

public class CloudTimeSeriesRequest {
    private List<String> instanceId;
    private Long startTime;
    private Integer period;
    @Pattern(regexp = "Average|Sum|Minimum|Maximum", message = "Invalid Statistics selected")
    private MetricsStatistics statistics;

    public List<String> getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(List<String> instanceId) {
        this.instanceId = instanceId;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public MetricsStatistics getStatistics() {
        return statistics;
    }

    public void setStatistics(MetricsStatistics statistics) {
        this.statistics = statistics;
    }
}
