package com.incs83.app.common.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.Objects;
import java.util.StringJoiner;

/**
 * Created by hari on 20/11/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class Device {
    private String macAddress;
    private boolean status;

    public String getMacAddress() {
        return Objects.nonNull(macAddress) ? macAddress.trim() : macAddress;
    }

    public Device setMacAddress(String macAddress) {
        this.macAddress = macAddress;
        return this;
    }

    public boolean isStatus() {
        return status;
    }

    public Device setStatus(boolean status) {
        this.status = status;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", "Device{", "}")
                .add("macAddress='" + macAddress + "'")
                .add("status=" + status)
                .toString();
    }
}
