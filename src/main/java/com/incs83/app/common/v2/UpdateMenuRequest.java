package com.incs83.app.common.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.incs83.auditor.AuditableData;


/**
 * Created by <PERSON><PERSON> on 10-07-2017.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateMenuRequest implements AuditableData {

    private String id;

    private String parentId;

    private String navName;

    private String url;

    private String fontAwesomeIconClass;

    private boolean newTab;

    private int position;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getParentId() { return parentId; }

    public void setParentId(String parentId) { this.parentId = parentId; }

    public String getNavName() {
        return navName;
    }

    public void setNavName(String navName) {
        this.navName = navName;
    }

    public String getUrl() { return url; }

    public void setUrl(String url) { this.url = url; }

    public String getFontAwesomeIconClass() {
        return fontAwesomeIconClass;
    }

    public void setFontAwesomeIconClass(String fontAwesomeIconClass) {
        this.fontAwesomeIconClass = fontAwesomeIconClass;
    }

    public boolean isNewTab() {
        return newTab;
    }

    public void setNewTab(boolean newTab) {
        this.newTab = newTab;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }
}
