package com.incs83.app.common.v2;

import io.swagger.annotations.ApiModelProperty;

public class ScheduleDetails {

    @ApiModelProperty(notes = "Day of the Week.\n" +
            "day - 1/2/3/4/5/6/7 where 1 means Sunday.", required = true)
    private int day;

    @ApiModelProperty(notes = "Internet on time.\n" +
            "internetOnTime - UTC timestamp of the time desired to switch \"on\" internet. This will be auto incremented by a week once the schedule triggers.", required = true)
    private long internetOnTime;

    @ApiModelProperty(notes = "internet Off time.\n" +
            "internetOffTime - UTC timestamp of the time desired to switch \"off\" internet. This will be auto incremented by a week once the schedule triggers.", required = true)
    private long internetOffTime;

    public int getDay() {
        return day;
    }

    public ScheduleDetails setDay(int day) {
        this.day = day;
        return this;
    }

    public long getInternetOnTime() {
        return internetOnTime;
    }

    public ScheduleDetails setInternetOnTime(long internetOnTime) {
        this.internetOnTime = internetOnTime;
        return this;
    }

    public long getInternetOffTime() {
        return internetOffTime;
    }

    public ScheduleDetails setInternetOffTime(long internetOffTime) {
        this.internetOffTime = internetOffTime;
        return this;
    }
}