package com.incs83.app.common.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.incs83.auditor.AuditableData;


import java.util.List;

/**
 * Created by <PERSON><PERSON> on 10-07-2017.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateRoleMenuMapping implements AuditableData {

    private String roleId;

    private List<String> menuId;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public List<String> getMenuId() {
        return menuId;
    }

    public void setMenuId(List<String> menuId) {
        this.menuId = menuId;
    }
}
