package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;

public class RGWPaginationRequest implements AuditableData {
    private Integer max;
    private Integer offset;
    private Long fromDate;
    private Long toDate;

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Long getFromDate() {
        return fromDate;
    }

    public void setFromDate(Long fromDate) {
        this.fromDate = fromDate;
    }

    public Long getToDate() {
        return toDate;
    }

    public void setToDate(Long toDate) {
        this.toDate = toDate;
    }
}


