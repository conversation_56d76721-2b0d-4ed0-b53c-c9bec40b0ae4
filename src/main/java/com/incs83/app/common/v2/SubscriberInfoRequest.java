package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import com.incs83.enums.sql.Order;

public class SubscriberInfoRequest implements AuditableData {
    private String fileId;
    private Integer offset = Integer.valueOf(0);
    private Integer max = Integer.valueOf(10);
    private Order order = Order.ASC;;
    private String sortBy = "serialNumber";
    private String serialNumber;
    private String macAddress;

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }
}
