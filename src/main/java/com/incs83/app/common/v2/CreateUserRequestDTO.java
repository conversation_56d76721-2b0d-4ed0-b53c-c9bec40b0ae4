package com.incs83.app.common.v2;

import com.incs83.app.entities.User;
import com.incs83.auditor.AuditableData;
import com.incs83.enums.Gender;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.*;
import java.util.List;

public class CreateUserRequestDTO implements AuditableData {
    @ApiModelProperty(notes = "First Name, Character Range [1-255]", required = true)
    @NotBlank(message = "UserRequest.NotBlank.firstName")
    @Size(min = 1, message = "UserRequest.Min.firstName", max = 255)
    @Pattern(regexp = "^[a-zA-Z\\d\\-_()\\s]+$", message = "First Name, Invalid characters Found")
    private String firstName;

    @NotBlank(message = "UserRequest.NotBlank.lastName")
    @Size(min = 1, message = "UserRequest.Min.lastName", max = 255)
    @ApiModelProperty(notes = "Last Name, Character Range [1-255]", required = true)
    @Pattern(regexp = "^[a-zA-Z\\d\\-_:\\s]+$", message = "Last Name, Invalid characters Found")
    private String lastName;

    /*@ApiModelProperty(notes = "Gender, Character Range [1-255]")
    @Pattern(regexp = "MALE|FEMALE|Male|Female|male|female", message = "Invalid Gender attribute")
    private String gender;*/

    @ApiModelProperty(notes = "Email Address, Character Range [1-255]", required = true)
    @NotEmpty(message = "Email cannot be empty")
    @Email(message = "UserRequest.Invalid.email")
    @Size(min = 1, message = "Email size must be between 1 to 255 character", max = 255)
    private String email;

    /*@ApiModelProperty(notes = "Portal url", required = true)
    private String portalUrl;*/

    /*@NotBlank(message = "UserRequest.NotBlank.company")
    @ApiModelProperty(notes = "Company, Character Range [1-255]", required = true)
    @Size(min = 1, message = "Company size must be between 1 to 255 character", max = 255)
    @Pattern(regexp = "^[a-zA-Z\\d\\-_()\\s]+$", message = "Company, Invalid characters Found")
    private String company;

    @ApiModelProperty(notes = "Title, Character Range [1-255]")
    @Size(min = 1, message = "Title size must be between 1 to 255 character", max = 255)
    @Pattern(regexp = "^[a-zA-Z\\d\\-_()\\s]+$", message = "Title, Invalid characters Found")
    private String title;*/

    @ApiModelProperty(notes = "Role Id", required = true)
    private List<String> roleIds;

    @ApiModelProperty(notes = "Compartment Id", required = true)
    private List<String> compartmentIds;

    private Boolean verify;


    public Boolean getVerify() {
        return verify;
    }

    public void setVerify(Boolean verify) {
        this.verify = verify;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    /*public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }
*/
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    /*public String getPortalUrl() {
        return portalUrl;
    }

    public void setPortalUrl(String portalUrl) {
        this.portalUrl = portalUrl;
    }*/

    /*public String getCompany() {
            return company;
        }

        public void setCompany(String company) {
            this.company = company;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    */

    public List<String> getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(List<String> roleIds) {
        this.roleIds = roleIds;
    }

    public List<String> getCompartmentIds() {
        return compartmentIds;
    }

    public void setCompartmentIds(List<String> compartmentIds) {
        this.compartmentIds = compartmentIds;
    }

    public static User generateFrom(CreateUserRequestDTO userRequest, User u) {

        /*if (userRequest.getCompany() != null) {
            u.setCompany(userRequest.getCompany());
        }

        if (userRequest.getTitle() != null) {
            u.setTitle(userRequest.getTitle());
        }
*/
        if (userRequest.getFirstName() != null) {
            u.setFirstName(userRequest.getFirstName());
        }

        if (userRequest.getLastName() != null) {
            u.setLastName(userRequest.getLastName());
        }

        u.setGender(Gender.valueOf("MALE"));
        if (userRequest.getEmail() != null) {
            u.setEmail(userRequest.getEmail());
        }
        return u;
    }
}
