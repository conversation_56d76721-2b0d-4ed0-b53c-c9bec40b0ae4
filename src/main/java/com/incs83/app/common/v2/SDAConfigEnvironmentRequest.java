package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import org.bson.Document;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * Created By  KOUSHINDRA on Wednesday, 08-May-2019
 */
public class SDAConfigEnvironmentRequest implements AuditableData {
    @NotBlank(message = "service can not blank")
    private String service;

    @NotEmpty(message = "data can not be empty")
    private List<Document> data;

    public String getService() {
        return service;
    }

    public SDAConfigEnvironmentRequest setService(String service) {
        this.service = service;
        return this;
    }

    public List<Document> getData() {
        return data;
    }

    public SDAConfigEnvironmentRequest setData(List<Document> data) {
        this.data = data;
        return this;
    }
}
