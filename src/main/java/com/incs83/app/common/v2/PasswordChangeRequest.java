package com.incs83.app.common.v2;

import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class PasswordChangeRequest {
    @NotNull(message = "NewPassword cannot be empty")
    @NotBlank(message = "NewPassword cannot be empty")
    @Pattern(regexp = "((?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%?=*&]).{10,64})", message = "Password, Invalid characters Found. Rule, At least one lowercase, At least one uppercase, At least one digit, At least one special character, At least it should have 10-64 characters long.")
    @Size(min = 1, max = 255, message = "size must be between 1 to 255 character")
    private String newPassword;

    private String tokenId;
    private String token;

    public String getToken() {
        return token == null ? null : token.trim();
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getNewPassword() {
        return newPassword.trim();
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getTokenId() {
        return tokenId == null ? null : tokenId.trim();
    }

    public void setTokenId(String tokenId) {
        this.tokenId = tokenId;
    }
}
