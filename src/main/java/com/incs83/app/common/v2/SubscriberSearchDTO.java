package com.incs83.app.common.v2;/* sakshi created on 6/11/19 inside the package - com.incs83.app.common.v2 */

import com.incs83.auditor.AuditableData;
import com.incs83.enums.sql.Order;

public class SubscriberSearchDTO implements AuditableData {
    private String name;
    private String phoneNo;
    private String camsAccountNo;
    private String globalAccountNo;
    private String email;
    private String groupName;
    private String groupId;
    private Integer offset = Integer.valueOf(0);
    private Integer max = Integer.valueOf(10);
    private Order order = Order.ASC;;
    private String sortBy="name";

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getCamsAccountNo() {
        return camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }

    public String getGlobalAccountNo() {
        return globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }
}
