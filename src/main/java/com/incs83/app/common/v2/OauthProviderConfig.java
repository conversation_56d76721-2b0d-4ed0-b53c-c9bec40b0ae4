package com.incs83.app.common.v2;


import com.incs83.auditor.AuditableData;
import com.incs83.app.entities.OauthConfig;
import com.incs83.util.CommonUtils;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

public class OauthProviderConfig implements AuditableData {

//    @NotNull
//    @Size(min = 1, message = "Name cannot be empty")
    private String name;

    @NotNull
    @Size(min = 1, message = "Client Id cannot be empty")
    private String clientId;

    @NotNull
    @Size(min = 1, message = "Client Secret cannot be empty")
    private String clientSecret;

    @NotNull
    @Size(min = 1, message = "Redirect URI cannot be empty")
    private String redirectUri;

    @NotNull
    @Size(min = 1, message = "OAuth Provider cannot be empty")
    private String oauthProvider;

    @NotNull
    @Size(min = 1, message = "Well known Configuration")
    private String wellKnownConfig;

    @NotNull
    @Size(min = 1, message = "ISP cannot be empty")
    private String isp;

    @NotNull
    @Size(min = 1, message = "Role Cannot be empty")
    private String defaultRoleId;

    private String respType;

    private String scope;

    private boolean defaultProvider;

    public OauthConfig toOauthConfig() {
        OauthConfig oauthConfig = new OauthConfig();
        oauthConfig.setName(getName());
        oauthConfig.setId(CommonUtils.generateUUID());
        oauthConfig.setClientId(getClientId());
        oauthConfig.setClientSecret(getClientSecret());
        oauthConfig.setOauthProvider(getOauthProvider());
        oauthConfig.setRedirectUri(getRedirectUri());
        oauthConfig.setIsp(getIsp());
        oauthConfig.setRoleId(getDefaultRoleId());
        oauthConfig.setWellKnownConfig(getWellKnownConfig());
        oauthConfig.setRespType(getRespType());
        oauthConfig.setScope(getScope());
        oauthConfig.setDefaultProvider(isDefaultProvider());
        return oauthConfig;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }

    public String getOauthProvider() {
        return oauthProvider;
    }

    public void setOauthProvider(String oauthProvider) {
        this.oauthProvider = oauthProvider;
    }

    public String getWellKnownConfig() {
        return wellKnownConfig;
    }

    public void setWellKnownConfig(String wellKnownConfig) {
        this.wellKnownConfig = wellKnownConfig;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getDefaultRoleId() {
        return defaultRoleId;
    }

    public void setDefaultRoleId(String defaultRoleId) {
        this.defaultRoleId = defaultRoleId;
    }

    public String getRespType() {
        return respType;
    }

    public void setRespType(String respType) {
        this.respType = respType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public boolean isDefaultProvider() {
        return defaultProvider;
    }

    public void setDefaultProvider(boolean defaultProvider) {
        this.defaultProvider = defaultProvider;
    }
}
