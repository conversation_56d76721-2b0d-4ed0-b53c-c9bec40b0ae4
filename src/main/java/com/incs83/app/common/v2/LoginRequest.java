package com.incs83.app.common.v2;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * Created by hari on 1/2/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginRequest implements AuditableData {

    @NotNull(message = "username Cannot be null")
//    @Size(min = 5, max = 255, message = "Username must be between 5 and 255 characters")
    @ApiModelProperty(notes = "Username for login", required = true)
    private String username;

    @NotNull(message = "password Cannot be null")
    @ApiModelProperty(notes = "Password for the authentication", required = true)
    private String password;

    public LoginRequest() {
    }

    public String getUsername() {
        return username == null ? null : username.trim();
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password == null ? null : password.trim();
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
