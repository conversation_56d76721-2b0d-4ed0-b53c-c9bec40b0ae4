package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Objects;

/**
 * Created by Jayant on 25/04/18.
 */
public class CreateUserProfileRequest implements AuditableData {

    @Size(min = 1, max = 255, message = "Profile name cannot be blank, must be between 1 and 255 characters")
    @NotBlank(message = "Profile Name cannot be blank")
    @NotNull(message = "Profile Name cannot be blank")
    @ApiModelProperty(notes = "Name of the Profile", required = true)
    @Pattern(regexp="^[a-zA-Z\\d\\-_()\\s]+$", message="profileName, Invalid characters Found")
    private String profileName;

    public String getProfileName() {
        return Objects.nonNull(profileName) ? profileName.trim() : profileName;
    }

    public CreateUserProfileRequest setProfileName(String profileName) {
        this.profileName = profileName;
        return this;
    }

}