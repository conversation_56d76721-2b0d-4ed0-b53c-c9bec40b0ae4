package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;

import java.util.List;

public class SDAActions implements AuditableData {
    private List<String> ipAddress;
    private String operation;
    private String service;
    private String action;

    public void setIp(List<String> ipAddress) {
        this.ipAddress = ipAddress;
    }

    public List<String> getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(List<String> ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }
}
