package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;

public class ElasticSearchRequest implements AuditableData {
    private String name;
    private String phoneNo;
    private String email;
    private String globalAccountNo;
    private String camsAccountNo;
    private String apId;
    private String rgwMAC;
    private String extMAC;
    private String extSerial;


    public String getApId() {
        return this.apId;
    }

    public ElasticSearchRequest setApId(String apId) {
        this.apId = apId;
        return this;
    }

    public String getRgwMAC() {
        return this.rgwMAC;
    }

    public ElasticSearchRequest setRgwMAC(String rgwMAC) {
        this.rgwMAC = rgwMAC;
        return this;
    }

    public String getExtMAC() {
        return this.extMAC;
    }

    public ElasticSearchRequest setExtMAC(String extMAC) {
        this.extMAC = extMAC;
        return this;
    }

    public String getExtSerial() {
        return this.extSerial;
    }

    public ElasticSearchRequest setExtSerial(String extSerial) {
        this.extSerial = extSerial;
        return this;
    }

    public String getName() {
        return this.name;
    }

    public ElasticSearchRequest setName(String name) {
        this.name = name;
        return this;
    }

    public String getPhoneNo() {
        return this.phoneNo;
    }

    public ElasticSearchRequest setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
        return this;
    }

    public String getEmail() {
        return this.email;
    }

    public ElasticSearchRequest setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getGlobalAccountNo() {
        return this.globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public String getCamsAccountNo() {
        return this.camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }

    public String toString() {
        return "EquipmentSearchDTO{name='" + this.name + '\'' + ", phoneNo='" + this.phoneNo + '\'' + ", email='" + this.email + '\'' + ", globalAccountNo='" + this.globalAccountNo + '\'' + ", camsAccountNo='" + this.camsAccountNo + '\'' + ", apId='" + this.apId + '\'' + ", rgwMAC='" + this.rgwMAC + '\'' + ", extMAC='" + this.extMAC + '\'' + ", extSerial='" + this.extSerial + '\'' + '}';
    }
}
