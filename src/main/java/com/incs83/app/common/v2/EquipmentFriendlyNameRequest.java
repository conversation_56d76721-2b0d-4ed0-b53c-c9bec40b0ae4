package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

public class EquipmentFriendlyNameRequest implements AuditableData {
    @NotNull(message = "SerialNumber cannot be Null")
    @NotBlank(message = "Serial Number cannot be empty")
    @Size(min = 1, message = "Serial Number cannot be empty")
    @ApiModelProperty(notes = "Equipment Serial Number", required = true)
    private String serialNumber;

    @NotNull(message = "Friendly Name cannot be Null")
    @NotBlank(message = "Friendly Name cannot be empty")
    @Size(min = 1, max = 255, message = "Friendly Name must be between 1 and 255 characters")
    @ApiModelProperty(notes = "Equipment Friendly Name", required = true)
    @Pattern(regexp="^[a-zA-Z\\d\\-_()\\s]+$", message="friendlyName, Invalid characters Found")
    private String friendlyName;

    public String getSerialNumber() {
        return serialNumber == null ? null : serialNumber.trim();
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getFriendlyName() {
        return friendlyName == null ? null : friendlyName.trim();
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }
}
