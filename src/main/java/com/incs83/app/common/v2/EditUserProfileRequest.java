package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON> on 25/04/18.
 */
public class EditUserProfileRequest implements AuditableData {

    @ApiModelProperty(notes = "Name of the Profile")
    @Pattern(regexp="^[a-zA-Z\\d\\-_()\\s]+$", message="profileName, Invalid characters Found")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String profileName;

    @ApiModelProperty(notes = "List of Device MAC addresses")
    private List<String> devices;

    @ApiModelProperty(notes = "Auto schedule enable/disable flag")
    private Boolean autoSchedule;

    @ApiModelProperty(notes = "Schedule Details object if supplied then attributes are mandatory")
    private List<ScheduleDetails> scheduleDetails;

    @ApiModelProperty(notes = "Content Filters, Allowed values [UNSAFE=0, ALCOHOL_AND_TOBACCO=1, ADULT=2,\n" +
            "ANONYMIZERS=3, BANKING=4,\n" +
            "BLOGS=5,\n" +
            "CHAT=6,\n" +
            "DATING=7,\n" +
            "DRUGS=19, ENTERTAINMENT=20, GAMBLING=21,\n" +
            "GAMES=22,\n" +
            "HATE=23,\n" +
            "PAYMENT=24, SHOPPING=25, SOCIAL_NETWORKING=26, SOFTWARE_DOWNLOAD=27, SPAM=28,\n" +
            "STREAMING=29, WAREZ=30, WEAPONS=31]")
    private List<Integer> contentFilters;

    public Boolean isAutoSchedule() {
        return autoSchedule;
    }

    public EditUserProfileRequest setAutoSchedule(boolean autoSchedule) {
        this.autoSchedule = autoSchedule;
        return this;
    }

    public List<ScheduleDetails> getScheduleDetails() {
        return scheduleDetails;
    }

    public EditUserProfileRequest setScheduleDetails(List<ScheduleDetails> scheduleDetails) {
        this.scheduleDetails = scheduleDetails;
        return this;
    }

    public String getProfileName() {
        return Objects.nonNull(profileName) ? profileName.trim() : profileName;
    }

    public EditUserProfileRequest setProfileName(String profileName) {
        this.profileName = profileName;
        return this;
    }

    public List<String> getDevices() {
        return devices;
    }

    public EditUserProfileRequest setDevices(List<String> devices) {
        this.devices = devices;
        return this;
    }

    public List<Integer> getContentFilters() {
        return contentFilters;
    }

    public void setContentFilters(List<Integer> contentFilters) {
        this.contentFilters = contentFilters;
    }
}