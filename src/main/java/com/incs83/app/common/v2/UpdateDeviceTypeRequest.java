package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.StringJoiner;

public class UpdateDeviceTypeRequest implements AuditableData {
    @NotBlank(message = "MAC Address cannot be blank.")
    @NotNull(message = "MAC Address cannot be blank.")
    @ApiModelProperty(notes = "MAC Address for the Device", required = true)
    private String macAddress;

    @ApiModelProperty(notes = "Device Type", required = true)
    @Pattern(regexp = "Device|Phone|Tablet|Computer|Camera|TV|Printer|Other", message = "validValues.DeviceType")
    @NotBlank(message = "Device Type cannot be blank.")
    @NotNull(message = "Device Type cannot be blank.")
    private String type;

    public String getMacAddress() {
        return macAddress == null ? null : macAddress.trim();
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getType() {
        return type == null ? null : type.trim();
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", "UpdateDeviceTypeRequest{", "}")
                .add("macAddress='" + macAddress + "'")
                .add("type=" + type)
                .toString();
    }
}
