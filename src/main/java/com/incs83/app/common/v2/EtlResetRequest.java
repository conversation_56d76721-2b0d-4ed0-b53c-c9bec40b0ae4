package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;

/**
 * Created By  Sahil on Wednesday, 01-May-2019
 */
public class EtlResetRequest implements AuditableData {

    private String type;
    private String action;
    private String etlName;
    private Integer offset;
    private Integer interval;
    private Integer threshold;

    public Integer getThreshold() {
        return threshold;
    }

    public EtlResetRequest setThreshold(Integer threshold) {
        this.threshold = threshold;
        return this;
    }

    public String getType() {
        return type;
    }

    public EtlResetRequest setType(String type) {
        this.type = type;
        return this;
    }

    public String getAction() {
        return action;
    }

    public EtlResetRequest setAction(String action) {
        this.action = action;
        return this;
    }

    public String getEtlName() {
        return etlName;
    }

    public EtlResetRequest setEtlName(String etlName) {
        this.etlName = etlName;
        return this;
    }

    public Integer getOffset() {
        return offset;
    }

    public EtlResetRequest setOffset(Integer offset) {
        this.offset = offset;
        return this;
    }

    public Integer getInterval() {
        return interval;
    }

    public EtlResetRequest setInterval(Integer interval) {
        this.interval = interval;
        return this;
    }
}
