package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;

public class ClusterRequest implements AuditableData {

    @ApiModelProperty(notes = "Cluster name", required = true)
    @NotBlank(message = "cluster name cannot be blank")
    @Size(min = 1, max = 255, message = "Cluster Name size must be between 1 to 255 character")
    @Pattern(regexp = "^[a-zA-Z\\d\\-_()\\s]+$", message = "Cluster Name, Invalid characters Found")
    private String clusterName;

    @ApiModelProperty(notes = "Cluster description")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String description;

    @ApiModelProperty(notes = "Add Subscribers to the cluster", required = true)
    private List<String> addedAps;

    public String getClusterName() {
        return Objects.nonNull(clusterName) ? clusterName.trim() : clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getDescription() {
        return Objects.nonNull(description) ? description.trim() : description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getAddedAps() {
        return addedAps;
    }

    public void setAddedAps(List<String> addedAps) {
        this.addedAps = addedAps;
    }
}
