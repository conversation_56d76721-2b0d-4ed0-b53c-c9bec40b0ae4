package com.incs83.app.common.v2;


import com.incs83.auditor.AuditableData;

import javax.validation.constraints.Size;
import java.util.HashMap;
import java.util.List;

public class OauthRoleMappingConfig implements AuditableData {
    private String oauthRole;
    private List<HashMap<String, Object>> optimRole;

    public String getOauthRole() {
        return oauthRole;
    }

    public void setOauthRole(String oauthRole) {
        this.oauthRole = oauthRole;
    }

    public List<HashMap<String, Object>> getOptimRole() {
        return optimRole;
    }

    public void setOptimRole(List<HashMap<String, Object>> optimRole) {
        this.optimRole = optimRole;
    }
}
