package com.incs83.app.common.v2;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.Objects;

public class ChangeNetworkState implements AuditableData {

    @ApiModelProperty(notes = "Enable/Disable the Network. Takes in only boolean true/false (Not String)", required = true)
    @NotNull(message = "Enabled cannot be null")
    private Boolean enabled;

    @ApiModelProperty(notes = "Type can be any one of the combinations of 1,2,5,6 or individual 1,2,5 & 6. They represent the 2.4G & 5G Guest and Primary Networks. 1 - 2.4G Primary, 2 - 2.4G Guest, 5 - 5G Primary & 6 - 5G Guest.\nExample: If the intended is primary and guest 2.4G, supply  1,2", required = true)
    @NotNull(message = "Type cannot be null")
    @Size(min = 1, max = 255, message = "Type size must be between 1 to 255 character")
    @Pattern(regexp = "1|2|5|6|1,2|1,5|1,6|2,5|2,6|5,6|1,2,5|1,2,6|1,2,5,6", message = "validValues.NetworkType")
    private String type;

    public boolean isEnabled() {
        return enabled;
    }

    public ChangeNetworkState setEnabled(boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public String getType() {
        return Objects.nonNull(type) ? type.trim() : type;
    }

    public ChangeNetworkState setType(String type) {
        this.type = type;
        return this;
    }
}
