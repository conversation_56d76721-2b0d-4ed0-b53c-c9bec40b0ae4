package com.incs83.app.dto;

import com.incs83.security.tokenFactory.Entitlement;

import java.util.List;
import java.util.Set;

public class RoleDetailDTO {
    private String id;
    private String name;
    private List<Entitlement> roleEntitlement;
    private String type;
    private Set<String> entity;
    private List<RolePermissionDTO> rolePermissions;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Entitlement> getRoleEntitlement() {
        return roleEntitlement;
    }

    public void setRoleEntitlement(List<Entitlement> roleEntitlement) {
        this.roleEntitlement = roleEntitlement;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Set<String> getEntity() {
        return entity;
    }

    public void setEntity(Set<String> entity) {
        this.entity = entity;
    }

    public List<RolePermissionDTO> getRolePermissions() {
        return rolePermissions;
    }

    public void setRolePermissions(List<RolePermissionDTO> rolePermissions) {
        this.rolePermissions = rolePermissions;
    }
}
