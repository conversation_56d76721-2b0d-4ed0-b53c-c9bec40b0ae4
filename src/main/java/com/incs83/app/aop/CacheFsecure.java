package com.incs83.app.aop;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.annotation.Cachable;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v3.ManageFSecureService;
import com.incs83.app.entities.Equipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.FsecureCacheHelper;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.FSECURE_CACHE;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.FsecureConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.IGNORE_FINAL;

@Aspect
@Component
@ConditionalOnProperty(name = "fSecureCacheEnabled", matchIfMissing = false)
@Order(1)
public class CacheFsecure {

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageFSecureService manageFSecureService;

    @Autowired
    private FsecureCacheHelper fsecureCacheHelper;


    private static final Logger LOG = LogManager.getLogger("org");

    @Around("@annotation(cachable)" + IGNORE_FINAL)
    public Object cachable(ProceedingJoinPoint joinPoint, Cachable cachable) throws Throwable {
        Object result;
        BasicDBObject dataToUpdate = new BasicDBObject();
        BasicDBObject update = new BasicDBObject();
        String operation = cachable.operation();
        String method = String.valueOf(cachable.method());
        int size = joinPoint.getArgs().length;
        String rgwSerial = joinPoint.getArgs()[0].toString();
        HttpServletRequest httpServletRequest = (HttpServletRequest) joinPoint.getArgs()[size - 1];

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(rgwSerial);

        if (Objects.isNull(equipment)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment not found");
        }

        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        if (Objects.isNull(equipment.getSubscriber()) || Objects.isNull(equipment.getSubscriber().getId())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This equipment is currently not attached to any Subscriber.");
        }

        String serviceTelephoneNo = equipment.getServiceTelephoneNo();
        BasicDBObject query = new BasicDBObject();
        query.put("serviceTelephoneNo", serviceTelephoneNo);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("id", 0);
        DBObject dbObject = mongoService.findOne(query, appendableParams, FSECURE_CACHE, TIMESTAMP, DESC, fieldsToRemove);
        if(dbObject == null) {
            HashMap<String, Object> dataToCreate = createFsecureData(serviceTelephoneNo);
            mongoService.create(FSECURE_CACHE, dataToCreate);
            dbObject = mongoService.findOne(query, appendableParams, FSECURE_CACHE, TIMESTAMP, DESC, fieldsToRemove);
        }

        if (Objects.nonNull(dbObject)) {
            Boolean isCacheSyncFromDB = (Boolean) dbObject.get(KEY_IS_CACHE_SYNC);
            if (Objects.isNull(isCacheSyncFromDB)) {
                isCacheSyncFromDB = false;
            }

            if (isCacheSyncFromDB && !(operation.equalsIgnoreCase(REGISTRATION) && method.equalsIgnoreCase("GET"))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This equipment is currently in sync process. Please try after some time");
            }
        }

        ApiResponseDTO responseDTO;
        try {
            result = joinPoint.proceed();
            responseDTO = (ApiResponseDTO) responseUtil.ok(result);    // throws error
        } catch (Exception e) {
            LOG.info("Error in proceeding the join proceed :  ");
            throw e;
        }


        try {

            if (responseDTO.getCode() == 0) {


                if (method.equalsIgnoreCase("POST") && operation.equalsIgnoreCase(DEVICE_TRIGGER_ACTION)) {
                    processPostDeviceTriggerAction(joinPoint, operation, size, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }


                if (method.equalsIgnoreCase("POST") && operation.equalsIgnoreCase(PROFILES)) {
                    processPostProfile(joinPoint, operation, size, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }


                if (method.equalsIgnoreCase("PUT") && operation.equalsIgnoreCase(DEVICE_BY_ID)) {
                    processPutDeviceById(joinPoint, operation, size, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }


                if (method.equalsIgnoreCase("PUT") && operation.equalsIgnoreCase(PROFILE_BY_ID)) {
                    processPutProfileById(joinPoint, operation, size, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }


                if (method.equalsIgnoreCase("PUT") && operation.equalsIgnoreCase(PROTECTION)) {
                    processPutProtection(joinPoint, operation, size, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }


                if (method.equalsIgnoreCase("PUT") && operation.equalsIgnoreCase(WEBSITE_EXCEPTION)) {
                    processPutWebException(joinPoint, operation, size, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }


                if (method.equalsIgnoreCase("DELETE") && operation.equalsIgnoreCase(EVENTS)) {
                    dataToUpdate.put(KEY_EVENTS, new ArrayList<>());
                    update.put("$set", dataToUpdate);
                    mongoService.update(query, update, false, false, FSECURE_CACHE);
                }


                if (method.equalsIgnoreCase("DELETE") && operation.equalsIgnoreCase(PROFILE_BY_ID)) {
                    processDeleteProfileById(joinPoint, query, dbObject, dataToUpdate, update);
                }


                if (method.equalsIgnoreCase("DELETE") && operation.equalsIgnoreCase(WEBSITE_EXCEPTION)) {
                    dataToUpdate.put(KEY_WEBSITE_EXCEPTIONS, new ArrayList<>());
                    update.put("$set", dataToUpdate);
                    mongoService.update(query, update, false, false, FSECURE_CACHE);
                }


                synchronized (this) {

                    if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(DEVICES)) {
                        processGetDevices(responseDTO, operation, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                    }


                    if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(PROTECTION)) {
                        processGetProtection(responseDTO, operation, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                    }
                }

                if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(DEVICE_BY_ID)) {
                    processGetDeviceById(joinPoint, responseDTO, operation, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }

                if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(PROFILES)) {
                    processGetProfiles(responseDTO, operation, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }

                if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(PROFILE_BY_ID)) {
                    processGetProfileById(joinPoint, responseDTO, operation, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }

                if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(EVENTS)) {
                    if ((Objects.nonNull(dbObject))) {
                        dataToUpdate.put(KEY_EVENTS, new ArrayList<>());
                        update.put("$set", dataToUpdate);
                        mongoService.update(query, update, false, false, FSECURE_CACHE);
                    }
                }

                if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(WEBSITE_EXCEPTION)) {
                    processGetWebException(responseDTO, operation, serviceTelephoneNo, query, dbObject, dataToUpdate, update);
                }

                if (method.equalsIgnoreCase("GET") && operation.equalsIgnoreCase(REGISTRATION)) {
                    HashMap<String, Object> registration = (HashMap<String, Object>) responseDTO.getData();
                    if (Objects.nonNull(dbObject)) {
                        dataToUpdate.put(KEY_REGISTRATION, registration);
                        update.put("$set", dataToUpdate);
                        mongoService.update(query, update, false, false, FSECURE_CACHE);
                    }
                }

            }
        } catch (Exception e) {
            LOG.info("Exception => ", e);
        }
        return result;

    }


    public void processGetWebException(ApiResponseDTO responseDTO, String operation, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        List<HashMap<String, Object>> websiteExceptions = (ArrayList<HashMap<String, Object>>) responseDTO.getData();
        if (Objects.nonNull(dbObject)) {
            dataToUpdate.put(KEY_WEBSITE_EXCEPTIONS, websiteExceptions);
            update.put("$set", dataToUpdate);
            mongoService.update(query, update, false, false, FSECURE_CACHE);
        }
    }

    public void processGetProfileById(ProceedingJoinPoint joinPoint, ApiResponseDTO responseDTO, String operation, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        String profileID = joinPoint.getArgs()[1].toString();
        HashMap<String, Object> rpcProfile = ((List<HashMap<String, Object>>) responseDTO.getData()).get(0);
        if (Objects.nonNull(dbObject)) {
            List<HashMap<String, Object>> profiles = (List<HashMap<String, Object>>) dbObject.get(KEY_PROFILES);
            if (Objects.nonNull(profiles) && !profiles.isEmpty()) {
                HashMap<String, Object> profile = profiles.stream().filter(e -> String.valueOf(e.get("id")).equalsIgnoreCase(profileID)).findAny().orElse(null);
                if (Objects.isNull(profile)) {
                    profiles.add(rpcProfile);
                    dataToUpdate.put(KEY_PROFILES, profiles);
                } else {
                    profiles.remove(profile);
                    profiles.add(rpcProfile);
                    dataToUpdate.put(KEY_PROFILES, profiles);
                }
            } else {
                ArrayList<HashMap<String, Object>> profilesList = new ArrayList<HashMap<String, Object>>();
                profilesList.add(rpcProfile);
                dataToUpdate.put(KEY_PROFILES, profilesList);
            }
            update.put("$set", dataToUpdate);
            mongoService.update(query, update, false, false, FSECURE_CACHE);
        }
    }

    public void processGetProfiles(ApiResponseDTO responseDTO, String operation, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        List<HashMap<String, Object>> rpcProfiles = (List<HashMap<String, Object>>) responseDTO.getData();
        if (Objects.nonNull(dbObject)) {
            if (rpcProfiles.size() != 0) {
                dataToUpdate.put(KEY_PROFILES, rpcProfiles);
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }

    public void processGetDeviceById(ProceedingJoinPoint joinPoint, ApiResponseDTO responseDTO, String operation, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        String deviceID = joinPoint.getArgs()[1].toString();
        HashMap<String, Object> rpcDevice = ((List<HashMap<String, Object>>) responseDTO.getData()).get(0);
        if (Objects.nonNull(dbObject)) {
            List<HashMap<String, Object>> devices = (List<HashMap<String, Object>>) dbObject.get(KEY_DEVICES);

            if (Objects.nonNull(devices) && !devices.isEmpty()) {
                HashMap<String, Object> device = devices.stream().filter(d -> String.valueOf(d.get("macAddress")).equalsIgnoreCase(deviceID)).findAny().orElse(null);
                if (Objects.isNull(device)) {
                    devices.add(rpcDevice);
                    dataToUpdate.put(KEY_DEVICES, devices);
                } else {
                    devices.remove(device);
                    devices.add(rpcDevice);
                    dataToUpdate.put(KEY_DEVICES, devices);
                }
            } else {
                List<HashMap<String, Object>> devicesList = new ArrayList<HashMap<String, Object>>();
                devicesList.add(rpcDevice);
                dataToUpdate.put(KEY_DEVICES, devicesList);
            }
            update.put("$set", dataToUpdate);
            mongoService.update(query, update, false, false, FSECURE_CACHE);

        }
    }

    public void processGetProtection(ApiResponseDTO responseDTO, String operation, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        HashMap<String, Object> protection = (HashMap<String, Object>) responseDTO.getData();
        if (Objects.nonNull(dbObject)) {
            if (!protection.isEmpty()) {
                boolean browsing = Boolean.valueOf(protection.get("browsing").toString());
                boolean iot = Boolean.valueOf(protection.get("iot").toString());
                boolean tracking = Boolean.valueOf(protection.get("tracking").toString());

                if (browsing && !iot && !tracking) {
                    dataToUpdate.put(KEY_PROTECTION, protection);
                    update.put("$set", dataToUpdate);
                    mongoService.update(query, update, false, false, FSECURE_CACHE);
                }
            }
        }
    }

    public void processGetDevices(ApiResponseDTO responseDTO, String operation, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        List<HashMap<String, Object>> rpcDevices = (List<HashMap<String, Object>>) responseDTO.getData();
        if (Objects.nonNull(dbObject)) {
            if (rpcDevices.size() != 0) {
                dataToUpdate.put(KEY_DEVICES, rpcDevices);
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }

    public void processDeleteProfileById(ProceedingJoinPoint joinPoint, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        String profileId = joinPoint.getArgs()[1].toString();

        List<HashMap<String, Object>> profiles = (List<HashMap<String, Object>>) dbObject.get(KEY_PROFILES);
        if (Objects.isNull(profiles) || profiles.isEmpty()) {
        } else {
            HashMap<String, Object> profile = profiles.stream().filter(e -> String.valueOf(e.get("id")).equalsIgnoreCase(profileId)).findAny().orElse(null);
            if (Objects.isNull(profile)) {
            } else {
                profiles.remove(profile);
                dataToUpdate.put(KEY_PROFILES, profiles);
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }


    public void processPutWebException(ProceedingJoinPoint joinPoint, String operation, int size, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        List<HashMap<String, Object>> websiteExceptionsToUpdate = new ArrayList<>();
        Object value = joinPoint.getArgs()[1];

        List<HashMap<String, Object>> payloads = (ArrayList<HashMap<String, Object>>) objectMapper.convertValue(value, ArrayList.class);
        if (Objects.nonNull(payloads) && !payloads.isEmpty()) {
            if (Objects.nonNull(dbObject)) {
                List<HashMap<String, Object>> websiteExceptions = (List<HashMap<String, Object>>) dbObject.get(KEY_WEBSITE_EXCEPTIONS);
                if (Objects.isNull(websiteExceptions) || websiteExceptions.isEmpty()) {
                    dataToUpdate.put(KEY_WEBSITE_EXCEPTIONS, payloads);
                } else {
                    dataToUpdate.put(KEY_WEBSITE_EXCEPTIONS, new ArrayList<>());
                    update.put("$set", dataToUpdate);
                    mongoService.update(query, update, false, false, FSECURE_CACHE);

                    for (HashMap<String, Object> websiteException : websiteExceptions) {
                        for (HashMap<String, Object> payld : payloads) {
                            if (String.valueOf(websiteException.get("url")).equalsIgnoreCase(String.valueOf(payld.get("url"))) && Objects.nonNull(payld.get("policy")) && !String.valueOf(payld.get("policy")).isEmpty()) {
                                BasicDBObject webEx = new BasicDBObject();
                                webEx.put("url", payld.get("url"));
                                webEx.put("policy", payld.get("policy"));
                                websiteExceptionsToUpdate.add(webEx);
                            } else {
                                websiteExceptionsToUpdate.add(websiteException);
                            }
                        }
                    }
                    dataToUpdate.put(KEY_WEBSITE_EXCEPTIONS, websiteExceptionsToUpdate);
                }
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }


    public void processPutProtection(ProceedingJoinPoint joinPoint, String operation, int size, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        Object value = joinPoint.getArgs()[1];
        HashMap<String, Object> payload = (HashMap<String, Object>) objectMapper.convertValue(value, HashMap.class);

        if (Objects.nonNull(payload) && !payload.isEmpty()) {
            if (Objects.nonNull(dbObject)) {
                HashMap<String, Object> protection = (HashMap<String, Object>) dbObject.get(KEY_PROTECTION);

                if (Objects.isNull(protection) || protection.isEmpty()) {
                    protection.putAll(payload);
                } else {

                    if (Objects.nonNull(payload.get("browsing"))) {
                        protection.put("browsing", payload.get("browsing"));
                    }
                    if (Objects.nonNull(payload.get("tracking"))) {
                        protection.put("tracking", payload.get("tracking"));
                    }
                    if (Objects.nonNull(payload.get("iot"))) {
                        protection.put("iot", payload.get("iot"));
                    }
                }
                dataToUpdate.put(KEY_PROTECTION, protection);
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }

    public void processPutProfileById(ProceedingJoinPoint joinPoint, String operation, int size, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        Object value = joinPoint.getArgs()[2];
        HashMap<String, Object> payload = (HashMap<String, Object>) objectMapper.convertValue(value, HashMap.class);
        String profileId = joinPoint.getArgs()[1].toString();

        if (Objects.nonNull(payload) && !payload.isEmpty()) {
            if (Objects.nonNull(dbObject)) {
                List<HashMap<String, Object>> profiles = (List<HashMap<String, Object>>) dbObject.get(KEY_PROFILES);
                if (Objects.isNull(profiles) || profiles.isEmpty()) {
                    profiles = new ArrayList<HashMap<String, Object>>();
                    payload.put("id", CommonUtils.getCurrentTimeInMillis());
                    profiles.add(payload);
                } else {
                    HashMap<String, Object> profile = profiles.stream().filter(elem -> String.valueOf(elem.get("id")).equalsIgnoreCase(profileId)).findAny().orElse(null);
                    if (Objects.isNull(profile)) {
                        payload.put("id", CommonUtils.getCurrentTimeInMillis());
                        profiles.add(payload);
                    } else {
                        profiles.remove(profile);
                        if (Objects.nonNull(payload.get("enable"))) {
                            profile.put("enable", payload.get("enable"));
                        }
                        if (Objects.nonNull(payload.get("name"))) {                               // should be commented also on rpc side
                            profile.put("name", payload.get("name"));
                        }
                        if (Objects.nonNull(payload.get("avatarURL"))) {
                            profile.put("avatarURL", payload.get("avatarURL"));
                        }
                        if (Objects.nonNull(payload.get("bonus"))) {
                            profile.put("bonus", payload.get("bonus"));
                        }
                        if (Objects.nonNull(payload.get("categories")) && !((List<String>) payload.get("categories")).isEmpty()) {
                            profile.put("categories", payload.get("categories"));
                        }
                        if (Objects.nonNull(payload.get("dailyLimit")) && !((List<Integer>) payload.get("dailyLimit")).isEmpty()) {
                            profile.put("dailyLimit", payload.get("dailyLimit"));
                        }
                        if (Objects.nonNull(payload.get("schedules")) && !((List<HashMap<String, Object>>) payload.get("schedules")).isEmpty()) {
                            profile.put("schedules", payload.get("schedules"));
                        }
                        profiles.add(profile);
                    }

                }
                dataToUpdate.put(KEY_PROFILES, profiles);
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }

    public void processPutDeviceById(ProceedingJoinPoint joinPoint, String operation, int size, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        String deviceID = joinPoint.getArgs()[1].toString();
        Object value = joinPoint.getArgs()[2];
        HashMap<String, Object> payload = (HashMap<String, Object>) objectMapper.convertValue(value, HashMap.class);

        if (Objects.nonNull(payload) && !payload.isEmpty()) {

            if (Objects.nonNull(dbObject)) {
                List<HashMap<String, Object>> devices = (List<HashMap<String, Object>>) dbObject.get(KEY_DEVICES);
                if (Objects.isNull(devices) || devices.isEmpty()) {
                    devices = new ArrayList<>();
                    payload.put("macAddress", deviceID);
                    devices.add(payload);
                } else {
                    HashMap<String, Object> device = devices.stream().filter(elem -> elem.get("macAddress").toString().equalsIgnoreCase(deviceID)).findAny().orElse(null);
                    if (Objects.isNull(device)) {
                        payload.put("macAddress", deviceID);
                        devices.add(payload);
                    } else {
                        devices.remove(device);
                        if (Objects.nonNull(payload.get("friendlyName")) && !String.valueOf(payload.get("friendlyName")).isEmpty()) {
                            device.put("friendlyName", payload.get("friendlyName"));
                        }
                        if (Objects.nonNull(payload.get("profileId")) && !String.valueOf(payload.get("profileId")).isEmpty()) {
                            device.put("profileId", payload.get("profileId"));
                        }
                        if (Objects.nonNull(payload.get(KEY_PROTECTION))) {

                            HashMap<String, Object> payloadDeviceDataProtection = (HashMap<String, Object>) payload.get(KEY_PROTECTION);
                            HashMap<String, Object> protection = (HashMap<String, Object>) device.get("protection");

                            if (Objects.nonNull(payloadDeviceDataProtection.get("browsing"))) {
                                protection.put("browsing", payloadDeviceDataProtection.get("browsing"));
                            }
                            if (Objects.nonNull(payloadDeviceDataProtection.get("tracking"))) {
                                protection.put("tracking", payloadDeviceDataProtection.get("tracking"));
                            }
                            if (Objects.nonNull(payloadDeviceDataProtection.get("iot"))) {
                                protection.put("iot", payloadDeviceDataProtection.get("iot"));
                            }
                            if (Objects.nonNull(payloadDeviceDataProtection.get("blockInternet"))) {
                                protection.put("blockInternet", payloadDeviceDataProtection.get("blockInternet"));
                            }
                            device.put("protection", protection);
                        }
                        devices.add(device);
                    }
                }
                dataToUpdate.put(KEY_DEVICES, devices);
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }

    public void processPostProfile(ProceedingJoinPoint joinPoint, String operation, int size, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        Object value = joinPoint.getArgs()[size - 2];
        HashMap<String, Object> payload = (HashMap<String, Object>) objectMapper.convertValue(value, HashMap.class);
        if (Objects.nonNull(payload) && !payload.isEmpty()) {
            payload.put("id", CommonUtils.getCurrentTimeInMillis());
            if (Objects.nonNull(dbObject)) {
                List<HashMap<String, Object>> profiles = (List<HashMap<String, Object>>) dbObject.get(KEY_PROFILES);
                if (Objects.nonNull(profiles) && !profiles.isEmpty()) {
                    profiles.add(payload);
                    dataToUpdate.put(KEY_PROFILES, profiles);
                } else {
                    ArrayList<HashMap<String, Object>> profilesList = new ArrayList<HashMap<String, Object>>();
                    profilesList.add(payload);
                    dataToUpdate.put(KEY_PROFILES, profilesList);
                }
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }

    private HashMap<String, Object> createFsecureData(String serviceTelephoneNo) {
        HashMap<String, Object> data = new HashMap<>();
        data.put("serviceTelephoneNo", serviceTelephoneNo);
        data.put("lastRefresh", CommonUtils.getCurrentTimeInMillis());

        ArrayList<HashMap<String, Object>> emptyList = new ArrayList<>();

        data.put(KEY_DEVICES, emptyList);
        data.put(KEY_PROFILES, emptyList);
        data.put(KEY_BLOCKED_DEVICES, emptyList);
        data.put(KEY_WEBSITE_EXCEPTIONS, emptyList);
        data.put(KEY_PROTECTION, new HashMap<String, Object>());
        data.put(KEY_REGISTRATION, new HashMap<String, Object>());  // new to know valid true
        data.put(KEY_EVENTS, new HashMap<String, Object>());
        data.put(KEY_IS_CACHE_SYNC, false);

        return data;
    }

    private HashMap<String, Object> createFsecureData(String serviceTelephoneNo, String operation, List<HashMap<String, Object>> payload) {

        HashMap<String, Object> data = new HashMap<>();
        data.put("serviceTelephoneNo", serviceTelephoneNo);
        data.put("lastRefresh", CommonUtils.getCurrentTimeInMillis());

        List<HashMap<String, Object>> emptyList = new ArrayList<>();

        if (operation.equalsIgnoreCase(DEVICES)) {
            data.put(KEY_DEVICES, payload);
        } else {
            data.put(KEY_DEVICES, emptyList);
        }

        if (operation.equalsIgnoreCase(PROFILES)) {
            data.put(KEY_PROFILES, payload);
        } else {
            data.put(KEY_PROFILES, emptyList);
        }

        if (operation.equalsIgnoreCase(WEBSITE_EXCEPTION)) {
            data.put(KEY_WEBSITE_EXCEPTIONS, payload);
        } else {
            data.put(KEY_WEBSITE_EXCEPTIONS, emptyList);
        }

        data.put(KEY_BLOCKED_DEVICES, emptyList);
        data.put(KEY_REGISTRATION, new HashMap<String, Object>());
        data.put(KEY_EVENTS, new HashMap<String, Object>());
        data.put(KEY_IS_CACHE_SYNC, false);

        return data;
    }


    private void processPostDeviceTriggerAction(ProceedingJoinPoint joinPoint, String operation, int size, String serviceTelephoneNo, BasicDBObject query, DBObject dbObject, BasicDBObject dataToUpdate, BasicDBObject update) {
        Object value = joinPoint.getArgs()[size - 2];
        String deviceId = joinPoint.getArgs()[1].toString();
        HashMap<String, Object> payload = (HashMap<String, Object>) objectMapper.convertValue(value, HashMap.class);

        if (Objects.nonNull(payload) && !payload.isEmpty()) {
            if (Objects.nonNull(dbObject)) {
                HashMap<String, Object> blockedDevice = new HashMap<>();
                HashMap<String, Object> deviceAction = null;
                String action = String.valueOf(payload.get("action"));
                List<HashMap<String, Object>> blockedDevices = (List<HashMap<String, Object>>) dbObject.get(KEY_BLOCKED_DEVICES);
                if(blockedDevices == null) {
                    blockedDevices = new ArrayList<>();
                }

                if (Objects.nonNull(blockedDevices) && !blockedDevices.isEmpty()) {
                    deviceAction = blockedDevices.stream().filter(device -> device.get("deviceId").equals(deviceId)).findAny().orElse(null);
                    if (!action.equalsIgnoreCase("BLOCK") && Objects.nonNull(deviceAction))
                        blockedDevices.remove(deviceAction);
                }

                if (action.equalsIgnoreCase("BLOCK") && Objects.isNull(deviceAction)) {
                    blockedDevice.put("deviceId", deviceId);
                    blockedDevice.put("action", action);
                    blockedDevices.add(blockedDevice);
                }

                dataToUpdate.put(KEY_BLOCKED_DEVICES, blockedDevices);
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, FSECURE_CACHE);
            }
        }
    }
}
