package com.incs83.app.aop;


import com.datastax.driver.core.utils.UUIDs;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.User;
import com.incs83.app.responsedto.v2.Equipment.DiagonosticsRPCResponseDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentModel;
import com.incs83.app.responsedto.v2.Subscriber.SpeedTestHistoryDTO;
import com.incs83.app.utils.AsyncUtils;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.auditor.AuditableData;
import com.incs83.config.JwtConfig;
import com.incs83.config.KafkaConfig;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.exceptions.handler.*;
import com.incs83.exceptions.handler.OAUTH2AuthenticationException;
import com.incs83.exceptions.handler.OAUTH2ConfigException;
import com.incs83.exceptions.handler.OAUTH2InvalidCodeException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.pubsub.KafkaPublisher;
import com.incs83.security.tokenFactory.RawAccessJwtToken;
import com.incs83.util.AuditLogsHelper;
import com.incs83.util.ClientInfoUtils;
import com.incs83.util.CommonUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.AuditorConstants.*;
import static com.incs83.app.constants.queries.UserSQL.GET_USER_BY_EMAIL;
import static com.incs83.constants.ApplicationCommonConstants.IGNORE_FINAL;
import static com.incs83.constants.ApplicationCommonConstants.ZERO;
import static com.incs83.queries.AuditLogsSQL_Common.GET_USER_BY_USER_ID;

@Aspect
@Component
@ConditionalOnProperty(name = "auditLogsEnabled", matchIfMissing = false)
@Order(0)
public class Auditor {

    @Autowired
    private ClientInfoUtils clientInfoUtils;

    @Autowired
    private KafkaPublisher kafkaPublisher;

    @Autowired
    private KafkaConfig kafkaConfig;

    @Autowired
    DataAccessService dataAccessService;

    @Autowired
    private JwtConfig jwtConfig;

    @Autowired
    ObjectMapper objectMapper;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private AsyncUtils asyncUtils;

    private final String REQUEST_TIME_OUT = "Request Timed Out, please try again after sometime.";

    private static final Logger LOG = LogManager.getLogger("org");

    @Around("@annotation(auditable)" + IGNORE_FINAL)
    public Object auditable(ProceedingJoinPoint joinPoint, Auditable auditable) throws Throwable {
        Object results = null;
        String operation = auditable.operation();
        String userId = null;
        String apId = null;
        Boolean isBeforeLoggableUser = true;
        HttpServletRequest request = null;
        String url = null;
        String ip = null;
        HashMap<String, Object> payload = null;
        Long requestTimestamp = null;
        Long responseTimestamp = null;
        Boolean persist = auditable.persist();
        Boolean isSpeedTestPerformed = true;
//        Boolean isSubscriber = false;
        Integer agsLength = joinPoint.getArgs().length;
        if (operation.equals(DEVICE_PAUSE_UNPAUSE)) {
            operation = joinPoint.getArgs()[2].equals(INTERNET_FORCE_RESET) ? DEVICE_RESET_INTERNET : operation;
        } else if (operation.equals(EQUIPMENT_CHANNEL_OPTIMIZATION)) {
            operation = joinPoint.getArgs()[3].equals(OBJECT_TYPE_RESET_WIFI) ? EQUIPMENT_RESET_WIFI : operation;
        }
        try {
            if(operation.equals(DATA_MODEL_ACCESS)) {
                request = ((ServletRequestAttributes) (RequestContextHolder.getRequestAttributes())).getRequest();
            } else if (operation.contains("RPC")) {
                request = (HttpServletRequest) joinPoint.getArgs()[--agsLength];
            } else {
                request = ((ServletRequestAttributes) (RequestContextHolder.getRequestAttributes())).getRequest();
            }
            url = request.getRequestURI();
            ip = String.valueOf(clientInfoUtils.getClientIpAddr(request));
            if (operation.equals(AuditorConstants.USER_LOGIN)) {
                isBeforeLoggableUser = false;
                String username = joinPoint.getArgs()[ZERO].getClass().getDeclaredMethod("getUsername")
                        .invoke(joinPoint.getArgs()[ZERO]).toString();
                if (!Objects.isNull(username) && !username.isEmpty()) {
                    HashMap<String, String> query = new HashMap<>();
                    query.put("email", username);
                    List<User> users = (List<User>) dataAccessService.read(User.class, GET_USER_BY_EMAIL, query);
                    isBeforeLoggableUser = Objects.nonNull(users) && !users.isEmpty() && !users.get(0).isInternalUser();
                    if (isBeforeLoggableUser) {
                        userId = users.get(ZERO).getId();
                    }
                }

            } else {
                userId = CommonUtils.getUserIdOfLoggedInUser();
            }
            if (operation.equals(AuditorConstants.EQUIPMENT_SPEED_TEST)) {
                Boolean isRPCPerformed = (Boolean) joinPoint.getArgs()[1];
                if (!isRPCPerformed) {
                    isSpeedTestPerformed = false;
                    persist = false;
//                    throw new Exception("Log not required.");
                }
            }

            payload = new HashMap<>();
            List<String> argsToRemove = Arrays.asList("accessToken", "httpServletRequest");
            List<String> keysToRemove = Arrays.asList("ssid", "password", "oldPassword", "newPassword", "key");
            for (int i = 0; i < agsLength; i++) {
                CodeSignature codeSignature = (CodeSignature) joinPoint.getSignature();
                String arg = codeSignature.getParameterNames()[i];
                if (!(argsToRemove.contains(arg))) {
                    Object value = joinPoint.getArgs()[i];
                    if (Objects.nonNull(value)) {
                        if (value instanceof AuditableData) {
                            HashMap<String, Object> data = (HashMap<String, Object>) objectMapper.convertValue(value, HashMap.class);
                            for (String key : keysToRemove) {
                                if (data.containsKey(key))
                                    data.remove(key);
                            }

                            payload.putAll(data);
                        } else if (value instanceof List<?>) {
                            if (Objects.nonNull(value) && ((List) value).get(0) instanceof AuditableData) {
                                HashMap<String, Object> data;
                                List<HashMap<String, Object>> dataList = new ArrayList<>();
                                for (Object val : (List) value) {
                                    data = (HashMap<String, Object>) objectMapper.convertValue(val, HashMap.class);
                                    dataList.add(data);
                                }
                                payload.put(arg, dataList);
                            }
                        } else if (value instanceof MultipartFile) {
                            payload.put(arg, ((MultipartFile) value).getOriginalFilename());
                        } else {
                            payload.put(arg, value);
                        }
                        if (arg.equals("equipmentIdOrSerialOrSTN") || arg.equals("equipmentId")) {
//                            if (ValidationUtil.validateMAC(String.valueOf(value)))
//                                value = manageCommonService.getAPIDFromMAC(String.valueOf(value));

                            Equipment userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(String.valueOf(value));
                            apId = userAP.getRgwSerial();
                        } else if (operation.equals(AuditorConstants.EQUIPMENT_CREATE) && arg.equals("createEquipmentRequest")) {
                            EquipmentModel model  = (EquipmentModel) value;
                            apId = StringUtils.isNotBlank(model.getRgwSerial())? model.getRgwSerial(): apId;
                        }
                    }
                }
            }
            if (payload.isEmpty())
                payload = null;
        } catch (Throwable e) {
//            LOG.error("Error in creating logs (Before) : ", e);
        }

        String tid = null;
        try {
            requestTimestamp = CommonUtils.getCurrentTimeInMillis();
            results = joinPoint.proceed();// Exception handling with specfic error codes with aop
            responseTimestamp = CommonUtils.getCurrentTimeInMillis();
            if (operation.contains("RPC") && isSpeedTestPerformed) {
                if(results instanceof HashMap) {
                    tid = String.valueOf(((HashMap) results).get("tid"));
                } else if(results instanceof SpeedTestHistoryDTO) {
                    tid = ((SpeedTestHistoryDTO) results).getTid();
                }
                if (operation.contains("Monitor Mode")) {
                    DiagonosticsRPCResponseDTO res = ((ArrayList<DiagonosticsRPCResponseDTO>) ((HashMap) results).get("rpcResult")).stream().filter(q -> q.getType().toString().equals("GATEWAY") && q.getMessage().toString().contains("Request Timed Out")).findAny().orElse(null);
                    if (Objects.nonNull(res)) {
                        asyncUtils.createRPCStats(operation, tid, requestTimestamp, responseTimestamp, false);
                        createLogAndPublish(auditable, operation, userId, ip, url, apId, payload, 400, REQUEST_TIME_OUT);
                        persist = false;
                    } else {
                        asyncUtils.createRPCStats(operation, tid, requestTimestamp, responseTimestamp, true);
                    }
                } else
                    asyncUtils.createRPCStats(operation, tid, requestTimestamp, responseTimestamp, true);
            }
        } catch (OAUTH2AuthenticationException exp) {
            operation = AuditorConstants.OAUTH2_AUTHENTICATION_EXCEPTION;
            if (persist)
                createLogAndPublish(auditable, operation, NO_REPLY_USER, ip, url, apId, payload, exp.getCode(), exp.getMessage());
            throw exp;
        } catch (OAUTH2InvalidCodeException exp) {
            operation = AuditorConstants.OAUTH2_INVALID_CODE_EXCEPTION;
            if (persist)
                createLogAndPublish(auditable, operation, NO_REPLY_USER, ip, url, apId, payload, exp.getCode(), exp.getMessage());
            throw exp;
        } catch (OAUTH2ConfigException exp) {
            operation = AuditorConstants.OAUTH2_CONFIG_EXCEPTION;
            if (persist)
                createLogAndPublish(auditable, operation, NO_REPLY_USER, ip, url, apId, payload, exp.getCode(), exp.getMessage());
            throw exp;
        } catch (UserLoginLimitException exp) {
            String email = exp.getMessage();
            payload = new HashMap<>();
            payload.put("loginEmail", email);
            operation = AuditorConstants.MAX_LOGIN_LIMIT;
            createLogAndPublish(auditable, operation, NO_REPLY_USER, ip, url, apId, payload, exp.getCode(), exp.getMessage());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Maximum login limit reached");
        } catch (ValidationException ex) {
            responseTimestamp = CommonUtils.getCurrentTimeInMillis();
            if(results instanceof HashMap) {
                tid = String.valueOf(((HashMap) results).get("tid"));
            } else if(results instanceof SpeedTestHistoryDTO) {
                tid = ((SpeedTestHistoryDTO) results).getTid();
            }
            if (operation.contains("RPC") && isSpeedTestPerformed && Objects.nonNull(ex.getMessage()) && ex.getMessage().contains("Request Timed Out"))
                asyncUtils.createRPCStats(operation, tid, requestTimestamp, responseTimestamp, false);
            else if (operation.contains("FRPC") && Objects.nonNull(ex.getCode()) && FSECURE_RESPONSE_MESSAGE.containsKey(ex.getCode()))
                asyncUtils.createRPCStats(operation, tid, requestTimestamp, responseTimestamp, true);

            if (persist)
                createLogAndPublish(auditable, operation, userId, ip, url, apId, payload, ex.getCode(), ex.getMessage());

            throw ex;
        } catch (Exception e) {
            int resCode = 500;
            if (Objects.nonNull(exceptionCodes.get(e.getClass().getSimpleName())))
                resCode = exceptionCodes.get(e.getClass().getSimpleName());

            createLogAndPublish(auditable, operation, userId, ip, url, apId, payload, resCode, e.getMessage());
            throw e;
        }
        if (!isBeforeLoggableUser) {
            try {
                RawAccessJwtToken rawToken = new RawAccessJwtToken(((HashMap<String, String>) ((ApiResponseDTO) results).getData()).get("accessToken"));
                Jws<Claims> jwsClaims = null;
                jwsClaims = rawToken.parseClaims(jwtConfig.getTokenSigningKey());
                userId = ((Claims) jwsClaims.getBody()).getId();
//                isSubscriber = (Boolean) ((Claims) jwsClaims.getBody()).get("subscriber");
//                LOG.info("isSubscriber : "+isSubscriber);
                if (persist)
                    createLogAndPublish(auditable, operation, userId, ip, url, apId, payload, 200, null);
            } catch (Throwable e) {
            }
        } else {
            if (persist)
                createLogAndPublish(auditable, operation, userId, ip, url, apId, payload, 200, null);
        }
        return results;
    }

    private void createLogAndPublish(Auditable auditable, String operation, String userId, String ip, String url, String apId, HashMap<String, Object> payload, int responseCode, String responseMessage) throws Exception {
        HashMap<String, Object> auditLogs = new HashMap();
        auditLogs.put(SUG_ACT_TYPE, PLATFORM_LOGS);
        auditLogs.put("id", UUIDs.timeBased().toString().replaceAll("-", ""));
        auditLogs.put("userId", userId);
        auditLogs.put("ip", ip);
        auditLogs.put("url", url);
        auditLogs.put("apId", apId);
        auditLogs.put("time", Calendar.getInstance().getTimeInMillis());
        auditLogs.put("method", String.valueOf(auditable.method()));
        auditLogs.put("operation", operation);
        auditLogs.put("payload", payload);
        auditLogs.put("responseCode", responseCode);
        auditLogs.put("responseMessage", responseMessage);
        kafkaPublisher.publishAuditLogsToKafka(auditLogs, kafkaConfig.getTopic());

    }

}
