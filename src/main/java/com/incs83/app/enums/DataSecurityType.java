package com.incs83.app.enums;

public enum DataSecurityType {
    mac<PERSON>dd<PERSON>("MAC Address"),
    ssid_Name("SSID Name"),
    ssid_Password("SSID Password"),
    camsAccount("CAMS Account No."),
    globalAccount("Global Account No."),
    phoneNumber("Phone No.");

    String value;

    DataSecurityType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
