package com.incs83.app.enums.auditor;

public enum AuditorEnum {
    CLUSTER_CREATE("Cluster Create"),
    CLUSTER_UPDATE("Cluster Update"),
    CLUSTER_DELETE("Cluster Delete"),
    DEVICE_PAUSE_UNPAUSE("Device Pause/Unpause (RPC)"),
    DEVICE_SPEED_TEST("Device Speed Test (RPC)"),
    DEVICE_FRIENDLY_NAME("Device Update Friendly Name"),
    DEVICE_RESET_INTERNET("Device Reset Internet (RPC)"),
    DEVICE_UPDATE_TYPE_FOR_MAC("Device Update MAC type"),
    DEVICE_FORGET("Device Forget"),
    DEVICE_DETAIL("Device Details"),
    DEVICE_INTERNET_ACCESS("Device Internet Access"),
    RGW_MONITOR_MODE("RGW Monitor Mode (RPC)"),
    ETL_RESTART("ETL LC Operation"),
    ETL_UPLOAD_CONFIG("ETL Upload Config"),
    NOTIFICATION_UPDATE("Notification Update"),
    APPLICATION_NOTIFICATION_UPDATE("Application Notification Create"),
    EQUIPMENT_UPDATE_FRIENDLY_NAME("RGW Update Friendly Name"),
    EQUIPMENT_REBOOT("RGW Reboot (RPC)"),
    EQUIPMENT_SPEED_TEST("RGW Speed Test (RPC)"),
    EQUIPMENT_FORGET("RGW Forget"),
    EQUIPMENT_WAN_DETAILS ("Equipment WAN Details"),
    EQUIPMENT_CHANNEL_OPTIMIZATION("RGW Channel Optimization (RPC)"),
    EQUIPMENT_RESET_WIFI("RGW Reset WIFI (RPC)"),
    EQUIPMENT_DETAILS("Subscriber Details"),
    EQUIPMENT_SELECT("Equipment Select"),
    EQUIPMENT_LIST("Equipment List"),
    SEARCH_EQUIPMENT_DETAILS("Subscriber lookup"),
    USER_CREATE("User Create "),
    USER_DELETE("User Delete"),
    USER_UPDATE("User Update"),
    //    USER_BY_ID ("User By ID"),
    USER_LOGIN("User Login (Oauth)"),
    USER_LOGIN_IAM("User Login (IAM)"),
    USER_LOGOUT("User Logout"),
    GROUP_CREATE("Group Create"),
    GROUP_UPDATE("Group Update"),
    GROUP_DELETE("Group Delete"),
    //    GROUPS_READ ("Groups Read"),
//    GROUP_DETAILS_BY_ID ("Group By ID"),
//    ROLE_READ ("Roles"),
//    ROLE_BY_ID ("Role By ID"),
    ROLE_CREATE("Role Create"),
    ROLE_UPDATE("Role Update"),
    ROLE_DELETE("Role Delete"),
    CHANGE_USER_PASSWORD("Change Password"),
    ISP_CREATE("ISP Create"),
    ISP_UPDATE("ISP Update"),
    ISP_DELETE("ISP Delete"),
    ROLE_MENU_MAPPING_UPDATE("Role-Menu Mapping update"),
    MENU_ATTRIBUTE_MAPPING_UPDATE("Menu-Attribute Mapping update"),
    NETWORK_CHANGE_UPDATE("Network State Update (RPC)"),
    NETWORK_CREDS_UPDATE("Network CREDS Update (RPC)"),
    SMART_STEERING_ENABLE_DISABLE("Smart Steering Enable/Disable (RPC)"),
    SUBSCRIBER_PROFILE_CREATE("Subscriber Profile Create"),
    SUBSCRIBER_PROFILE_DELETE("Subscriber Profile Delete"),
    SUBSCRIBER_PROFILE_PAUSE_UNPAUSE("Subscriber Profile Pause/Unpause (RPC)"),
    SUBSCRIBER_PROFILE_PAUSE_UNPAUSE_FOR_DURATION("Subscriber Profile Pause/Unpause For Duration (RPC)"),
    SUBSCRIBER_PROFILE_UPDATE("Subscriber Profile Update"),
    SCHEDULE_CREATE("Schedule Create"),
    SCHEDULE_UPDATE("Schedule Update"),
    SCHEDULE_DELETE("Schedule Delete"),
    SPEED_TEST_URL_CREATE("Speed Test URL Create"),
    SPEED_TEST_URL_UPDATE("Speed Test URL Update"),
    SPEED_TEST_URL_DELETE("Speed Test URL Delete"),
    SUBSCRIBER_DELETE("Subscriber Delete"),
    SUBSCRIBER_BULK_DELETE("Subscriber Bulk Delete"),
    SUBSCRIBER_UPDATE("Subscriber Update"),
    SUBSCRIBERS_FILE_UPLOAD("Subscribers file upload"),
    CONFIG_PROPERTY_UPDATE("Config Property Update"),
    SUBSCRIBER_FAILED_RECORDS("Subscriber Failed Records"),
    //    RGW_DISCONNECTED_LIST ("RGW Disconnected List"),
//    SUBSCRIBER_LIST ("Subscribers List"),
//    USERS_LIST ("Users List"),
//    DATA_LOGS ("Data Logs"),
    INDEX_TTL_UPDATE("Index TTL Update"),
    INDEX_TTL_DELETE("Index TTL Delete"),
    DEVICE_VENDOR_DETAIL_UPDATE("Device Vendor Update"),
    RGW_FRIENDLY_NAME("RGW Friendly Name Update"),
    DEVICE_INTERNET_PAUSE_UNPAUSE("Device Internet Pause/Unpause (RPC)"),
    OAUTH_CREATE("Oauth Config Create"),
    OAUTH_DELETE("Oauth Config Delete"),
    OAUTH_MAPPING_CREATE("Oauth Mapping Create"),
    PROCESS_SUBSCRIBERS_BULK_UPDATE("Invoke Subscribers Bulk Updation"),
    SUBSCRIBERS_FILE_DELETE("Subscribers file Delete"),
    UPDATE_DATA_OBSCURITY("Data Obscurity Update"),
    UPDATE_SERVICE_CONFIG("Service Configuration Update"),
    UPDATE_SERVICE_CONFIG_ENV("Service Configuration Environment Update"),
    UPLOAD_LOGO("Logo Upload"),
    SUBSCRIBER_CREATE("Subscriber Create"),
    SDA_ACTIONS("SDA Actions Performed"),
    OAUTH2_CONFIG_EXCEPTION("OAUTH2 CONFIG EXCEPTION"),
    OAUTH2_INVALID_CODE_EXCEPTION("OAUTH2 INVALID CODE EXCEPTION"),
    OAUTH2_AUTHENTICATION_EXCEPTION("OAUTH2 AUTHENTICATION EXCEPTION"),
    MAX_LOGIN_LIMIT("Maximum Login Limit Reached"),
    EQUIPMENT_CREATE("Equipment Create"),
    EQUIPMENT_UPDATE("Equipment Update"),
    EQUIPMENT_DELETE("Equipment Delete"),
    FAILED_IAM_LOGIN("Failed User Login (IAM)"),

    // Auto WiFi Config Restore
    AUTO_WIFI_CONFIG_RESTORE("Auto WiFi Config Restore"),
    AUTO_WIFI_CONFIG_RESTORE_SETTINGS("Auto WiFi Config Restore Settings"),

    // F-SECURE
    SET_FSECURE_KEY("Set Fsecure Key"),
    FSECURE_CONFIG_RESTORE("F-Secure Config Restore"),
    PROFILE_CREATE("Profile Create (FRPC)"),
    PROFILE_DELETE("Profile Delete (FRPC)"),
    PROFILE_UPDATE("Profile Update (FRPC)"),
    PROFILE_ACTION_CREATE("Profile Action Create (FRPC)"),
    PROTECTION_UPDATE("Protection Update (FRPC)"),
    WEBSITE_EXCEPTION_UPDATE("Website Exception Update (FRPC)"),
    WEBSITE_EXCEPTION_DELETE("Website Exception Delete (FRPC)"),
    DEVICE_UPDATE("Device Update (FRPC)"),
    DEVICE_ACTION_CREATE("Device Action Create (FRPC)"),
    EVENTS_DELETE("Events Delete (FRPC)"),
    REGISTRATION_UPDATE("Registration Update (FRPC)"),
    REGISTRATION_DELETE("Registration Delete (FRPC)"),
    TIMEZONE_UPDATE("Timezone Update (FRPC)"),
    GET_ALL_DEVICE("Get All Devices (FRPC)"),
    GET_DEVICE_BY_ID("Get Device By ID (FRPC)"),
    GET_ALL_EVENTS("Get All Events (FRPC)"),
    GET_EVENTS_COUNTERS("Get Event Counters (FRPC)"),
    GET_ALL_PROFILES("Get All Profiles (FRPC)"),
    GET_PROFILE_BY_ID("Get Profile By ID (FRPC)"),
    GET_PROTECTION_POLICIES("Get Protection Policies (FRPC)"),
    GET_REGISTRATION_KEY("Get Registration Key (FRPC)"),
    GET_WEBSITE_EXCEPTIONS("Get Website Exceptions (FRPC)"),
    GET_TIMEZONE_DATA("Get Timezone (FRPC)"),
    GET_FIRMWARE("Get Firmware Info (RPC)"),
    POST_FIRMWARE("POST Firmware Action (RPC)"),
    GET_FIRMWARE_POLICY("Get Firmware Policy"),
    POST_FIRMWARE_POLICY("Post Firmware Policy"),
    PUT_FIRMWARE_POLICY("Put Firmware Policy"),
    DELETE_FIRMWARE_POLICY("Delete Firmware Policy"),
    REMOTEACESS_HTTP_GET("RemoteAccess Http GET (RPC)"),
    REMOTEACESS_HTTP_PUT("RemoteAccess Http PUT (RPC)"),
    REMOTEACESS_RTTY_GET("RemoteAccess Rtty GET (RPC)"),
    REMOTEACESS_RTTY_PUT("RemoteAccess Rtty PUT (RPC)"),
    DATA_MODEL_ACCESS("Data model access (RPC)"),
    RESET_PASSWORD("Reset password"),

    // Remote Debug Session Operations
    REMOTE_DEBUG_GET_ALL_SESSIONS("Remote Debug Get All Sessions"),
    REMOTE_DEBUG_CREATE_SESSION("Remote Debug Create Session"),
    REMOTE_DEBUG_JOIN_OR_EXTEND_SESSION("Remote Debug Join Or Extend Session"),
    REMOTE_DEBUG_DELETE_SESSION("Remote Debug Delete Session");

    String value;

    public String getValue() {
        return value;
    }

    AuditorEnum(String value) {
        this.value = value;
    }
}
