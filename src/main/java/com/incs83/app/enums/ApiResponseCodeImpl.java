package com.incs83.app.enums;


import com.incs83.abstraction.ResponseCode;

public enum ApiResponseCodeImpl implements ResponseCode {

   /* FLOW_VERSION_NOT_FOUND(6, "FLOW_VERSION_NOT_FOUND"),
    FORM_TEMPLATE_NOT_FOUND(7, "FORM_TEMPLATE_NOT_FOUND"),
    NULL_OR_EMPTY_TEMPLATE_VERSIONS(8, "NULL_OR_EMPTY_TEMPLATE_VERSIONS"),
    CANNOT_DELETE_ACTIVATED_VERSION(9, "CANNOT_DELETE_ACTIVATED_VERSION"),

    FLOW_NOT_FOUND(16, "FLOW_NOT_FOUND"),
    REPORT_POLICY_CORRUPTED(18, "REPORT_POLICY_CORRUPTED"),
    PROFILE_SCHEDULE_FOUND(19, "PROFILE_SCHEDULE_FOUND"),
    SCHEDULE_ATTACHED_TO_PROFILE(20, "SCHEDULE_ATTACHED_TO_PROFILE"),
    NO_RESOURCE_EXIST_WITH_ID(21, "NO_RESOURCE_EXIST_WITH_ID"),
    MAC_ADDRESS_NOT_FOUND(22, "MAC_ADDRESS_NOT_FOUND"),
    START_TIME_CAN_NOT_BE_GREATER_THAN_END_TIME(23, "START_TIME_CAN_NOT_BE_GREATER_THAN_END_TIME"),
    MOBILE_NOT_SUBSCRIBED(24, "MOBILE_NOT_SUBSCRIBED"),
    NO_AP_DETAIL_FOR_USER(25, "NO_AP_DETAIL_FOR_USER"),
    START_TIME_EQUAL_TO_END_TIME(26, "START_TIME_EQUAL_TO_END_TIME"),
    NO_INTERNET_CONNECTION(27, "NO_INTERNET_CONNECTION");
*/
   MAC_ADDRESS_NOT_FOUND(10, "MAC_ADDRESS_NOT_FOUND");
    private int code;
    private String message;

    ApiResponseCodeImpl(int code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }

}
