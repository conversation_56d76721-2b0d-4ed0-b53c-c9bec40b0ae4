package com.incs83.app.enums;

public enum BandType {
    BAND_5G_HIGH(0, "5G High"),
    BAND_5G(1, "5G"),
    BAND_24G(2, "2.4G"),
    BAND_6G(4, "6G");

    private int idx;
    private String name;

    BandType(int idx, String name) {
        this.idx = idx;
        this.name = name;
    }

    public static String getBandByIndex(int bandIdx) {

        for(BandType bandType : BandType.values()) {
            if(bandType.idx == bandIdx) {
                return bandType.name;
            }
        }

        return null;
    }
}
