package com.incs83.app.enums;

public enum ProfileCategories {

    <PERSON><PERSON><PERSON><PERSON>("ALCOH<PERSON>"),
    ADULT("ADULT"),
    <PERSON>LEGAL("ILLEGAL"),
    DISTURBING("DISTURBING"),
    UNKNOWN("UNKNOWN"),
    DATING("DATING"),
    DRUGS("DRUGS"),
    VIOLENCE("VIOLENCE"),
    GAMBLING("GAMBLING"),
    HATE("HATE"),
    SHOPPING("SHOPPING"),
    SOCIAL_NETWORKING("SOCIAL_NETWORKING"),
    STREAMING("STREAMING"),
    WAREZ("WAREZ"),
    WEAPONS("WEAPONS");
    String value;
    ProfileCategories(String value) {
        this.value = value;
    }
    public String getValue() {
        return value;
    }
}
