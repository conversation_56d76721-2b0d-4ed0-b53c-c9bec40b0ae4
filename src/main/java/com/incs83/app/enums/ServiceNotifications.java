package com.incs83.app.enums;

public enum  ServiceNotifications {
    SLOW_ETL("NA"),
    RGW_DISCONNECTED("RGW_DISCONNECTED_PERCENTAGE_NOTIFICATION_THRESHOLD"),
    VMQ_DISCONNECTED_TCP_CONNECTION("ESTABLISHED_TCP_CONNECTION_PERCENTAGE_NOTIFICATION_THRESHOLD"),
    VMQ_HIGH_MEMORY_CONSUMPTION("VMQ_MEMORY_INCREASED_PERCENTAGE_THRESHOLD"),
    VMQ_HIGH_DISK_CONSUMPTION("VMQ_DISK_INCREASED_PERCENTAGE_THRESHOLD"),
    ETL_S3_UPLOAD_V3("NA"),
    ETL_S3_UPLOAD_V4("NA"),
    KAFKA_MESSAGE_RATE("KAFKA_MESSAGE_RATE_THRESHOLD"),
    FAILED_LOGIN("FAILED_LOGIN_PERCENTAGE_NOTIFICATION_THRESHOLD"),
    RPC_FAILURE_INCREASED_PERCENTAGE_THRESHOLD("RPC_FAILURE_PERCENTAGE_NOTIFICATION_THRESHOLD");

    String value;

    ServiceNotifications(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
