package com.incs83.app.enums;

public enum SDAOperations {
    REFRESH(":4545/refreshservice"),
    HARD_REFRESH(":4545/hardrefresh"),
    STOP_MACHINE(":4545/stopMachine"),
    RUN_SHELL_SCRIPT(":4545/runShellScript"),
    SDA_ENABLE(":4545/sda/start"),
    SDA_DISABLE(":4545/sda/pause"),
    SDA_STATUS(":4545/sda/status");

    String value;

    SDAOperations(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
