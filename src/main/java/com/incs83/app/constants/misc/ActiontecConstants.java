/**
 * <AUTHOR> Puri
 * @Created 10-Apr-2017
 */
package com.incs83.app.constants.misc;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public interface ActiontecConstants {

    String AP_DASHBOARD_WIFI_HEALTH_PER_USER_PER_DAY = "wifiHealthPerUserPerDay";
    String USER_NETWORK_POLICY = "userNetworkPolicy";
    String NETWORK_POLICY = "networkPolicy";
    String RADIO_OCCUPANCY_PER_AP = "radioOccupancyPerEquipment";
    String REPORT_POLICY = "reportPolicy";
    String TIMELY_REPORT_DATA = "timelyReportData";

    String DEVICE_CAPABILITY = "deviceCapability";
    String DEVICE_ASSOCIATION = "evAssociation";
    String DEVICE_DISASSOCIATION = "evDisassociation";
    String DEVICE_STEERING = "evSteeringLogs";
    String DEVICE_ROAMING = "evRoaming";
    String DEVICE_EVENT_LOG = "evLogs";
    String DEVICE_STATION_CONNECT_LOGS = "evStaConnectLogs";
    String BSSID_MAPPING = "bssidMapping";

    String CLUSTER_INFO = "clusterInfo";
    String WAN_SPEED_TEST_INFO = "wanspeedtestinfo";
    String STATION_SPEED_TEST_INFO = "stationSpeedTest";
    String INTERNET_STATE_INFO = "internetStateInfo";
    String MQTT_CLUSTER_CONNECTION_INFO = "mqttclusterconnectioninfo";
    String NORE_RED_END_POINTS = "nodeRedEndPoints";

    String ACTIONTEC = "actiontec";

    // RPC Operation
    String INTERNET_ON = "on";
    String INTERNET_OFF = "off";
    String INTERNET_FORCE_RESET = "force";
    String AP_REBOOT = "reboot";
    String REBOOT_EQUIPMENT = "rebootEquipment";
    String WAN_SPEED_TEST = "wanSpeedTest";
    String OPTIMIZE_AP = "optimizeAp";
    String BAND_WIDTH_UTILIZATION = "bandWidthUtilization";
    String DISABLE_GUEST_WIFI = "disableGuestWifi";

    String USER_DIRECTED_HEAL = "user";
    String MAC_ADDRESS = "macAddress";
    String BSID = "bssid";
    String SERIAL_NUMBER = "serialNumber";
    String OBSS_BSSID = "obssBSSID";
    String OBSSID = "obssid";


    Integer _24GRadio = 1;
    Integer _5GRadio = 2;
    Integer LOWER_LIMIT = -75;

    Integer _24G_PRIMARY = 1;
    Integer _24G_GUEST = 2;
    Integer _5G_PRIMARY = 5;
    Integer _5G_GUEST = 6;

    Integer CHANNEL_CHANGE_SUCCESS = 1;

    String REPORT_GENERATION_MANUAL = "Manual";
    String REPORT_GENERATION_BY_SCHEDULE = "Per Schedule";

    Map<String, Object> MQTT_CLIENT_ID = Collections.synchronizedMap(new HashMap<>());

    String AMAZON_ASSISTANT = "amazon";
    String GOOGLE_ASSISTANT = "google";

    /////////////// MONGO COLLECTION V2 ///////////////////////////////////
    String NETWORK_WIFI_INSIGHTS_PER_MINUTE = "networkWiFiInsightsPerMinute";
    String NETWORK_WIFI_INSIGHTS_PER_HOUR = "networkWifiInsightsPerHour";
    String NETWORK_UPLOAD_SPEED_DIST_PER_HOUR = "networkUploadSpeedDistributionPerHour";
    String NETWORK_UPLOAD_SPEED_DIST_PER_DAY = "networkUploadSpeedDistributionPerDay";
    String NETWORK_WIFI_INSIGHTS_PER_DAY = "networkWifiInsightsPerDay";
    String NETWORK_DOWNLOAD_SPEED_DIST_PER_HOUR = "networkDownloadSpeedDistributionPerHour";
    String NETWORK_DOWNLOAD_SPEED_DIST_PER_DAY = "networkDownloadSpeedDistributionPerDay";
    String NETWORK_WIFI_HEALTH_SCORE_DIST_PER_HOUR = "networkWiFiHealthScoreDistributionPerHour";
    String NETWORK_WIFI_HEALTH_SCORE_DIST_PER_DAY = "networkWiFiHealthScoreDistributionPerDay";
    String NETWORK_AIR_TIME_UTILIZATION_DIST_PER_DAY = "networkAirtimeUtilizationDistributionPerDay";
    String NETWORK_AIR_TIME_UTILIZATION_DIST_PER_HOUR = "networkAirtimeUtilizationDistributionPerHour";
    String NETWORK_WIFI_SIGNAL_STRENGTH_DIST_PER_HOUR = "networkWiFiSignalStrengthDistributionPerHour";
    String NETWORK_WIFI_SIGNAL_STRENGTH_DIST_PER_DAY = "networkWiFiSignalStrengthDistributionPerDay";
    String NETWORK_WIFI_CHANNEL_INSIGHTS_PER_HOUR = "networkWiFiChannelInsightsPerHour";
    String NETWORK_WIFI_CHANNEL_INSIGHTS_PER_DAY = "networkWiFiChannelInsightsPerDay";

    String NETWORK_5G_AIR_TIME_UTILIZATION_DIST_PER_DAY = "network5GAirtimeUtilizationDistributionPerDay";
    String NETWORK_5G_AIR_TIME_UTILIZATION_DIST_PER_HOUR = "network5GAirtimeUtilizationDistributionPerHour";


    String NETWORK_24G_AIR_TIME_UTILIZATION_DIST_PER_DAY = "network24GAirtimeUtilizationDistributionPerDay";
    String NETWORK_24G_AIR_TIME_UTILIZATION_DIST_PER_HOUR = "network24GAirtimeUtilizationDistributionPerHour";

    String USER_WIFI_INSIGHTS_PER_DAY = "userWifiInsightsPerDay";
    String USER_WIFI_INSIGHTS_PER_MINUTE = "userWiFiInsightsPerMinute";
    String USER_WIFI_INSIGHTS_PER_HOUR = "userWifiInsightsPerHour";
    String USER_DOWNLOAD_SPEED_DIST_PER_HOUR = "userDownloadSpeedDistributionPerHour";
    String USER_DOWNLOAD_SPEED_DIST_PER_DAY = "userDownloadSpeedDistributionPerDay";
    String USER_WIFI_HEALTH_SCORE_DIST_PER_HOUR = "userWiFiHealthScoreDistributionPerHour";
    String USER_WIFI_HEALTH_SCORE_DIST_PER_DAY = "userWiFiHealthScoreDistributionPerDay";
    String USER_AIR_TIME_UTILIZATION_DIST_PER_HOUR = "userAirtimeUtilizationDistributionPerHour";
    String USER_AIR_TIME_UTILIZATION_DIST_PER_DAY = "userAirtimeUtilizationDistributionPerDay";
    String USER_WIFI_SIGNAL_STRENGTH_DIST_PER_HOUR = "userWiFiSignalStrengthDistributionPerHour";
    String USER_WIFI_SIGNAL_STRENGTH_DIST_PER_DAY = "userWiFiSignalStrengthDistributionPerDay";
    String USER_UPLOAD_SPEED_DIST_PER_HOUR = "userUploadSpeedDistributionPerHour";
    String USER_UPLOAD_SPEED_DIST_PER_DAY = "userUploadSpeedDistributionPerDay";

    String USER_5G_AIR_TIME_UTILIZATION_DIST_PER_HOUR = "user5GAirtimeUtilizationDistributionPerHour";
    String USER_5G_AIR_TIME_UTILIZATION_DIST_PER_DAY = "user5GAirtimeUtilizationDistributionPerDay";

    String USER_24G_AIR_TIME_UTILIZATION_DIST_PER_HOUR = "user24GAirtimeUtilizationDistributionPerHour";
    String USER_24G_AIR_TIME_UTILIZATION_DIST_PER_DAY = "user24GAirtimeUtilizationDistributionPerDay";

    String STATS_PER_ISP_PER_HOUR = "statsPerIspPerHour";
    String STATS_PER_ISP_PER_DAY = "statsPerIspPerDay";
    String STATS_PER_USER_PER_HOUR = "statsPerUserPerHour";
    String STATS_PER_USER_PER_DAY = "statsPerUserPerDay";

    String AP_DETAIL = "apDetail";
    String CPE_SMM_APP = "cpeSmmApp";

    String ISP_DEFAULT_CONFIG = "ispDefaultConfig";

    String DELAYED_AP = "delayedAP";
    String VMQ_METRICS = "vmqMetrics";
    String RADIO_CHANNEL_DETAIL = "radioChannelDetail";
    String NEIGHBOR_DETAIL = "neighborDetail";

    String STATION_DETAIL = "stationDetail";
    String USER_STATION_SLIDING_DATA = "userStationSlidingData";
    String DS_HOST_INSIGHTS = "dsHostInsights";
    String INTERNET_UTILIZATION_SLIDING_DATA = "internetUtilizationSlidingData";
    String AP_WIFI_INSIGHTS_PER_MINUTE = "apWiFiInsightsPerMinute";
    String WIFI_TRAFFIC = "wifiTraffic";
    String BANDWIDTH = "bandwidth";
    String INTERNET_RX = "internetRx";
    String INTERNET_TX = "internetTx";
    String INTERNET_TRAFFIC = "internetTraffic";
    String INTERNET_TRAFFIC_PERCENT = "internetTrafficPercent";
    String UP_STREAM = "upstream";
    String DOWN_STREAM = "downstream";
    HashMap<String, String> TRAFFIC_DIRECTION = new HashMap<String, String>() {
        {
            put(UP_STREAM, INTERNET_TX);
            put(DOWN_STREAM, INTERNET_RX);
            put(null, INTERNET_TRAFFIC);
        }
    };

    String RGW_CONNECTIVITY_STATS = "rgwConnectivityOverTime";
    String RGW_CONNECTIVITY_STATS_PER_DAY = "rgwConnectivityStatsPerDay";
    String DEVICE_CONNECTIVITY_STATS = "devicesConnectivityOverTime";
    String NETWORK_CONNECTIVITY_STATS = "networkConnectivityStats";
    String AUDITLOGS = "auditLogs";
    String BULK_UPLOAD_SUB_DIR = "subscriberBulkUpload";
    String BULK_UPLOAD_FILE_PRIFIX = "SubscriberData";
    String SDA_CONFIG = "sdaConfig";

    ////////////////////TELUS DROP2 CONSTANT ////////////////
    String system = "SYSTEM";
    String all = "ALL";
    String wan = "WAN";
    String moca = "MOCA";
    String mocaInfo = "MOCAINFO";
    String ethernet = "ETHERNET";
    String wireLess24g = "WIRELESS24G";
    String wireLess5g = "WIRELESS5G";
    String wifiHealth = "WIFIHEALTH";
    String wifiDlSpeed = "WIFIDLSPEED";
    String wifiUlSpeed = "WIFIULSPEED";
    String wifiAirTimeUtlz = "WIFIAIRTIMEUTLZ";
    String busy5g = "BUSY5G";
    String busy24g = "BUSY24G";
    String busy5gByDevice = "BUSY5GBYDEVICE";
    String busy24gByDevice = "BUSY24GBYDEVICE";
    String evAssociation = "EVASSOCIATION";
    String evDisassociation = "EVDIASSOCIATION";
    String steer = "STEER";
    String diagnostic = "DIAGNOSTIC";
    String roam = "ROAM";
    String eventLog = "EVENTLOG";
    String staConnectState = "STATION_CONNECT_STATE";
    String ALEX_AND_GOOGLEHOME_SUBSCRIBERS = "alexaAndGoogleHomeSubscribers";
    String INTERNET_SERVICE_PROVIDER = "internetServiceProvider";
    String SERVICE_NOTIFICATION = "serviceNotification";
    String ETL_UPLOAD_CONFIG = "etlUploadConfig";
    String DIAGONOSTIC_CONFIG = "DiagnosticConfig";
    String ETL_DEBUG_MINUTES = "ETL_DEBUG_MINUTES";
    ///////////////////////v3 Collection///////////////////
    String HOSTNAME_DETAIL = "hostnameDetail";

    String WAN_SPEED_TEST_COLLECTION = "wanSpeedTest";
    String CHANNEL_OPTIMIZATION = "channelOptimization";
    String RPC_REBOOT = "rpcReboot";
    String TOPOLOGY_PROXY_RESULT = "topologyProxyResult";

    ///////// New Profile functionality /////////////
    String USER_PROFILE = "userProfile";
    String PAUSE = "pause";
    String UNPAUSE = "unpause";
    String HEALTH_SCORE = "health_score";
    String ADD_PROFILE = "add_profile";
    String REMOVE_PROFILE = "remove_profile";

    /////////Reset Password Token////////////////
    String USER_PASSWORD_RESET_TOKEN = "userPasswordResetToken";

    String DEFAULT_ROLE_ID = "552cef1d9f0b415a8d22ccf7835f69ba";
    String ERROR_VOICE_MESSAGE = "I'm sorry, I'm having trouble processing your request. Please try again later.";
    String cleanupExtender = "apDetail,apWiFiInsightsPerMinute,evAssociation,evDisassociation,evRoaming,evSteeringLogs,internetUtilizationSlidingData,radioOccupancyPerEquipment,stationDetail,userNetworkPolicy";
    String cleanupRGW = "apDetail,apWiFiInsightsPerMinute,evAssociation,evDisassociation,evRoaming,evSteeringLogs,internetUtilizationSlidingData,radioOccupancyPerEquipment,stationDetail,userNetworkPolicy,userStationSlidingData,hostnameDetail,internetStateInfo,stationSpeedTest,userAirtimeUtilizationDistributionPerDay,userAirtimeUtilizationDistributionPerHour,userDownloadSpeedDistributionPerDay,userDownloadSpeedDistributionPerHour,userUploadSpeedDistributionPerDay,userUploadSpeedDistributionPerHour,userWiFiHealthScoreDistributionPerDay,userWiFiHealthScoreDistributionPerHour,userWiFiInsightsPerMinute,userWiFiSignalStrengthDistributionPerDay,userWiFiSignalStrengthDistributionPerHour,userWifiInsightsPerDay,userWifiInsightsPerHour,wanSpeedTest";
    String cleanupEquipment = "apWiFiInsightsPerMinute,internetUtilizationSlidingData,radioOccupancyPerEquipment,stationDetail,userNetworkPolicy,userStationSlidingData,hostnameDetail,internetStateInfo,stationSpeedTest,wanSpeedTest";

    String ATTR_DEVICE_ASSOC_EVENT = "assocEvents";
    String ATTR_DEVICE_DIS_ASSOC_EVENT = "disAssocEvents";
    String ATTR_DEVICE_ROAM_EVENT = "roamEvents";
    String ATTR_DEVICE_STEERING_EVENT = "steeringEvents";
    String ATTR_DEVICE_STEERING_LOGS = "steeringLogs";
    String ATTR_DEVICE_WIFI_THROUGHPUT = "wifiThroughput";
    String ATTR_DEVICE_WIFI_RSSI = "rssi";
    String ATTR_DEVICE_WIFI_WIFI_PHY = "wifiPhy";

    String ATTR_AP_ETHERNET = "ethernet";
    String ATTR_AP_MOCA = "MoCA";
    String ATTR_AP_MOCA_LAN = "MoCALAN";
    String ATTR_AP_SYSTEM_INFO = "systemInfo";
    String ATTR_AP_WAN = "wan";
    String ATTR_AP_24G = "_24g";
    String ATTR_AP_5G = "_5g";

    String ATTR_AP_BUSY_24G = "_24gBusy";
    String ATTR_AP_BUSY_5G = "_5gBusy";
    String ATTR_AP_BUSY_BY_DEVICE_24G = "_24gBusyByDevice";
    String ATTR_AP_BUSY_BY_DEVICE_5G = "_5gBusyByDevice";

    String ATTR_SUBSCRIBER_AVG_UP_LINK_RATE = "avgUplinkRate";
    String ATTR_SUBSCRIBER_AVG_DOWN_LINK_RATE = "avgDownlinkRate";
    String ATTR_SUBSCRIBER_WIFI_HEALTH_SOCRE = "healthScore";
    String ATTR_SUBSCRIBER_INTERNET_DETAILS = "internet";
    String ATTR_SUBSCRIBER_INTERNET_UTILIZATION = "internetUtilization";

    String DROP_DOWN_TIME_RANGE_COMMON = "DROP_DOWN_TIME_RANGE_COMMON";
    String DROP_DOWN_TIME_RANGE_STEERING = "DROP_DOWN_TIME_RANGE_STEERING";

    String MAC_ID_PARENT = "macIdParent";

    String TTL_EXPIRY_ACTION_HISTORY = "ttlExpiryActionHistory";

    String ETL_QUERY_PROGRESS = "etlQueryProgress";
    String ETL_QUERY_DURATION_MS = "ETL_QUERY_DURATION_MS";
    String ETL_CONFIG_LINGER_MINUTES = "ETL_CONFIG_LINGER_MINUTES";
    String ETL_METADATA = "etlMetadata";

    String COLLECTION_METADATA = "collectionMetadata";
    String INVALID_JSON_TABLE_HIVE = "invalidjson_hive";
    String RPC_RESULT_INFO = "rpcResultInfo";
    String JSON_RPC_V3_INFO = "jsonRpcResultV3";
    String RPC_RESPONSE_KEY = "isRPCResponseRecived";
    String ETL_RPC_RESULT_INFO = "etlRpcResultInfo";
    String PLATFORM_LOGS = "platformLogs";
    String SUG_ACT_TYPE = "sugActType";

    String OBJECT_TYPE_DOACS = "DoACS";
    String OBJECT_TYPE_RESET_WIFI = "ResetWiFi";
    String OBJECT_TYPE_DIAGNOSTICS = "SetBrustDiag";

    String ON_BOARDING_SUBSCRIBER_STATS = "onBoardingStats";

    String EQUIPMENT_DIAGNOSTIC_CONFIG = "DiagnosticConfig";
    String DIAGNOSTIC_DURATION_SECONDS = "DIAGNOSTIC_DURATION_SECONDS";
    String DIAGNOSTIC_COLLECTION_INTERVAL_SECONDS = "DIAGNOSTIC_COLLECTION_INTERVAL_SECONDS";
    String DIAGNOSTIC_REPORTING_INTERVAL_SECONDS = "DIAGNOSTIC_REPORTING_INTERVAL_SECONDS";
    String RPC_POLL_COUNT = "RPC_POLL_DURATION_SECONDS";
    String LONG_RPC_POLL_COUNT = "LONG_RPC_POLL_DURATION_SECONDS";
    String WAN_SPEED_TEST_RPC_POLL_COUNT = "LONG_RPC_POLL_DURATION_SECONDS";
    String SPEED_TEST_SAMPLE = "SPEED_TEST_SAMPLE";
    String CONNECTIVITY_REPORT_LOAD_RATE_MINUTES = "CONNECTIVITY_REPORT_LOAD_RATE_MINUTES";
    String RGW_DISCONNECTED_ALARM_THRESHOLD = "RGW_DISCONNECTED_ALARM_THRESHOLD";
    String EVENT_RGW_DISCONNECTED = "RGW_DISCONNECTED_PERCENTAGE_NOTIFICATION_THRESHOLD";
    String TCP_CONNECTION_ESTABLISHED = "ESTABLISHED_TCP_CONNECTION_PERCENTAGE_NOTIFICATION_THRESHOLD";
    String VMQ_MEMORY_PERCENTAGE_USED = "VMQ_MEMORY_INCREASED_PERCENTAGE_THRESHOLD";
    String VMQ_DISK_PERCENTAGE_USED = "VMQ_DISK_INCREASED_PERCENTAGE_THRESHOLD";

    String EQUIPMENT_REBOOT_ALERT_THRESHOLD = "EQUIPMENT_REBOOT_ALERT_THRESHOLD";

    String ETL_STATUS = "etlStatus";

    String ERROR = "Error";
    String SUCCESS = "Success";

    String ABS_DATABASE = "ABS_DATABASE";

    String TELEMETRY_DETAIL = "telemetryDetails";
    String NGINX_STATS = "nginxStats";
    String USER_IMAGE_PATH = "user/profile/";

    String FSECURE_CACHE = "fsecureCache";
    String ACTIVE_USER_LIMIT = "ACTIVE_USER_LIMIT";

    String SERVICE_DETAIL = "serviceDetail" ;

    String REBOOT_HISTORY = "rebootHistory";

    // Equipment batch upload
    String EQUIPMENT_BATCH_UPLOAD_DIR = "equipmentBatchUpload";
    String EQUIPMENT_UPLOAD_FILE_DETAILS = "equipmentUploadFileDetails";

    String SSID_BACKHAUL_AP = "Backhaul-AP";
    String INVALID_MAC_ADDRESS = "00:00:00:00:00:00";

    ArrayList<String> ASSOC_TYPE_LIST = new ArrayList<String>(){
        {
            add("associate");
            add("disassociated");
            add("WPA-auth failed");
            add("RSSI_REFRESH");
            add("DISASSOC_STEERING");
            add("DUPLICATED_STA");
            add("ROAMED_OUT_STA");
            add("MANUAL_STEERING");
            add("UNKNOWN");
        }
    };

    ArrayList<String> ASSOC_TYPE_WPA_AUTH_FAILED_LIST = new ArrayList<String>(){
        {
            add("WPA-auth failed");

        }
    };

    String EV_VICTIM_SELECTION = "VictimSelection";
    String EV_TARGET_AP_SELECTION = "TargetApSelection";
    final int RPC_DEFAULT_MAX_TRIES = 5;
    final long RPC_DEFUALT_RETRY_INTERVAL_MILLIS = 3000L;

    Map<Integer, String> ABS_TRIGGER_STRING = Stream.of(
            new AbstractMap.SimpleEntry<>(0, "ABS_TRIGGER_ST"),
            new AbstractMap.SimpleEntry<>(1, "ABS_TRIGGER_DY"),
            new AbstractMap.SimpleEntry<>(2, "ABS_TRIGGER_ST_RSSI"),
            new AbstractMap.SimpleEntry<>(3, "ABS_TRIGGER_DY_RSSI"),
            new AbstractMap.SimpleEntry<>(4, "ABS_TRIGGER_DS_ST"),
            new AbstractMap.SimpleEntry<>(5, "ABS_TRIGGER_DS_DY")
    ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));

    Map<Integer, String> ABS_VICTIM_REASON_STRING = Stream.of(
            new AbstractMap.SimpleEntry<>(0x1, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x2, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x4, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x8, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x10, "Reached the number of max steering(8)"),
            new AbstractMap.SimpleEntry<>(0x20, "Not dualband STA"),
            new AbstractMap.SimpleEntry<>(0x40, "STA for manual steering"),
            new AbstractMap.SimpleEntry<>(0x80, "STA being steered"),
            new AbstractMap.SimpleEntry<>(0x100, "Reached the number of max failure(5)"),
            new AbstractMap.SimpleEntry<>(0x200, "Roamed STA"),
            new AbstractMap.SimpleEntry<>(0x400, "Not a stationary STA"),
            new AbstractMap.SimpleEntry<>(0x800, "Legacy STA"),
            new AbstractMap.SimpleEntry<>(0x1000, "STA cooldown timer"),
            new AbstractMap.SimpleEntry<>(0x2000, "Frequent BTM-req failures"),
            new AbstractMap.SimpleEntry<>(0x4000, "Duplicated STA"),
            new AbstractMap.SimpleEntry<>(0x8000, "Overloaded original AP(prevent pingpong in offloading"),
            new AbstractMap.SimpleEntry<>(0x10000, "STA is repeater"),
            new AbstractMap.SimpleEntry<>(0x20000, "Traffic rate is above threshold"),
            new AbstractMap.SimpleEntry<>(0x40000, "STA RSSI is stronger than threshold"),
            new AbstractMap.SimpleEntry<>(0x80000, "Projected RSSI on target AP is weaker than current RSSI"),
            new AbstractMap.SimpleEntry<>(0x100000, "STA candidate timer"),
            new AbstractMap.SimpleEntry<>(0x200000, "No better AP ever found(prevent frequest disassociation of legacy client"),
            new AbstractMap.SimpleEntry<>(0x400000, "STA on video SSID")
    ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));

    Map<Integer, String> ABS_TARGET_REASON_STRING = Stream.of(
            new AbstractMap.SimpleEntry<>(0x1, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x2, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x4, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x8, "Reserved"),
            new AbstractMap.SimpleEntry<>(0x10, "AP is on DFS channel"),
            new AbstractMap.SimpleEntry<>(0x20, "Blacklisted AP"),
            new AbstractMap.SimpleEntry<>(0x40, "STA transitted to another AP before the best AP is determined"),
            new AbstractMap.SimpleEntry<>(0x80, "Target AP is overloaded"),
            new AbstractMap.SimpleEntry<>(0x100, "Target AP in BS is down"),
            new AbstractMap.SimpleEntry<>(0x200, "Preventing the steering loop"),
            new AbstractMap.SimpleEntry<>(0x400, "Projected target AP CU is above threshold"),
            new AbstractMap.SimpleEntry<>(0x800, "RSSI on current AP is the best"),
            new AbstractMap.SimpleEntry<>(0x1000, "No idle AP found"),
            new AbstractMap.SimpleEntry<>(0x2000, "No 11k responded"),
            new AbstractMap.SimpleEntry<>(0x4000, "No 11v transition observed")
    ).collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));
}
