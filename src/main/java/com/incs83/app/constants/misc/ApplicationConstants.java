
/**
 * <AUTHOR> Puri
 * @Created 10-Apr-2017
 */
package com.incs83.app.constants.misc;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;

/**
 * <AUTHOR>
 */
public interface ApplicationConstants {

    DecimalFormat TWO_DECIMAL_PLACE = new DecimalFormat("#.##");
    DecimalFormat FOUR_DECIMAL_PLACE = new DecimalFormat("#.####");
    HashSet<String> ALLOWED_DAYS_OF_WEEK = new HashSet<>();
    int HOURLY_DATA = 60;
    int ZERO = 0;
    int DESC = -1;
    int ASC = 1;
    String VERIZON_ISP = "verizon";
    String GET = "get";
    String EMPTY_STRING = "";
    String HYPHEN_STRING = "-";
    String UNDER_SCORE_STRING = "_";
    String DOT = ".";
    String PERCENT_SYMBOL_20 = "%20";
    String PERCENT_SYMBOL_27 = "%27";
    String PERCENT_SYMBOL_28 = "%28";
    String PERCENT_SYMBOL_29 = "%29";
    String PERCENT_SYMBOL_21 = "%21";
    String PERCENT_SYMBOL_3C = "%3C";
    String PERCENT_SYMBOL_5D = "%5D";
    String PERCENT_SYMBOL_7D = "%7D";
    String PERCENT_SYMBOL_7B = "%7B";
    String PERCENT_SYMBOL_5B = "%5B";
    String PERCENT_SYMBOL_3E = "%3E";
    String PERCENT_SYMBOL_3F = "%3F";
    String SINGLE_QUOTE = "'";
    String EXCLAMATION = "!";
    String QUESTION_MARK = "?";
    String OPEN_BRACKET = "(";
    String CLOSE_BRACKET = ")";
    String CLOSE_BRACKET_CURLY = "}";
    String OPEN_BRACKET_CURLY = "{";
    String CLOSE_BRACKET_SQUARE = "]";
    String OPEN_BRACKET_SQUARE = "[";
    String LESS_THAN = "<";
    String GREATER_THAN = ">";
    String LOG_DASH_SEPARATOR = "-------";
    String COMMA = ",";
    String OAUTH_ERROR_KEY = "error_description";
    String DEFAULT_ERROR = "Some error occurred";

    int CALENDER_CRITERIA_TODAY = -1;
    int CALENDER_CRITERIA_YESTERDAY = -1;
    int CALENDER_CRITERIA_LAST_3_DAYS = -3;
    int CALENDER_CRITERIA_LAST_7_DAYS = -7;
    int ONE = 1;
    int CALENDER_CRITERIA_LAST_30_DAYS = -30;
    int CALENDER_CRITERIA_LAST_90_DAYS = -90;

    int LOCALDATETIME_CRITERIA_LAST_DAY = 1;
    int LOCALDATETIME_CRITERIA_LAST_7_DAYS = 7;
    int LOCALDATETIME_CRITERIA_LAST_30_DAYS = 30;
    int LOCALDATETIME_CRITERIA_LAST_90_DAYS = 90;

    String TIMESTAMP = "timestamp";
    String ORIGIN = "origin";
    String DATE = "date";
    String MONTH = "month";
    String STATS_DATE_TIME = "statsDateTime";
    String CREATEDAT = "createdAt";

    String INTERNAL_DNS = "internalDns";
    String MAIL_FROM = "mailFrom";
    String SPACE = " ";
    String APP_MAIL_CONFIGURED = "app.mail.configured";

    String ENV_NAME = "env-name";

    String GATEWAY = "GATEWAY";
    String EXTENDER = "EXTENDER";
    String MICROSERVICE_CDS = "actiontec/api/v2/cds";

    String EXTERNAL_USER_PASSWORD = "iPEcZw5BdbpNRH-YmRIIM2iY";
    String GOOGLE_USER_ACCOUNT_INFO = "https://www.googleapis.com/oauth2/v1/userinfo";
    String VOICE_ACCESS_TOKEN = "CTJW26pGI3jwYObUO7ctpcoxaqBM0SAKoptR";
    String ALEXA = "alexa";
    String GOOGLE = "google";
    String DEFAULT = "DEFAULT-";
    String RPC_RESULT = "OK";
    String F_SECURE_RPC_RESULT = "200";
    String F_SECURE_RPC_FAIL = "503";
    String JOB_STARTED = "STARTED";
    String JOB_NOT_STARTED = "NOT_STARTED";
    String DEFAULT_COMPARTMENT = "DEFAULT";
    String DEFAULT_GROUP = "DEFAULT_GROUP";
    String DEFAULT_CLUSTER = "DEFAULT_CLUSTER";

    String END_USER = "USER";
    String SYSTEM_ADMIN = "SYSTEM_ADMIN";
    String ETL_TYPE_STREAM = "STREAM_QUERY_NAME";
    String ETL_TYPE_HISTORY = "HISTORY_QUERY_NAME";
    String ETL_TYPE_RPC = "RPC_QUERY_NAME";
    String UPDATE_ALL_CONFIG = "updateAllConfig";
    HashSet<String> ETL_OPERATION = new HashSet() {
        {
            add("restart");
            add("resume");
            add("pause");
            add("restartWithCleanUp");
            add("updateConfig");
            add("updateAllConfig");
        }
    };

    String BAND_5G = "5G";
    String BAND_24G = "24G";
    String BAND_6G = "6G";

    //CACHE MAPS CONSTANTS
    String NETWORK_INFO_KEY = "netInfo_key";
    String NETWORK_INFO_MAP_NAME = "NET_INFO";
    String KEY = "key";
    String MAP = "map";
    String COMPARTMENT = "COMPARTMENT";
    String ROLE = "ROLE";
    String NAVIGATION = "NAVIGATION";
    String SYSTEM_CONFIG = "SYSTEM_CONFIG";
    String TOKEN = "TOKEN";
    String ISP = "ISP";
    String ISP_KEY = "isp_key";
    String SYSTEM_CONFIG_KEY = "system_config";
    String COLLECTIONS_KEY = "actiontec_collections";
    String COLLECTIONS = "COLLECTIONS";
    String RGW_COUNT = "rgw_count";
    String STA_COUNT = "sta_count";

    Integer[] contentFilter = {0, 1, 2, 3, 4, 5, 6, 7, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31};
    ArrayList<Integer> contentFilterList = new ArrayList<>(Arrays.asList(contentFilter));

    String TELUS = "telus";

    String WIRE_SHARK_URL = "https://code.wireshark.org/review/gitweb?p=wireshark.git;a=blob_plain;f=manuf;hb=HEAD";

    String MAC_VENDOR_DETAIL = "VENDOR_DETAIL";
    String COLON = ":";
    String TIME_OUT = "TIME-OUT";
    String EXECUTE_FAIL = "Fail";
    String EXECUTE_OK = "OK";

    Long THREAD_TO_SLEEP = 1000L;

    String UC_CONTROL_TOPIC = "UCCTRL";

    String ETL_RESTART_TOPIC = "CTRL";
    String ETL_PROPERTIES = "etlProperties";
    String WIFI_HEALTH = "WIFI_HEALTH";
    String LAST_DATA_UPLINK_RATE = "LAST_DATA_UPLINK_RATE";
    String LAST_DATA_DOWN_LINK_RATE = "LAST_DATA_DOWN_LINK_RATE";
    String KAFKA_TOPICS_KEY = "KAFKA_TOPICS";

    String MQTT_CONNECTION_NAME = "Default_Channel_for_Cluster";

    String DASHBOARD_COUNTS = "DashboardCounts";
    String GLOBAL = "GLOBAL";

    String COMPARTMENT_LIST_MAP_NAME = "COMPARTMENT_LIST";
    String COMPARTMENT_INFO = "COMPARTMENT_INFO";
    String WINDSTREAM = "windstream";

    // OAuth2.0 Config
    String GRANT_TYPE = "authorization_code";
    String CLIENT_IDENTIFIER_SECRET_KEY = "AmQFtlXPHU7DYi1XLS8ElBmjuYwBtBdVF74fb777e093f647b9a0ec8d6ff3ff2bd6";
    String REPORT_TYPE_EXCEL = "EXCEL";

    String _FILE_TYPE = "csv";

    // Well Known Config Constants
    String AUTHORIZATION_INFO_URL = "authorization_endpoint";
    String INTROSPECTION_ENDPOINT = "introspection_endpoint";
    String TOKEN_INFO_URL = "token_endpoint";
    String USER_INFO_URL = "userinfo_endpoint";

    // Well Known Config User Claims

    String SUB = "sub";
    //    String NAME = "name";
    String GIVEN_NAME = "given_name";
    String FAMILY_NAME = "family_name";
    String PICTURE = "picture";
    String EMAIL = "email";
    String USER_TYPE = "userType";
    String MEMBER_OF = "memberOf";
    String CUSTOMER_PHONE_NO = "phoneNo";
    String LAST_NAME = "lastName";
    String FIRST_NAME = "firstName";
    String EMAIL_ADDRESS = "emailAddress";

    // FB response fields
    String FB_ID = "id";
    String FB_EMAIL = "email";
    String FB_FIRST_NAME = "first_name";
    String FB_LAST_NAME = "last_name";
    String FB_PICTURE = "picture";
    String FB_PICTURE_DATA = "data";
    String FB_PICTURE_DATA_URL = "url";


    String ACCESS_TOKEN = "access_token";
    String ADMIN_USER_ID = "bdc124f4da284a3396ec283ccc068fcb";

    String NO_REPLY_USER = "45c368a4a71f11eabb370242ac130002";


    String END_USER_ID = "552cef1d9f0b415a8d22ccf7835f69ba";

    String SERVICE_STATS_API = "checkstatus/all";
    String ELB_INFO_API = "elbinfo";


    String INTERNAL_SERVICE_STATS_DNS = "internal.service.stats.dns";
    String AWS_NAME_SPACE_EC2 = "AWS/EC2";
    String AWS_NAME_SPACE_NETWORK_ELB = "AWS/NetworkELB";
    String AWS_NAME_SPACE_CLASSIC_ELB = "AWS/ELB";
    String AWS_NAME_SPACE_EBS_VOLUME = "AWS/EBS";
    String CPU_UTILIZATION = "CPUUtilization";
    String NETWORK_PACKETS_IN = "NetworkPacketsIn";
    String NETWORK_PACKETS_OUT = "NetworkPacketsOut";
    String ACTIVE_FLOW_COUNT = "ActiveFlowCount";
    String ACTIVE_FLOW_COUNT_TLS = "ActiveFlowCount_TLS";
    String CONSUMED_LCUS = "ConsumedLCUs";
    String NEW_FLOW_COUNT = "NewFlowCount";
    String NEW_FLOW_COUNT_TLS = "NewFlowCount_TLS";
    String TCP_CLIENT_RESET_COUNT = "TCP_Client_Reset_Count";
    String TCP_ELB_RESET_COUNT = "TCP_ELB_Reset_Count";
    String TCP_TARGET_RESET_COUNT = "TCP_Target_Reset_Count";
    String LATENCY = "Latency";
    String REQUEST_COUNT = "RequestCount";
    String HTTP_CODE_ELB_5XX = "HTTPCode_ELB_5XX";
    String HTTP_CODE_BACKEND_4XX = "HTTPCode_Backend_4XX";
    String HTTP_CODE_BACKEND_5XX = "HTTPCode_Backend_5XX";
    String VOLUME_READ_OPS = "VolumeReadOps";
    String VOLUME_WRITE_OPS = "VolumeWriteOps";

    String RESOURCE_SPECIFICATION_API_PATH = ":4545/metrics";
    String VMQ_DEBUG_PASSWORD = "vmqDebugPassword";
    String VMQ_NOTIFICATION_PASSWORD = "vmqNotificationPassword";
    String VMQ_URL = "vmqUrls";
    String SUBSCRIBER_STATUS_API_PATH = ":4545/mqttdevicestats/";
    String APPLICATION_NOTIFICATION = "applicationNotification";
    String NOTIFICATION_TOPIC = "NOTIFICATION";
    String SDACONFIG = "sdaConfig";
    String DASHBOARD_LOGO = "dashboardLogo";
    String SPLASH_LOGO = "splashLogo";
    String LOGO_INITIAL_PATH = "logos/";
    String ZONE_AVAILABILITY_URL = "http://***************/latest/meta-data/placement/availability-zone";
    String S3_BUCKET = "s3.bucket";
    String BUILD_VERSION_INFO = "buildVersion/version.info";
    HashMap<Integer, String> FSECURE_RESPONSE_MESSAGE = new HashMap() {
        {
            put(400, "Bad Request, bad input parameter");
            put(401, "Unauthorized");
            put(404, "Not Found, the equipment of this subId is not found");
            put(405, "Method Not Allowed");
            put(500, "Internal Server Error");
            put(501, "Not Implemented");
            put(503, "Service Unavailable");
            put(409, "Device already registered");
        }
    };

    String END_USER_ROLE_ID = "552cef1d9f0b415a8d22ccf7835f69ba";
    String GROUP_ADMIN = "GROUP_ADMIN";
    String GROUP_ADMIN_ROLE_ID = "b19be782933f4849858361099f48ffaf";
    String APP_CLOUD_ADMIN = "APP_CLOUD_ADMIN";
    String APP_CLOUD_ADMIN_ROLE_ID = "7ae46f136d7a11f09dcb0242ac120002";
    String EMPLOYEE = "Employee";
    String CUSTOMER = "Customer";
    String GLOBAL_ACCOUNT_ID = "globalAccountId";
    String SERVICE_ACTIONS = "serviceActions";
    String USER_PASSWORD = "xxxxxxxxx";
    String INTERNAL_USER = "INTERNAL";
    String EXTERNAL_USER = "EXTERNAL";
    String SUBSCRIBER = "SUBSCRIBER";
    String REALTIME_CONNECTIVITY_STATUS = "PLUGIN_BASED_REALTIME_CONNECTIVITY_STATUS";
    String RPC_STATS = "rpcStats";
    String TOTAL_USER_INFO = "totalUserInfo";
    String LINK_RATE_PATTERN = "^(?:[1-9][0-9]{0,3}(?:\\.\\d{1,5})?|1000|1000.0|1000.00|10000|10000.0|10000.00)$";
    Long LAST_SEVEN_DAYS_MIN = 7 * 24 * 60l;

    HashMap<String, Integer> exceptionCodes = new HashMap() {
        {
            put("MultipartException", 400);
            put("HttpRequestMethodNotSupportedException", 405);
            put("TypeMismatchException", 400);
            put("AuthMethodNotSupportedException", 403);
            put("AuthEntityNotAllowedException", 403);
            put("HttpMessageConversionException", 400);
            put("BindException", 400);
            put("NumberFormatException", 400);
            put("MethodArgumentNotValidException", 400);
            put("MissingServletRequestParameterException", 400);
            put("MethodArgumentTypeMismatchException", 400);
            put("ServletRequestBindingException", 400);
            put("BadCredentialsException", 401);
            put("UrlNotFoundException", 404);
            put("ConflictException", 409);
        }
    };

    Long OAUTH2_TOKEN_TIMEOUT = 30*1000L;

    String GEN_TYPE_AUTO = "auto";
    String GEN_TYPE_MANUAL = "manual";

    Integer MAX_EXPIRED_HOURS = 72;
    String RTTY_SSH_CONNECT_PATH = "/connect/";
    String RTTY_HTTP_ACCESS_WEB_PATH = "/web/";
    String RTTY_HTTP_ACCESS_HTTP_PATH = "/http/127.0.0.1";
    String HTTPS = "https://";
    String WSS = "wss://";
}
