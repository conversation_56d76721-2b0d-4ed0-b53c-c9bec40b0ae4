package com.incs83.app.constants.misc;

/**
 * Created By AMAN on Monday, 25-February-2019
 */
public interface AuditorConstants {
    String CLUSTER_CREATE = "Cluster Create";
    String CLUSTER_UPDATE = "Cluster Update";
    String CLUSTER_DELETE = "Cluster Delete";
    String AUTO_WIFI_CONFIG_RESTORE_SETTINGS = "Auto WiFi Config Restore Settings";
    String DEVICE_PAUSE_UNPAUSE = "Device Pause/Unpause (RPC)";
    String DEVICE_SPEED_TEST = "Device Speed Test (RPC)";
    String DEVICE_FRIENDLY_NAME = "Device Update Friendly Name";
    String DEVICE_RESET_INTERNET = "Device Reset Internet (RPC)";
    String DEVICE_UPDATE_TYPE_FOR_MAC = "Device Update MAC type";
    String DEVICE_FORGET = "Device Forget";
    String DEVICE_DETAIL = "Device Details";
    String DEVICE_INTERNET_ACCESS = "Device Internet Access";
    String RGW_MONITOR_MODE = "RGW Monitor Mode (RPC)";
    String ETL_RESTART = "ETL LC Operation";
    String ETL_UPLOAD_CONFIG = "ETL Upload Config";
    String NOTIFICATION_UPDATE = "Notification Update";
    String APPLICATION_NOTIFICATION_UPDATE = "Application Notification Create";
    String EQUIPMENT_WAN_DETAILS = "Equipment WAN Details";
    String EQUIPMENT_UPDATE_FRIENDLY_NAME = "RGW Update Friendly Name";
    String EQUIPMENT_REBOOT = "RGW Reboot (RPC)";
    String EQUIPMENT_SPEED_TEST = "RGW Speed Test (RPC)";
    String EQUIPMENT_FORGET = "RGW Forget";
    String EQUIPMENT_CHANNEL_OPTIMIZATION = "RGW Channel Optimization (RPC)";
    String EQUIPMENT_RESET_WIFI = "RGW Reset WIFI (RPC)";
    String EQUIPMENT_DETAILS = "Subscriber Details";
    String EQUIPMENT_SELECT = "Equipment Select";
    String EQUIPMENT_LIST = "Equipment List";
    String USER_CREATE = "User Create ";
    String USER_DELETE = "User Delete";
    //    String USER_BY_ID = "User By ID";
    String USER_UPDATE = "User Update";
    String USER_LOGIN = "User Login (Oauth)";
    String USER_LOGIN_IAM = "User Login (IAM)";
    String USER_LOGOUT = "User Logout";
    String GROUP_CREATE = "Group Create";
    String GROUP_UPDATE = "Group Update";
    String GROUP_DELETE = "Group Delete";
    //    String GROUPS_READ = "Groups Read";
//    String GROUP_DETAILS_BY_ID = "Group By ID";
//    String ROLE_READ = "Roles";
//    String ROLE_BY_ID = "Role By ID";
    String ROLE_CREATE = "Role Create";
    String ROLE_UPDATE = "Role Update";
    String ROLE_DELETE = "Role Delete";
    String CHANGE_USER_PASSWORD = "Change Password ";
    String ISP_CREATE = "ISP Create";
    String ISP_UPDATE = "ISP Update";
    String ISP_DELETE = "ISP Delete";
    String ROLE_MENU_MAPPING_UPDATE = "Role-Menu Mapping update";
    String MENU_ATTRIBUTE_MAPPING_UPDATE = "Menu-Attribute Mapping update";
    String NETWORK_CHANGE_UPDATE = "Network State Update (RPC)";
    String NETWORK_CREDS_UPDATE = "Network CREDS Update (RPC)";
    String SMART_STEERING_ENABLE_DISABLE = "Smart Steering Enable/Disable (RPC)";
    String SMART_STEERING_CONFIG = "Smart Steering Config (RPC)";
    String SUBSCRIBER_PROFILE_CREATE = "Subscriber Profile Create";
    String SUBSCRIBER_PROFILE_DELETE = "Subscriber Profile Delete";
    String SUBSCRIBER_PROFILE_PAUSE_UNPAUSE = "Subscriber Profile Pause/Unpause (RPC)";
    String SUBSCRIBER_PROFILE_PAUSE_UNPAUSE_FOR_DURATION = "Subscriber Profile Pause/Unpause For Duration (RPC)";
    String SUBSCRIBER_PROFILE_UPDATE = "Subscriber Profile Update";
    //    String RGW_DISCONNECTED_LIST = "RGW Disconnected List";
//    String SUBSCRIBER_LIST = "Subscribers List";
    String SCHEDULE_CREATE = "Schedule Create";
    String SCHEDULE_UPDATE = "Schedule Update";
    String SCHEDULE_DELETE = "Schedule Delete";
    String SPEED_TEST_URL_CREATE = "Speed Test URL Create";
    String SPEED_TEST_URL_UPDATE = "Speed Test URL Update";
    String SPEED_TEST_URL_DELETE = "Speed Test URL Delete";
    String SUBSCRIBER_DELETE = "Subscriber Delete";
    String SUBSCRIBER_BULK_DELETE = "Subscriber Bulk Delete";
    String SUBSCRIBER_UPDATE = "Subscriber Update";
    String SUBSCRIBER_CREATE = "Subscriber Create";
    String EQUIPMENT_CREATE = "Equipment Create";
    String EQUIPMENT_UPDATE = "Equipment Update";
    String EQUIPMENT_DELETE = "Equipment Delete";
    String SUBSCRIBERS_FILE_UPLOAD = "Subscribers file upload";
    String CONFIG_PROPERTY_UPDATE = "Config Property Update";
    String SUBSCRIBER_FAILED_RECORDS = "Subscriber Failed Records";
    String SEARCH_EQUIPMENT_DETAILS = "Subscriber lookup";
    //    String USERS_LIST = "Users List";
//    String DATA_LOGS = "Data Logs";
    String INDEX_TTL_UPDATE = "Index TTL Update";
    String INDEX_TTL_DELETE = "Index TTL Delete";
    String DEVICE_VENDOR_DETAIL_UPDATE = "Device Vendor Update";
    String RGW_FRIENDLY_NAME = "RGW Friendly Name Update";
    String DEVICE_INTERNET_PAUSE_UNPAUSE = "Device Internet Pause/Unpause (RPC)";
    String OAUTH_CREATE = "Oauth Config Create";
    String OAUTH_UPDATE = "Oauth Config Update";
    String OAUTH_DELETE = "Oauth Config Delete";
    String OAUTH_MAPPING_CREATE = "Oauth Mapping Create";
    String PROCESS_SUBSCRIBERS_BULK_UPDATE = "Invoke Subscribers Bulk Updation";
    String SUBSCRIBERS_FILE_DELETE = "Subscribers file Delete";
    String UPDATE_DATA_OBSCURITY = "Data Obscurity Update";
    String UPDATE_SERVICE_CONFIG = "Service Configuration Update";
    String UPDATE_SERVICE_CONFIG_ENV = "Service Configuration Environment Update";
    String UPLOAD_LOGO = "Logo Upload";
    String SDA_ACTIONS = "SDA Actions Performed";
    String OAUTH2_CONFIG_EXCEPTION = "OAUTH2 CONFIG EXCEPTION";
    String OAUTH2_INVALID_CODE_EXCEPTION = "OAUTH2 INVALID CODE EXCEPTION";
    String OAUTH2_AUTHENTICATION_EXCEPTION = "OAUTH2 AUTHENTICATION EXCEPTION";
    String MAX_LOGIN_LIMIT = "Maximum Login Limit Reached";

    String POST_PROFILE = " Post Profile (frpc)" ;
    String PUT_PROTECTION = " Put Protection (frpc)" ;
    String PUT_WEBSITE_EXCEPTION = "Put Website Exception (frpc)";
    String PUT_DEVICE_BY_ID = "Put Device By Id (frpc)";
    String PUT_PROFILE_BY_ID = "Put Profile By Id (frpc)" ;
    String PUT_REGISTRATION = "Put Registration";
    String DELETE_WEBSITE_EXCEPTION = "Delete Website Exception" ;
    String DELETE_PROFILE_BY_ID = "Delete Profile By Id";


    // F-SECURE

    String PROFILE_CREATE = "Profile Create (FRPC)";
    String PROFILE_DELETE = "Profile Delete (FRPC)";
    String PROFILE_UPDATE = "Profile Update (FRPC)";
    String PROFILE_ACTION_CREATE = "Profile Action Create (FRPC)";
    String GET_ALL_PROFILES = "Get All Profiles (FRPC)";
    String GET_PROFILE_BY_ID = "Get Profile By ID (FRPC)";
    String PROTECTION_UPDATE = "Protection Update (FRPC)";
    String GET_PROTECTION_POLICIES = "Get Protection Policies (FRPC)";
    String WEBSITE_EXCEPTION_UPDATE = "Website Exception Update (FRPC)";
    String WEBSITE_EXCEPTION_DELETE = "Website Exception Delete (FRPC)";
    String GET_WEBSITE_EXCEPTIONS = "Get Website Exceptions (FRPC)";
    String DEVICE_UPDATE = "Device Update (FRPC)";
    String DEVICE_ACTION_CREATE = "Device Action Create (FRPC)";
    String GET_ALL_DEVICE = "Get All Devices (FRPC)";
    String GET_DEVICE_BY_ID = "Get Device By ID (FRPC)";
    String GET_ALL_EVENTS = "Get All Events (FRPC)";
    String GET_EVENTS_COUNTERS = "Get Event Counters (FRPC)";
    String EVENTS_DELETE = "Events Delete (FRPC)";
    String REGISTRATION_UPDATE = "Registration Update (FRPC)";
    String REGISTRATION_DELETE = "Registration Delete (FRPC)";
    String GET_REGISTRATION_KEY = "Get Registration Key (FRPC)";
    String TIMEZONE_UPDATE = "Timezone Update (FRPC)";
    String GET_TIMEZONE_DATA = "Get Timezone (FRPC)";
    String GET_FIRMWARE = "Get Firmware Info (RPC)";
    String POST_FIRMWARE = "POST Firmware Action (RPC)";
    String GET_FIRMWARE_POLICY = "Get Firmware Policy";
    String POST_FIRMWARE_POLICY = "Post Firmware Policy";
    String PUT_FIRMWARE_POLICY = "Put Firmware Policy";
    String DELETE_FIRMWARE_POLICY = "Delete Firmware Policy";
    String POST_FIRMWARE_POLICY_ACTION = "Post Firmware Policy Action";
    String GET_FIRMWARE_POLICY_LOGS = "Get Firmware Policy Logs";
    String GET_TRIBAND_INFO= "Get TriBand Info";
    String POST_TRIBAND = "POST TriBand Action (RPC)";
    String PUT_RADIO = "Put Radio Action (RPC)";
    String PUT_EQUIPMENT = "Put Equipment";

    String POST_EQUIPMENT = "POST Equipment (RPC)";
    String DELETE_EQUIPMENT = "POST Equipment (RPC)";
    String POST_EQUIPMENT_ACTION = "POST Equipment Action (RPC)";
    String REMOTEACESS_HTTP_GET = "RemoteAccess Http GET (RPC)";
    String REMOTEACESS_HTTP_PUT = "RemoteAccess Http PUT (RPC)";
    String REMOTEACESS_RTTY_GET = "RemoteAccess Rtty GET (RPC)";
    String REMOTEACESS_RTTY_PUT = "RemoteAccess Rtty PUT (RPC)";

    String POST_TRACKING_TRANSACTION = "POST Tracking Transaction";
    // Fsecure Provisioning

    String GET_FSECURE_STATUS = "GET Fsecure Status" ;
    String DELETE_FSECURE_STATUS = "Delete Fsecure STATUS";
    String SET_FSECURE_KEY = "Set Fsecure Key" ;

    String DATA_MODEL_ACCESS = "Data model access (RPC)";

    String RESET_PASSWORD = "Reset password";

    String NCS_NETWORK_CREATE = "NCS Network Create";




}
