package com.incs83.app.constants.misc;

public interface RpcConstants {

    String NETWORK_ACTION_URI = "/cpe-api/network/actions";
    String EQUIPMENT_ACTION_URI = "/cpe-api/equipment/actions";
    String DEVICE_ACTION_URI = "/cpe-api/devices/%s/actions";
    String DEVICE_INTERETACCESS_ACTION_URI = "/cpe-api/devices/%s/internetAccess/action";
    String DEVICE_SPEEDTEST_URI = "/cpe-api/diag/%s/phyRate";
    String RADIO_URI = "/cpe-api/radios/%s";
    String RADIO_ACTION_URI = "/cpe-api/radios/%s/actions";
    String SSID_ACTION_URI = "/cpe-api/radios/%s/ssids/%s";
    String STEERING_URI = "/cpe-api/steering";
    String STEERING_URLES_URI = "/cpe-api/steering/rules";
    String DIAG_ACTION_URI = "/cpe-api/diag/actions";
    String BURST_MODE_URI = "/cpe-api/diag/burstMode";
    String DM_ACCESS_URI = "/cpe-acs/dmaccess";
    String REMOTE_ACCESS_HTTP_URI = "/cpe-api/remoteAccess/http";
    String REMOTE_ACCESS_RTTY_URI = "/cpe-api/remoteAccess/rtty";
    String REMOTE_DEBUG_RTTY_URI = "/cpe-api/remoteDebug/rtty";

    String HOUSEHOLD_MGR_URI = "/cpe-api/equipment/onboarding";
    String ACTION_WAN_SPEED_TEST = "WANSpeedTest";
    String ACTION_KICK_WIFI_STA = "kickWiFiSta";
    String ACTION_NEIGHBOR_SCAN = "NeighborScan";
    String ACTION_WIFI_RESET = "Reset";
    String ACTION_DO_ACS = "DoACS";
    String ACTION_REBOOT = "reboot";

    String CPE_API_METHOD = "POST";
    String NCS_NETWORK_CPE_URI = "/cpe-api/ncs-cpe-api";
}
