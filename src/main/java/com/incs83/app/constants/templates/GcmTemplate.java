package com.incs83.app.constants.templates;

public interface GcmTemplate {

    String TEMPLATE_FOR_PUSH_WIFI_HEALTH_LOW = "{\n" +
            "  \"to\": \"%s\",\n" +
            "  \"notification\": {\n" +
            "    \"body\": \"-MESSAGE-\",\n" +
            "    \"sound\": \"default\",\n" +
            "    \"icon\": \"optim_logo\",\n" +
            "    \"color\": \"#1f316b\",\n" +
            "    \"title\": \"Warning !!\"\n" +
            "  },\n" +
            "  \"data\": {\n" +
            "    \"type\": \"respNeededForWifiHealth\",\n" +
            "    \"apId\": \"####\",\n" +
            "    \"text\": \"-MESSAGE-\",\n" +
            "    \"id\": \"-NOTIFICATION_ID-\"\n" +
            "  }\n" +
            "}";
    String OPTIMIZATION_IN_PROGRESS = "{\n" +
            "  \"to\": \"%s\",\n" +
            "  \"notification\": {\n" +
            "    \"body\": \"-MESSAGE-\",\n" +
            "    \"sound\": \"default\",\n" +
            "    \"icon\": \"optim_logo\",\n" +
            "    \"color\": \"#1f316b\",\n" +
            "    \"title\": \"In Progress...\"\n" +
            "  },\n" +
            "  \"data\": {\n" +
            "    \"type\": \"respNeededForWifiHealthInProgress\",\n" +
            "    \"text\": \"-MESSAGE-\",\n" +
            "    \"id\": \"-NOTIFICATION_ID-\"\n" +
            "  }\n" +
            "}";
    String OPTIMIZATION_SUCCESS = "{\n" +
            "  \"to\": \"%s\",\n" +
            "  \"notification\": {\n" +
            "    \"body\": \"-MESSAGE-\",\n" +
            "    \"sound\": \"default\",\n" +
            "    \"icon\": \"optim_logo\",\n" +
            "    \"color\": \"#1f316b\",\n" +
            "    \"title\": \"Success..\"\n" +
            "  },\n" +
            "  \"data\": {\n" +
            "    \"type\": \"respNeededForWifiHealthComplete\",\n" +
            "    \"text\": \"-MESSAGE-\",\n" +
            "    \"id\": \"-NOTIFICATION_ID-\"\n" +
            "  }\n" +
            "}";

}
