package com.incs83.app.constants.templates;


public interface MqttTemplate {

    String TOPIC_FOR_RPC_CALL = "CTRL/-USER_ID-/-S_ID-";
    String MESSAGE_FOR_RPC_30 = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"-METHOD-\",\"tid\": \"-TID-\",\"payload\": -PAYLOAD-}";
    String MESSAGE_FOR_RPC_31 = "{\"jsonrpc\": \"3.1\",\"isp\": \"-ISP-\",\"tid\": \"-TID-\" , \"request\": -REQUEST-}";

    String WAN_SPEED_TEST_TEMPLATE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"WANSpeedTest\", \"parameter\" : {\"TestServer\" :    \"-TEST_URL-\",    \"TID\"     : \"-TID-\" }}]}";
    String INTERNET_OFF_FOR_DEVICE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"SchedAccess\",\"parameter\": {\"Method\": \"Add\",\"StartTime\": \"0:0\",\"EndTime\": \"0:1\",\"Days\": \"Sun\",\"MACAddress\": \"-MAC_ADDRESS-\",\"Username\": \"-MAC_ADDRESS-_block\",\"TID\": \"-TID-\"}}]}";
    String INTERNET_ON_FOR_DEVICE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"SchedAccess\",\"parameter\": {\"Method\": \"Del\",\"StartTime\": \"0:0\",\"EndTime\": \"0:1\",\"Days\": \"Sun\",\"MACAddress\": \"-MAC_ADDRESS-\",\"Username\": \"-MAC_ADDRESS-_block\",\"TID\": \"-TID-\"}}]}";
    String FORCE_DISCONNECT_DEVICE_FOR_USER = "{  \"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{ \"object\": \"kickWiFiSTA\",\"parameter\": {\"StaMAC\": \"-MAC_ADDRESS-\", \"SsidIndex\": -RADIO-,        \"TID\": \"-TID-\" }}]}";
    String STA_RATE_FOR_DEVICE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"StaLinkRate\",\"parameter\": {\"StaMAC\": \"-MAC_ADDRESS-\",\"StaIP\": \"-IP_ADDR-\",\"StaBand\": \"-RADIO-\",\"TID\": \"-TID-\"}}]}";
    String REBOOT_AP_FOR_USER = "{\"jsonrpc\":\"2.0\",\"id\":-ID-,\"method\":\"set\",\"params\":[  {\"object\":\"Reboot\",\"parameter\":{\"TID\":\"-TID-\"}}]}";
    String NEIGHBOR_SCAN_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"NeighborScan\", \"parameter\" : {\"TID\"     : \"-TID-\" }}]}";

    String CHANGE_NETWORK_STATE = "{\"jsonrpc\": \"2.0\", \"id\": -ID-,  \"method\": \"set\",  \"params\": [  {   \"object\": \"setSSID\",  \"parameter\": {   \"TID\": \"-TID-\",  \"SsidIndex\": \"-TYPE-\", \"Enable\": \"-ENABLE-\"  }}]}";
    String MODIFY_NETWORK_CREDS = "{\"jsonrpc\": \"2.0\", \"id\": -ID-,  \"method\": \"set\",  \"params\": [  {   \"object\": \"setSSID\",  \"parameter\": {   \"TID\": \"-TID-\",  \"SsidIndex\": \"-TYPE-\", \"SSID\": \"-NEW_SSID-\", \"Password\": \"-NEW_PASSWORD-\"  }}]}";
    String ENABLE_DISABLE_SMART_STEERING = "{ \"jsonrpc\":\"2.0\", \"id\":-ID-, \"method\":\"set\", \"params\":[ { \"object\":\"SmartSteering\", \"parameter\":{ \"TID\":\"-TID-\", \"Enable\":\"-ENABLE-\" } } ] }";

    ////////////////v3 Template For Pause List////////////////
    String INTERNET_PAUSE_UN_PAUSE_FOR_DEVICE_FOR_USER_WITH_BOTH = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"PauseInternet\",\"parameter\": {\"TID\": \"-TID-\",\"Enable\": \"1\",\"RedirectionPage\": \"-REDIRECTION_PAGE-\",\"WhiteList\": \"-WHITE_LIST_PAGE-\",\"PauseList\": \"-PAUSE_LIST-\"}}]}";
    String INTERNET_PAUSE_UN_PAUSE_FOR_DEVICE_FOR_USER_WITH_REDIRECTION = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"PauseInternet\",\"parameter\": {\"TID\": \"-TID-\",\"Enable\": \"1\",\"RedirectionPage\": \"-REDIRECTION_PAGE-\",\"PauseList\": \"-PAUSE_LIST-\"}}]}";
    String INTERNET_PAUSE_UN_PAUSE_FOR_DEVICE_FOR_USER_WITH_WHITELIST = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"PauseInternet\",\"parameter\": {\"TID\": \"-TID-\",\"Enable\": \"1\",\"WhiteList\": \"-WHITE_LIST_PAGE-\",\"PauseList\": \"-PAUSE_LIST-\"}}]}";
    String INTERNET_PAUSE_UN_PAUSE_FOR_DEVICE_FOR_USER_WITHOUT_BOTH = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"PauseInternet\",\"parameter\": {\"TID\": \"-TID-\",\"Enable\": \"1\",\"PauseList\": \"-PAUSE_LIST-\"}}]}";

    String DIRECTED_HEAL_SELF_HEAL_TEMPLATE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"-OBJECT-\",\"parameter\" : {\"TID\"        : \"-TID-\",    \"RadioIdx\": -RADIO- }}]}";

    String ENABLE_DISABLE_DIAGNOSTICS_TEMPLATE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"-OBJECT-\",\"parameter\" : {\"TID\"        : \"-TID-\",    \"BurstDuration\": -DURATION- ,    \"CollectionInterval\": -COLL_INTERVAL- ,    \"ReportInterval\": -REP_INTERVAL- }}]}";

    //////////For Telus Demo /////////////////////////
    String TELUS_INTERNET_OFF_FOR_DEVICE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"SchedAccess\",\"parameter\": {\"Method\": \"Add\",\"StartTime\": \"0:0\",\"EndTime\": \"0:1\",\"Days\": \"Sun\",\"MACAddress\": \"-MAC_ADDRESS-\",\"Username\": \"-MAC_ADDRESS-_block\",\"TID\": \"-TID-\",\"Categories\": -Categories-}}]}";
    String TELUS_INTERNET_ON_FOR_DEVICE_FOR_USER = "{\"jsonrpc\": \"2.0\",\"id\": -ID-,\"method\": \"set\",\"params\": [{\"object\": \"SchedAccess\",\"parameter\": {\"Method\": \"Del\",\"StartTime\": \"0:0\",\"EndTime\": \"0:1\",\"Days\": \"Sun\",\"MACAddress\": \"-MAC_ADDRESS-\",\"Username\": \"-MAC_ADDRESS-_block\",\"TID\": \"-TID-\",\"Categories\": -Categories-}}]}";

    //F-secure
    String PROFILE = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"-METHOD-\",\"tid\": \"-TID-\",\"payload\": -PAYLOAD-}";
    String DELETE_PROFILE = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"DELETE\",\"tid\": \"-TID-\"}";
    String GET_PROFILE = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"GET\",\"tid\": \"-TID-\"}";
    String GET_CPE_SECURITY_PROTECTION = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-security/protection\",\"method\": \"GET\", \"tid\": \"-TID-\"}";
    String PUT_CPE_SECURITY_PROTECTION = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-security/protection\",\"method\": \"PUT\",\"tid\": \"-TID-\",\"payload\": -PAYLOAD-}";
    String GET_CPE_SECURITY_WEB_EXCEPTION = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-security/websiteException\",\"method\": \"GET\",\"tid\": \"-TID-\"}";
    String PUT_CPE_SECURITY_WEB_EXCEPTION = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-security/websiteException\",\"method\": \"PUT\",\"tid\": \"-TID-\",\"payload\": -PAYLOAD-}";
    String DELETE_CPE_SECURITY_WEB_EXCEPTION = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-security/websiteException\",\"method\": \"DELETE\",\"tid\": \"-TID-\"}";
    String GET_CPE_SECURITY_DEVICES = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"GET\",\"tid\": \"-TID-\"}";
    String UPDATE_CPE_SECURITY_DEVICES = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"-METHOD-\",\"tid\": \"-TID-\",\"payload\": -PAYLOAD-}";
    String PUT_CPE_SECURITY_REGISTRATION = "{\"jsonrpc\": \"3.1\",\"isp\": \"-ISP-\",\"tid\": \"-TID-\",\"request\": [{\"uri\": \"/cpe-security/registration\",\"method\": \"PUT\",\"payload\": -PAYLOAD-}]}";
    String CPE_SECURITY_REGISTRATION = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-security/registration\",\"method\": \"-METHOD-\",\"tid\": \"-TID-\"}";
    String GET_CPE_SECURITY_EVENTS = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"GET\",\"tid\": \"-TID-\"}";
    String DELETE_CPE_SECURITY_EVENTS = "{\"jsonrpc\":\"3.0\",\"uri\":\"-URI-\",\"method\": \"DELETE\",\"tid\": \"-TID-\"}";
    String CATEGORY_COUNTERS = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-security/events/categoryCounters\",\"method\": \"-METHOD-\",\"tid\": \"-TID-\"}";
    String PUT_CPE_RGW_TIME_ZONE = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-api/time\",\"method\": \"PUT\",\"tid\": \"-TID-\",\"payload\": -PAYLOAD-}";
    String GET_CPE_RGW_TIME_ZONE = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-api/time\",\"method\": \"GET\",\"tid\": \"-TID-\"}";
    String PUT_CPE_RADIOS = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"PUT\",\"tid\": \"-TID-\",\"payload\": -PAYLOAD-}";
    String GET_CPE_RADIOS = "{\"jsonrpc\": \"3.0\",\"uri\": \"-URI-\",\"method\": \"GET\",\"tid\": \"-TID-\"}";


    // ------------------------

    String fRPC_Registration = "{\"jsonrpc\": \"3.1\",\"isp\": \"-ISP-\",\"tid\": \"-TID-\" , \"request\": -REQUEST-}";

    String fRPC_PROFILE = "{\"uri\": \"-URI-\",\"method\": \"POST\",\"payload\": -PROFILE-}";
    String fRPC_DEVICE = "{\"uri\": \"-URI-\",\"method\": \"PUT\",\"payload\": -DEVICE-}";
    String fRPC_WEBSITE_EXCEPTION = "{\"uri\": \"/cpe-security/websiteException\",\"method\": \"PUT\",\"payload\": -WEBSITE_EXCEPTION-}";
    String fRPC_PROTECTION = "{\"uri\": \"/cpe-security/protection\",\"method\": \"PUT\",\"payload\": -PROTECTION-}";

    String SMM_RPC_CALL = "{\"jsonrpc\": \"3.1\",\"isp\": \"-ISP-\",\"tid\": \"-TID-\" , \"request\": -REQUEST-}";
    String SSM_RPC_REQUEST = "{\"uri\": \"-URI-\",\"method\": \"-METHOD-\",\"payload\": -PAYLOAD-}";

    // --------------------------------

    String GET_DATA_FOR_RADIO = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-api/radios/-RADIO_ID-\",\"method\": \"GET\",\"tid\": \"-TID-\"}";

    String PUT_DATA_FOR_RADIO = "{\"jsonrpc\": \"3.0\",\"uri\": \"/cpe-api/radios/-RADIO_ID-\",\"method\": \"PUT\",\"tid\": \"-TID-\" , \"payload\":{\"DFSEnable\" : -DFSENABLE-}  }";

}
