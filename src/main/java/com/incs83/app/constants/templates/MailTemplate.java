package com.incs83.app.constants.templates;

/**
 * <AUTHOR>
 * @Created 23/08/18
 */
public interface MailTemplate {
    String RESET_PASSWORD_MAIL = "Hi -USER-,\n" +
            "\n" +
            "You have recently requested to reset your Optim password.\n" +
            "\n" +
            "Please click on this -LINK- to reset your password.\n" +
            "\n" +
            "Note: This link is only valid for 15 minutes.\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String PASSWORD_CHANGE_NOTIFICATION_MAIL = "Hi -USER-,\n" +
            "\n" +
            "As per your request, we have successfully changed your Optim password.\n" +
            "\n" +
            "Please click on this -LINK- to login \n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String PASSWORD_CHANGE_NOTIFICATION_MAIL_FROM_ADMIN = "Hi -USER-,\n" +
            "\n" +
            "Your password has been changed by your System Admin. Please touch base with him/her to get your new password.\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String AUTO_GENERATED_PASSWORD = "Hello -USER-!!\n" +
            "\n" +
            "Your account has been created successfully\n" +
            "\n" +
            "Your one time password is -PASSWORD-. Please login to -LINK- and you will be asked to change your password once.\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String ALEXA_VOICE_SUBSCRIPTION_MAIL = "Hi -USER-,\n" +
            "\n" +
            "Thanks for Subscribing to Optim with Alexa. You will receive a mail from Amazon shortly confirming the same after which you will be able to use Alexa with Optim Skill\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String ALEXA_VOICE_UN_SUBSCRIPTION_MAIL = "Hi -USER-,\n" +
            "\n" +
            "Thanks for Subscribing to Optim with Alexa. You will receive a mail from Amazon shortly confirming the same after which you will be able to use Alexa with Optim Skill\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String GOOGLE_VOICE_SUBSCRIPTION_MAIL = "Hi -USER-,\n" +
            "\n" +
            "Thanks for Subscribing to Optim with Google Home. Please grant access to the actions API of google for your account by clicking+" +
            "<a href = ''>here</a>\n" +
            "Below are some screen shots that may guide you how to enable the Actions API of google.\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String GOOGLE_VOICE_UN_SUBSCRIPTION_MAIL = "Hi -USER-,\n" +
            "\n" +
            "Thanks for Subscribing to Optim with Google Home. Please grant access to the actions API of google for your account by clicking+" +
            "<a href = ''>here</a>\n" +
            "Below are some screen shots that may guide you how to enable the Actions API of google.\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String REPORT_GENERATED = "Hi User,\n" +
            "\n" +
            "Thanks for Subscribing to Optim with Alexa. You will receive a mail from Amazon shortly confirming the same after which you will be able to use Alexa with Optim Skill\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";


    String AUTONOMOUS_SELF_HEAL = "Hi -USER-,\n" +
            "\n" +
            "Thanks for Subscribing to Optim with Alexa. You will receive a mail from Amazon shortly confirming the same after which you will be able to use Alexa with Optim Skill\n" +
            "\n" +
            "Regards,\n" +
            "Team Actiontec";

    String CONTACT_US_REPLY = "Hi -USER-,\n" +
            "\n" +
            "Thanks for writing to us. We have received your query and we will get back to you shortly.\n" +
            "\n" +
            "Regards.\n";

    String CONTACT_US = "Hi Admin,\n\n" +
            "Please respond to the below query as per the details are given below.\n\n" +
            "-CONTENT-\n" +
            "\n" +
            "Query/Request By :-\n" +
            "Name : -NAME-\n" +
            "Phone no : -PHONENO-\n" +
            "email : -EMAIL-\n\n" +

            "Regards.\n";

}
