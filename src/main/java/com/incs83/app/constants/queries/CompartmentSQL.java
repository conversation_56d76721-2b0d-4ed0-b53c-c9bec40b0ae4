/**
 * <AUTHOR> <PERSON>uri
 * @Created 28-May-2017
 */
package com.incs83.app.constants.queries;
/**
 * <AUTHOR>
 */
public interface CompartmentSQL {

    String GET_COMPARTMENT_FOR_GROUP_ADMIN = "from Compartment where id = :id";
    String GET_COMPARTMENT_BY_NAME = "from Compartment where name = :name";
    String GET_COMPARTMENT_BY_ISPID = "from Compartment where ispId = :ispId";
    String GET_COMPARTMENT_FOR_SYS_ADMIN = "from Compartment";
    String GET_COMPARTMENT_ID_BY_ISPID_LIST = "SELECT id from Compartment where ispId in :ispIdList";
    String GET_ISP_ID_FROM_COMPARTMENT = "Select ispId from Compartment";
    String GET_USER_COUNT_BY_COMPARTMENT_ID="Select count(User_id) from user_compartment where compartment_id =:compartment_id";
    String GET_SUBSCRIBER_COUNT_BY_COMPARTMENT_ID="Select count(Subscriber_id) from subscriber_compartment where compartment_id =:compartment_id";
}
