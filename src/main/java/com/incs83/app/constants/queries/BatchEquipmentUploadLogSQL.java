package com.incs83.app.constants.queries;

public interface BatchEquipmentUploadLogSQL {
    String GET_ALL_UPLOAD_LOGS = "SELECT beul.id, beul.isp_id ispId, beul.file_id fileId, f.fileName, beul.`action`, beul.status, beul.createdAt, beul.createdBy, beul.updatedAt, beul.report_file_id reportFileId " +
                                 "FROM batch_equipment_upload_log beul " +
                                 "INNER JOIN file f on beul.file_id = f.id";
    String COUNT_ALL_UPLOAD_LOGS = "SELECT COUNT(*) FROM actiontec.batch_equipment_upload_log beul " +
                                   "INNER JOIN file f ON beul.file_id = f.id";
    String GET_BATCH_LOG_BY_ID = "FROM BatchEquipmentUploadLog WHERE id = :id";
    String GET_BATCH_LOG_BY_FILE_ID = "FROM BatchEquipmentUploadLog WHERE fileId = :fileId";
    String GET_BATCH_LOG_BY_ANY_FILE_ID = "FROM BatchEquipmentUploadLog WHERE fileId = :fileId or reportFileId = :reportFileId";
}
