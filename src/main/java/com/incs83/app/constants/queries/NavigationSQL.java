package com.incs83.app.constants.queries;

public interface NavigationSQL {

    String GET_NAVIGATION = "select m.position, m.id, m.parentId,m.navName, m.url,fontAwesomeIconClass, m.newTab, m.entities from menu m inner join menu_role mr on (mr.menu_id = m.id) where mr.role_id = :roleId order by 1 asc";
    String GET_MENU_FOR_ROLE = "select m.id, m.parentId, m.position from menu m inner join menu_role mr on (mr.menu_id = m.id) where mr.role_id = :roleId order by m.position asc";
    String GET_ALL_MENU = "select m.position,m.id, m.parentId,m.navName, m.url,fontAwesomeIconClass, m.newTab, m.entities from menu m order by 1 asc";
    String INSERT_ROLE_MAPPING = "insert into menu_role values (:menuId, :roleId)";
    String DELETE_ROLE_MENU_MAPPING = "delete from menu_role where role_id =:role_id";
    String GET_ALL_ENTITY_FOR_ROLE = "select re.entity from role_entity re where re.Role_id = :roleId";
    String DELETE_ALL_ROLE_ENTITY_MAPPING = "delete from role_entity where Role_id =:role_id";
    String UPDATE_MENU = "Update MenuItems set fontAwesomeIconClass=:fontAwesomeIconClass, navName=:navName, newTab=:newTab, position=:position, parentId = :parentId, url=:url where id=:menu_id";
}
