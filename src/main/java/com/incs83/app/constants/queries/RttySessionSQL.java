package com.incs83.app.constants.queries;

public interface RttySessionSQL {
    String GET_RTTY_SESSIONS_BY_CREATOR_NAME = "SELECT rs.id, rs.equipment_id, ne.serial, ne.isp_id, rs.created_by, u.fullName, " +
            "rs.created_at, rs.expired_at " +
            "FROM actiontec.rtty_session rs " +
            "JOIN actiontec.ncs_equipment ne ON rs.equipment_id = ne.id " +
            "JOIN actiontec.user u ON rs.created_by = u.id " +
            "WHERE MATCH(u.fullName) AGAINST (CONCAT(:creatorName, '*') IN BOOLEAN MODE) " +
            "AND rs.status = 'ACTIVE' " +
            "AND rs.expired_at > NOW()";
    String RTTY_SESSIONS_BY_CREATOR_NAME_COUNT = "SELECT count(rs.id) " +
            "FROM actiontec.rtty_session rs " +
            "JOIN actiontec.ncs_equipment ne ON rs.equipment_id = ne.id " +
            "JOIN actiontec.user u ON rs.created_by = u.id " +
            "WHERE MATCH(u.fullName) AGAINST (CONCAT(:creatorName, '*') IN BOOLEAN MODE) " +
            "AND rs.status = 'ACTIVE' " +
            "AND rs.expired_at > NOW()";
    String GET_RTTY_SESSIONS_BY_SERIAL = "SELECT rs.id, rs.equipment_id, ne.serial, ne.isp_id, rs.created_by, u.fullName, " +
            "rs.created_at, rs.expired_at " +
            "FROM actiontec.rtty_session rs " +
            "JOIN actiontec.ncs_equipment ne ON rs.equipment_id = ne.id " +
            "JOIN actiontec.user u ON rs.created_by = u.id " +
            "WHERE ne.serial = :serial " +
            "AND rs.status = 'ACTIVE' " +
            "AND rs.expired_at > NOW()";
    String RTTY_SESSIONS_BY_SERIAL_COUNT = "SELECT count(rs.id) " +
            "FROM actiontec.rtty_session rs " +
            "JOIN actiontec.ncs_equipment ne ON rs.equipment_id = ne.id " +
            "WHERE ne.serial = :serial " +
            "AND rs.status = 'ACTIVE' " +
            "AND rs.expired_at > NOW()";
    String GET_SESSION_BY_EQUIPMENT_ID = "from RttySession where equipmentId =:equipmentId";
    String ACTIVE_RTTY_SESSIONS_COUNT = "SELECT COUNT(rs.id) " +
            "FROM actiontec.rtty_session rs " +
            "WHERE rs.status = 'ACTIVE' " +
            "AND rs.expired_at > NOW()";
    String GET_RTTY_SESSIONS_BY_CREATED_BY = "SELECT rs.id, rs.equipment_id, ne.serial, ne.isp_id, rs.created_by, u.fullName, " +
            "rs.created_at, rs.expired_at " +
            "FROM actiontec.rtty_session rs " +
            "JOIN actiontec.ncs_equipment ne ON rs.equipment_id = ne.id " +
            "JOIN actiontec.user u ON rs.created_by = u.id " +
            "WHERE rs.created_by = :createdBy " +
            "AND rs.status = 'ACTIVE' " +
            "AND rs.expired_at > NOW()";
    String RTTY_SESSIONS_BY_CREATED_BY_COUNT = "SELECT count(rs.id) " +
            "FROM actiontec.rtty_session rs " +
            "WHERE rs.created_by = :createdBy " +
            "AND rs.status = 'ACTIVE' " +
            "AND rs.expired_at > NOW()";;
}

