package com.incs83.app.constants.queries;

public interface EquipmentSQL {
    String GET_EQUIPMENT_LIST_BY_RGW_SERIAL = "from Equipment where rgwSerial =:rgwSerial";
    String GET_EQUIPMENT_LIST_BY_EQUIPMENT_ID_OR_RGW_SERIAL_OR_STN = "from Equipment where id =:id or rgwSerial =:id or serviceTelephoneNo =:id order by createdAt DESC";
    String GET_EQUIPMENT_LIST_BY_RGW_MAC = "from Equipment where rgwMAC =:rgwMAC";
    String GET_EQUIPMENT_COUNT_BY_GROUP = "select count(*) from Equipment ###";
    String GET_EQUIPMENT_COUNT_HQL = "SELECT count(*) from Equipment";
    String GET_EQUIPMENT_BY_STN = "from Equipment where serviceTelephoneNo =:serviceTelephoneNo";
    String GET_EQUIPMENT_BY_MACADDRESS= "from Equipment where rgwMAC =:rgwMAC and rgwSerial <> :requestedRgwSerial ";
    String GET_EQUIPMENT_FOR_SUBSCRIBER = "from Equipment where subscriberId=:subscriberId";
    String GET_EQUIPMENT_COUNT_BY_CLUSTER="SELECT count(cluster.equipment_id) FROM cluster_equipment cluster inner join equipment eq ON(cluster.equipment_id=eq.id) ";
    String GET_EQUIPMENT_COUNT_SQL="SELECT count(id) FROM equipment eq ";
    String REMOVE_SUBSCRIBER_ID_FROM_EQUIPMENT = "UPDATE Equipment set subscriberId = null , serviceTelephoneNo = :serviceTelephoneNo where id=:equipmentId";
    String UPDATE_SUBSCRIBER_ID_FOR_EQUIPMENT = "UPDATE Equipment set subscriberId = :subscriberId where id=:equipmentId";
    String GET_EQUIPMENT_BY_CLUSTER = "select eq.id,eq.rgwSerial,eq.rgwMAC,eq.serviceTelephoneNo,eq.upLinkRate,eq.downLinkRate,subs.firstName,subs.lastName,subs.camsAccountNo,subs.globalAccountNo,subs.email from equipment eq left join subscriber subs on (subs.id=eq.subscriberId) left join cluster_equipment cluster on (eq.id=cluster.equipment_id) ";
    String GET_EQUIPMENT_LIST = "select eq.id,eq.rgwSerial,eq.rgwMAC,eq.serviceTelephoneNo,eq.upLinkRate,eq.downLinkRate,subs.firstName,subs.lastName,subs.camsAccountNo,subs.globalAccountNo,subs.email from equipment eq left join subscriber subs on (subs.id=eq.subscriberId) ";
    String GET_EQUIPMENT_BY_RGW_SERIAL_AND_SUBSCRIBER = "from Equipment WHERE rgwSerial =:rgwSerial AND subscriberId =:subscriberId";
    String GET_EQUIPMENT_BY_EQUIPMENT_ID_AND_SUBSCRIBER = "from Equipment WHERE id =:id AND subscriberId =:subscriberId";
    String GET_DETACHED_EQUIPMENT_BY_CLUSTER = "select eq.id,eq.rgwSerial,eq.rgwMAC,eq.serviceTelephoneNo from equipment eq left join cluster_equipment cluster on (eq.id=cluster.equipment_id) where eq.subscriberId is null ";
    String GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER="SELECT count(equipment_id) FROM cluster_equipment cluster inner join equipment eq ON(cluster.equipment_id=eq.id) where eq.subscriberId is null ";
    String GET_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER = "select eq.id,eq.rgwSerial,eq.rgwMAC,eq.serviceTelephoneNo from equipment eq where subscriberId = :subscriberId or subscriberId is  null ";
    String COUNT_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER = "select count(id) from equipment eq where subscriberId = :subscriberId or subscriberId is  null ";

    String GET_EQUIPMENT_COUNT_BY_COMPARTMENT = "SELECT count(*) from Equipment where groupId=:groupId";
    String GET_EQUIPMENT_COUNT_BY_CLUSTER_ID = "SELECT count(equipment_id) FROM cluster_equipment Where cluster_id =:cluster_id";
    String MAPPING_OF_CLUSTER_AND_AP = "INSERT INTO cluster_equipment values (:clusterId, :equipmentId)";
    String DELETE_EQUIPMENT_FROM_CLUSTER = "DELETE FROM cluster_equipment WHERE equipment_id =:equipment_id";
    String UPDATE_SN_MAC_OF_EQUIPMENT = "UPDATE Equipment set rgwSerial = NULL , rgwMAC = NULL where id = :equipmentId";
    String GET_EQUIPMENT_COUNT = "SELECT count(*) from equipment";
    String GET_SMMSERVICE_COUNT_BY_NAME = "SELECT count(id) FROM service where name =:name";
    String GET_SMMAPP_LIST_BY_VERSION = "from SmmApp where serviceId =:serviceId AND version =:version";
    String GET_SMMAPP_LIST_BY_SERVICE_ID = "from SmmApp where serviceId =:serviceId";


    String GET_EQUIPMENT_FOR_ELASTIC = "select eq.id,eq.rgwSerial,eq.rgwMAC,eq.serviceTelephoneNo,eq.upLinkRate,eq.downLinkRate,eq.subscriberId,eq.groupId,eq.createdAt,eq.createdBy,subs.firstName,subs.lastName,subs.camsAccountNo,subs.globalAccountNo,subs.email from equipment eq left join subscriber subs on (subs.id=eq.subscriberId) %s %s";
}
