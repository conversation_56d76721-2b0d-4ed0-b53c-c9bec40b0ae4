package com.incs83.app.constants.queries;

public interface NCSNetworkSQL {
    String NETWORK_COUNT = "SELECT COUNT(DISTINCT n.id) " +
                           "FROM network n " +
                           "LEFT JOIN subscriber_network sn ON n.id = sn.network_id";

    String GET_NETWORKS = "SELECT DISTINCT(n.id), " +
                          "CASE WHEN n.gen_type = 'auto' THEN 'TRUE' ELSE 'FALSE' END autoFlag, " +
                          "n.auto_network_id autoNetworkId, " +
                          "n.name, n.isp_id ispId, sn.subscriber_id subscriberId, n.updatedAt " +
                          "FROM network n " +
                          "LEFT JOIN subscriber_network sn on n.id = sn.network_id";

    String GET_NETWORK_BY_ID = "SELECT n.id, " +
                               "CASE WHEN n.gen_type = 'auto' THEN 'TRUE' ELSE 'FALSE' END autoFlag, " +
                               "n.auto_network_id autoNetworkId, " +
                               "n.name, n.isp_id ispId, sn.subscriber_id subscriberId, n.updatedAt " +
                               "FROM network n " +
                               "LEFT JOIN subscriber_network sn on n.id = sn.network_id " +
                               "WHERE n.id = :id";
    String NETWORK_BY_KEYBOARD_COUNT = "SELECT COUNT(DISTINCT n.id) " +
                                       "FROM network n " +
                                       "JOIN ncs_equipment ne ON ne.network_id = n.id " +
                                       "LEFT JOIN subscriber s ON ne.subscriber_id = s.id " +
                                       "WHERE n.id IN ( " +
                                           "SELECT DISTINCT ne.network_id " +
                                           "FROM ncs_equipment ne " +
                                           "WHERE ne.serial LIKE CONCAT(:keyword, '%') " +
                                           "UNION ALL " +
                                           "SELECT DISTINCT ne.network_id " +
                                           "FROM ncs_equipment ne " +
                                           "LEFT JOIN subscriber s ON ne.subscriber_id = s.id " +
                                           "WHERE MATCH(s.fullName) AGAINST (CONCAT(:keyword, '*') IN BOOLEAN MODE) " +
                                       ") ";

    String GET_NETWORK_BY_KEYWORD = "SELECT " +
            "n.id, " +
            "n.name, " +
            "s.id subscriberId, " +
            "s.firstName, " +
            "s.lastName, " +
            "GROUP_CONCAT(DISTINCT ne.serial ORDER BY ne.serial ASC SEPARATOR ',') equipmentSerialList " +
            "FROM network n " +
            "JOIN ncs_equipment ne ON ne.network_id = n.id " +
            "LEFT JOIN subscriber s ON ne.subscriber_id = s.id " +
            "WHERE n.id IN ( " +
                "SELECT DISTINCT ne.network_id " +
                "FROM ncs_equipment ne " +
                "WHERE ne.serial LIKE CONCAT(:keyword, '%') " +
                "UNION ALL " +
                "SELECT DISTINCT ne.network_id " +
                "FROM ncs_equipment ne " +
                "LEFT JOIN subscriber s ON ne.subscriber_id = s.id " +
                "WHERE MATCH(s.fullName) AGAINST (CONCAT(:keyword, '*') IN BOOLEAN MODE) " +
            ") " +
            "GROUP BY n.id, s.id " +
            "ORDER BY n.createdAt DESC ";
}
