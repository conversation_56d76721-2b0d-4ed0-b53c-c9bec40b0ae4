package com.incs83.app.constants.queries;

public interface AuditLogsSQL {

    String GET_AUDIT_LOGS_FOR_USER = "select u.firstName,u.lastName, u.email, al.method,al.url,al.time,al.ip, al.operation,al.payload,al.apId,al.responseCode,al.responseMessage from user u inner join `auditLogs` al on(al.loggedInUser=u.id) %s";
    String AUDIT_LOGS_COUNT_FOR_USER = "select COUNT(*) from `auditLogs` al inner join `user` u on(al.loggedInUser=u.id) %s";


    String GET_AUDIT_LOGS_FOR_SUBSCRIBER = "select s.firstName,s.lastName, s.email, al.method,al.url,al.time,al.ip, al.operation,al.payload,al.apId,al.responseCode,al.responseMessage from subscriber s inner join `auditLogs` al on(al.loggedInUser=s.id) %s";
    String AUDIT_LOGS_COUNT_FOR_SUSBCRIBER = "select COUNT(*) from `auditLogs` al inner join `subscriber` s on(al.loggedInUser=s.id) %s";


    String AUDIT_LOGS_DATA_FOR_USER_SUBSCRIBER = "( select  u.firstName,u.lastName, u.email, al.method,al.url,al.time,al.ip, al.operation,al.payload,al.apId,al.responseCode,al.responseMessage from user u inner join `auditLogs` al on(al.loggedInUser=u.id) %s )  union (  select s.firstName,s.lastName, s.email, al.method,al.url,al.time,al.ip, al.operation,al.payload,al.apId,al.responseCode,al.responseMessage from subscriber s inner join `auditLogs` al on(al.loggedInUser=s.id)  %s  ) order by time desc";

    String AUDIT_LOGS_COUNT_FOR_USER_SUBSCRIBER = "select count(*) from ( (select al.id  from auditLogs al inner join user u on al.loggedInUser = u.id  %s )    union  ( select al.id from subscriber s inner join auditLogs al on s.id = al.loggedInUser %s  )     ) user_subscriber ";


    String  AUDIT_LOGS_DATA_FOR_USER_SUBSCRIBER_NEW = "select  ifnull(u.firstName,s.firstName) as firstName ,ifnull(u.lastName,s.lastName) as lastName  , ifnull( u.email,s.email) as email , al.method,al.url,al.time,al.ip, al.operation,al.payload,al.apId,al.responseCode,al.responseMessage from auditLogs al left join user u on al.loggedInUser = u.id  left join subscriber s on  al.loggedInUser = s.id   where   ifnull(u.id,s.id)  is not null %s "  ;

    String AUDIT_LOGS_COUNT_FOR_USER_SUBSCRIBER_NEW = "select  count(*) from auditLogs al left join user u on al.loggedInUser = u.id  left join subscriber s on  al.loggedInUser = s.id   where   ifnull(u.id,s.id)  is not null %s " ;


//    String GET_USER_BY_USER_ID = "select u.firstName,u.lastName, u.email from user u where u.id = :userOrSubscriberId";
//
//    String GET_SUBSCRIBER_BY_SUBSCRIBER_ID = "select s.firstName,s.lastName, s.email from subscriber s where s.id = :userOrSubscriberId";
//

//
//    String GET_AUDIT_LOGS_FOR_USER_BY_AUDIT_LOG_ID ="select u.firstName,u.lastName, u.email, al.method,al.url,al.time,al.ip, al.operation,al.payload,al.apId,al.responseCode from user u inner join `auditLogs` al on(al.loggedInUser=u.id) where al.id = :auditLogId   %s" ;
//
//
//    String GET_AUDIT_LOGS_FOR_SUBSCRIBER_BY_AUDIT_LOG_ID ="select s.firstName,s.lastName, s.email, al.method,al.url,al.time,al.ip, al.operation,al.payload,al.apId,al.responseCode from subscriber s inner join `auditLogs` al on(al.loggedInUser=s.id)  where al.id = :auditLogId   %s ";
}
