/**
 * <AUTHOR> <PERSON>
 * @Created 28-May-2017
 */
package com.incs83.app.constants.queries;

public interface SubscriberDemographicBulkInfoSQL {

    String GET_ALL = "from SubscriberDemographicInfo";
    String GET_COUNT = "Select count(*) from SubscriberDemographicInfo";
    String GET_ALL_BY_STATUS = "from SubscriberDemographicInfo where status = :status";
    String GET_COUNT_BY_STATUS = "Select count(*) from SubscriberDemographicInfo where status = :status";
    String GET_USER_AP_BY_AP_ID = "from UserAP where apId =:apId and rgwMAC=:rgwMAC";
    String GET_USER_AP_BY_AP_ID_GROUP_ID = "from UserAP where apId =:apId and rgwMAC=:rgwMAC and groupId=:groupId";
}
