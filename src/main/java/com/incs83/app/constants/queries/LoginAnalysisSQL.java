package com.incs83.app.constants.queries;

public interface LoginAnalysisSQL {

    String GET_LOGIN_ANALYSIS_BY_OPERATION = "select count(*) from AuditLogs where operation =:operation";
    //    String GET_LOGIN_ANALYSIS_BY_OPERATION_FAIL_OAUTH = "select count(*) from AuditLogs where operation =:op1 OR operation =:op2 OR operation =:op3";

    String GET_LOGIN_USER_INFO =  "select u.role ,u.passwordAttempt passwordAttempt, datediff(CURRENT_TIMESTAMP, u.passwordUpdatedAt) passwordAge , ld.updatedAt lastLogin, concat( u.firstName,' ',u.lastName ) name, u.ip , u.userId from   ( select  id userId, email , passwordAttempt, passwordUpdatedAt,firstName,lastName,r.name role,ip from user usr  inner join  (select role_id,user_id  from user_role)  ur on  usr.id = ur.user_id  inner join  (select id roleId,name from role) r  on  r.roleId = ur.role_id ) u left join   ( select email,updatedAt from login_details ) ld  on  u.email = ld.email " ;

    String GET_COUNT_LOGIN_USER_INFO = " select  count(u.id) from user u , user_role ur , role r  where  u.id = ur.user_id  and  r.id = ur.role_id  ";

}
