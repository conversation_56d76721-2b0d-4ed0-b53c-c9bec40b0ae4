package com.incs83.app.constants.queries;

import java.util.Hashtable;

public interface TokenSQL {
    String GET_TOTAL_LOGGED_IN_USER="SELECT count(id) from login_details login inner join user_compartment uc ON (login.id=uc.User_id) WHERE token IS NOT NULL AND tokenUpdatedAt > DATE_SUB(NOW(), INTERVAL :tokenExpiration MINUTE) ###";
    String GET_TOTAL_LOGGED_IN_USER_SUBSCRIBER = "SELECT count(*) from login_details login  WHERE token IS NOT NULL AND tokenUpdatedAt > DATE_SUB(NOW(), INTERVAL :tokenExpiration MINUTE) ";
    String GET_ACTIVE_TOKEN_FOR_EXTERNAL_AND_MOBILE_USER = "SELECT count(distinct id) from login_details where token IS NOT NULL and updatedAt > DATE_SUB(NOW(), INTERVAL :tokenExpiration MINUTE) and tokenType != 'INTERNAL'";
}
