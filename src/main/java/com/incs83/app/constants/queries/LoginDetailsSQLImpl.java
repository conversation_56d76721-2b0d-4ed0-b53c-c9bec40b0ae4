package com.incs83.app.constants.queries;

import com.incs83.queries.LoginDetailsSQL;

public interface LoginDetailsSQLImpl extends LoginDetailsSQL {
    String GET_LOGIN_COUNT = "SELECT count(id) FROM LoginDetails WHERE token IS NOT null";
    String GET_LOGIN_DETAILS = "FROM LoginDetails WHERE token IS NOT null";
    String DELETE_LOGIN_DETAILS_BY_EMAIL = "delete from login_details where email =:email";
    String DELETE_LOGIN_DETAILS_BY_SUBSCRIBER_ID = "delete from login_details where id =:subscriberId";
}
