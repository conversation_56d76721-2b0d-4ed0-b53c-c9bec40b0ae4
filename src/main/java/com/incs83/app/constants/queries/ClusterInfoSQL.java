package com.incs83.app.constants.queries;

public interface ClusterInfoSQL {
    String GET_COUNT_USER_AP_BY_CLUSTER_ID = "SELECT count(userAp_userId) FROM cluster_userAp WHERE cluster_id =:clusterId";
    String GET_ALL_USER_AP_ID_BY_CLUSTER_ID = "SELECT eq.rgwSerial FROM cluster_equipment c inner join equipment eq on(c.equipment_id=eq.id) where c.cluster_id=:clusterId";
    String MAPPING_OF_CLUSTER_AND_AP = "INSERT INTO cluster_equipment values (:clusterId, :userId)";
    String MAPPING_OF_CLUSTER_AND_EQUIPMENT = "INSERT INTO cluster_equipment values (:clusterId, :equipmentId)";
    String DELETE_EQUIPMENT_FROM_CLUSTER_BY_EQUIPMENT_ID = "DELETE FROM cluster_equipment WHERE equipment_id=:equipmentId";

    String DELETE_ALL_USER_AP_BY_CLUSTER_ID = "DELETE FROM cluster_equipment WHERE cluster_id=:clusterId";

    String DELETE_USER_AP_FROM_CLUSTER = "DELETE FROM cluster_equipment WHERE cluster_id=:clusterId AND equipment_id=:userId";
    String DELETE_USER_AP_FROM_CLUSTER_BY_USER_ID = "DELETE FROM cluster_equipment WHERE equipment_id=:userId";
    String GET_GROUP_ID_BY_CLUSTER_ID = "SELECT compartment_id FROM cluster_info WHERE id =:clusterId";
    String GET_CLUSTER_INFO_BY_NAME = "FROM ClusterInfo WHERE name=:name";

    String GET_PAGINATED_USER_AP_ID_BY_CLUSTER_ID = "SELECT u.apId,usr.email,usr.firstName,usr.lastName,u.rgwMac,u.userId,u.globalAccountNo,u.camsAccountNo FROM cluster_userAp c inner join userAp u on(c.userAp_userId=u.userId) inner join user usr on(usr.id=u.userId) WHERE c.cluster_id=:clusterId %s";
    String GET_USER_AP_BY_CLUSTER_ID_AND_USER_ID = "SELECT userAp_userId FROM cluster_userAp WHERE cluster_id=:clusterId AND userAp_userId=:userId";
    String GET_USER_ID_BY_CLUSTER_ID = "SELECT userAp_userId FROM cluster_userAp WHERE cluster_id=:clusterId";

    String GET_EQUIPMENT_BY_CLUSTER_AND_RGW_SERIAL = "SELECT count(equipment_id) from cluster_equipment where cluster_id=:cluster_id && equipment_id=:equipment_id";
    String GET_EQUIPMENT_COUNT_BY_CLUSTER_ID = "SELECT count(equipment_id) from cluster_equipment where cluster_id=:clusterId";

    String DELETE_CLUSTER_BY_GROUP_ID = "DELETE FROM cluster_info WHERE compartment_id=:compartment_id";
}
