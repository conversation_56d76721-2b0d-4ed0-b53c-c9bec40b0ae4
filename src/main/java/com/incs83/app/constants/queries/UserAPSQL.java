/**
 * <AUTHOR> <PERSON>
 * @Created 28-May-2017
 */
package com.incs83.app.constants.queries;

@Deprecated
public interface UserAPSQL {

    String GET_PLATFORM_USER_ID_FROM_AP_ID = "from UserAP where apId =:apId";
    String DELETE_USER_AP = "delete from userAp where userId =:userId";
//    String UPDATE_USER_AP_GROUP = "update userAp set groupId =:groupId where userId =:userId";
    String GET_AP_ID_FROM_PLATFORM_USER_ID = "from UserAP where userId =:userId";
    String GET_USER_AP_FROM_PLATFORM_USER_ID_OR_AP_ID = "from UserAP where userId =:userId OR apId=:apId";
    String GET_AP_ID_FOR_USER_CSV = "from UserAP where userId in (###)";
    String UPDATE_AP_ID_FOR_USER_CSV = "update userAp set apId =:apId, mobilSubscribed =:isMobilSubscribed, alexaVoiceSubscribed =:isAlexaVoiceSubscribed, googleHomeVoiceSubscribed =:isGoogleVoiceSubscribed, isStatic =:isStatic, downLinkRate=:subscriberBandwidth where userId =:userId";
    String DELETE_AP_ID_FOR_USER_CSV = "delete from userAp where userId =:userId";
    String CHECK_AP_ID_AND_USER_MAPPING = "from UserAP where userId =:userId and apId =:apId";

    String GET_PAGINATED_USER_AP_BY_COMPARTMENT = "SELECT apId from UserAP ###";
//    String GET_PAGINATED_USER_AP = "from UserAP";
    String COUNT_USER_AP = "SELECT count(*) from userAp";

    String COUNT_TOTAL_SUBSCRIBER  = "SELECT count(*) from userAp ###";


    String GET_USER_AP_BY_ID = "from UserAP where userId in (:userIds)";

    String SEARCH_USER_BY_COMPARTMENT_AND_RGW_SERIAL_OR_PHONE_0R_ACCOUNT = "from UserAP ###";
    String SEARCH_USER_BY_RGW_SERIAL_OR_PHONE_OR_ACCOUNT = "from UserAP ###";


    String GET_COUNT_AP_BY_GROUP = "select count(*) from UserAP ###";
    String GET_COUNT_AP_BY_ALL = "select count(*) from UserAP ###";

    String GET_USER_AP_BY_ACCOUNT_NUMBER = "from UserAP where globalAccountNo =:globalAccountNo"  ;
    String GET_USER_AP_BY_CAMS_ACCOUNT_NUMBER = "from UserAP where camsAccountNo =:camsAccountNo" ;
    String GET_USER_AP_BY_PHONE_NUMBER = "from UserAP where phoneNo =:phoneNo";
    String GET_SUBSCRIBER_COUNT = "SELECT count(*) from user u inner join userAp ua on(u.id=ua.userId)";
    String GET_SUBSCRIBER_COUNT_BY_COMPARTMENT = "select count(*) from user u inner join user_compartment uc on (uc.user_id = u.id) inner join compartment c on (c.id=uc.compartment_id) inner join userAp ua on (ua.userId = u.id) where uc.compartment_id =:id";

    String GET_SUBSCRIBERS = "select u.firstName, u.lastName, u.email as email, ua.alexaVoiceSubscribed as alexaVoiceSubscribed, ua.googleHomeVoiceSubscribed as googleHomeVoiceSubscribed, ua.mobileSubscribed as mobileSubscribed, ua.downLinkRate as downLinkRate, ua.staticRGW as staticRGW,ua.phoneNo as phoneNo ,ua.globalAccountNo as globalAccountNo, u.alexaEmail as alexaEmail,u.googleHomeEmail as googleHomeEmail, ua.rgwMAC as rgwMAC, u.id as subscriberId, ua.apId as serialNumber, ua.camsAccountNo as camsAccountNo, ua.upLinkRate from user u inner join user_compartment uc on (uc.user_id = u.id) inner join compartment c on (c.id=uc.compartment_id %s) inner join userAp ua on (ua.userId = u.id and ua.groupId = uc.compartment_id) %s";
    String GET_SUBSCRIBERS_BY_CLUSTER = " select u.firstName, u.lastName, u.email as email, ua.alexaVoiceSubscribed as alexaVoiceSubscribed, ua.googleHomeVoiceSubscribed as googleHomeVoiceSubscribed, ua.mobileSubscribed as mobileSubscribed, ua.downLinkRate as downLinkRate, ua.staticRGW as staticRGW,ua.phoneNo as phoneNo ,ua.globalAccountNo as globalAccountNo, u.alexaEmail as alexaEmail,u.googleHomeEmail as googleHomeEmail, ua.rgwMAC as rgwMAC, u.id as subscriberId, ua.apId as serialNumber, ua.camsAccountNo as camsAccountNo, ua.upLinkRate FROM cluster_userAp c inner join userAp ua on(c.userAp_userId=ua.userId) inner join user u on(u.id=ua.userId) %s";
    String GET_COUNT_OF_SEARCH_SUBSCRIBERS = "select count(*) from user u inner join user_compartment uc on (uc.user_id = u.id) inner join compartment c on (c.id=uc.compartment_id %s) inner join userAp ua on (ua.userId = u.id) %s";

    String GET_USER_AP_FROM_RGW_MAC = "from UserAP where rgwMAC =:rgwMAC";

    String GET_AP_COUNT_GROUP_BY_DATE = "SELECT count(id) as total ,DATE(createdAt) as date FROM equipment WHERE createdAt >= (NOW() - INTERVAL 1 MONTH) GROUP BY DATE(createdAt)";
    String GET_AP_COUNT_GROUP_BY_DATE_BY_COMPARTMENT = "SELECT count(id) as total,DATE(createdAt) as date FROM equipment WHERE groupId = %s AND createdAt >= (NOW() - INTERVAL 1 MONTH) GROUP BY DATE(createdAt)";

    String GET_SUBSCRIBERS_FOR_ELASTIC = "select u.firstName, u.lastName, u.createdAt, u.createdBy , u.email, u.alexaEmail, ua.googleHomeVoiceSubscribed, ua.alexaVoiceSubscribed," +
            "u.id as subscriberId, ua.mobileSubscribed, ua.downLinkRate, ua.staticRGW, ua.rgwMAC,  ua.apId as serialNumber, uc.compartment_id as groupId, ua.phoneNo, ua.camsAccountNo, u.googleHomeEmail,ua.globalAccountNo, ua.upLinkRate from user u inner join user_compartment uc on (uc.user_id = u.id) inner join compartment c on (c.id=uc.compartment_id) inner join userAp ua on (ua.userId = u.id and ua.groupId = uc.compartment_id) %s %s";
}
