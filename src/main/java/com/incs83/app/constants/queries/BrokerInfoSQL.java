package com.incs83.app.constants.queries;

public interface BrokerInfoSQL {
    String BROKER_COUNT = "SELECT COUNT(DISTINCT bi.id) " +
                         "FROM broker_info bi ";

    String GET_BROKER_INFOS = "SELECT DISTINCT bi.id, " +
                             "bi.env_name envName, " +
                             "bi.broker_url brokerUrl, " +
                             "bi.port port, " +
                             "bi.username " +
                             "FROM broker_info bi ";

    String GET_BROKER_INFO_BY_ID = "SELECT bi.id, " +
                             "bi.env_name envName, " +
                             "bi.broker_url brokerUrl, " +
                             "bi.port port, " +
                             "bi.username " +
                             "FROM broker_info bi " +
                             "WHERE bi.id = :id";

    String CHECK_ENV_NAME_EXISTS = "SELECT COUNT(1) " +
                                 "FROM broker_info bi " +
                                 "WHERE bi.env_name = :envName";

    String CLEAR_DEFAULT_STATUS = "UPDATE broker_info " +
                                "SET is_default = false " +
                                "WHERE is_default = true";

    String GET_DEFAULT_BROKER_INFO_ID = "SELECT bi.id FROM broker_info bi WHERE bi.is_default = true";
}
