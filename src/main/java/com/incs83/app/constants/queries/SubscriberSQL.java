package com.incs83.app.constants.queries;

public interface SubscriberSQL {

    String GET_SUBSCRIBER_BY_PHONE_NUMBER = "from Subscriber where phoneNo =:phoneNo";
    String GET_SUBSCRIBER_BY_EMAIL = "from Subscriber where email = :email";
    String GET_SUBSCRIBER_BY_GLOBAL_ACCOUNT_NUMBER = "from Subscriber where globalAccountNo =:globalAccountNo";
    String GET_EQUIPMENT_BY_SERIAL_NUMBER = "from Equipment where id =:id or rgwSerial =:id or serviceTelephoneNo =:id";
    String GET_ALL_SUBSCRIBER = "select s.id,s.firstName,s.lastName,s.email,s.globalAccountNo,s.imageUrl,s.camsAccountNo,s.phoneNo,s.isActive,c.name,c.id as compartmentId from subscriber as s inner join subscriber_compartment sc on(s.id=sc.Subscriber_id) inner join compartment c on (c.id = sc.compartment_id) ###";
    String COUNT_TOTAL_SUBSCRIBERS = "SELECT count(*) from subscriber ###";
    String COUNT_OF_SEARCH_SUBSCRIBERS = "SELECT count(*) from subscriber s inner join subscriber_compartment sc on(s.id=sc.Subscriber_id) inner join compartment c on(c.id = sc.compartment_id) ###";

    String GET_SUBSCRIBERS_FOR_ELASTIC = "select s.id,s.firstName,s.lastName,s.email,s.globalAccountNo,s.imageUrl,s.camsAccountNo,s.phoneNo,s.isActive,s.createdAt,s.createdBy,c.name,c.id as compartmentId from subscriber as s inner join subscriber_compartment sc on(s.id=sc.Subscriber_id) inner join compartment c on (c.id = sc.compartment_id) %s %s";
    String GET_SUBSCRIBER_COUNT = "SELECT count(*) from subscriber";
    String GET_SUBSCRIBER_COUNT_BY_COMPARTMENT = "SELECT count(*) from subscriber s inner join subscriber_compartment sc on(s.id=sc.Subscriber_id) inner join compartment c on(c.id = sc.compartment_id) where sc.compartment_id =:id";
    String GET_SUBSCRIBER_LIST_BY_SUBSCRIBER_ID_OR_GAN = "from Subscriber where id =:id or globalAccountNo =:id order by createdAt DESC";

    String COUNT_DUPLICATE_SUBCRIBER_BY_EMAIL = "SELECT count(*) from subscriber where email = :email ";
    String COUNT_DUPLICATE_SUBCRIBER_BY_GAN = "SELECT count(*) from subscriber where globalAccountNo = :gan ";
    String COUNT_DUPLICATE_LOGIN_DETAIL_BY_EMAIL = "SELECT count(*) from login_details where email = :email ";

    String GET_SUBSCRIBER_BY_EMAIL_AND_GAN = "from Subscriber where email = :email and globalAccountNo <> :gan " ;

    // v6
    String GET_SUBSCRIBER_V6 = "SELECT s.id, s.firstName, s.lastName, c.ispId " +
            "FROM subscriber as s " +
            "INNER JOIN subscriber_compartment sc on s.id = sc.Subscriber_id " +
            "INNER JOIN compartment c on c.id = sc.compartment_id ";
    String COUNT_ALL_SUBSCRIBER_V6 = "SELECT count(*) " +
            "FROM subscriber as s " +
            "INNER JOIN subscriber_compartment sc on s.id = sc.Subscriber_id " +
            "INNER JOIN compartment c on c.id = sc.compartment_id ";
    String GET_SUBSCRIBER_LIST_BY_SUBSCRIBER_ID = "from Subscriber where id = :id order by createdAt DESC";
}
