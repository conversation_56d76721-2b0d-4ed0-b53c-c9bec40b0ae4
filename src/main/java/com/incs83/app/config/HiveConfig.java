//package com.incs83.app.config;
//
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//
//import java.sql.Connection;
//import java.sql.DriverManager;
//import java.sql.SQLException;
//import java.util.Objects;
//
//@ConditionalOnProperty("hive.url")
//@Configuration
//@ConfigurationProperties(
//        prefix = "hive"
//)
//public class HiveConfig {
//
//    private static final Logger LOG = LogManager.getLogger("org");
//
//    private String url;
//    private String userName;
//    private String password;
//    private String dataBase;
//    private String driverClassName;
//    private Boolean auth;
//    private Connection connection;
//
//    public Connection getConnection() {
//        try {
//            if (Objects.isNull(connection) || connection.isClosed()) {
//                Class.forName(driverClassName);
//                if (auth != null && auth)
//                    connection = DriverManager.getConnection(url + "/" + dataBase, userName, password);
//                else
//                    connection = DriverManager.getConnection(url + "/" + dataBase);
//            }
//        } catch (SQLException e) {
//            LOG.error("Unable to establish connection with Hive.");
//        } catch (ClassNotFoundException e) {
//            LOG.error("Hive JDBC driver not found.");
//        } catch (Exception e) {
//            LOG.error("Unable to establish connection with Hive.");
//        }
//        return connection;
//    }
//
//
//    public void setUrl(String url) {
//        this.url = url;
//    }
//
//    public void setUserName(String userName) {
//        this.userName = userName;
//    }
//
//    public void setPassword(String password) {
//        this.password = password;
//    }
//
//    public void setDataBase(String dataBase) {
//        this.dataBase = dataBase;
//    }
//
//    public void setDriverClassName(String driverClassName) {
//        this.driverClassName = driverClassName;
//    }
//
//    public void setAuth(Boolean auth) {
//        this.auth = auth;
//    }
//}
