package com.incs83.app.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CassandraConfig {
    @Value("${cassandra.server}")
    private String server;
    @Value("${cassandra.port}")
    private Integer port;
    @Value("${cassandra.database.name}")
    private String database;
    @Value("${cassandra.database.username}")
    private String username;
    @Value("${cassandra.database.password}")
    private String password;
    @Value("${cassandra.auth.enabled}")
    private boolean authEnabled;
    @Value("${cassandra.connect.enable}")
    private boolean cassandraConnect;

    public boolean isCassandraConnect() {
        return cassandraConnect;
    }

    public void setCassandraConnect(boolean cassandraConnect) {
        this.cassandraConnect = cassandraConnect;
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isAuthEnabled() {
        return authEnabled;
    }

    public void setAuthEnabled(boolean authEnabled) {
        this.authEnabled = authEnabled;
    }

}
