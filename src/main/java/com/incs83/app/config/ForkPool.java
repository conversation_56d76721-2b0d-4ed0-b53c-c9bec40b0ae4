package com.incs83.app.config;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import javax.annotation.PostConstruct;
import java.util.concurrent.ForkJoinPool;

@Configuration
public class ForkPool {
    @Scope(value = ConfigurableBeanFactory.SCOPE_SINGLETON)
    @PostConstruct
    public ForkJoinPool getForkJoinPool(){
        ForkJoinPool forkJoinPool = new ForkJoinPool(100);
        return forkJoinPool;
    }
}
