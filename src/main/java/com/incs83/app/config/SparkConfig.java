package com.incs83.app.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Created By AMAN on Tuesday, 08-January-2019
 */
@ConditionalOnProperty("spark.host")
@Configuration
@ConfigurationProperties(
        prefix = "spark"
)
public class SparkConfig {
    private String host;
    private String port;

    public String getHost() {
        return host;
    }

    public String getPort() {
        return port;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public void setPort(String port) {
        this.port = port;
    }
}
