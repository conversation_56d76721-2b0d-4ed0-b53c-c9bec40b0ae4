package com.incs83.app.config;

import com.actiontec.optim.platform.api.v6.validator.AppCloudJwtValidator;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AppCloudFilterConfig {
    private static final Logger logger = LogManager.getLogger(AppCloudFilterConfig.class);

    @Autowired
    private AppCloudJwtValidator appCloudJwtFilter;

    @Bean
    public FilterRegistrationBean<AppCloudJwtValidator> appCloudJwtFilterRegistration() {
        FilterRegistrationBean<AppCloudJwtValidator> registrationBean = new FilterRegistrationBean<>();

        // Use Spring-managed filter instance
        registrationBean.setFilter(appCloudJwtFilter);

        // Set URL patterns to intercept
        registrationBean.addUrlPatterns("/*/api/v6/test");

        // Set filter name
        registrationBean.setName("AppCloudJwtFilter");

        // Set filter order (higher number = lower priority)
        registrationBean.setOrder(0);

        logger.info("AppCloudJwtFilter registered successfully");

        return registrationBean;
    }
}
