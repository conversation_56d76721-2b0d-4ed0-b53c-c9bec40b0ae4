package com.incs83.app.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class ThreadPoolConfig {

    @Bean("longTaskExecutor")
    public Executor longTaskExecutor() {
        ThreadPoolTaskExecutor longWaitTaskExecutor = new ThreadPoolTaskExecutor();
        longWaitTaskExecutor.setCorePoolSize(10);
        longWaitTaskExecutor.setMaxPoolSize(50);
        longWaitTaskExecutor.setQueueCapacity(200);
        longWaitTaskExecutor.setKeepAliveSeconds(60);
        longWaitTaskExecutor.setThreadNamePrefix("[longTaskExecutor] ");
        longWaitTaskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        longWaitTaskExecutor.setAwaitTerminationSeconds(60);
        longWaitTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        longWaitTaskExecutor.initialize();
        return longWaitTaskExecutor;
    }
}
