package com.incs83.app.responsedto.v1;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentDataDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentData> data;

    @Override
    public ArrayList<EquipmentData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentData> data) {
        this.data = data;
    }

    public class EquipmentData {
        @ApiModelProperty(notes = "id", required = true)
        private String id;
        @ApiModelProperty(notes = "serialNumber", required = true)
        private String serialNumber;
        @ApiModelProperty(notes = "modelName", required = true)
        private String modelName;
        @ApiModelProperty(notes = "isp", required = true)
        private String isp;
        @ApiModelProperty(notes = "firmwareVersion", required = true)
        private String firmwareVersion;
        @ApiModelProperty(notes = "friendlyName", required = true)
        private String friendlyName;
        @ApiModelProperty(notes = "uptime", required = true)
        private long uptime;
        @ApiModelProperty(notes = "lastReported", required = true)
        private long lastReported;
        @ApiModelProperty(notes = "cpuUsage", required = true)
        private int cpuUsage;
        @ApiModelProperty(notes = "memoryUsage", required = true)
        private int memoryUsage;
        @ApiModelProperty(notes = "freeSpace", required = true)
        private int freeSpace;
        @ApiModelProperty(notes = "active", required = true)
        private boolean active;
        @ApiModelProperty(notes = "containerNumber", required = true)
        private int containerNumber;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getModelName() {
            return modelName;
        }

        public void setModelName(String modelName) {
            this.modelName = modelName;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }

        public String getFirmwareVersion() {
            return firmwareVersion;
        }

        public void setFirmwareVersion(String firmwareVersion) {
            this.firmwareVersion = firmwareVersion;
        }

        public String getFriendlyName() {
            return friendlyName;
        }

        public void setFriendlyName(String friendlyName) {
            this.friendlyName = friendlyName;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }

        public long getLastReported() {
            return lastReported;
        }

        public void setLastReported(long lastReported) {
            this.lastReported = lastReported;
        }

        public int getCpuUsage() {
            return cpuUsage;
        }

        public void setCpuUsage(int cpuUsage) {
            this.cpuUsage = cpuUsage;
        }

        public int getMemoryUsage() {
            return memoryUsage;
        }

        public void setMemoryUsage(int memoryUsage) {
            this.memoryUsage = memoryUsage;
        }

        public int getFreeSpace() {
            return freeSpace;
        }

        public void setFreeSpace(int freeSpace) {
            this.freeSpace = freeSpace;
        }

        public boolean getActive() {
            return active;
        }

        public void setActive(boolean active) {
            this.active = active;
        }

        public int getContainerNumber() {
            return containerNumber;
        }

        public void setContainerNumber(int containerNumber) {
            this.containerNumber = containerNumber;
        }
    }
}
