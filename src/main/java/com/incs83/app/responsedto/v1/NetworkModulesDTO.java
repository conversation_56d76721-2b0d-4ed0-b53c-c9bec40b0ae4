package com.incs83.app.responsedto.v1;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class NetworkModulesDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<NetworkModuleData> data;

    @Override
    public ArrayList<NetworkModuleData> getData() {
        return data;
    }

    public void setData(ArrayList<NetworkModuleData> data) {
        this.data = data;
    }

    public class NetworkModuleData {
        @ApiModelProperty(notes = "id", required = true)
        private String id;
        @ApiModelProperty(notes = "imageName", required = true)
        private String imageName;
        @ApiModelProperty(notes = "imageVersion", required = true)
        private String imageVersion;
        @ApiModelProperty(notes = "installedNumber", required = true)
        private int installedNumber;
        @ApiModelProperty(notes = "runningNumber", required = true)
        private int runningNumber;
        @ApiModelProperty(notes = "stoppedNumber", required = true)
        private int stoppedNumber;
        @ApiModelProperty(notes = "pausedNumber", required = true)
        private int pausedNumber;
        @ApiModelProperty(notes = "failedNumber", required = true)
        private int failedNumber;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getImageName() {
            return imageName;
        }

        public void setImageName(String imageName) {
            this.imageName = imageName;
        }

        public String getImageVersion() {
            return imageVersion;
        }

        public void setImageVersion(String imageVersion) {
            this.imageVersion = imageVersion;
        }

        public int getInstalledNumber() {
            return installedNumber;
        }

        public void setInstalledNumber(int installedNumber) {
            this.installedNumber = installedNumber;
        }

        public int getRunningNumber() {
            return runningNumber;
        }

        public void setRunningNumber(int runningNumber) {
            this.runningNumber = runningNumber;
        }

        public int getStoppedNumber() {
            return stoppedNumber;
        }

        public void setStoppedNumber(int stoppedNumber) {
            this.stoppedNumber = stoppedNumber;
        }

        public int getPausedNumber() {
            return pausedNumber;
        }

        public void setPausedNumber(int pausedNumber) {
            this.pausedNumber = pausedNumber;
        }

        public int getFailedNumber() {
            return failedNumber;
        }

        public void setFailedNumber(int failedNumber) {
            this.failedNumber = failedNumber;
        }
    }
}
