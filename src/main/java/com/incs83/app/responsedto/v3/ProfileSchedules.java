package com.incs83.app.responsedto.v3;

import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Pattern;
import java.util.List;

public class ProfileSchedules {
    @ApiModelProperty(notes = "Enable/Disable the Schedule")
    private Boolean enable;
    @ApiModelProperty(notes = "Seconds since midnight to start/end the restriction of Internet access. When applied to a \"WEEKDAY\" or \"WEEKEND\" enum day type where a 24-hr day can span 2 consecutive days, timestart has to be greater than timeend. When applied to non \"WEEKDAY\" and \"WEEKEND\" enum type, timestart has to be less than timeend.")
    private Integer timestart;
    @ApiModelProperty(notes = "Seconds since midnight to start/end the restriction of Internet access. When applied to a \"WEEKDAY\" or \"WEEKEND\" enum day type where a 24-hr day can span 2 consecutive days, timestart has to be greater than timeend. When applied to non \"WEEKDAY\" and \"WEEKEND\" enum type, timestart has to be less than timeend.")
    private Integer timeend;
    @ApiModelProperty(example = "'day':['MON','TUE','WED']", notes = "Schedules are meant to block devices tied to the profile from accessing the Internet.\n" +
            "There are 3 types of restrictive scheduling.\n" +
            "\n" +
            "A uniform schedule to be applied across all school nights (days ranging from Sunday afternoon until Friday afternoon)should set the enum type 'WEEKDAY' in the day array with no other types present.\n" +
            "Another uniform schedule to be applied on the weekend across all days starting from Friday afternoon to Sunday afternoon should set the enum type 'WEEKEND' in the day array with no other types present.\n" +
            "The third type of schedule consists of permutations of \"MON\",\"TUE\",\"WED\",\"THU\",\"FRI\",\"SAT\",\"SUN\" within the day array for more flexibility in scheduling.\n")
    private List<String> day;

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public Integer getTimestart() {
        return timestart;
    }

    public void setTimestart(Integer timestart) {
        this.timestart = timestart;
    }

    public Integer getTimeend() {
        return timeend;
    }

    public void setTimeend(Integer timeend) {
        this.timeend = timeend;
    }

    public List<String> getDay() {
        return day;
    }

    public void setDay(List<String> day) {
        this.day = day;
    }
}
