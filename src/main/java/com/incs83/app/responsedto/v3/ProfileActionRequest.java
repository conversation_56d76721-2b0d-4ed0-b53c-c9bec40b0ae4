package com.incs83.app.responsedto.v3;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

public class ProfileActionRequest implements AuditableData {
    @ApiModelProperty(notes = "Allowed action : (PauseInternet, UnPauseInternet)")
    private String action;
    @ApiModelProperty(notes = "delay this action in 'x' seconds")
    private Long delay;
    @ApiModelProperty(notes = "minutes of duration, 0 means unlimited.")
    private Long duration;

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getDelay() {
        return delay;
    }

    public void setDelay(Long delay) {
        this.delay = delay;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }
}
