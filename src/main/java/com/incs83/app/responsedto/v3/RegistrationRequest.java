package com.incs83.app.responsedto.v3;

import com.incs83.auditor.AuditableData;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

public class RegistrationRequest implements AuditableData {
    @NotNull(message = "Registration key cannot be empty")
    private String key;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
