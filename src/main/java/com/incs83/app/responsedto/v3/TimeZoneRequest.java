package com.incs83.app.responsedto.v3;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class TimeZoneRequest {
    @ApiModelProperty(notes = "setting timezone for RGW. Time difference from UTC, e.g, \"-05:00\"")
    @NotNull(message = "Please provide timezone for RGW")
    private String timezone;

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }
}
