package com.incs83.app.responsedto.v3.FsecureProvisioning;


import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class FsecureStatusResponseDTO{

    @ApiModelProperty(notes = "userId", required = true)
    private String userId;
    @ApiModelProperty(notes = "fsecureForce", required = true)
    private String fsecureForce;
    @ApiModelProperty(notes = "isp", required = true)
    private String isp;
    @ApiModelProperty(notes = "attempt", required = true)
    private String attempt;
    @ApiModelProperty(notes = "serviceTelephoneNo", required = true)
    private String serviceTelephoneNo;
    @ApiModelProperty(notes = "enabled", required = true)
    private boolean enabled;
    @ApiModelProperty(notes = "previousEnabled", required = true)
    private boolean previousEnabled;
    @ApiModelProperty(notes = "createdAt", required = true)
    private String createdAt;
    @ApiModelProperty(notes = "updatedAt", required = true)
    private String updatedAt;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getServiceTelephoneNo() {
        return serviceTelephoneNo;
    }

    public void setServiceTelephoneNo(String serviceTelephoneNo) {
        this.serviceTelephoneNo = serviceTelephoneNo;
    }

    public String getFsecureForce() {
        return fsecureForce;
    }

    public void setFsecureForce(String fsecureForce) {
        this.fsecureForce = fsecureForce;
    }

    public String getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(String createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(String updatedAt) {
        this.updatedAt = updatedAt;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }


    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getAttempt() {
        return attempt;
    }

    public void setAttempt(String attempt) {
        this.attempt = attempt;
    }

    public boolean isPreviousEnabled() {
        return previousEnabled;
    }

    public void setPreviousEnabled(boolean previousEnabled) {
        this.previousEnabled = previousEnabled;
    }
}
