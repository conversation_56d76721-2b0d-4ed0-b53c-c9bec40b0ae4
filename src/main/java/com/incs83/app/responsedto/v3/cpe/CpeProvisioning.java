package com.incs83.app.responsedto.v3.cpe;

import com.incs83.app.business.v3.AutoWifiConfigRestoreSettingService;
import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.Map;

public class CpeProvisioning implements AuditableData {
    @ApiModelProperty(notes = "config restore")
    private Boolean configRestore;

    public static CpeProvisioning fromMap(Map<String, Object> map) {
        CpeProvisioning cpeProvisioning = new CpeProvisioning();
        if (map.containsKey(AutoWifiConfigRestoreSettingService.FIELD__setting_autoConfigRestoreEnabled)) {
            cpeProvisioning.setConfigRestore((Boolean) map.get(AutoWifiConfigRestoreSettingService.FIELD__setting_autoConfigRestoreEnabled));
        } else {
            cpeProvisioning.setConfigRestore(false);
        }
        return cpeProvisioning;
    }

    public Map<String, Object> toMap() {
        HashMap<String, Object> map = new HashMap<>();
        if (configRestore != null) {
            map.put(AutoWifiConfigRestoreSettingService.FIELD__setting_autoConfigRestoreEnabled, configRestore);
        }
        return map;
    }

    public boolean isConfigRestore() {
        return configRestore;
    }

    public void setConfigRestore(boolean configRestore) {
        this.configRestore = configRestore;
    }

}
