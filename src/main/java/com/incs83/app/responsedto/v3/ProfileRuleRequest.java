package com.incs83.app.responsedto.v3;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

    public class ProfileRuleRequest implements AuditableData {
    @ApiModelProperty(notes = "Enable/Disable the Profile")
    private Boolean enable;
    @ApiModelProperty(notes = "min 1 and max 63 characters allowed")
    private String name;
    @ApiModelProperty(notes = "The URL of avatar of this profile.min 1 and max 2048 characters allowed")
    private String avatarURL;
    @ApiModelProperty(notes = "Enum:[ ALCOHOL, ADULT, ANONYMIZERS, ILLEGAL, DISTURBING, UNKNOWN, DATING, DRUGS, VIOLENCE, GAMBLING, HATE, SHOPPING, SOCIAL_NETWORKING, STREAMING, WAREZ, WEAPONS ]")
    private List<String> categories;
    @ApiModelProperty(notes = "Profile Daily Limit", example = "[120,120]")
    private List<Integer> dailyLimit;
    @ApiModelProperty(notes = "Bonus in minutes to be applied in addition to the daily allowance limit. It is valid until midnight of day it is applied. Unused bonus minutes are not carried over to the next day.")
    private Integer bonus;
    // @ApiModelProperty(example = "[{'enable':true,'timestart':0,'timeend':86399,'day':['MON','TUE','WED']}]") // // Single quotes cause json file format error
    private List<ProfileSchedules> schedules;

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getCategories() {
        return categories;
    }

    public void setCategories(List<String> categories) {
        this.categories = categories;
    }

    public List<Integer> getDailyLimit() {
        return dailyLimit;
    }

    public void setDailyLimit(List<Integer> dailyLimit) {
        this.dailyLimit = dailyLimit;
    }

    public Integer getBonus() {
        return bonus;
    }

    public void setBonus(Integer bonus) {
        this.bonus = bonus;
    }

    public List<ProfileSchedules> getSchedules() {
        return schedules;
    }

    public void setSchedules(List<ProfileSchedules> schedules) {
        this.schedules = schedules;
    }

    public String getAvatarURL() {
        return avatarURL;
    }

    public void setAvatarURL(String avatarURL) {
        this.avatarURL = avatarURL;
    }


}
