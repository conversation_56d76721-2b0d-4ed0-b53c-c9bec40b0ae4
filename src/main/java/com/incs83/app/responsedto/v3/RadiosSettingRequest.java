package com.incs83.app.responsedto.v3;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

public class RadiosSettingRequest {
    @ApiModelProperty(notes = "Radio Id")
    @NotNull(message = "Radio Id cannot be empty")
    private String radioId;
    @ApiModelProperty(notes = "Boolean value to set Radio Enable/Disable")
    @NotNull(message = "Enable cannot be empty")
    private Boolean enable;

    public String getRadioId() {
        return radioId;
    }

    public void setRadioId(String radioId) {
        this.radioId = radioId;
    }

    public Boolean getEnable() {
        return enable;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }
}
