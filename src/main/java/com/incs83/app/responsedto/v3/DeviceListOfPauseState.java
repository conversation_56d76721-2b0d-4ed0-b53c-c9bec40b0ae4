package com.incs83.app.responsedto.v3;

import com.incs83.app.responsedto.v2.Device.DevicePauseStateDTO;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceListOfPauseState extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<DevicePauseStateDTO.DeviceStateData> deviceList;

    public ArrayList<DevicePauseStateDTO.DeviceStateData> getDeviceList() {
        return deviceList;
    }

    public void setDeviceList(ArrayList<DevicePauseStateDTO.DeviceStateData> deviceList) {
        this.deviceList = deviceList;
    }

}
