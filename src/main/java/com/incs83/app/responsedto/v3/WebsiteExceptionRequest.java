package com.incs83.app.responsedto.v3;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

public class WebsiteExceptionRequest implements AuditableData {
    @ApiModelProperty(notes = "url")
    private String url;
    @ApiModelProperty(notes = "Enum : [ ALLOW, BLOCK ]")
    private String policy;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getPolicy() {
        return policy;
    }

    public void setPolicy(String policy) {
        this.policy = policy;
    }
}
