package com.incs83.app.responsedto.v3;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;

public class DeviceDataRequest implements AuditableData {
    @ApiModelProperty(notes = "Friendly Name")
    private String friendlyName;
    @ApiModelProperty(notes = "max 63 characters allowed")
    private String profileId;
    // @ApiModelProperty(example = "{'tracking':false,'browsing':false,'blockInternet':false,'iot':false}")  // Single quotes cause json file format error
    private DeviceDataProtection protection;

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    public DeviceDataProtection getProtection() {
        return protection;
    }

    public void setProtection(DeviceDataProtection protection) {
        this.protection = protection;
    }
}
