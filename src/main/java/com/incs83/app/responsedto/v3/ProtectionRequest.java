package com.incs83.app.responsedto.v3;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Pattern;

public class ProtectionRequest implements AuditableData {
    @ApiModelProperty(notes = "Enable/Disable the Browsing")
    private Boolean browsing;
    @ApiModelProperty(notes = "Enable/Disable the Tracking")
    private Boolean tracking;
    @ApiModelProperty(notes = "Enable/Disable the Iot")
    private Boolean iot;

    public Boolean getBrowsing() {
        return browsing;
    }

    public void setBrowsing(Boolean browsing) {
        this.browsing = browsing;
    }

    public Boolean getTracking() {
        return tracking;
    }

    public void setTracking(Boolean tracking) {
        this.tracking = tracking;
    }

    public Boolean getIot() {
        return iot;
    }

    public void setIot(Boolean iot) {
        this.iot = iot;
    }
}
