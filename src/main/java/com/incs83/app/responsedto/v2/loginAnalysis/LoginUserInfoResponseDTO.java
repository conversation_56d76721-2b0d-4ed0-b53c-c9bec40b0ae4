package com.incs83.app.responsedto.v2.loginAnalysis;

import java.util.List;

public class LoginUserInfoResponseDTO {

private List<LoginUserInfoResponseData> loginUserInfoResponseDataList;
private Long count ;

    public List<LoginUserInfoResponseData> getLoginUserInfoResponseDataList() {
        return loginUserInfoResponseDataList;
    }

    public void setLoginUserInfoResponseDataList(List<LoginUserInfoResponseData> loginUserInfoResponseDataList) {
        this.loginUserInfoResponseDataList = loginUserInfoResponseDataList;
    }

    public Long getCount() {
        return count;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public class LoginUserInfoResponseData {

        private String role;
        private String passwordAttempt;
        private Long passwordAge;
        private String lastLogin;
        private String name;
        private String ip;
        private String id ;


        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }

        public String getPasswordAttempt() {
            return passwordAttempt;
        }

        public void setPasswordAttempt(String passwordAttempt) {
            this.passwordAttempt = passwordAttempt;
        }

        public Long getPasswordAge() {
            return passwordAge;
        }

        public void setPasswordAge(Long passwordAge) {
            this.passwordAge = passwordAge;
        }

        public String getLastLogin() {
            return lastLogin;
        }

        public void setLastLogin(String lastLogin) {
            this.lastLogin = lastLogin;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }
    }


}
