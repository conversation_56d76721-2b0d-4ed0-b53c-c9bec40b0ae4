package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.List;

public class EquipmentSystemDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private EquipmentSystemData data;

    @Override
    public EquipmentSystemData getData() {
        return data;
    }

    public void setData(EquipmentSystemData data) {
        this.data = data;
    }

    public class EquipmentSystemData {
        @ApiModelProperty(notes = "modelname", required = true)
        private String modelName;
        @ApiModelProperty(notes = "cpuUsage", required = true)
        private Double cpuUsage;
        @ApiModelProperty(notes = "equipmentType", required = true)
        private String equipmentType;
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "memoryUsage", required = true)
        private Double memoryUsage;
        @ApiModelProperty(notes = "serialNumber", required = true)
        private String serialNumber;
        @ApiModelProperty(notes = "lastReported", required = true)
        private long lastReported;
        @ApiModelProperty(notes = "fwVersion", required = true)
        private String fwVersion;
        @ApiModelProperty(notes = "connectivityStatus", required = true)
        private String connectivityStatus;
        @ApiModelProperty(notes = "lastReboot", required = true)
        private long lastReboot;
        @ApiModelProperty(notes = "health", required = true)
        private Object health;
        @ApiModelProperty(notes = "uptime", required = true)
        private String uptime;
        @ApiModelProperty(notes = "Smart Steering", required = true)
        private boolean smartSteering;
        @ApiModelProperty(notes = "Friendly name", required = true)
        private String friendlyName;
        @ApiModelProperty(notes = "Alarms for this Equipment", required = true)
        private List<HashMap<String, Object>> alarms;
        @ApiModelProperty(notes = "Severity For Equipment", required = true)
        private int severity;
        @ApiModelProperty(notes = "Build version", required = true)
        private String buildVersion;
        @ApiModelProperty(notes = "Equipment ISP", required = true)
        private String isp;
        @ApiModelProperty(notes = "Network Info Timestamp", required = true)
        private long ntInfoTimestamp;
        @ApiModelProperty(notes = "Network Report Timestamp", required = true)
        private long ntReportTimestamp;
        @ApiModelProperty(notes = "DB Report Timestamp", required = true)
        private long dbReportTimestamp;
        @ApiModelProperty(notes = "DB Info Timestamp", required = true)
        private long dbInfoTimestamp;
        @ApiModelProperty(notes = "Local Timestamp", required = true)
        private long localTimestamp;

        public String getModelName() {
            return modelName;
        }

        public void setModelName(String modelName) {
            this.modelName = modelName;
        }

        public Double getCpuUsage() {
            return cpuUsage;
        }

        public void setCpuUsage(Double cpuUsage) {
            this.cpuUsage = cpuUsage;
        }

        public String getEquipmentType() {
            return equipmentType;
        }

        public void setEquipmentType(String equipmentType) {
            this.equipmentType = equipmentType;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public Double getMemoryUsage() {
            return memoryUsage;
        }

        public void setMemoryUsage(Double memoryUsage) {
            this.memoryUsage = memoryUsage;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public long getLastReported() {
            return lastReported;
        }

        public void setLastReported(long lastReported) {
            this.lastReported = lastReported;
        }

        public String getFwVersion() {
            return fwVersion;
        }

        public void setFwVersion(String fwVersion) {
            this.fwVersion = fwVersion;
        }

        public String getConnectivityStatus() {
            return connectivityStatus;
        }

        public void setConnectivityStatus(String connectivityStatus) {
            this.connectivityStatus = connectivityStatus;
        }

        public long getLastReboot() {
            return lastReboot;
        }

        public void setLastReboot(long lastReboot) {
            this.lastReboot = lastReboot;
        }

        public String getUptime() {
            return uptime;
        }

        public Object getHealth() {
            return health;
        }
        public void setHealth(Object health) {
            this.health = health;
        }

        public void setUptime(String uptime) {
            this.uptime = uptime;
        }

        public boolean isSmartSteering() {
            return smartSteering;
        }

        public void setSmartSteering(boolean smartSteering) {
            this.smartSteering = smartSteering;
        }

        public String getFriendlyName() {
            return friendlyName;
        }

        public void setFriendlyName(String friendlyName) {
            this.friendlyName = friendlyName;
        }

        public List<HashMap<String, Object>> getAlarms() {
            return alarms;
        }

        public void setAlarms(List<HashMap<String, Object>> alarms) {
            this.alarms = alarms;
        }

        public int getSeverity() {
            return severity;
        }

        public void setSeverity(int severity) {
            this.severity = severity;
        }

        public String getBuildVersion() {
            return buildVersion;
        }

        public void setBuildVersion(String buildVersion) {
            this.buildVersion = buildVersion;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }

        public long getNtInfoTimestamp() {
            return ntInfoTimestamp;
        }

        public void setNtInfoTimestamp(long ntInfoTimestamp) {
            this.ntInfoTimestamp = ntInfoTimestamp;
        }

        public long getNtReportTimestamp() {
            return ntReportTimestamp;
        }

        public void setNtReportTimestamp(long ntReportTimestamp) {
            this.ntReportTimestamp = ntReportTimestamp;
        }

        public long getDbReportTimestamp() {
            return dbReportTimestamp;
        }

        public void setDbReportTimestamp(long dbReportTimestamp) {
            this.dbReportTimestamp = dbReportTimestamp;
        }

        public long getDbInfoTimestamp() {
            return dbInfoTimestamp;
        }

        public void setDbInfoTimestamp(long dbInfoTimestamp) {
            this.dbInfoTimestamp = dbInfoTimestamp;
        }

        public long getLocalTimestamp() {
            return localTimestamp;
        }

        public void setLocalTimestamp(long localTimestamp) {
            this.localTimestamp = localTimestamp;
        }
    }
}
