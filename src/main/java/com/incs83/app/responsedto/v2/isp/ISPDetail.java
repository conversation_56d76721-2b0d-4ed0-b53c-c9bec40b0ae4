package com.incs83.app.responsedto.v2.isp;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;

import java.io.IOException;
import java.io.Serializable;

public class ISPDetail implements Serializable, DataSerializable {
    private String ispName;
    private Long mapped;
    private Long unMapped;
    private Long failedRecord;

    public String getIspName() {
        return ispName;
    }

    public void setIspName(String ispName) {
        this.ispName = ispName;
    }

    public Long getMapped() {
        return mapped;
    }

    public void setMapped(Long mapped) {
        this.mapped = mapped;
    }

    public Long getUnMapped() {
        return unMapped;
    }

    public void setUnMapped(Long unMapped) {
        this.unMapped = unMapped;
    }

    public Long getFailedRecord() {
        return failedRecord;
    }

    public void setFailedRecord(Long failedRecord) {
        this.failedRecord = failedRecord;
    }

    @Override
    public void writeData(ObjectDataOutput out) throws IOException {
        out.writeUTF(this.ispName);
        out.writeLong(this.mapped.longValue());
        out.writeLong(this.unMapped.longValue());
        out.writeLong(this.failedRecord.longValue());
    }

    @Override
    public void readData(ObjectDataInput in) throws IOException {
        this.ispName = in.readUTF();
        this.mapped = in.readLong();
        this.unMapped = in.readLong();
        this.failedRecord = in.readLong();
    }
}
