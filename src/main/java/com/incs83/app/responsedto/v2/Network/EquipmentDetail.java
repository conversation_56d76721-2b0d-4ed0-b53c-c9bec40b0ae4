package com.incs83.app.responsedto.v2.Network;

import com.incs83.app.responsedto.v2.Device.DeviceDetailDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class EquipmentDetail {
    @ApiModelProperty(notes = "macAddress", required = true)
    private String macAddress;
    @ApiModelProperty(notes = "connectivityStatus", required = true)
    private String connectivityStatus;
    @ApiModelProperty(notes = "name", required = true)
    private String name;
    @ApiModelProperty(notes = "serialNumber", required = true)
    private String serialNumber;
    @ApiModelProperty(notes = "type", required = true)
    private String type;
    @ApiModelProperty(notes = "_24GChannelNumber", required = true)
    private Integer _24GChannelNumber;
    @ApiModelProperty(notes = "5GChannelNumber", required = true)
    private Integer _5GChannelNumber;
    /*@ApiModelProperty(notes = "devices", required = true)
    private ArrayList<DeviceDetail> devices;*/
    @ApiModelProperty(notes = "devices", required = true)
    private List<DeviceDetailDTO.DeviceDetailData> devices;

    @ApiModelProperty(notes = "extenders", required = true)
    private ArrayList<ExtenderDetail> extenders;
    @ApiModelProperty(notes = "Alarms for this Equipment", required = true)
    private List<HashMap<String, Object>> alarms;
    @ApiModelProperty(notes = "Severity Status for the Equipment", required = true)
    private int severity;


    public List<DeviceDetailDTO.DeviceDetailData> getDevices() {
        return devices;
    }

    public void setDevices(List<DeviceDetailDTO.DeviceDetailData> devices) {
        this.devices = devices;
    }

    public ArrayList<ExtenderDetail> getExtenders() {
        return extenders;
    }

    public void setExtenders(ArrayList<ExtenderDetail> extenders) {
        this.extenders = extenders;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getConnectivityStatus() {
        return connectivityStatus;
    }

    public void setConnectivityStatus(String connectivityStatus) {
        this.connectivityStatus = connectivityStatus;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer get_24GChannelNumber() {
        return _24GChannelNumber;
    }

    public void set_24GChannelNumber(Integer _24GChannelNumber) {
        this._24GChannelNumber = _24GChannelNumber;
    }

    public Integer get_5GChannelNumber() {
        return _5GChannelNumber;
    }

    public void set_5GChannelNumber(Integer _5GChannelNumber) {
        this._5GChannelNumber = _5GChannelNumber;
    }

    public List<HashMap<String, Object>> getAlarms() {
        return alarms;
    }

    public void setAlarms(List<HashMap<String, Object>> alarms) {
        this.alarms = alarms;
    }

    public int getSeverity() {
        return severity;
    }

    public void setSeverity(int severity) {
        this.severity = severity;
    }
}
