package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class SubscriberList extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> data;

    @Override
    public ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> getData() {
        return data;
    }

    public void setData(ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> data) {
        this.data = data;
    }
}
