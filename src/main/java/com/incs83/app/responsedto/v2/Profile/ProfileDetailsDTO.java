package com.incs83.app.responsedto.v2.Profile;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class ProfileDetailsDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ProfileDetailsData data;

    @Override
    public ProfileDetailsData getData() {
        return data;
    }

    public void setData(ProfileDetailsData data) {
        this.data = data;
    }

    public class ProfileDetailsData {
        @ApiModelProperty(notes = "id", required = true)
        private String id;
        @ApiModelProperty(notes = "profileName", required = true)
        private String profileName;
        @ApiModelProperty(notes = "isPaused", required = true)
        private boolean isPaused;
        @ApiModelProperty(notes = "pauseDuration", required = true)
        private Long pauseDuration;
        @ApiModelProperty(notes = "timeRemaining", required = true)
        private Long timeRemaining;
        /*@ApiModelProperty(notes = "userId", required = true)
        private String userId;*/
        @ApiModelProperty(notes = "devices", required = true)
        private ArrayList<DeviceDetail> devices;
        @ApiModelProperty(notes = "schedules", required = true)
        private ArrayList<ProfileSchedule> schedules;

        /*public String getEmail() {
            return userId;
        }

        public void setEmail(String userId) {
            this.userId = userId;
        }*/

        public Long getPauseDuration() {
            return pauseDuration;
        }

        public void setPauseDuration(Long pauseDuration) {
            this.pauseDuration = pauseDuration;
        }

        public Long getTimeRemaining() {
            return timeRemaining;
        }

        public void setTimeRemaining(Long timeRemaining) {
            this.timeRemaining = timeRemaining;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getProfileName() {
            return profileName;
        }

        public void setProfileName(String profileName) {
            this.profileName = profileName;
        }

        public boolean isPaused() {
            return isPaused;
        }

        public void setPaused(boolean paused) {
            isPaused = paused;
        }

        public ArrayList<DeviceDetail> getDevices() {
            return devices;
        }

        public void setDevices(ArrayList<DeviceDetail> devices) {
            this.devices = devices;
        }

        public ArrayList<ProfileSchedule> getSchedules() {
            return schedules;
        }

        public void setSchedules(ArrayList<ProfileSchedule> schedules) {
            this.schedules = schedules;
        }

        private class DeviceDetail {
            @ApiModelProperty(notes = "macAddress", required = true)
            private String macAddress;
            @ApiModelProperty(notes = "name", required = true)
            private String name;
            @ApiModelProperty(notes = "status", required = true)
            private Boolean status;
            @ApiModelProperty(notes = "id", required = true)
            private String id;


            public Boolean getStatus() {
                return status;
            }

            public void setStatus(Boolean status) {
                this.status = status;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getMacAddress() {
                return macAddress;
            }

            public void setMacAddress(String macAddress) {
                this.macAddress = macAddress;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }

        private class ProfileSchedule {
            @ApiModelProperty(notes = "id", required = true)
            private String id;
            @ApiModelProperty(notes = "name", required = true)
            private String name;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }
        }


    }
}
