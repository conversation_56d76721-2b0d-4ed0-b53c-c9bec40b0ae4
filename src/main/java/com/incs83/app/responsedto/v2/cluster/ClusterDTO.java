package com.incs83.app.responsedto.v2.cluster;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class ClusterDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<ClusterData> data;

    @Override
    public ArrayList<ClusterData> getData() {
        return data;
    }

    public void setData(ArrayList<ClusterData> data) {
        this.data = data;
    }

    public class ClusterData {
        @ApiModelProperty(notes = "Cluster id")
        private String id;
        @ApiModelProperty(notes = "Cluster name")
        private String name;
        @ApiModelProperty(notes = "ISP")
        private String isp;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }
    }
}

