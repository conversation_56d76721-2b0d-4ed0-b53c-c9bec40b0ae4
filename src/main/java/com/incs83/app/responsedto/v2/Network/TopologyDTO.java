package com.incs83.app.responsedto.v2.Network;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class TopologyDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private TopologyData data;

    @Override
    public TopologyData getData() {
        return data;
    }

    public void setData(TopologyData data) {
        this.data = data;
    }

    public class TopologyData {
        @ApiModelProperty(notes = "equipment", required = true)
        private EquipmentDetail equipment;

        private Integer severity;

        public EquipmentDetail getEquipment() {
            return equipment;
        }

        public void setEquipment(EquipmentDetail equipment) {
            this.equipment = equipment;
        }

        public Integer getSeverity() {
            return severity;
        }

        public void setSeverity(Integer severity) {
            this.severity = severity;
        }
    }
}
