package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentDetailsDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private EquipmentDetailData data;

    @Override
    public EquipmentDetailData getData() {
        return data;
    }

    public void setData(EquipmentDetailData data) {
        this.data = data;
    }

    public class EquipmentDetailData {
        @ApiModelProperty(notes = "moca", required = true)
        private EquipmentMoCADTO.EquipmentMoCAData moca;
        @ApiModelProperty(notes = "ethernet", required = true)
        private ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> ethernet;
        @ApiModelProperty(notes = "mocaLan", required = true)
        private ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> mocaLan;
        @ApiModelProperty(notes = "wan", required = true)
        private EquipmentWANDTO.EquipmentWANData wan;
        @ApiModelProperty(notes = "system", required = true)
        private EquipmentSystemDTO.EquipmentSystemData system;

        public EquipmentMoCADTO.EquipmentMoCAData getMoca() {
            return moca;
        }

        public void setMoca(EquipmentMoCADTO.EquipmentMoCAData moca) {
            this.moca = moca;
        }

        public ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> getEthernet() {
            return ethernet;
        }

        public void setEthernet(ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> ethernet) {
            this.ethernet = ethernet;
        }

        public ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> getMocaLan() {
            return mocaLan;
        }

        public void setMocaLan(ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> mocaLan) {
            this.mocaLan = mocaLan;
        }

        public EquipmentWANDTO.EquipmentWANData getWan() {
            return wan;
        }

        public void setWan(EquipmentWANDTO.EquipmentWANData wan) {
            this.wan = wan;
        }

        public EquipmentSystemDTO.EquipmentSystemData getSystem() {
            return system;
        }

        public void setSystem(EquipmentSystemDTO.EquipmentSystemData system) {
            this.system = system;
        }
    }
}
