package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.List;

public class DeviceDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private DeviceDetailData data;

    public DeviceDetailData getData() {
        return data;
    }

    public void setData(DeviceDetailData data) {
        this.data = data;
    }

    public class DeviceDetailData {
        @ApiModelProperty(notes = "Device rssi", required = true)
        private double rssi;
        @ApiModelProperty(notes = "Device last Report time", required = true)
        private Long lastReportAt;
        @ApiModelProperty(notes = "Device downlinkBytes", required = true)
        private double downlinkBytes;
        @ApiModelProperty(notes = "Device name", required = true)
        private String name;
        @ApiModelProperty(notes = "Device ip address", required = true)
        private String ip;
        @ApiModelProperty(notes = "Device down link error", required = true)
        private double downlinkErrors;
        @ApiModelProperty(notes = "Device status ON/OFF", required = true)
        private boolean internetOn;
        @ApiModelProperty(notes = "Device wifi mode", required = true)
        private String wifiMode;
        @ApiModelProperty(notes = "Device ssid", required = true)
        private String ssid;
        @ApiModelProperty(notes = "Device MAC", required = true)
        private String mac;
        @ApiModelProperty(notes = "Device host name", required = true)
        private String hostname;
        @ApiModelProperty(notes = "Device downlinkPhyRate", required = true)
        private double downlinkPhyRate;
        @ApiModelProperty(notes = "Device capability", required = true)
        private String capability;
        @ApiModelProperty(notes = "Device downlinkRetransmissions", required = true)
        private double downlinkRetransmissions;
        @ApiModelProperty(notes = "Device vendor name", required = true)
        private String vendor;
        @ApiModelProperty(notes = "Device uplinkBytes", required = true)
        private double uplinkBytes;
        @ApiModelProperty(notes = "Device uplinkPhyRate", required = true)
        private double uplinkPhyRate;
        @ApiModelProperty(notes = "Device band", required = true)
        private String band;
        @ApiModelProperty(notes = "Device Misc", required = false)
        private boolean isFromDSHost;
        @ApiModelProperty(notes = "Device Connectivity Status", required = true)
        private String connectivityStatus;
        @ApiModelProperty(notes = "SpeedTestStats", required = true)
        private DeviceSpeedTest speedTestStats;
        @ApiModelProperty(notes = "Device Type", required = true)
        private String deviceType;
        @ApiModelProperty(notes = "RGW of Equipment, this device is connected to", required = true)
        private String connectedTo;
        @ApiModelProperty(notes = "Friendly Name of Equipment, this device is connected to", required = true)
        private String connectedToName;
        @ApiModelProperty(notes = "Alarms for this device", required = true)
        private List<HashMap<String, Object>> alarms;
        @ApiModelProperty(notes = "RSSI Lower Limit value")
        private Integer rssiRange;
        @ApiModelProperty(notes = "ISP", required = true)
        private String isp;
        @ApiModelProperty(notes = "internetAccessBlocked", required = true)
        private boolean internetAccessBlocked;
        @ApiModelProperty(notes = "internetAccessBlockStartTime", required = true)
        private long internetAccessBlockStartTime;
        @ApiModelProperty(notes = "internetAccessBlockDuration", required = true)
        private long internetAccessBlockDuration;
        @ApiModelProperty(notes = "dfsSupported", required = true)
        private boolean dfsSupported;
        @ApiModelProperty(notes = "supportedBands", required = true)
        private String supportedBands;


        public class DeviceSpeedTest {
            @ApiModelProperty(notes = "Date", required = true)
            private long date;
            @ApiModelProperty(notes = "DataRate", required = true)
            private float dataRate;

            public long getDate() {
                return date;
            }

            public void setDate(long date) {
                this.date = date;
            }

            public float getDataRate() {
                return dataRate;
            }

            public void setDataRate(float dataRate) {
                this.dataRate = dataRate;
            }
        }

        public boolean isFromDSHost() {
            return isFromDSHost;
        }

        public void setFromDSHost(boolean fromDSHost) {
            isFromDSHost = fromDSHost;
        }

        public double getRssi() {
            return rssi;
        }

        public void setRssi(double rssi) {
            this.rssi = rssi;
        }

        public double getDownlinkBytes() {
            return downlinkBytes;
        }

        public void setDownlinkBytes(double downlinkBytes) {
            this.downlinkBytes = downlinkBytes;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public double getDownlinkErrors() {
            return downlinkErrors;
        }

        public void setDownlinkErrors(double downlinkErrors) {
            this.downlinkErrors = downlinkErrors;
        }

        public boolean isInternetOn() {
            return internetOn;
        }

        public void setInternetOn(boolean internetOn) {
            this.internetOn = internetOn;
        }

        public String getWifiMode() {
            return wifiMode;
        }

        public void setWifiMode(String wifiMode) {
            this.wifiMode = wifiMode;
        }

        public String getSsid() {
            return ssid;
        }

        public void setSsid(String ssid) {
            this.ssid = ssid;
        }

        public String getMac() {
            return mac;
        }

        public void setMac(String mac) {
            this.mac = mac;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public double getDownlinkPhyRate() {
            return downlinkPhyRate;
        }

        public void setDownlinkPhyRate(double downlinkPhyRate) {
            this.downlinkPhyRate = downlinkPhyRate;
        }

        public String getCapability() {
            return capability;
        }

        public void setCapability(String capability) {
            this.capability = capability;
        }

        public double getDownlinkRetransmissions() {
            return downlinkRetransmissions;
        }

        public void setDownlinkRetransmissions(double downlinkRetransmissions) {
            this.downlinkRetransmissions = downlinkRetransmissions;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public double getUplinkBytes() {
            return uplinkBytes;
        }

        public void setUplinkBytes(double uplinkBytes) {
            this.uplinkBytes = uplinkBytes;
        }

        public double getUplinkPhyRate() {
            return uplinkPhyRate;
        }

        public void setUplinkPhyRate(double uplinkPhyRate) {
            this.uplinkPhyRate = uplinkPhyRate;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public String getConnectivityStatus() {
            return connectivityStatus;
        }

        public void setConnectivityStatus(String connectivityStatus) {
            this.connectivityStatus = connectivityStatus;
        }

        public DeviceSpeedTest getSpeedTestStats() {
            return speedTestStats;
        }

        public void setSpeedTestStats(DeviceSpeedTest speedTestStats) {
            this.speedTestStats = speedTestStats;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }

        public String getConnectedTo() {
            return connectedTo;
        }

        public void setConnectedTo(String connectedTo) {
            this.connectedTo = connectedTo;
        }

        public String getConnectedToName() {
            return connectedToName;
        }

        public void setConnectedToName(String connectedToName) {
            this.connectedToName = connectedToName;
        }

        public List<HashMap<String, Object>> getAlarms() {
            return alarms;
        }

        public void setAlarms(List<HashMap<String, Object>> alarms) {
            this.alarms = alarms;
        }

        public Integer getRssiRange() {
            return rssiRange;
        }

        public void setRssiRange(Integer rssiRange) {
            this.rssiRange = rssiRange;
        }

        public Long getLastReportAt() {
            return lastReportAt;
        }

        public void setLastReportAt(Long lastReportAt) {
            this.lastReportAt = lastReportAt;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }

        public boolean isInternetAccessBlocked() {
            return internetAccessBlocked;
        }

        public void setInternetAccessBlocked(boolean internetAccessBlocked) {
            this.internetAccessBlocked = internetAccessBlocked;
        }

        public long getInternetAccessBlockStartTime() {
            return internetAccessBlockStartTime;
        }

        public void setInternetAccessBlockStartTime(long internetAccessBlockStartTime) {
            this.internetAccessBlockStartTime = internetAccessBlockStartTime;
        }

        public long getInternetAccessBlockDuration() {
            return internetAccessBlockDuration;
        }

        public void setInternetAccessBlockDuration(long internetAccessBlockDuration) {
            this.internetAccessBlockDuration = internetAccessBlockDuration;
        }

        public boolean isDfsSupported() {
            return dfsSupported;
        }

        public void setDfsSupported(boolean dfsSupported) {
            this.dfsSupported = dfsSupported;
        }

        public String getSupportedBands() {
            return supportedBands;
        }

        public void setSupportedBands(String supportedBands) {
            this.supportedBands = supportedBands;
        }
    }
}
