package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentChannelChangeEventDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentChannelChangeEventData> data;

    @Override
    public ArrayList<EquipmentChannelChangeEventData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentChannelChangeEventData> data) {
        this.data = data;
    }

    public class EquipmentChannelChangeEventData {
        @ApiModelProperty(notes = "serialNumber", required = true)
        private String serialNumber;
        @ApiModelProperty(notes = "band", required = true)
        private String band;
        @ApiModelProperty(notes = "fromChannel", required = true)
        private int fromChannel;
        @ApiModelProperty(notes = "toChannel", required = true)
        private int toChannel;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public int getFromChannel() {
            return fromChannel;
        }

        public void setFromChannel(int fromChannel) {
            this.fromChannel = fromChannel;
        }

        public int getToChannel() {
            return toChannel;
        }

        public void setToChannel(int toChannel) {
            this.toChannel = toChannel;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }
}
