package com.incs83.app.responsedto.v2.Network;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashSet;

public class NetworkListDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private HashSet<NetworkListData> data;

    @Override
    public HashSet<NetworkListData> getData() {
        return data;
    }

    public void setData(HashSet<NetworkListData> data) {
        this.data = data;
    }

    public class NetworkListData {
        @ApiModelProperty(notes = "password", required = true)
        private String password;
        @ApiModelProperty(notes = "band", required = true)
        private String band;
        @ApiModelProperty(notes = "type", required = true)
        private String type;
        @ApiModelProperty(notes = "ssid", required = true)
        private String ssid;
        @ApiModelProperty(notes = "enabled", required = true)
        private boolean enabled;
        @ApiModelProperty(notes = "AP serialNumber", required = true)
        private String serialNumber;
        @ApiModelProperty(notes = "AP type", required = true)
        private String apType;

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getSsid() {
            return ssid;
        }

        public void setSsid(String ssid) {
            this.ssid = ssid;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getApType() {
            return apType;
        }

        public void setApType(String apType) {
            this.apType = apType;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            NetworkListData that = (NetworkListData) o;

            if (enabled != that.enabled) return false;
            if (password != null ? !password.equals(that.password) : that.password != null) return false;
            if (band != null ? !band.equals(that.band) : that.band != null) return false;
            if (type != null ? !type.equals(that.type) : that.type != null) return false;
            if (apType != null ? !apType.equals(that.apType) : that.apType != null) return false;
            return ssid != null ? ssid.equals(that.ssid) : that.ssid == null;
        }

        @Override
        public int hashCode() {
            int result = password != null ? password.hashCode() : 0;
            result = 31 * result + (band != null ? band.hashCode() : 0);
            result = 31 * result + (type != null ? type.hashCode() : 0);
            result = 31 * result + (ssid != null ? ssid.hashCode() : 0);
            result = 31 * result + (enabled ? 1 : 0);
            return result;
        }
    }
}
