package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentChannelInterferenceDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentChannelInterferenceData> data;

    @Override
    public ArrayList<EquipmentChannelInterferenceData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentChannelInterferenceData> data) {
        this.data = data;
    }

    public class EquipmentChannelInterferenceData {
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;
        @ApiModelProperty(notes = "band", required = true)
        private String band;
        @ApiModelProperty(notes = "ssid", required = true)
        private ArrayList<EquipmentSsidDTO.EquipmentSsidData> ssids;

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public ArrayList<EquipmentSsidDTO.EquipmentSsidData> getSsids() {
            return ssids;
        }

        public void setSsids(ArrayList<EquipmentSsidDTO.EquipmentSsidData> ssids) {
            this.ssids = ssids;
        }
    }
}
