package com.incs83.app.responsedto.v2.dataLogs;

import com.incs83.dto.ApiResponseDTO;

public class DataLogDTO extends ApiResponseDTO {
    private DataLog data;

    @Override
    public DataLog getData() {
        return data;
    }

    public void setData(DataLog data) {
        this.data = data;
    }

    public class DataLog{
        private String userId;
        private Long timestamp;
        private String recordList;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        public String getRecordList() {
            return recordList;
        }

        public void setRecordList(String recordList) {
            this.recordList = recordList;
        }
    }
}
