package com.incs83.app.responsedto.v2.Profile;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class SubscriberProfileListDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private List<SubscriberProfileDTO.SubscriberProfileData> data;

    @Override
    public List<SubscriberProfileDTO.SubscriberProfileData> getData() {
        return data;
    }

    public void setData(List<SubscriberProfileDTO.SubscriberProfileData> data) {
        this.data = data;
    }
}
