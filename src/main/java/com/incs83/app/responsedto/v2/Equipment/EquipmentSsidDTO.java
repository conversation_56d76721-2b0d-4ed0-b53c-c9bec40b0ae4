package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentSsidDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentSsidData> data;

    @Override
    public ArrayList<EquipmentSsidData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentSsidData> data) {
        this.data = data;
    }

    public class EquipmentSsidData {
        @ApiModelProperty(notes = "ssid", required = true)
        private String ssid;
        @ApiModelProperty(notes = "bssid", required = true)
        private String bssid;
        @ApiModelProperty(notes = "channel", required = true)
        private int channel;
        @ApiModelProperty(notes = "bandwidth", required = true)
        private int bandwidth;
        @ApiModelProperty(notes = "signalStrength", required = true)
        private int signalStrength;
        @ApiModelProperty(notes = "reporterSerialNumber", required = true)
        private String reporterSerialNumber;

        public String getSsid() {
            return ssid;
        }

        public void setSsid(String ssid) {
            this.ssid = ssid;
        }

        public String getBssid() {
            return bssid;
        }

        public void setBssid(String bssid) {
            this.bssid = bssid;
        }

        public int getChannel() {
            return channel;
        }

        public void setChannel(int channel) {
            this.channel = channel;
        }

        public int getBandwidth() {
            return bandwidth;
        }

        public void setBandwidth(int bandwidth) {
            this.bandwidth = bandwidth;
        }

        public int getSignalStrength() {
            return signalStrength;
        }

        public void setSignalStrength(int signalStrength) {
            this.signalStrength = signalStrength;
        }

        public String getReporterSerialNumber() {
            return reporterSerialNumber;
        }

        public void setReporterSerialNumber(String reporterSerialNumber) {
            this.reporterSerialNumber = reporterSerialNumber;
        }
    }
}
