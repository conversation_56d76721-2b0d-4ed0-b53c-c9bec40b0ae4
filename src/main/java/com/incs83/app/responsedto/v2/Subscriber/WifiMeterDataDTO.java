package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class WifiMeterDataDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private double data;

    @Override
    public Double getData() {
        return data;
    }

    public void setData(Double data) {
        this.data = data;
    }
}
