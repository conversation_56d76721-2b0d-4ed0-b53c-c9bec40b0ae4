package com.incs83.app.responsedto.v2.loginAnalysis;

public class LoginAnalysisResponseDTO {

    private Long totalSuccessIAM  ;
    private Long totalSuccessOAUTH  ;
    private Long totalFailIAM  ;
//    private Long totalFAILOAUTH  ;
    private Long attemptsLimit ;


    public Long getTotalSuccessIAM() {
        return totalSuccessIAM;
    }

    public void setTotalSuccessIAM(Long totalSuccessIAM) {
        this.totalSuccessIAM = totalSuccessIAM;
    }

    public Long getTotalSuccessOAUTH() {
        return totalSuccessOAUTH;
    }

    public void setTotalSuccessOAUTH(Long totalSuccessOAUTH) {
        this.totalSuccessOAUTH = totalSuccessOAUTH;
    }

    public Long getTotalFailIAM() {
        return totalFailIAM;
    }

    public void setTotalFailIAM(Long totalFailIAM) {
        this.totalFailIAM = totalFailIAM;
    }

//    public Long getTotalFAILOAUTH() {
//        return totalFAILOAUTH;
//    }

//    public void setTotalFAILOAUTH(Long totalFAILOAUTH) {
//        this.totalFAILOAUTH = totalFAILOAUTH;
//    }

    public Long getAttemptsLimit() {
        return attemptsLimit;
    }

    public void setAttemptsLimit(Long attemptsLimit) {
        this.attemptsLimit = attemptsLimit;
    }
}
