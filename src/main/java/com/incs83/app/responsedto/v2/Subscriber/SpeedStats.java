package com.incs83.app.responsedto.v2.Subscriber;

import io.swagger.annotations.ApiModelProperty;

public class SpeedStats {
    @ApiModelProperty(notes = "date", required = true)
    private long date;
    @ApiModelProperty(notes = "uploadSpeed", required = true)
    private float uploadSpeed;
    @ApiModelProperty(notes = "downloadSpeed", required = true)
    private float downloadSpeed;
    @ApiModelProperty(notes = "latency", required = true)
    private long latency;
    @ApiModelProperty(notes = "RPC Result", required = true)
    private String result;
    @ApiModelProperty(notes = "Speed Test URL", required = true)
    private String url;

    public long getDate() {
        return date;
    }

    public void setDate(long date) {
        this.date = date;
    }

    public float getUploadSpeed() {
        return uploadSpeed;
    }

    public void setUploadSpeed(float uploadSpeed) {
        this.uploadSpeed = uploadSpeed;
    }

    public float getDownloadSpeed() {
        return downloadSpeed;
    }

    public void setDownloadSpeed(float downloadSpeed) {
        this.downloadSpeed = downloadSpeed;
    }

    public long getLatency() {
        return latency;
    }

    public void setLatency(long latency) {
        this.latency = latency;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
