package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentCommonDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentBusyData> data;

    @Override
    public ArrayList<EquipmentBusyData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentBusyData> data) {
        this.data = data;
    }

    public class EquipmentBusyData {
        @ApiModelProperty(notes = "value", required = true)
        private double value;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public double getValue() {
            return value;
        }

        public void setValue(double value) {
            this.value = value;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }

}
