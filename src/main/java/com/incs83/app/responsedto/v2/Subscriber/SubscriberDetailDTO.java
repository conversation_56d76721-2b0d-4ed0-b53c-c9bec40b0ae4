package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class SubscriberDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private SubscriberDetail data;

    @Override
    public SubscriberDetail getData() {
        return data;
    }

    public void setData(SubscriberDetail data) {
        this.data = data;
    }

    public class SubscriberDetail {
        @ApiModelProperty(notes = "serialNumber", required = true)
        private String serialNumber;
        @ApiModelProperty(notes = "RGW MAC", required = true)
        private String rgwMAC;
        @ApiModelProperty(notes = "name", required = true)
        private String name;
        @ApiModelProperty(notes = "email", required = true)
        private String email;
        @ApiModelProperty(notes = "subscriberId", required = true)
        private String subscriberId;
        @ApiModelProperty(notes = "phoneNo", required = true)
        private String phoneNo;
        @ApiModelProperty(notes = "globalAccountNo", required = true)
        private String globalAccountNo;
        @ApiModelProperty(notes = "camsAccountNo", required = true)
        private String camsAccountNo;
        @ApiModelProperty(notes = "Allocated Bandwidth to Subscriber")
        private Double downLinkRate;
        @ApiModelProperty(notes = "Allocated Bandwidth to Subscriber")
        private Double upLinkRate;
        @ApiModelProperty(notes = "Equipment Last Reported At")
        private long lastReportedAt;
        @ApiModelProperty(notes = "ISP of subscriber")
        private String isp;

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getRgwMAC() {
            return rgwMAC;
        }

        public void setRgwMAC(String rgwMAC) {
            this.rgwMAC = rgwMAC;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getSubscriberId() {
            return subscriberId;
        }

        public void setSubscriberId(String subscriberId) {
            this.subscriberId = subscriberId;
        }

        public String getPhoneNo() {
            return phoneNo;
        }

        public void setPhoneNo(String phoneNo) {
            this.phoneNo = phoneNo;
        }

        public String getGlobalAccountNo() {
            return globalAccountNo;
        }

        public void setGlobalAccountNo(String globalAccountNo) {
            this.globalAccountNo = globalAccountNo;
        }

        public String getCamsAccountNo() {
            return camsAccountNo;
        }

        public void setCamsAccountNo(String camsAccountNo) {
            this.camsAccountNo = camsAccountNo;
        }

        public Double getDownLinkRate() {
            return downLinkRate;
        }

        public void setDownLinkRate(Double downLinkRate) {
            this.downLinkRate = downLinkRate;
        }

        public Double getUpLinkRate() {
            return upLinkRate;
        }

        public void setUpLinkRate(Double upLinkRate) {
            this.upLinkRate = upLinkRate;
        }

        public long getLastReportedAt() {
            return lastReportedAt;
        }

        public void setLastReportedAt(long lastReportedAt) {
            this.lastReportedAt = lastReportedAt;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }
    }
}
