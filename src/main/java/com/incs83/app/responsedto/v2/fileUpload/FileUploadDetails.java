package com.incs83.app.responsedto.v2.fileUpload;

public class FileUploadDetails {
    private String fileId;
    private String name;
    private String size;
    private Long updateOn;
    private String status;
    private String url;
    private Long failedRecord;
    private Long totalRecordProcessed;
    private Long noOfRecordUpdated;
    private Long totalRecord;
    private Long pending;
    private Long invalidRecord;
    private String urlForInvalidRecord;
    private String timeTaken;

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public Long getUpdateOn() {
        return updateOn;
    }

    public void setUpdateOn(Long updateOn) {
        this.updateOn = updateOn;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getFailedRecord() {
        return failedRecord;
    }

    public void setFailedRecord(Long failedRecord) {
        this.failedRecord = failedRecord;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getTotalRecordProcessed() {
        return totalRecordProcessed;
    }

    public void setTotalRecordProcessed(Long totalRecordProcessed) {
        this.totalRecordProcessed = totalRecordProcessed;
    }

    public Long getTotalRecord() {
        return totalRecord;
    }

    public void setTotalRecord(Long totalRecord) {
        this.totalRecord = totalRecord;
    }

    public Long getPending() {
        return pending;
    }

    public void setPending(Long pending) {
        this.pending = pending;
    }

    public Long getInvalidRecord() {
        return invalidRecord;
    }

    public void setInvalidRecord(Long invalidRecord) {
        this.invalidRecord = invalidRecord;
    }

    public String getUrlForInvalidRecord() {
        return urlForInvalidRecord;
    }

    public void setUrlForInvalidRecord(String urlForInvalidRecord) {
        this.urlForInvalidRecord = urlForInvalidRecord;
    }

    public String getTimeTaken() {
        return timeTaken;
    }

    public void setTimeTaken(String timeTaken) {
        this.timeTaken = timeTaken;
    }

    public Long getNoOfRecordUpdated() {
        return noOfRecordUpdated;
    }

    public void setNoOfRecordUpdated(Long noOfRecordUpdated) {
        this.noOfRecordUpdated = noOfRecordUpdated;
    }
}
