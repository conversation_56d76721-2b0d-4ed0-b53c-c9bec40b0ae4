package com.incs83.app.responsedto.v2.VMQResponse;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class VMQResponseDTO {

    private List<Map<String, Object>> objects = new ArrayList<>();

    public List<Map<String, Object>> getObjects() {
        return objects;
    }

    public void setObjects(List<Map<String, Object>> objects) {
        this.objects = objects;
    }
}
