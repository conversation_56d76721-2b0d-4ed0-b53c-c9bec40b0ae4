package com.incs83.app.responsedto.v2.isp;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class ISPDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ISPModel data;

    @Override
    public ISPModel getData() {
        return data;
    }

    public void setData(ISPModel data) {
        this.data = data;
    }
}
