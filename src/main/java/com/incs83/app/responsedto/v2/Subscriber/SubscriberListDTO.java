package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class SubscriberListDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private SubscriberData data;

    @Override
    public SubscriberData getData() {
        return data;
    }

    public void setData(SubscriberData data) {
        this.data = data;
    }

    public class SubscriberData {
        private Long totalCount;
        private ArrayList<SubscriberModel> objects;

        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public ArrayList<SubscriberModel> getObjects() {
            return objects;
        }

        public void setObjects(ArrayList<SubscriberModel> objects) {
            this.objects = objects;
        }

        public class SubscriberModel implements Comparable<SubscriberModel> {
            @ApiModelProperty(notes = "serialNumber", required = true)
            private String serialNumber;
            @ApiModelProperty(notes = "RGW MAC", required = true)
            private String rgwMAC;
            @ApiModelProperty(notes = "name", required = true)
            private String name;
            @ApiModelProperty(notes = "email", required = true)
            private String email;
            @ApiModelProperty(notes = "subscriberId", required = true)
            private String equipmentId;
            @ApiModelProperty(notes = "phoneNo", required = true)
            private String phoneNo;
            @ApiModelProperty(notes = "globalAccountNo", required = true)
            private String globalAccountNo;
            @ApiModelProperty(notes = "camsAccountNo", required = true)
            private String camsAccountNo;
            @ApiModelProperty(notes = "Allocated Bandwidth to Subscriber")
            private Double downLinkRate;
            @ApiModelProperty(notes = "Allocated Bandwidth to Subscriber")
            private Double upLinkRate;

            public String getSerialNumber() {
                return serialNumber;
            }

            public void setSerialNumber(String serialNumber) {
                this.serialNumber = serialNumber;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getEmail() {
                return email;
            }

            public void setEmail(String email) {
                this.email = email;
            }

            public String getEquipmentId() {
                return equipmentId;
            }

            public void setEquipmentId(String equipmentId) {
                this.equipmentId = equipmentId;
            }

            public String getPhoneNo() {
                return phoneNo;
            }

            public void setPhoneNo(String phoneNo) {
                this.phoneNo = phoneNo;
            }

            public String getGlobalAccountNo() {
                return globalAccountNo;
            }

            public void setGlobalAccountNo(String globalAccountNo) {
                this.globalAccountNo = globalAccountNo;
            }

            public String getCamsAccountNo() {
                return camsAccountNo;
            }

            public void setCamsAccountNo(String camsAccountNo) {
                this.camsAccountNo = camsAccountNo;
            }


            public Double getDownLinkRate() {
                return downLinkRate;
            }

            public void setDownLinkRate(Double downLinkRate) {
                this.downLinkRate = downLinkRate;
            }

            public Double getUpLinkRate() {
                return upLinkRate;
            }

            public void setUpLinkRate(Double upLinkRate) {
                this.upLinkRate = upLinkRate;
            }

            public String getRgwMAC() {
                return rgwMAC;
            }

            public void setRgwMAC(String rgwMAC) {
                this.rgwMAC = rgwMAC;
            }

            @Override
            public int compareTo(SubscriberModel o) {
                return this.getName().compareTo(o.getName());
            }
        }
    }
}
