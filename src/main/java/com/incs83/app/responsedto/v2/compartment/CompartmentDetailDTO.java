package com.incs83.app.responsedto.v2.compartment;

import com.incs83.app.responsedto.v2.cds.CompartmentModel;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class CompartmentDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data")
    private List<CompartmentModel> data;

    @Override
    public List<CompartmentModel> getData() {
        return data;
    }

    public void setData(List<CompartmentModel> data) {
        this.data = data;
    }
}
