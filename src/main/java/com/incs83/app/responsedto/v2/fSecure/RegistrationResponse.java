package com.incs83.app.responsedto.v2.fSecure;

import com.incs83.dto.ApiResponseDTO;

import java.util.List;

public class RegistrationResponse extends ApiResponseDTO {
    private List<Registration> data;

    @Override
    public List<Registration> getData() {
        return data;
    }

    public void setData(List<Registration> data) {
        this.data = data;
    }

    private class Registration {
        private Boolean valid;
        private List<String> protection;

        public Boolean getValid() {
            return valid;
        }

        public void setValid(Boolean valid) {
            this.valid = valid;
        }

        public List<String> getProtection() {
            return protection;
        }

        public void setProtection(List<String> protection) {
            this.protection = protection;
        }
    }
}
