package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class HealthDistDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<HealthDistData> data;

    @Override
    public ArrayList<HealthDistData> getData() {
        return data;
    }

    public void setData(ArrayList<HealthDistData> data) {
        this.data = data;
    }

    public class HealthDistData {
        @ApiModelProperty(notes = "lt60", required = true)
        private double lt60;
        @ApiModelProperty(notes = "lt70", required = true)
        private double lt70;
        @ApiModelProperty(notes = "lt80", required = true)
        private double lt80;
        @ApiModelProperty(notes = "lt90", required = true)
        private double lt90;
        @ApiModelProperty(notes = "gt90", required = true)
        private double gt90;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public double getLt60() {
            return lt60;
        }

        public void setLt60(double lt60) {
            this.lt60 = lt60;
        }

        public double getLt70() {
            return lt70;
        }

        public void setLt70(double lt70) {
            this.lt70 = lt70;
        }

        public double getLt80() {
            return lt80;
        }

        public void setLt80(double lt80) {
            this.lt80 = lt80;
        }

        public double getLt90() {
            return lt90;
        }

        public void setLt90(double lt90) {
            this.lt90 = lt90;
        }

        public double getGt90() {
            return gt90;
        }

        public void setGt90(double gt90) {
            this.gt90 = gt90;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }
}