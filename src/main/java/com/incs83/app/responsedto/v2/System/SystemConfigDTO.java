package com.incs83.app.responsedto.v2.System;

import com.incs83.app.entities.SystemConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class SystemConfigDTO implements Comparable<SystemConfigDTO>{
    private String id;
    private String title;
    private String description;
    private boolean applied;
    private List<ConfigPropertyDTO> props;

    public List<ConfigPropertyDTO> getProps() {
        return props;
    }

    public void setProps(List<ConfigPropertyDTO> configPropertyDTO) {
        this.props = configPropertyDTO;
    }

    public static SystemConfigDTO mapToConfigPropertyDto(SystemConfig systemConfig) {
        SystemConfigDTO scDTO = new SystemConfigDTO();

        if (Objects.nonNull(systemConfig)) {
            scDTO.id = systemConfig.getId();
            if (Objects.nonNull(systemConfig.getTitle()) && !systemConfig.getTitle().isEmpty()) {
                scDTO.title = systemConfig.getTitle();
            }
            if (Objects.nonNull(systemConfig.getDescription()) && !systemConfig.getDescription().isEmpty()) {
                scDTO.description = systemConfig.getDescription();
            }
            if (Objects.nonNull(systemConfig.getIsActive())) {
                scDTO.applied = systemConfig.getIsActive();
            }
            if (Objects.nonNull(systemConfig.getConfigProperty()) && !systemConfig.getConfigProperty().isEmpty()) {
                scDTO.props = ConfigPropertyDTO.getConfigPropertyList(systemConfig.getConfigProperty());
            }
        }
        return scDTO;
    }

    public static List<SystemConfigDTO> systemConfigList(List<SystemConfig> systemConfig) {
        List<SystemConfigDTO> sc = new ArrayList<>();

        if (Objects.nonNull(systemConfig) && !systemConfig.isEmpty()) {
            for (SystemConfig systemC : systemConfig) {
                sc.add(SystemConfigDTO.mapToConfigPropertyDto(systemC));
            }
        }

        return sc;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isApplied() {
        return applied;
    }

    public void setApplied(boolean applied) {
        this.applied = applied;
    }

    @Override
    public int compareTo(SystemConfigDTO o)  {
        return this.getTitle().compareTo(o.getTitle());
    }
}
