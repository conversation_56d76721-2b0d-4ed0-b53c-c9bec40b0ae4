package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DetachedEquipmentListDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private DetachedEquipmentListData data;

    @Override
    public DetachedEquipmentListData getData() {
        return data;
    }

    public void setData(DetachedEquipmentListData data) {
        this.data = data;
    }

    public class DetachedEquipmentListData {
        private Long totalCount;
        private ArrayList<DetachedEquipmentModel> objects;

        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public ArrayList<DetachedEquipmentModel> getObjects() {
            return objects;
        }

        public void setObjects(ArrayList<DetachedEquipmentModel> objects) {
            this.objects = objects;
        }

        public class DetachedEquipmentModel {
            @ApiModelProperty(notes = "serialNumber", required = true)
            private String serialNumber;
            @ApiModelProperty(notes = "name", required = true)
            private String serviceTelephoneNo;
            @ApiModelProperty(notes = "connectivityStatus", required = true)
            private String macAddress;
            @ApiModelProperty(notes = "connectivityStatus", required = true)
            private String equipmentId;

            public String getEquipmentId() {
                return equipmentId;
            }

            public void setEquipmentId(String equipmentId) {
                this.equipmentId = equipmentId;
            }

            public String getSerialNumber() {
                return serialNumber;
            }

            public void setSerialNumber(String serialNumber) {
                this.serialNumber = serialNumber;
            }

            public String getMacAddress() {
                return macAddress;
            }

            public void setMacAddress(String macAddress) {
                this.macAddress = macAddress;
            }

            public String getServiceTelephoneNo() {
                return serviceTelephoneNo;
            }

            public void setServiceTelephoneNo(String serviceTelephoneNo) {
                this.serviceTelephoneNo = serviceTelephoneNo;
            }

        }

    }
}
