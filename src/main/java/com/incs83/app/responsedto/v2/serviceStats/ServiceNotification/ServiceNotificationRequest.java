package com.incs83.app.responsedto.v2.serviceStats.ServiceNotification;


import com.incs83.auditor.AuditableData;

import java.util.ArrayList;

public class ServiceNotificationRequest implements AuditableData {
    private String id;
    private boolean active;
    private ArrayList emails;


    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public ArrayList getEmails() {
        return emails;
    }

    public void setEmails(ArrayList emails) {
        this.emails = emails;
    }
}
