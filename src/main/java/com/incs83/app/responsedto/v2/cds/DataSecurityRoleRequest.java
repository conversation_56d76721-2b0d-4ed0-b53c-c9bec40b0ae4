package com.incs83.app.responsedto.v2.cds;

import com.incs83.app.enums.DataSecurityType;
import com.incs83.auditor.AuditableData;

import java.util.List;

public class DataSecurityRoleRequest implements AuditableData {
    private List<DataSecurityType> dataSecurityEntitlement;

    public List<DataSecurityType> getDataSecurityEntitlement() {
        return dataSecurityEntitlement;
    }

    public void setDataSecurityEntitlement(List<DataSecurityType> dataSecurityEntitlement) {
        this.dataSecurityEntitlement = dataSecurityEntitlement;
    }
}
