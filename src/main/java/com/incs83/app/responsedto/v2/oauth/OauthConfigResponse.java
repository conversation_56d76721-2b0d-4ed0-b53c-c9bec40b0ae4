package com.incs83.app.responsedto.v2.oauth;

import com.incs83.app.entities.OauthConfig;

public class OauthConfigResponse {
    private String id;
    private String name;
    private String oauthProvider;
    private String clientId;
    private String redirectUri;
    private String wellKnownConfig;
    private String respType;
    private String scope;
    private String isp;
    private String defaultRoleId;
    private String authorizationURL;
    private boolean defaultProvider;

    public static OauthConfigResponse fromOauthConfig(OauthConfig oauthConfig) {
        OauthConfigResponse oauthConfigResponse = new OauthConfigResponse();
        oauthConfigResponse.id = oauthConfig.getId();
        oauthConfigResponse.name = oauthConfig.getName();
        oauthConfigResponse.oauthProvider = oauthConfig.getOauthProvider();
        oauthConfigResponse.clientId = oauthConfig.getClientId();
        oauthConfigResponse.redirectUri = oauthConfig.getRedirectUri();
        oauthConfigResponse.wellKnownConfig = oauthConfig.getWellKnownConfig();
        //oauthConfigResponse.respType = "code";
        //oauthConfigResponse.scope = "openid%20profile";
        oauthConfigResponse.respType = oauthConfig.getRespType();
        oauthConfigResponse.scope = oauthConfig.getScope();
        oauthConfigResponse.isp = oauthConfig.getIsp();
        oauthConfigResponse.defaultRoleId = oauthConfig.getRoleId();

        if (oauthConfig.getDefaultProvider() != null) {
            oauthConfigResponse.defaultProvider = oauthConfig.getDefaultProvider().booleanValue();
        }

        return oauthConfigResponse;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getOauthProvider() {
        return oauthProvider;
    }

    public String getClientId() {
        return clientId;
    }

    public String getRedirectUri() {
        return redirectUri;
    }

    public String getWellKnownConfig() {
        return wellKnownConfig;
    }

    public String getRespType() {
        return respType;
    }

    public String getScope() {
        return scope;
    }

    public String getIsp() {
        return isp;
    }

    public String getDefaultRoleId() {
        return defaultRoleId;
    }

    public String getAuthorizationURL() {
        return authorizationURL;
    }

    public void setAuthorizationURL(String authorizationURL) {
        this.authorizationURL = authorizationURL;
    }

    public boolean isDefaultProvider() {
        return defaultProvider;
    }
}