package com.incs83.app.responsedto.v2.UsersDTO;

import com.incs83.app.entities.User;
import com.incs83.enums.Gender;

import java.util.StringJoiner;

/**
 * Created by hari on 2/2/18.
 */
public class UserResponseDTO {
    private String id;
    private String firstName = "";
    private String lastName = "";
    private Gender gender = Gender.MALE;
    private String email = "";

    public UserResponseDTO() {
    }

    public String getId() {
        return id;
    }

    public UserResponseDTO setId(String id) {
        this.id = id;
        return this;
    }

    public String getFirstName() {
        return firstName;
    }

    public UserResponseDTO setFirstName(String firstName) {
        this.firstName = firstName;
        return this;
    }

    public String getLastName() {
        return lastName;
    }

    public UserResponseDTO setLastName(String lastName) {
        this.lastName = lastName;
        return this;
    }

    public Gender getGender() {
        return gender;
    }

    public UserResponseDTO setGender(Gender gender) {
        this.gender = gender;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public UserResponseDTO setEmail(String email) {
        this.email = email;
        return this;
    }

    public static UserResponseDTO mapFromUser(User user) {
        return new UserResponseDTO()
                .setId(user.getId())
                .setEmail(user.getEmail())
                .setFirstName(user.getFirstName())
                .setLastName(user.getLastName())
                .setGender(user.getGender());
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", "UserResponseDTO{", "}")
                .add("id='" + id + "'")
                .add("firstName='" + firstName + "'")
                .add("lastName='" + lastName + "'")
                .add("gender=" + gender)
                .add("email='" + email + "'")
                .toString();
    }
}
