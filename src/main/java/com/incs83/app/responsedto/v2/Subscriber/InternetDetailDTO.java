package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class InternetDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private InternetDetailData data;

    @Override
    public InternetDetailData getData() {
        return data;
    }

    public void setData(InternetDetailData data) {
        this.data = data;
    }

    public class InternetDetailData {
        @ApiModelProperty(notes = "Internet Utilization", required = true)
        private double internetUtilization;
        @ApiModelProperty(notes = "Subscriber Rate", required = true)
        private double subscriberRate;
        @ApiModelProperty(notes = "speedStats", required = true)
        private SpeedStats speedStats;
        @ApiModelProperty(notes = "UpLink Rate", required = true)
        private double upLinkRate;
        @ApiModelProperty(notes = "DownLink Rate", required = true)
        private double downLinkRate;

        public double getInternetUtilization() {
            return internetUtilization;
        }

        public void setInternetUtilization(double internetUtilization) {
            this.internetUtilization = internetUtilization;
        }

        public SpeedStats getSpeedStats() {
            return speedStats;
        }

        public void setSpeedStats(SpeedStats speedStats) {
            this.speedStats = speedStats;
        }

        public double getSubscriberRate() {
            return subscriberRate;
        }

        public void setSubscriberRate(double subscriberRate) {
            this.subscriberRate = subscriberRate;
        }

        public double getUpLinkRate() {
            return upLinkRate;
        }

        public void setUpLinkRate(double upLinkRate) {
            this.upLinkRate = upLinkRate;
        }

        public double getDownLinkRate() {
            return downLinkRate;
        }

        public void setDownLinkRate(double downLinkRate) {
            this.downLinkRate = downLinkRate;
        }
    }
}
