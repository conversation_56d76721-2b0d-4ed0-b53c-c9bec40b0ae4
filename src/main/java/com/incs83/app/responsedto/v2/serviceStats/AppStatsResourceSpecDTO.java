package com.incs83.app.responsedto.v2.serviceStats;

import com.incs83.app.responsedto.v2.serviceStats.ResourceSpecification.*;

import java.util.List;

public class AppStatsResourceSpecDTO {

    private RAMInfoDTO RAMInfo;
    private ROMInfoDTO ROMInfo;
    private LoadAverageInfoDTO loadAverageInfo;
    private NetworkInfoDTO networkInfo;
    private KafkaConnectDTO kafkaConnectInfo;
    private NGINXInfoDTO nginxStats;
    private VMQStatsDTO vmqStats;
    private TCPInfoDTO tcpInfo;
    private List<TCPStatsInfoDTO> tcpStats;

    public TCPInfoDTO getTcpInfo() {
        return tcpInfo;
    }

    public void setTcpInfo(TCPInfoDTO tcpInfo) {
        this.tcpInfo = tcpInfo;
    }

    public List<TCPStatsInfoDTO> getTcpStats() {
        return tcpStats;
    }

    public void setTcpStats(List<TCPStatsInfoDTO> tcpStats) {
        this.tcpStats = tcpStats;
    }

    public VMQStatsDTO getVmqStats() {
        return vmqStats;
    }

    public void setVmqStats(VMQStatsDTO vmqStats) {
        this.vmqStats = vmqStats;
    }

    public NGINXInfoDTO getNginxStats() {
        return nginxStats;
    }

    public void setNginxStats(NGINXInfoDTO nginxStats) {
        this.nginxStats = nginxStats;
    }

    public KafkaConnectDTO getKafkaConnectInfo() {
        return kafkaConnectInfo;
    }

    public void setKafkaConnectInfo(KafkaConnectDTO kafkaConnectInfo) {
        this.kafkaConnectInfo = kafkaConnectInfo;
    }

    public RAMInfoDTO getRAMInfo() {
        return RAMInfo;
    }

    public void setRAMInfo(RAMInfoDTO RAMInfo) {
        this.RAMInfo = RAMInfo;
    }

    public ROMInfoDTO getROMInfo() {
        return ROMInfo;
    }

    public void setROMInfo(ROMInfoDTO ROMInfo) {
        this.ROMInfo = ROMInfo;
    }

    public LoadAverageInfoDTO getLoadAverageInfo() {
        return loadAverageInfo;
    }

    public void setLoadAverageInfo(LoadAverageInfoDTO loadAverageInfo) {
        this.loadAverageInfo = loadAverageInfo;
    }

    public NetworkInfoDTO getNetworkInfo() {
        return networkInfo;
    }

    public void setNetworkInfo(NetworkInfoDTO networkInfo) {
        this.networkInfo = networkInfo;
    }
}
