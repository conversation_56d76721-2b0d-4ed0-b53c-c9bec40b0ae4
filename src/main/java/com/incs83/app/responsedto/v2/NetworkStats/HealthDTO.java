package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class HealthDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<HealthData> data;

    @Override
    public ArrayList<HealthData> getData() {
        return data;
    }

    public void setData(ArrayList<HealthData> data) {
        this.data = data;
    }

    public class HealthData {
        @ApiModelProperty(notes = "avgWifiHealth", required = true)
        private double avgWifiHealth;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public double getAvgWifiHealth() {
            return avgWifiHealth;
        }

        public void setAvgWifiHealth(double avgWifiHealth) {
            this.avgWifiHealth = avgWifiHealth;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }
}
