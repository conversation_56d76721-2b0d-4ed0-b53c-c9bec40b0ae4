package com.incs83.app.responsedto.v2.Profile;

import com.incs83.app.common.v2.ScheduleDetails;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class SubscriberProfileDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private SubscriberProfileData data;

    @Override
    public SubscriberProfileData getData() {
        return data;
    }

    public void setData(SubscriberProfileData data) {
        this.data = data;
    }

    public class SubscriberProfileData {
        private String id;
        private String profileName;
        private List<ProfileDevicesDTO> devices;
        private boolean currentState;
        private boolean onDemandState;
        private String subscriberId;
        private boolean isScheduleActivated;
        private List<ScheduleDetails> scheduleDetails;
        private String nextActionDetails;
        private List<Integer> contentFilters;
        private String ispName;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getProfileName() {
            return profileName;
        }

        public void setProfileName(String profileName) {
            this.profileName = profileName;
        }

        public List<ProfileDevicesDTO> getDevices() {
            return devices;
        }

        public void setDevices(List<ProfileDevicesDTO> devices) {
            this.devices = devices;
        }

        public boolean isCurrentState() {
            return currentState;
        }

        public void setCurrentState(boolean currentState) {
            this.currentState = currentState;
        }

        public boolean isOnDemandState() {
            return onDemandState;
        }

        public void setOnDemandState(boolean onDemandState) {
            this.onDemandState = onDemandState;
        }

        public String getSubscriberId() {
            return subscriberId;
        }

        public void setSubscriberId(String subscriberId) {
            this.subscriberId = subscriberId;
        }

        public boolean isScheduleActivated() {
            return isScheduleActivated;
        }

        public void setScheduleActivated(boolean scheduleActivated) {
            isScheduleActivated = scheduleActivated;
        }

        public List<ScheduleDetails> getScheduleDetails() {
            return scheduleDetails;
        }

        public void setScheduleDetails(List<ScheduleDetails> scheduleDetails) {
            this.scheduleDetails = scheduleDetails;
        }

        public String getNextActionDetails() {
            return nextActionDetails;
        }

        public SubscriberProfileData setNextActionDetails(String nextActionDetails) {
            this.nextActionDetails = nextActionDetails;
            return this;
        }

        public List<Integer> getContentFilters() {
            return contentFilters;
        }

        public void setContentFilters(List<Integer> contentFilters) {
            this.contentFilters = contentFilters;
        }

        public String getIspName() {
            return ispName;
        }

        public void setIspName(String ispName) {
            this.ispName = ispName;
        }
    }
}
