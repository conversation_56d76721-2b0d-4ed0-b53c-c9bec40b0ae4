package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceListDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<DeviceDetailListData> data;

    public ArrayList<DeviceDetailListData> getData() {
        return data;
    }

    public void setData(ArrayList<DeviceDetailListData> data) {
        this.data = data;
    }

     public class DeviceDetailListData {
        @ApiModelProperty(notes = "Device name", required = true)
        private String name;
        @ApiModelProperty(notes = "Device ip address", required = true)
        private String ip;
        @ApiModelProperty(notes = "Device MAC", required = true)
        private String mac;
        @ApiModelProperty(notes = "Device vendor name", required = true)
        private String vendor;
        @ApiModelProperty(notes = "Device ssid", required = true)
        private String ssid;
        @ApiModelProperty(notes = "Device rssi", required = true)
        private double rssi;
        @ApiModelProperty(notes = "Device connectivity status", required = true)
        private String connectivityStatus;
        @ApiModelProperty(notes = "Device status ON/OFF", required = true)
        private boolean internetOn;
        @ApiModelProperty(notes = "Device Type", required = true)
        private String deviceType;
        @ApiModelProperty(notes = "Device band", required = true)
        private String band;
        @ApiModelProperty(notes = "Device connected To", required = true)
        private String connectedTo;
        @ApiModelProperty(notes = "Device last reported at", required = true)
        private Long lastReportedAt;

        public Long getLastReportedAt() { return lastReportedAt;}

        public void setLastReportedAt(Long lastReportedAt) { this.lastReportedAt = lastReportedAt; }

        public String getConnectedTo() {
            return connectedTo;
        }

        public void setConnectedTo(String connectedTo) {
            this.connectedTo = connectedTo;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public boolean isInternetOn() {
            return internetOn;
        }

        public void setInternetOn(boolean internetOn) {
            this.internetOn = internetOn;
        }

        public String getDeviceType() {
            return deviceType;
        }

        public void setDeviceType(String deviceType) {
            this.deviceType = deviceType;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getMac() {
            return mac;
        }

        public void setMac(String mac) {
            this.mac = mac;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public String getSsid() {
            return ssid;
        }

        public void setSsid(String ssid) {
            this.ssid = ssid;
        }

        public double getRssi() {
            return rssi;
        }

        public void setRssi(double rssi) {
            this.rssi = rssi;
        }

        public String getConnectivityStatus() {
            return connectivityStatus;
        }

        public void setConnectivityStatus(String connectivityStatus) {
            this.connectivityStatus = connectivityStatus;
        }
    }
}
