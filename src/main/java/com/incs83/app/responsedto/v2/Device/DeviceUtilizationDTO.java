package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.Example;
import io.swagger.annotations.ExampleProperty;

import java.util.HashMap;
import java.util.List;

public class DeviceUtilizationDTO extends ApiResponseDTO {
    private DeviceUtilizationData data;

    @Override
    public DeviceUtilizationData getData() {
        return data;
    }

    public void setData(DeviceUtilizationData data) {
        this.data = data;
    }

    public class DeviceUtilizationData{
        @ApiModelProperty(notes = "total downlink bytes", required = true)
        private double totalDownLinkBytes;
        @ApiModelProperty(notes = "total uplink bytes", required = true)
        private double totalUpLinkBytes;
        // @ApiModelProperty(example = "[{'timestamp':'0','tx':'0','rx':'0','uploadPercentage':'0','downloadPercentage':'0','downLinkRate':'0','upLinkRate':'0'}]") // Single quotes cause json file format error
        private List<HashMap<String,Object>> datapoints;

        public double getTotalDownLinkBytes() {
            return totalDownLinkBytes;
        }

        public void setTotalDownLinkBytes(double totalDownLinkBytes) {
            this.totalDownLinkBytes = totalDownLinkBytes;
        }

        public double getTotalUpLinkBytes() {
            return totalUpLinkBytes;
        }

        public void setTotalUpLinkBytes(double totalUpLinkBytes) {
            this.totalUpLinkBytes = totalUpLinkBytes;
        }

        public List<HashMap<String, Object>> getDatapoints() {
            return datapoints;
        }

        public void setDatapoints(List<HashMap<String, Object>> datapoints) {
            this.datapoints = datapoints;
        }
    }

}
