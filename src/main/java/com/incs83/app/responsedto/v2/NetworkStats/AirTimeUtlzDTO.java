package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class AirTimeUtlzDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<AirTimeUtlzData> data;

    @Override
    public ArrayList<AirTimeUtlzData> getData() {
        return data;
    }

    public void setData(ArrayList<AirTimeUtlzData> data) {
        this.data = data;
    }

    public class AirTimeUtlzData {
        @ApiModelProperty(notes = "avgRadioOccupancy6G", required = true)
        private double avgRadioOccupancy6G;
        @ApiModelProperty(notes = "avgRadioOccupancy5G", required = true)
        private double avgRadioOccupancy5G;
        @ApiModelProperty(notes = "avgRadioOccupancy24G", required = true)
        private double avgRadioOccupancy24G;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public double getAvgRadioOccupancy6G() {
            return avgRadioOccupancy6G;
        }

        public void setAvgRadioOccupancy6G(double avgRadioOccupancy6G) {
            this.avgRadioOccupancy6G = avgRadioOccupancy6G;
        }

        public double getAvgRadioOccupancy5G() {
            return avgRadioOccupancy5G;
        }

        public void setAvgRadioOccupancy5G(double avgRadioOccupancy5G) {
            this.avgRadioOccupancy5G = avgRadioOccupancy5G;
        }

        public double getAvgRadioOccupancy24G() {
            return avgRadioOccupancy24G;
        }

        public void setAvgRadioOccupancy24G(double avgRadioOccupancy24G) {
            this.avgRadioOccupancy24G = avgRadioOccupancy24G;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }
}
