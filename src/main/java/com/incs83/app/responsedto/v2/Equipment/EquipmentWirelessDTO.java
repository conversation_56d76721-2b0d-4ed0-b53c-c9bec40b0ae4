package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class EquipmentWirelessDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private EquipmentWirelessData data;

    @Override
    public EquipmentWirelessData getData() {
        return data;
    }

    public void setData(EquipmentWirelessData data) {
        this.data = data;
    }

    public class EquipmentWirelessData {
        @ApiModelProperty(notes = "channelWidth", required = true)
        private long channelWidth;
        @ApiModelProperty(notes = "channel", required = true)
        private long channel;
        @ApiModelProperty(notes = "operatingStandards", required = true)
        private String operatingStandards;
        @ApiModelProperty(notes = "mode", required = true)
        private String mode;
        @ApiModelProperty(notes = "enable", required = true)
        private long enable;
        @ApiModelProperty(notes = "band", required = true)
        private String band;
        @ApiModelProperty(notes = "deviceAssociated", required = true)
        private long deviceAssociated;
        @ApiModelProperty(notes = "txBytes", required = true)
        private double txBytes;
        @ApiModelProperty(notes = "rxBytes", required = true)
        private double rxBytes;
        @ApiModelProperty(notes = "isBackhaul", required = true)
        private boolean isBackhaul;

        public long getChannelWidth() {
            return channelWidth;
        }

        public void setChannelWidth(long channelWidth) {
            this.channelWidth = channelWidth;
        }

        public long getChannel() {
            return channel;
        }

        public void setChannel(long channel) {
            this.channel = channel;
        }

        public String getOperatingStandards() {
            return operatingStandards;
        }

        public void setOperatingStandards(String operatingStandards) {
            this.operatingStandards = operatingStandards;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }

        public long getEnable() {
            return enable;
        }

        public void setEnable(long enable) {
            this.enable = enable;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public long getDeviceAssociated() {
            return deviceAssociated;
        }

        public void setDeviceAssociated(long deviceAssociated) {
            this.deviceAssociated = deviceAssociated;
        }

        public double getTxBytes() {
            return txBytes;
        }

        public void setTxBytes(double txBytes) {
            this.txBytes = txBytes;
        }

        public double getRxBytes() {
            return rxBytes;
        }

        public void setRxBytes(double rxBytes) {
            this.rxBytes = rxBytes;
        }

        public boolean getIsBackhaul() {
            return isBackhaul;
        }

        public void setIsBackhaul(boolean isBackhaul) {
            this.isBackhaul = isBackhaul;
        }
    }
}
