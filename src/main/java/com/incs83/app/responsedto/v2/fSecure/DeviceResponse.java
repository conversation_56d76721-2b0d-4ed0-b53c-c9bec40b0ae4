package com.incs83.app.responsedto.v2.fSecure;

import com.incs83.dto.ApiResponseDTO;

import java.util.List;

public class DeviceResponse extends ApiResponseDTO {
    private List<DeviceSchema> data;

    @Override
    public List<DeviceSchema> getData() {
        return data;
    }

    public void setData(List<DeviceSchema> data) {
        this.data = data;
    }

    private class DeviceSchema{
        private boolean active;
        private String ipAddress;
        private String friendlyName;
        private String hostname;
        private Long firstSeen;
        private Long lastSeen;
        private String macAddress;
        private String profileId;
        private String type;
        private String blocked;
        private Long blockInternetDuration;
        private Long blockInternetExpireUTC;
        private Protection protection;

        public boolean isActive() {
            return active;
        }

        public void setActive(boolean active) {
            this.active = active;
        }

        public String getIpAddress() {
            return ipAddress;
        }

        public void setIpAddress(String ipAddress) {
            this.ipAddress = ipAddress;
        }

        public String getFriendlyName() {
            return friendlyName;
        }

        public void setFriendlyName(String friendlyName) {
            this.friendlyName = friendlyName;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public Long getFirstSeen() {
            return firstSeen;
        }

        public void setFirstSeen(Long firstSeen) {
            this.firstSeen = firstSeen;
        }

        public Long getLastSeen() {
            return lastSeen;
        }

        public void setLastSeen(Long lastSeen) {
            this.lastSeen = lastSeen;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getProfileId() {
            return profileId;
        }

        public void setProfileId(String profileId) {
            this.profileId = profileId;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getBlocked() {
            return blocked;
        }

        public void setBlocked(String blocked) {
            this.blocked = blocked;
        }

        public Protection getProtection() {
            return protection;
        }

        public void setProtection(Protection protection) {
            this.protection = protection;
        }

        public Long getBlockInternetDuration() {
            return blockInternetDuration;
        }

        public void setBlockInternetDuration(Long blockInternetDuration) {
            this.blockInternetDuration = blockInternetDuration;
        }

        public Long getBlockInternetExpireUTC() {
            return blockInternetExpireUTC;
        }

        public void setBlockInternetExpireUTC(Long blockInternetExpireUTC) {
            this.blockInternetExpireUTC = blockInternetExpireUTC;
        }

        private class Protection{
            private Boolean browsing;
            private Boolean tracking;
            private Boolean iot;
            private Boolean blockInternet;

            public Boolean getBrowsing() {
                return browsing;
            }

            public void setBrowsing(Boolean browsing) {
                this.browsing = browsing;
            }

            public Boolean getTracking() {
                return tracking;
            }

            public void setTracking(Boolean tracking) {
                this.tracking = tracking;
            }

            public Boolean getIot() {
                return iot;
            }

            public void setIot(Boolean iot) {
                this.iot = iot;
            }

            public Boolean getBlockInternet() {
                return blockInternet;
            }

            public void setBlockInternet(Boolean blockInternet) {
                this.blockInternet = blockInternet;
            }
        }
    }



}
