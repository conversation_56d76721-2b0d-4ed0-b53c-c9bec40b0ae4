package com.incs83.app.responsedto.v2.isp;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.IOException;
import java.util.ArrayList;

public class ISPDTOList extends ApiResponseDTO implements DataSerializable {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<ISPModel> data;

    @Override
    public ArrayList<ISPModel> getData() {
        return data;
    }

    public void setData(ArrayList<ISPModel> data) {
        this.data = data;
    }

    @Override
    public void writeData(ObjectDataOutput out) throws IOException {
        super.writeData(out);
        out.writeObject(data);
    }

    @Override
    public void readData(ObjectDataInput in) throws IOException {
        super.readData(in);
        data=in.readObject();
    }
}
