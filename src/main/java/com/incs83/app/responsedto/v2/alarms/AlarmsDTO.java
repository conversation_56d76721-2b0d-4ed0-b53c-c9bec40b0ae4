package com.incs83.app.responsedto.v2.alarms;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class AlarmsDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<AlarmData> data;

    @Override
    public ArrayList<AlarmData> getData() {
        return data;
    }

    public void setData(ArrayList<AlarmData> data) {
        this.data = data;
    }

    public class AlarmData {
        @ApiModelProperty(notes = "Alarm Desc")
        private String desc;
        @ApiModelProperty(notes = "Severity Level (0 is lowest)")
        private int severity;
        @ApiModelProperty(notes = "Alarm Type (Device/Equipment)")
        private String alarmType;
        @ApiModelProperty(notes = "MAC Address of Station, in case for alarm is for Station")
        private String mac;
        @ApiModelProperty(notes = "Serial Number of Station, in case for alarm is for Equipment")
        private String serial;
        @ApiModelProperty(notes = "Name of equipment/Device")
        private String name;
        @ApiModelProperty(notes = "Type of the equipment GATEWAY/EXTENDER")
        private String equipmentType;

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public int getSeverity() {
            return severity;
        }

        public void setSeverity(int severity) {
            this.severity = severity;
        }

        public String getAlarmType() {
            return alarmType;
        }

        public void setAlarmType(String alarmType) {
            this.alarmType = alarmType;
        }

        public String getMac() {
            return mac;
        }

        public void setMac(String mac) {
            this.mac = mac;
        }

        public String getSerial() {
            return serial;
        }

        public void setSerial(String serial) {
            this.serial = serial;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getEquipmentType() {
            return equipmentType;
        }

        public void setEquipmentType(String equipmentType) {
            this.equipmentType = equipmentType;
        }
    }
}

