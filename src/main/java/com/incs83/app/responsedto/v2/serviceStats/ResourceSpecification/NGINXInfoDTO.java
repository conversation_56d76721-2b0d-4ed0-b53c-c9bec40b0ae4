package com.incs83.app.responsedto.v2.serviceStats.ResourceSpecification;

public class NGINXInfoDTO {
    private String activeConnections;
    private ServerInfo serverInfo;

    public String getActiveConnections() {
        return activeConnections;
    }

    public void setActiveConnections(String activeConnections) {
        this.activeConnections = activeConnections;
    }

    public ServerInfo getServerInfo() {
        return serverInfo;
    }

    public void setServerInfo(ServerInfo serverInfo) {
        this.serverInfo = serverInfo;
    }

    public class ServerInfo{
        private String requests;
        private String accepts;
        private String handled;

        public String getRequests() {
            return requests;
        }

        public void setRequests(String requests) {
            this.requests = requests;
        }

        public String getAccepts() {
            return accepts;
        }

        public void setAccepts(String accepts) {
            this.accepts = accepts;
        }

        public String getHandled() {
            return handled;
        }

        public void setHandled(String handled) {
            this.handled = handled;
        }
    }
}
