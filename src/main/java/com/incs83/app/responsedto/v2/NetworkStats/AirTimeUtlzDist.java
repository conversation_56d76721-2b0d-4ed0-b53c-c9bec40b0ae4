package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class AirTimeUtlzDist extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<AirTimeUtlzDistData> data;

    @Override
    public ArrayList<AirTimeUtlzDistData> getData() {
        return data;
    }

    public void setData(ArrayList<AirTimeUtlzDistData> data) {
        this.data = data;
    }

    public class AirTimeUtlzDistData {
        @ApiModelProperty(notes = "high", required = true)
        private double high;
        @ApiModelProperty(notes = "veryLow", required = true)
        private double veryLow;
        @ApiModelProperty(notes = "low", required = true)
        private double low;
        @ApiModelProperty(notes = "medium", required = true)
        private double medium;
        @ApiModelProperty(notes = "veryHigh", required = true)
        private double veryHigh;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public double getHigh() {
            return high;
        }

        public void setHigh(double high) {
            this.high = high;
        }

        public double getVeryLow() {
            return veryLow;
        }

        public void setVeryLow(double veryLow) {
            this.veryLow = veryLow;
        }

        public double getLow() {
            return low;
        }

        public void setLow(double low) {
            this.low = low;
        }

        public double getMedium() {
            return medium;
        }

        public void setMedium(double medium) {
            this.medium = medium;
        }

        public double getVeryHigh() {
            return veryHigh;
        }

        public void setVeryHigh(double veryHigh) {
            this.veryHigh = veryHigh;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }
}
