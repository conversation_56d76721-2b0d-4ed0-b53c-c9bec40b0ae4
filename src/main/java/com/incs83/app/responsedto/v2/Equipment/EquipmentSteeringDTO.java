package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentSteeringDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentSteeringData> data;

    @Override
    public ArrayList<EquipmentSteeringData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentSteeringData> data) {
        this.data = data;
    }

    public class EquipmentSteeringData implements Comparable<EquipmentSteeringData> {
        @ApiModelProperty(notes = "actualBand", required = true)
        private String actualBand;
        @ApiModelProperty(notes = "log", required = true)
        private String log;
        @ApiModelProperty(notes = "toRssi", required = true)
        private String toRssi;
        @ApiModelProperty(notes = "fromRssi", required = true)
        private String fromRssi;
        @ApiModelProperty(notes = "actualRssi", required = true)
        private String actualRssi;
        @ApiModelProperty(notes = "steeringType", required = true)
        private String steeringType;
        @ApiModelProperty(notes = "steeringEnd", required = true)
        private String steeringEnd;
        @ApiModelProperty(notes = "friendlyName", required = true)
        private String friendlyName;
        @ApiModelProperty(notes = "hostname", required = true)
        private String hostname;
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "actualDevice", required = true)
        private String actualDevice;
        @ApiModelProperty(notes = "vendor", required = true)
        private String vendor;
        @ApiModelProperty(notes = "fromDevice", required = true)
        private String fromDevice;
        @ApiModelProperty(notes = "fromBand", required = true)
        private String fromBand;
        @ApiModelProperty(notes = "time", required = true)
        private long time;
        @ApiModelProperty(notes = "event", required = true)
        private String event;
        @ApiModelProperty(notes = "toDevice", required = true)
        private String toDevice;
        @ApiModelProperty(notes = "toBand", required = true)
        private String toBand;
        @ApiModelProperty(notes = "oPhyrate", required = true)
        private String oPhyrate;
        @ApiModelProperty(notes = "tPhyrate", required = true)
        private String tPhyrate;
        @ApiModelProperty(notes = "oAirusage", required = true)
        private String oAirusage;
        @ApiModelProperty(notes = "tAirusage", required = true)
        private String tAirusage;
        @ApiModelProperty(notes = "oFat", required = true)
        private String oFat;
        @ApiModelProperty(notes = "iFat", required = true)
        private String iFat;
        @ApiModelProperty(notes = "tFat", required = true)
        private String tFat;
        @ApiModelProperty(notes = "steeringTime", required = true)
        private long steeringTime;
        @ApiModelProperty(notes = "toChannel", required = true)
        private int toChannel;
        @ApiModelProperty(notes = "fromChannel", required = true)
        private int fromChannel;
        @ApiModelProperty(notes = "actualChannel", required = true)
        private int actualChannel;

        public String getActualBand() {
            return actualBand;
        }

        public void setActualBand(String actualBand) {
            this.actualBand = actualBand;
        }

        public String getLog() {
            return log;
        }

        public void setLog(String log) {
            this.log = log;
        }

        public String getToRssi() {
            return toRssi;
        }

        public void setToRssi(String toRssi) {
            this.toRssi = toRssi;
        }

        public String getFromRssi() {
            return fromRssi;
        }

        public void setFromRssi(String fromRssi) {
            this.fromRssi = fromRssi;
        }

        public String getActualRssi() {
            return actualRssi;
        }

        public void setActualRssi(String actualRssi) {
            this.actualRssi = actualRssi;
        }

        public String getSteeringType() {
            return steeringType;
        }

        public void setSteeringType(String steeringType) {
            this.steeringType = steeringType;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getActualDevice() {
            return actualDevice;
        }

        public void setActualDevice(String actualDevice) {
            this.actualDevice = actualDevice;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public String getFromDevice() {
            return fromDevice;
        }

        public void setFromDevice(String fromDevice) {
            this.fromDevice = fromDevice;
        }

        public String getFromBand() {
            return fromBand;
        }

        public void setFromBand(String fromBand) {
            this.fromBand = fromBand;
        }

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public String getEvent() {
            return event;
        }

        public void setEvent(String event) {
            this.event = event;
        }

        public String getToDevice() {
            return toDevice;
        }

        public void setToDevice(String toDevice) {
            this.toDevice = toDevice;
        }

        public String getToBand() {
            return toBand;
        }

        public void setToBand(String toBand) {
            this.toBand = toBand;
        }

        public String getFriendlyName() {
            return friendlyName;
        }

        public void setFriendlyName(String friendlyName) {
            this.friendlyName = friendlyName;
        }

        public String getSteeringEnd() {
            return steeringEnd;
        }

        public void setSteeringEnd(String steeringEnd) {
            this.steeringEnd = steeringEnd;
        }

        public String getoPhyrate() {
            return oPhyrate;
        }

        public void setoPhyrate(String oPhyrate) {
            this.oPhyrate = oPhyrate;
        }

        public String gettPhyrate() {
            return tPhyrate;
        }

        public void settPhyrate(String tPhyrate) {
            this.tPhyrate = tPhyrate;
        }

        public String getoAirusage() {
            return oAirusage;
        }

        public void setoAirusage(String oAirusage) {
            this.oAirusage = oAirusage;
        }

        public String gettAirusage() {
            return tAirusage;
        }

        public void settAirusage(String tAirusage) {
            this.tAirusage = tAirusage;
        }

        public String getoFat() {
            return oFat;
        }

        public void setoFat(String oFat) {
            this.oFat = oFat;
        }

        public String getiFat() {
            return iFat;
        }

        public void setiFat(String iFat) {
            this.iFat = iFat;
        }

        public String gettFat() {
            return tFat;
        }

        public void settFat(String tFat) {
            this.tFat = tFat;
        }

        public long getSteeringTime() {
            return steeringTime;
        }

        public void setSteeringTime(long steeringTime) {
            this.steeringTime = steeringTime;
        }

        public int getToChannel() {
            return toChannel;
        }

        public void setToChannel(int toChannel) {
            this.toChannel = toChannel;
        }

        public int getFromChannel() {
            return fromChannel;
        }

        public void setFromChannel(int fromChannel) {
            this.fromChannel = fromChannel;
        }

        public int getActualChannel() {
            return actualChannel;
        }

        public void setActualChannel(int actualChannel) {
            this.actualChannel = actualChannel;
        }

        @Override
        public int compareTo(EquipmentSteeringData o) {
            return this.time<o.time?-1: this.time>o.time?1:0;
        }
    }
}
