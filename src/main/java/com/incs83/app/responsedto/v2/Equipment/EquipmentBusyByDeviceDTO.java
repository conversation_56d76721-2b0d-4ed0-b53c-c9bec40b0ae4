package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentBusyByDeviceDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentBusyByDeviceData> data;

    @Override
    public ArrayList<EquipmentBusyByDeviceData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentBusyByDeviceData> data) {
        this.data = data;
    }

    public class EquipmentBusyByDeviceData {
        private String name;
        private ArrayList<BusyByDevice> data;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public ArrayList<BusyByDevice> getData() {
            return data;
        }

        public void setData(ArrayList<BusyByDevice> data) {
            this.data = data;
        }

        public class BusyByDevice {
            @ApiModelProperty(notes = "macAddress", required = true)
            private String macAddress;
            @ApiModelProperty(notes = "name", required = true)
            private String name;
            @ApiModelProperty(notes = "radio", required = true)
            private String radio;
            @ApiModelProperty(notes = "airTimePercentage", required = true)
            private double airTimePercentage;
            @ApiModelProperty(notes = "timestamp", required = true)
            private long timestamp;

            public String getMacAddress() {
                return macAddress;
            }

            public void setMacAddress(String macAddress) {
                this.macAddress = macAddress;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getRadio() {
                return radio;
            }

            public void setRadio(String radio) {
                this.radio = radio;
            }

            public double getAirTimePercentage() {
                return airTimePercentage;
            }

            public void setAirTimePercentage(double airTimePercentage) {
                this.airTimePercentage = airTimePercentage;
            }

            public long getTimestamp() {
                return timestamp;
            }

            public void setTimestamp(long timestamp) {
                this.timestamp = timestamp;
            }
        }
    }
}
