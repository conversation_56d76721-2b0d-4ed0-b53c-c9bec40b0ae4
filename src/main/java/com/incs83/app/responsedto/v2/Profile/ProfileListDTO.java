package com.incs83.app.responsedto.v2.Profile;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class ProfileListDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<ProfileListData> data;

    @Override
    public ArrayList<ProfileListData> getData() {
        return data;
    }

    public void setData(ArrayList<ProfileListData> data) {
        this.data = data;
    }

    public class ProfileListData implements Comparable<ProfileListData> {
        @ApiModelProperty(notes = "profileName", required = true)
        private String profileName;
        @ApiModelProperty(notes = "totalBandwidth", required = true)
        private double totalBandwidth;
        @ApiModelProperty(notes = "profileId", required = true)
        private String profileId;

        public String getProfileName() {
            return profileName;
        }

        public void setProfileName(String profileName) {
            this.profileName = profileName;
        }

        public double getTotalBandwidth() {
            return totalBandwidth;
        }

        public void setTotalBandwidth(double totalBandwidth) {
            this.totalBandwidth = totalBandwidth;
        }

        public String getProfileId() {
            return profileId;
        }

        public void setProfileId(String profileId) {
            this.profileId = profileId;
        }

        @Override
        public int compareTo(ProfileListData o) {
            if(totalBandwidth==o.totalBandwidth)
                return 0;
            else if(totalBandwidth>o.totalBandwidth)
                return 1;
            else
                return -1;
        }
    }
}
