package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

public class DeviceSteeringLogDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private Set<DeviceSteeringLogData> data;

    @Override
    public Set<DeviceSteeringLogData> getData() {
        return data;
    }

    public void setData(Set<DeviceSteeringLogData> data) {
        this.data = data;
    }

    public class DeviceSteeringLogData implements Comparable {
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "log", required = true)
        private String log;
        @ApiModelProperty(notes = "vendor", required = true)
        private String vendor;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getLog() {
            return log;
        }

        public void setLog(String log) {
            this.log = log;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            DeviceSteeringLogData that = (DeviceSteeringLogData) o;

            if (timestamp != that.timestamp) return false;
            return macAddress != null ? macAddress.equals(that.macAddress) : that.macAddress == null;
        }

        @Override
        public int hashCode() {
            int result = macAddress != null ? macAddress.hashCode() : 0;
            result = 31 * result + (log != null ? log.hashCode() : 0);
            result = 31 * result + (vendor != null ? vendor.hashCode() : 0);
            result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
            return result;
        }

        @Override
        public int compareTo(Object o) {
            DeviceSteeringLogDTO.DeviceSteeringLogData steeringLogData = (DeviceSteeringLogDTO.DeviceSteeringLogData) o;

            return this.timestamp == steeringLogData.getTimestamp() ? 0 : Integer.valueOf(String.valueOf(this.timestamp - steeringLogData.getTimestamp()));
        }
    }
}
