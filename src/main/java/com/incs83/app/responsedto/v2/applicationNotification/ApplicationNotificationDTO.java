package com.incs83.app.responsedto.v2.applicationNotification;

import java.util.HashMap;
import java.util.List;

public class ApplicationNotificationDTO {
    private List<HashMap<String, Object>> upcoming;
    private List<HashMap<String, Object>> current;
    private List<HashMap<String, Object>> history;

    public List<HashMap<String, Object>> getUpcoming() {
        return upcoming;
    }

    public void setUpcoming(List<HashMap<String, Object>> upcoming) {
        this.upcoming = upcoming;
    }

    public List<HashMap<String, Object>> getCurrent() {
        return current;
    }

    public void setCurrent(List<HashMap<String, Object>> current) {
        this.current = current;
    }

    public List<HashMap<String, Object>> getHistory() {
        return history;
    }

    public void setHistory(List<HashMap<String, Object>> history) {
        this.history = history;
    }
}
