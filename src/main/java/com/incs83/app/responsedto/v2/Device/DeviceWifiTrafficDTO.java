package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceWifiTrafficDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    ArrayList<DeviceWifiTrafficData> data;

    public ArrayList<DeviceWifiTrafficData> getData() {
        return data;
    }

    public void setData(ArrayList<DeviceWifiTrafficData> data) {
        this.data = data;
    }

    public class DeviceWifiTrafficData {
        @ApiModelProperty(notes = "MAC address", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "wifiTrafficPercentage", required = true)
        private double wifiTrafficPercentage;
        @ApiModelProperty(notes = "Device name", required = true)
        private String name;
        @ApiModelProperty(notes = "traffic", required = true)
        private double traffic;
        @ApiModelProperty(notes = "fromDSHost")
        private boolean fromDSHost;

        public boolean isFromDSHost() {
            return fromDSHost;
        }

        public void setFromDSHost(boolean fromDSHost) {
            this.fromDSHost = fromDSHost;
        }

        public double getTraffic() {
            return traffic;
        }

        public void setTraffic(double traffic) {
            this.traffic = traffic;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public double getWifiTrafficPercentage() {
            return wifiTrafficPercentage;
        }

        public void setWifiTrafficPercentage(double wifiTrafficPercentage) {
            this.wifiTrafficPercentage = wifiTrafficPercentage;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
