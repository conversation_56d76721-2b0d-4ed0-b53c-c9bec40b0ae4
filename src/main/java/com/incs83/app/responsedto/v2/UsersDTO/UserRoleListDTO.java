package com.incs83.app.responsedto.v2.UsersDTO;

import io.swagger.annotations.ApiModelProperty;

public class UserRoleListDTO {
    @ApiModelProperty(notes = "id", required = true)
    private String id;
    @ApiModelProperty(notes = "role", required = true)
    private RoleDTO role;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public RoleDTO getRole() {
        return role;
    }

    public void setRole(RoleDTO role) {
        this.role = role;
    }
}
