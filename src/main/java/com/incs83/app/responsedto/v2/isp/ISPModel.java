package com.incs83.app.responsedto.v2.isp;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;

import java.io.IOException;

public class ISPModel implements DataSerializable {
    private String id;
    private String name;
    private String displayName;
    private String description;
    private boolean defaultType;
    private String groupId;

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public boolean isDefaultType() {
        return defaultType;
    }

    public void setDefaultType(boolean defaultType) {
        this.defaultType = defaultType;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    @Override
    public void writeData(ObjectDataOutput out) throws IOException {
        out.writeUTF(id);
        out.writeUTF(name);
        out.writeUTF(displayName);
        out.writeUTF(description);
        out.writeBoolean(defaultType);
    }

    @Override
    public void readData(ObjectDataInput in) throws IOException {
        id=in.readUTF();
        name=in.readUTF();
        displayName=in.readUTF();
        description=in.readUTF();
        defaultType=in.readBoolean();
    }
}
