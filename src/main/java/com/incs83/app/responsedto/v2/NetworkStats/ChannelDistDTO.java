package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class ChannelDistDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<ChannelData> data;

    @Override
    public ArrayList<ChannelData> getData() {
        return data;
    }

    public void setData(ArrayList<ChannelData> data) {
        this.data = data;
    }

    public class ChannelData {
        @ApiModelProperty(notes = "WiFi 2.4G Channel 1", required = true)
        private long ch1;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 2", required = true)
        private long ch2;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 3", required = true)
        private long ch3;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 4", required = true)
        private long ch4;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 5", required = true)
        private long ch5;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 6", required = true)
        private long ch6;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 7", required = true)
        private long ch7;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 8", required = true)
        private long ch8;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 9", required = true)
        private long ch9;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 10", required = true)
        private long ch10;
        @ApiModelProperty(notes = "WiFi 2.4G Channel 11", required = true)
        private long ch11;

        @ApiModelProperty(notes = "WiFi 5G Channel 36", required = true)
        private long ch36;
        @ApiModelProperty(notes = "WiFi 5G Channel 40", required = true)
        private long ch40;
        @ApiModelProperty(notes = "WiFi 5G Channel 44", required = true)
        private long ch44;
        @ApiModelProperty(notes = "WiFi 5G Channel 48", required = true)
        private long ch48;

        @ApiModelProperty(notes = "WiFi 5G Channel 52", required = true)
        private long ch52;
        @ApiModelProperty(notes = "WiFi 5G Channel 56", required = true)
        private long ch56;
        @ApiModelProperty(notes = "WiFi 5G Channel 60", required = true)
        private long ch60;
        @ApiModelProperty(notes = "WiFi 5G Channel 64", required = true)
        private long ch64;

        @ApiModelProperty(notes = "WiFi 5G Channel 100", required = true)
        private long ch100;
        @ApiModelProperty(notes = "WiFi 5G Channel 104", required = true)
        private long ch104;
        @ApiModelProperty(notes = "WiFi 5G Channel 108", required = true)
        private long ch108;
        @ApiModelProperty(notes = "WiFi 5G Channel 112", required = true)
        private long ch112;
        @ApiModelProperty(notes = "WiFi 5G Channel 116", required = true)
        private long ch116;
        @ApiModelProperty(notes = "WiFi 5G Channel 120", required = true)
        private long ch120;
        @ApiModelProperty(notes = "WiFi 5G Channel 124", required = true)
        private long ch124;
        @ApiModelProperty(notes = "WiFi 5G Channel 128", required = true)
        private long ch128;
        @ApiModelProperty(notes = "WiFi 5G Channel 132", required = true)
        private long ch132;
        @ApiModelProperty(notes = "WiFi 5G Channel 136", required = true)
        private long ch136;
        @ApiModelProperty(notes = "WiFi 5G Channel 140", required = true)
        private long ch140;
        @ApiModelProperty(notes = "WiFi 5G Channel 144", required = true)
        private long ch144;

        @ApiModelProperty(notes = "WiFi 5G Channel 149", required = true)
        private long ch149;
        @ApiModelProperty(notes = "WiFi 5G Channel 153", required = true)
        private long ch153;
        @ApiModelProperty(notes = "WiFi 5G Channel 157", required = true)
        private long ch157;
        @ApiModelProperty(notes = "WiFi 5G Channel 161", required = true)
        private long ch161;
        @ApiModelProperty(notes = "WiFi 5G Channel 165", required = true)
        private long ch165;

        @ApiModelProperty(notes = "equipment type", required = true)
        private String equipment;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public long getCh1() {
            return ch1;
        }

        public void setCh1(long ch1) {
            this.ch1 = ch1;
        }

        public long getCh2() {
            return ch2;
        }

        public void setCh2(long ch2) {
            this.ch2 = ch2;
        }

        public long getCh3() {
            return ch3;
        }

        public void setCh3(long ch3) {
            this.ch3 = ch3;
        }

        public long getCh4() {
            return ch4;
        }

        public void setCh4(long ch4) {
            this.ch4 = ch4;
        }

        public long getCh5() {
            return ch5;
        }

        public void setCh5(long ch5) {
            this.ch5 = ch5;
        }

        public long getCh6() {
            return ch6;
        }

        public void setCh6(long ch6) {
            this.ch6 = ch6;
        }

        public long getCh7() {
            return ch7;
        }

        public void setCh7(long ch7) {
            this.ch7 = ch7;
        }

        public long getCh8() {
            return ch8;
        }

        public void setCh8(long ch8) {
            this.ch8 = ch8;
        }

        public long getCh9() {
            return ch9;
        }

        public void setCh9(long ch9) {
            this.ch9 = ch9;
        }

        public long getCh10() {
            return ch10;
        }

        public void setCh10(long ch10) {
            this.ch10 = ch10;
        }

        public long getCh11() {
            return ch11;
        }

        public void setCh11(long ch11) {
            this.ch11 = ch11;
        }

        public long getCh36() {
            return ch36;
        }

        public void setCh36(long ch36) {
            this.ch36 = ch36;
        }

        public long getCh40() {
            return ch40;
        }

        public void setCh40(long ch40) {
            this.ch40 = ch40;
        }

        public long getCh44() {
            return ch44;
        }

        public void setCh44(long ch44) {
            this.ch44 = ch44;
        }

        public long getCh48() {
            return ch48;
        }

        public void setCh48(long ch48) {
            this.ch48 = ch48;
        }

        public long getCh52() {
            return ch52;
        }

        public void setCh52(long ch52) {
            this.ch52 = ch52;
        }

        public long getCh56() {
            return ch56;
        }

        public void setCh56(long ch56) {
            this.ch56 = ch56;
        }

        public long getCh60() {
            return ch60;
        }

        public void setCh60(long ch60) {
            this.ch60 = ch60;
        }

        public long getCh64() {
            return ch64;
        }

        public void setCh64(long ch64) {
            this.ch64 = ch64;
        }

        public long getCh100() {
            return ch100;
        }

        public void setCh100(long ch100) {
            this.ch100 = ch100;
        }

        public long getCh104() {
            return ch104;
        }

        public void setCh104(long ch104) {
            this.ch104 = ch104;
        }

        public long getCh108() {
            return ch108;
        }

        public void setCh108(long ch108) {
            this.ch108 = ch108;
        }

        public long getCh112() {
            return ch112;
        }

        public void setCh112(long ch112) {
            this.ch112 = ch112;
        }

        public long getCh116() {
            return ch116;
        }

        public void setCh116(long ch116) {
            this.ch116 = ch116;
        }

        public long getCh120() {
            return ch120;
        }

        public void setCh120(long ch120) {
            this.ch120 = ch120;
        }

        public long getCh124() {
            return ch124;
        }

        public void setCh124(long ch124) {
            this.ch124 = ch124;
        }

        public long getCh128() {
            return ch128;
        }

        public void setCh128(long ch128) {
            this.ch128 = ch128;
        }

        public long getCh132() {
            return ch132;
        }

        public void setCh132(long ch132) {
            this.ch132 = ch132;
        }

        public long getCh136() {
            return ch136;
        }

        public void setCh136(long ch136) {
            this.ch136 = ch136;
        }

        public long getCh140() {
            return ch140;
        }

        public void setCh140(long ch140) {
            this.ch140 = ch140;
        }

        public long getCh144() {
            return ch144;
        }

        public void setCh144(long ch144) {
            this.ch144 = ch144;
        }

        public long getCh149() {
            return ch149;
        }

        public void setCh149(long ch149) {
            this.ch149 = ch149;
        }

        public long getCh153() {
            return ch153;
        }

        public void setCh153(long ch153) {
            this.ch153 = ch153;
        }

        public long getCh157() {
            return ch157;
        }

        public void setCh157(long ch157) {
            this.ch157 = ch157;
        }

        public long getCh161() {
            return ch161;
        }

        public void setCh161(long ch161) {
            this.ch161 = ch161;
        }

        public long getCh165() {
            return ch165;
        }

        public void setCh165(long ch165) {
            this.ch165 = ch165;
        }

        public String getEquipment() {
            return equipment;
        }

        public void setEquipment(String equipment) {
            this.equipment = equipment;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }
}
