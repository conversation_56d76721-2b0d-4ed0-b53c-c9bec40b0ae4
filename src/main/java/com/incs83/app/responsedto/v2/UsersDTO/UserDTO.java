package com.incs83.app.responsedto.v2.UsersDTO;

import java.util.StringJoiner;

/**
 * Created by hari on 2/2/18.
 */
public class UserDTO {
    private String id;
    private String firstName = "";
    private String middleName = "";
    private String lastName = "";
    private String gender;
    private String email = "";
    private String alexaEmail="";
    private String googleHomeEmail="";

    public UserDTO() {
    }

    public String getId() {
        return id;
    }

    public UserDTO setId(String id) {
        this.id = id;
        return this;
    }

    public String getFirstName() {
        return firstName;
    }

    public UserDTO setFirstName(String firstName) {
        this.firstName = firstName;
        return this;
    }

    public String getMiddleName() {
        return middleName;
    }

    public UserDTO setMiddleName(String middleName) {
        this.middleName = middleName;
        return this;
    }

    public String getLastName() {
        return lastName;
    }

    public UserDTO setLastName(String lastName) {
        this.lastName = lastName;
        return this;
    }

    public String getGender() {
        return gender;
    }

    public UserDTO setGender(String gender) {
        this.gender = gender;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public UserDTO setEmail(String email) {
        this.email = email;
        return this;
    }

    public String getAlexaEmail() {
        return alexaEmail;
    }

    public UserDTO setAlexaEmail(String alexaEmail) {
        this.alexaEmail = alexaEmail;
        return this;
    }

    public String getGoogleHomeEmail() {
        return googleHomeEmail;
    }

    public UserDTO setGoogleHomeEmail(String googleHomeEmail) {
        this.googleHomeEmail = googleHomeEmail;
        return this;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", "UserResponseDTO{", "}")
                .add("id='" + id + "'")
                .add("firstName='" + firstName + "'")
                .add("middleName='" + middleName + "'")
                .add("lastName='" + lastName + "'")
                .add("gender=" + gender)
                .add("email='" + email + "'")
                .add("alexaEmail='"+alexaEmail+"'")
                .add("googleHomeEmail='"+googleHomeEmail+"'")
                .toString();
    }
}
