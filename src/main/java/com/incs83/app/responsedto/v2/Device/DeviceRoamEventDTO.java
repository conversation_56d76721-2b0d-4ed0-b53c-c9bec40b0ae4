package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

public class DeviceRoamEventDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private Set<DeviceRoamEventData> data;

    @Override
    public Set<DeviceRoamEventData> getData() {
        return data;
    }

    public void setData(Set<DeviceRoamEventData> data) {
        this.data = data;
    }

    public class DeviceRoamEventData implements Comparable {
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "from", required = true)
        private String from;
        @ApiModelProperty(notes = "to", required = true)
        private String to;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getFrom() {
            return from;
        }

        public void setFrom(String from) {
            this.from = from;
        }

        public String getTo() {
            return to;
        }

        public void setTo(String to) {
            this.to = to;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            DeviceRoamEventData that = (DeviceRoamEventData) o;

            if (timestamp != that.timestamp) return false;
            return macAddress != null ? macAddress.equals(that.macAddress) : that.macAddress == null;
        }

        @Override
        public int hashCode() {
            int result = macAddress != null ? macAddress.hashCode() : 0;
            result = 31 * result + (from != null ? from.hashCode() : 0);
            result = 31 * result + (to != null ? to.hashCode() : 0);
            result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
            return result;
        }

        @Override
        public int compareTo(Object o) {
            DeviceRoamEventDTO.DeviceRoamEventData roamEventData = (DeviceRoamEventDTO.DeviceRoamEventData) o;

            return this.timestamp == roamEventData.getTimestamp() ? 0 : Integer.valueOf(String.valueOf(this.timestamp - roamEventData.getTimestamp()));
        }
    }
}
