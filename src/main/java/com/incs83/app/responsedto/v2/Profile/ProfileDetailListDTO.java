package com.incs83.app.responsedto.v2.Profile;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class ProfileDetailListDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<ProfileDetailsDTO.ProfileDetailsData> data;

    @Override
    public ArrayList<ProfileDetailsDTO.ProfileDetailsData> getData() {
        return data;
    }

    public void setData(ArrayList<ProfileDetailsDTO.ProfileDetailsData> data) {
        this.data = data;
    }
}
