package com.incs83.app.responsedto.v2.UsersDTO;/* sakshi created on 6/11/19 inside the package - com.incs83.app.responsedto.v2.UsersDTO */

import com.incs83.dto.ApiResponseDTO;

import java.util.ArrayList;

public class SubscriberListDTO extends ApiResponseDTO {
    private SubscriberData data;

    @Override
    public SubscriberData getData() {
        return data;
    }

    public void setData(SubscriberData data) {
        this.data = data;
    }

    public class SubscriberData{
        private Long totalCount;
        private ArrayList<SubscriberModel> objects;

        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public ArrayList<SubscriberModel> getObjects() {
            return objects;
        }

        public void setObjects(ArrayList<SubscriberModel> objects) {
            this.objects = objects;
        }

        public class SubscriberModel {
            private String id;
            private String name;
            private String globalAccountNo;
            private String imageUrl;
            private boolean isActive;
            private String camsAccountNo;
            private String phoneNo;
            private String email;
            private String groupName;
            private String groupId;

            public boolean isActive() {
                return isActive;
            }

            public String getGroupName() {
                return groupName;
            }

            public void setGroupName(String groupName) {
                this.groupName = groupName;
            }

            public String getGroupId() {
                return groupId;
            }

            public void setGroupId(String groupId) {
                this.groupId = groupId;
            }

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public String getGlobalAccountNo() {
                return globalAccountNo;
            }

            public void setGlobalAccountNo(String globalAccountNo) {
                this.globalAccountNo = globalAccountNo;
            }

            public String getImageUrl() {
                return imageUrl;
            }

            public void setImageUrl(String imageUrl) {
                this.imageUrl = imageUrl;
            }

            public boolean isActive(Boolean aBoolean) {
                return isActive;
            }

            public void setActive(boolean active) {
                isActive = active;
            }

            public String getCamsAccountNo() {
                return camsAccountNo;
            }

            public void setCamsAccountNo(String camsAccountNo) {
                this.camsAccountNo = camsAccountNo;
            }

            public String getPhoneNo() {
                return phoneNo;
            }

            public void setPhoneNo(String phoneNo) {
                this.phoneNo = phoneNo;
            }

            public String getEmail() {
                return email;
            }

            public void setEmail(String email) {
                this.email = email;
            }
        }
    }
}
