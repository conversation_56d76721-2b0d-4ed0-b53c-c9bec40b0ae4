package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentDiagnosticDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentDiagnosticData> data;

    @Override
    public ArrayList<EquipmentDiagnosticData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentDiagnosticData> data) {
        this.data = data;
    }

    public class EquipmentDiagnosticData implements Comparable<EquipmentDiagnosticData> {
        private long time;
        private String event;
        private String macAddress;
        private String hostname;
        private String friendlyName;
        private String device;
        private String band;
        private String vendor;
        private long trigger;
        private String triggerDescription;
        private long reasonCode;
        private String reasonDescription;

        public long getTime() {
            return time;
        }

        public void setTime(long time) {
            this.time = time;
        }

        public String getEvent() {
            return event;
        }

        public void setEvent(String event) {
            this.event = event;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getHostname() {
            return hostname;
        }

        public void setHostname(String hostname) {
            this.hostname = hostname;
        }

        public String getFriendlyName() {
            return friendlyName;
        }

        public void setFriendlyName(String friendlyName) {
            this.friendlyName = friendlyName;
        }

        public String getDevice() {
            return device;
        }

        public void setDevice(String device) {
            this.device = device;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public long getTrigger() {
            return trigger;
        }

        public void setTrigger(long trigger) {
            this.trigger = trigger;
        }

        public String getTriggerDescription() {
            return triggerDescription;
        }

        public void setTriggerDescription(String triggerDescription) {
            this.triggerDescription = triggerDescription;
        }

        public long getReasonCode() {
            return reasonCode;
        }

        public void setReasonCode(long reasonCode) {
            this.reasonCode = reasonCode;
        }

        public String getReasonDescription() {
            return reasonDescription;
        }

        public void setReasonDescription(String reasonDescription) {
            this.reasonDescription = reasonDescription;
        }

        @Override
        public int compareTo(EquipmentDiagnosticDTO.EquipmentDiagnosticData o) {
            return this.time<o.time?-1: this.time>o.time?1:0;
        }
    }
}
