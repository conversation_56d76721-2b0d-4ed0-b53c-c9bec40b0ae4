package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class DeviceSpeedTestDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private DeviceSpeedTestData data;

    @Override
    public DeviceSpeedTestData getData() {
        return data;
    }

    public void setData(DeviceSpeedTestData data) {
        this.data = data;
    }

    public class DeviceSpeedTestData {
        @ApiModelProperty(notes = "date", required = true)
        private Long date;
        @ApiModelProperty(notes = "userId", required = true)
        private String userId;
        @ApiModelProperty(notes = "txRate", required = true)
        private Float txRate;
        @ApiModelProperty(notes = "rxRate", required = true)
        private Float rxRate;
        @ApiModelProperty(notes = "dataRate", required = true)
        private Float dataRate;
        @ApiModelProperty(notes = "mac Address", required = true)
        private String macAddr;
        @ApiModelProperty(notes = "ip Address", required = true)
        private String ipAddr;
        @ApiModelProperty(notes = "band", required = true)
        private String band;
        @ApiModelProperty(notes = "RPC Result", required = true)
        private String result;

        public Long getDate() {
            return date;
        }

        public void setDate(Long date) {
            this.date = date;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public Float getTxRate() {
            return txRate;
        }

        public void setTxRate(Float txRate) {
            this.txRate = txRate;
        }

        public Float getRxRate() {
            return rxRate;
        }

        public void setRxRate(Float rxRate) {
            this.rxRate = rxRate;
        }

        public Float getDataRate() {
            return dataRate;
        }

        public void setDataRate(Float dataRate) {
            this.dataRate = dataRate;
        }

        public String getMacAddr() {
            return macAddr;
        }

        public void setMacAddr(String macAddr) {
            this.macAddr = macAddr;
        }

        public String getIpAddr() {
            return ipAddr;
        }

        public void setIpAddr(String ipAddr) {
            this.ipAddr = ipAddr;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public String getResult() {
            return result;
        }

        public void setResult(String result) {
            this.result = result;
        }
    }
}
