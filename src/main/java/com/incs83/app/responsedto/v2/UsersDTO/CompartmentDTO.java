package com.incs83.app.responsedto.v2.UsersDTO;

import io.swagger.annotations.ApiModelProperty;

public class CompartmentDTO {
    @ApiModelProperty(notes = "id", required = true)
    private String id;
    @ApiModelProperty(notes = "name", required = true)
    private String name;
    @ApiModelProperty(notes = "description", required = true)
    private String description;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
