package com.incs83.app.responsedto.v2.clusters;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class ClusterSubscriberResponseDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ClusterSubscriberResponseDTO.ClusterInfoDetail data;

    @Override
    public ClusterInfoDetail getData() {
        return data;
    }

    public void setData(ClusterInfoDetail data) {
        this.data = data;
    }

    public class ClusterInfoDetail {
        @ApiModelProperty(notes = "Cluster id")
        private String id;
        @ApiModelProperty(notes = "Cluster name")
        private String name;
        @ApiModelProperty(notes = "Cluster description")
        private String description;
        @ApiModelProperty(notes = "Is Default Cluster")
        private boolean defaultCluster;
        @ApiModelProperty(notes = "Cluster description")
        private String isp;
        @ApiModelProperty(notes = "Users List")
        private ClusterSubscriber users;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public boolean isDefaultCluster() {
            return defaultCluster;
        }

        public void setDefaultCluster(boolean defaultCluster) {
            this.defaultCluster = defaultCluster;
        }

        public ClusterSubscriber getUsers() {
            return users;
        }

        public void setUsers(ClusterSubscriber users) {
            this.users = users;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }
    }
}
