package com.incs83.app.responsedto.v2.Equipment;/* sakshi created on 5/11/19 inside the package - com.incs83.app.responsedto.v2.Equipment */

import com.incs83.auditor.AuditableData;

public class EditEquipmentRequest implements AuditableData {
    private String rgwSerial;
    private String rgwMAC;
    private String friendlyName;
    private String serviceTelephoneNo;
    private double downLinkRate;
    private double upLinkRate;
//    private String isp;


    public String getRgwMAC() {
        return rgwMAC;
    }

    public void setRgwMAC(String rgwMAC) {
        this.rgwMAC = rgwMAC;
    }

    /*public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }
    */
    public String getRgwSerial() {
        return rgwSerial;
    }

    public void setRgwSerial(String rgwSerial) {
        this.rgwSerial = rgwSerial;
    }

    public String getFriendlyName() {
        return friendlyName;
    }

    public void setFriendlyName(String friendlyName) {
        this.friendlyName = friendlyName;
    }

    public String getServiceTelephoneNo() {
        return serviceTelephoneNo;
    }

    public void setServiceTelephoneNo(String serviceTelephoneNo) {
        this.serviceTelephoneNo = serviceTelephoneNo;
    }

    public double getDownLinkRate() {
        return downLinkRate;
    }

    public void setDownLinkRate(double downLinkRate) {
        this.downLinkRate = downLinkRate;
    }

    public double getUpLinkRate() {
        return upLinkRate;
    }

    public void setUpLinkRate(double upLinkRate) {
        this.upLinkRate = upLinkRate;
    }
}
