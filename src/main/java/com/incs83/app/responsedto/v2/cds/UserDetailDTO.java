package com.incs83.app.responsedto.v2.cds;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class UserDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private UserDetail data;

    @Override
    public UserDetail getData() {
        return data;
    }

    public void setData(UserDetail data) {
        this.data = data;
    }

    public class UserDetail{
        private String id;
        private String firstName;
        private String lastName;
        private String email;
        private String roleId;
        private String roleName;
        private String compartmentId;
        private Boolean verify;
        private Boolean skipEmailVerification;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getFirstName() {
            return firstName;
        }

        public void setFirstName(String firstName) {
            this.firstName = firstName;
        }

        public String getLastName() {
            return lastName;
        }

        public void setLastName(String lastName) {
            this.lastName = lastName;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getRoleId() {
            return roleId;
        }

        public void setRoleId(String roleId) {
            this.roleId = roleId;
        }

        public String getRoleName() {
            return roleName;
        }

        public void setRoleName(String roleName) {
            this.roleName = roleName;
        }

        public String getCompartmentId() {
            return compartmentId;
        }

        public void setCompartmentId(String compartmentId) {
            this.compartmentId = compartmentId;
        }

        public Boolean getVerify() {
            return verify;
        }

        public void setVerify(Boolean verify) {
            this.verify = verify;
        }

        public Boolean getSkipEmailVerification() {
            return skipEmailVerification;
        }

        public void setSkipEmailVerification(Boolean skipEmailVerification) {
            this.skipEmailVerification = skipEmailVerification;
        }
    }
}
