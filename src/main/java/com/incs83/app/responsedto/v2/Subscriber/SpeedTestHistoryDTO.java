package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

public class SpeedTestHistoryDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private List<SpeedStats> data;

    private String tid;

    @Override
    public List<SpeedStats> getData() {
        return data;
    }

    public void setData(List<SpeedStats> data) {
        this.data = data;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }
}
