package com.incs83.app.responsedto.v2;

import com.fasterxml.jackson.annotation.JsonSetter;

import java.io.Serializable;

public class RadioResponseDTO implements Serializable {

    private String band;
    private boolean enable;
    private boolean autoChannelEnable;
    private boolean dynamicChannelSwitch;
    @JsonSetter("DFSEnable")
    private boolean dFSEnable;
    private boolean scheduleAcsEnabled;
    private String scheduleAcsStartTime;
    private String scheduleAcsEndTime;
    @JsonSetter("ChannelsInUse")
    private String channelsInUse;
    @JsonSetter("ChannelsInSet")
    private String channelsInSet;

    public String getChannelsInSet() {
        return channelsInSet;
    }

    public void setChannelsInSet(String channelsInSet) {
        this.channelsInSet = channelsInSet;
    }

    public String getChannelsInUse() {
        return channelsInUse;
    }

    public void setChannelsInUse(String channelsInUse) {
        this.channelsInUse = channelsInUse;
    }

    public String getBand() {
        return band;
    }

    public void setBand(String band) {
        this.band = band;
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public boolean isAutoChannelEnable() {
        return autoChannelEnable;
    }

    public void setAutoChannelEnable(boolean autoChannelEnable) {
        this.autoChannelEnable = autoChannelEnable;
    }

    public boolean isDynamicChannelSwitch() {
        return dynamicChannelSwitch;
    }

    public void setDynamicChannelSwitch(boolean dynamicChannelSwitch) {
        this.dynamicChannelSwitch = dynamicChannelSwitch;
    }

    public boolean isdFSEnable() {
        return dFSEnable;
    }

    public void setdFSEnable(boolean dFSEnable) {
        this.dFSEnable = dFSEnable;
    }

    public boolean isScheduleAcsEnabled() {
        return scheduleAcsEnabled;
    }

    public void setScheduleAcsEnabled(boolean scheduleAcsEnabled) {
        this.scheduleAcsEnabled = scheduleAcsEnabled;
    }

    public String getScheduleAcsStartTime() {
        return scheduleAcsStartTime;
    }

    public void setScheduleAcsStartTime(String scheduleAcsStartTime) {
        this.scheduleAcsStartTime = scheduleAcsStartTime;
    }

    public String getScheduleAcsEndTime() {
        return scheduleAcsEndTime;
    }

    public void setScheduleAcsEndTime(String scheduleAcsEndTime) {
        this.scheduleAcsEndTime = scheduleAcsEndTime;
    }


}
