package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class InternetUtilizationDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<InternetUtilizationData> data;

//    @ApiModelProperty(notes = "totalDownloadSpeed", required = true)
//    private double totalDownloadSpeed;
//    @ApiModelProperty(notes = "totalUploadSpeed", required = true)
//    private double totalUploadSpeed;
//
//    public double getTotalDownloadSpeed() {
//        return totalDownloadSpeed;
//    }
//
//    public void setTotalDownloadSpeed(double totalDownloadSpeed) {
//        this.totalDownloadSpeed = totalDownloadSpeed;
//    }
//
//    public double getTotalUploadSpeed() {
//        return totalUploadSpeed;
//    }
//
//    public void setTotalUploadSpeed(double totalUploadSpeed) {
//        this.totalUploadSpeed = totalUploadSpeed;
//    }

    @Override
    public ArrayList<InternetUtilizationData> getData() {
        return data;
    }

    public void setData(ArrayList<InternetUtilizationData> data) {
        this.data = data;
    }

    public class InternetUtilizationData  {
        @ApiModelProperty(notes = "downloadSpeed", required = true)
        private double downloadSpeed;
        @ApiModelProperty(notes = "uploadSpeed", required = true)
        private double uploadSpeed;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;
        @ApiModelProperty(notes = "Allocated Bandwidth to Subscriber")
        private Double downLinkRate;
        @ApiModelProperty(notes = "Allocated Bandwidth to Subscriber")
        private Double upLinkRate;
        @ApiModelProperty(notes = "Download rate percentage")
        private Double downloadPercentage;

        public Double getDownloadPercentage() {
            return downloadPercentage;
        }

        public void setDownloadPercentage(Double downloadPercentage) {
            this.downloadPercentage = downloadPercentage;
        }

        public Double getUploadPercentage() {
            return uploadPercentage;
        }

        public void setUploadPercentage(Double uploadPercentage) {
            this.uploadPercentage = uploadPercentage;
        }

        @ApiModelProperty(notes = "Upload rate percentage")
        private Double uploadPercentage;


        public double getDownloadSpeed() {
            return downloadSpeed;
        }

        public void setDownloadSpeed(double downloadSpeed) {
            this.downloadSpeed = downloadSpeed;
        }

        public double getUploadSpeed() {
            return uploadSpeed;
        }

        public void setUploadSpeed(double uploadSpeed) {
            this.uploadSpeed = uploadSpeed;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
        public Double getDownLinkRate() {
            return downLinkRate;
        }

        public void setDownLinkRate(Double downLinkRate) {
            this.downLinkRate = downLinkRate;
        }

        public Double getUpLinkRate() {
            return upLinkRate;
        }

        public void setUpLinkRate(Double upLinkRate) {
            this.upLinkRate = upLinkRate;
        }


    }
}
