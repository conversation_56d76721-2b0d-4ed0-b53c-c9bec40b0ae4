package com.incs83.app.responsedto.v2.clusters;

import java.util.ArrayList;

public class ClusterSubscriber {
    private long totalCount;
    private ArrayList<UserDetail> objects;

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public ArrayList<UserDetail> getObjects() {
        return objects;
    }

    public void setObjects(ArrayList<UserDetail> objects) {
        this.objects = objects;
    }

    public class UserDetail{
        private String name;
        private String serialNumber;
        private String email;
        private String equipmentId;
        private String rgwMAC;
        private String globalAccountNo;
        private String camsAccountNo;
        private boolean selected;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSerialNumber() {
            return serialNumber;
        }

        public void setSerialNumber(String serialNumber) {
            this.serialNumber = serialNumber;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getEquipmentId() {
            return equipmentId;
        }

        public void setEquipmentId(String equipmentId) {
            this.equipmentId = equipmentId;
        }

        public String getRgwMAC() {
            return rgwMAC;
        }

        public void setRgwMAC(String rgwMAC) {
            this.rgwMAC = rgwMAC;
        }

        public boolean isSelected() {
            return selected;
        }

        public void setSelected(boolean selected) {
            this.selected = selected;
        }

        public String getGlobalAccountNo() {
            return globalAccountNo;
        }

        public void setGlobalAccountNo(String globalAccountNo) {
            this.globalAccountNo = globalAccountNo;
        }

        public String getCamsAccountNo() {
            return camsAccountNo;
        }

        public void setCamsAccountNo(String camsAccountNo) {
            this.camsAccountNo = camsAccountNo;
        }
    }
}
