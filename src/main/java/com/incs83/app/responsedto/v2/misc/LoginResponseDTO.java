package com.incs83.app.responsedto.v2.misc;

import io.swagger.annotations.ApiModelProperty;

public class LoginResponseDTO {

    @ApiModelProperty(notes = "Response Code", required = true)
    private Integer code;
    @ApiModelProperty(notes = "Response Message", required = true)
    private String message;
    @ApiModelProperty(notes = "Response Data", required = true)
    private LoginDataDTO data;

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public LoginDataDTO getData() {
        return data;
    }

    public void setData(LoginDataDTO data) {
        this.data = data;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    private class LoginDataDTO {
        @ApiModelProperty(notes = "Access Token for the Login request", required = true)
        private String accessToken;
        @ApiModelProperty(notes = "Status of user email verification", required = true)
        private Boolean verify;

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public Boolean getVerify() {
            return verify;
        }

        public void setVerify(Boolean verify) {
            this.verify = verify;
        }
    }
}


