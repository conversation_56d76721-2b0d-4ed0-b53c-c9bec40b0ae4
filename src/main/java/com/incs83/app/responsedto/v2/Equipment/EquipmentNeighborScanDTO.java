package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentNeighborScanDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentNeighborScanData> data;

    @Override
    public ArrayList<EquipmentNeighborScanData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentNeighborScanData> data) {
        this.data = data;
    }

    public class EquipmentNeighborScanData {
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;
        @ApiModelProperty(notes = "ssid", required = true)
        private String ssid;
        @ApiModelProperty(notes = "bssid", required = true)
        private String bssid;
        @ApiModelProperty(notes = "vendor", required = true)
        private String vendor;
        @ApiModelProperty(notes = "band", required = true)
        private String band;
        @ApiModelProperty(notes = "channel", required = true)
        private int channel;
        @ApiModelProperty(notes = "bandwidth", required = true)
        private int bandwidth;
        @ApiModelProperty(notes = "operatingStandard", required = true)
        private String operatingStandard;
        @ApiModelProperty(notes = "signalStrength", required = true)
        private int signalStrength;
        @ApiModelProperty(notes = "noise", required = true)
        private int noise;
        @ApiModelProperty(notes = "reporterSerialNumber", required = true)
        private String reporterSerialNumber;

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getSSID() {
            return ssid;
        }

        public void setSSID(String ssid) {
            this.ssid = ssid;
        }

        public String getBSSID() {
            return bssid;
        }

        public void setBSSID(String bssid) {
            this.bssid = bssid;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public int getChannel() {
            return channel;
        }

        public void setChannel(int channel) {
            this.channel = channel;
        }

        public int getBandwidth() {
            return bandwidth;
        }

        public void setBandwidth(int bandwidth) {
            this.bandwidth = bandwidth;
        }

        public String getOperatingStandard() {
            return operatingStandard;
        }

        public void setOperatingStandard(String operatingStandard) {
            this.operatingStandard = operatingStandard;
        }

        public int getSignalStrength() {
            return signalStrength;
        }

        public void setSignalStrength(int signalStrength) {
            this.signalStrength = signalStrength;
        }

        public int getNoise() {
            return noise;
        }

        public void setNoise(int noise) {
            this.noise = noise;
        }

        public String getReporterSerialNumber() {
            return reporterSerialNumber;
        }

        public void setReporterSerialNumber(String reporterSerialNumber) {
            this.reporterSerialNumber = reporterSerialNumber;
        }
    }
}
