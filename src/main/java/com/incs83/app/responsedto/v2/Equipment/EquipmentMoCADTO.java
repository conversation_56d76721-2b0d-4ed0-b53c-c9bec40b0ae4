package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class EquipmentMoCADTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private EquipmentMoCAData data;

    public EquipmentMoCAData getData() {
        return data;
    }

    public void setData(EquipmentMoCAData data) {
        this.data = data;
    }

    public class EquipmentMoCAData {
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "rxBytes", required = true)
        private double rxBytes;
        @ApiModelProperty(notes = "mocaStandard", required = true)
        private String mocaStandard;
        @ApiModelProperty(notes = "txBytes", required = true)
        private double txBytes;
        @ApiModelProperty(notes = "status", required = true)
        private String status;
        @ApiModelProperty(notes = "lastChange", required = true)
        private long lastChange;
        @ApiModelProperty(notes = "uptime", required = true)
        private long uptime;

        public double getRxBytes() {
            return rxBytes;
        }

        public void setRxBytes(double rxBytes) {
            this.rxBytes = rxBytes;
        }

        public double getTxBytes() {
            return txBytes;
        }

        public void setTxBytes(double txBytes) {
            this.txBytes = txBytes;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getMocaStandard() {
            return mocaStandard;
        }

        public void setMocaStandard(String mocaStandard) {
            this.mocaStandard = mocaStandard;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public long getLastChange() {
            return lastChange;
        }

        public void setLastChange(long lastChange) {
            this.lastChange = lastChange;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }
    }
}
