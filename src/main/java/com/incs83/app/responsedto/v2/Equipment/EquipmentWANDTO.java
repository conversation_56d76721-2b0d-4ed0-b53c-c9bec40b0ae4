package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class EquipmentWANDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private EquipmentWANData data;

    @Override
    public EquipmentWANData getData() {
        return data;
    }

    public void setData(EquipmentWANData data) {
        this.data = data;
    }

    public class EquipmentWANData {
        @ApiModelProperty(notes = "rxBytes", required = true)
        private double rxBytes;
        @ApiModelProperty(notes = "txBytes", required = true)
        private double txBytes;
        @ApiModelProperty(notes = "dns2", required = true)
        private String dns2;
        @ApiModelProperty(notes = "dns1", required = true)
        private String dns1;
        @ApiModelProperty(notes = "devIpAddress", required = true)
        private String devIpAddress;
        @ApiModelProperty(notes = "leaseTimeRemaining", required = true)
        private String leaseTimeRemaining;
        @ApiModelProperty(notes = "leaseTimeRemainingInSeconds", required = true)
        private int leaseTimeRemainingInSeconds;
        @ApiModelProperty(notes = "gateway", required = true)
        private String gateway;
        @ApiModelProperty(notes = "dhcp", required = true)
        private String dhcp;
        @ApiModelProperty(notes = "status", required = true)
        private String status;
        @ApiModelProperty(notes = "phyType", required = true)
        private String phyType;
        @ApiModelProperty(notes = "phyTransmitRate", required = true)
        private long phyTransmitRate;
        @ApiModelProperty(notes = "phyReceiveRate", required = true)
        private long phyReceiveRate;
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "lastChange", required = true)
        private long lastChange;
        @ApiModelProperty(notes = "uptime", required = true)
        private long uptime;

        public double getRxBytes() {
            return rxBytes;
        }

        public void setRxBytes(double rxBytes) {
            this.rxBytes = rxBytes;
        }

        public double getTxBytes() {
            return txBytes;
        }

        public void setTxBytes(double txBytes) {
            this.txBytes = txBytes;
        }

        public String getDns2() {
            return dns2;
        }

        public void setDns2(String dns2) {
            this.dns2 = dns2;
        }

        public String getDns1() {
            return dns1;
        }

        public void setDns1(String dns1) {
            this.dns1 = dns1;
        }

        public String getDevIpAddress() {
            return devIpAddress;
        }

        public void setDevIpAddress(String devIpAddress) {
            this.devIpAddress = devIpAddress;
        }

        public String getLeaseTimeRemaining() {
            return leaseTimeRemaining;
        }

        public void setLeaseTimeRemaining(String leaseTimeRemaining) {
            this.leaseTimeRemaining = leaseTimeRemaining;
        }

        public int getLeaseTimeRemainingInSeconds() {
            return leaseTimeRemainingInSeconds;
        }

        public void setLeaseTimeRemainingInSeconds(int leaseTimeRemainingInSeconds) {
            this.leaseTimeRemainingInSeconds = leaseTimeRemainingInSeconds;
        }

        public String getGateway() {
            return gateway;
        }

        public void setGateway(String gateway) {
            this.gateway = gateway;
        }

        public String getDhcp() {
            return dhcp;
        }

        public void setDhcp(String dhcp) {
            this.dhcp = dhcp;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getPhyType() {
            return phyType;
        }

        public void setPhyType(String phyType) {
            this.phyType = phyType;
        }

        public long getPhyTransmitRate() {
            return phyTransmitRate;
        }

        public void setPhyTransmitRate(long phyTransmitRate) {
            this.phyTransmitRate = phyTransmitRate;
        }

        public long getPhyReceiveRate() {
            return phyReceiveRate;
        }

        public void setPhyReceiveRate(long phyReceiveRate) {
            this.phyReceiveRate = phyReceiveRate;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public long getLastChange() {
            return lastChange;
        }

        public void setLastChange(long lastChange) {
            this.lastChange = lastChange;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }
    }
}
