package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceWifiThroughputDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<DeviceWifiThroughputData> data;

    @Override
    public ArrayList<DeviceWifiThroughputData> getData() {
        return data;
    }

    public void setData(ArrayList<DeviceWifiThroughputData> data) {
        this.data = data;
    }

    public class DeviceWifiThroughputData implements Comparable<DeviceWifiThroughputData>{
        @ApiModelProperty(notes = "tx", required = true)
        private double tx;
        @ApiModelProperty(notes = "rx", required = true)
        private double rx;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public double getTx() {
            return tx;
        }

        public void setTx(double tx) {
            this.tx = tx;
        }

        public double getRx() {
            return rx;
        }

        public void setRx(double rx) {
            this.rx = rx;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        @Override
        public int compareTo(DeviceWifiThroughputData o) {
            return (this.getTimestamp() < o.getTimestamp()) ? -1 : ((this.getTimestamp() == o.getTimestamp()) ? 0 : 1);
        }
    }
}
