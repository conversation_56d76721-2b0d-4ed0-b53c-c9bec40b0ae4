package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceAssignedDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<DeviceAssignedData> data;

    public ArrayList<DeviceAssignedData> getData() {
        return data;
    }

    public void setData(ArrayList<DeviceAssignedData> data) {
        this.data = data;
    }

    public class DeviceAssignedData {
        @ApiModelProperty(notes = "Device Mac Address", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "Device name", required = true)
        private String name;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }
}
