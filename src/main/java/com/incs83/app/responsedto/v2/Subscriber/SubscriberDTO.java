package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class SubscriberDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private SubscriberListDTO.SubscriberData.SubscriberModel data;

    @Override
    public SubscriberListDTO.SubscriberData.SubscriberModel getData() {
        return data;
    }

    public void setData(SubscriberListDTO.SubscriberData.SubscriberModel data) {
        this.data = data;
    }
}
