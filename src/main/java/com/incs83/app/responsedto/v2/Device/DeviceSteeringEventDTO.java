package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

public class DeviceSteeringEventDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private Set<DeviceSteeringEventData> data;

    @Override
    public Set<DeviceSteeringEventData> getData() {
        return data;
    }

    public void setData(Set<DeviceSteeringEventData> data) {
        this.data = data;
    }

    public class DeviceSteeringEventData implements Comparable {
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "intended", required = true)
        private String intended;
        @ApiModelProperty(notes = "vendor", required = true)
        private String vendor;
        @ApiModelProperty(notes = "origin", required = true)
        private String origin;
        @ApiModelProperty(notes = "type", required = true)
        private String type;
        @ApiModelProperty(notes = "end", required = true)
        private String end;
        @ApiModelProperty(notes = "target", required = true)
        private String target;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;
        @ApiModelProperty(notes = "steeringTime", required = true)
        private long steeringTime;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getIntended() {
            return intended;
        }

        public void setIntended(String intended) {
            this.intended = intended;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public String getOrigin() {
            return origin;
        }

        public void setOrigin(String origin) {
            this.origin = origin;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getTarget() {
            return target;
        }

        public void setTarget(String target) {
            this.target = target;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getEnd() {
            return end;
        }

        public void setEnd(String end) {
            this.end = end;
        }

        public long getSteeringTime() {
            return steeringTime;
        }

        public void setSteeringTime(long steeringTime) {
            this.steeringTime = steeringTime;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            DeviceSteeringEventData that = (DeviceSteeringEventData) o;

            if (timestamp != that.timestamp) return false;
            return macAddress != null ? macAddress.equals(that.macAddress) : that.macAddress == null;
        }

        @Override
        public int hashCode() {
            int result = macAddress != null ? macAddress.hashCode() : 0;
            result = 31 * result + (intended != null ? intended.hashCode() : 0);
            result = 31 * result + (vendor != null ? vendor.hashCode() : 0);
            result = 31 * result + (origin != null ? origin.hashCode() : 0);
            result = 31 * result + (type != null ? type.hashCode() : 0);
            result = 31 * result + (end != null ? end.hashCode() : 0);
            result = 31 * result + (target != null ? target.hashCode() : 0);
            result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
            return result;
        }

        @Override
        public int compareTo(Object o) {
            DeviceSteeringEventDTO.DeviceSteeringEventData steeringEventData = (DeviceSteeringEventDTO.DeviceSteeringEventData) o;
            return this.timestamp == steeringEventData.getTimestamp() ? 0 : Integer.valueOf(String.valueOf(this.timestamp - steeringEventData.getTimestamp()));
        }
    }
}
