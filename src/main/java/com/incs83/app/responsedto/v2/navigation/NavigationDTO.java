package com.incs83.app.responsedto.v2.navigation;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;
import com.incs83.app.utils.ValidationUtil;

import java.io.IOException;
import java.util.List;

public class NavigationDTO implements DataSerializable{
    private String id;
    private String navName;
    private String fontAwesomeIconClass;
    private String url;
    private boolean isNewTab;
    private List<NavigationDTO> subMenus;
    private String entities;
    private int position;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getNavName() {
        return navName;
    }

    public void setNavName(String navName) {
        this.navName = navName;
    }

    public String getFontAwesomeIconClass() {
        return fontAwesomeIconClass;
    }

    public void setFontAwesomeIconClass(String fontAwesomeIconClass) {
        this.fontAwesomeIconClass = fontAwesomeIconClass;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isNewTab() {
        return isNewTab;
    }

    public void setNewTab(boolean newTab) {
        isNewTab = newTab;
    }

    public List<NavigationDTO> getSubMenus() {
        return subMenus;
    }

    public void setSubMenus(List<NavigationDTO> subMenus) {
        this.subMenus = subMenus;
    }

    public String getEntities() {
        return entities;
    }

    public void setEntities(String entities) {
        this.entities = entities;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public static NavigationDTO mapToNavigationDTO(Object[] tuple) {
        NavigationDTO menuSub = new NavigationDTO();
        menuSub.setFontAwesomeIconClass(ValidationUtil.isNullString(String.valueOf(tuple[5])));
        menuSub.setNavName(ValidationUtil.isNullString(String.valueOf(tuple[3])));
        menuSub.setNewTab(Boolean.valueOf(String.valueOf(tuple[6])));
        menuSub.setUrl(ValidationUtil.isNullString(String.valueOf(tuple[4])));
        menuSub.setId(ValidationUtil.isNullString(String.valueOf(tuple[1])));
        menuSub.setEntities(ValidationUtil.isNullString(String.valueOf(tuple[7])));
        menuSub.setPosition(Integer.valueOf(ValidationUtil.isNullString(String.valueOf(tuple[0]))));
        return menuSub;
    }

    @Override
    public void writeData(ObjectDataOutput out) throws IOException {
        out.writeUTF(id);
        out.writeUTF(navName);
        out.writeUTF(fontAwesomeIconClass);
        out.writeUTF(url);
        out.writeBoolean(isNewTab);
        out.writeObject(subMenus);
        out.writeUTF(entities);
        out.writeInt(position);
    }

    @Override
    public void readData(ObjectDataInput in) throws IOException {
        id = in.readUTF();
        navName = in.readUTF();
        fontAwesomeIconClass = in.readUTF();
        url = in.readUTF();
        isNewTab = in.readBoolean();
        subMenus = in.readObject();
        entities = in.readUTF();
        position = in.readInt();
    }
}
