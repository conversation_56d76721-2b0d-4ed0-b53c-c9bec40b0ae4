package com.incs83.app.responsedto.v2.compartment;

import com.hazelcast.nio.ObjectDataInput;
import com.hazelcast.nio.ObjectDataOutput;
import com.hazelcast.nio.serialization.DataSerializable;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.IOException;
import java.util.List;

public class GroupDTO extends ApiResponseDTO implements DataSerializable {
    @ApiModelProperty(notes = "Response Data")
    private List<CompartmentDetail> data;

    @Override
    public List<CompartmentDetail> getData() {
        return data;
    }

    public void setData(List<CompartmentDetail> data) {
        this.data = data;
    }

    public static class CompartmentDetail implements DataSerializable {
        @ApiModelProperty(notes = "id")
        private String id;
        @ApiModelProperty(notes = "name")
        private String name;
        @ApiModelProperty(notes = "description")
        private String description;
        @ApiModelProperty(notes = "ispName")
        private String ispName;
        @ApiModelProperty(notes = "mqttConnection")
        private String mqttConnection;
        @ApiModelProperty(notes = "totalUsers")
        private Long totalUsers;
        @ApiModelProperty(notes = "userOnly")
        private Long userOnly;
        @ApiModelProperty(notes = "subscriberOnly")
        private Long subscriberOnly;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getIspName() {
            return ispName;
        }

        public void setIspName(String ispName) {
            this.ispName = ispName;
        }

        public Long getTotalUsers() {
            return totalUsers;
        }

        public void setTotalUsers(Long totalUsers) {
            this.totalUsers = totalUsers;
        }

        public Long getUserOnly() {
            return userOnly;
        }

        public void setUserOnly(Long userOnly) {
            this.userOnly = userOnly;
        }

        public Long getSubscriberOnly() {
            return subscriberOnly;
        }

        public void setSubscriberOnly(Long subscriberOnly) {
            this.subscriberOnly = subscriberOnly;
        }

        public String getMqttConnection() {
            return mqttConnection;
        }

        public void setMqttConnection(String mqttConnection) {
            this.mqttConnection = mqttConnection;
        }

        @Override
        public void writeData(ObjectDataOutput out) throws IOException {
            out.writeUTF(id);
            out.writeUTF(name);
            out.writeUTF(description);
            out.writeUTF(ispName);
            out.writeUTF(mqttConnection);
            out.writeLong(totalUsers);
            out.writeLong(userOnly);
            out.writeLong(subscriberOnly);
        }

        @Override
        public void readData(ObjectDataInput in) throws IOException {
            id = in.readUTF();
            name = in.readUTF();
            description = in.readUTF();
            ispName = in.readUTF();
            mqttConnection = in.readUTF();
            totalUsers = in.readLong();
            userOnly = in.readLong();
            subscriberOnly = in.readLong();
        }
    }

    @Override
    public void writeData(ObjectDataOutput out) throws IOException {
        out.writeObject(data);
    }

    @Override
    public void readData(ObjectDataInput in) throws IOException {
        data = in.readObject();
    }
}
