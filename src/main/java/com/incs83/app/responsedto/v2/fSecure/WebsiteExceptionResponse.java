package com.incs83.app.responsedto.v2.fSecure;

import com.incs83.dto.ApiResponseDTO;

import java.util.List;

public class WebsiteExceptionResponse extends ApiResponseDTO {
    private List<WebsiteException> data;

    @Override
    public List<WebsiteException> getData() {
        return data;
    }

    public void setData(List<WebsiteException> data) {
        this.data = data;
    }

    private class WebsiteException {
        private String url;
        private String policy;

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

        public String getPolicy() {
            return policy;
        }

        public void setPolicy(String policy) {
            this.policy = policy;
        }
    }
}
