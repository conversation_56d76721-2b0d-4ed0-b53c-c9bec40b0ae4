package com.incs83.app.responsedto.v2.fSecure;

import com.incs83.dto.ApiResponseDTO;

import java.util.List;

public class EventLogResponse extends ApiResponseDTO {
    private List<EventLog> data;

    @Override
    public List<EventLog> getData() {
        return data;
    }

    public void setData(List<EventLog> data) {
        this.data = data;
    }

    private class EventLog{
        private String id;
        private String type;
        private Long timestamp;
        private String macAddress;
        private String url;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }
    }
}
