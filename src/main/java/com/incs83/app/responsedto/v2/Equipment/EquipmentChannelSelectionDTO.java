package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentChannelSelectionDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentChannelSelectionData> data;

    @Override
    public ArrayList<EquipmentChannelSelectionData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentChannelSelectionData> data) {
        this.data = data;
    }

    public class EquipmentChannelSelectionData {
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;
        @ApiModelProperty(notes = "channel", required = true)
        private int channel;

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public int getChannel() {
            return channel;
        }

        public void setChannel(int channel) {
            this.channel = channel;
        }
    }
}
