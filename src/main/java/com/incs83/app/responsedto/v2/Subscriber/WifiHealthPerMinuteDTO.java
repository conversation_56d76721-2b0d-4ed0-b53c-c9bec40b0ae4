package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class WifiHealthPerMinuteDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<WifiHealthPerMinuteDTO.WifiHealthPerMinuteData> data;

    @Override
    public ArrayList<WifiHealthPerMinuteData> getData() {
        return data;
    }

    public void setData(ArrayList<WifiHealthPerMinuteData> data) {
        this.data = data;
    }

    public class WifiHealthPerMinuteData {
        @ApiModelProperty(notes = "value", required = true)
        private double value;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public double getValue() {
            return value;
        }

        public void setValue(double value) {
            this.value = value;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }
    }
}
