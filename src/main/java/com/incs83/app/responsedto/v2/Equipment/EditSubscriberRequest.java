package com.incs83.app.responsedto.v2.Equipment;/* sakshi created on 5/11/19 inside the package - com.incs83.app.responsedto.v2.Equipment */

import com.incs83.auditor.AuditableData;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

public class EditSubscriberRequest implements AuditableData {
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String globalAccountNo;
    private String camsAccountNo;
    private String email;
//    private Set<String> apId;

//    public Set<String> getApId() {
//        return apId;
//    }
//
//    public void setApId(Set<String> apId) {
//        this.apId = apId;
//    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getGlobalAccountNo() {
        return globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public String getCamsAccountNo() {
        return camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
