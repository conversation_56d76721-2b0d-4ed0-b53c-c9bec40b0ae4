package com.incs83.app.responsedto.v2.Profile;

import io.swagger.annotations.ApiModelProperty;

public class ProfileDevicesDTO {
    @ApiModelProperty(notes = "Device MAC", required = true)
    private String mac;
    @ApiModelProperty(notes = "Device name", required = true)
    private String name;
    @ApiModelProperty(notes = "Is Device attach To Profile", required = true)
    private boolean isAttachToProfile;

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isAttachToProfile() {
        return isAttachToProfile;
    }

    public void setAttachToProfile(boolean attachToProfile) {
        isAttachToProfile = attachToProfile;
    }
}
