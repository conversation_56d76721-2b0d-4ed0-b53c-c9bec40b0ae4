package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentMoCALanDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentMoCALanData> data;

    @Override
    public ArrayList<EquipmentMoCALanData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentMoCALanData> data) {
        this.data = data;
    }

    public class EquipmentMoCALanData {
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "hostName", required = true)
        private String hostName;
        @ApiModelProperty(notes = "ip", required = true)
        private String ip;
        @ApiModelProperty(notes = "txPHYRate", required = true)
        private double txPHYRate;
        @ApiModelProperty(notes = "rxPHYRate", required = true)
        private double rxPHYRate;
        @ApiModelProperty(notes = "txBytes", required = true)
        private double txPower;
        @ApiModelProperty(notes = "rxBytes", required = true)
        private double rxPower;
        @ApiModelProperty(notes = "uptime", required = true)
        private long uptime;
        @ApiModelProperty(notes = "attenuation", required = true)
        private String attenuation;
        @ApiModelProperty(notes = "mode", required = true)
        private String mode;

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getHostName() {
            return hostName;
        }

        public void setHostName(String hostName) {
            this.hostName = hostName;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public double getTxPHYRate() {
            return txPHYRate;
        }

        public void setTxPHYRate(double txPHYRate) {
            this.txPHYRate = txPHYRate;
        }

        public double getRxPHYRate() {
            return rxPHYRate;
        }

        public void setRxPHYRate(double rxPHYRate) {
            this.rxPHYRate = rxPHYRate;
        }

        public double getTxPower() {
            return txPower;
        }

        public void setTxPower(double txPower) {
            this.txPower = txPower;
        }

        public double getRxPower() {
            return rxPower;
        }

        public void setRxPower(double rxPower) {
            this.rxPower = rxPower;
        }

        public long getUptime() {
            return uptime;
        }

        public void setUptime(long uptime) {
            this.uptime = uptime;
        }

        public String getAttenuation() {
            return attenuation;
        }

        public void setAttenuation(String attenuation) {
            this.attenuation = attenuation;
        }

        public String getMode() {
            return mode;
        }

        public void setMode(String mode) {
            this.mode = mode;
        }
    }
}
