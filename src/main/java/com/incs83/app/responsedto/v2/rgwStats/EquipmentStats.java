package com.incs83.app.responsedto.v2.rgwStats;

public class EquipmentStats {
    private Long connected;
    private Long disconnected;
    private String isp;
    private Long timestamp;

    public Long getConnected() {
        return connected;
    }

    public void setConnected(Long connected) {
        this.connected = connected;
    }

    public Long getDisconnected() {
        return disconnected;
    }

    public void setDisconnected(<PERSON> disconnected) {
        this.disconnected = disconnected;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        EquipmentStats that = (EquipmentStats) o;

        if (isp != null ? !isp.equals(that.isp) : that.isp != null) return false;
        return timestamp != null ? timestamp.equals(that.timestamp) : that.timestamp == null;
    }

    @Override
    public int hashCode() {
        int result = isp != null ? isp.hashCode() : 0;
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        return result;
    }
}
