package com.incs83.app.responsedto.v2.System;

import com.incs83.app.entities.ConfigProperty;

import java.util.ArrayList;
import java.util.List;

public class ConfigPropertyDTO {

    private String id;
    private String property;
    private String value;
    private boolean applied;
    private String description;

    public ConfigPropertyDTO(){}

    public ConfigPropertyDTO(ConfigProperty configProperty){
        this.id = configProperty.getId();
        this.property = configProperty.getProperty();
        this.value = configProperty.getValue();
        this.applied = configProperty.getIsActive();
    }

    public static List<ConfigPropertyDTO> getConfigPropertyList(List<ConfigProperty> configProperties){
        List<ConfigPropertyDTO> cp = new ArrayList<ConfigPropertyDTO>();

        //if(Objects.nonNull(configProperties) && !configProperties.isEmpty()) {
            for (ConfigProperty configP : configProperties) {
                ConfigPropertyDTO cpDto = new ConfigPropertyDTO();

                cpDto.id = configP.getId();
                //if(Objects.nonNull(configP.getProperty()) && !configP.getProperty().isEmpty())
                    cpDto.property = configP.getProperty();
                //if (Objects.nonNull(configP.getValue()) && !configP.getValue().isEmpty())
                    cpDto.value = configP.getValue();
                //if(Objects.nonNull(configP.getIsActive()))
                    cpDto.applied = configP.getIsActive();
                    cpDto.description = configP.getDescription();


                cp.add(cpDto);
            }
        //}

        return cp;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean isApplied() {
        return applied;
    }

    public void setApplied(boolean applied) {
        this.applied = applied;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
