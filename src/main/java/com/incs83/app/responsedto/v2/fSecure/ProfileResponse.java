package com.incs83.app.responsedto.v2.fSecure;

import com.incs83.dto.ApiResponseDTO;

import java.util.List;

public class ProfileResponse extends ApiResponseDTO {
    private Profile data;

    @Override
    public Profile getData() {
        return data;
    }

    public void setData(Profile data) {
        this.data = data;
    }

    private class Profile {
        private String id;
        private String name;
        private Long bonus;
        private Long usage;
        private Boolean enable;
        private List<String> categories;
        private List<Integer> dailyLimit;
        private List<Schedules> schedules;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public Long getBonus() {
            return bonus;
        }

        public void setBonus(Long bonus) {
            this.bonus = bonus;
        }

        public Long getUsage() {
            return usage;
        }

        public void setUsage(Long usage) {
            this.usage = usage;
        }

        public Boolean getEnable() {
            return enable;
        }

        public void setEnable(Boolean enable) {
            this.enable = enable;
        }

        public List<String> getCategories() {
            return categories;
        }

        public void setCategories(List<String> categories) {
            this.categories = categories;
        }

        public List<Integer> getDailyLimit() {
            return dailyLimit;
        }

        public void setDailyLimit(List<Integer> dailyLimit) {
            this.dailyLimit = dailyLimit;
        }

        public List<Schedules> getSchedules() {
            return schedules;
        }

        public void setSchedules(List<Schedules> schedules) {
            this.schedules = schedules;
        }

        private class Schedules {
            private List<String> day;
            private Long timestart;
            private Long timeend;
            private Boolean enable;

            public List<String> getDay() {
                return day;
            }

            public void setDay(List<String> day) {
                this.day = day;
            }

            public Long getTimestart() {
                return timestart;
            }

            public void setTimestart(Long timestart) {
                this.timestart = timestart;
            }

            public Long getTimeend() {
                return timeend;
            }

            public void setTimeend(Long timeend) {
                this.timeend = timeend;
            }

            public Boolean getEnable() {
                return enable;
            }

            public void setEnable(Boolean enable) {
                this.enable = enable;
            }
        }
    }
}
