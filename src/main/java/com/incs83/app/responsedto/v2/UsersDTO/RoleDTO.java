package com.incs83.app.responsedto.v2.UsersDTO;

import io.swagger.annotations.ApiModelProperty;

public class RoleDTO {
    @ApiModelProperty(notes = "id", required = true)
    private String id;
    @ApiModelProperty(notes = "name", required = true)
    private String name;
    @ApiModelProperty(notes = "type", required = true)
    private String type;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
