package com.incs83.app.responsedto.v2.serviceStats.ResourceSpecification;

public class NetworkInfoDTO {

    private EthernetReceive ethernetReceive;
    private EthernetTransmit ethernetTransmit;

    public EthernetReceive getEthernetReceive() {
        return ethernetReceive;
    }

    public void setEthernetReceive(EthernetReceive ethernetReceive) {
        this.ethernetReceive = ethernetReceive;
    }

    public EthernetTransmit getEthernetTransmit() {
        return ethernetTransmit;
    }

    public void setEthernetTransmit(EthernetTransmit ethernetTransmit) {
        this.ethernetTransmit = ethernetTransmit;
    }

    public class EthernetReceive {
        private String bytes;
        private String drop;
        private String packets;

        public String getBytes() {
            return bytes;
        }

        public void setBytes(String bytes) {
            this.bytes = bytes;
        }

        public String getDrop() {
            return drop;
        }

        public void setDrop(String drop) {
            this.drop = drop;
        }

        public String getPackets() { return packets; }

        public void setPackets(String packets) {
            this.packets = packets;
        }
    }

    public class EthernetTransmit {
        private String bytes;
        private String drop;
        private String packets;

        public String getBytes() {
            return bytes;
        }

        public void setBytes(String bytes) {
            this.bytes = bytes;
        }

        public String getDrop() {
            return drop;
        }

        public void setDrop(String drop) {
            this.drop = drop;
        }

        public String getPackets() {
            return packets;
        }

        public void setPackets(String packets) {
            this.packets = packets;
        }
    }
}
