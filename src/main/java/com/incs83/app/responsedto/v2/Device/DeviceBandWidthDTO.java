package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceBandWidthDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    ArrayList<DeviceBandWidthData> data;

    public ArrayList<DeviceBandWidthData> getData() {
        return data;
    }

    public void setData(ArrayList<DeviceBandWidthData> data) {
        this.data = data;
    }

    public class DeviceBandWidthData {
        @ApiModelProperty(notes = "MAC address", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "Bandwidth consumed", required = true)
        private double bandwidth;
        @ApiModelProperty(notes = "Device name", required = true)
        private String name;
        @ApiModelProperty(notes = "Upstream", required = true)
        private double tx;
        @ApiModelProperty(notes = "Downstream", required = true)
        private double rx;
        @ApiModelProperty(notes = "fromDSHost")
        private boolean fromDSHost;

        public boolean isFromDSHost() {
            return fromDSHost;
        }

        public void setFromDSHost(boolean fromDSHost) {
            this.fromDSHost = fromDSHost;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public double getBandwidth() {
            return bandwidth;
        }

        public void setBandwidth(double bandwidth) {
            this.bandwidth = bandwidth;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public double getTx() {
            return tx;
        }

        public void setTx(double tx) {
            this.tx = tx;
        }

        public double getRx() {
            return rx;
        }

        public void setRx(double rx) {
            this.rx = rx;
        }
    }
}
