package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

public class DeviceAssociationDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private Set<DeviceAssociationData> data;

    @Override
    public Set<DeviceAssociationData> getData() {
        return data;
    }

    public void setData(Set<DeviceAssociationData> data) {
        this.data = data;
    }

    public class DeviceAssociationData implements Comparable {
        @ApiModelProperty(notes = "associatedTo", required = true)
        private String associatedTo;
        @ApiModelProperty(notes = "macAddress", required = true)
        private String macAddress;
        @ApiModelProperty(notes = "vendor", required = true)
        private String vendor;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public String getAssociatedTo() {
            return associatedTo;
        }

        public void setAssociatedTo(String associatedTo) {
            this.associatedTo = associatedTo;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;

            DeviceAssociationData that = (DeviceAssociationData) o;

            if (timestamp != that.timestamp) return false;

            return macAddress != null ? macAddress.equals(that.macAddress) : that.macAddress == null;
        }

        @Override
        public int hashCode() {
            int result = associatedTo != null ? associatedTo.hashCode() : 0;
            result = 31 * result + (macAddress != null ? macAddress.hashCode() : 0);
            result = 31 * result + (vendor != null ? vendor.hashCode() : 0);
            result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
            return result;
        }

        @Override
        public int compareTo(Object o) {
            DeviceAssociationData associationData = (DeviceAssociationData) o;
            return this.timestamp == associationData.getTimestamp() ? 0 : Integer.valueOf(String.valueOf(this.timestamp - associationData.getTimestamp()));
        }
    }
}
