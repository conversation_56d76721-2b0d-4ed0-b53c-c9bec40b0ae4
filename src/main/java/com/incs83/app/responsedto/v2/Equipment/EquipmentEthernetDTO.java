package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentEthernetDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentEthernetData> data;

    @Override
    public ArrayList<EquipmentEthernetData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentEthernetData> data) {
        this.data = data;
    }

    public class EquipmentEthernetData {
        @ApiModelProperty(notes = "status", required = true)
        private String status;
        @ApiModelProperty(notes = "lastChange", required = true)
        private long lastChange;
        @ApiModelProperty(notes = "port", required = true)
        private long port;
        @ApiModelProperty(notes = "rxBytes", required = true)
        private double rxBytes;
        @ApiModelProperty(notes = "txBytes", required = true)
        private double txBytes;
        @ApiModelProperty(notes = "maxBitRate", required = true)
        private int maxBitRate;
        @ApiModelProperty(notes = "currentBitRate", required = true)
        private int currentBitRate;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public long getLastChange() {
            return lastChange;
        }

        public void setLastChange(long lastChange) {
            this.lastChange = lastChange;
        }

        public long getPort() {
            return port;
        }

        public void setPort(long port) {
            this.port = port;
        }

        public double getRxBytes() {
            return rxBytes;
        }

        public void setRxBytes(double rxBytes) {
            this.rxBytes = rxBytes;
        }

        public double getTxBytes() {
            return txBytes;
        }

        public void setTxBytes(double txBytes) {
            this.txBytes = txBytes;
        }

        public int getMaxBitRate() {
            return maxBitRate;
        }

        public void setMaxBitRate(int maxBitRate) {
            this.maxBitRate = maxBitRate;
        }

        public int getCurrentBitRate() {
            return currentBitRate;
        }

        public void setCurrentBitRate(int currentBitRate) {
            this.currentBitRate = currentBitRate;
        }
    }
}
