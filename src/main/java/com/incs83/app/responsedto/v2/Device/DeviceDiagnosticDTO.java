package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

public class DeviceDiagnosticDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private Set<DeviceDiagnosticData> data;

    @Override
    public Set<DeviceDiagnosticData> getData() {
        return data;
    }

    public void setData(Set<DeviceDiagnosticData> data) {
        this.data = data;
    }

    public class DeviceDiagnosticData {
        private long timestamp;
        private String event;
        private String macAddress;
        private String device;
        private String band;
        private String vendor;
        private long trigger;
        private String triggerDescription;
        private long reasonCode;
        private String reasonDescription;

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getEvent() {
            return event;
        }

        public void setEvent(String event) {
            this.event = event;
        }

        public String getMacAddress() {
            return macAddress;
        }

        public void setMacAddress(String macAddress) {
            this.macAddress = macAddress;
        }

        public String getDevice() {
            return device;
        }

        public void setDevice(String device) {
            this.device = device;
        }

        public String getBand() {
            return band;
        }

        public void setBand(String band) {
            this.band = band;
        }

        public String getVendor() {
            return vendor;
        }

        public void setVendor(String vendor) {
            this.vendor = vendor;
        }

        public long getTrigger() {
            return trigger;
        }

        public void setTrigger(long trigger) {
            this.trigger = trigger;
        }

        public String getTriggerDescription() {
            return triggerDescription;
        }

        public void setTriggerDescription(String triggerDescription) {
            this.triggerDescription = triggerDescription;
        }

        public long getReasonCode() {
            return reasonCode;
        }

        public void setReasonCode(long reasonCode) {
            this.reasonCode = reasonCode;
        }

        public String getReasonDescription() {
            return reasonDescription;
        }

        public void setReasonDescription(String reasonDescription) {
            this.reasonDescription = reasonDescription;
        }
    }
}
