package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class RGWDisconnectedDTO extends ApiResponseDTO {

    @ApiModelProperty(notes = "Response Data", required = true)
    private RGWDisconnectedData data;

    @Override
    public RGWDisconnectedData getData() {
        return data;
    }

    public void setData(RGWDisconnectedData data) {
        this.data = data;
    }

    public class RGWDisconnectedData {
        private Long totalCount;
        private ArrayList<RGWDisconnectedModel> objects;

        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public ArrayList<RGWDisconnectedModel> getObjects() {
            return objects;
        }

        public void setObjects(ArrayList<RGWDisconnectedModel> objects) {
            this.objects = objects;
        }

        public class RGWDisconnectedModel {
            @ApiModelProperty(notes = "RGW Serial Number", required = true)
            private String serialNumber;
            @ApiModelProperty(notes = "User Id", required = true)
            private Long lastReportedAt;
            @ApiModelProperty(notes = "RGW MAC Address", required = true)
            private String macAddress;
            @ApiModelProperty(notes = "RGW ISP", required = true)
            private String isp;
            @ApiModelProperty(notes = "RGW Version", required = true)
            private String version;
            @ApiModelProperty(notes = "RGW fwVersion", required = true)
            private String fwVersion;

            public String getSerialNumber() {
                return serialNumber;
            }

            public void setSerialNumber(String serialNumber) {
                this.serialNumber = serialNumber;
            }

            public Long getLastReportedAt() {
                return lastReportedAt;
            }

            public void setLastReportedAt(Long lastReportedAt) {
                this.lastReportedAt = lastReportedAt;
            }

            public String getMacAddress() {
                return macAddress;
            }

            public void setMacAddress(String macAddress) {
                this.macAddress = macAddress;
            }

            public String getIsp() {
                return isp;
            }

            public void setIsp(String isp) {
                this.isp = isp;
            }

            public String getVersion() {
                return version;
            }

            public void setVersion(String version) {
                this.version = version;
            }

            public String getFwVersion() {
                return fwVersion;
            }

            public void setFwVersion(String fwVersion) {
                this.fwVersion = fwVersion;
            }
        }
    }
}