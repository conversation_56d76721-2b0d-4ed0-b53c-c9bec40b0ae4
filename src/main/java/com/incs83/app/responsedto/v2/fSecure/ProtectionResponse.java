package com.incs83.app.responsedto.v2.fSecure;

import com.incs83.dto.ApiResponseDTO;

public class ProtectionResponse extends ApiResponseDTO {
    private boolean browsing;
    private boolean tracking;
    private boolean iot;

    public boolean isBrowsing() {
        return browsing;
    }

    public void setBrowsing(boolean browsing) {
        this.browsing = browsing;
    }

    public boolean isTracking() {
        return tracking;
    }

    public void setTracking(boolean tracking) {
        this.tracking = tracking;
    }

    public boolean isIot() {
        return iot;
    }

    public void setIot(boolean iot) {
        this.iot = iot;
    }
}
