package com.incs83.app.responsedto.v2.clusters;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class ClustersDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<ClustersDTO.ClustersDetail> data;

    @Override
    public ArrayList<ClustersDetail> getData() {
        return data;
    }

    public void setData(ArrayList<ClustersDetail> data) {
        this.data = data;
    }

    public class ClustersDetail {
        @ApiModelProperty(notes = "Cluster id")
        private String id;
        @ApiModelProperty(notes = "Cluster name")
        private String name;
        @ApiModelProperty(notes = "Cluster description")
        private String description;
        @ApiModelProperty(notes = "Total")
        private long total;
        @ApiModelProperty(notes = "Selected")
        private long selected;
        @ApiModelProperty(notes = "UnSelected")
        private long unSelected;
        @ApiModelProperty(notes = "Default Cluster")
        private boolean defaultCluster;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public long getTotal() {
            return total;
        }

        public void setTotal(long total) {
            this.total = total;
        }

        public long getSelected() {
            return selected;
        }

        public void setSelected(long selected) {
            this.selected = selected;
        }

        public long getUnSelected() {
            return unSelected;
        }

        public void setUnSelected(long unSelected) {
            this.unSelected = unSelected;
        }

        public boolean isDefaultCluster() {
            return defaultCluster;
        }

        public void setDefaultCluster(boolean defaultCluster) {
            this.defaultCluster = defaultCluster;
        }
    }
}
