package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceRssiDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<DeviceRssiData> data;

    public ArrayList<DeviceRssiData> getData() {
        return data;
    }

    public void setData(ArrayList<DeviceRssiData> data) {
        this.data = data;
    }

    public class DeviceRssiData implements Comparable<DeviceRssiData> {
        @ApiModelProperty(notes = "RSSI", required = true)
        private double rssi;
        @ApiModelProperty(notes = "Device MAC", required = true)
        private String equipment;
        @ApiModelProperty(notes = "Timestamp", required = true)
        private long timestamp;
        @ApiModelProperty(notes = "RSSI Lower Limit value")
        private Integer rssiRange;

        public double getRssi() {
            return rssi;
        }

        public void setRssi(double rssi) {
            this.rssi = rssi;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public String getEquipment() {
            return equipment;
        }

        public void setEquipment(String equipment) {
            this.equipment = equipment;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public Integer getRssiRange() {
            return rssiRange;
        }

        public void setRssiRange(Integer rssiRange) {
            this.rssiRange = rssiRange;
        }

        @Override
        public int compareTo(DeviceRssiData o) {
            return (this.getTimestamp() < o.getTimestamp()) ? -1 : ((this.getTimestamp() == o.getTimestamp()) ? 0 : 1);
        }
    }
}
