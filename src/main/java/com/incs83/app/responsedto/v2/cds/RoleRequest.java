package com.incs83.app.responsedto.v2.cds;

import com.incs83.auditor.AuditableData;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Objects;
import java.util.Set;

public class RoleRequest implements AuditableData {

    @ApiModelProperty(notes = "Role name, Character Range [1-255]", required = true)
    @NotBlank(message = "RoleRequest.notBlank.name")
    @NotNull(message = "RoleRequest.notBlank.name")
    @Size(message = "Role name size must be between 1 to 255 character", min = 1, max = 255)
    @Pattern(regexp = "^[a-zA-Z\\d\\-_()\\s]+$", message = "Role name, Invalid characters Found")
    private String name;

    @ApiModelProperty(notes = "Role description, Character Range [1-255]")
    @Size(max = 255, message = "Input can be max 254 characters long")
    private String description;

    //@NotEmpty(message = "RoleRequest.notEmpty.roleEntitlement")
    private List<RoleEntitlement> roleEntitlement;

    private List<RolePermission> rolePermissions;

    private Set<String> entities;

    public String getName() {
        return Objects.isNull(name) ? null : name.trim();
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<RoleEntitlement> getRoleEntitlement() {
        return roleEntitlement;
    }

    public void setRoleEntitlement(List<RoleEntitlement> roleEntitlement) {
        this.roleEntitlement = roleEntitlement;
    }

    public String getDescription() {
        return Objects.isNull(description) ? null : description.trim();
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Set<String> getEntities() {
        return entities;
    }

    public void setEntities(Set<String> entities) {
        this.entities = entities;
    }

    public List<RolePermission> getRolePermissions() {
        return rolePermissions;
    }

    public void setRolePermissions(List<RolePermission> rolePermissions) {
        this.rolePermissions = rolePermissions;
    }
}
