package com.incs83.app.responsedto.v2.Equipment;

import com.incs83.auditor.AuditableData;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class EquipmentModel implements AuditableData {
        private String rgwSerial;
        private String rgwMAC;
        private String friendlyName;
        @NotNull(message = "STN can not be null")
        @NotEmpty(message = "STN can not be empty")
        private String serviceTelephoneNo;
        private double downLinkRate = 1000.0;
        private double upLinkRate = 1000.0;
        @NotNull(message = "ISP can not be null")
        @NotEmpty(message = "ISP can not be empty")
        private String isp;
        @NotNull(message = "Subscriber Id can not be null")
        @NotEmpty(message = "Subscriber Id can not be empty")
        private String subscriberId;

        public String getSubscriberId() {
            return subscriberId;
        }

        public void setSubscriberId(String subscriberId) {
            this.subscriberId = subscriberId;
        }

        public String getIsp() {
            return isp;
        }

        public void setIsp(String isp) {
            this.isp = isp;
        }

        public double getDownLinkRate() {
            return downLinkRate;
        }

        public void setDownLinkRate(double downLinkRate) {
            this.downLinkRate = downLinkRate;
        }

        public double getUpLinkRate() {
            return upLinkRate;
        }

        public void setUpLinkRate(double upLinkRate) {
            this.upLinkRate = upLinkRate;
        }

        public String getServiceTelephoneNo() {
            return serviceTelephoneNo;
        }

        public void setServiceTelephoneNo(String serviceTelephoneNo) {
            this.serviceTelephoneNo = serviceTelephoneNo;
        }

        public String getRgwSerial() {
            return rgwSerial;
        }

        public void setRgwSerial(String rgwSerial) {
            this.rgwSerial = rgwSerial;
        }

        public String getRgwMAC() {
            return rgwMAC;
        }

        public void setRgwMAC(String rgwMAC) {
            this.rgwMAC = rgwMAC;
        }

        public String getFriendlyName() {
            return friendlyName;
        }

        public void setFriendlyName(String friendlyName) {
            this.friendlyName = friendlyName;
        }
}
