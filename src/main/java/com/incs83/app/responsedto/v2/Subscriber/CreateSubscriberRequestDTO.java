package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.auditor.AuditableData;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

public class CreateSubscriberRequestDTO implements AuditableData {
    @NotNull(message = "First name can not be null")
    @NotEmpty(message = "First name can not be empty")
    private String firstName;
    @NotNull(message = "Last name can not be null")
    @NotEmpty(message = "Last name can not be empty")
    private String lastName;
    private String phoneNumber;
    @NotNull(message = "Cams Account No can not be null")
    @NotEmpty(message = "Cams Account No can not be empty")
    private String camsAccountNo;
    //    private Set<String> apId;
    @NotNull(message = "ISP can not be null")
    @NotEmpty(message = "ISP can not be empty")
    private String isp;
    @NotNull(message = "Global Account No can not be null")
    @NotEmpty(message = "Global Account No can not be empty")
    private String globalAccountNo;
    @NotNull(message = "Email can not be null")
    @NotEmpty(message = "Email can not be empty")
    private String email;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getCamsAccountNo() {
        return camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }

    public String getGlobalAccountNo() {
        return globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    /*public Set<String> getApId() {
        return apId;
    }

    public void setApId(Set<String> apId) {
        this.apId = apId;
    }*/

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }
}
