package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DeviceWifiPhyDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<DeviceWifiPhyData> data;

    @Override
    public ArrayList<DeviceWifiPhyData> getData() {
        return data;
    }

    public void setData(ArrayList<DeviceWifiPhyData> data) {
        this.data = data;
    }

    public class DeviceWifiPhyData implements Comparable<DeviceWifiPhyData> {
        @ApiModelProperty(notes = "downlinkPhyRate", required = true)
        private double downlinkPhyRate;
        @ApiModelProperty(notes = "uplinkPhyRate", required = true)
        private double uplinkPhyRate;
        @ApiModelProperty(notes = "timestamp", required = true)
        private long timestamp;

        public double getDownlinkPhyRate() {
            return downlinkPhyRate;
        }

        public void setDownlinkPhyRate(double downlinkPhyRate) {
            this.downlinkPhyRate = downlinkPhyRate;
        }

        public double getUplinkPhyRate() {
            return uplinkPhyRate;
        }

        public void setUplinkPhyRate(double uplinkPhyRate) {
            this.uplinkPhyRate = uplinkPhyRate;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        @Override
        public int compareTo(DeviceWifiPhyData o) {
            return (this.getTimestamp() < o.getTimestamp()) ? -1 : ((this.getTimestamp() == o.getTimestamp()) ? 0 : 1);
        }
    }
}
