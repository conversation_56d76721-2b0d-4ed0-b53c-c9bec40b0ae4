package com.incs83.app.responsedto.v2.dataObscurity;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.List;

public class DataObscurityDTO extends ApiResponseDTO {
    private DataObscurity data;

    @Override
    public DataObscurity getData() {
        return data;
    }

    public void setData(DataObscurity data) {
        this.data = data;
    }

    public class DataObscurity {
        private String roleId;
        private String roleName;
        // @ApiModelProperty(example = "[{'key':'macAddress','value':'true','displayName':'Mac Address'}]") // Single quotes cause json file format error
        private List<HashMap<String, Object>> obscureFields;

        public String getRoleId() {
            return roleId;
        }

        public void setRoleId(String roleId) {
            this.roleId = roleId;
        }

        public String getRoleName() {
            return roleName;
        }

        public void setRoleName(String roleName) {
            this.roleName = roleName;
        }

        public List<HashMap<String, Object>> getObscureFields() {
            return obscureFields;
        }

        public void setObscureFields(List<HashMap<String, Object>> obscureFields) {
            this.obscureFields = obscureFields;
        }
    }
}
