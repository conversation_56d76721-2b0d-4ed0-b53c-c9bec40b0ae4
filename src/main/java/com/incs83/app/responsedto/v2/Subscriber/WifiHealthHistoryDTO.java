package com.incs83.app.responsedto.v2.Subscriber;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class WifiHealthHistoryDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<WifiHealthHistoryData> data;

    @Override
    public ArrayList<WifiHealthHistoryData> getData() {
        return data;
    }

    public void setData(ArrayList<WifiHealthHistoryData> data) {
        this.data = data;
    }

    public class WifiHealthHistoryData {
        @ApiModelProperty(notes = "avgWifiHealth", required = true)
        private double avgWifiHealth;
        @ApiModelProperty(notes = "Day", required = true)
        private String Day;

        public double getAvgWifiHealth() {
            return avgWifiHealth;
        }

        public void setAvgWifiHealth(double avgWifiHealth) {
            this.avgWifiHealth = avgWifiHealth;
        }

        public String getDay() {
            return Day;
        }

        public void setDay(String day) {
            Day = day;
        }
    }
}
