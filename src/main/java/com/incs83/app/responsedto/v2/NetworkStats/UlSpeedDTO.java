package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class UlSpeedDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<UlSpeedData> data;

    @Override
    public ArrayList<UlSpeedData> getData() {
        return data;
    }

    public void setData(ArrayList<UlSpeedData> data) {
        this.data = data;
    }

    public class UlSpeedData {
        @ApiModelProperty(notes = "avgUlSpeed", required = true)
        private double avgUlSpeed;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public double getAvgUlSpeed() {
            return avgUlSpeed;
        }

        public void setAvgUlSpeed(double avgUlSpeed) {
            this.avgUlSpeed = avgUlSpeed;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }
}
