package com.incs83.app.responsedto.v2.misc;

import io.swagger.annotations.ApiModelProperty;

public class BadRequestResponseDTO {

    @ApiModelProperty(notes = "Response Code", required = true)
    private Integer code = 401;
    @ApiModelProperty(notes = "Response Message", required = true)
    private String message;
    @ApiModelProperty(notes = "Response Data", required = true)
    private Object data;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }
}
