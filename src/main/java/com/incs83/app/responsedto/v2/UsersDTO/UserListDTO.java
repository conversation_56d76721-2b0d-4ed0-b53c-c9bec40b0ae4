package com.incs83.app.responsedto.v2.UsersDTO;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class UserListDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private UserData data;

    @Override
    public UserData getData() {
        return data;
    }

    public void setData(UserData data) {
        this.data = data;
    }

    public class UserData {
        private Long totalCount;
        private ArrayList<UserModel> objects;

        public Long getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Long totalCount) {
            this.totalCount = totalCount;
        }

        public ArrayList<UserModel> getObjects() {
            return objects;
        }

        public void setObjects(ArrayList<UserModel> objects) {
            this.objects = objects;
        }

        public class UserModel {
            @ApiModelProperty(notes = "id", required = true)
            private String id;
            @ApiModelProperty(notes = "Name", required = true)
            private String name;
            @ApiModelProperty(notes = "email", required = true)
            private String email;
            /*@ApiModelProperty(notes = "Alexa Email", required = true)
            private String alexaEmail;
            @ApiModelProperty(notes = "Google Home Email", required = true)
            private String googleHomeEmail;*/
            /*@ApiModelProperty(notes = "company", required = true)
            private String company;*/
            @ApiModelProperty(notes = "roleName", required = true)
            private String roleName;
            @ApiModelProperty(notes = "groupName", required = true)
            private String groupName;
            @ApiModelProperty(notes = "isSubscriber")
            private boolean subscriber;
            @ApiModelProperty(notes = "isInternalUser")
            private boolean internalUser;

            public String getId() {
                return id;
            }

            public void setId(String id) {
                this.id = id;
            }

            public String getEmail() {
                return email;
            }

            public void setEmail(String email) {
                this.email = email;
            }

            /*public String getCompany() {
                return company;
            }

            public void setCompany(String company) {
                this.company = company;
            }
*/
            public boolean isSubscriber() {
                return subscriber;
            }

            public void setSubscriber(boolean subscriber) {
                this.subscriber = subscriber;
            }

            /*public String getAlexaEmail() {
                return alexaEmail;
            }

            public void setAlexaEmail(String alexaEmail) {
                this.alexaEmail = alexaEmail;
            }

            public String getGoogleHomeEmail() {
                return googleHomeEmail;
            }

            public void setGoogleHomeEmail(String googleHomeEmail) {
                this.googleHomeEmail = googleHomeEmail;
            }*/

            public String getRoleName() {
                return roleName;
            }

            public void setRoleName(String roleName) {
                this.roleName = roleName;
            }

            public String getGroupName() {
                return groupName;
            }

            public void setGroupName(String groupName) {
                this.groupName = groupName;
            }

            public String getName() {
                return name;
            }

            public void setName(String name) {
                this.name = name;
            }

            public boolean isInternalUser() {
                return internalUser;
            }

            public void setInternalUser(boolean internalUser) {
                this.internalUser = internalUser;
            }
        }
    }
}
