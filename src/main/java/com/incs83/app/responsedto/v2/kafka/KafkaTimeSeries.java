package com.incs83.app.responsedto.v2.kafka;

import java.math.BigDecimal;
import java.util.ArrayList;

public class KafkaTimeSeries {
    private String topic;
    private ArrayList<TimeSeries> data;

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public ArrayList<TimeSeries> getData() {
        return data;
    }

    public void setData(ArrayList<TimeSeries> data) {
        this.data = data;
    }

    public class TimeSeries{
        private long timestamp;
        private BigDecimal value;

        public long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(long timestamp) {
            this.timestamp = timestamp;
        }

        public BigDecimal getValue() {
            return value;
        }

        public void setValue(BigDecimal value) {
            this.value = value;
        }
    }
}
