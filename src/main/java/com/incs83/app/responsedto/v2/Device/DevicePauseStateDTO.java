package com.incs83.app.responsedto.v2.Device;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class DevicePauseStateDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private DevicePauseStateDTO.DeviceStateData data;

    @Override
    public DeviceStateData getData() {
        return data;
    }

    public void setData(DeviceStateData data) {
        this.data = data;
    }

    public class DeviceStateData {
        @ApiModelProperty(notes = "Internet Status", required = true)
        private boolean internetOn;
        @ApiModelProperty(notes = "Device name", required = true)
        private String name;
        @ApiModelProperty(notes = "Device MAC", required = true)
        private String mac;

        public boolean isInternetOn() {
            return internetOn;
        }

        public void setInternetOn(boolean internetOn) {
            this.internetOn = internetOn;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getMac() {
            return mac;
        }

        public void setMac(String mac) {
            this.mac = mac;
        }
    }
}
