package com.incs83.app.responsedto.v2.clusters;

import com.incs83.enums.sql.Order;

import java.util.Objects;

public class ClusterSearch {
    private String name;
    private String serialNumber;
    private String email;
    private String rgwMAC;
    private String globalAccountNo;
    private String camsAccountNo;
    private Integer offset = Integer.valueOf(0);
    private Integer max = Integer.valueOf(10);
    private Order order = Order.ASC;
    private String sortBy = "id";
    private boolean selected = false;

    public Integer getOffset() {
        return offset;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Order getOrder() {
        return order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy;
    }

    public String getName() {
        return Objects.nonNull(name) ? name.trim() : null;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getEmail() {
        return Objects.nonNull(email) ? email.trim() : null;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getRgwMAC() {
        return Objects.nonNull(rgwMAC) ? rgwMAC.trim() : null;
    }

    public void setRgwMAC(String rgwMAC) {
        this.rgwMAC = rgwMAC;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getGlobalAccountNo() {
        return globalAccountNo;
    }

    public void setGlobalAccountNo(String globalAccountNo) {
        this.globalAccountNo = globalAccountNo;
    }

    public String getCamsAccountNo() {
        return camsAccountNo;
    }

    public void setCamsAccountNo(String camsAccountNo) {
        this.camsAccountNo = camsAccountNo;
    }
}
