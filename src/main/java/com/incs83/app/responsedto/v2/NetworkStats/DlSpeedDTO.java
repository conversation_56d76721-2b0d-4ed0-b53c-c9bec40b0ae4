package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class DlSpeedDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<DlSpeedData> data;

    @Override
    public ArrayList<DlSpeedData> getData() {
        return data;
    }

    public void setData(ArrayList<DlSpeedData> data) {
        this.data = data;
    }

    public class DlSpeedData {
        @ApiModelProperty(notes = "avgDlSpeed", required = true)
        private double avgDlSpeed;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public double getAvgDlSpeed() {
            return avgDlSpeed;
        }

        public void setAvgDlSpeed(double avgDlSpeed) {
            this.avgDlSpeed = avgDlSpeed;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }
}
