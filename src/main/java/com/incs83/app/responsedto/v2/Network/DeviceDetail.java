package com.incs83.app.responsedto.v2.Network;

import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.List;

public class DeviceDetail {
    @ApiModelProperty(notes = "connectivityStatus", required = true)
    private String connectivityStatus;
    @ApiModelProperty(notes = "macAddress", required = true)
    private String mac;
    @ApiModelProperty(notes = "name", required = true)
    private String name;
    @ApiModelProperty(notes = "connectedTo", required = true)
    private String connectedTo;
    @ApiModelProperty(notes = "rssi", required = true)
    private long rssi;
    @ApiModelProperty(notes = "ssid", required = true)
    private String ssid;
    @ApiModelProperty(notes = "ip", required = true)
    private String ip;
    @ApiModelProperty(notes = "band", required = true)
    private String band;
    @ApiModelProperty(notes = "deviceType", required = true)
    private String deviceType;
    @ApiModelProperty(notes = "InternetOn", required = true)
    private boolean internetOn;
    @ApiModelProperty(notes = "wifiMode", required = true)
    private String wifiMode;
    @ApiModelProperty(notes = "Friendly Name of Equipment, this device is connected to", required = true)
    private String connectedToName;
    @ApiModelProperty(notes = "Alarms for this device", required = true)
    private List<HashMap<String,Object>> alarms;

    public String getConnectivityStatus() {
        return connectivityStatus;
    }

    public void setConnectivityStatus(String connectivityStatus) {
        this.connectivityStatus = connectivityStatus;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getConnectedTo() {
        return connectedTo;
    }

    public void setConnectedTo(String connectedTo) {
        this.connectedTo = connectedTo;
    }

    public long getRssi() {
        return rssi;
    }

    public void setRssi(long rssi) {
        this.rssi = rssi;
    }

    public String getSsid() {
        return ssid;
    }

    public void setSsid(String ssid) {
        this.ssid = ssid;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getBand() {
        return band;
    }

    public void setBand(String band) {
        this.band = band;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public boolean isInternetOn() {
        return internetOn;
    }

    public void setInternetOn(boolean internetOn) {
        this.internetOn = internetOn;
    }

    public String getWifiMode() {
        return wifiMode;
    }

    public void setWifiMode(String wifiMode) {
        this.wifiMode = wifiMode;
    }

    public String getConnectedToName() {
        return connectedToName;
    }

    public void setConnectedToName(String connectedToName) {
        this.connectedToName = connectedToName;
    }

    public List<HashMap<String, Object>> getAlarms() {
        return alarms;
    }

    public void setAlarms(List<HashMap<String, Object>> alarms) {
        this.alarms = alarms;
    }
}
