package com.incs83.app.responsedto.v2.onBoardingStats;

public class OnBoardingStats {
    private String id;
    private String rgw;
    private long timestamp;
    private String remark;
    private String status;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRgw() {
        return rgw;
    }

    public void setRgw(String rgw) {
        this.rgw = rgw;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        OnBoardingStats that = (OnBoardingStats) o;

        return rgw != null ? rgw.equals(that.rgw) : that.rgw == null;
    }

    @Override
    public int hashCode() {
        return 0;
    }
}
