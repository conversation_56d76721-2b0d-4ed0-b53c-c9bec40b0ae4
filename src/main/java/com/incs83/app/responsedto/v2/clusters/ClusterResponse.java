package com.incs83.app.responsedto.v2.clusters;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

public class ClusterResponse extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ClusterInfo data;

    public class ClusterInfo {
        @ApiModelProperty(notes = "Cluster id")
        private String id;
        @ApiModelProperty(notes = "Cluster name")
        private String name;
        @ApiModelProperty(notes = "Cluster description")
        private String description;
        @ApiModelProperty(notes = "Cluster associatedAps")
        private Set<String> associatedAps;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Set<String> getAssociatedAps() {
            return associatedAps;
        }

        public void setAssociatedAps(Set<String> associatedAps) {
            this.associatedAps = associatedAps;
        }
    }

    @Override
    public ClusterInfo getData() {
        return data;
    }

    public void setData(ClusterInfo data) {
        this.data = data;
    }
}
