package com.incs83.app.responsedto.v2.fSecure;

import com.incs83.dto.ApiResponseDTO;

public class EventCategoryResponse extends ApiResponseDTO{

    private Event data;

    @Override
    public Event getData() {
        return data;
    }

    public void setData(Event data) {
        this.data = data;
    }

    private class Event{
        private Long httpBlockBrowsingProtection;
        private Long httpBlockAntibot;
        private Long httpBlockTrackingProtection;
        private Long httpBlockContentFiltering;
        private Long httpBlockBlacklist;
        private Long dnsBlockBrowingProtection;
        private Long dnsBlockAntibot;
        private Long dnsBlockContentFiltering;
        private Long dnsBlockBlacklist;
        private Long iotProtection;
        private Long total;

        public Long getHttpBlockBrowsingProtection() {
            return httpBlockBrowsingProtection;
        }

        public void setHttpBlockBrowsingProtection(Long httpBlockBrowsingProtection) {
            this.httpBlockBrowsingProtection = httpBlockBrowsingProtection;
        }

        public Long getHttpBlockAntibot() {
            return httpBlockAntibot;
        }

        public void setHttpBlockAntibot(Long httpBlockAntibot) {
            this.httpBlockAntibot = httpBlockAntibot;
        }

        public Long getHttpBlockTrackingProtection() {
            return httpBlockTrackingProtection;
        }

        public void setHttpBlockTrackingProtection(Long httpBlockTrackingProtection) {
            this.httpBlockTrackingProtection = httpBlockTrackingProtection;
        }

        public Long getHttpBlockContentFiltering() {
            return httpBlockContentFiltering;
        }

        public void setHttpBlockContentFiltering(Long httpBlockContentFiltering) {
            this.httpBlockContentFiltering = httpBlockContentFiltering;
        }

        public Long getHttpBlockBlacklist() {
            return httpBlockBlacklist;
        }

        public void setHttpBlockBlacklist(Long httpBlockBlacklist) {
            this.httpBlockBlacklist = httpBlockBlacklist;
        }

        public Long getDnsBlockBrowingProtection() {
            return dnsBlockBrowingProtection;
        }

        public void setDnsBlockBrowingProtection(Long dnsBlockBrowingProtection) {
            this.dnsBlockBrowingProtection = dnsBlockBrowingProtection;
        }

        public Long getDnsBlockAntibot() {
            return dnsBlockAntibot;
        }

        public void setDnsBlockAntibot(Long dnsBlockAntibot) {
            this.dnsBlockAntibot = dnsBlockAntibot;
        }

        public Long getDnsBlockContentFiltering() {
            return dnsBlockContentFiltering;
        }

        public void setDnsBlockContentFiltering(Long dnsBlockContentFiltering) {
            this.dnsBlockContentFiltering = dnsBlockContentFiltering;
        }

        public Long getDnsBlockBlacklist() {
            return dnsBlockBlacklist;
        }

        public void setDnsBlockBlacklist(Long dnsBlockBlacklist) {
            this.dnsBlockBlacklist = dnsBlockBlacklist;
        }

        public Long getIotProtection() {
            return iotProtection;
        }

        public void setIotProtection(Long iotProtection) {
            this.iotProtection = iotProtection;
        }

        public Long getTotal() {
            return total;
        }

        public void setTotal(Long total) {
            this.total = total;
        }
    }
}
