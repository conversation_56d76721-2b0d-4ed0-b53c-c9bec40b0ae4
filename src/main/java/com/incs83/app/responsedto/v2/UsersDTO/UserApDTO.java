package com.incs83.app.responsedto.v2.UsersDTO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Created by hari on 2/2/18.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserApDTO {
    private String subscriberId;
    private String serialNumber;
    private String email;

    public UserApDTO() {
    }

    public String getSubscriberId() {
        return subscriberId;
    }

    public UserApDTO setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public UserApDTO setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getEmail() {
        return email;
    }

    public UserApDTO setEmail(String email) {
        this.email = email;
        return this;
    }
}
