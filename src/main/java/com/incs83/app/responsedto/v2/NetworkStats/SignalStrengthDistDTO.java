package com.incs83.app.responsedto.v2.NetworkStats;

import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class SignalStrengthDistDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<SignalStrengthDistDTO.SignalStrength> data;

    public ArrayList<SignalStrength> getData() {
        return data;
    }

    public void setData(ArrayList<SignalStrength> data) {
        this.data = data;
    }

    public class SignalStrength {
        @ApiModelProperty(notes = "excellent", required = true)
        private double excellent;
        @ApiModelProperty(notes = "poor", required = true)
        private double poor;
        @ApiModelProperty(notes = "good", required = true)
        private double good;
        @ApiModelProperty(notes = "veryGood", required = true)
        private double veryGood;
        @ApiModelProperty(notes = "day", required = true)
        private String day;

        public double getExcellent() {
            return excellent;
        }

        public void setExcellent(double excellent) {
            this.excellent = excellent;
        }

        public double getPoor() {
            return poor;
        }

        public void setPoor(double poor) {
            this.poor = poor;
        }

        public double getGood() {
            return good;
        }

        public void setGood(double good) {
            this.good = good;
        }

        public double getVeryGood() {
            return veryGood;
        }

        public void setVeryGood(double veryGood) {
            this.veryGood = veryGood;
        }

        public String getDay() {
            return day;
        }

        public void setDay(String day) {
            this.day = day;
        }
    }

}
