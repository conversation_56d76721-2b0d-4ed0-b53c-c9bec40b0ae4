package com.incs83.app.responsedto.v2.UsersDTO;

import java.util.HashMap;
import java.util.List;

public class DataSecurityDTO {
    private String roleId;
    private String roleName;
    private List<HashMap<String,Object>> obscureFields;

    public String getRoleId() {
        return roleId;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public List<HashMap<String, Object>> getObscureFields() {
        return obscureFields;
    }

    public void setObscureFields(List<HashMap<String, Object>> obscureFields) {
        this.obscureFields = obscureFields;
    }
}
