package com.incs83.app.responsedto.v4;

import com.incs83.app.responsedto.v2.Equipment.EquipmentBusyByDeviceDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentCommonDTO;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentHistoricalDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<EquipmentGraphData> data;

    @Override
    public ArrayList<EquipmentGraphData> getData() {
        return data;
    }

    public void setData(ArrayList<EquipmentGraphData> data) {
        this.data = data;
    }

    private class EquipmentGraphData {
        private ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> _5gBusyByDevice;
        private ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> _24gBusyByDevice;
        private ArrayList<EquipmentCommonDTO.EquipmentBusyData> _24gBusy;
        private ArrayList<EquipmentCommonDTO.EquipmentBusyData> _5gBusy;

        public ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> get_5gBusyByDevice() {
            return _5gBusyByDevice;
        }

        public void set_5gBusyByDevice(ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> _5gBusyByDevice) {
            this._5gBusyByDevice = _5gBusyByDevice;
        }

        public ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> get_24gBusyByDevice() {
            return _24gBusyByDevice;
        }

        public void set_24gBusyByDevice(ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> _24gBusyByDevice) {
            this._24gBusyByDevice = _24gBusyByDevice;
        }

        public ArrayList<EquipmentCommonDTO.EquipmentBusyData> get_24gBusy() {
            return _24gBusy;
        }

        public void set_24gBusy(ArrayList<EquipmentCommonDTO.EquipmentBusyData> _24gBusy) {
            this._24gBusy = _24gBusy;
        }

        public ArrayList<EquipmentCommonDTO.EquipmentBusyData> get_5gBusy() {
            return _5gBusy;
        }

        public void set_5gBusy(ArrayList<EquipmentCommonDTO.EquipmentBusyData> _5gBusy) {
            this._5gBusy = _5gBusy;
        }
    }
}
