package com.incs83.app.responsedto.v4;

import com.incs83.app.responsedto.v2.Device.*;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.Set;

public class DeviceHistoricalDetailDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private DeviceHistoricalData data;

    @Override
    public DeviceHistoricalData getData() {
        return data;
    }

    public void setData(DeviceHistoricalData data) {
        this.data = data;
    }

    private class DeviceHistoricalData {
        private Set<DeviceAssociationDTO.DeviceAssociationData> assocEvents;
        private Set<DeviceDisAssociationDTO.DeviceDisAssociationData> disAssocEvents;
        private Set<DeviceRoamEventDTO.DeviceRoamEventData> roamEvents;
        private Set<DeviceSteeringEventDTO.DeviceSteeringEventData> steeringEvents;
        private Set<DeviceSteeringLogDTO.DeviceSteeringLogData> steeringLogs;
        private ArrayList<DeviceRssiDetailDTO.DeviceRssiData> rssi;
        private ArrayList<DeviceWifiPhyDTO.DeviceWifiPhyData> wifiPhy;
        private ArrayList<DeviceWifiThroughputDTO.DeviceWifiThroughputData> wifiThroughput;

        public Set<DeviceAssociationDTO.DeviceAssociationData> getAssocEvents() {
            return assocEvents;
        }

        public void setAssocEvents(Set<DeviceAssociationDTO.DeviceAssociationData> assocEvents) {
            this.assocEvents = assocEvents;
        }

        public Set<DeviceDisAssociationDTO.DeviceDisAssociationData> getDisAssocEvents() {
            return disAssocEvents;
        }

        public void setDisAssocEvents(Set<DeviceDisAssociationDTO.DeviceDisAssociationData> disAssocEvents) {
            this.disAssocEvents = disAssocEvents;
        }

        public Set<DeviceRoamEventDTO.DeviceRoamEventData> getRoamEvents() {
            return roamEvents;
        }

        public void setRoamEvents(Set<DeviceRoamEventDTO.DeviceRoamEventData> roamEvents) {
            this.roamEvents = roamEvents;
        }

        public Set<DeviceSteeringEventDTO.DeviceSteeringEventData> getSteeringEvents() {
            return steeringEvents;
        }

        public void setSteeringEvents(Set<DeviceSteeringEventDTO.DeviceSteeringEventData> steeringEvents) {
            this.steeringEvents = steeringEvents;
        }

        public Set<DeviceSteeringLogDTO.DeviceSteeringLogData> getSteeringLogs() {
            return steeringLogs;
        }

        public void setSteeringLogs(Set<DeviceSteeringLogDTO.DeviceSteeringLogData> steeringLogs) {
            this.steeringLogs = steeringLogs;
        }

        public ArrayList<DeviceRssiDetailDTO.DeviceRssiData> getRssi() {
            return rssi;
        }

        public void setRssi(ArrayList<DeviceRssiDetailDTO.DeviceRssiData> rssi) {
            this.rssi = rssi;
        }

        public ArrayList<DeviceWifiPhyDTO.DeviceWifiPhyData> getWifiPhy() {
            return wifiPhy;
        }

        public void setWifiPhy(ArrayList<DeviceWifiPhyDTO.DeviceWifiPhyData> wifiPhy) {
            this.wifiPhy = wifiPhy;
        }

        public ArrayList<DeviceWifiThroughputDTO.DeviceWifiThroughputData> getWifiThroughput() {
            return wifiThroughput;
        }

        public void setWifiThroughput(ArrayList<DeviceWifiThroughputDTO.DeviceWifiThroughputData> wifiThroughput) {
            this.wifiThroughput = wifiThroughput;
        }
    }
}
