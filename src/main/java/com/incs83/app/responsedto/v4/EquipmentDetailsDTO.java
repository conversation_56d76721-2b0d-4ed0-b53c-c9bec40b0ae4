package com.incs83.app.responsedto.v4;

import com.incs83.app.responsedto.v2.Equipment.*;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;

public class EquipmentDetailsDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private ArrayList<APDetail> data;

    @Override
    public ArrayList<APDetail> getData() {
        return data;
    }

    public void setData(ArrayList<APDetail> data) {
        this.data = data;
    }

    private class APDetail {
        private ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> ethernet;
        private ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> MoCALAN;
        private EquipmentSystemDTO.EquipmentSystemData systemInfo;
        private EquipmentWirelessDTO.EquipmentWirelessData _5g;
        private EquipmentWirelessDTO.EquipmentWirelessData _24g;
        private EquipmentMoCADTO.EquipmentMoCAData MoCA;
        private EquipmentWANDTO.EquipmentWANData wan;

        public ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> getEthernet() {
            return ethernet;
        }

        public void setEthernet(ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> ethernet) {
            this.ethernet = ethernet;
        }

        public ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> getMoCALAN() {
            return MoCALAN;
        }

        public void setMoCALAN(ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> moCALAN) {
            MoCALAN = moCALAN;
        }

        public EquipmentSystemDTO.EquipmentSystemData getSystemInfo() {
            return systemInfo;
        }

        public void setSystemInfo(EquipmentSystemDTO.EquipmentSystemData systemInfo) {
            this.systemInfo = systemInfo;
        }

        public EquipmentWirelessDTO.EquipmentWirelessData get_5g() {
            return _5g;
        }

        public void set_5g(EquipmentWirelessDTO.EquipmentWirelessData _5g) {
            this._5g = _5g;
        }

        public EquipmentWirelessDTO.EquipmentWirelessData get_24g() {
            return _24g;
        }

        public void set_24g(EquipmentWirelessDTO.EquipmentWirelessData _24g) {
            this._24g = _24g;
        }

        public EquipmentMoCADTO.EquipmentMoCAData getMoCA() {
            return MoCA;
        }

        public void setMoCA(EquipmentMoCADTO.EquipmentMoCAData moCA) {
            MoCA = moCA;
        }

        public EquipmentWANDTO.EquipmentWANData getWan() {
            return wan;
        }

        public void setWan(EquipmentWANDTO.EquipmentWANData wan) {
            this.wan = wan;
        }
    }
}


