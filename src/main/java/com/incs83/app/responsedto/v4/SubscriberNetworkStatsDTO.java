package com.incs83.app.responsedto.v4;

import com.incs83.app.responsedto.v2.Subscriber.InternetDetailDTO;
import com.incs83.dto.ApiResponseDTO;
import io.swagger.annotations.ApiModelProperty;

public class SubscriberNetworkStatsDTO extends ApiResponseDTO {
    @ApiModelProperty(notes = "Response Data", required = true)
    private SubscriberNetworkStats data;

    @Override
    public SubscriberNetworkStats getData() {
        return data;
    }

    public void setData(SubscriberNetworkStats data) {
        this.data = data;
    }

    private class SubscriberNetworkStats {
        private double avgDownlinkRate;
        private double avgUplinkRate;
        private double healthScore;
        private InternetDetailDTO.InternetDetailData internet;

        public double getAvgDownlinkRate() {
            return avgDownlinkRate;
        }

        public void setAvgDownlinkRate(double avgDownlinkRate) {
            this.avgDownlinkRate = avgDownlinkRate;
        }

        public double getAvgUplinkRate() {
            return avgUplinkRate;
        }

        public void setAvgUplinkRate(double avgUplinkRate) {
            this.avgUplinkRate = avgUplinkRate;
        }

        public double getHealthScore() {
            return healthScore;
        }

        public void setHealthScore(double healthScore) {
            this.healthScore = healthScore;
        }

        public InternetDetailDTO.InternetDetailData getInternet() {
            return internet;
        }

        public void setInternet(InternetDetailDTO.InternetDetailData internet) {
            this.internet = internet;
        }
    }

}
