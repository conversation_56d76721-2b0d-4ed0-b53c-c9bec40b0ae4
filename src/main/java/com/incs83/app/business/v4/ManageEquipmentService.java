package com.incs83.app.business.v4;

import com.actiontec.optim.mongodb.dto.RadioEnum;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.ManageSubscriberNetworkStatsService;
import com.incs83.app.business.v2.RPCUtilityService;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.entities.Equipment;
import com.incs83.app.responsedto.v2.Equipment.EquipmentBusyByDeviceDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentCommonDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentEthernetDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentMoCADTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentMoCALanDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentSystemDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentWANDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentWirelessDTO;
import com.incs83.app.responsedto.v3.TimeZoneRequest;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.collections4.map.LinkedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_WIFI_INSIGHTS_PER_MINUTE;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_24G;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_5G;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_BUSY_24G;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_BUSY_5G;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_BUSY_BY_DEVICE_24G;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_BUSY_BY_DEVICE_5G;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_ETHERNET;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_MOCA;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_MOCA_LAN;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_SYSTEM_INFO;
import static com.incs83.app.constants.misc.ActiontecConstants.ATTR_AP_WAN;
import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_DIAGNOSTIC_CONFIG;
import static com.incs83.app.constants.misc.ActiontecConstants.JSON_RPC_V3_INFO;
import static com.incs83.app.constants.misc.ActiontecConstants.RADIO_OCCUPANCY_PER_AP;
import static com.incs83.app.constants.misc.ActiontecConstants.RPC_POLL_COUNT;
import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.HOURLY_DATA;
import static com.incs83.app.constants.misc.ApplicationConstants.THREAD_TO_SLEEP;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_CONFIG;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_INTERNET_UTILIZATION_USAGE_ALARM_THRESHOLD;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_INTERNET_UTILIZATION_USAGE_ALERT_THRESHOLD;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_MEMORY_USAGE_ALARM_THRESHOLD;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_MEMORY_USAGE_ALERT_THRESHOLD;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_RADIO_OCCUPANCY_ALARM_THRESHOLD;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_RADIO_OCCUPANCY_ALERT_THRESHOLD;

/**
 * Created by Jayant on 29/1/18.
 */
@Service("v4.ManageEquipmentService")
public class ManageEquipmentService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private ManageSubscriberNetworkStatsService manageSubscriberNetworkStatsService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private RPCUtilityService rpcUtilityService;

    @Autowired
    private ObjectMapper mapper;

    private List<Map<String, Object>> generateTimeSeriesDataForUser(Equipment userEquipment, String serialNumber, long noOfRecord) {
        List<Map<String, Object>> timeSeriesData = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        long dateHour = manageCommonService.convertMinutesToDateHour(HOURLY_DATA);

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", serialNumber);
        params.put("dateHour", dateCriteria);

        List<DBObject> dbObjectList = mongoService.findList(params, null, AP_WIFI_INSIGHTS_PER_MINUTE, mongoFieldOptions);
        if (!dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                if (Objects.nonNull(dbObject.get("hourlyData"))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get("hourlyData");
                    for (Map.Entry<String, Object> entry : dataByDay.entrySet()) {
                        List<Map<String, Object>> v = (List<Map<String, Object>>) entry.getValue();
                        timeSeriesData.addAll(v);
                    }
                }
            }
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (noOfRecord * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            timeSeriesData = timeSeriesData.stream().filter(element -> Objects.nonNull(element.get("timestamp")) && (Long.valueOf(element.get("timestamp").toString()) > currTimeStamp)).collect(Collectors.toList());
        }
        return timeSeriesData;
    }

    private List<Map<String, Object>> getEquipmentTimeSeriesDataForUser(Equipment userEquipment, String serialNumber, long durationFrom, long durationTo, String collection) throws Exception {
        List<Map<String, Object>> actualData = new ArrayList<>();
        List<Map<String, Object>> combinedData = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        long dateHourFrom = ZonedDateTime.ofInstant( Instant.ofEpochMilli(durationFrom), ZoneOffset.UTC ).truncatedTo(ChronoUnit.DAYS).toEpochSecond();
        long dateHourTo = ZonedDateTime.ofInstant( Instant.ofEpochMilli(durationTo), ZoneOffset.UTC ).truncatedTo(ChronoUnit.DAYS).toEpochSecond();

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHourFrom);
        dateCriteria.put("$lte", dateHourTo);

        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", serialNumber);
        params.put("dateHour", dateCriteria);

        List<DBObject> dbObjectList = mongoService.findList(params, null, collection, mongoFieldOptions);

        if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                if (Objects.nonNull(dbObject.get("hourlyData"))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get("hourlyData");
                    for (Map.Entry<String, Object> mapEntry : dataByDay.entrySet()) {
                        List<Map<String, Object>> slidingDataMap = (List<Map<String, Object>>) mapEntry.getValue();
                        if (!slidingDataMap.isEmpty()) {
                            combinedData.addAll(slidingDataMap);
                        }
                    }
                }
            }

            List<Map<String, Object>> dbData = combinedData.stream().filter(element -> Objects.nonNull(element.get("timestamp")) && ((Long.valueOf(element.get("timestamp").toString()) >= durationFrom) && (Long.valueOf(element.get("timestamp").toString()) < durationTo))).collect(Collectors.toList());
            actualData.addAll(dbData);
        }
        return actualData;
    }

    private List<Map<String, Object>> getEquipmentRadioOccupancyDataForUser(Equipment userEquipment, String serialNumber, long durationFrom, long durationTo, String collection) throws Exception {
        List<Map<String, Object>> actualData = new ArrayList<>();
        List<Map<String, Object>> combinedData = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        long dateHourFrom = ZonedDateTime.ofInstant( Instant.ofEpochMilli(durationFrom), ZoneOffset.UTC ).truncatedTo(ChronoUnit.DAYS).toEpochSecond();
        long dateHourTo = ZonedDateTime.ofInstant( Instant.ofEpochMilli(durationTo), ZoneOffset.UTC ).truncatedTo(ChronoUnit.DAYS).toEpochSecond();

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHourFrom);
        dateCriteria.put("$lte", dateHourTo);

        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", serialNumber);
        params.put("dateHour", dateCriteria);

        List<DBObject> dbObjectList = mongoService.findList(params, null, collection, mongoFieldOptions);

        if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        List<Map<String, Object>> slidingDataMap = (List<Map<String, Object>>) dataByDay.get(key);
                        if (!slidingDataMap.isEmpty()) {
                            combinedData.addAll(slidingDataMap);
                        }
                    }
                }
            }

            List<Map<String, Object>> dbData = combinedData.stream().filter(element -> Objects.nonNull(element.get("timestamp")) && ((Long.valueOf(element.get("timestamp").toString()) >= durationFrom) && (Long.valueOf(element.get("timestamp").toString()) < durationTo))).collect(Collectors.toList());
            actualData.addAll(dbData);
        }
        return actualData;
    }

    private int calculateSeverityForEquipment(/*ArrayList<DeviceDetail> deviceDetailList, */List<HashMap<String, Object>> alarmListForEquipment) {
        int alarmLevelSeverity = -1;
        for (HashMap<String, Object> i : alarmListForEquipment) {
            if (Objects.nonNull(i.get("severity"))) {
                if (Integer.parseInt(i.get("severity").toString()) > alarmLevelSeverity) {
                    alarmLevelSeverity = Integer.parseInt(i.get("severity").toString());
                }
            }
        }
        return alarmLevelSeverity;
    }

    public List<HashMap<String, Object>> getEquipmentAlarmList(BasicDBObject equipment, Equipment userEquipment) {
        //// ALARMS FOR EQUIPMENT START
        List<HashMap<String, Object>> alarmListForEquipment = new ArrayList<>();
        HashMap<String, Object> oneWifiBandAlarm = new HashMap<>();
        HashMap<String, Object> bothWifiBandAlarm = new HashMap<>();
        HashMap<String, Object> radioOccupancy5GAlarm = new HashMap<>();
        HashMap<String, Object> radioOccupancy24GAlarm = new HashMap<>();
        HashMap<String, Object> lowMemoryAlarm = new HashMap<>();
        HashMap<String, Object> notReportingAlarm = new HashMap<>();
        HashMap<String, Object> bandWidthUsageAlarm = new HashMap<>();
        BasicDBList radioList = (BasicDBList) equipment.get("wifiRadios");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_CONFIG);
        Double radioOccupancyHigh = 80.0;
        Double radioOccupancyLow = 60.0;
        try {
            radioOccupancyHigh = Double.valueOf(equipmentProps.get(EQUIPMENT_RADIO_OCCUPANCY_ALARM_THRESHOLD));
            radioOccupancyLow = Double.valueOf(equipmentProps.get(EQUIPMENT_RADIO_OCCUPANCY_ALERT_THRESHOLD));
        } catch (Exception e) {

        }

        Double memUsageHigh = 90.0;
        Double memUsageLow = 80.0;
        try {
            memUsageHigh = Double.valueOf(equipmentProps.get(EQUIPMENT_MEMORY_USAGE_ALARM_THRESHOLD));
            memUsageLow = Double.valueOf(equipmentProps.get(EQUIPMENT_MEMORY_USAGE_ALERT_THRESHOLD));
        } catch (Exception e) {

        }

        Double utilizationHigh = 80.00;
        Double utilizationLow = 60.00;
        try {
            utilizationHigh = Double.valueOf(String.valueOf(equipmentProps.get(EQUIPMENT_INTERNET_UTILIZATION_USAGE_ALARM_THRESHOLD)));
            utilizationLow = Double.valueOf(String.valueOf(equipmentProps.get(EQUIPMENT_INTERNET_UTILIZATION_USAGE_ALERT_THRESHOLD)));
        } catch (Exception e) {

        }

        int deviceConnectedTo24G = 0;
        int deviceConnectedTo5G = 0;
        if (equipment.get("wifiRadios") != null) {
            BasicDBList radioDbList = (BasicDBList) equipment.get("wifiRadios");
            for (Object dbObj : radioDbList) {
                String radioKeyStr = ((BasicDBObject) dbObj).getString("radioKey");
                if (StringUtils.equals(RadioEnum.RADIO_24G.getRadioKey(), radioKeyStr)) {
                    deviceConnectedTo24G = ((BasicDBObject) dbObj).getInt("devicesAssociated");
                } else if (StringUtils.equals(RadioEnum.RADIO_5G.getRadioKey(), radioKeyStr)) {
                    deviceConnectedTo5G = ((BasicDBObject) dbObj).getInt("devicesAssociated");
                }
            }
        }

        if (radioList.size() == 2 && (Objects.nonNull(((BasicDBObject) radioList.get(0)).get("enable")) ? Long.valueOf(((BasicDBObject) radioList.get(0)).get("enable").toString()) : 0) == 0 && (Objects.nonNull(((BasicDBObject) radioList.get(1)).get("enable")) ? Long.valueOf(((BasicDBObject) radioList.get(1)).get("enable").toString()) : 0) == 0) {
            bothWifiBandAlarm.put("alarmType", "equipmentAlarm");
            bothWifiBandAlarm.put("mac", null);
            bothWifiBandAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            bothWifiBandAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            bothWifiBandAlarm.put("severity", 1);
            bothWifiBandAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            bothWifiBandAlarm.put("desc", "is operating with both WiFi bands out of service. Check " + bothWifiBandAlarm.get("name") + "’s configuration and REBOOT if necessary.");
            alarmListForEquipment.add(bothWifiBandAlarm);
        } else if (radioList.size() < 2 || (radioList.size() == 2 && (Objects.nonNull(((BasicDBObject) radioList.get(0)).get("enable")) ? Long.valueOf(((BasicDBObject) radioList.get(0)).get("enable").toString()) : 0) == 0 || (Objects.nonNull(((BasicDBObject) radioList.get(1)).get("enable")) ? Long.valueOf(((BasicDBObject) radioList.get(1)).get("enable").toString()) : 0) == 0)) {
            oneWifiBandAlarm.put("alarmType", "equipmentAlarm");
            oneWifiBandAlarm.put("mac", null);
            oneWifiBandAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            oneWifiBandAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            oneWifiBandAlarm.put("severity", 0);
            oneWifiBandAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            oneWifiBandAlarm.put("desc", "is operating with one WiFi band out of service. Check " + oneWifiBandAlarm.get("name") + "’s configuration and REBOOT if necessary.");
            alarmListForEquipment.add(oneWifiBandAlarm);
        }

        /*BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", userAP.getApId());
        queryParams.put("serialNumber", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
        List<DBObject> radioOccInfo = mongoService.findListByTimestamp(queryParams, AP_WIFI_INSIGHTS_PER_MINUTE, TIMESTAMP, 15, DESC, mongoFieldOptions);*/

        String serialNumber = Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null;
        List<Map<String, Object>> radioOccInfo = generateTimeSeriesDataForUser(userEquipment, serialNumber, 15);

        Double busy5g = 0.0;
        Double busy24g = 0.0;
        ArrayList<Double> busy5gList = new ArrayList<>();
        ArrayList<Double> busy2gList = new ArrayList<>();
        if (!radioOccInfo.isEmpty()) {
            for(Map<String, Object> radioInfo : radioOccInfo) {
                Map<String, Object> radioElement = (Map<String, Object>) radioInfo.get("radio");
                Map<String, Object> wifi5g = (Map<String, Object>) radioElement.get("5g");
                if(wifi5g != null) {
                    busy5gList.add(Double.valueOf(wifi5g.get("occupancy").toString()));
                }
                Map<String, Object> wifi2g = (Map<String, Object>) radioElement.get("2g");
                if(wifi2g != null) {
                    busy2gList.add(Double.valueOf(wifi2g.get("occupancy").toString()));
                }
            }
            busy5g = busy5gList.stream().mapToDouble(val -> val).average().orElse(0.0);
            busy24g = busy2gList.stream().mapToDouble(val -> val).average().orElse(0.0);
        }

        busy5g = Double.valueOf(TWO_DECIMAL_PLACE.format(busy5g));
        busy24g = Double.valueOf(TWO_DECIMAL_PLACE.format(busy24g));

        if (deviceConnectedTo5G > 0) {
            if (busy5g >= radioOccupancyLow && busy5g < radioOccupancyHigh) {
                radioOccupancy5GAlarm.put("alarmType", "equipmentAlarm");
                radioOccupancy5GAlarm.put("mac", null);
                radioOccupancy5GAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                radioOccupancy5GAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                radioOccupancy5GAlarm.put("severity", 0);
                radioOccupancy5GAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                radioOccupancy5GAlarm.put("desc", "5G WiFi average radio occupancy for last 15 minutes is" + busy5g + "%. Check that Smart Steering is enabled. If this problem persists, consider adding a WiFi extender to your network.");
                alarmListForEquipment.add(radioOccupancy5GAlarm);
            } else if (busy5g >= radioOccupancyHigh) {
                radioOccupancy5GAlarm.put("alarmType", "equipmentAlarm");
                radioOccupancy5GAlarm.put("mac", null);
                radioOccupancy5GAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                radioOccupancy5GAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                radioOccupancy5GAlarm.put("severity", 1);
                radioOccupancy5GAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                radioOccupancy5GAlarm.put("desc", "5G WiFi average radio occupancy for last 15 minutes is " + busy5g + "%. Check that Smart Steering is enabled. If this problem persists, consider adding a WiFi extender to your network.");
                alarmListForEquipment.add(radioOccupancy5GAlarm);
            }
        }

        if (deviceConnectedTo24G > 0) {
            if (busy24g >= radioOccupancyLow && busy24g < radioOccupancyHigh) {
                radioOccupancy24GAlarm.put("alarmType", "equipmentAlarm");
                radioOccupancy24GAlarm.put("mac", null);
                radioOccupancy24GAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                radioOccupancy24GAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                radioOccupancy24GAlarm.put("severity", 0);
                radioOccupancy24GAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                radioOccupancy24GAlarm.put("desc", "2.4G WiFi average radio occupancy for last 15 minutes is " + busy24g + "%. Check that Smart Steering is enabled. If this problem persists, consider adding a WiFi extender to your network.");
                alarmListForEquipment.add(radioOccupancy24GAlarm);
            } else if (busy24g >= radioOccupancyHigh) {
                radioOccupancy24GAlarm.put("alarmType", "equipmentAlarm");
                radioOccupancy24GAlarm.put("mac", null);
                radioOccupancy24GAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                radioOccupancy24GAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                radioOccupancy24GAlarm.put("severity", 1);
                radioOccupancy24GAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                radioOccupancy24GAlarm.put("desc", "2.4G WiFi average radio occupancy for last 15 minutes is " + busy24g + "%. Check that Smart Steering is enabled. If this problem persists, consider adding a WiFi extender to your network.");
                alarmListForEquipment.add(radioOccupancy24GAlarm);
            }
        }

        Double memUsage = Objects.nonNull(equipment.get("memUsage")) ? Double.valueOf(equipment.get("memUsage").toString()) : 0.0;
        if (memUsage >= memUsageLow && memUsage < memUsageHigh) {
            lowMemoryAlarm.put("alarmType", "equipmentAlarm");
            lowMemoryAlarm.put("mac", null);
            lowMemoryAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            lowMemoryAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            lowMemoryAlarm.put("severity", 0);
            lowMemoryAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            lowMemoryAlarm.put("desc", "is running low on memory. Power cycle the equipment or click REBOOT when convenient.");
            alarmListForEquipment.add(lowMemoryAlarm);
        } else if (memUsage >= memUsageHigh) {
            lowMemoryAlarm.put("alarmType", "equipmentAlarm");
            lowMemoryAlarm.put("mac", null);
            lowMemoryAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            lowMemoryAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            lowMemoryAlarm.put("severity", 1);
            lowMemoryAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            lowMemoryAlarm.put("desc", "is running low on memory. Power cycle the equipment or click REBOOT when convenient.");
            alarmListForEquipment.add(lowMemoryAlarm);
        }
        if (Objects.nonNull(equipment.get("type")) && equipment.get("type").toString().equals(ApplicationConstants.GATEWAY)) {
            double avgInternetUtilizationFor30Mins = manageSubscriberNetworkStatsService.getAvgInterNetUtilizationForDuration(60, userEquipment);
            avgInternetUtilizationFor30Mins = (avgInternetUtilizationFor30Mins * 8) / (1000F * 1000F); // Converting to Mbps
            double subscriberBandwidth = userEquipment.getDownLinkRate();
            if (subscriberBandwidth == 0)
                subscriberBandwidth = 1000;
            double percentageUtilization = (Double.parseDouble(TWO_DECIMAL_PLACE.format((avgInternetUtilizationFor30Mins * 100 / subscriberBandwidth))));
            if (percentageUtilization >= utilizationLow && percentageUtilization < utilizationHigh) {
                bandWidthUsageAlarm.put("alarmType", "equipmentAlarm");
                bandWidthUsageAlarm.put("mac", null);
                bandWidthUsageAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                bandWidthUsageAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                bandWidthUsageAlarm.put("severity", 0);
                bandWidthUsageAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                bandWidthUsageAlarm.put("desc", "Your Current Internet Usage is approaching the maximum available bandwidth. Review the Top 5 Devices Chart to identify the devices that are using the most bandwidth..");
                alarmListForEquipment.add(bandWidthUsageAlarm);
            } else if (percentageUtilization >= utilizationHigh) {
                bandWidthUsageAlarm.put("alarmType", "equipmentAlarm");
                bandWidthUsageAlarm.put("mac", null);
                bandWidthUsageAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
                bandWidthUsageAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
                bandWidthUsageAlarm.put("severity", 1);
                bandWidthUsageAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                bandWidthUsageAlarm.put("desc", "Your Current Internet Usage is approaching the maximum available bandwidth. Review the Top 5 Devices Chart to identify the devices that are using the most bandwidth..");
                alarmListForEquipment.add(bandWidthUsageAlarm);
            }
        }
        if (manageCommonService.getConnectivityStatusForEquipment(equipment).equals("RED")) {
            notReportingAlarm.put("alarmType", "equipmentAlarm");
            notReportingAlarm.put("mac", null);
            notReportingAlarm.put("serial", Objects.nonNull(equipment.get("serialNumber")) ? equipment.get("serialNumber").toString() : null);
            notReportingAlarm.put("equipmentType", Objects.nonNull(equipment.get("type")) ? equipment.get("type").toString() : null);
            notReportingAlarm.put("severity", 1);
            notReportingAlarm.put("name", Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            notReportingAlarm.put("desc", "is not reporting to Optim. Check that all cables are plugged in correctly, confirm that the " + notReportingAlarm.get("name") + "’s lights are green and then power cycle if necessary.");
            alarmListForEquipment.add(notReportingAlarm);
        }

        return alarmListForEquipment;
    }

    public HashMap<String, Object> getEquipmentDetails(String equipmentIdOrSerialOrSTN, String serialNumber, String attr) throws Exception {
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, Object> aggregationParams = new HashMap<>();
        HashMap<String, Object> aggregationWhereClause = new HashMap<>();

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("serialNumber", serialNumber);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, ApplicationCommonConstants.AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment with SerialNumber : " + serialNumber + " does not belong to Subscriber with Id / RGW Serial: " + userEquipment.getId() + " / " + userEquipment.getRgwSerial());
        }

        ArrayList<String> attributes = new ArrayList<>(Arrays.asList(attr.trim().split(COMMA)));

        Map<String, Object> txRxBytesData = null;

        if (!attributes.contains(ATTR_AP_SYSTEM_INFO)) {
            /*aggregationParams.put("outputParams", "wanTxBytes,wanRxBytes,mocaTxBytes,mocaRxBytes,ethP0TxBytes,ethP0RxBytes,ethP1TxBytes,ethP1RxBytes,ethP2TxBytes,ethP2RxBytes,ethP3TxBytes,ethP3RxBytes,5gTxBytes,5gRxBytes,24gTxBytes,24gRxBytes");
            aggregationParams.put("label", "txRxBytes");
            aggregationParams.put("operation", "$sum");
            aggregationParams.put("keyToAggregate", "$wanDeltaBytesSent,$wanDeltaBytesReceived,$mocaDeltaBytesSent,$mocaDeltaBytesReceived,$ethP0DeltaBytesSent,$ethP0DeltaBytesReceived,$ethP1DeltaBytesSent,$ethP1DeltaBytesReceived,$ethP2DeltaBytesSent,$ethP2DeltaBytesReceived,$ethP3DeltaBytesSent,$ethP3DeltaBytesReceived,$deltaBytesReceived5g,$deltaBytesSent5g,$deltaBytesReceived24g,$deltaBytesSent24g");
            aggregationParams.put("operand", "$gte");
            aggregationWhereClause.put("userId", aPDetails.get("userId"));
            aggregationWhereClause.put("serialNumber", aPDetails.get("serialNumber"));
            txRxBytesData = mongoService.aggregateDataForAllOrResourceId(aggregationWhereClause, null, AP_WIFI_INSIGHTS_PER_MINUTE, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_YESTERDAY), aggregationParams, TIMESTAMP);*/
            txRxBytesData = manageCommonService.getTimeSeriesDataForWifiHealthPerMinute(userEquipment, String.valueOf(aPDetails.get("serialNumber")));
        }

        final Map<String, Object> txRxBytes = Objects.isNull(txRxBytesData) ? null : txRxBytesData;

        HashMap<String, Object> response = new HashMap<>();

        attributes.forEach(type -> {
            switch (type.trim()) {
                case ATTR_AP_ETHERNET:
                    EquipmentEthernetDTO equipmentEthernetDTO = new EquipmentEthernetDTO();
                    ArrayList<EquipmentEthernetDTO.EquipmentEthernetData> equipmentEthernetDataList = new ArrayList<>();
                    List<DBObject> ethernetDetails = (List<DBObject>) aPDetails.get("ethPorts");

                    if (!ethernetDetails.isEmpty()) {
                        ethernetDetails.removeAll(Collections.singleton(null)); // TO REMOVE NULL VALUES FROM ethernetDetails
                        for (int i = 0; i < ethernetDetails.size(); i++) {
                            DBObject item = ethernetDetails.get(i);
                            EquipmentEthernetDTO.EquipmentEthernetData equipmentEthernetData = equipmentEthernetDTO.new EquipmentEthernetData();
                            equipmentEthernetData.setLastChange((Long.parseLong(Objects.isNull(item.get("lastChange")) ? "0" : item.get("lastChange").toString())) * 1000);
                            equipmentEthernetData.setPort(Objects.isNull(item.get("port")) ? 0 : Long.valueOf(item.get("port").toString()));
                            equipmentEthernetData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP" + i + "RxBytes")) ? "0" : (txRxBytes.get("ethP" + i + "RxBytes")).toString()));
                            equipmentEthernetData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP" + i + "TxBytes")) ? "0" : txRxBytes.get("ethP" + i + "TxBytes").toString()));
                            equipmentEthernetData.setStatus(Objects.isNull(item.get("status")) ? null : item.get("status").toString());
                            equipmentEthernetDataList.add(equipmentEthernetData);
                        }

                        if (!equipmentEthernetDataList.isEmpty()) {
                            double totalRx = Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP0RxBytes")) ? "0.0" : txRxBytes.get("ethP0RxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP1RxBytes")) ? "0.0" : txRxBytes.get("ethP1RxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP2RxBytes")) ? "0.0" : txRxBytes.get("ethP2RxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP3RxBytes")) ? "0.0" : txRxBytes.get("ethP3RxBytes").toString());
                            double totalTx = Objects.isNull(txRxBytes) ? 0.0 : Double.valueOf(Objects.isNull(txRxBytes.get("ethP0TxBytes")) ? "0.0" : txRxBytes.get("ethP0TxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP1TxBytes")) ? "0.0" : txRxBytes.get("ethP1TxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP2TxBytes")) ? "0.0" : txRxBytes.get("ethP2TxBytes").toString()) + Double.valueOf(Objects.isNull(txRxBytes.get("ethP3TxBytes")) ? "0.0" : txRxBytes.get("ethP3TxBytes").toString());

                            EquipmentEthernetDTO.EquipmentEthernetData equipmentEthernetData = equipmentEthernetDTO.new EquipmentEthernetData();
                            equipmentEthernetData.setLastChange(0L);
                            equipmentEthernetData.setPort(0L);
                            equipmentEthernetData.setRxBytes(totalRx);
                            equipmentEthernetData.setTxBytes(totalTx);
                            equipmentEthernetData.setStatus(null);
                            equipmentEthernetDataList.add(equipmentEthernetData);
                        }
                    }

                    response.put(type, equipmentEthernetDataList);
                    break;

                case ATTR_AP_MOCA:
                    EquipmentMoCADTO equipmentMoCADTO = new EquipmentMoCADTO();
                    EquipmentMoCADTO.EquipmentMoCAData equipmentMoCAData = equipmentMoCADTO.new EquipmentMoCAData();

                    DBObject mocaDetails = (DBObject) aPDetails.get("moca");

                    if (Objects.nonNull(mocaDetails)) {
                        equipmentMoCAData.setMocaStandard(Objects.isNull(mocaDetails.get("mocaStandard")) ? null : mocaDetails.get("mocaStandard").toString());
                        equipmentMoCAData.setMacAddress(Objects.isNull(mocaDetails.get("macAddress")) ? null : mocaDetails.get("macAddress").toString());
                        equipmentMoCAData.setStatus("1".equals(Objects.isNull(mocaDetails.get("status")) ? null : mocaDetails.get("status").toString()) ? "Up" : "Down");
                        equipmentMoCAData.setLastChange(Long.valueOf(String.valueOf(Objects.isNull(mocaDetails.get("lastChange")) ? 0 : mocaDetails.get("lastChange"))));
                        equipmentMoCAData.setUptime(Objects.isNull(mocaDetails.get("uptime")) ? 0 : (Long.parseLong(mocaDetails.get("uptime").toString())) * 1000);
                        if (Objects.nonNull(equipmentMoCAData.getMocaStandard()) && (!equipmentMoCAData.getMocaStandard().equals("0.0") || !equipmentMoCAData.getStatus().equals("Down"))) {
                            equipmentMoCAData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("mocaRxBytes")) ? "0.0" : String.valueOf(txRxBytes.get("mocaRxBytes"))));
                            equipmentMoCAData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("mocaTxBytes")) ? "0.0" : String.valueOf(txRxBytes.get("mocaTxBytes"))));
                        } else {
                            equipmentMoCAData.setRxBytes(0.0);
                            equipmentMoCAData.setTxBytes(0.0);
                        }
                        response.put(type, equipmentMoCAData);
                    } else {
                        response.put(type, null);
                    }
                    break;

                case ATTR_AP_MOCA_LAN:
                    EquipmentMoCALanDTO equipmentMoCALanDTO = new EquipmentMoCALanDTO();
                    ArrayList<EquipmentMoCALanDTO.EquipmentMoCALanData> equipmentMoCALanDataList = new ArrayList<>();
                    List<DBObject> mocaLanDevices = (List<DBObject>) aPDetails.get("mocaDevices");
                    if (Objects.nonNull(mocaLanDevices)) {
                        mocaLanDevices.parallelStream().forEach(item -> {
                            EquipmentMoCALanDTO.EquipmentMoCALanData equipmentMoCALanData = equipmentMoCALanDTO.new EquipmentMoCALanData();
                            equipmentMoCALanData.setTxPower(Objects.isNull(item.get("txPower")) ? 0.0 : Double.valueOf(item.get("txPower").toString()));
                            equipmentMoCALanData.setRxPower(Objects.isNull(item.get("rxPower")) ? 0.0 : Double.valueOf(item.get("rxPower").toString()));
                            equipmentMoCALanData.setTxPHYRate(Objects.isNull(item.get("txPHYRate")) ? 0.0 : Double.valueOf(item.get("txPHYRate").toString()));
                            equipmentMoCALanData.setRxPHYRate(Objects.isNull(item.get("rxPHYRate")) ? 0.0 : Double.valueOf(item.get("rxPHYRate").toString()));
                            equipmentMoCALanData.setAttenuation(Objects.isNull(item.get("attenuation")) ? "0" : item.get("attenuation").toString());
                            equipmentMoCALanData.setHostName(Objects.isNull(item.get("hostName")) ? null : item.get("hostName").toString());
                            equipmentMoCALanData.setIp(Objects.isNull(item.get("ip")) ? null : item.get("ip").toString());
                            equipmentMoCALanData.setMacAddress(Objects.isNull(item.get("macAddress")) ? null : item.get("macAddress").toString());
                            equipmentMoCALanData.setMode(Objects.isNull(item.get("mode")) ? null : item.get("mode").toString());
                            equipmentMoCALanData.setUptime(Long.valueOf(Objects.isNull(item.get("uptime")) ? "0" : item.get("uptime").toString()));
                            equipmentMoCALanDataList.add(equipmentMoCALanData);
                        });
                    }
                    response.put(type, equipmentMoCALanDataList);
                    break;

                case ATTR_AP_SYSTEM_INFO:
                    EquipmentSystemDTO equipmentSystemDTO = new EquipmentSystemDTO();
                    EquipmentSystemDTO.EquipmentSystemData equipmentSystemData = equipmentSystemDTO.new EquipmentSystemData();

                    Calendar now = Calendar.getInstance();
                    long nowMillSec = now.getTimeInMillis();

                    equipmentSystemData.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(aPDetails));
                    equipmentSystemData.setModelName(Objects.isNull(aPDetails.get("modelName")) ? null : aPDetails.get("modelName").toString());
                    equipmentSystemData.setFwVersion(Objects.isNull(aPDetails.get("fwVersion")) ? null : aPDetails.get("fwVersion").toString());
                    equipmentSystemData.setSerialNumber(Objects.isNull(aPDetails.get("serialNumber")) ? null : aPDetails.get("serialNumber").toString());
                    equipmentSystemData.setMacAddress(Objects.isNull(aPDetails.get("macAddress")) ? null : aPDetails.get("macAddress").toString());
                    equipmentSystemData.setLastReboot(Objects.isNull(aPDetails.get("uptime")) ? nowMillSec : (Long.parseLong(aPDetails.get("uptime").toString()) * 1000));
                    if(manageCommonService.getConnectivityStatusForEquipment(aPDetails).equals("RED"))
                        equipmentSystemData.setUptime("0");
                    else
                        equipmentSystemData.setUptime(CommonUtils.getHistoricalTimeFromDate(Long.parseLong(Objects.isNull(aPDetails.get("uptime")) ? "0" : aPDetails.get("uptime").toString()) * 1000));
                    equipmentSystemData.setMemoryUsage(Objects.isNull(aPDetails.get("memUsage")) ? 0.0 : Double.valueOf(aPDetails.get("memUsage").toString()));
                    equipmentSystemData.setCpuUsage(Objects.isNull(aPDetails.get("cpuUsage")) ? 0.0 : Double.valueOf(aPDetails.get("cpuUsage").toString()));
                    equipmentSystemData.setLastReported(Objects.isNull(aPDetails.get("timestamp")) ? 0 : Long.valueOf(aPDetails.get("timestamp").toString()));

                    equipmentSystemData.setNtInfoTimestamp(Objects.isNull(aPDetails.get("ntInfoTimestamp")) ? 0 : Long.valueOf(aPDetails.get("ntInfoTimestamp").toString()));
                    equipmentSystemData.setNtReportTimestamp(Objects.isNull(aPDetails.get("ntReportTimestamp")) ? 0 : Long.valueOf(aPDetails.get("ntReportTimestamp").toString()));
                    equipmentSystemData.setDbInfoTimestamp(Objects.isNull(aPDetails.get("dbInfoTimestamp")) ? 0 : Long.valueOf(aPDetails.get("dbInfoTimestamp").toString()));
                    equipmentSystemData.setDbReportTimestamp(Objects.isNull(aPDetails.get("dbReportTimestamp")) ? 0 : Long.valueOf(aPDetails.get("dbReportTimestamp").toString()));

                    equipmentSystemData.setSmartSteering(manageCommonService.isSmartSteeringEnabledForDeviceMac(userEquipment.getRgwSerial(), serialNumber));
                    equipmentSystemData.setBuildVersion(Objects.nonNull(aPDetails.get("buildVersion")) ? aPDetails.get("buildVersion").toString() : "N/A");
                    equipmentSystemData.setIsp(Objects.nonNull(aPDetails.get("isp")) ? aPDetails.get("isp").toString() : "N/A");
                    String friendlyName = Objects.isNull(aPDetails.get("friendlyName")) ? Objects.isNull(aPDetails.get("modelName")) ? aPDetails.get("macAddress").toString() : aPDetails.get("modelName").toString() : aPDetails.get("friendlyName").toString();

                    equipmentSystemData.setFriendlyName(friendlyName);
                    List<HashMap<String, Object>> alarmListForEquipment = getEquipmentAlarmList((BasicDBObject) aPDetails, userEquipment);

                    equipmentSystemData.setAlarms(alarmListForEquipment);
                    equipmentSystemData.setSeverity(calculateSeverityForEquipment(alarmListForEquipment));
                    response.put(type, equipmentSystemData);
                    break;

                case ATTR_AP_WAN:
                    EquipmentWANDTO equipmentWANDTO = new EquipmentWANDTO();
                    EquipmentWANDTO.EquipmentWANData equipmentWANData = equipmentWANDTO.new EquipmentWANData();

                    DBObject wanDetails = (DBObject) aPDetails.get("wan");

                    if (Objects.nonNull(wanDetails)) {
                        equipmentWANData.setDevIpAddress(Objects.isNull(wanDetails.get("devIpAddress")) ? null : wanDetails.get("devIpAddress").toString());
                        equipmentWANData.setStatus((wanDetails.get("status") != null && "1".equals(Objects.isNull(wanDetails.get("status")) ? null : wanDetails.get("status").toString())) ? "Up" : "Down");
                        equipmentWANData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("wanRxBytes")) ? "0.0" : txRxBytes.get("wanRxBytes").toString()));
                        equipmentWANData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : Double.parseDouble(Objects.isNull(txRxBytes.get("wanTxBytes")) ? "0.0" : txRxBytes.get("wanTxBytes").toString()));
                        equipmentWANData.setLeaseTimeRemaining(CommonUtils.getHistoricalTimeFromDate(Calendar.getInstance().getTimeInMillis() + (Long.parseLong(String.valueOf(Objects.isNull(wanDetails.get("leaseTimeRemaining")) ? 0 : wanDetails.get("leaseTimeRemaining"))) * 1000)));
                        equipmentWANData.setGateway(Objects.isNull(wanDetails.get("gateway")) ? null : wanDetails.get("gateway").toString());
                        equipmentWANData.setDhcp(Objects.isNull(wanDetails.get("dhcp")) ? null : wanDetails.get("dhcp").toString());
                        equipmentWANData.setDns1(Objects.isNull(wanDetails.get("dns1")) ? null : wanDetails.get("dns1").toString());
                        equipmentWANData.setDns2(Objects.isNull(wanDetails.get("dns2")) ? null : wanDetails.get("dns2").toString());
                        response.put(type, equipmentWANData);
                    } else {
                        response.put(type, null);
                    }

                    break;

                case ATTR_AP_24G:
                    EquipmentWirelessDTO equipmentWirelessDTO = new EquipmentWirelessDTO();
                    EquipmentWirelessDTO.EquipmentWirelessData equipmentWirelessData = equipmentWirelessDTO.new EquipmentWirelessData();

                    DBObject wireLess24g = (DBObject) ((DBObject) aPDetails.get("wifiRadios")).get("0");

                    if (Objects.nonNull(wireLess24g)) {
                        equipmentWirelessData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("24gRxBytes").toString())));
                        equipmentWirelessData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("24gTxBytes").toString())));
                        equipmentWirelessData.setBand(Objects.isNull(wireLess24g.get("band")) ? null : wireLess24g.get("band").toString());
                        equipmentWirelessData.setChannel(Objects.isNull(wireLess24g.get("channel")) ? 0 : Long.valueOf(wireLess24g.get("channel").toString()));
                        equipmentWirelessData.setChannelWidth(Objects.isNull(wireLess24g.get("channelWidth")) ? 0 : Long.valueOf(wireLess24g.get("channelWidth").toString()));
                        equipmentWirelessData.setEnable(Objects.isNull(wireLess24g.get("enable")) ? 0 : Long.valueOf(wireLess24g.get("enable").toString()));
                        Object operatingStandardsObj = wireLess24g.get("operatingStandards");
                        StringBuilder opStandardStrBuilder = new StringBuilder();
                        if (operatingStandardsObj != null) {
                            if (operatingStandardsObj instanceof BasicDBList) { // XXX: for backward compatible
                                boolean first = true;
                                for (Object opStandard : (BasicDBList) operatingStandardsObj) {
                                    if (first) {
                                        first = false;
                                    } else {
                                        opStandardStrBuilder.append(",");
                                    }
                                    opStandardStrBuilder.append(opStandard);
                                }
                            } else {
                                opStandardStrBuilder.append(operatingStandardsObj.toString());
                            }
                        }
                        equipmentWirelessData.setOperatingStandards(opStandardStrBuilder.toString());
                        equipmentWirelessData.setMode("802.11 " + opStandardStrBuilder.toString());
                        equipmentWirelessData.setDeviceAssociated(Objects.isNull(wireLess24g.get("devicesAssociated")) ? 0 : Double.valueOf(wireLess24g.get("devicesAssociated").toString()).longValue());
                        response.put(type, equipmentWirelessData);
                    } else {
                        response.put(type, null);
                    }

                    break;

                case ATTR_AP_5G:
                    EquipmentWirelessDTO equipmentWireless5GDTO = new EquipmentWirelessDTO();
                    EquipmentWirelessDTO.EquipmentWirelessData equipmentWireless5GData = equipmentWireless5GDTO.new EquipmentWirelessData();

                    DBObject wireLess5g = (DBObject) ((DBObject) aPDetails.get("wifiRadios")).get("1");

                    if (Objects.nonNull(wireLess5g)) {
                        equipmentWireless5GData.setRxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("5gRxBytes").toString())));
                        equipmentWireless5GData.setTxBytes(Objects.isNull(txRxBytes) ? 0.0 : (Double.parseDouble(txRxBytes.get("5gTxBytes").toString())));
                        equipmentWireless5GData.setBand(Objects.isNull(wireLess5g.get("band")) ? null : wireLess5g.get("band").toString());
                        equipmentWireless5GData.setChannel(Objects.isNull(wireLess5g.get("channel")) ? 0 : Long.valueOf(wireLess5g.get("channel").toString()));
                        equipmentWireless5GData.setChannelWidth(Objects.isNull(wireLess5g.get("channelWidth")) ? 0 : Long.valueOf(wireLess5g.get("channelWidth").toString()));
                        equipmentWireless5GData.setEnable(Objects.isNull(wireLess5g.get("enable")) ? 0 : Long.valueOf(wireLess5g.get("enable").toString()));
                        Object operatingStandardsObj = wireLess5g.get("operatingStandards");
                        StringBuilder opStandardStrBuilder = new StringBuilder();
                        if (operatingStandardsObj != null) {
                            if (operatingStandardsObj instanceof BasicDBList) { // XXX: for backward compatible
                                boolean first = true;
                                for (Object opStandard : (BasicDBList) operatingStandardsObj) {
                                    if (first) {
                                        first = false;
                                    } else {
                                        opStandardStrBuilder.append(",");
                                    }
                                    opStandardStrBuilder.append(opStandard);
                                }
                            } else {
                                opStandardStrBuilder.append(operatingStandardsObj.toString());
                            }
                        }
                        equipmentWireless5GData.setOperatingStandards(opStandardStrBuilder.toString());
                        equipmentWireless5GData.setMode("802.11 " + opStandardStrBuilder.toString());
                        equipmentWireless5GData.setDeviceAssociated(Objects.isNull(wireLess5g.get("devicesAssociated")) ? 0 : Double.valueOf(wireLess5g.get("devicesAssociated").toString()).longValue());
                        response.put(type, equipmentWireless5GData);
                    } else {
                        response.put(type, null);
                    }

                    break;

                default:
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid attr value : " + type.trim());
            }
        });


        return response;
    }

    public HashMap<String, Object> getEquipmentGraph(String equipmentIdOrSerialOrSTN, String serialNumber, String attr, Long durationFrom, Long durationTo) throws Exception {
        if ((Objects.isNull(durationFrom) || durationFrom <= 0) || (Objects.isNull(durationTo) || durationTo <= 0))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value for duration");

        if (durationFrom > durationTo)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value, durationFrom cannot be greater than durationTo");


//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        String userId = manageCommonService.checkSerialNumOfEquipment(serialNumber, userEquipment);
        manageCommonService.checkAPSerialNumberBelongsToUser(serialNumber, userEquipment);

        List<Map<String, Object>> hourlyData = getEquipmentTimeSeriesDataForUser(userEquipment, serialNumber, durationFrom, durationTo, AP_WIFI_INSIGHTS_PER_MINUTE);
        ArrayList<String> attributes = new ArrayList<>(Arrays.asList(attr.trim().split(COMMA)));
        HashMap<String, Object> response = new HashMap<>();

        attributes.forEach(type -> {
            switch (type.trim()) {
                case ATTR_AP_BUSY_24G:
                    EquipmentCommonDTO equipmentCommon24GDTO = new EquipmentCommonDTO();
                    ArrayList<EquipmentCommonDTO.EquipmentBusyData> equipmentBusy24GData = new ArrayList<>();
                    if (!hourlyData.isEmpty()) {
                        for(Map<String, Object> item : hourlyData) {
                            Map<String, Object> radioElement = (Map<String, Object>) item.get("radio");
                            Map<String, Object> wifi2g = (Map<String, Object>) radioElement.get("2g");
                            if(wifi2g != null) {
                                EquipmentCommonDTO.EquipmentBusyData busyData = equipmentCommon24GDTO.new EquipmentBusyData();
                                busyData.setTimestamp(Long.valueOf(item.get("timestamp").toString()));
                                busyData.setValue(Double.valueOf(wifi2g.get("occupancy").toString()));
                                equipmentBusy24GData.add(busyData);
                            }
                        }
                    }
                    equipmentBusy24GData.sort(Comparator.comparing(EquipmentCommonDTO.EquipmentBusyData::getTimestamp));
                    response.put(type, equipmentBusy24GData);
                    break;

                case ATTR_AP_BUSY_BY_DEVICE_24G:
                    EquipmentBusyByDeviceDTO equipmentBusyByDevice24GDTO = new EquipmentBusyByDeviceDTO();
                    ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> equipmentBusyByDevice24GDataList = new ArrayList<>();
                    if (!hourlyData.isEmpty()) {
                        Map<String, List<Map<String, Object>>> devMap24g = new HashMap<>();
                        for (Map<String, Object> busyByDevice : hourlyData) {
                            Long timestamp = (Long) busyByDevice.get("timestamp");
                            List<Map<String, Object>> deviceRadio24g = (List<Map<String, Object>>) ((Map<String, Object>) busyByDevice.get("wifiSta")).get("2g");

                            if (Objects.nonNull(deviceRadio24g) && !deviceRadio24g.isEmpty()) {
                                for (Map<String, Object> device : deviceRadio24g) {
                                    String macAddress = (String) device.get("macAddress");
                                    device.put("timestamp", timestamp);
                                    device.put("radio", "2.4 GHz");
                                    List<Map<String, Object>> devList = devMap24g.get(macAddress);
                                    if (Objects.nonNull(devList) && !devList.isEmpty()) {
                                        devList.add(device);
                                    } else {
                                        devList = new ArrayList<>();
                                        devMap24g.put(macAddress, devList);
                                        devList.add(device);
                                    }
                                }
                            }
                        }

                        Map<String, Double> aggregatedPercentage24g = new HashMap<>();
                        devMap24g.values().forEach(devList -> {
                            Double percentagePerHour = devList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.isNull(p.get("airTimePercentage")) ? "0.0" : p.get("airTimePercentage").toString())).sum();
                            if (!devList.isEmpty()) {
                                String macAddress = (String) devList.get(0).get("macAddress");
                                aggregatedPercentage24g.put(macAddress, percentagePerHour);
                            }
                        });

                        Map<String, Double> top5Devices24g = aggregatedPercentage24g.entrySet().parallelStream().sorted(Map.Entry.comparingByValue(Comparator.reverseOrder())).limit(5)
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                        (oldValue, newValue) -> oldValue, LinkedMap::new));

                        Set<String> deviceMacAddr = top5Devices24g.keySet();
                        HashMap<String, String> deviceFriendlyName24g = manageCommonService.getFriendlyNameForStation(deviceMacAddr, userEquipment);
                        top5Devices24g.forEach((k, v) -> {
                            List<Map<String, Object>> devEntries = devMap24g.get(k);
                            Set<String> device24GMacList = devEntries.stream().map(element -> String.valueOf(element.get("macAddress"))).collect(Collectors.toSet());
                            HashMap<String, String> device24GFriendlyNameList = manageCommonService.getFriendlyNameForStation(device24GMacList, userEquipment);
                            for (Map<String, Object> device : devEntries) {
                                device.put("name", device24GFriendlyNameList.get(String.valueOf(device.get("macAddress"))));
                            }
                            Collections.reverse(devEntries);
                            EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData equipmentBusyByDeviceData = equipmentBusyByDevice24GDTO.new EquipmentBusyByDeviceData();
                            equipmentBusyByDeviceData.setName(deviceFriendlyName24g.get(k));
                            ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice> busyByDevicesList = new ArrayList<>();
                            devEntries.forEach(element -> {
                                if (Objects.nonNull(element.get("timestamp"))) {
                                    EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice busy24gByDevice = equipmentBusyByDeviceData.new BusyByDevice();
                                    busy24gByDevice.setAirTimePercentage(Double.valueOf(Objects.isNull(element.get("airTimePercentage")) ? "0.0" : element.get("airTimePercentage").toString()));
                                    busy24gByDevice.setMacAddress(Objects.isNull(element.get("macAddress")) ? null : element.get("macAddress").toString());
                                    busy24gByDevice.setName(Objects.isNull(element.get("name")) ? null : element.get("name").toString());
                                    busy24gByDevice.setRadio(Objects.isNull(element.get("radio")) ? null : element.get("radio").toString());
                                    busy24gByDevice.setTimestamp(Long.valueOf(element.get("timestamp").toString()));

                                    busyByDevicesList.add(busy24gByDevice);
                                }
                            });

                            equipmentBusyByDeviceData.setData(busyByDevicesList);
                            equipmentBusyByDevice24GDataList.add(equipmentBusyByDeviceData);
                        });
                    }
                    response.put(type, equipmentBusyByDevice24GDataList);
                    break;

                case ATTR_AP_BUSY_5G:
                    EquipmentCommonDTO equipmentCommon5GDTO = new EquipmentCommonDTO();
                    ArrayList<EquipmentCommonDTO.EquipmentBusyData> equipmentBusy5GData = new ArrayList<>();
                    if (!hourlyData.isEmpty()) {
                        for(Map<String, Object> item : hourlyData) {
                            Map<String, Object> radioElement = (Map<String, Object>) item.get("radio");
                            Map<String, Object> wifi5g = (Map<String, Object>) radioElement.get("5g");
                            if(wifi5g != null) {
                                EquipmentCommonDTO.EquipmentBusyData busyData = equipmentCommon5GDTO.new EquipmentBusyData();
                                busyData.setTimestamp(Long.valueOf(item.get("timestamp").toString()));
                                busyData.setValue(Double.valueOf(wifi5g.get("occupancy").toString()));
                                equipmentBusy5GData.add(busyData);
                            }
                        }
                    }
                    equipmentBusy5GData.sort(Comparator.comparing(EquipmentCommonDTO.EquipmentBusyData::getTimestamp));
                    response.put(type, equipmentBusy5GData);
                    break;

                case ATTR_AP_BUSY_BY_DEVICE_5G:
                    EquipmentBusyByDeviceDTO equipmentBusyByDevice5GDTO = new EquipmentBusyByDeviceDTO();
                    ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData> equipmentBusyByDevice5GDataList = new ArrayList<>();
                    if (!hourlyData.isEmpty()) {
                        Map<String, List<Map<String, Object>>> devMap5g = new HashMap<>();
                        for (Map<String, Object> item: hourlyData) {
                            Long timestamp = (Long) item.get("timestamp");
                            List<Map<String, Object>> deviceRadio5g = (List<Map<String, Object>>) ((Map<String, Object>) item.get("wifiSta")).get("5g");

                            if (Objects.nonNull(deviceRadio5g) && !deviceRadio5g.isEmpty()) {
                                for (Map<String, Object> device : deviceRadio5g) {
                                    String macAddress = (String) device.get("macAddress");
                                    device.put("timestamp", timestamp);
                                    device.put("radio", "5 GHz");
                                    List<Map<String, Object>> devList = devMap5g.get(macAddress);
                                    if (Objects.nonNull(devList) && !devList.isEmpty()) {
                                        devList.add(device);
                                    } else {
                                        devList = new ArrayList<>();
                                        devMap5g.put(macAddress, devList);
                                        devList.add(device);
                                    }
                                }
                            }
                        }

                        Map<String, Double> aggregatedPercentage5g = new HashMap<>();
                        devMap5g.values().forEach(devList -> {
                            Double percentagePerHour = devList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.isNull(p.get("airTimePercentage")) ? "0.0" : p.get("airTimePercentage").toString())).sum();
                            if (!devList.isEmpty()) {
                                String macAddress = (String) devList.get(0).get("macAddress");
                                aggregatedPercentage5g.put(macAddress, percentagePerHour);
                            }
                        });

                        Map<String, Double> top5Devices5g = aggregatedPercentage5g.entrySet().parallelStream().sorted(Map.Entry.comparingByValue(Comparator.reverseOrder())).limit(5)
                                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue,
                                        (oldValue, newValue) -> oldValue, LinkedMap::new));


                        Set<String> deviceMacAddr = top5Devices5g.keySet();
                        HashMap<String, String> deviceFriendlyName5g = manageCommonService.getFriendlyNameForStation(deviceMacAddr, userEquipment);

                        top5Devices5g.forEach((k, v) -> {
                            List<Map<String, Object>> devEntries = devMap5g.get(k);
                            Set<String> device5GMacList = devEntries.stream().map(element -> element.get("macAddress").toString()).collect(Collectors.toSet());
                            HashMap<String, String> device5GFriendlyNameList = manageCommonService.getFriendlyNameForStation(device5GMacList, userEquipment);
                            for (Map<String, Object> device : devEntries) {
                                device.put("name", device5GFriendlyNameList.get(String.valueOf(device.get("macAddress"))));
                            }
                            Collections.reverse(devEntries);
                            EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData equipmentBusyByDeviceData = equipmentBusyByDevice5GDTO.new EquipmentBusyByDeviceData();
                            equipmentBusyByDeviceData.setName(deviceFriendlyName5g.get(k));
                            ArrayList<EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice> busyByDevicesList = new ArrayList<>();
                            devEntries.forEach(element -> {
                                if (Objects.nonNull(element.get("timestamp"))) {
                                    EquipmentBusyByDeviceDTO.EquipmentBusyByDeviceData.BusyByDevice busyByDevice = equipmentBusyByDeviceData.new BusyByDevice();
                                    busyByDevice.setAirTimePercentage(Double.valueOf(Objects.isNull(element.get("airTimePercentage")) ? "0.0" : element.get("airTimePercentage").toString()));
                                    busyByDevice.setMacAddress(Objects.isNull(element.get("macAddress")) ? null : element.get("macAddress").toString());
                                    busyByDevice.setName(Objects.isNull(element.get("name")) ? null : element.get("name").toString());
                                    busyByDevice.setRadio(Objects.isNull(element.get("radio")) ? null : element.get("radio").toString());
                                    busyByDevice.setTimestamp(Long.valueOf(element.get("timestamp").toString()));

                                    busyByDevicesList.add(busyByDevice);
                                }
                            });
                            equipmentBusyByDeviceData.setData(busyByDevicesList);
                            equipmentBusyByDevice5GDataList.add(equipmentBusyByDeviceData);
                        });
                    }
                    response.put(type, equipmentBusyByDevice5GDataList);
                    break;

                default:
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid attr value : " + type.trim());
            }
        });

        return response;
    }

    public Object setTimeZoneForRGW(String equipmentIdOrSerialOrSTN, TimeZoneRequest timeZoneRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId)) {
//            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
//        }
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-PAYLOAD-", mapper.writeValueAsString(timeZoneRequest));
        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-api/time");
        data.put("method", "PUT");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());

        mongoService.create(JSON_RPC_V3_INFO, data);

        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PUT_CPE_RGW_TIME_ZONE, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);
    }

    public Object getTimeZoneForRGW(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId)) {
//            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
//        }

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
//        publishParam.put("-METHOD-", "GET");

        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", equipment.getRgwSerial());

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("uri", "/cpe-api/time");
        data.put("method", "GET");
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());


        mongoService.create(JSON_RPC_V3_INFO, data);



        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.GET_CPE_RGW_TIME_ZONE, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        return manageCommonService.processFSecureRPCResult(tid, max_Tries, THREAD_TO_SLEEP);

    }

}
