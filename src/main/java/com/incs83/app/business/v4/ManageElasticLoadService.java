package com.incs83.app.business.v4;

import com.incs83.Constants.ESConstants;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.constants.queries.EquipmentSQL;
import com.incs83.app.constants.queries.SubscriberSQL;
import com.incs83.app.constants.queries.UserAPSQL;
import com.incs83.app.constants.queries.UserSQL;
import com.incs83.app.entities.ClusterInfo;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.Subscriber;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.business.ESService;
import com.incs83.config.MongoConfig;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.ApiException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.ESServiceImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;

import static com.incs83.Constants.ESConstants.EXT_MAC;
import static com.incs83.Constants.ESConstants.EXT_SERIAL;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.utils.ValidationUtil.isNullString;
import static com.incs83.constants.ApplicationCommonConstants.*;

@Service
@EnableAsync
public class ManageElasticLoadService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Value("${internal.secretKey}")
    private String internalSecretKey;

    @Autowired
    private ESService esService;
    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private MongoConfig mongoConfig;

    @Autowired
    private ESServiceImpl esServiceImpl;

    @Autowired
    private MongoServiceImpl mongoServiceImpl;

    final int max = 10;

    @Async("threadPoolTaskExecutor")
    public void loadUserDataToES(String secretKey, boolean isSecure) throws Exception {
        if (secretKey.equals(internalSecretKey) && isSecure) {
            createElasticSearchIndexes();
            try {
                int offset = 0;
                String queries = UserSQL.COUNT_TOTAL_USERS;
                queries = queries.replaceAll("###", "");
                List<Object> count = (List<Object>) dataAccessService.readNative(queries, new HashMap<>());
                double iterationCount = Math.ceil(Double.valueOf(String.valueOf(count.get(0))) / max);
                for (int i = 0; i < iterationCount; i++) {
                    String limit = " LIMIT " + max;
                    String offsetRow = " OFFSET " + offset;
                    String queryParams = String.format(UserSQL.GET_USER_FOR_ELASTIC, limit, offsetRow);

                    List<Object[]> userList = dataAccessService.readNative(queryParams, new HashMap<>());

                    List<Map<String, Object>> userListDataArrayList = new ArrayList<>();

                    if (Objects.nonNull(userList) && !userList.isEmpty()) {
                        Integer totalIndexed = max * i;
                        for (Object[] userDataList : userList) {
                            Map<String, Object> userData = esServiceImpl.prepareUserDataForES(userDataList);
                            userListDataArrayList.add(userData);
                        }
                    }
                    esService.bulkInsert("user", userListDataArrayList, null);
                    offset += max;
                }
            } catch (Exception e) {
//                throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
                LOG.error("Some Error occured");
            }
        } else {
//            throw new ValidationException((HttpStatus.BAD_REQUEST.value()), "Secret Key Is not Correct");
            LOG.error("Secret Key Is not Correct");
        }

    }

    @Async("threadPoolTaskExecutor")
    public void loadSubscriberDataToES(String secretKey, boolean isSecure) throws Exception {
        if (!(secretKey.equals(internalSecretKey) && isSecure)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Secret Key is not correct");
            LOG.error("Secret Key Is not Correct");
        }
        createElasticSearchIndexes();
        int offset = 0;
        String queries = SubscriberSQL.COUNT_TOTAL_SUBSCRIBERS;
        queries = queries.replaceAll("###", "");
        try {
            List<Object> count = (List<Object>) dataAccessService.readNative(queries, new HashMap<>());
            double iterationCount = Math.ceil(Double.valueOf(String.valueOf(count.get(0))) / max);
            for (int i = 0; i < iterationCount; i++) {

                String offset_Row = " OFFSET " + +(Objects.nonNull(offset) ? offset : 0);
                String subscriber_Limit = " LIMIT " + max;

                String queryParams = String.format(SubscriberSQL.GET_SUBSCRIBERS_FOR_ELASTIC, subscriber_Limit, offset_Row);
                List<Object[]> subscribers = dataAccessService.readNative(queryParams, new HashMap<>());
                List<Map<String, Object>> subscriberDataList = new ArrayList<>();
                Integer totalIndexed = max * i;
                if (Objects.nonNull(subscribers) && !subscribers.isEmpty()) {
                    for (Object subscriber[] : subscribers) {
                        subscriberDataList.add(prepareDataForESSubscriber(subscriber));
                    }
                }
                esService.bulkInsert(SUBSCRIBER_INDEX, subscriberDataList, null);
                offset = offset + max;
            }

        } catch (Exception e) {
            e.printStackTrace();
//            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
            LOG.error("Some Error occured");
        }
    }

    @Async("threadPoolTaskExecutor")
    public void loadEquipmentDataToES(String secretKey, boolean isSecure) throws Exception {
        if (!(secretKey.equals(internalSecretKey) && isSecure)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Secret Key is not correct");
            LOG.error("Secret Key Is not Correct");
        }
        createElasticSearchIndexes();
        int offset = 0;
        String queries = EquipmentSQL.GET_EQUIPMENT_COUNT;
        try {
            List<Object> count = (List<Object>) dataAccessService.readNative(queries, new HashMap<>());
            double iterationCount = Math.ceil(Double.valueOf(String.valueOf(count.get(0))) / max);
            for (int i = 0; i < iterationCount; i++) {
                LOG.info("max ::" + max);
                String offset_Row = " OFFSET " + +(Objects.nonNull(offset) ? offset : 0);
                String subscriber_Limit = " LIMIT " + max;

                String queryParams = String.format(EquipmentSQL.GET_EQUIPMENT_FOR_ELASTIC, subscriber_Limit, offset_Row);
                List<Object[]> equipments = dataAccessService.readNative(queryParams, new HashMap<>());
                List<Map<String, Object>> subscriberDataList = new ArrayList<>();
                Integer totalIndexed = max * i;
                if (Objects.nonNull(equipments) && !equipments.isEmpty()) {
                    for (Object equipment[] : equipments) {
                        subscriberDataList.add(prepareDataForES2(equipment));
                    }
                }
                esService.bulkInsert(EQUIPMENT_INDEX, subscriberDataList, null);
                offset = offset + max;
            }

        } catch (Exception e) {
            e.printStackTrace();
//            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
            LOG.error("Some Error occured");
        }


    }

    private HashMap<String, Object> prepareDataForES2(Object[] equipment) {
        List<String> clusters = new ArrayList<>();
        HashMap<String, Object> userAPMap = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, String.valueOf(equipment[7]));
            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster() && cluster.getName().equals(DEFAULT + compartment.getName())).findAny().orElse(null);
            if (Objects.nonNull(clusterInfo)) {
                clusters.add(clusterInfo.getId());
            }

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", String.valueOf(equipment[1]));
            HashMap<String, Object> projection = new HashMap<>();
            projection.put("type", 1);
            projection.put("serialNumber", 1);
            projection.put("macAddress", 1);
            List<HashMap<String, String>> apDetails = mongoServiceImpl.findList(queryParams, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, projection);
            List<Map<String, Object>> extInfo = new ArrayList<>();
            apDetails.stream().filter(ap -> !GATEWAY.equals(ap.get("type"))).forEach(elem -> {
                Map<String, Object> extender = new HashMap<>();
                extender.put(EXT_MAC, elem.get("macAddress"));
                extender.put(EXT_SERIAL, elem.get("serialNumber"));
                extInfo.add(extender);
            });

            userAPMap = new HashMap<>();
            userAPMap.put("id", isNullString(String.valueOf(equipment[0])));
            userAPMap.put("name", Objects.nonNull(String.valueOf(equipment[10])) && !String.valueOf(equipment[10]).isEmpty()   ? equipment[10] + " " + equipment[11] : isNullString(String.valueOf(equipment[1])) + " " + isNullString(String.valueOf(equipment[2])));
            userAPMap.put("createdAt", ((Timestamp) equipment[8]).getTime());
            userAPMap.put("createdBy", isNullString(String.valueOf(equipment[9])));
            userAPMap.put("email", isNullString(String.valueOf(equipment[14])));
            userAPMap.put("subscriberId", isNullString(String.valueOf(equipment[6])));
            userAPMap.put("mobileSubscribed", false);
            userAPMap.put("downLinkRate", Double.valueOf(String.valueOf(equipment[5])));
            userAPMap.put("upLinkRate", Double.valueOf(String.valueOf(equipment[4])));
            userAPMap.put("rgwMAC", isNullString(String.valueOf(equipment[2])));
            userAPMap.put("apId", isNullString(String.valueOf(equipment[1])));
            userAPMap.put("serialNumber", isNullString(String.valueOf(equipment[1])));
            userAPMap.put("groupId", isNullString(String.valueOf(equipment[7])));
            userAPMap.put("phoneNo", isNullString(String.valueOf(equipment[3])));
            userAPMap.put("globalAccountNo", isNullString(String.valueOf(equipment[13])));
            userAPMap.put("camsAccountNo", isNullString(String.valueOf(equipment[12])));
            userAPMap.put("extInfo", extInfo);
            userAPMap.put("clusters", clusters);
            userAPMap.put("serviceTelephoneNo", isNullString(String.valueOf(equipment[3])));
        } catch (Exception e) {
            LOG.error("ERROR in prepare data for ES." + e);
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }
        return userAPMap;
    }

    private HashMap<String, Object> prepareDataForESSubscriber(Object[]  subscriber) {
        HashMap<String, Object> subscriberMap = new HashMap<>();
        subscriberMap.put("id", isNullString(String.valueOf(subscriber[0])));
        subscriberMap.put("camsAccountNo", isNullString(String.valueOf(subscriber[6])));
        subscriberMap.put("globalAccountNo", isNullString(String.valueOf(subscriber[4])));
        subscriberMap.put("phoneNo", isNullString(String.valueOf(subscriber[7])));
        subscriberMap.put("createdAt", (((Timestamp) subscriber[9]).getTime()));
        subscriberMap.put("createdBy", isNullString(String.valueOf(subscriber[10])));
        subscriberMap.put("email", isNullString(String.valueOf(subscriber[3])));
        subscriberMap.put("name", String.valueOf(subscriber[1]) + " " + String.valueOf(subscriber[2]));
        subscriberMap.put("groupId", isNullString(String.valueOf(subscriber[12])));
        subscriberMap.put("groupName", isNullString(String.valueOf(subscriber[11])));
        subscriberMap.put("imageUrl", isNullString(String.valueOf(subscriber[5])));
        subscriberMap.put("isActive", Boolean.valueOf(String.valueOf(subscriber[8])));
        return subscriberMap;
    }

    private void createElasticSearchIndexes() {
        esService.createIndex(USER_INDEX, ESConstants.USER_INDEX_SOURCE);
        esService.createIndex(EQUIPMENT_INDEX, ESConstants.EQUIPMENT_INDEX_SOURCE);
        esService.createIndex(SUBSCRIBER_INDEX, ESConstants.SUBSCRIBER_INDEX_SOURCE);
    }
}
