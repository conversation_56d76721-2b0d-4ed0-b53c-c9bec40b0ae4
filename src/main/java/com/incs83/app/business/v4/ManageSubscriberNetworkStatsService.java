package com.incs83.app.business.v4;

import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.StationSpeedTest;
import com.incs83.app.enums.APDetailLookType;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.Device.DeviceDetailDTO;
import com.incs83.app.responsedto.v2.Network.EquipmentDetail;
import com.incs83.app.responsedto.v2.Network.ExtenderDetail;
import com.incs83.app.responsedto.v2.Network.TopologyDTO;
import com.incs83.app.responsedto.v2.Subscriber.InternetDetailDTO;
import com.incs83.app.responsedto.v2.Subscriber.InternetUtilizationDTO;
import com.incs83.app.responsedto.v2.Subscriber.SpeedStats;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.DEVICE_CONFIG;
import static com.incs83.constants.ApplicationCommonConstants.DEVICE_RSSI_LIMIT;

@Service("v4.ManageSubscriberNetworkStatsService")
public class ManageSubscriberNetworkStatsService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

    @Autowired
    private ManageDeviceService manageDeviceService;

    private List<Map<String, Object>> getApWifiInsightPerMinuteDataForUser(Equipment userEquipment) {
        List<Map<String, Object>> actualData = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);


        BasicDBObject sort = new BasicDBObject();
        sort.put("dateHour", DESC);

        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", userEquipment.getRgwSerial());

        DBObject wifiInsightPerHour = mongoService.findOne(params, AP_WIFI_INSIGHTS_PER_MINUTE, sort, mongoFieldOptions);
        if (Objects.nonNull(wifiInsightPerHour)) {
            if (Objects.nonNull(wifiInsightPerHour.get("hourlyData"))) {
                Map<String, Object> dataByDay = (Map<String, Object>) wifiInsightPerHour.get("hourlyData");
                String maxDate = Collections.max(dataByDay.keySet());
                List<Map<String, Object>> apWifiInsightData = (List<Map<String, Object>>) dataByDay.get(maxDate);
                if (Objects.nonNull(apWifiInsightData) && !apWifiInsightData.isEmpty()) {
                    apWifiInsightData.sort((q1, q2) -> Long.compare((Long) q2.get("timestamp"), (Long) q1.get("timestamp")));
                    actualData.add(apWifiInsightData.get(0));
                }
            }
        }

        return actualData;
    }


    public double getAvgInterNetUtilizationForDuration(long minutes, Equipment userEquipment) {
        double internetUtilization = 0.0;
        /*BasicDBObject mongoFieldOptions = new BasicDBObject();
        HashMap<String, Object> params = new HashMap<>();
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();
        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);
        params.put("userId", userAP.getApId());
        params.put("dateHour", dateCriteria);
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        List<DBObject> internetUtilizationTableData = (List<DBObject>) mongoService.findList(params, new HashMap<>(), INTERNET_UTILIZATION_SLIDING_DATA, mongoFieldOptions);
        List<Map<String, Object>> actualSlidingData = new ArrayList<>();
        if (Objects.nonNull(internetUtilizationTableData) && !internetUtilizationTableData.isEmpty()) {
            for (DBObject dbObject : internetUtilizationTableData) {
                List<Map<String, Object>> slidingData = (List<Map<String, Object>>) dbObject.get("slidingData");
                if (Objects.nonNull(slidingData) && !slidingData.isEmpty()) {
                    Calendar criteriaCalendar = Calendar.getInstance();
                    criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
                    Long currTimeStamp = criteriaCalendar.getTimeInMillis();
                    slidingData = slidingData.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0.0) > currTimeStamp).collect(Collectors.toList());
                    actualSlidingData.addAll(slidingData);
                }
            }
        }*/
       /* if (!actualSlidingData.isEmpty()) {
            internetUtilization = actualSlidingData.stream().collect(Collectors.averagingDouble(p -> p.get("inetUtilz") == null ? 0.0 : Double.valueOf(String.valueOf(p.get("inetUtilz")))));
        }*/
        return internetUtilization;

    }


    /*public InternetUtilizationDTO getInternetUtilizationLineChartData(String serialNumberOrSubscriberId, String attr, Long durationFrom, Long durationTo) throws Exception {
        if ((Objects.isNull(durationFrom) || durationFrom <= 0) || (Objects.isNull(durationTo) || durationTo <= 0))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value for duration");

        if (durationFrom > durationTo)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value, durationFrom cannot be greater than durationTo");

        if (!attr.equals("internetUtilization"))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid attr value : " + attr);

        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId))
            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
        UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberId);
        manageCommonService.subscriberDataExistInMongo(userAP);
        manageCommonService.subscriberGroupMisMatch(userAP);

        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long dateHourFrom = (durationFrom / 1000) - ((((durationFrom / 1000) / 60) % 60) * 60);
        long dateHourTo = (durationTo / 1000) - ((((durationTo / 1000) / 60) % 60) * 60);

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHourFrom);
        dateCriteria.put("$lte", dateHourTo);

        params.put("userId", userAP.getApId());
        params.put("dateHour", dateCriteria);

        List<DBObject> internetUtilization = (List<DBObject>) mongoService.findList(params, appendableParams, INTERNET_UTILIZATION_SLIDING_DATA, mongoFieldOptions);

        InternetUtilizationDTO internetUtilizationDTO = new InternetUtilizationDTO();
        ArrayList<InternetUtilizationDTO.InternetUtilizationData> internetUtilizationDataList = new ArrayList<>();

        if (Objects.isNull(internetUtilization) || internetUtilization.isEmpty()) {
            internetUtilizationDTO.setData(internetUtilizationDataList);
            return internetUtilizationDTO;
        }

        List<BasicDBObject> actualSlidingDataList = new ArrayList<>();

        for (DBObject dbObject : internetUtilization) {
            List<BasicDBObject> slidingDataList = (List<BasicDBObject>) dbObject.get("slidingData");
            if (Objects.nonNull(slidingDataList) && !slidingDataList.isEmpty()) {
                slidingDataList = slidingDataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) && (durationFrom <= Long.valueOf(element.get("timestamp").toString()) && Long.valueOf(element.get("timestamp").toString()) <= durationTo))).collect(Collectors.toList());
                actualSlidingDataList.addAll(slidingDataList);
            }
        }

        for (int i = 0; i < actualSlidingDataList.size();*//*i += CommonUtils.getSkipFactorForGraphs(actualSlidingDataList)*//*i++) {
            InternetUtilizationDTO.InternetUtilizationData internetUtilizationData = internetUtilizationDTO.new InternetUtilizationData();
            HashMap<String, Object> obj = actualSlidingDataList.get(i);
            internetUtilizationData.setDownloadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format((Objects.isNull(obj.get("downloadSpeed")) ? 0 : Double.valueOf(obj.get("downloadSpeed").toString())) / 1000F / 1000F * 8)));
            internetUtilizationData.setUploadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format((Objects.isNull(obj.get("uploadSpeed")) ? 0 : Double.valueOf(obj.get("uploadSpeed").toString())) / 1000F / 1000F * 8)));
            internetUtilizationData.setTimestamp(Long.valueOf(obj.get("timestamp").toString()));
            internetUtilizationDataList.add(internetUtilizationData);
        }

        if (!internetUtilizationDataList.isEmpty()) {
            internetUtilizationDataList.sort(Comparator.comparing(InternetUtilizationDTO.InternetUtilizationData::getTimestamp));
        }
        internetUtilizationDTO.setData(internetUtilizationDataList);

        return internetUtilizationDTO;
    }*/

    public InternetUtilizationDTO getInternetUtilizationLineChartData(String equipmentIdOrSerialOrSTN, String attr, Long durationFrom, Long durationTo) throws Exception {
        if ((Objects.isNull(durationFrom) || durationFrom <= 0) || (Objects.isNull(durationTo) || durationTo <= 0))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value for duration");

        if (durationFrom > durationTo)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value, durationFrom cannot be greater than durationTo");

        if (!attr.equals("internetUtilization"))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid attr value : " + attr);

//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN))
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        InternetUtilizationDTO internetUtilizationDTO = new InternetUtilizationDTO();
        ArrayList<InternetUtilizationDTO.InternetUtilizationData> internetUtilizationDataList = new ArrayList<>();

        List<Map<String, Object>> internetUtilization = prepareDataForInternetUtilizationSlidingData(userEquipment, durationFrom, durationTo);

        if (Objects.isNull(internetUtilization) || internetUtilization.isEmpty()) {
            internetUtilizationDTO.setData(internetUtilizationDataList);
            return internetUtilizationDTO;
        }

        if (Objects.nonNull(internetUtilization) && !internetUtilization.isEmpty()) {
            internetUtilization = internetUtilization.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) && (durationFrom <= Long.valueOf(element.get("timestamp").toString()) && Long.valueOf(element.get("timestamp").toString()) <= durationTo))).collect(Collectors.toList());
        }


        for (int i = 0; i < internetUtilization.size();/*i += CommonUtils.getSkipFactorForGraphs(actualSlidingDataList)*/i++) {
            InternetUtilizationDTO.InternetUtilizationData internetUtilizationData = internetUtilizationDTO.new InternetUtilizationData();
            Map<String, Object> obj = internetUtilization.get(i);
            if (Objects.nonNull(obj.get("downloadSpeed")) && Objects.nonNull(obj.get("uploadSpeed"))) {
                internetUtilizationData.setDownloadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format((Double.valueOf(obj.get("downloadSpeed").toString())) / 1000F / 1000F * 8)));
                internetUtilizationData.setUploadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format((Double.valueOf(obj.get("uploadSpeed").toString())) / 1000F / 1000F * 8)));
                internetUtilizationData.setTimestamp(Long.valueOf(obj.get("timestamp").toString()));
                internetUtilizationDataList.add(internetUtilizationData);
            }
        }

        if (!internetUtilizationDataList.isEmpty()) {
            internetUtilizationDataList.sort(Comparator.comparing(InternetUtilizationDTO.InternetUtilizationData::getTimestamp));
        }
        internetUtilizationDTO.setData(internetUtilizationDataList);

        return internetUtilizationDTO;
    }


    private List<Map<String, Object>> prepareDataForInternetUtilizationSlidingData(Equipment userEquipment, long durationFrom, long durationTo) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        Calendar now = Calendar.getInstance();

        now.setTimeInMillis(durationFrom);
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        long dateHourFrom = TimeUnit.MILLISECONDS.toSeconds(now.getTimeInMillis());

        now.setTimeInMillis(durationTo);
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        long dateHourTo = TimeUnit.MILLISECONDS.toSeconds(now.getTimeInMillis());

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHourFrom);
        dateCriteria.put("$lte", dateHourTo);

        params.put("userId", userEquipment.getRgwSerial());
        params.put("dateHour", dateCriteria);

        List<DBObject> internetUtilization = (List<DBObject>) mongoService.findList(params, appendableParams, INTERNET_UTILIZATION_SLIDING_DATA, mongoFieldOptions);
        List<Map<String, Object>> actualSlidingData = new ArrayList<>();

        if (Objects.nonNull(internetUtilization) && !internetUtilization.isEmpty()) {
            for (DBObject dbObject : internetUtilization) {
                //TODO Remove on Release36
                String today = String.valueOf(dbObject.get("date"));
                List<Map<String, Object>> slidingData = (List<Map<String, Object>>) dbObject.get("slidingData");
                if (Objects.nonNull(slidingData)) {
                    if (!slidingData.isEmpty()) {
                        actualSlidingData.addAll(slidingData);
                    }
                }
                //TODO END

                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        List<Map<String, Object>> slidingDataMap = (List<Map<String, Object>>) dataByDay.get(key);
                        if (!slidingDataMap.isEmpty()) {
                            actualSlidingData.addAll(slidingDataMap);
                        }
                    }
                }
            }
        }

        return actualSlidingData;
    }


    public HashMap<String, Object> getSubscriberNetworkStats(String equipmentIdOrSerialOrSTN, String attr) throws Exception {
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        params.put("userId", userEquipment.getRgwSerial());
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);


        ArrayList<String> attributes = new ArrayList<>(Arrays.asList(attr.trim().split(COMMA)));

        HashMap<String, Object> response = new HashMap<>();

        attributes.forEach(type -> {
            switch (type.trim()) {
                case ATTR_SUBSCRIBER_AVG_DOWN_LINK_RATE:
                    response.put(type, getSubscriberNetworkStats(userEquipment, LAST_DATA_DOWN_LINK_RATE));
                    break;

                case ATTR_SUBSCRIBER_AVG_UP_LINK_RATE:
                    response.put(type, getSubscriberNetworkStats(userEquipment, LAST_DATA_UPLINK_RATE));
                    break;

                case ATTR_SUBSCRIBER_WIFI_HEALTH_SOCRE:
                    Double healthScore = getSubscriberNetworkStats(userEquipment, WIFI_HEALTH);
                    response.put(type, healthScore);
                    break;

                case ATTR_SUBSCRIBER_INTERNET_DETAILS:
//                    List<WANSpeedTest> wanSpeedTests = mongoService.read(WANSpeedTest.class, new Query().addCriteria(Criteria.where("UserId").is(userAP.getApId())).addCriteria(Criteria.where("date").exists(true)).with(new Sort(Sort.Direction.DESC, DATE)));
                    HashMap<String, Object> query = new HashMap<>();
                    query.put("userId", userEquipment.getRgwSerial());
                    query.put("date", new BasicDBObject("$exists", true));

                    List<DBObject> wanSpeedTests = mongoService.findList(WAN_SPEED_TEST_COLLECTION, query, DATE, DESC);

                    InternetDetailDTO internetDetailDTO = new InternetDetailDTO();
                    InternetDetailDTO.InternetDetailData internetDetailData = internetDetailDTO.new InternetDetailData();
                    SpeedStats speedStats = new SpeedStats();
                    if (!wanSpeedTests.isEmpty() && Objects.nonNull(wanSpeedTests.get(0).get("date"))) {
                        speedStats.setDate(((Date) wanSpeedTests.get(0).get("date")).getTime());
                        speedStats.setDownloadSpeed(Objects.isNull(wanSpeedTests.get(0).get("downloadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeedTests.get(0).get("downloadSpeed"))));
                        speedStats.setLatency(Objects.isNull(wanSpeedTests.get(0).get("latency")) ? 0 : Long.valueOf(wanSpeedTests.get(0).get("latency").toString()));
                        speedStats.setUploadSpeed(Objects.isNull(wanSpeedTests.get(0).get("uploadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeedTests.get(0).get("uploadSpeed"))));
                        speedStats.setResult(Objects.isNull(wanSpeedTests.get(0).get("result")) ? ((speedStats.getDownloadSpeed() == 0 && speedStats.getUploadSpeed() == 0) ? TIME_OUT : "OK") : wanSpeedTests.get(0).get("result").toString());
                    } else {
                        speedStats.setDate(0);
                        speedStats.setDownloadSpeed(0);
                        speedStats.setLatency(0);
                        speedStats.setUploadSpeed(0);
                        speedStats.setResult(TIME_OUT);
                    }

                    Double subscriberBandwidth = userEquipment.getDownLinkRate();
                    if (subscriberBandwidth == 0) {
                        subscriberBandwidth = 1000.0;
                    }

                    internetDetailData.setSpeedStats(speedStats);
                    internetDetailData.setSubscriberRate(userEquipment.getDownLinkRate() > 10000 || userEquipment.getDownLinkRate() <= 0 ? 1000.0 : userEquipment.getDownLinkRate());
                    internetDetailData.setDownLinkRate(userEquipment.getDownLinkRate() > 10000 || userEquipment.getDownLinkRate() <= 0 ? 1000.0 : userEquipment.getDownLinkRate());
                    internetDetailData.setUpLinkRate(userEquipment.getUpLinkRate() > 10000 || userEquipment.getUpLinkRate() <= 0 ? 1000.0 : userEquipment.getUpLinkRate());
                    double utilization = getAvgInterNetUtilizationForDuration(60, userEquipment);
                    utilization = (utilization * 8) / (1000F * 1000F); // Converting to Mbps
                    internetDetailData.setInternetUtilization(Double.parseDouble(TWO_DECIMAL_PLACE.format((utilization * 100 / subscriberBandwidth))));

                    internetDetailDTO.setData(internetDetailData);
                    response.put(type, internetDetailDTO.getData());
                    break;

                default:
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid attr value : " + type.trim());
            }
        });
        return response;
    }

    /*private double getSubscriberNetworkStats(UserAP userAP, String type) {
        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userAP.getApId());

        BasicDBObject grouping = new BasicDBObject();
        grouping.put("userId", "$userId");
        grouping.put("timestamp", "$timestamp");


        BasicDBObject groupParam = new BasicDBObject();
        groupParam.put("_id", grouping);
        groupParam.put("lastDataDownlinkRate", new BasicDBObject("$sum", "$lastDataDownlinkRate"));
        groupParam.put("lastDataUplinkRate", new BasicDBObject("$sum", "$lastDataUplinkRate"));
        groupParam.put("staCount", new BasicDBObject("$sum", "staCount"));
        groupParam.put("avg", new BasicDBObject("$avg", "$score"));

        BasicDBObject match = new BasicDBObject();
        match.put("$match", matchParam);

        BasicDBObject group = new BasicDBObject();
        group.put("$group", groupParam);

        BasicDBObject sort = new BasicDBObject();
        sort.put("$sort", new BasicDBObject("timestamp", DESC));

        BasicDBObject limit = new BasicDBObject();
        limit.put("$limit", 1);

        List<BasicDBObject> pipeline = new ArrayList<>();
        pipeline.add(match);
        pipeline.add(group);
        pipeline.add(sort);
        pipeline.add(limit);

        List<DBObject> dbObjectList = mongoService.aggregationPipeline(pipeline, AP_WIFI_INSIGHTS_PER_MINUTE);

        double data = 0.0;
        try {
            switch (type) {
                case WIFI_HEALTH:
                    if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty())
                        data = (Objects.nonNull(dbObjectList.get(0)) && Objects.nonNull(dbObjectList.get(0).get("avg"))) ? Double.valueOf(TWO_DECIMAL_PLACE.format(dbObjectList.get(0).get("avg"))) : 0;
                    break;

                case LAST_DATA_DOWN_LINK_RATE:
                    if (Objects.isNull(dbObjectList) || dbObjectList.isEmpty() || Objects.isNull(dbObjectList.get(0).get("lastDataDownlinkRate")) || Double.valueOf(dbObjectList.get(0).get("lastDataDownlinkRate").toString()) == 0 || Objects.isNull(dbObjectList.get(0).get("staCount")) || Long.valueOf(dbObjectList.get(0).get("staCount").toString()) == 0)
                        data = 0;
                    else {
                        Double lastDataDownlinkRate = Double.valueOf(dbObjectList.get(0).get("lastDataDownlinkRate").toString());
                        Long staCount = Long.valueOf(dbObjectList.get(0).get("staCount").toString());
                        data = Double.valueOf(TWO_DECIMAL_PLACE.format((lastDataDownlinkRate / 1000F / staCount)));
                    }
                    break;

                case LAST_DATA_UPLINK_RATE:
                    if (Objects.isNull(dbObjectList) || dbObjectList.isEmpty() || Objects.isNull(dbObjectList.get(0).get("lastDataUplinkRate")) || Double.valueOf(dbObjectList.get(0).get("lastDataUplinkRate").toString()) == 0 || Objects.isNull(dbObjectList.get(0).get("staCount")) || Long.valueOf(dbObjectList.get(0).get("staCount").toString()) == 0)
                        data = 0;
                    else {
                        Double lastDataUplinkRate = Double.valueOf(dbObjectList.get(0).get("lastDataUplinkRate").toString());
                        Long staCount = Long.valueOf(dbObjectList.get(0).get("staCount").toString());
                        data = Double.valueOf(TWO_DECIMAL_PLACE.format((lastDataUplinkRate / 1000F / staCount)));
                    }
                    break;
            }

        } catch (Exception e) {
            LOG.error("NumberFormatException occured for getAvgWifiDownlinkRate");
        }


        return data;
    }*/

    private double getSubscriberNetworkStats(Equipment userEquipment, String type) {
 /*       HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        long minutes = HOURLY_DATA;

        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        params.put("userId", userAP.getApId());
        params.put("dateHour", dateCriteria);

        List<DBObject> dbObjectList = mongoService.findList(params, null, AP_WIFI_INSIGHTS_PER_MINUTE, mongoFieldOptions);
        DBObject actualData = null;
        if (!dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                List<DBObject> data = (List<DBObject>) dbObject.get("data");
                if (Objects.nonNull(data) && !data.isEmpty()) {
                    Calendar criteriaCalendar = Calendar.getInstance();
                    criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
                    long currTimeStamp = criteriaCalendar.getTimeInMillis();
                    data = data.stream().filter(element -> Objects.nonNull(element.get("timestamp")) && (Long.valueOf(element.get("timestamp").toString()) > currTimeStamp)).collect(Collectors.toList());
                    if (Objects.nonNull(data) && !data.isEmpty()) {
                        Collections.sort(data, new Comparator<DBObject>() {
                            public int compare(DBObject one, DBObject two) {
                                if (Long.valueOf(one.get("timestamp").toString()) < Long.valueOf(two.get("timestamp").toString()))
                                    return -1;
                                else if (Long.valueOf(one.get("timestamp").toString()) == Long.valueOf(two.get("timestamp").toString()))
                                    return 0;
                                else
                                    return 1;
                            }
                        });
                        Collections.reverse(data);
                    }
                    actualData = data.get(0);
                }
            }
        }*/

        List<Map<String, Object>> actualData = getApWifiInsightPerMinuteDataForUser(userEquipment);
        double data = 0.0;
        double lastDataDownlinkRate = 0.0;
        double lastDataUplinkRate = 0.0;
        long staCount = 0;
        try {
            switch (type) {
                case WIFI_HEALTH:
                    if (!actualData.isEmpty()) {
                        data = actualData.stream().collect(Collectors.averagingDouble(p -> p.get("wifiHealthScore") == null ? 0.0 : Double.valueOf(String.valueOf(p.get("wifiHealthScore")))));
                        data = Double.valueOf(TWO_DECIMAL_PLACE.format(data));
                    }

                    break;

                case LAST_DATA_DOWN_LINK_RATE:
                    try {
                        if (!actualData.isEmpty()) {
                            lastDataDownlinkRate = actualData.stream().collect(Collectors.averagingDouble(p -> p.get("avgDataDownlinkRate") == null ? 0.0 : Double.valueOf(String.valueOf(p.get("avgDataDownlinkRate")))));
                            staCount = actualData.stream().collect(Collectors.summingLong(p -> (Objects.isNull(p.get("staCount")) ? 0 : Long.valueOf(String.valueOf(p.get("staCount"))))));

                            if (lastDataDownlinkRate == 0.0 || staCount == 0)
                                data = 0.0;
                            else {
                                data = Double.valueOf(TWO_DECIMAL_PLACE.format((lastDataDownlinkRate / 1000F)));
                            }
                        }
                    } catch (Exception e) {
                        LOG.error("NumberFormatException occured for getAvgWifiDownlinkRate");
                    }
                    break;

                case LAST_DATA_UPLINK_RATE:
                    try {
                        if (!actualData.isEmpty()) {
                            lastDataUplinkRate = actualData.stream().collect(Collectors.averagingDouble(p -> p.get("avgDataUplinkRate") == null ? 0.0 : Double.valueOf(String.valueOf(p.get("avgDataUplinkRate")))));
                            staCount = actualData.stream().collect(Collectors.summingLong(p -> (Objects.isNull(p.get("staCount")) ? 0 : Long.valueOf(String.valueOf(p.get("staCount"))))));

                            if (lastDataUplinkRate == 0.0 || staCount == 0)
                                data = 0.0;
                            else {
                                data = Double.valueOf(TWO_DECIMAL_PLACE.format((lastDataUplinkRate / 1000F)));
                            }
                        }
                    } catch (Exception e) {
                        LOG.error("NumberFormatException occured for getAvgWifiDownlinkRate");
                    }
                    break;
            }

        } catch (Exception e) {
            LOG.error("NumberFormatException occured for getAvgWifiDownlinkRate");
        }


        return data;
    }

//    private DeviceDetailDTO fetchDeviceDetails(Equipment userEquipment, String macAddr, DBObject device) throws Exception {
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//
//        HashMap<String, String> queryParams = new HashMap<>();
//        HashMap<String, String> appendableParams = new HashMap<>();
//        queryParams.put("userId", userEquipment.getRgwSerial());
//        queryParams.put("macAddress", macAddr);
//
//        DBObject deviceDetails = device;
//
//        queryParams.clear();
//        queryParams.put("macAddress", macAddr);
//
//        DBObject deviceCapability = mongoService.findOne(queryParams, appendableParams, DEVICE_CAPABILITY, TIMESTAMP, DESC, mongoFieldOptions);
//
//        TimeZone timeZone = TimeZone.getTimeZone("UTC");
//        Calendar now = Calendar.getInstance(timeZone);
//        now.set(Calendar.MINUTE, 0);
//        now.set(Calendar.SECOND, 0);
//        now.set(Calendar.MILLISECOND, 0);
//
//        long nowMillSec = now.getTimeInMillis();
//
//        int minutes = 60;
//
//        BasicDBObject dateCriteria = new BasicDBObject();
//        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));
//
//        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
//        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
//        aggregationWhereClause.put("dateHour", dateCriteria);
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
////        mongoFieldOptions.put("devMap." + macAddr, 1);
//
//        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(aggregationWhereClause, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);
//        double downLinkBytes = 0.0;
//        double upLinkBytes = 0.0;
//        if (!deviceDownLinkUpLinkBytes.isEmpty()) {
//            List<DBObject> actualDevMap = new ArrayList<>();
//            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
//                if (Objects.nonNull(dbObject.get("devMap"))) {
//                    List<DBObject> devMap = (List<DBObject>) ((DBObject) dbObject.get("devMap")).get(macAddr);
//                    if (Objects.nonNull(devMap)) {
//                        actualDevMap.addAll(devMap);
//                    }
//                }
//
//                String today = String.valueOf(dbObject.get("date"));
//                if (Objects.nonNull(dbObject.get(today))) {
//                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
//                    for (String key : dataByDay.keySet()) {
//                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
//                        List<DBObject> latestDevMap = (List<DBObject>) userSlidingData.get("devMap").get(macAddr);
//                        if (Objects.nonNull(latestDevMap)) {
//                            actualDevMap.addAll(latestDevMap);
//                        }
//                    }
//                }
//            }
//            Calendar criteriaCalendar = Calendar.getInstance();
//            criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
//            long currTimeStamp = criteriaCalendar.getTimeInMillis();
//            actualDevMap = actualDevMap.stream().filter(element -> ((Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp && (Objects.nonNull(element.get("rx")) && Objects.nonNull(element.get("tx"))))).collect(Collectors.toList());
//            if (!actualDevMap.isEmpty()) {
//                downLinkBytes = downLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("rx")) ? Double.valueOf(element.get("rx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
//                upLinkBytes = upLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("tx")) ? Double.valueOf(element.get("tx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
//            }
//        }
//
////        List<StationSpeedTest> stationSpeedTestList = mongoService.read(StationSpeedTest.class, new Query().addCriteria(Criteria.where("macAddr").is(macAddr)).with(new Sort(Sort.Direction.DESC, DATE)).limit(1));
//
//        HashMap<String, Object> query = new HashMap<>();
//        query.put("macAddr", macAddr);
//
//        List<DBObject> stationSpeedTestList = mongoService.findList(STATION_SPEED_TEST_INFO, query, DATE, DESC);
//
//        StationSpeedTest stationSpeedTest = null;
//        if (Objects.nonNull(stationSpeedTestList) && !stationSpeedTestList.isEmpty()) {
//            stationSpeedTest = new StationSpeedTest();
//            stationSpeedTest.setDate((Date) (stationSpeedTestList.get(0).get("date")));
//            stationSpeedTest.setDataRate(Objects.isNull(stationSpeedTestList.get(0).get("dataRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("dataRate"))));
//            stationSpeedTest.setRxRate(Objects.isNull(stationSpeedTestList.get(0).get("rxRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("rxRate"))));
//            stationSpeedTest.setTxRate(Objects.isNull(stationSpeedTestList.get(0).get("txRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("txRate"))));
//            stationSpeedTest.setBand(Objects.isNull(stationSpeedTestList.get(0).get("band")) ? "N/A" : stationSpeedTestList.get(0).get("band").toString());
//            stationSpeedTest.setIpAddr(Objects.isNull(stationSpeedTestList.get(0).get("ipAddr")) ? "N/A" : stationSpeedTestList.get(0).get("ipAddr").toString());
//            stationSpeedTest.setMacAddr(Objects.isNull(stationSpeedTestList.get(0).get("macAddr")) ? "N/A" : stationSpeedTestList.get(0).get("macAddr").toString());
//            stationSpeedTest.setUserId(Objects.isNull(stationSpeedTestList.get(0).get("userId")) ? "N/A" : stationSpeedTestList.get(0).get("userId").toString());
//        }
//
//        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
//        DeviceDetailDTO.DeviceDetailData deviceDetailData = deviceDetailDTO.new DeviceDetailData();
//        DeviceDetailDTO.DeviceDetailData.DeviceSpeedTest deviceSpeedTest = deviceDetailData.new DeviceSpeedTest();
//
//        if (Objects.nonNull(stationSpeedTest)) {
//            deviceSpeedTest.setDataRate(stationSpeedTest.getDataRate());
//            deviceSpeedTest.setDate(Objects.isNull(stationSpeedTest.getDate()) ? 0 : stationSpeedTest.getDate());
//        } else {
//            deviceSpeedTest.setDataRate(0f);
//            deviceSpeedTest.setDate(0l);
//        }
//        deviceDetailData.setSpeedTestStats(deviceSpeedTest);
//
//        if (Objects.nonNull(deviceDetails)) {
//            String band = Objects.nonNull(deviceDetails.get("band")) ? deviceDetails.get("band").toString() : null;
//            String userId = Objects.nonNull(deviceDetails.get("userId")) ? deviceDetails.get("userId").toString() : null;
//            String serialNumber = Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString();
//            String bssid = Objects.isNull(deviceDetails.get("bssid")) ? null : deviceDetails.get("bssid").toString();
//
//            if (band != null || userId != null || serialNumber != null || bssid != null) {
//                if (band.equals("2.4 GHz"))
//                    deviceDetailData.setSsid(manageCommonService.getSsidForDeviceDetail(userId, serialNumber, bssid, "bssid24G"));
//                else if (band.equals("5 GHz"))
//                    deviceDetailData.setSsid(manageCommonService.getSsidForDeviceDetail(userId, serialNumber, bssid, "bssid5G"));
//            } else {
//                deviceDetailData.setSsid("N/A");
//            }
//
//            deviceDetailData.setConnectedTo(Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString());
//            if (Objects.nonNull(deviceDetailData.getConnectedTo())) {
//                HashSet<String> serialNumbers = new HashSet<>();
//                serialNumbers.add(deviceDetailData.getConnectedTo());
//                HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
//                deviceDetailData.setConnectedToName(equipmentFriendlyName.get(deviceDetailData.getConnectedTo()));
//            }
//            deviceDetailData.setName(manageCommonService.getDisplayNameForStation(deviceDetails.get("macAddress").toString(), userEquipment));
//            deviceDetailData.setDeviceType(Objects.isNull(deviceDetails.get("deviceType")) ? "Other" : deviceDetails.get("deviceType").toString());
//            Set<String> macAddressSet = new HashSet<>();
//            macAddressSet.add(deviceDetails.get("macAddress").toString());
//            deviceDetailData.setHostname(manageCommonService.getHostNameForStation(macAddressSet, userEquipment).get(deviceDetails.get("macAddress").toString()));
//            deviceDetailData.setIp(Objects.isNull(deviceDetails.get("ip")) ? null : deviceDetails.get("ip").toString());
//            deviceDetailData.setMac(Objects.isNull(deviceDetails.get("macAddress")) ? null : deviceDetails.get("macAddress").toString());
//            deviceDetailData.setVendor(manageCommonService.getVendorName(String.valueOf(deviceDetails.get("macAddress"))));
//            deviceDetailData.setWifiMode(Objects.isNull(deviceDetails.get("wifiMode")) ? null : deviceDetails.get("wifiMode").toString());
//            deviceDetailData.setBand(Objects.isNull(deviceDetails.get("band")) ? null : deviceDetails.get("band").toString());
//            deviceDetailData.setRssi(Objects.isNull(deviceDetails.get("rssi")) ? 0 : Double.valueOf(deviceDetails.get("rssi").toString()));
//            deviceDetailData.setDownlinkPhyRate(Objects.isNull(deviceDetails.get("downlinkPhyRate")) ? 0 : Double.valueOf(deviceDetails.get("downlinkPhyRate").toString()));
//            deviceDetailData.setUplinkPhyRate(Objects.isNull(deviceDetails.get("uplinkPhyRate")) ? 0 : Double.valueOf(deviceDetails.get("uplinkPhyRate").toString()));
//            deviceDetailData.setDownlinkErrors(Objects.isNull(deviceDetails.get("downlinkErrors")) ? 0 : Double.valueOf(deviceDetails.get("downlinkErrors").toString()));
//            deviceDetailData.setDownlinkRetransmissions(Objects.isNull(deviceDetails.get("downlinkRetransmissions")) ? 0 : Double.valueOf(String.valueOf(deviceDetails.get("downlinkRetransmissions"))));
//            if (Objects.nonNull(deviceDetails.get("lastReportAt"))) {
//                if (deviceDetails.get("lastReportAt") instanceof Date) {
//                    deviceDetailData.setLastReportAt(((Date) deviceDetails.get("lastReportAt")).getTime());
//                } else {
//                    deviceDetailData.setLastReportAt(Long.valueOf(deviceDetails.get("lastReportAt").toString()));
//                }
//            } else {
//                deviceDetailData.setLastReportAt(new Date().getTime());
//            }
//            deviceDetailData.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(deviceDetails));
//            deviceDetailData.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetailData.getMac(), userEquipment.getRgwSerial()));
//            deviceDetailData.setDownlinkBytes(downLinkBytes != 0.0 ? downLinkBytes : 0);
//            deviceDetailData.setUplinkBytes(upLinkBytes != 0.0 ? upLinkBytes : 0);
//            deviceDetailData.setCapability(Objects.nonNull(deviceCapability) ? (Objects.isNull(deviceCapability.get("capability")) ? null : deviceCapability.get("capability").toString()) : null);
//
//            deviceDetailData.setAlarms(manageDeviceService.getAlarmListForDevice(deviceDetailData));
//        }
//        deviceDetailDTO.setData(deviceDetailData);
//        return deviceDetailDTO;
//    }

//    public TopologyDTO getTopologyForSubscriber(String serialNumberOrSubscriberId) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId))
//            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
//        UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberId);
//        HashMap<String, Object> params = new HashMap<>();
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//        params.put("userId", userAP.getApId());
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//        mongoFieldOptions.put("ethPorts", 0);
//        mongoFieldOptions.put("wan", 0);
//        mongoFieldOptions.put("moca", 0);
//
//        List<BasicDBObject> equipmentList = mongoService.findList(params, ActiontecConstants.AP_DETAIL, mongoFieldOptions);
//        equipmentList = manageCommonService.filterEquipmentsAndCheckForRgwAndExt(equipmentList);
//        HashMap<String, String> aggregationParams = new HashMap<>();
//        aggregationParams.put("outputParams", "devices,count");
//        aggregationParams.put("label", "$serialNumber");
//        aggregationParams.put("operation", "$push,$sum");
//        aggregationParams.put("keyToAggregate", "$$ROOT,1");
//        aggregationParams.put("operand", "$gte");
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("devices._id", 0);
//        List<BasicDBObject> devicesList = mongoService.aggregate(params, null, ActiontecConstants.STATION_DETAIL, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_3_DAYS), aggregationParams, TIMESTAMP, mongoFieldOptions);
//
//        TopologyDTO topologyDTO = new TopologyDTO();
//        TopologyDTO.TopologyData topologyData = topologyDTO.new TopologyData();
//        EquipmentDetail equipmentDetail = new EquipmentDetail();
//        ArrayList<ExtenderDetail> extenderDetailList = new ArrayList<>();
//
//        HashSet<String> connectivityStatus = new HashSet<>();
//        HashSet<Integer> severity = new HashSet<>();
//
//        for (BasicDBObject equipment : equipmentList) {
//            String serialNumber = equipment.getString("serialNumber");
//            List<BasicDBObject> filteredDeviceList = devicesList.parallelStream().filter(d -> d.getString("_id").equals(serialNumber)).collect(Collectors.toList());
//            ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList = new ArrayList<>();
//            if (!filteredDeviceList.isEmpty()) {
//                List<BasicDBObject> devices = (List<BasicDBObject>) filteredDeviceList.get(0).get("devices");
//                HashSet<String> deviceMacList = devices.stream().map((device) -> String.valueOf(device.get("macAddress"))).collect(Collectors.toCollection(HashSet::new));
//                HashMap<String, String> stationFriendlyName = manageCommonService.getFriendlyNameForStation(deviceMacList, userAP);
//                for (BasicDBObject device : devices) {
//                    if (Objects.nonNull(device) && Objects.nonNull(device.get("macAddress"))) {
//
//                        DeviceDetailDTO.DeviceDetailData deviceDetail = fetchDeviceDetails(userAP, device.get("macAddress").toString(), device).getData();
//
//                        HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
//                        int rssiRange = LOWER_LIMIT;
//                        try {
//                            rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
//                        } catch (Exception e) {
//
//                        }
//                        deviceDetail.setRssiRange(rssiRange);
//                        deviceDetailList.add(deviceDetail);
//                    }
//                }
//            }
//
//            if (equipment.getString("type").equals(GATEWAY)) {
//                equipmentDetail.setDevices(deviceDetailList);
//                equipmentDetail.setMacAddress(String.valueOf(equipment.get("macAddress")));
//                equipmentDetail.setName((Objects.isNull(equipment.get("friendlyName")) || equipment.get("friendlyName").toString().trim().isEmpty()) ? (Objects.isNull(equipment.get("modelName")) || equipment.get("modelName").toString().trim().isEmpty()) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
//                equipmentDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
//                equipmentDetail.setType(equipment.getString("type"));
//                equipmentDetail.setSerialNumber(equipment.getString("serialNumber"));
//                //// ALARMS FOR EQUIPMENT START
//                List<HashMap<String, Object>> alarmListForEquipment = manageEquipmentService.getEquipmentAlarmList(equipment, userAP);
//                equipmentDetail.setAlarms(alarmListForEquipment);
//                equipmentDetail.setSeverity(calculateSeverityForEquipment(/*deviceDetailList,*/ alarmListForEquipment, equipmentDetail.getConnectivityStatus()));
//                severity.add(equipmentDetail.getSeverity());
////                severity.add(calculateSeverityForDevice(deviceDetailList));
//                connectivityStatus.add(equipmentDetail.getConnectivityStatus());
//                //// ALARMS FOR EQUIPMENT END
//                for (BasicDBObject wifiRadio : (List<BasicDBObject>) equipment.get("wifiRadios")) {
//                    if (wifiRadio.getString("band") != null) {
//                        if ("5G".equals(wifiRadio.getString("band"))) {
//                            equipmentDetail.set_5GChannelNumber(Integer.valueOf(String.valueOf(wifiRadio.get("channel"))));
//                        } else {
//                            equipmentDetail.set_24GChannelNumber(Integer.valueOf(String.valueOf(wifiRadio.get("channel"))));
//                        }
//                    }
//                }
//            } else if ((equipment.getString("type").equals(EXTENDER))) {
//                ExtenderDetail extenderDetail = new ExtenderDetail();
//                extenderDetail.setDevices(deviceDetailList);
//                extenderDetail.setMacAddress(String.valueOf(equipment.get("macAddress")));
//                extenderDetail.setName((Objects.isNull(equipment.get("friendlyName")) || equipment.get("friendlyName").toString().trim().isEmpty()) ? (Objects.isNull(equipment.get("modelName")) || equipment.get("modelName").toString().trim().isEmpty()) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
//                extenderDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
//                extenderDetail.setType(equipment.getString("type"));
//                extenderDetail.setSerialNumber(equipment.getString("serialNumber"));
//
//                //// ALARMS FOR EQUIPMENT START
//                List<HashMap<String, Object>> alarmListForExtender = manageEquipmentService.getEquipmentAlarmList(equipment, userAP);
//
//                extenderDetail.setAlarms(alarmListForExtender);
//                extenderDetail.setSeverity(calculateSeverityForEquipment(/*deviceDetailList,*/ alarmListForExtender, extenderDetail.getConnectivityStatus()));
//                severity.add(extenderDetail.getSeverity());
////                severity.add(calculateSeverityForDevice(deviceDetailList));
//                connectivityStatus.add(extenderDetail.getConnectivityStatus());
//                //// ALARMS FOR EQUIPMENT END
//                for (BasicDBObject wifiRadio : (List<BasicDBObject>) equipment.get("wifiRadios")) {
//                    if (Objects.nonNull(wifiRadio.getString("band"))) {
//                        if ("5G".equals(wifiRadio.getString("band"))) {
//                            extenderDetail.set_5GChannelNumber(Integer.valueOf(wifiRadio.getString("channel")));
//                        } else {
//                            extenderDetail.set_24GChannelNumber(Integer.valueOf(wifiRadio.getString("channel")));
//                        }
//                    }
//                }
//                extenderDetailList.add(extenderDetail);
//            }
//        }
//        if (Objects.isNull(equipmentDetail.getDevices())) {
//            equipmentDetail.setDevices(new ArrayList<>());
//        }
//        if (Objects.isNull(equipmentDetail.getAlarms()))
//            equipmentDetail.setAlarms(new ArrayList<>());
//        equipmentDetail.setExtenders(extenderDetailList);
//        topologyData.setSeverity(-1);
//        if (!connectivityStatus.isEmpty() && connectivityStatus.size() == 1 && connectivityStatus.iterator().next().equals("RED")) {
//            topologyData.setSeverity(2);
//        } else if (!severity.isEmpty() && severity.size() == 1) {
//            topologyData.setSeverity(severity.iterator().next());
//        } else if (severity.size() > 1) {
//            topologyData.setSeverity(Collections.max(severity));
//        }
//        topologyData.setEquipment(equipmentDetail);
//        topologyDTO.setData(topologyData);
//        return topologyDTO;
//    }

    public TopologyDTO getTopology(String equipmentIdOrSerialOrSTN) throws Exception {
        TopologyDTO topologyDTO = new TopologyDTO();
        TopologyDTO.TopologyData topologyData = topologyDTO.new TopologyData();
        EquipmentDetail equipmentDetail = new EquipmentDetail();

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN))
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);


        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        params.put("userId", userEquipment.getRgwSerial());
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("moca", 0);
        List<BasicDBObject> equipmentList = mongoService.findList(params, ActiontecConstants.AP_DETAIL, mongoFieldOptions);
        equipmentList = manageCommonService.filterEquipmentsAndCheckForRgwAndExt(equipmentList);

        HashMap<String, String> aggregationParams = new HashMap<>();
        aggregationParams.put("outputParams", "devices,count");
        aggregationParams.put("label", "$serialNumber");
        aggregationParams.put("operation", "$push,$sum");
        aggregationParams.put("keyToAggregate", "$$ROOT,1");
        aggregationParams.put("operand", "$gte");
        mongoFieldOptions.clear();
        mongoFieldOptions.put("devices._id", 0);
        List<BasicDBObject> devicesList = new ArrayList();
        List<BasicDBObject> StationDevices = mongoService.aggregate(params, null, ActiontecConstants.STATION_DETAIL, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_3_DAYS), aggregationParams, TIMESTAMP, mongoFieldOptions);
        if (Objects.nonNull(StationDevices) && !StationDevices.isEmpty()) {
            for (int i = 0; i < StationDevices.size(); i++) {
                devicesList.addAll((List<BasicDBObject>) StationDevices.get(i).get("devices"));
            }
        }
        String isp = equipmentList.get(ZERO).getString("isp");
        String[] actualIsp = isp.split(HYPHEN_STRING);
        if (!VERIZON_ISP.equals(actualIsp[0]))
            devicesList.addAll(manageCommonService.getHostnameDetail(userEquipment.getRgwSerial()));
        HashMap<String, List<BasicDBObject>> allDevices = new HashMap();
        for (BasicDBObject device : devicesList) {
            if (!allDevices.containsKey(device.getString("macAddress"))) {
                allDevices.put(device.getString("macAddress"), new ArrayList());
            }
            allDevices.get(device.getString("macAddress")).add(device);
        }
        ArrayList<BasicDBObject> filteredDeviceList = new ArrayList<>();
        allDevices.values().forEach(deviceList -> {
            if (deviceList.size() == ONE) {
                filteredDeviceList.addAll(deviceList);
            } else {
                for (BasicDBObject d : deviceList) {
                    if (Objects.isNull(d.getString("phyType"))) {
                        filteredDeviceList.add(d);
                        break;
                    }
                }
            }
        });
        ArrayList<ExtenderDetail> extenderDetailList = new ArrayList<>();
        HashSet<String> connectivityStatus = new HashSet<>();
        HashSet<Integer> severity = new HashSet<>();
        for (BasicDBObject equipment : equipmentList) {
            addDevicesToEquipment(userEquipment, filteredDeviceList, equipmentDetail, extenderDetailList, connectivityStatus, severity, equipment);
        }

        if (Objects.isNull(equipmentDetail.getDevices())) {
            equipmentDetail.setDevices(new ArrayList<>());
        }
        if (Objects.isNull(equipmentDetail.getAlarms()))
            equipmentDetail.setAlarms(new ArrayList<>());
        equipmentDetail.setExtenders(extenderDetailList);
        topologyData.setSeverity(-1);
        if (!connectivityStatus.isEmpty() && connectivityStatus.size() == 1 && connectivityStatus.iterator().next().equals("RED")) {
            topologyData.setSeverity(2);
        } else if (!severity.isEmpty() && severity.size() == 1) {
            topologyData.setSeverity(severity.iterator().next());
        } else if (severity.size() > 1) {
            topologyData.setSeverity(Collections.max(severity));
        }
        topologyData.setEquipment(equipmentDetail);
        topologyDTO.setData(topologyData);
        return topologyDTO;
    }

    private void addDevicesToEquipment(Equipment userEquipment, List<BasicDBObject> devicesList, EquipmentDetail equipmentDetail, ArrayList<ExtenderDetail> extenderDetailList, HashSet<String> connectivityStatus, HashSet<Integer> severity, BasicDBObject equipment) throws Exception {
        String serialNumber = equipment.getString("serialNumber");
        List<BasicDBObject> filteredDeviceList = devicesList.stream().filter(d -> d.getString("serialNumber").equals(serialNumber)).collect(Collectors.toList());
        //ArrayList<DeviceDetail> deviceDetailList = new ArrayList<>();
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList = new ArrayList<>();
        if (!filteredDeviceList.isEmpty()) {
            List<BasicDBObject> devices = filteredDeviceList;
            HashSet<String> deviceMacList = devices.stream().filter((device) -> Objects.isNull(device.getString("phyType"))).map((device) -> device.getString("macAddress")).collect(Collectors.toCollection(HashSet::new));
            HashMap<String, String> stationFriendlyName = manageCommonService.getFriendlyNameForStation(deviceMacList, userEquipment);
            for (BasicDBObject device : devices) {
                if (Objects.nonNull(device)) {
                    DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
                    DeviceDetailDTO.DeviceDetailData deviceDetail = deviceDetailDTO.new DeviceDetailData();
                    if (Objects.isNull(device.getString("phyType"))) {
                        addStationDevices(userEquipment, equipment, serialNumber, deviceDetailList, stationFriendlyName, device, deviceDetail);
                    } else {
                        addMiscDevices(device, deviceDetailList, userEquipment);
                    }
                }

            }
        }

        if (equipment.getString("type").equals(GATEWAY)) {
            equipmentDetail.setDevices(deviceDetailList);
            equipmentDetail.setMacAddress(String.valueOf(equipment.get("macAddress")));
            equipmentDetail.setName((Objects.isNull(equipment.get("friendlyName")) || equipment.get("friendlyName").toString().trim().isEmpty()) ? (Objects.isNull(equipment.get("modelName")) || equipment.get("modelName").toString().trim().isEmpty()) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            equipmentDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
            equipmentDetail.setType(equipment.getString("type"));
            equipmentDetail.setSerialNumber(equipment.getString("serialNumber"));
            //// ALARMS FOR EQUIPMENT START
            List<HashMap<String, Object>> alarmListForEquipment = manageEquipmentService.getEquipmentAlarmList(equipment, userEquipment);
            equipmentDetail.setAlarms(alarmListForEquipment);
            equipmentDetail.setSeverity(calculateSeverityForEquipment(/*deviceDetailList,*/ alarmListForEquipment, equipmentDetail.getConnectivityStatus()));
            severity.add(equipmentDetail.getSeverity());
//                severity.add(calculateSeverityForDevice(deviceDetailList));
            connectivityStatus.add(equipmentDetail.getConnectivityStatus());
            //// ALARMS FOR EQUIPMENT END
            for (BasicDBObject wifiRadio : (List<BasicDBObject>) equipment.get("wifiRadios")) {
                if (wifiRadio.getString("band") != null) {
                    if ("5G".equals(wifiRadio.getString("band"))) {
                        equipmentDetail.set_5GChannelNumber(Integer.valueOf(String.valueOf(wifiRadio.get("channel"))));
                    } else {
                        equipmentDetail.set_24GChannelNumber(Integer.valueOf(String.valueOf(wifiRadio.get("channel"))));
                    }
                }
            }
        } else if ((equipment.getString("type").equals("EXTENDER"))) {
            ExtenderDetail extenderDetail = new ExtenderDetail();
            extenderDetail.setDevices(deviceDetailList);
            extenderDetail.setMacAddress(String.valueOf(equipment.get("macAddress")));
            extenderDetail.setName((Objects.isNull(equipment.get("friendlyName")) || equipment.get("friendlyName").toString().trim().isEmpty()) ? (Objects.isNull(equipment.get("modelName")) || equipment.get("modelName").toString().trim().isEmpty()) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            extenderDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
            extenderDetail.setType(equipment.getString("type"));
            extenderDetail.setSerialNumber(equipment.getString("serialNumber"));

            //// ALARMS FOR EQUIPMENT START
            List<HashMap<String, Object>> alarmListForExtender = manageEquipmentService.getEquipmentAlarmList(equipment, userEquipment);

            extenderDetail.setAlarms(alarmListForExtender);
            extenderDetail.setSeverity(calculateSeverityForEquipment(/*deviceDetailList,*/ alarmListForExtender, extenderDetail.getConnectivityStatus()));
            severity.add(extenderDetail.getSeverity());
//                severity.add(calculateSeverityForDevice(deviceDetailList));
            connectivityStatus.add(extenderDetail.getConnectivityStatus());
            //// ALARMS FOR EQUIPMENT END
            for (BasicDBObject wifiRadio : (List<BasicDBObject>) equipment.get("wifiRadios")) {
                if (Objects.nonNull(wifiRadio.getString("band"))) {
                    if ("5G".equals(wifiRadio.getString("band"))) {
                        extenderDetail.set_5GChannelNumber(Integer.valueOf(wifiRadio.getString("channel")));
                    } else {
                        extenderDetail.set_24GChannelNumber(Integer.valueOf(wifiRadio.getString("channel")));
                    }
                }
            }
            extenderDetailList.add(extenderDetail);
        }
    }

    private void addStationDevices(Equipment userEquipment, BasicDBObject equipment, String serialNumber, ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList, HashMap<String, String> stationFriendlyName, BasicDBObject device, DeviceDetailDTO.DeviceDetailData deviceDetail) throws Exception {
        String band = Objects.nonNull(device.get("band")) ? device.get("band").toString() : null;
        String userId = Objects.nonNull(device.get("userId")) ? device.get("userId").toString() : null;
        String serialNo = Objects.isNull(device.get("serialNumber")) ? null : device.get("serialNumber").toString();
        String bssid = Objects.isNull(device.get("bssid")) ? null : device.get("bssid").toString();
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        if (band != null || userId != null || serialNo != null || bssid != null) {
            deviceDetail.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : manageCommonService.getSsidForDeviceDetail(userId, serialNo, bssid));
        } else {
            deviceDetail.setSsid("N/A");
        }
        deviceDetail.setFromDSHost(false);
        deviceDetail.setMac(String.valueOf(device.get("macAddress")));
        deviceDetail.setDeviceType(Objects.isNull(device.get("deviceType")) ? "Other" : device.get("deviceType").toString());
        deviceDetail.setConnectedTo(serialNumber);
        HashSet<String> serialNumbers = new HashSet<>();
        serialNumbers.add(deviceDetail.getConnectedTo());
        HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
        deviceDetail.setConnectedToName(equipmentFriendlyName.get(deviceDetail.getConnectedTo()));
        deviceDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(device)); //Changed from status to ConnectivityStatus to make sync with device detail api
        deviceDetail.setName(stationFriendlyName.get(device.get("macAddress").toString()));
        deviceDetail.setBand(Objects.isNull(device.get("band")) ? "N/A" : device.get("band").toString());
        deviceDetail.setRssi(Objects.isNull(device.get("rssi")) ? 0.0 : Double.valueOf(device.get("rssi").toString()));
        deviceDetail.setIp(String.valueOf(device.get("ip")));
        deviceDetail.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetail.getMac(), userEquipment.getRgwSerial()));
        deviceDetail.setWifiMode(Objects.isNull(device.get("wifiMode")) ? "N/A" : device.get("wifiMode").toString());
        // ALARMS for DEVICE START
        deviceDetail.setAlarms(manageDeviceService.getAlarmListForDevice(deviceDetail));
        deviceDetail.setLastReportAt(Objects.nonNull(device.get("lastReportAt")) ? Long.valueOf(device.get("lastReportAt").toString()) : 0);
        HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
        int rssiRange = LOWER_LIMIT;
        try {
            rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
        } catch (Exception e) {

        }


        deviceDetail.setRssiRange(rssiRange);
        // ALARMS for DEVICE END
        deviceDetailList.add(deviceDetail);
    }

    public void addMiscDevices(BasicDBObject q, ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList, Equipment userEquipment) throws Exception {
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
        DeviceDetailDTO.DeviceDetailData deviceDetail = deviceDetailDTO.new DeviceDetailData();
        deviceDetail.setSsid("N/A");
        deviceDetail.setMac(String.valueOf(q.get("macAddress")));
        deviceDetail.setDeviceType(q.getBoolean("isExtender") ? "EXT" : "Other");
        deviceDetail.setConnectedTo(q.getString("serialNumber"));
        HashSet<String> serialNumbers = new HashSet<>();
        serialNumbers.add(deviceDetail.getConnectedTo());
        HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
        deviceDetail.setConnectedToName(equipmentFriendlyName.get(deviceDetail.getConnectedTo()));
        deviceDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(q)); //Changed from status to ConnectivityStatus to make sync with device detail api
        deviceDetail.setName((Objects.nonNull(q.get("hostname")) && !q.getString("hostname").isEmpty()) ? q.getString("hostname") : manageCommonService.getDisplayNameByPriorityForDevice(q));
        deviceDetail.setBand(q.getString("phyType"));
        deviceDetail.setRssi(0.0);
        deviceDetail.setIp(String.valueOf(q.get("ip")));
        deviceDetail.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetail.getMac(), q.getString("userId")));
        deviceDetail.setWifiMode("N/A");
        // ALARMS for DEVICE START
        deviceDetail.setAlarms(new ArrayList<>());
        deviceDetail.setLastReportAt(q.getLong(TIMESTAMP));
        deviceDetail.setRssiRange(0);
        deviceDetail.setFromDSHost(true);
        // ALARMS for DEVICE END
        deviceDetailList.add(deviceDetail);


    }

    private int calculateSeverityForEquipment(/*ArrayList<DeviceDetail> deviceDetailList,*/ List<HashMap<String, Object>> alarmListForEquipment, String connectivityStatus) {
        int alarmLevelSeverity = -1;
        /*for (DeviceDetail deviceDetail : deviceDetailList) {
            for (HashMap<String, Object> alarm : deviceDetail.getAlarms()) {
                if (Integer.parseInt(alarm.get("severity").toString()) > alarmLevelSeverity) {
                    alarmLevelSeverity = Integer.parseInt(alarm.get("severity").toString());
                }
            }
        }*/
        for (HashMap<String, Object> i : alarmListForEquipment) {
            if (Integer.parseInt(i.get("severity").toString()) > alarmLevelSeverity) {
                alarmLevelSeverity = Integer.parseInt(i.get("severity").toString());
            }
        }

        return (alarmLevelSeverity == 1 && "RED".equals(connectivityStatus)) ? 1 : alarmLevelSeverity;
    }

}
