package com.incs83.app.business.v4;

import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.StationSpeedTest;
import com.incs83.app.entities.cassandra.DeviceCapability;
import com.incs83.app.enums.APDetailLookType;
import com.incs83.app.responsedto.v2.Device.*;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.service.repository.CassandraRepository;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;
import static com.incs83.constants.ApplicationCommonConstants.*;

/**
 * Created by Jayant on 29/1/18.
 */
@Service("v4.ManageDeviceService")
public class ManageDeviceService {
    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    CassandraRepository cassandraRepository;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private CommonService commonService;

    public DeviceDetailDTO fetchDeviceDetailsByMacAddr(String equipmentIdOrSerialOrSTN, String macAddr) throws Exception {
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkMacAddressOfStation(macAddr, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddr, userEquipment);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
        boolean flag = (Objects.nonNull(commonProps.get(ABS_DATABASE)) && commonProps.get(ABS_DATABASE).equalsIgnoreCase("MONGO")) ? true : false;

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);

        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        queryParams.clear();

        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);

        List<String> projection = new ArrayList<>();

        List<DeviceCapability> deviceCapability;
        if (flag) {
            List<DBObject> deviceCapabilityMongo = mongoService.findList(DEVICE_CAPABILITY, queryParams, TIMESTAMP, DESC);
            deviceCapability = manageCommonService.convertToDeviceCapability(deviceCapabilityMongo);
        } else {
            deviceCapability = (List<DeviceCapability>) cassandraRepository.read(DeviceCapability.class, queryParams, null, null, projection, 0);
        }
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        int minutes = 60;

        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
        aggregationWhereClause.put("dateHour", dateCriteria);

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(aggregationWhereClause, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);

        double downLinkBytes = 0.0;
        double upLinkBytes = 0.0;
        if (!deviceDownLinkUpLinkBytes.isEmpty()) {
            List<DBObject> actualDevMap = new ArrayList<>();
            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    List<DBObject> devMap = (List<DBObject>) ((DBObject) dbObject.get("devMap")).get(macAddr);
                    if (Objects.nonNull(devMap)) {
                        actualDevMap.addAll(devMap);
                    }
                }

                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<DBObject> latestDevMap = (List<DBObject>) userSlidingData.get("devMap").get(macAddr);
                        if (Objects.nonNull(latestDevMap)) {
                            actualDevMap.addAll(latestDevMap);
                        }
                    }
                }
            }
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            actualDevMap = actualDevMap.stream().filter(element -> ((Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp && (Objects.nonNull(element.get("rx")) && Objects.nonNull(element.get("tx"))))).collect(Collectors.toList());
            if (!actualDevMap.isEmpty()) {
                downLinkBytes = downLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("rx")) ? Double.valueOf(element.get("rx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
                upLinkBytes = upLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("tx")) ? Double.valueOf(element.get("tx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
            }
        }

        HashMap<String, Object> query = new HashMap<>();
        query.put("macAddr", macAddr);

        List<DBObject> stationSpeedTestList = mongoService.findList(STATION_SPEED_TEST_INFO, query, DATE, DESC);

        StationSpeedTest stationSpeedTest = null;
        if (Objects.nonNull(stationSpeedTestList) && !stationSpeedTestList.isEmpty()) {
            stationSpeedTest = new StationSpeedTest();
            stationSpeedTest.setDate(((Date) stationSpeedTestList.get(0).get("date")));
            stationSpeedTest.setDataRate(Objects.isNull(stationSpeedTestList.get(0).get("dataRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("dataRate"))));
            stationSpeedTest.setRxRate(Objects.isNull(stationSpeedTestList.get(0).get("rxRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("rxRate"))));
            stationSpeedTest.setTxRate(Objects.isNull(stationSpeedTestList.get(0).get("txRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("txRate"))));
            stationSpeedTest.setBand(Objects.isNull(stationSpeedTestList.get(0).get("band")) ? "N/A" : stationSpeedTestList.get(0).get("band").toString());
            stationSpeedTest.setIpAddr(Objects.isNull(stationSpeedTestList.get(0).get("ipAddr")) ? "N/A" : stationSpeedTestList.get(0).get("ipAddr").toString());
            stationSpeedTest.setMacAddr(Objects.isNull(stationSpeedTestList.get(0).get("macAddr")) ? "N/A" : stationSpeedTestList.get(0).get("macAddr").toString());
            stationSpeedTest.setUserId(Objects.isNull(stationSpeedTestList.get(0).get("userId")) ? "N/A" : stationSpeedTestList.get(0).get("userId").toString());
        }

        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
        DeviceDetailDTO.DeviceDetailData deviceDetailData = deviceDetailDTO.new DeviceDetailData();
        DeviceDetailDTO.DeviceDetailData.DeviceSpeedTest deviceSpeedTest = deviceDetailData.new DeviceSpeedTest();

        if (Objects.nonNull(stationSpeedTest)) {
            deviceSpeedTest.setDataRate(stationSpeedTest.getDataRate());
            deviceSpeedTest.setDate(Objects.isNull(stationSpeedTest.getDate()) ? 0 : stationSpeedTest.getDate());
        } else {
            deviceSpeedTest.setDataRate(0f);
            deviceSpeedTest.setDate(0l);
        }
        deviceDetailData.setSpeedTestStats(deviceSpeedTest);

        if (Objects.nonNull(deviceDetails)) {
            String band = Objects.nonNull(deviceDetails.get("band")) ? deviceDetails.get("band").toString() : null;
            String userId = Objects.nonNull(deviceDetails.get("userId")) ? deviceDetails.get("userId").toString() : null;
            String serialNumber = Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString();
            String bssid = Objects.isNull(deviceDetails.get("bssid")) ? null : deviceDetails.get("bssid").toString();

            if (band != null || userId != null || serialNumber != null || bssid != null) {
                deviceDetailData.setSsid(manageCommonService.getSsidForDeviceDetail(userId, serialNumber, bssid));
            } else {
                deviceDetailData.setSsid(null);
            }

            deviceDetailData.setConnectedTo(Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString());
            if (Objects.nonNull(deviceDetailData.getConnectedTo())) {
                HashSet<String> serialNumbers = new HashSet<>();
                serialNumbers.add(deviceDetailData.getConnectedTo());
                HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
                deviceDetailData.setConnectedToName(equipmentFriendlyName.get(deviceDetailData.getConnectedTo()));
            }
            deviceDetailData.setName(manageCommonService.getDisplayNameForStation(String.valueOf(deviceDetails.get("macAddress")), userEquipment));
            deviceDetailData.setDeviceType(Objects.isNull(deviceDetails.get("deviceType")) ? "Other" : deviceDetails.get("deviceType").toString());
            Set<String> macAddressSet = new HashSet<>();
            macAddressSet.add(String.valueOf(deviceDetails.get("macAddress")));
            deviceDetailData.setHostname(manageCommonService.getHostNameForStation(macAddressSet, userEquipment).get(String.valueOf(deviceDetails.get("macAddress"))));
            deviceDetailData.setIp(Objects.isNull(deviceDetails.get("ip")) ? null : deviceDetails.get("ip").toString());
            deviceDetailData.setMac(Objects.isNull(deviceDetails.get("macAddress")) ? null : deviceDetails.get("macAddress").toString());
            deviceDetailData.setVendor(manageCommonService.getVendorName(String.valueOf(deviceDetails.get("macAddress"))));
            deviceDetailData.setWifiMode(Objects.isNull(deviceDetails.get("wifiMode")) ? null : deviceDetails.get("wifiMode").toString());
            deviceDetailData.setBand(Objects.isNull(deviceDetails.get("band")) ? null : deviceDetails.get("band").toString());
            deviceDetailData.setRssi(Objects.isNull(deviceDetails.get("rssi")) ? 0 : Double.valueOf(deviceDetails.get("rssi").toString()));
            deviceDetailData.setDownlinkPhyRate(Objects.isNull(deviceDetails.get("uplinkPhyRate")) ? 0 : Double.valueOf(deviceDetails.get("uplinkPhyRate").toString()));
            deviceDetailData.setUplinkPhyRate(Objects.isNull(deviceDetails.get("downlinkPhyRate")) ? 0 : Double.valueOf(deviceDetails.get("downlinkPhyRate").toString()));
            deviceDetailData.setDownlinkErrors(Objects.isNull(deviceDetails.get("downlinkErrors")) ? 0 : Double.valueOf(deviceDetails.get("downlinkErrors").toString()));
            deviceDetailData.setDownlinkRetransmissions(Objects.isNull(deviceDetails.get("downlinkRetransmissions")) ? 0 : Double.valueOf(String.valueOf(deviceDetails.get("downlinkRetransmissions"))));
            if (Objects.nonNull(deviceDetails.get("lastReportAt"))) {
                if (deviceDetails.get("lastReportAt") instanceof Date) {
                    deviceDetailData.setLastReportAt(((Date) deviceDetails.get("lastReportAt")).getTime());
                } else {
                    deviceDetailData.setLastReportAt(Long.valueOf(deviceDetails.get("lastReportAt").toString()));
                }
            } else {
                deviceDetailData.setLastReportAt(new Date().getTime());
            }
            deviceDetailData.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(deviceDetails));
            deviceDetailData.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetailData.getMac(), userEquipment.getRgwSerial()));
            deviceDetailData.setDownlinkBytes(downLinkBytes != 0.0 ? downLinkBytes : 0);
            deviceDetailData.setUplinkBytes(upLinkBytes != 0.0 ? upLinkBytes : 0);
            deviceDetailData.setCapability(Objects.nonNull(deviceCapability) && !deviceCapability.isEmpty() ? (Objects.isNull(deviceCapability.get(0).getCapability()) ? null : deviceCapability.get(0).getCapability()) : null);
            deviceDetailData.setIsp(Objects.isNull(deviceDetails.get("isp")) ? "N/A" : String.valueOf(deviceDetails.get("isp")));
            HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
            int rssiRange = LOWER_LIMIT;
            try {
                rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
            } catch (Exception e) {

            }
            deviceDetailData.setRssiRange(rssiRange);

            deviceDetailData.setAlarms(getAlarmListForDevice(deviceDetailData));
        }
        deviceDetailDTO.setData(deviceDetailData);
        return deviceDetailDTO;
    }


    public List<HashMap<String, Object>> getAlarmListForDevice(DeviceDetailDTO.DeviceDetailData deviceDetailData) throws Exception {
        HashMap<String, Object> rssiAlarm = new HashMap<>();
        HashMap<String, Object> wifiModeAlarm = new HashMap<>();
        List<HashMap<String, Object>> alarmListForDevice = new ArrayList<>();

        HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
        Double rssiRange = -75.0;
        try {
            rssiRange = Double.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
        } catch (Exception e) {

        }

        if (deviceDetailData.getRssi() < rssiRange && !deviceDetailData.getConnectivityStatus().equals("GREY")) {
            rssiAlarm.put("alarmType", "deviceAlarm");
            rssiAlarm.put("mac", deviceDetailData.getMac());
            rssiAlarm.put("serial", null);
            rssiAlarm.put("equipmentType", null);
            rssiAlarm.put("severity", 0);
            rssiAlarm.put("name", deviceDetailData.getName());
            rssiAlarm.put("desc", "WiFi signal is low(RSSI-: " + deviceDetailData.getRssi() + " dBm). Please move the device closer to the WiFi Router, Gateway or Extender, or consider adding a WiFi Network Extender to improve the WiFi coverage.");

            alarmListForDevice.add(rssiAlarm);
        }
        String wifiMode = deviceDetailData.getWifiMode();
        if (("802.11 b").equals(wifiMode) || ("802.11 bg").equals(wifiMode) || ("802.11 a").equals(wifiMode) || ("802.11 g").equals(wifiMode)) {
            wifiModeAlarm.put("alarmType", "deviceAlarm");
            wifiModeAlarm.put("mac", deviceDetailData.getMac());
            wifiModeAlarm.put("serial", null);
            wifiModeAlarm.put("equipmentType", null);
            wifiModeAlarm.put("severity", 0);
            wifiModeAlarm.put("name", deviceDetailData.getName());
            wifiModeAlarm.put("desc", "is not using the latest WiFi standard, and may be slowing down your WiFi network.  Disconnect or upgrade this device to help improve your WiFi network performance.");

            alarmListForDevice.add(wifiModeAlarm);
        }

        return alarmListForDevice;
    }

    public List<Map<String, Object>> getDataFromUserStationSlidingData(Equipment userEquipment, String macAddress, Long durationFrom, Long durationTo) throws Exception {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.setTimeInMillis(durationFrom);
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        long dateHourFrom = TimeUnit.MILLISECONDS.toSeconds(now.getTimeInMillis());

        now.setTimeInMillis(durationTo);
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        long dateHourTo = TimeUnit.MILLISECONDS.toSeconds(now.getTimeInMillis());



        /*long dateHourFrom = (durationFrom / 1000) - ((((durationFrom / 1000) / 60) % 60) * 60);
        long dateHourTo = (durationTo / 1000) - ((((durationTo / 1000) / 60) % 60) * 60);*/

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHourFrom);
        dateCriteria.put("$lte", dateHourTo);

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("dateHour", dateCriteria);

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
//        mongoFieldOptions.put("devMap." + macAddress, 1);

        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(query, USER_STATION_SLIDING_DATA, mongoFieldOptions);

        List<Map<String, Object>> devMapData;
        List<Map<String, Object>> actualDevMapData = new ArrayList<>();
        List<Map<String, Object>> combineData = new ArrayList<>();
        if (!deviceDownLinkUpLinkBytes.isEmpty()) {
            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    devMapData = (List<Map<String, Object>>) ((DBObject) dbObject.get("devMap")).get(macAddress);
                    if (Objects.nonNull(devMapData) && !devMapData.isEmpty()) {
                        combineData.addAll(devMapData);
                    }
                }

                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<Map<String, Object>> latestDevMap = (List<Map<String, Object>>) userSlidingData.get("devMap").get(macAddress);
                        if (Objects.nonNull(latestDevMap) && !latestDevMap.isEmpty()) {
                            combineData.addAll(latestDevMap);
                        }
                    }
                }

            }
            devMapData = combineData.parallelStream().filter(element -> Objects.nonNull(element.get("timestamp")) && (Long.valueOf(element.get("timestamp").toString()) >= durationFrom && Long.valueOf(element.get("timestamp").toString()) <= durationTo)).collect(Collectors.toList());
            if (Objects.nonNull(devMapData) && !devMapData.isEmpty())
                actualDevMapData.addAll(devMapData);
        }

        return actualDevMapData;
    }

    public HashMap<String, Object> getDeviceSteeringEventsByMACAddress(String equipmentIdOrSerialOrSTN, String macAddress, Long durationFrom, Long durationTo, String attr) throws Exception {
        if ((Objects.isNull(durationFrom) || durationFrom <= 0) || (Objects.isNull(durationTo) || durationTo <= 0))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value for duration");

        if (durationFrom > durationTo)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid value, durationFrom cannot be greater than durationTo");
//
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddress, userEquipment);
        manageCommonService.checkDeviceMACBelongsToUser(macAddress, userEquipment);

        long currentTimestamp = Calendar.getInstance().getTimeInMillis();

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("_id", 0);

        BasicDBObject timeCriteria = new BasicDBObject();
        timeCriteria.put("$gte", durationFrom);
        timeCriteria.put("$lte", durationTo);


        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("macAddress", macAddress);
        query.put("timestamp", timeCriteria);

        HashMap<String, Object> response = new HashMap<>();

        ArrayList<String> attributes = new ArrayList<>(Arrays.asList(attr.trim().split(COMMA)));

        List<Map<String, Object>> actualDevMapData = new ArrayList<>();
        if (attributes.contains(ATTR_DEVICE_WIFI_RSSI) || attributes.contains(ATTR_DEVICE_WIFI_THROUGHPUT) || attributes.contains(ATTR_DEVICE_WIFI_WIFI_PHY)) {
            actualDevMapData = getDataFromUserStationSlidingData(userEquipment, macAddress, durationFrom, durationTo);
        }

        final List<Map<String, Object>> userStationData = actualDevMapData.isEmpty() ? new ArrayList<>() : actualDevMapData;

        attributes.forEach(type -> {
            HashSet<String> bssidSets;
            HashMap<String, Object> bssidDetails;
            switch (type.trim()) {
                case ATTR_DEVICE_ASSOC_EVENT:
                    List<DBObject> associationEvents = mongoService.findList(query, DEVICE_ASSOCIATION, mongoFieldOptions);
                    bssidSets = (HashSet<String>) associationEvents.stream().map(event -> String.valueOf(event.get("bssid"))).collect(Collectors.toSet());
                    bssidDetails = getBssidDetails(bssidSets, userEquipment);

                    DeviceAssociationDTO deviceAssociationDTO = new DeviceAssociationDTO();
                    HashSet<DeviceAssociationDTO.DeviceAssociationData> deviceAssociationDataList = new HashSet<>();
                    associationEvents.forEach(associationEvent -> {
                        HashMap<String, String> detailsMap = (HashMap<String, String>) bssidDetails.get(String.valueOf(associationEvent.get("bssid")));
                        if (Objects.nonNull(detailsMap) && Objects.nonNull(associationEvent.get("timestamp")) && Long.valueOf(associationEvent.get("timestamp").toString()) <= currentTimestamp) {
                            DeviceAssociationDTO.DeviceAssociationData deviceAssociationData = deviceAssociationDTO.new DeviceAssociationData();
                            deviceAssociationData.setAssociatedTo("Associated to " + detailsMap.get("friendlyName") + " / " + detailsMap.get("ssid") + " / " + detailsMap.get("band"));
                            deviceAssociationData.setMacAddress(Objects.isNull(associationEvent.get("macAddress")) ? null : associationEvent.get("macAddress").toString());
                            deviceAssociationData.setVendor(manageCommonService.getVendorName(String.valueOf(associationEvent.get("macAddress"))));
                            deviceAssociationData.setTimestamp(Long.valueOf(associationEvent.get("timestamp").toString()));
                            deviceAssociationDataList.add(deviceAssociationData);
                        }
                    });
                    response.put(type, deviceAssociationDataList);
                    break;

                case ATTR_DEVICE_DIS_ASSOC_EVENT:
                    List<DBObject> disAssociationEvents = mongoService.findList(query, DEVICE_DISASSOCIATION, mongoFieldOptions);
                    bssidSets = (HashSet<String>) disAssociationEvents.stream().map(event -> String.valueOf(event.get("bssid"))).collect(Collectors.toSet());
                    bssidDetails = getBssidDetails(bssidSets, userEquipment);

                    DeviceDisAssociationDTO deviceDisAssociationDTO = new DeviceDisAssociationDTO();
                    HashSet<DeviceDisAssociationDTO.DeviceDisAssociationData> deviceDisAssociationDataList = new HashSet<>();
                    disAssociationEvents.forEach(disAssociationEvent -> {
                        HashMap<String, Object> detailsMap = (HashMap<String, Object>) bssidDetails.get(String.valueOf(disAssociationEvent.get("bssid")));
                        if (Objects.nonNull(detailsMap) && Objects.nonNull(disAssociationEvent.get("timestamp")) && Long.valueOf(disAssociationEvent.get("timestamp").toString()) <= currentTimestamp) {
                            DeviceDisAssociationDTO.DeviceDisAssociationData deviceDisAssociationData = deviceDisAssociationDTO.new DeviceDisAssociationData();
                            deviceDisAssociationData.setDisassociatedFrom("Disassociated from " + detailsMap.get("friendlyName") + " / " + detailsMap.get("ssid") + " / " + detailsMap.get("band"));
                            deviceDisAssociationData.setMacAddress(Objects.isNull(disAssociationEvent.get("macAddress")) ? null : disAssociationEvent.get("macAddress").toString());
                            deviceDisAssociationData.setTimestamp(Long.valueOf(disAssociationEvent.get("timestamp").toString()));
                            deviceDisAssociationData.setVendor(manageCommonService.getVendorName(String.valueOf(disAssociationEvent.get("macAddress"))));
                            deviceDisAssociationDataList.add(deviceDisAssociationData);
                        }
                    });

                    response.put(type, deviceDisAssociationDataList);
                    break;

                case ATTR_DEVICE_ROAM_EVENT:
                    List<DBObject> roamingEvents = mongoService.findList(query, DEVICE_ROAMING, mongoFieldOptions);
                    bssidSets = new HashSet<>();
                    roamingEvents.forEach(roamingEvent -> {
                        bssidSets.add(String.valueOf(roamingEvent.get("obssid")));
                        bssidSets.add(String.valueOf(roamingEvent.get("nbssid")));
                    });

                    bssidDetails = getBssidDetails(bssidSets, userEquipment);

                    DeviceRoamEventDTO deviceRoamEventDTO = new DeviceRoamEventDTO();
                    HashSet<DeviceRoamEventDTO.DeviceRoamEventData> deviceRoamEventDataList = new HashSet<>();
                    roamingEvents.forEach(roamingEvent -> {
                        HashMap<String, Object> detailsMapOrigin = (HashMap<String, Object>) bssidDetails.get(String.valueOf(roamingEvent.get("obssid")));
                        HashMap<String, Object> detailsMapTarget = (HashMap<String, Object>) bssidDetails.get(String.valueOf(roamingEvent.get("nbssid")));
                        if (Objects.nonNull(detailsMapOrigin) && Objects.nonNull(detailsMapTarget) && Objects.nonNull(roamingEvent.get("timestamp")) && Long.valueOf(roamingEvent.get("timestamp").toString()) <= currentTimestamp) {
                            DeviceRoamEventDTO.DeviceRoamEventData deviceRoamEventData = deviceRoamEventDTO.new DeviceRoamEventData();
                            deviceRoamEventData.setFrom("Original / " + detailsMapOrigin.get("friendlyName") + " / " + detailsMapOrigin.get("ssid") + " / " + detailsMapOrigin.get("band") + " / " + roamingEvent.get("orssi") + " dBm");
                            deviceRoamEventData.setTo("Actual / " + detailsMapTarget.get("friendlyName") + " / " + detailsMapTarget.get("ssid") + " / " + detailsMapTarget.get("band") + " / " + roamingEvent.get("nrssi") + " dBm");
                            deviceRoamEventData.setMacAddress(Objects.isNull(roamingEvent.get("macAddress")) ? null : roamingEvent.get("macAddress").toString());
                            deviceRoamEventData.setTimestamp(Long.valueOf(roamingEvent.get("timestamp").toString()));
                            deviceRoamEventDataList.add(deviceRoamEventData);
                        }
                    });
                    response.put(type, deviceRoamEventDataList);
                    break;


                case ATTR_DEVICE_STEERING_EVENT:
                    List<DBObject> steeringEvents = mongoService.findList(query, DEVICE_STEERING, mongoFieldOptions);
                    mongoService.findList(query, DEVICE_STEERING, mongoFieldOptions);
                    steeringEvents = steeringEvents.stream().filter(p -> (!(Objects.nonNull(p.get("steeringEnd")) ? p.get("steeringEnd").toString() : EMPTY_STRING).equals(EMPTY_STRING))).collect(Collectors.toList());

                    bssidSets = new HashSet<>();
                    steeringEvents.forEach(steeringEvent -> {
                        bssidSets.add(String.valueOf(steeringEvent.get("ibssBSSID")));
                        bssidSets.add(String.valueOf(steeringEvent.get("tbssBSSID")));
                        bssidSets.add(String.valueOf(steeringEvent.get("obssBSSID")));
                    });

                    bssidDetails = getBssidDetails(bssidSets, userEquipment);

                    DeviceSteeringEventDTO deviceSteeringEventDTO = new DeviceSteeringEventDTO();
                    HashSet<DeviceSteeringEventDTO.DeviceSteeringEventData> deviceSteeringEventDataList = new HashSet<>();
                    steeringEvents.forEach(steeringEvent -> {
                        HashMap<String, Object> detailsMapOrigin = (HashMap<String, Object>) bssidDetails.get(String.valueOf(steeringEvent.get("obssBSSID")));
                        HashMap<String, Object> detailsMapIntended = (HashMap<String, Object>) bssidDetails.get(String.valueOf(steeringEvent.get("ibssBSSID")));
                        HashMap<String, Object> detailsMapTarget = (HashMap<String, Object>) bssidDetails.get(String.valueOf(steeringEvent.get("tbssBSSID")));
                        if (Objects.nonNull(detailsMapOrigin) && Objects.nonNull(detailsMapIntended) && Objects.nonNull(detailsMapTarget) && Objects.nonNull(steeringEvent.get("timestamp")) && Long.valueOf(steeringEvent.get("timestamp").toString()) <= currentTimestamp) {
                            DeviceSteeringEventDTO.DeviceSteeringEventData deviceSteeringEventData = deviceSteeringEventDTO.new DeviceSteeringEventData();
                            deviceSteeringEventData.setIntended("Intended / " + detailsMapIntended.get("friendlyName") + " / " + detailsMapIntended.get("ssid") + " / " + detailsMapIntended.get("band") + " / " + steeringEvent.get("ibssRSSI") + " dBm");
                            deviceSteeringEventData.setType("Steering Type : " + steeringEvent.get("steeringType"));
                            deviceSteeringEventData.setEnd("Steering End : " + steeringEvent.get("steeringEnd"));
                            deviceSteeringEventData.setOrigin("Original / " + detailsMapOrigin.get("friendlyName") + " / " + detailsMapOrigin.get("ssid") + " / " + detailsMapOrigin.get("band") + " / " + steeringEvent.get("obssRSSI") + " dBm");
                            deviceSteeringEventData.setTarget("Actual / " + ((Objects.isNull(steeringEvent.get("steeringEnd")) ? "N/A" : (steeringEvent.get("steeringEnd").equals("N/A") ? "N/A" : detailsMapTarget.get("friendlyName"))) + " / " + detailsMapTarget.get("ssid") + " / " + detailsMapTarget.get("band") + " / " + steeringEvent.get("tbssRSSI") + " dBm"));
                            deviceSteeringEventData.setMacAddress(Objects.isNull(steeringEvent.get("macAddress")) ? null : steeringEvent.get("macAddress").toString());
                            deviceSteeringEventData.setVendor(manageCommonService.getVendorName(String.valueOf(steeringEvent.get("macAddress"))));
                            deviceSteeringEventData.setTimestamp(Long.valueOf(steeringEvent.get("timestamp").toString()));
                            deviceSteeringEventDataList.add(deviceSteeringEventData);
                        }
                    });
                    response.put(type, deviceSteeringEventDataList);
                    break;

                case ATTR_DEVICE_STEERING_LOGS:
                    List<DBObject> steeringLogEvents = mongoService.findList(query, DEVICE_EVENT_LOG, mongoFieldOptions);
                    DeviceSteeringLogDTO deviceSteeringLogDTO = new DeviceSteeringLogDTO();
                    HashSet<DeviceSteeringLogDTO.DeviceSteeringLogData> deviceSteeringLogDataList = new HashSet<>();
                    steeringLogEvents.forEach(steeringLogEvent -> {
                        if (Objects.nonNull(steeringLogEvent.get("timestamp")) && Long.valueOf(steeringLogEvent.get("timestamp").toString()) <= currentTimestamp) {
                            DeviceSteeringLogDTO.DeviceSteeringLogData deviceSteeringLogData = deviceSteeringLogDTO.new DeviceSteeringLogData();
                            deviceSteeringLogData.setLog(Objects.isNull(steeringLogEvent.get("log")) ? null : steeringLogEvent.get("log").toString());
                            deviceSteeringLogData.setMacAddress(Objects.isNull(steeringLogEvent.get("macAddress")) ? null : steeringLogEvent.get("macAddress").toString());
                            deviceSteeringLogData.setVendor(manageCommonService.getVendorName(String.valueOf(steeringLogEvent.get("macAddress"))));
                            deviceSteeringLogData.setTimestamp(Long.valueOf(steeringLogEvent.get("timestamp").toString()));
                            deviceSteeringLogDataList.add(deviceSteeringLogData);
                        }
                    });
                    response.put(type, deviceSteeringLogDataList);
                    break;

                case ATTR_DEVICE_WIFI_RSSI:
                    DeviceRssiDetailDTO deviceRssiDetailDTO = new DeviceRssiDetailDTO();
                    ArrayList<DeviceRssiDetailDTO.DeviceRssiData> deviceRssiDataList = new ArrayList<>();
                    if (!userStationData.isEmpty()) {
                        HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
                        int rssiRange = LOWER_LIMIT;
                        try {
                            rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
                        } catch (Exception e) {

                        }
                        HashSet<String> serialNumbers = userStationData.parallelStream().map((devMap) -> String.valueOf(devMap.get("serialNumber"))).collect(Collectors.toCollection(HashSet::new));
                        HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
                        for (Map<String, Object> deviceGraph : userStationData) {
                            if (Objects.nonNull(deviceGraph.get("rssi")) && Double.valueOf(deviceGraph.get("rssi").toString()) != 0) {
                                DeviceRssiDetailDTO.DeviceRssiData deviceRssiData = deviceRssiDetailDTO.new DeviceRssiData();
                                if (Objects.nonNull(deviceGraph.get("serialNumber"))) {
                                    String band = Objects.isNull(deviceGraph.get("band")) ? "" : deviceGraph.get("band").toString().equals("1") ? "5G" : "2.4G";
                                    String friendlyName = equipmentFriendlyName.get(deviceGraph.get("serialNumber")) + " (" + band + ")";
                                    deviceRssiData.setEquipment(friendlyName);
                                } else {
                                    deviceRssiData.setEquipment("N/A");
                                }
                                deviceRssiData.setRssiRange(rssiRange);
                                deviceRssiData.setRssi(Double.valueOf(deviceGraph.get("rssi").toString()));
                                deviceRssiData.setTimestamp(Long.valueOf(deviceGraph.get("timestamp").toString()));
                                deviceRssiDataList.add(deviceRssiData);
                            }
                        }
                    }
                    response.put(type, deviceRssiDataList);
                    break;

                case ATTR_DEVICE_WIFI_THROUGHPUT:
                    DeviceWifiThroughputDTO deviceWifiThroughputDTO = new DeviceWifiThroughputDTO();
                    ArrayList<DeviceWifiThroughputDTO.DeviceWifiThroughputData> deviceWifiThroughputDataList = new ArrayList<>();
                    if (!userStationData.isEmpty()) {
                        for (Map<String, Object> throughputItem : userStationData) {
                            if (Objects.nonNull(throughputItem.get("avgTx")) && Objects.nonNull(throughputItem.get("avgRx"))) {
                                DeviceWifiThroughputDTO.DeviceWifiThroughputData deviceWifiThroughputData = deviceWifiThroughputDTO.new DeviceWifiThroughputData();
                                deviceWifiThroughputData.setRx(Double.valueOf(TWO_DECIMAL_PLACE.format(Double.parseDouble(throughputItem.get("avgRx").toString()))));
                                deviceWifiThroughputData.setTx(Double.valueOf(TWO_DECIMAL_PLACE.format(Double.parseDouble(throughputItem.get("avgTx").toString()))));
                                deviceWifiThroughputData.setTimestamp(Long.valueOf(throughputItem.get("timestamp").toString()));
                                deviceWifiThroughputDataList.add(deviceWifiThroughputData);
                            }
                        }
                    }
                    response.put(type, deviceWifiThroughputDataList);
                    break;

                case ATTR_DEVICE_WIFI_WIFI_PHY:
                    DeviceWifiPhyDTO deviceWifiPhyDTO = new DeviceWifiPhyDTO();
                    ArrayList<DeviceWifiPhyDTO.DeviceWifiPhyData> deviceWifiPhyDataList = new ArrayList<>();
                    if (!userStationData.isEmpty()) {
                        for (Map<String, Object> deviceGraph : userStationData) {
                            DeviceWifiPhyDTO.DeviceWifiPhyData deviceWifiPhyData = deviceWifiPhyDTO.new DeviceWifiPhyData();
                            deviceWifiPhyData.setDownlinkPhyRate(Objects.isNull(deviceGraph.get("lastDataUplinkRate")) ? 0.0 : Double.valueOf(deviceGraph.get("lastDataUplinkRate").toString()));
                            deviceWifiPhyData.setUplinkPhyRate(Objects.isNull(deviceGraph.get("lastDataDownlinkRate")) ? 0.0 : Double.valueOf(deviceGraph.get("lastDataDownlinkRate").toString()));
                            deviceWifiPhyData.setTimestamp(Long.valueOf(deviceGraph.get("timestamp").toString()));
                            deviceWifiPhyDataList.add(deviceWifiPhyData);
                        }
                    }
                    response.put(type, deviceWifiPhyDataList);
                    break;
                default:
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid attr value : " + type.trim());
            }
        });

        return response;
    }

    private HashMap<String, Object> getBssidDetails(HashSet<String> bssidSet, Equipment userEquipment) {
        HashMap<String, Object> bssidDetails = new HashMap<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject projection = new BasicDBObject();
        projection.put("macAddress", 1);
        projection.put("friendlyName", 1);
        projection.put("modelName", 1);
        projection.put("serialNumber", 1);
        projection.put("bssid24G", 1);
        projection.put("bssid5G", 1);

        List<BasicDBObject> apDetailList = mongoService.findList(query, null, ActiontecConstants.AP_DETAIL, projection);

        bssidSet.forEach(bssid -> {
            // Handle for 00:00:00:00:00:00 BSSID
            if (bssid.trim().equals("00:00:00:00:00:00")) {
                HashMap<String, String> details = new HashMap<>();
                details.put("ssid", "N/A");
                details.put("band", "N/A");
                details.put("friendlyName", "N/A");
                bssidDetails.put(bssid, details);
            } else {
                for (BasicDBObject apDetail : apDetailList) {
                    HashMap<String, String> details = new HashMap<>();
                    List<HashMap<String, String>> bssid5G = Objects.nonNull(apDetail.get("bssid5G")) ? (List<HashMap<String, String>>) apDetail.get("bssid5G") : new ArrayList<>();
                    bssid5G = bssid5G.stream().filter((bssid5GDetail) -> String.valueOf(bssid5GDetail.get("bssid")).equals(bssid)).collect(Collectors.toList());
                    if (!bssid5G.isEmpty()) {
                        details.put("ssid", String.valueOf(bssid5G.get(0).get("ssid")));
                        details.put("band", "5GHz");
                        details.put("friendlyName", manageCommonService.getDisplayNameByPriorityForEquipment(apDetail));

                        bssidDetails.put(bssid, details);
                        break;
                    }

                    List<HashMap<String, String>> bssid24G = Objects.nonNull(apDetail.get("bssid24G")) ? (List<HashMap<String, String>>) apDetail.get("bssid24G") : new ArrayList<>();
                    bssid24G = bssid24G.stream().filter((bssid24GDetail) -> String.valueOf(bssid24GDetail.get("bssid")).equals(bssid)).collect(Collectors.toList());
                    if (!bssid24G.isEmpty()) {
                        details.put("ssid", String.valueOf(bssid24G.get(0).get("ssid")));
                        details.put("band", "2.4GHz");
                        details.put("friendlyName", manageCommonService.getDisplayNameByPriorityForEquipment(apDetail));

                        bssidDetails.put(bssid, details);
                        break;
                    }
                }
            }
        });

        return bssidDetails;
    }
}
