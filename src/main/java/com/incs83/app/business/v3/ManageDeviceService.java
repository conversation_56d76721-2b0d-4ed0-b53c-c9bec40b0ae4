package com.incs83.app.business.v3;

import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.ManageNetworkService;
import com.incs83.app.business.v2.RPCUtilityService;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.StationSpeedTest;
import com.incs83.app.entities.cassandra.DeviceCapability;
import com.incs83.app.enums.APDetailLookType;
import com.incs83.app.responsedto.v2.Device.DeviceDetailDTO;
import com.incs83.app.responsedto.v2.Device.DeviceListDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.service.repository.CassandraRepository;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.*;

@Service("v3.ManageDeviceService")
public class ManageDeviceService {

    @Autowired
    CassandraRepository cassandraRepository;
    @Autowired
    private RPCUtilityService rpcUtilityService;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private com.incs83.app.business.v2.ManageDeviceService manageDeviceServiceV2;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ManageNetworkService manageNetworkService;

    public DeviceListDTO getDeviceList(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        params.put("userId", userEquipment.getRgwSerial());
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("moca", 0);
        mongoFieldOptions.put("bssid24G", 0);
        mongoFieldOptions.put("bssid5G", 0);

        List<BasicDBObject> equipmentList = mongoService.findList(params, AP_DETAIL, mongoFieldOptions);

        HashMap<String, String> aggregationParams = new HashMap<>();
        aggregationParams.put("outputParams", "devices,count");
        aggregationParams.put("label", "$serialNumber");
        aggregationParams.put("operation", "$push,$sum");
        aggregationParams.put("keyToAggregate", "$$ROOT,1");
        aggregationParams.put("operand", "$gte");


        mongoFieldOptions.clear();
        mongoFieldOptions.put("devices._id", 0);
        List<BasicDBObject> devicesList = new ArrayList();
        List<BasicDBObject> StationDevices = mongoService.aggregate(params, null, STATION_DETAIL, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_3_DAYS), aggregationParams, TIMESTAMP, mongoFieldOptions);
        if (Objects.nonNull(StationDevices) && !StationDevices.isEmpty()) {
            for (int i = 0; i < StationDevices.size(); i++) {
                devicesList.addAll((List<BasicDBObject>) StationDevices.get(i).get("devices"));
            }
        }
        String isp = equipmentList.get(ZERO).getString("isp");
        String version = equipmentList.get(ZERO).getString("etlVersion");
        String actualIsp = isp.split(HYPHEN_STRING)[0];
        if (!VERIZON_ISP.equals(actualIsp) || (VERIZON_ISP.equals(actualIsp) && version.equalsIgnoreCase("v4"))) {
            devicesList.addAll(manageCommonService.getHostnameDetail(userEquipment.getRgwSerial()));
        }
        HashMap<String, List<BasicDBObject>> allDevices = new HashMap();
        for (BasicDBObject device : devicesList) {
            if (!allDevices.containsKey(device.getString("macAddress"))) {
                allDevices.put(device.getString("macAddress"), new ArrayList());
            }
            allDevices.get(device.getString("macAddress")).add(device);
        }
        ArrayList<BasicDBObject> filteredDeviceList = new ArrayList<>();
        allDevices.values().forEach(deviceList -> {
            if (deviceList.size() == ONE) {
                filteredDeviceList.addAll(deviceList);
            } else {
                for (BasicDBObject d : deviceList) {
                    if (Objects.isNull(d.getString("phyType"))) {
                        filteredDeviceList.add(d);
                        break;
                    }
                }
            }
        });
        DeviceListDTO deviceListDTO = new DeviceListDTO();
        ArrayList<DeviceListDTO.DeviceDetailListData> deviceDetailDataList = new ArrayList<>();
        for (BasicDBObject equipment : equipmentList) {
            String equipmentSerialNumber = equipment.getString("serialNumber");
            List<BasicDBObject> filterBySerialDeviceList = filteredDeviceList.parallelStream().filter(d ->
                    d.getString("serialNumber").equals(equipmentSerialNumber)).collect(Collectors.toList());
            if (!filterBySerialDeviceList.isEmpty()) {
//                List<BasicDBObject> devices = (List<BasicDBObject>) filterBySerialDeviceList.get(0).get("devices");
                HashSet<String> deviceMacList = filterBySerialDeviceList.stream().map((device) -> String.valueOf(device.get("macAddress"))).collect(Collectors.toCollection(HashSet::new));
                HashMap<String, String> stationFriendlyName = manageCommonService.getFriendlyNameForStation(deviceMacList, userEquipment);
                for (BasicDBObject device : filterBySerialDeviceList) {
                    if (Objects.nonNull(device)) {
                        DeviceListDTO.DeviceDetailListData deviceDetail = deviceListDTO.new DeviceDetailListData();
                        String band = device.getString("band");
                        String userId = device.getString("userId");
                        String serialNumber = device.getString("serialNumber");
                        String bssid = device.getString("bssid");
                        if (band != null || userId != null || serialNumber != null || bssid != null) {
                            deviceDetail.setSsid(manageCommonService.getSsidForDeviceDetail(userId, serialNumber, bssid));
                        } else {
                            deviceDetail.setSsid(null);
                        }
                        deviceDetail.setConnectedTo(device.getString("serialNumber"));
                        deviceDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(device));
                        deviceDetail.setIp(device.getString("ip"));
                        deviceDetail.setMac(device.getString("macAddress"));
                        deviceDetail.setRssi(Objects.isNull(device.get("rssi")) ? 0D : Long.valueOf(String.valueOf(device.get("rssi"))));
                        deviceDetail.setVendor(manageCommonService.getVendorName(device.getString("macAddress")));
                        deviceDetail.setName(Objects.isNull(device.get("phyType")) ? stationFriendlyName.get(device.getString("macAddress")) : manageCommonService.getDisplayNameByPriorityForDevice(device));
                        deviceDetail.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetail.getMac(), device.getString("userId")));
                        deviceDetail.setDeviceType(Objects.isNull(device.get("deviceType")) ? "Other" : device.getString("deviceType"));
                        deviceDetail.setBand(Objects.isNull(device.get("phyType")) ? device.getString("band") : device.getString("phyType"));
                        deviceDetail.setLastReportedAt(Objects.isNull(device.get("lastReportAt")) ? device.getLong("timestamp") : device.getLong("lastReportAt"));
                        deviceDetailDataList.add(deviceDetail);
                    }
                }
            }
//            manageCommonService.addMiscDevicesForEquipment(equipmentSerialNumber, dataFromHostnameDetail, deviceDetailDataList, userAP);
        }
        if (deviceDetailDataList.isEmpty()) {
            DeviceListDTO.DeviceDetailListData deviceDetail = deviceListDTO.new DeviceDetailListData();
            deviceDetailDataList.add(deviceDetail);
        }
        deviceListDTO.setData(deviceDetailDataList);
        return deviceListDTO;
    }

    public DeviceDetailDTO fetchDeviceDetailsByMacAddr(String equipmentIdOrSerialOrSTN, String macAddr) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddr, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddr, userEquipment);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
        boolean flag = (Objects.nonNull(commonProps.get(ABS_DATABASE)) && commonProps.get(ABS_DATABASE).equalsIgnoreCase("MONGO")) ? true : false;

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("macAddress", macAddr);
        queryParams.put("userId", userEquipment.getRgwSerial());
        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        queryParams.clear();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);

        List<String> projection = new ArrayList<>();

        List<DeviceCapability> deviceCapability;
        if (flag) {
            List<DBObject> deviceCapabilityMongo = mongoService.findList(DEVICE_CAPABILITY, queryParams, TIMESTAMP, DESC);
            deviceCapability = manageCommonService.convertToDeviceCapability(deviceCapabilityMongo);
        } else {
            deviceCapability = (List<DeviceCapability>) cassandraRepository.read(DeviceCapability.class, queryParams, null, null, projection, 0);
        }
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        int minutes = 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
        aggregationWhereClause.put("dateHour", dateCriteria);

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
//        mongoFieldOptions.put("devMap." + macAddr, 1);

        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(aggregationWhereClause, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);

        double downLinkBytes = 0.0;
        double upLinkBytes = 0.0;
        if (!deviceDownLinkUpLinkBytes.isEmpty()) {
            List<DBObject> actualDevMap = new ArrayList<>();
            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    List<DBObject> devMap = (List<DBObject>) ((DBObject) dbObject.get("devMap")).get(macAddr);
                    if (Objects.nonNull(devMap)) {
                        actualDevMap.addAll(devMap);
                    }
                }

                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<DBObject> latestDevMap = (List<DBObject>) userSlidingData.get("devMap").get(macAddr);
                        if (Objects.nonNull(latestDevMap)) {
                            actualDevMap.addAll(latestDevMap);
                        }
                    }
                }
            }
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            actualDevMap = actualDevMap.stream().filter(element -> ((Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp && (Objects.nonNull(element.get("rx")) && Objects.nonNull(element.get("tx"))))).collect(Collectors.toList());
            if (!actualDevMap.isEmpty()) {
                downLinkBytes = downLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("rx")) ? Double.valueOf(element.get("rx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
                upLinkBytes = upLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("tx")) ? Double.valueOf(element.get("tx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
            }
        }

//        List<StationSpeedTest> stationSpeedTestList = mongoService.read(StationSpeedTest.class, new Query().addCriteria(Criteria.where("macAddr").is(macAddr)).with(new Sort(Sort.Direction.DESC, DATE)).limit(1));
        HashMap<String, Object> query = new HashMap<>();
        query.put("macAddr", macAddr);

        List<DBObject> stationSpeedTestList = mongoService.findList(STATION_SPEED_TEST_INFO, query, DATE, DESC);

        StationSpeedTest stationSpeedTest = null;
        if (Objects.nonNull(stationSpeedTestList) && !stationSpeedTestList.isEmpty()) {
            stationSpeedTest = new StationSpeedTest();
            stationSpeedTest.setDate((Date) (stationSpeedTestList.get(0).get("date")));
            stationSpeedTest.setDataRate(Objects.isNull(stationSpeedTestList.get(0).get("dataRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("dataRate"))));
            stationSpeedTest.setRxRate(Objects.isNull(stationSpeedTestList.get(0).get("rxRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("rxRate"))));
            stationSpeedTest.setTxRate(Objects.isNull(stationSpeedTestList.get(0).get("txRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("txRate"))));
            stationSpeedTest.setBand(Objects.isNull(stationSpeedTestList.get(0).get("band")) ? "N/A" : stationSpeedTestList.get(0).get("band").toString());
            stationSpeedTest.setIpAddr(Objects.isNull(stationSpeedTestList.get(0).get("ipAddr")) ? "N/A" : stationSpeedTestList.get(0).get("ipAddr").toString());
            stationSpeedTest.setMacAddr(Objects.isNull(stationSpeedTestList.get(0).get("macAddr")) ? "N/A" : stationSpeedTestList.get(0).get("macAddr").toString());
            stationSpeedTest.setUserId(Objects.isNull(stationSpeedTestList.get(0).get("userId")) ? "N/A" : stationSpeedTestList.get(0).get("userId").toString());
        }

        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
        DeviceDetailDTO.DeviceDetailData deviceDetailData = deviceDetailDTO.new DeviceDetailData();
        DeviceDetailDTO.DeviceDetailData.DeviceSpeedTest deviceSpeedTest = deviceDetailData.new DeviceSpeedTest();

        if (Objects.nonNull(stationSpeedTest)) {
            deviceSpeedTest.setDataRate(stationSpeedTest.getDataRate());
            deviceSpeedTest.setDate(stationSpeedTest.getDate());
        } else {
            deviceSpeedTest.setDataRate(0f);
            deviceSpeedTest.setDate(0l);
        }
        deviceDetailData.setSpeedTestStats(deviceSpeedTest);

        if (Objects.nonNull(deviceDetails)) {
            String band = Objects.nonNull(deviceDetails.get("band")) ? deviceDetails.get("band").toString() : null;
            String userId = Objects.nonNull(deviceDetails.get("userId")) ? deviceDetails.get("userId").toString() : null;
            String serialNumber = Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString();
            String bssid = Objects.isNull(deviceDetails.get("bssid")) ? null : deviceDetails.get("bssid").toString();

            if (band != null || userId != null || serialNumber != null || bssid != null) {
                deviceDetailData.setSsid(manageCommonService.getSsidForDeviceDetail(userId, serialNumber, bssid));
            } else {
                deviceDetailData.setSsid(null);
            }

            deviceDetailData.setConnectedTo(Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString());
            if (Objects.nonNull(deviceDetailData.getConnectedTo())) {
                HashSet<String> serialNumbers = new HashSet<>();
                serialNumbers.add(deviceDetailData.getConnectedTo());
                HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
                deviceDetailData.setConnectedToName(equipmentFriendlyName.get(deviceDetailData.getConnectedTo()));
            }
            deviceDetailData.setName(manageCommonService.getDisplayNameForStation(deviceDetails.get("macAddress").toString(), userEquipment));
            deviceDetailData.setDeviceType(Objects.isNull(deviceDetails.get("deviceType")) ? "Other" : deviceDetails.get("deviceType").toString());
            Set<String> maccAddressSet = new HashSet<>();
            maccAddressSet.add(deviceDetails.get("macAddress").toString());
            deviceDetailData.setHostname(manageCommonService.getHostNameForStation(maccAddressSet, userEquipment).get(deviceDetails.get("macAddress").toString()));
            deviceDetailData.setIp(Objects.isNull(deviceDetails.get("ip")) ? null : deviceDetails.get("ip").toString());
            deviceDetailData.setMac(Objects.isNull(deviceDetails.get("macAddress")) ? null : deviceDetails.get("macAddress").toString());
            deviceDetailData.setVendor(manageCommonService.getVendorName(String.valueOf(deviceDetails.get("macAddress"))));
            deviceDetailData.setWifiMode(Objects.isNull(deviceDetails.get("wifiMode")) ? null : deviceDetails.get("wifiMode").toString());
            deviceDetailData.setBand(Objects.isNull(deviceDetails.get("band")) ? null : deviceDetails.get("band").toString());
            deviceDetailData.setRssi(Objects.isNull(deviceDetails.get("rssi")) ? 0.0 : Double.valueOf(deviceDetails.get("rssi").toString()));
            deviceDetailData.setDownlinkPhyRate(Objects.isNull(deviceDetails.get("downlinkPhyRate")) ? 0.0 : Double.valueOf(deviceDetails.get("downlinkPhyRate").toString()));
            deviceDetailData.setUplinkPhyRate(Objects.isNull(deviceDetails.get("uplinkPhyRate")) ? 0.0 : Double.valueOf(deviceDetails.get("uplinkPhyRate").toString()));
            deviceDetailData.setDownlinkErrors(Objects.isNull(deviceDetails.get("downlinkErrors")) ? 0.0 : Double.valueOf(deviceDetails.get("downlinkErrors").toString()));
            deviceDetailData.setDownlinkRetransmissions(Objects.isNull(deviceDetails.get("downlinkRetransmissions")) ? 0.0 : Double.valueOf(String.valueOf(deviceDetails.get("downlinkRetransmissions"))));
            if (Objects.nonNull(deviceDetails.get("lastReportAt"))) {
                if (deviceDetails.get("lastReportAt") instanceof Date) {
                    deviceDetailData.setLastReportAt(((Date) deviceDetails.get("lastReportAt")).getTime());
                } else {
                    deviceDetailData.setLastReportAt(Long.valueOf(deviceDetails.get("lastReportAt").toString()));
                }
            } else {
                deviceDetailData.setLastReportAt(new Date().getTime());
            }
            deviceDetailData.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(deviceDetails));
            deviceDetailData.setInternetOn(true);
            deviceDetailData.setDownlinkBytes(downLinkBytes != 0.0 ? downLinkBytes : 0);
            deviceDetailData.setUplinkBytes(upLinkBytes != 0.0 ? upLinkBytes : 0);
            deviceDetailData.setCapability(Objects.nonNull(deviceCapability) && !deviceCapability.isEmpty() ? (Objects.isNull(deviceCapability.get(0).getCapability()) ? null : deviceCapability.get(0).getCapability()) : null);
            deviceDetailData.setIsp(Objects.isNull(deviceDetails.get("isp")) ? "N/A" : String.valueOf(deviceDetails.get("isp")));
            // ALARMS for DEVICE START
            deviceDetailData.setAlarms(manageDeviceServiceV2.getAlarmListForDevice(deviceDetailData,userId, manageDeviceServiceV2.getRgDfsEnabled(userId)));
            HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
            int rssiRange = LOWER_LIMIT;
            try {
                rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
            } catch (Exception e) {

            }

            deviceDetailData.setRssiRange(rssiRange);
        }
        deviceDetailDTO.setData(deviceDetailData);
        return deviceDetailDTO;
    }
}
