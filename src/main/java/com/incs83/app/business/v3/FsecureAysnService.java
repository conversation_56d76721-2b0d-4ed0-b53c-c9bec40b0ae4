package com.incs83.app.business.v3;

import com.incs83.context.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

@Service
@EnableAsync
public class FsecureAysnService {

    @Autowired
    private ManageFSecureService manageFSecureService;

    @Async
    public void deleteRegistrationKey(String rgwSerial, ExecutionContext executionContext) throws Exception {
        ExecutionContext.CONTEXT.set(executionContext);
        manageFSecureService.deleteRegistrationKey(rgwSerial);
    }


}
