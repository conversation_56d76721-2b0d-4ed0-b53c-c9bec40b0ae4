package com.incs83.app.business.v3;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.common.v3.FsecureProvisionPaginatedRequestDTO;
import com.incs83.app.common.v3.FsecureProvisionRequestDTO;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.responsedto.v3.FsecureProvisioning.FsecureProvisionResponsePaginatedDTO;
import com.incs83.app.responsedto.v3.FsecureProvisioning.FsecureStatusResponseDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;


@Service
public class ManageFSecureProvisioning {

    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private FsecureAysnService fsecureAysnService;

    private static final Logger LOG = LogManager.getLogger("org");
    private final String SERVICE_DETAIL = ActiontecConstants.SERVICE_DETAIL;

    public FsecureStatusResponseDTO getFsecureStatus(String equipmentIdOrSerialOrSTN) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        manageCommonService.requireNonNull(equipment, "Equipment does not found");
        manageCommonService.requireNonNullAndNonEmpty(equipment.getRgwSerial(), "RGW Serial cannot be null or empty");
        manageCommonService.requireNonNull(equipment.getSubscriber(), "This equipment is currently not attached to any Subscriber");
        manageCommonService.requireNonNull(equipment.getSubscriber().getId(), "This equipment is currently not attached to any Subscriber");

        HashMap<String, Object> appendableParams = new HashMap<String, Object>();
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", equipment.getRgwSerial());
        queryParams.put("enabled", new BasicDBObject("$exists", true));

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.append("_id", 0);
        mongoFieldOptions.put("userId", 1);
        mongoFieldOptions.put("isp", 1);
        mongoFieldOptions.put("attempt", 1);
        mongoFieldOptions.put("enabled", 1);
        mongoFieldOptions.put("previousEnabled", 1);
        mongoFieldOptions.put("createdAt", 1);
        mongoFieldOptions.put("updatedAt", 1);
        mongoFieldOptions.put("fsecureForce", 1);
        mongoFieldOptions.put("serviceTelephoneNo", 1);
//        mongoFieldOptions.put("CPE-Security.active",1);

        DBObject dbObject = mongoService.findOne(queryParams, appendableParams, SERVICE_DETAIL, mongoFieldOptions);
        manageCommonService.requireNonNull(dbObject, "This record does not found");
        FsecureStatusResponseDTO res = objectMapper.convertValue(dbObject, FsecureStatusResponseDTO.class);

        return res;
    }


    public Object removeFsecure(String equipmentIdOrSerialOrSTN) throws Exception {
        LOG.debug("remove Fsecure. id:[{}]", equipmentIdOrSerialOrSTN);

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        manageCommonService.requireNonNull(equipment, "This equipment does not exist");
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", equipment.getRgwSerial());
        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject mongoFieldsOption = new BasicDBObject();
        mongoFieldsOption.put("_id", 0);
        BasicDBObject dbObject = (BasicDBObject) mongoService.findOne(queryParams, appendableParams, SERVICE_DETAIL, mongoFieldsOption);

        manageCommonService.requireNonNull(dbObject, "This record does not exist ");

        DBObject queryParam = new BasicDBObject();
        queryParam.put("userId", equipment.getRgwSerial());

        BasicDBObject dataToUpdate = new BasicDBObject();
        dataToUpdate.put("fsecureForce", true);
        dataToUpdate.put("transitionId", "");
        dataToUpdate.put("state", 0);
        dataToUpdate.put("attempt", 0);
        dataToUpdate.put("enabled", false);
        dataToUpdate.put("key", "");
        dataToUpdate.put("updatedAt", CommonUtils.getCurrentTimeInMillis());
        dataToUpdate.put("previousEnabled", dbObject.get("enabled"));
        DBObject setData = new BasicDBObject();
        setData.put("$set", dataToUpdate);

        BasicDBObject dataToRemove = new BasicDBObject();
        dataToRemove.put("fsecureLastAttemptTime", 1);
        setData.put("$unset", dataToRemove);

        mongoService.update(queryParam, setData, false, false, SERVICE_DETAIL);
        return new ArrayList();
    }


    public Object setFsecureKey(String equipmentIdOrSerialOrSTN, FsecureProvisionRequestDTO fsecureProvisionRequestDTO) throws Exception {

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        manageCommonService.requireNonNull(equipment, "Equipment does not found");
        manageCommonService.requireNonNullAndNonEmpty(equipment.getRgwSerial(), "RGW Serial cannot be null or empty");
        manageCommonService.requireNonNull(equipment.getSubscriber(), "This equipment is currently not attached to any Subscriber");
        manageCommonService.requireNonNull(equipment.getSubscriber().getId(), "This equipment is currently not attached to any Subscriber");
        manageCommonService.requireNonNull(equipment, "The request Body cannot remain empty");

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", equipment.getRgwSerial());
        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject mongoFieldsOption = new BasicDBObject();
        mongoFieldsOption.put("_id", 0);
        BasicDBObject dbObject = (BasicDBObject) mongoService.findOne(queryParams, appendableParams, SERVICE_DETAIL, mongoFieldsOption);

        if (Objects.isNull(dbObject)) {
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("userId", equipment.getRgwSerial());
            if (Objects.nonNull(fsecureProvisionRequestDTO.getKey()) && !fsecureProvisionRequestDTO.getKey().isEmpty())
                dataMap.put("key", fsecureProvisionRequestDTO.getKey());

            dataMap.put("enabled", fsecureProvisionRequestDTO.isEnabled());
            dataMap.put("previousEnabled", fsecureProvisionRequestDTO.isEnabled());

            if (fsecureProvisionRequestDTO.isEnabled()) {
                dataMap.put("fsecureForce", true);
                dataMap.put("attempt", 0);
                dataMap.put("transitionId", "");
                dataMap.put("state", 0);
            }

            dataMap.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
            dataMap.put("createdAt", CommonUtils.getCurrentTimeInMillis());
            dataMap.put("updatedAt", CommonUtils.getCurrentTimeInMillis());
            mongoService.create(SERVICE_DETAIL, dataMap);

        } else {
            BasicDBObject dataToUpdate = new BasicDBObject();
            if (Objects.isNull(dbObject.get("enabled"))) {
                dataToUpdate.put("createdAt", CommonUtils.getCurrentTimeInMillis());
                dataToUpdate.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
            }
            BasicDBObject queryParam = new BasicDBObject();
            queryParam.put("userId", equipment.getRgwSerial());
            if (Objects.nonNull(fsecureProvisionRequestDTO.getKey()) && !fsecureProvisionRequestDTO.getKey().isEmpty())
                dataToUpdate.put("key", fsecureProvisionRequestDTO.getKey());
            dataToUpdate.put("previousEnabled", dbObject.get("enabled"));
            dataToUpdate.put("enabled", fsecureProvisionRequestDTO.isEnabled());

            if (fsecureProvisionRequestDTO.isEnabled()) {
                dataToUpdate.put("fsecureForce", true);
                dataToUpdate.put("attempt", 0);
                dataToUpdate.put("transitionId", "");
                dataToUpdate.put("state", 0);
            }
            dataToUpdate.put("updatedAt", CommonUtils.getCurrentTimeInMillis());
            DBObject setData = new BasicDBObject();
            setData.put("$set", dataToUpdate);

            BasicDBObject dataToRemove = new BasicDBObject();
            dataToRemove.put("fsecureLastAttemptTime", 1);
            setData.put("$unset", dataToRemove);

            mongoService.update(queryParam, setData, false, false, SERVICE_DETAIL);
            LOG.info(" api updated successful  =====> ");
        }
        return new ArrayList();
    }


    public FsecureProvisionResponsePaginatedDTO getFsecurePaginatedProvisions(FsecureProvisionPaginatedRequestDTO fsecureProvisionPaginatedRequestDTO) {
        if (CommonUtils.isEndUser()) {
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        }

        BasicDBObject queryParams = new BasicDBObject();
        queryParams.put("CPE-Security.active", false);
        queryParams.put("enabled", true);
        if (CommonUtils.isGroupAdmin()) {
            String isp = at3Adapter.getIspNameByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            if (!StringUtils.isBlank(isp))
                queryParams.put("isp", isp);
        }

        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", 0);
        projection.put("userId", 1);
        projection.put("attempt", 1);
        projection.put("serviceTelephoneNo", 1);
        projection.put("fsecureForce", 1);
        projection.put("enabled", 1);
        projection.put("previousEnabled", 1);
        projection.put("createdAt", 1);
        projection.put("updatedAt", 1);
        projection.put("isp", 1);

//        projection.put("CPE-Security.active", 1);
//        query =>      db.getCollection('SERVICE_DETAIL').aggregate([ {"$match" : { "userId" : "GTDA8341604724" }} , { "$sort" : { "createdAt" : -1 }} ,  { "$skip" : 0  } , { "$limit" : 10 }    ,   {"$project": { "_id" : 0 , "enabled" : 1 , "cpe_security_Active" : "$CPE-Security.active" , userId : 1 , attempt : 1 , serviceTelephoneNo : 1 , createdAt : 1 , updatedAt : 1 , fsecureForce : 1 , isp : 1  }}    ]);

        String sortBy = fsecureProvisionPaginatedRequestDTO.getSortBy();

        List<Integer> sortTypeList = Arrays.asList(1, -1);
        int sortType = fsecureProvisionPaginatedRequestDTO.getSortType();
        if (!(sortTypeList.contains(sortType)))
            sortType = -1;

        int max = fsecureProvisionPaginatedRequestDTO.getMax();
        if (max < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max Could not be less than 0 ");

        int offset = fsecureProvisionPaginatedRequestDTO.getOffset();
        if (offset < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Offset Could not be less than 0 ");

        if (offset > max)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Offset cann't be greater than max ");

        long totalCount = mongoService.count(queryParams, null, SERVICE_DETAIL);
        Object paginatedFsecureList = mongoService.findList(SERVICE_DETAIL, queryParams, sortBy, sortType, projection, max, offset);

        List<FsecureStatusResponseDTO> paginatedDTOS = new ArrayList<>();

        ArrayList fsecureDTOList = manageCommonService.requireNonNull(objectMapper.convertValue(paginatedFsecureList, ArrayList.class), "Error in mapping the paginated list");
        for (Object fsecureDTO : fsecureDTOList) {
            BasicDBObject dbObject = (BasicDBObject) fsecureDTO;
            FsecureStatusResponseDTO obj = objectMapper.convertValue(dbObject, FsecureStatusResponseDTO.class);
            paginatedDTOS.add(obj);
        }

        FsecureProvisionResponsePaginatedDTO fsecureProvisionResponsePaginatedDTO = new FsecureProvisionResponsePaginatedDTO();
        fsecureProvisionResponsePaginatedDTO.setTotalCount(totalCount);
        fsecureProvisionResponsePaginatedDTO.setFsecureStatusResponseDTO(paginatedDTOS);
        return fsecureProvisionResponsePaginatedDTO;
    }
}
