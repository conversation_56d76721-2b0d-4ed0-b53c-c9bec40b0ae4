package com.incs83.app.business.v3;

import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.Equipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.ValidationException;
import com.mongodb.BasicDBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
public class AutoWifiConfigRestoreSettingService {
    private static final Logger logger = LogManager.getLogger("org");
    private final String COLLECTION_cpeExportedConfigStorageArea = "cpeExportedConfigStorageArea";
    private final String FIELD__serviceTelephoneNumber = "serviceTelephoneNumber";
    private final String FIELD__setting = "setting";
    public static final String FIELD__setting_autoConfigRestoreEnabled = "autoRestoreEnabled";

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    private String readStn(String equipmentIdOrSerialOrSTN) throws Exception {
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        }
        return equipment.getServiceTelephoneNo();
    }

    public void updateByVagueId(String equipmentIdOrSerialOrSTN, Map<String, Object> autoWifiConfigRestoreSettingMap) throws Exception {
        updateByServiceTel(readStn(equipmentIdOrSerialOrSTN), autoWifiConfigRestoreSettingMap);
    }

    public Map<String, Object> findByVagueId(String equipmentIdOrSerialOrSTN) throws Exception {
        return findByServiceTel(readStn(equipmentIdOrSerialOrSTN));
    }

    public void updateByServiceTel(String serviceTel, Map<String, Object> autoWifiConfigRestoreSettingMap) {

        logger.warn("update by servicetel serviceTel:[{}] setting[{}]", serviceTel, autoWifiConfigRestoreSettingMap);

        BasicDBObject queryDbObj = new BasicDBObject();
        queryDbObj.put(FIELD__serviceTelephoneNumber, serviceTel);

        BasicDBObject settingDbObj = new BasicDBObject();
        settingDbObj.put(FIELD__setting_autoConfigRestoreEnabled, (Boolean) autoWifiConfigRestoreSettingMap.get(FIELD__setting_autoConfigRestoreEnabled));
        BasicDBObject setDbObj = new BasicDBObject();
        setDbObj.put(FIELD__setting, settingDbObj);
        BasicDBObject updateDbObj = new BasicDBObject();
        updateDbObj.put("$set", setDbObj);

        mongoService.update(queryDbObj, updateDbObj, false, true, COLLECTION_cpeExportedConfigStorageArea);
    }

    public Map<String, Object> findByServiceTel(String serviceTel) {
        BasicDBObject queryDbObj = new BasicDBObject();
        queryDbObj.put(FIELD__serviceTelephoneNumber, serviceTel);

        Map<String, Object> result;
        BasicDBObject storageAreaDbObj = (BasicDBObject) mongoService.findOne(COLLECTION_cpeExportedConfigStorageArea, queryDbObj);
        if (storageAreaDbObj == null || storageAreaDbObj.get(FIELD__setting) == null) {
            result = new HashMap<>();
            result.put(FIELD__setting_autoConfigRestoreEnabled, false);
        } else {
            result = (BasicDBObject) storageAreaDbObj.get(FIELD__setting);
        }
        return result;
    }
}
