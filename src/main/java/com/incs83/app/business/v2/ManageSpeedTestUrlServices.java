package com.incs83.app.business.v2;

import com.incs83.app.common.v2.WanSpeedTestRequest;
import com.incs83.app.entities.Job;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.WAN_SPEED_TEST_INFO;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class ManageSpeedTestUrlServices {

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private DataAccessService dataAccessService;

    public WanSpeedTestRequest createWanSpeedTest(WanSpeedTestRequest wanSpeedTestRequest) {

        /*if (Objects.isNull(wanSpeedTestRequest.getUrls()) || wanSpeedTestRequest.getUrls().trim().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please Enter a Valid URL");*/

       /* if (Objects.nonNull(wanSpeedTestRequest.getUrls()) && !wanSpeedTestRequest.getUrls().isEmpty()) {
           if (!ValidationUtil.isValidURL(wanSpeedTestRequest.getUrls()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid URL specified.");
        }*/
        if (Objects.nonNull(wanSpeedTestRequest.getScheduledEvent()) && (wanSpeedTestRequest.getScheduledEvent() > 5000 || wanSpeedTestRequest.getScheduledEvent() < 1))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max Limit allowed is greater than or equals to 1 and less than or equal to 5000 (max >= 1 and max <= 5000)");

        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);
        fieldsToRemove.put("_class", ZERO);
        fieldsToRemove.put("timestamp", ZERO);
        List<DBObject> resp = mongoService.findList(new HashMap<>(), WAN_SPEED_TEST_INFO, fieldsToRemove);
        if (Objects.nonNull(resp) && !resp.isEmpty()) {
            resp = resp.parallelStream().filter(element -> Objects.nonNull(element.get("name")) && element.get("name").toString().equalsIgnoreCase(wanSpeedTestRequest.getName())).collect(Collectors.toList());
            if (!resp.isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Speed Test URL already exist with same name");
        }

        HashMap<String, Object> wanSpeedTestDetails = new HashMap<>();
        wanSpeedTestDetails.put("name", wanSpeedTestRequest.getName());
        wanSpeedTestDetails.put("id", CommonUtils.generateUUID());
        wanSpeedTestDetails.put("urls", (Objects.isNull(wanSpeedTestRequest.getUrls()) || wanSpeedTestRequest.getUrls().trim().isEmpty()) ? null : wanSpeedTestRequest.getUrls());
        wanSpeedTestDetails.put("scheduledEvent", wanSpeedTestRequest.getScheduledEvent());
        List<String> groups = new ArrayList<>();
        groups.add(ExecutionContext.get().getUsercontext().getGroupId());
        wanSpeedTestDetails.put("compartments", groups);
        wanSpeedTestDetails.put("default", false);
        mongoService.create(WAN_SPEED_TEST_INFO, wanSpeedTestDetails);
        return wanSpeedTestRequest;
    }

    public void editWanSpeedTest(WanSpeedTestRequest wanSpeedTestRequest, String id) {
        if (Objects.nonNull(id)) {
            checkAccessForGroupAdmin(id, "You are not authorized to edit this Speed Test URL");

        /*if (Objects.isNull(wanSpeedTestRequest.getUrls()) || wanSpeedTestRequest.getUrls().trim().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please Enter a Valid URL");*/

        /*if (Objects.nonNull(wanSpeedTestRequest.getUrls()) && !wanSpeedTestRequest.getUrls().isEmpty()) {
            if (!ValidationUtil.isValidURL(wanSpeedTestRequest.getUrls()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please enter a valid URL");
        }*/

            if (Objects.nonNull(wanSpeedTestRequest.getScheduledEvent()) && (wanSpeedTestRequest.getScheduledEvent() > 5000 || wanSpeedTestRequest.getScheduledEvent() < 1))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max Limit allowed is greater than or equals to 1 and less than or equal to 5000 (max >= 1 and max <= 5000) ");

            HashMap<String, Object> params = new HashMap<>();
            params.put("id", id);
            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("_id", ZERO);
            fieldsToRemove.put("_class", ZERO);
            fieldsToRemove.put("timestamp", ZERO);
            DBObject resp = mongoService.findOne(params, new HashMap<>(), WAN_SPEED_TEST_INFO, TIMESTAMP, DESC, fieldsToRemove);

            //ODI-2043
            /*if (Boolean.valueOf(resp.get("default").toString())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Default Speed Test URL cannot be updated");
            }*/

            DBObject query = new BasicDBObject();
            query.put("id", id);

            DBObject update = new BasicDBObject();
            update.put("name", wanSpeedTestRequest.getName());
            update.put("urls", (Objects.isNull(wanSpeedTestRequest.getUrls()) || wanSpeedTestRequest.getUrls().trim().isEmpty()) ? null : wanSpeedTestRequest.getUrls());
            update.put("scheduledEvent", wanSpeedTestRequest.getScheduledEvent());

            mongoService.update(query, new BasicDBObject().append("$set", update), true, false, WAN_SPEED_TEST_INFO);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Id :: " + id);
        }
    }

    public List<BasicDBObject> getWanSpeedTest(String id) {
        List<BasicDBObject> wanSpeedTestResponse = new ArrayList<>();
        List<BasicDBObject> wanSpeedTestResponseActual = new ArrayList<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);
        fieldsToRemove.put("_class", ZERO);
        fieldsToRemove.put("timestamp", ZERO);
        HashMap<String, Object> params = new HashMap<>();
        if (Objects.isNull(id)) {
            wanSpeedTestResponse = mongoService.findList(params, WAN_SPEED_TEST_INFO, fieldsToRemove);
        } else {
            //checkAccessForGroupAdmin(id, "You are not authorized to access this speed Test url");
            params.put("id", id);
            DBObject resp = mongoService.findOne(params, new HashMap<>(), WAN_SPEED_TEST_INFO, TIMESTAMP, DESC, fieldsToRemove);
            if (Objects.nonNull((resp))) {
                if (Objects.nonNull(resp.get("default")) && Boolean.valueOf(resp.get("default").toString()))
                    wanSpeedTestResponse.add((BasicDBObject) resp);
                else {
                    checkAccessForGroupAdmin(id, "You are not authorized to access this speed Test url");
                    wanSpeedTestResponse.add((BasicDBObject) resp);
                }
            }
        }
        if (CommonUtils.isSysAdmin()) {
            return wanSpeedTestResponse;
        }
        List<String> compartments = new ArrayList<>();
        compartments.add(ExecutionContext.get().getUsercontext().getGroupId());
        for (BasicDBObject dbObject : wanSpeedTestResponse) {
            if (dbObject.getBoolean("default")) {
                wanSpeedTestResponseActual.add(dbObject);
            } else {
                List<String> compartmentsFromDB = (List<String>) dbObject.get("compartments");
                if (compartmentsFromDB.containsAll(compartments)) {
                    wanSpeedTestResponseActual.add(dbObject);
                }
            }
        }
        // Finally show the Default Speed Test URL aslo for the Group Admin
        // wanSpeedTestResponseActual.addAll(wanSpeedTestResponse.stream().filter(p -> (p.getBoolean("default"))).collect(Collectors.toList()));
        return wanSpeedTestResponseActual;
    }

    public String getDefaultWanSpeedTest() {
        String speedTestUrl;
        List<BasicDBObject> wanSpeedTestResponse;
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);
        fieldsToRemove.put("_class", ZERO);
//        fieldsToRemove.put("default", true);
        fieldsToRemove.put("timestamp", ZERO);
        HashMap<String, Object> params = new HashMap<>();
        params.put("default", true);
        wanSpeedTestResponse = mongoService.findList(params, WAN_SPEED_TEST_INFO, fieldsToRemove);
        speedTestUrl = wanSpeedTestResponse.size() > 0 ? wanSpeedTestResponse.get(0).getString("urls") : null;
        return speedTestUrl;
    }

    public void deleteWanSpeedTest(String id) throws Exception {
        checkAccessForGroupAdmin(id, "You are not authorized to delete this Speed Test URL");
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", id);
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);
        fieldsToRemove.put("_class", ZERO);
        fieldsToRemove.put("timestamp", ZERO);
        DBObject resp = mongoService.findOne(params, new HashMap<>(), WAN_SPEED_TEST_INFO, TIMESTAMP, DESC, fieldsToRemove);

        if (Objects.isNull(resp))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Speed Test URL cannot be found with id :: " + id);
        if (Boolean.valueOf(resp.get("default").toString())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Default Speed Test URL cannot be deleted");
        }
        List<Job> jobList = (List<Job>) dataAccessService.read(Job.class);
        jobList = jobList.parallelStream().filter(element -> element.getTaskType().equalsIgnoreCase(String.valueOf(resp.get("urls")))).collect(Collectors.toList());
        if (!jobList.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Speed Test URL is associated with Schedule, hence cannot be deleted");

        mongoService.deleteOne(params, WAN_SPEED_TEST_INFO);
    }

    private void checkAccessForGroupAdmin(String id, String message) {
        if (Objects.nonNull(id)) {
            if (!CommonUtils.isSysAdmin()) {
                HashMap<String, Object> params = new HashMap<>();
                params.put("id", id);
                BasicDBObject fieldsToRemove = new BasicDBObject();
                fieldsToRemove.put("_id", ZERO);
                fieldsToRemove.put("_class", ZERO);
                fieldsToRemove.put("timestamp", ZERO);
                DBObject resp = mongoService.findOne(params, new HashMap<>(), WAN_SPEED_TEST_INFO, TIMESTAMP, DESC, fieldsToRemove);
                if (Objects.nonNull((resp.get("compartments")))) {
                    List<String> compartments = (List<String>) resp.get("compartments");
                    if (!compartments.contains(CommonUtils.getGroupIdOfLoggedInUser())) {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), message);
                    }
                    /*if (!CommonUtils.getCompartmentIdOfLoggedInUser().equals(resp.get("compartments"))) {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), message);
                    }*/
                }
            }
        }
    }
}
