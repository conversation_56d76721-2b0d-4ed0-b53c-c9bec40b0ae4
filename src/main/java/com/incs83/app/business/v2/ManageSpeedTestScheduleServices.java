package com.incs83.app.business.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.common.v2.ScheduleRequest;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.entities.Job;
import com.incs83.app.entities.JobDetail;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.SPEED_TEST_SAMPLE;
import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.constants.ApplicationCommonConstants.COMMON_CONFIG;

@Service
public class ManageSpeedTestScheduleServices {

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private CommonService commonService;

    private static final Logger LOG = LogManager.getLogger("org");

    public ScheduleRequest createSchedule(ScheduleRequest scheduleRequest) throws Exception {
        if (Objects.isNull(scheduleRequest.getSampleSize()) || scheduleRequest.getSampleSize().equals(""))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No of Sample cannot be empty");
        if (Integer.parseInt(scheduleRequest.getSampleSize()) == -1) {
            scheduleRequest.setSampleSize(Integer.valueOf(commonService.read(COMMON_CONFIG).get(SPEED_TEST_SAMPLE)) + "");
        }
        JobDetail jobDetail = new JobDetail();
        jobDetail.setId(CommonUtils.generateUUID());
        List<String> apIdsToPick = new ArrayList<>();
        List<String> associatedAps = new ArrayList<>();
        for (int i = 0; i < scheduleRequest.getClusterId().split(COMMA).length; i++) {
            String clusterId = scheduleRequest.getClusterId().split(COMMA)[i];
            if (Objects.isNull(clusterId) || !manageCommonService.isClusterExist(clusterId))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + clusterId);
            associatedAps.addAll(manageCommonService.getAssociatedApsByClusterId(clusterId));
        }
        Random r = new Random();
        if (Integer.parseInt(scheduleRequest.getSampleSize()) < 0) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some Error occured");
        } else {
            if (associatedAps.size() == 0) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The selected Cluster has no RGW so nothing to schedule");
            }
            if (Integer.valueOf(scheduleRequest.getSampleSize()) >= associatedAps.size()) {
                apIdsToPick = associatedAps;
            } else {
                for (int i = 0; i < Integer.parseInt(scheduleRequest.getSampleSize()); i++) {
                    apIdsToPick.add(associatedAps.get(r.nextInt(associatedAps.size() - 1)));
                }
            }
        }
        try {
            jobDetail.setJobMetaData(new ObjectMapper().writeValueAsString(apIdsToPick));
        } catch (JsonProcessingException e) {
            LOG.error("Error in set Job meta data", e);
        }
        jobDetail.setSampleSize(Integer.parseInt(scheduleRequest.getSampleSize()));
        Date runScheduled;
        if (scheduleRequest.getIsRandom()) {
            runScheduled = manageCommonService.calculateRandomTimeByDate(new Date());
        } else {
            runScheduled = new Date(scheduleRequest.getScheduleTime());
        }
        jobDetail.setRunDueOn(runScheduled);
        jobDetail.setStatus(ApplicationConstants.JOB_NOT_STARTED);
        jobDetail.setTaskType(scheduleRequest.getTaskType());
        CommonUtils.setCreateEntityFields(jobDetail);

        Job job = new Job();
        job.setClusterId(scheduleRequest.getClusterId());
        job.setSampleSize(scheduleRequest.getSampleSize());
        job.setTaskType(scheduleRequest.getTaskType());
        job.setId(CommonUtils.generateUUID());
        job.setJobName(scheduleRequest.getScheduleName());
        job.setRecurring(scheduleRequest.getIsRecurring());
        job.setDescription(scheduleRequest.getDesc());
        job.setScheduleTime(runScheduled);
        job.setRandom(scheduleRequest.getIsRandom());
        job.setGroupId(CommonUtils.getGroupIdOfLoggedInUser());
        job.setReportType(scheduleRequest.getReportType() == null ? ApplicationConstants.REPORT_TYPE_EXCEL : scheduleRequest.getReportType());
        jobDetail.setJob(job);
        CommonUtils.setCreateEntityFields(job);
        CommonUtils.setUpdateEntityFields(job);
        List<JobDetail> jobDetails = new ArrayList<>();
        jobDetails.add(jobDetail);
        job.setJobDetail(jobDetails);
        try {
            dataAccessService.create(Job.class, job);
        } catch (Exception e) {
            LOG.error("Error in Create job", e);
        }

        scheduleRequest.setId(job.getId());
        return scheduleRequest;
    }

    public void editSchedule(ScheduleRequest scheduleRequest, String id) throws Exception {
        if (Objects.isNull(scheduleRequest.getSampleSize()) || scheduleRequest.getSampleSize().equals(""))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No of Sample cannot be empty");
        Job job = (Job) dataAccessService.read(Job.class, id);

        if (!CommonUtils.isSysAdmin()) {
            if (!CommonUtils.getGroupIdOfLoggedInUser().equals(job.getGroupId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You are not authorized to edit this schedule");
            }
        }
        List<JobDetail> jdList = job.getJobDetail();
        JobDetail jobDetail = jdList.stream().filter(element -> element.getStatus().equals(ApplicationConstants.JOB_NOT_STARTED)).findAny().orElse(null);
        if (Objects.nonNull(jobDetail)) {
            jobDetail.setRunDueOn(new Date(scheduleRequest.getScheduleTime()));
        } else if (Objects.nonNull(jdList.stream().filter(element -> element.getStatus().equals(ApplicationConstants.JOB_STARTED)).findAny().orElse(null))) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "cannot be edited since schedule is already in progress");
        } else {
            jobDetail = new JobDetail();
            jobDetail.setId(CommonUtils.generateUUID());
            List<String> apIdsToPick = new ArrayList<>();
            List<String> associatedAps = new ArrayList<>();
            for (int i = 0; i < scheduleRequest.getClusterId().split(COMMA).length; i++) {
                String clusterId = scheduleRequest.getClusterId().split(COMMA)[i];
                if (Objects.isNull(clusterId) || !manageCommonService.isClusterExist(clusterId))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + clusterId);
                associatedAps.addAll(manageCommonService.getAssociatedApsByClusterId(clusterId));
            }
            Random r = new Random();
            if (Integer.parseInt(scheduleRequest.getSampleSize()) == -1) {
                apIdsToPick.addAll(associatedAps);
            } else {
                if (associatedAps.size() == 0) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The selected Cluster has no RGW so nothing to schedule");
                }
                if (Integer.valueOf(scheduleRequest.getSampleSize()) >= associatedAps.size()) {
                    apIdsToPick = associatedAps;
                } else {
                    for (int i = 0; i < Integer.parseInt(scheduleRequest.getSampleSize()); i++) {
                        apIdsToPick.add(associatedAps.get(r.nextInt(associatedAps.size() - 1)));
                    }
                }
            }
            try {
                jobDetail.setJobMetaData(new ObjectMapper().writeValueAsString(apIdsToPick));
            } catch (JsonProcessingException e) {
                LOG.error("Error during set job meta data", e);
            }
            jobDetail.setSampleSize(Integer.parseInt(scheduleRequest.getSampleSize()));
            Date runScheduled;
            if (scheduleRequest.getIsRandom()) {
                runScheduled = manageCommonService.calculateRandomTimeByDate(new Date());
            } else {
                runScheduled = new Date(scheduleRequest.getScheduleTime());
            }
            jobDetail.setRunDueOn(runScheduled);
            jobDetail.setStatus("NOT_STARTED");
            jobDetail.setTaskType(scheduleRequest.getTaskType());
            CommonUtils.setCreateEntityFields(jobDetail);
            jobDetail.setJob(job);
            jdList.add(jobDetail);
        }

        job.setJobDetail(jdList);
        job.setTaskType(scheduleRequest.getTaskType());
        job.setRecurring(scheduleRequest.getIsRecurring());
        job.setRandom(scheduleRequest.getIsRandom());
        job.setSampleSize(scheduleRequest.getSampleSize());
        job.setClusterId(scheduleRequest.getClusterId());
        job.setDescription(scheduleRequest.getDesc());
        job.setJobName(scheduleRequest.getScheduleName());
        job.setScheduleTime(new Date(scheduleRequest.getScheduleTime()));
        job.setReportType(scheduleRequest.getReportType());
        CommonUtils.setUpdateEntityFields(job);
        dataAccessService.update(Job.class, job);

    }

    public List<HashMap<String, Object>> getSchedules(String id) throws Exception {
        List<HashMap<String, Object>> response = new ArrayList<>();
        List<Job> jobs = new ArrayList<>();
        if (Objects.isNull(id)) {
            try {
                jobs = (List<Job>) dataAccessService.read(Job.class);
                if (!CommonUtils.isSysAdmin()) {
                    if (Objects.nonNull(jobs) && !jobs.isEmpty()) {
                        String groupId = CommonUtils.getGroupIdOfLoggedInUser();
                        jobs = jobs.stream().filter(job -> groupId.equals(job.getGroupId())).collect(Collectors.toList());
                    }
                }
            } catch (Exception e) {
                LOG.error("Error during fetching from DB", e);
            }
            jobs.forEach(job -> {
                HashMap<String, Object> respData = new HashMap<>();
                respData.put("desc", job.getDescription());
                respData.put("createdAt", Objects.nonNull(job.getUpdatedAt()) ? job.getUpdatedAt() : job.getCreatedAt());
                respData.put("jobName", job.getJobName());
                respData.put("recurring", job.getRecurring());
                respData.put("randomSchedule", job.getRandom());
                List<HashMap<String, Object>> jobDetails = new ArrayList<>();

                for (int i = 0; i < job.getJobDetail().size(); i++) {
                    HashMap<String, Object> data = new HashMap<>();
                    try {
                        data.put("apList", new ObjectMapper().readValue(job.getJobDetail().get(i).getJobMetaData(), List.class));
                    } catch (IOException e) {
                        LOG.error("Error adding value in apList", e);
                    }
                    data.put("dtlId", job.getJobDetail().get(i).getId());
                    data.put("status", job.getJobDetail().get(i).getStatus());
                    data.put("runsOn", job.getJobDetail().get(i).getRunDueOn());
                    data.put("downloadUrl", job.getJobDetail().get(i).getDownloadUrl());
                    try {
                        data.put("result", job.getJobDetail().get(i).getJobResultData() == null ? new HashMap<>() : new ObjectMapper().readValue(job.getJobDetail().get(i).getJobResultData(), HashMap.class));
                    } catch (IOException e) {
                        LOG.error("Error during adding value in result key", e);
                    }
                    jobDetails.add(data);
                }
                respData.put("sampleSize", job.getSampleSize());
                respData.put("clusterIds", job.getClusterId());
                respData.put("taskType", job.getTaskType());
                respData.put("details", jobDetails);
                respData.put("id", job.getId());
                respData.put("scheduledTime", job.getScheduleTime());
                respData.put("reportType", job.getReportType());
                response.add(respData);
            });
            response.sort((q1, q2) -> Long.compare((Long) q2.get("createdAt"), (Long) q1.get("createdAt")));

        } else {
            Job job = (Job) dataAccessService.read(Job.class, id);
            if (!CommonUtils.isSysAdmin()) {
                if (!CommonUtils.getGroupIdOfLoggedInUser().equals(job.getGroupId())) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You are not authorized to access this schedule");
                }
            }
            if (Objects.nonNull(job)) {
                HashMap<String, Object> respData = new HashMap<>();
                respData.put("desc", job.getDescription());
                respData.put("createdAt", Objects.nonNull(job.getUpdatedAt()) ? job.getUpdatedAt() : job.getCreatedAt());
                respData.put("jobName", job.getJobName());
                respData.put("recurring", job.getRecurring());
                respData.put("randomSchedule", job.getRandom());
                List<HashMap<String, Object>> jobDetails = new ArrayList<>();
                for (int i = 0; i < job.getJobDetail().size(); i++) {
                    HashMap<String, Object> data = new HashMap<>();
                    data.put("apList", new ObjectMapper().readValue(job.getJobDetail().get(i).getJobMetaData(), List.class));
                    data.put("dtlId", job.getJobDetail().get(i).getId());
                    data.put("status", job.getJobDetail().get(i).getStatus());
                    data.put("runsOn", job.getJobDetail().get(i).getRunDueOn());
                    data.put("downloadUrl", job.getJobDetail().get(i).getDownloadUrl());
                    try {
                        data.put("result", job.getJobDetail().get(i).getJobResultData() == null ? new HashMap<>() : new ObjectMapper().readValue(job.getJobDetail().get(i).getJobResultData(), HashMap.class));
                    } catch (IOException e) {
                        LOG.error("Error during adding value in result", e);
                    }
                    jobDetails.add(data);
                }
                Collections.sort(jobDetails, new Comparator<HashMap<String, Object>>() {
                    public int compare(HashMap<String, Object> one, HashMap<String, Object> two) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        Date date1 = new Date();
                        Date date2 = new Date();
                        try {
                            date1 = sdf.parse(String.valueOf(one.get("runsOn")));
                            date2 = sdf.parse(String.valueOf(two.get("runsOn")));

                        } catch (Exception e) {

                        }
                        return date1.compareTo(date2);
                    }
                });
                Collections.reverse(jobDetails);
                respData.put("sampleSize", job.getSampleSize());
                respData.put("clusterIds", job.getClusterId());
                respData.put("taskType", job.getTaskType());
                respData.put("details", jobDetails.subList(0, jobDetails.size() <= 10 ? jobDetails.size() : 10));
                respData.put("id", job.getId());
                respData.put("scheduledTime", job.getScheduleTime());
                respData.put("reportType", job.getReportType());
                response.add(respData);
            }
        }
        return response;
    }

    public void deleteSchedule(String id) throws Exception {
        if (Objects.nonNull(id)) {
            Job job = (Job) dataAccessService.read(Job.class, id);
            if (!CommonUtils.isSysAdmin()) {
                if (!CommonUtils.getGroupIdOfLoggedInUser().equals(job.getGroupId())) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You are not authorized to delete this schedule");
                }
            }
            dataAccessService.delete(Job.class, id);
        }
    }
}
