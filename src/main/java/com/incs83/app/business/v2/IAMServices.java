package com.incs83.app.business.v2;

import com.actiontec.optim.mongodb.dao.InternetServiceProviderDao;
import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.actiontec.optim.service.AuditService;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.Transactional;
import com.incs83.app.authResources.Common;
import com.incs83.app.common.v2.*;
import com.incs83.app.common.v3.UserCompositeSearchDTO;
import com.incs83.app.constants.queries.*;
import com.incs83.app.dto.RoleDetailDTO;
import com.incs83.app.dto.RolePermissionDTO;
import com.incs83.app.entities.*;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.UsersDTO.DataSecurityDTO;
import com.incs83.app.responsedto.v2.UsersDTO.UserListDTO;
import com.incs83.app.responsedto.v2.UsersDTO.UserResponseDTO;
import com.incs83.app.responsedto.v2.cds.*;
import com.incs83.app.responsedto.v2.compartment.GroupDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.isp.ISPModel;
import com.incs83.app.service.components.MailService;
import com.incs83.app.utils.CacheUtils;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.business.ESService;
import com.incs83.context.ExecutionContext;
import com.incs83.dto.ESRequest;
import com.incs83.dto.ElasticSearchDTO;
import com.incs83.dto.UserESDTO;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.request.CompartmentRequest;
import com.incs83.security.tokenFactory.Entitlement;
import com.incs83.security.tokenFactory.Permission;
import com.incs83.service.ESServiceImpl;
import com.incs83.service.MongoService;
import com.incs83.service.S3Service;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.incs83.util.DateUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.USER_PASSWORD_RESET_TOKEN;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.queries.CompartmentSQL.GET_SUBSCRIBER_COUNT_BY_COMPARTMENT_ID;
import static com.incs83.app.constants.queries.CompartmentSQL.GET_USER_COUNT_BY_COMPARTMENT_ID;
import static com.incs83.app.constants.queries.NavigationSQL.DELETE_ALL_ROLE_ENTITY_MAPPING;
import static com.incs83.app.constants.queries.NavigationSQL.DELETE_ROLE_MENU_MAPPING;
import static com.incs83.app.constants.queries.RolePermissionSQL.DELETE_ALL_ROLE_PERMISSION_MAPPING;
import static com.incs83.constants.ApplicationCommonConstants.USER_INDEX;

@Service
public class IAMServices {

    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    private DataAccessService dataAccessService;
    /*@Autowired
    private ManageClusterServices manageClusterServices;*/
    @Autowired
    private MongoService mongoService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private ISPService ispService;
    @Autowired
    private BCryptPasswordEncoder encoder;
    @Autowired
    private MailService mailService;
    @Autowired
    private HazelcastService cacheService;
    @Autowired
    private ESService esService;
    @Autowired
    private ESServiceImpl esServiceImpl;
    @Autowired
    private S3Service s3Service;
    @Autowired
    private At3Adapter at3Adapter;
    @Autowired
    private InternetServiceProviderDao internetServiceProviderDao;

    @Autowired
    private AuditService auditService;

    @Value("${s3.bucket}")
    private String s3Bucket;

    private ObjectMapper objectMapper = new ObjectMapper();

    {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public GroupDTO getAllCompartment() throws Exception {
        GroupDTO groupDTO = new GroupDTO();
        List<GroupDTO.CompartmentDetail> compartmentList = new ArrayList<>();
        if (cacheService.isMapExist(COMPARTMENT_LIST_MAP_NAME) && Objects.nonNull(cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME))) {
            List<HashMap<String, Object>> compartmentDataList = (List<HashMap<String, Object>>) cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME);
            if (!CommonUtils.isSysAdmin()) {
                String allIspId = internetServiceProviderDao.getIspIdByName(ApplicationConstants.ALL_ISP);
                String allGroupId = at3Adapter.getGroupIdByIspId(allIspId);
                String loggedInUserGroupId = CommonUtils.getGroupIdOfLoggedInUser();

                compartmentDataList = compartmentDataList.stream()
                        .filter(q -> {
                            Object idObj = q.get("id");
                            return allGroupId.equals(idObj) || loggedInUserGroupId.equals(String.valueOf(idObj));
                        })
                        .collect(Collectors.toList());
            }
            for (HashMap<String, Object> map : compartmentDataList) {
                GroupDTO.CompartmentDetail compartmentDetail = new GroupDTO.CompartmentDetail();
                compartmentDetail.setId(String.valueOf(map.get("id")));
                compartmentDetail.setName(String.valueOf(map.get("name")));
                compartmentDetail.setDescription(String.valueOf(map.get("description")));
                compartmentDetail.setIspName(String.valueOf(map.get("ispName")));
                compartmentDetail.setMqttConnection(MQTT_CONNECTION_NAME);
                compartmentDetail.setSubscriberOnly(Objects.nonNull(map.get("subscriberOnly")) ? Long.valueOf(map.get("subscriberOnly").toString()) : 0);
                compartmentDetail.setUserOnly(Objects.nonNull(map.get("userOnly")) ? Long.valueOf(map.get("userOnly").toString()) : 0);
                compartmentDetail.setTotalUsers(Objects.nonNull(map.get("totalUsers")) ? Long.valueOf(map.get("totalUsers").toString()) : 0);
                compartmentList.add(compartmentDetail);
            }
        } else {

            List<Compartment> compartment;
            HashMap<String, Object> params = new HashMap<>();
            if (CommonUtils.isSysAdmin()) {
                compartment = (List<Compartment>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_COMPARTMENT_FOR_SYS_ADMIN, params);

            } else {
                compartment = new ArrayList<>();

                params.put("id", CommonUtils.getGroupIdOfLoggedInUser());
                List<Compartment> compartment_1 = (List<Compartment>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_COMPARTMENT_FOR_GROUP_ADMIN, params);
                compartment.addAll(compartment_1);

                String allIspId = internetServiceProviderDao.getIspIdByName(ApplicationConstants.ALL_ISP);
                if(allIspId != null) {
                    String allGroupId = at3Adapter.getGroupIdByIspId(allIspId);

                    params.clear();
                    params.put("id", allGroupId);
                    List<Compartment> compartment_2 = (List<Compartment>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_COMPARTMENT_FOR_GROUP_ADMIN, params);
                    compartment.addAll(compartment_2);
                }
            }

            if (Objects.nonNull(compartment) && !compartment.isEmpty()) {
                ISPDTOList ispdtoList = ispService.getAllISPs();
                ArrayList<ISPModel> ispModels = new ArrayList<>();
                if (Objects.nonNull(ispdtoList) && !ispdtoList.getData().isEmpty()) {
                    ispModels = ispdtoList.getData();
                }

                final ArrayList<ISPModel> modelArrayList = ispModels;
                for (Compartment comp : compartment) {
                    GroupDTO.CompartmentDetail compartmentDetail = new GroupDTO.CompartmentDetail();
                    compartmentDetail.setId(comp.getId());
                    compartmentDetail.setName(comp.getName());
                    compartmentDetail.setDescription(comp.getDescription());

                    String ispName = EMPTY_STRING;
                    if (!modelArrayList.isEmpty()) {
                        ISPModel ispModelData = modelArrayList.stream().filter(ispModel -> ispModel.getId().equals(comp.getIspId())).findAny().orElse(null);
                        if (Objects.nonNull(ispModelData))
                            ispName = ispModelData.getDisplayName();
                    }

                    compartmentDetail.setIspName(ispName);
                    compartmentDetail.setMqttConnection(MQTT_CONNECTION_NAME);

                    HashMap<String, Object> param = new HashMap<>();

                    try {
                        param.put("compartment_id", comp.getId());

                        Long userCount = Long.valueOf(dataAccessService.readNative(GET_USER_COUNT_BY_COMPARTMENT_ID, param).iterator().next().toString());
                        Long subscriberCount = Long.valueOf(dataAccessService.readNative(GET_SUBSCRIBER_COUNT_BY_COMPARTMENT_ID, param).iterator().next().toString());
                        compartmentDetail.setSubscriberOnly(subscriberCount);
                        compartmentDetail.setUserOnly(userCount);
                        compartmentDetail.setTotalUsers(userCount + subscriberCount);

                    } catch (Exception e) {
                    }

                    compartmentList.add(compartmentDetail);
                }
            }
        }
        if (compartmentList.isEmpty()) {
            groupDTO.setData(new ArrayList<>());
        } else {
            groupDTO.setData(compartmentList);
        }
        return groupDTO;
    }

    public List<CompartmentModel> getCompartmentById(String id) throws Exception {
        List<CompartmentModel> compartments = new ArrayList<>();
        if (Objects.nonNull(id)) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, id);
            if (Objects.isNull(compartment)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group does not exist");
            } else {
                if (!CommonUtils.isSysAdmin()) {
                    if (!compartment.getId().equals(CommonUtils.getGroupIdOfLoggedInUser())) {
                        throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
                    }
                }

                CompartmentModel compartmentModel = new CompartmentModel();
                compartmentModel.setId(compartment.getId());
                compartmentModel.setName(compartment.getName());
                compartmentModel.setDescription(compartment.getDescription());
                compartmentModel.setProcessed(compartment.isProcessed());

                ISPDTO ispdto = ispService.getISPById(compartment.getIspId());

                compartmentModel.setIspName(Objects.nonNull(ispdto) ? ispdto.getData().getDisplayName() : compartment.getIspId());
                compartmentModel.setIspId(compartment.getIspId());
                compartmentModel.setMqttConnection(MQTT_CONNECTION_NAME);
                compartments.add(compartmentModel);
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group Id cannot be null");
        }

        return compartments;
    }

    public List<CompartmentModel> getGroupInfoByGroupId(String id) throws Exception {
        List<CompartmentModel> compartments = new ArrayList<>();
        if (Objects.nonNull(id)) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, id);
            if (Objects.isNull(compartment)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Compartment does not exist");
            } else {
                if (!CommonUtils.isSysAdmin()) {
                    if (!compartment.getId().equals(CommonUtils.getGroupIdOfLoggedInUser())) {
                        throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
                    }
                }

                CompartmentModel compartmentModel = new CompartmentModel();
                compartmentModel.setId(compartment.getId());
                compartmentModel.setName(compartment.getName());
                compartmentModel.setDescription(compartment.getDescription());
                compartmentModel.setProcessed(compartment.isProcessed());

                ISPDTO ispdto = ispService.getISPById(compartment.getIspId());

                compartmentModel.setIspName(Objects.nonNull(ispdto) ? ispdto.getData().getName() : compartment.getIspId());
                compartmentModel.setIspId(compartment.getIspId());
                compartmentModel.setMqttConnection(MQTT_CONNECTION_NAME);
                compartments.add(compartmentModel);
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group Id cannot be null");
        }

        return compartments;
    }

    public void updateCompartmentById(String id, CompartmentRequest compartmentRequest) throws Exception {
        if (Objects.nonNull(compartmentRequest.getDescription()) && compartmentRequest.getDescription().length() >= 255)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Description can be max 254 characters long");
        if (Objects.nonNull(compartmentRequest.getServiceProvider()) && compartmentRequest.getServiceProvider().length() >= 255)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Service Provider Name can be max 254 characters long");
        if (Objects.isNull(id)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group Id cannot be null");
        }

        HashMap<String, Object> response = updateGroup(id, compartmentRequest);
        Compartment compartment = (Compartment) response.get("compartment");
        Boolean isNameUpdated = Boolean.valueOf(response.get("isNameUpdated").toString());

        if (cacheService.isMapExist(COMPARTMENT_LIST_MAP_NAME) && Objects.nonNull(cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME))) {
            ISPDTO ispdto = ispService.getISPById(compartmentRequest.getServiceProvider());
            List<HashMap<String, Object>> compartmentDataList = (List<HashMap<String, Object>>) cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME);
            compartmentDataList = compartmentDataList.stream().filter(q -> !(String.valueOf(q.get("id")).equals(compartment.getId()))).collect(Collectors.toList());

            HashMap<String, Object> data = new HashMap<>();
            data.put("id", compartment.getId());
            data.put("name", compartment.getName());
            data.put("description", compartment.getDescription());
            data.put("ispId", compartment.getIspId());
            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(ele -> ele.isDefaultCluster()).findAny().orElse(null);
            data.put("defaultClusterId", Objects.nonNull(clusterInfo) ? clusterInfo.getId() : null);
            data.put("ispName", ispdto.getData().getName());
            data.put("mqttConnection", MQTT_CONNECTION_NAME);

            data.put("subscriberOnly", 0);
            data.put("totalUsers", 0);
            data.put("userOnly", 0);

            compartmentDataList.add(data);

            cacheService.create(COMPARTMENT_INFO, compartmentDataList, COMPARTMENT_LIST_MAP_NAME);
        }
        //UPDATE INTO ELASTIC-SEARCH
        if (isNameUpdated) {
            HashMap<String, Object> query = new HashMap<>();
            query.put("groupId", compartment.getId());

            HashMap<String, Object> data = new HashMap<>();
            data.put("groupName", compartmentRequest.getName());

            esService.updateByQuery(USER_INDEX, query, data, null, true);
            TimerTask timerTask = new TimerTask() {
                @Override
                public void run() {
                    esService.getTaskDetails();
                }
            };
            Timer timer = new Timer();
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            timer.schedule(timerTask, calendar.getTime(), TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS));
        }
    }

    @Transactional
    private HashMap<String, Object> updateGroup(String id, CompartmentRequest compartmentRequest) throws Exception {
        HashMap<String, Object> response = new HashMap<>();
        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, id);
        if (Objects.isNull(compartment))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Compartment does not exist with id " + id);

        if (Objects.nonNull(compartmentRequest.getName())) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("name", compartmentRequest.getName());
            List<Compartment> compartmentRecord = (List<Compartment>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_COMPARTMENT_BY_NAME, param);
            if (Objects.nonNull(compartmentRecord) && !compartmentRecord.isEmpty() && !compartmentRequest.getName().equalsIgnoreCase(compartment.getName()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group already exist with same name : " + compartmentRequest.getName());
        }

        String name = String.valueOf((compartment.getName()));
        if (DEFAULT_COMPARTMENT.equals(name) && !DEFAULT_COMPARTMENT.equals(compartment.getName()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Name of default Group cannot be updated");

        if (Objects.nonNull(compartmentRequest.getServiceProvider())) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("ispId", compartmentRequest.getServiceProvider());
            List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, param);
            if (Objects.nonNull(compartmentList) && !compartmentList.isEmpty() && !compartmentList.get(0).getId().equals(compartment.getId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP is already mapped with other group");
            }

            compartment.setIspId(compartmentRequest.getServiceProvider());
        }

        boolean isNameUpdated = false;
        if (!name.equals(compartmentRequest.getName())) {
            isNameUpdated = true;
            compartment.setName(compartmentRequest.getName());
        }
        if ((compartmentRequest.getDescription() != null)) {
            if (compartmentRequest.getDescription().length() < 255)
                compartment.setDescription(compartmentRequest.getDescription());
            else
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group description length cannot be more than 255 Character");
        }

        if (!String.valueOf(compartment.isProcessed()).equalsIgnoreCase(String.valueOf(compartmentRequest.isProcessed()))) {
            compartment.setProcessed(compartmentRequest.isProcessed());
        }

        CommonUtils.setUpdateEntityFields(compartment);
        dataAccessService.update(Compartment.class, compartment);

        if (isNameUpdated) {
            ClusterInfo cluster = compartment.getCluster().stream().filter(clusterInfo -> clusterInfo.isDefaultCluster()).findAny().orElse(null);
            if (Objects.nonNull(cluster)) {
                cluster.setName(DEFAULT_COMPARTMENT + "-" + compartmentRequest.getName());
                dataAccessService.update(ClusterInfo.class, cluster);
            }
        }

        response.put("compartment", compartment);
        response.put("isNameUpdated", isNameUpdated);

        return response;
    }

    public CompartmentModel createCompartment(CompartmentRequest compartmentRequest) throws Exception {
        if (Objects.nonNull(compartmentRequest.getDescription()) && compartmentRequest.getDescription().length() >= 255)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Description can be max 254 characters long");
        if (Objects.nonNull(compartmentRequest.getServiceProvider()) && compartmentRequest.getServiceProvider().length() >= 255)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Service Provider Name can be max 254 characters long");
        HashMap<String, Object> response = createGroup(compartmentRequest);
        CompartmentModel compartmentModel = (CompartmentModel) response.get("compartmentModel");
        Compartment compartment = (Compartment) response.get("compartment");
        ClusterInfo clusterInfo = (ClusterInfo) response.get("cluster");
        ISPDTO ispdto = (ISPDTO) response.get("ispDTO");

        if (cacheService.isMapExist(COMPARTMENT_LIST_MAP_NAME) && Objects.nonNull(cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME))) {

            HashMap<String, Object> data = new HashMap<>();
            data.put("id", compartment.getId());
            data.put("name", compartment.getName());
            data.put("description", compartment.getDescription());
            data.put("ispName", ispdto.getData().getName());
            data.put("ispId", compartment.getIspId());
            data.put("defaultClusterId", clusterInfo.getId());
            data.put("mqttConnection", MQTT_CONNECTION_NAME);

            data.put("subscriberOnly", 0);
            data.put("totalUsers", 0);
            data.put("userOnly", 0);

            List<HashMap<String, Object>> compartmentDataList = (List<HashMap<String, Object>>) cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME);
            compartmentDataList.add(data);

            cacheService.create(COMPARTMENT_INFO, compartmentDataList, COMPARTMENT_LIST_MAP_NAME);
        }


        return compartmentModel;
    }

    @Transactional
    private HashMap<String, Object> createGroup(CompartmentRequest compartmentRequest) throws Exception {
        HashMap<String, Object> response = new HashMap<>();
        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Not authorized to create Group.");
        }
        if (Objects.nonNull(compartmentRequest.getName()) && compartmentRequest.getName().startsWith(DEFAULT_COMPARTMENT))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group name cannot be started with DEFAULT");

        if (Objects.nonNull(compartmentRequest.getName())) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("name", compartmentRequest.getName());
            List<Compartment> compartmentRecord = (List<Compartment>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_COMPARTMENT_BY_NAME, param);
            if (Objects.nonNull(compartmentRecord) && !compartmentRecord.isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group already exist with same name : " + compartmentRequest.getName());
        }

        ISPDTO ispdto = ispService.getISPById(compartmentRequest.getServiceProvider());

        if (Objects.nonNull(compartmentRequest.getServiceProvider())) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("ispId", compartmentRequest.getServiceProvider());
            List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, param);
            if (Objects.nonNull(compartmentList) && !compartmentList.isEmpty()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP is already mapped with other group");
            }
        }

        String compartmentDesc = EMPTY_STRING;
        if ((compartmentRequest.getDescription() != null)) {
            if (compartmentRequest.getDescription().length() < 255)
                compartmentDesc = compartmentRequest.getDescription();
            else
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group description length cannot be more than 255 Character");
        }

        Compartment compartment = new Compartment();
        compartment.setId(CommonUtils.generateUUID());
        compartment.setName(compartmentRequest.getName());
        compartment.setDescription(compartmentDesc);
        compartment.setIspId(compartmentRequest.getServiceProvider());
        compartment.setProcessed(compartmentRequest.isProcessed());
        CommonUtils.setCreateEntityFields(compartment);

        ClusterInfo clusterInfo = new ClusterInfo();
        clusterInfo.setId(CommonUtils.generateUUID());
        clusterInfo.setName(DEFAULT + compartmentRequest.getName());
        clusterInfo.setDefaultCluster(true);
        clusterInfo.setDescription(compartmentDesc);
        CommonUtils.setCreateEntityFields(clusterInfo);

        Set<ClusterInfo> clusterInfoSet = new HashSet<>();
        clusterInfoSet.add(clusterInfo);
        compartment.setCluster(clusterInfoSet);

        dataAccessService.create(Compartment.class, compartment);

        CompartmentModel compartmentModel = new CompartmentModel();
        compartmentModel.setId(compartment.getId());
        compartmentModel.setName(compartment.getName());
        compartmentModel.setDescription(compartment.getDescription());
        compartmentModel.setIspId(compartmentRequest.getServiceProvider());
        compartmentModel.setMqttConnection(MQTT_CONNECTION_NAME);

        compartmentModel.setIspName(Objects.nonNull(ispdto) ? ispdto.getData().getDisplayName() : compartmentRequest.getServiceProvider());
        compartmentModel.setProcessed(compartment.isProcessed());

        response.put("compartmentModel", compartmentModel);
        response.put("compartment", compartment);
        response.put("cluster", clusterInfo);
        response.put("ispDTO", ispdto);
        return response;
    }

    public void deleteCompartmentById(String id) throws Exception {
        if (Objects.nonNull(id)) {
            deleteCompartment(id);

            if (cacheService.isMapExist(COMPARTMENT_LIST_MAP_NAME) && Objects.nonNull(cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME))) {
                List<HashMap<String, Object>> compartmentDataList = (List<HashMap<String, Object>>) cacheService.read(COMPARTMENT_INFO, COMPARTMENT_LIST_MAP_NAME);
                compartmentDataList = compartmentDataList.stream().filter(q -> !(String.valueOf(q.get("id")).equals(id))).collect(Collectors.toList());
                cacheService.create(COMPARTMENT_INFO, compartmentDataList, COMPARTMENT_LIST_MAP_NAME);
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group ID cannot be null");
        }
    }

    @Transactional
    public void deleteCompartment(String id) throws Exception {
        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, id);
        if (Objects.isNull(compartment)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group does not exist with id " + id);
        } else if (compartment.getName().startsWith(DEFAULT_COMPARTMENT)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Default Group cannot be deleted");
        } else {
            HashMap<String, Object> params = new HashMap<>();
            params.put("compartment", compartment);
            List totalUser = (List) dataAccessService.read(User.class, UserSQL.COUNT_OF_GET_PAGINATED_USER_BY_COMPARTMENT, params);
            if (Objects.nonNull(totalUser) && !totalUser.isEmpty() && Integer.valueOf(totalUser.get(0).toString()) > 0) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group cannot be deleted, Users associated to it");
            }

            params.clear();
            params.put("id",compartment.getId());
            Integer totalSubscribers = Integer.valueOf(dataAccessService.readNative(SubscriberSQL.GET_SUBSCRIBER_COUNT_BY_COMPARTMENT, params).iterator().next().toString());
            if (totalSubscribers > 0) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group cannot be deleted, Subscribers associated to it");
            }

            params.clear();
            params.put("id", id);
            dataAccessService.deleteUpdateNative(UserSQL.DELETE_USER_ROLE_BY_COMPARTMENT_ID, params);
            dataAccessService.deleteUpdateNative(UserSQL.DELETE_USER_COMPARTMENT_BY_COMPARTMENT_ID, params);

            compartment.getCluster().forEach(clusterInfo -> {
                try {
//                    manageClusterServices.deleteClusterInfo(clusterInfo.getId(), true);
                } catch (Exception e) {
                    if (e instanceof ValidationException)
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot delete Group if associated with running schedules on clusters!");
                }
            });
            dataAccessService.delete(Compartment.class, id);
        }
    }

    private void updateRolePermissions(String roleId, List<RolePermission> rolePermissions) {

        if (rolePermissions == null || rolePermissions.isEmpty()) { return; }

        Role role = (Role) dataAccessService.read(Role.class, roleId);
        if (role == null) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role Not Found");
        }

        List<Resource> allResources = (List<Resource>) dataAccessService.read(Resource.class);
        List<PermissionType> allPermissionTypes = (List<PermissionType>) dataAccessService.read(PermissionType.class);

        Map<String, Resource> resourceMap = allResources.stream()
                .collect(Collectors.toMap(
                        Resource::getName,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        Map<String, PermissionType> permissionTypeMap = allPermissionTypes.stream()
                .collect(Collectors.toMap(
                        PermissionType::getName,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));

        Set<String> requiredResources = rolePermissions.stream()
                .filter(rp -> !rp.getPermissions().isEmpty())
                .map(RolePermission::getResource)
                .collect(Collectors.toSet());

        Set<String> requiredPermissions = rolePermissions.stream()
                .flatMap(rp -> rp.getPermissions().stream())
                .collect(Collectors.toSet());

        List<String> missingResources = requiredResources.stream()
                .filter(resourceName -> !resourceMap.containsKey(resourceName))
                .collect(Collectors.toList());

        List<String> missingPermissions = requiredPermissions.stream()
                .filter(permissionName -> !permissionTypeMap.containsKey(permissionName))
                .collect(Collectors.toList());

        if (!missingResources.isEmpty() || !missingPermissions.isEmpty()) {
            String errorMessage = "";
            if (!missingResources.isEmpty()) {
                errorMessage += "Missing resources: " + missingResources;
            }
            if (!missingPermissions.isEmpty()) {
                if (!errorMessage.isEmpty()) errorMessage += "; ";
                errorMessage += "Missing permissions: " + missingPermissions;
            }
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), errorMessage);
        }

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("role_id", role.getId());
        dataAccessService.deleteUpdateNative(DELETE_ALL_ROLE_PERMISSION_MAPPING, queryParams);

        for (RolePermission rp : rolePermissions) {
            if (rp.getPermissions().isEmpty()) {
                continue;
            }

            Resource resource = resourceMap.get(rp.getResource());

            for (String permissionName : rp.getPermissions()) {
                PermissionType permissionType = permissionTypeMap.get(permissionName);

                RoleResourcePermission rrp = new RoleResourcePermission();
                rrp.setRole(role);
                rrp.setResource(resource);
                rrp.setPermissionType(permissionType);
                rrp.setCreatedAt(new Date());
                rrp.setCreatedBy(CommonUtils.getUserIdOfLoggedInUser());
                dataAccessService.create(RoleResourcePermission.class, rrp);
            }
        }
    }

    public Role createRole(RoleRequest roleRequest) throws Exception {
        if (Objects.nonNull(roleRequest.getName())) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("name", roleRequest.getName());
            List<Role> roleList = (List<Role>) dataAccessService.read(Role.class, RoleSQL.GET_ROLE_BY_NAME, param);
            if (Objects.nonNull(roleList) && !roleList.isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role already exist with same name : " + roleRequest.getName());
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role name cannot be empty");
        }

        if (!ValidationUtil.isNullOrEmpty(roleRequest.getName()) && (roleRequest.getName().equalsIgnoreCase(SYSTEM_ADMIN) || roleRequest.getName().equalsIgnoreCase(END_USER))) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "SYSTEM_ADMIN and USER are reserved Role cannot be created with this name");
        }
        Role role = new Role();
        role.setId(CommonUtils.generateUUID());
        role.setName(roleRequest.getName());
        if (Objects.nonNull(roleRequest.getDescription()) && roleRequest.getDescription().length() > 255) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role description length cannot be more than 255 Character");
        }
        role.setDescription(roleRequest.getDescription());
        role.setType("ENTERPRISE_ROLE");
        role.setMapping(createRoleEntitlementMapping(roleRequest.getRoleEntitlement()));
        Set<String> entities = new HashSet<>();
        entities.add(Common.class.getSimpleName());
        role.setEntity(entities);

        List<String> permission = new ArrayList<>();
        permission.add("ALL");

        RoleEntitlement roleEntitlement = new RoleEntitlement();
        roleEntitlement.setResource(Common.class.getSimpleName());
        roleEntitlement.setPermissions(permission);

        List<RoleEntitlement> roleEntitlementList = new ArrayList<>();
        roleEntitlementList.add(roleEntitlement);

        role.setMapping(createRoleEntitlementMapping(roleEntitlementList));
        role.setDataSecurityMapping(null);

        CommonUtils.setCreateEntityFields(role);

        dataAccessService.create(Role.class, role);
        updateRolePermissions(role.getId(), roleRequest.getRolePermissions());
        cacheService.delete(GROUP_ADMIN, ROLE);
        cacheService.delete(SYSTEM_ADMIN, ROLE);
        getAllRole();
        return role;

    }

    private String createRoleEntitlementMapping(List<RoleEntitlement> roleEntitlements) throws Exception {
        List<Entitlement> entitlements = new ArrayList<>();
        if (roleEntitlements != null) {
            roleEntitlements.forEach(roleEntitlement -> {
                entitlements.add(roleEntitlement.mapToEntitlement());
            });
        }
        return new ObjectMapper().writeValueAsString(entitlements);
    }

    public void updateRoleById(String id, RoleRequest roleRequest, boolean isMenuUpdateForUser) throws Exception {
        cacheService.delete(GROUP_ADMIN, ROLE);
        cacheService.delete(SYSTEM_ADMIN, ROLE);
        if (isMenuUpdateForUser) {
            cacheService.delete(id, NAVIGATION);
        }
        boolean flag = false;
        Role role = (Role) dataAccessService.read(Role.class, id);
        if (Objects.nonNull(role)) {
            boolean isRoleNameUpdated = false;
            if (Objects.nonNull(roleRequest.getName())) {
                HashMap<String, Object> param = new HashMap<>();
                param.put("name", roleRequest.getName());
                List<Role> roleList = (List<Role>) dataAccessService.read(Role.class, RoleSQL.GET_ROLE_BY_NAME, param);
                if (Objects.nonNull(roleList) && !roleList.isEmpty() && !roleRequest.getName().equalsIgnoreCase(role.getName()))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role already exist with same name : " + roleRequest.getName());
            }

            if (roleRequest.getName() != null && !roleRequest.getName().equals(role.getName())) {
                role.setName(roleRequest.getName());
                isRoleNameUpdated = true;
            }

            if (!ValidationUtil.isNullOrEmpty(roleRequest.getName()) && !role.getName().equalsIgnoreCase(roleRequest.getName()) && (roleRequest.getName().equalsIgnoreCase(SYSTEM_ADMIN) || roleRequest.getName().equalsIgnoreCase(END_USER))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "SYSTEM_ADMIN and USER are reserved Role cannot be created with this name");
            }


            if (roleRequest.getDescription() != null && !roleRequest.getDescription().equalsIgnoreCase(role.getDescription())) {
                role.setDescription(roleRequest.getDescription());
            }
            if (roleRequest.getRoleEntitlement() != null && !roleRequest.getRoleEntitlement().isEmpty()) {
                String roleMapping = role.getMapping();
                String requestRoleMapping = createRoleEntitlementMapping(roleRequest.getRoleEntitlement());
                if (Objects.nonNull(requestRoleMapping) && !requestRoleMapping.isEmpty() && !requestRoleMapping.equals(roleMapping)) {
                    flag = true;
                    role.setMapping(requestRoleMapping);
                }
            }

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("role_id", id);
            dataAccessService.deleteUpdateNative(DELETE_ALL_ROLE_ENTITY_MAPPING, queryParams);
            role.setEntity(roleRequest.getEntities());
            if (Objects.isNull(roleRequest.getEntities()) || roleRequest.getEntities().isEmpty())
                role.setMapping(null);

            CommonUtils.setUpdateEntityFields(role);
            dataAccessService.update(Role.class, role);
            updateRolePermissions(role.getId(), roleRequest.getRolePermissions());

            if (isRoleNameUpdated) {
                //UPDATE INTO ELASTIC-SEARCH
                HashMap<String, Object> query = new HashMap<>();
                query.put("roleId", role.getId());

                HashMap<String, Object> data = new HashMap<>();
                data.put("roleName", Objects.isNull(roleRequest.getName()) ? role.getName() : roleRequest.getName());
                esService.updateByQuery(USER_INDEX, query, data, null, true);
                TimerTask timerTask = new TimerTask() {
                    @Override
                    public void run() {
                        esService.getTaskDetails();
                    }
                };
                Timer timer = new Timer();
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                timer.schedule(timerTask, calendar.getTime(), TimeUnit.MILLISECONDS.convert(30, TimeUnit.SECONDS));

            }

            if (flag || isMenuUpdateForUser) {
                Runnable cleanUpToken = () -> {
                    try {
                        HashMap<String, Object> param = new HashMap<>();
                        param.put("roleId", role);
                        List<String> userIds = (List<String>) dataAccessService.readNative(UserSQL.GET_USER_ROLE_BY_ROLE, param);
                        for (String userId : userIds)
                            manageCommonService.deleteUserTokenFromHazelcast(userId);
                    } catch (Exception e) {
                        LOG.error("Error in processing update role", e);
                    }
                };
                new Thread(cleanUpToken).start();
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role not found with id : " + id);
        }
        getAllRole();
    }

    public void updateDataSecurityForRole(String id, DataSecurityRoleRequest roleRequest) throws Exception {
        cacheService.delete(CommonUtils.getGroupIdOfLoggedInUser(), ROLE);
        cacheService.delete(SYSTEM_ADMIN, ROLE);

        Role role = (Role) dataAccessService.read(Role.class, id);

        if (Objects.isNull(role))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role not found with id : " + id);

        if (Objects.nonNull(roleRequest.getDataSecurityEntitlement())) {
            List<String> actualRoleMapping = new ArrayList<>();
            if (Objects.nonNull(role.getDataSecurityMapping()) && !role.getDataSecurityMapping().isEmpty()) {
                actualRoleMapping = Arrays.asList(role.getDataSecurityMapping());
            }

            if ((actualRoleMapping.isEmpty() || roleRequest.getDataSecurityEntitlement().isEmpty()) || !roleRequest.getDataSecurityEntitlement().containsAll(actualRoleMapping)) {
                String dataCommaSeparated = roleRequest.getDataSecurityEntitlement().stream().map(DataSecurityType::name).collect(Collectors.joining(COMMA));
                if (EMPTY_STRING.equals(dataCommaSeparated))
                    dataCommaSeparated = null;
                role.setDataSecurityMapping(dataCommaSeparated);
                CommonUtils.setUpdateEntityFields(role);
                dataAccessService.update(Role.class, role);
            }
        }
        getAllRole();
    }

    private List<RolePermissionDTO> convertRolePermissions(Set<RoleResourcePermission> permissions) {
        List<Resource> allResources = (List<Resource>) dataAccessService.read(Resource.class);

        if (permissions == null || permissions.isEmpty()) {
            LOG.debug("Permissions is null or empty");
            return allResources.stream()
                    .map(res -> new RolePermissionDTO(res.getName(), Collections.emptyList()))
                    .collect(Collectors.toList());
        }

        LOG.debug("Permissions size = " + permissions.size());

        Map<String, Set<String>> grouped = permissions.stream()
                .filter(rrp -> rrp.getResource() != null && rrp.getPermissionType() != null)
                .collect(Collectors.groupingBy(
                        rrp -> rrp.getResource().getName(),
                        Collectors.mapping(
                                rrp -> rrp.getPermissionType().getName(),
                                Collectors.toCollection(LinkedHashSet::new) // preserves order, avoids duplicates
                        )
                ));

        return allResources.stream()
                .map(res -> new RolePermissionDTO(
                        res.getName(),
                        new ArrayList<>(grouped.getOrDefault(res.getName(), Collections.emptySet()))
                ))
                .collect(Collectors.toList());
    }

    public List<RoleDetailDTO> getAllRole() throws Exception {
        List<RoleDetailDTO> roleDetailDTOList = new ArrayList<>();
        HashMap<String, String> cacheParams = new HashMap<>();
        cacheParams.put(KEY, SYSTEM_ADMIN);
        cacheParams.put(MAP, ROLE);
        String roleSql = RoleSQL.GET_ROLES_FOR_SYS_ADMIN;
        if (!CommonUtils.isSysAdmin()) {
            roleSql = RoleSQL.GET_ROLES_FOR_GRP_ADMIN;
            cacheParams.put(KEY, GROUP_ADMIN);
        }
        List<Role> roles = (List<Role>) dataAccessService.read(Role.class, roleSql, new HashMap<>());
        Iterator rolesItr = roles.iterator();
        while (rolesItr.hasNext()) {
            Role role = (Role) rolesItr.next();
            setRoleMappingWithEntitlement(role);

            RoleDetailDTO roleDetailDTO = new RoleDetailDTO();
            roleDetailDTO.setId(role.getId());
            roleDetailDTO.setName(role.getName());
            roleDetailDTO.setRoleEntitlement(role.getRoleEntitlement());
            roleDetailDTO.setType(role.getType());
            roleDetailDTO.setEntity(role.getEntity());
            roleDetailDTO.setRolePermissions(convertRolePermissions(role.getRolePermissions()));

            roleDetailDTOList.add(roleDetailDTO);
        }

        return roleDetailDTOList;
    }

    private void setRoleMappingWithEntitlement(Role role) {
        List<Entitlement> entitlements = new ArrayList<>();
        if (role.getMapping() == null) {
            role.setRoleEntitlement(new ArrayList<>());
        } else {
            List<Entitlement> list = null;
            try {
                list = (List<Entitlement>) new ObjectMapper().readValue(role.getMapping(), List.class);
            } catch (IOException e) {
                LOG.error("Error during set role mapping entitlement", e);
            }
            for (int i = 0; i < list.size(); i++) {
                Entitlement entitlement = new ObjectMapper().convertValue(list.get(i), Entitlement.class);
                entitlements.add(entitlement);
            }
            role.setRoleEntitlement(entitlements);
        }
    }

    public List<RoleDetailDTO> getRoleById(String id) throws Exception {
        if (id == null) {
            return Collections.emptyList();
        }

        Role role = (Role) dataAccessService.read(Role.class, id);
        if (role == null) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role Not Found");
        }

        if ("SYSTEM_ADMIN".equals(role.getName()) && !CommonUtils.isSysAdmin()) {
            return Collections.emptyList();
        }

        setRoleMappingWithEntitlement(role);

        RoleDetailDTO dto = new RoleDetailDTO();
        dto.setId(role.getId());
        dto.setName(role.getName());
        dto.setRoleEntitlement(role.getRoleEntitlement());
        dto.setType(role.getType());
        dto.setEntity(role.getEntity());
        dto.setRolePermissions(convertRolePermissions(role.getRolePermissions()));

        return Collections.singletonList(dto);
    }

    public List getDataSecurityMappingForAllRoles() throws Exception {
        List<Role> roles;
        Role role;
        List<DataSecurityDTO> dataSecurityDTOList = new ArrayList<>();
        HashMap<String, String> cacheParams = new HashMap<>();
        cacheParams.put(KEY, SYSTEM_ADMIN);
        cacheParams.put(MAP, ROLE);
        String roleSql = RoleSQL.GET_ROLES_FOR_SYS_ADMIN;
        roles = (List<Role>) dataAccessService.read(cacheParams, Role.class, roleSql, new HashMap<>());
        Iterator rolesItr = roles.iterator();
        while (rolesItr.hasNext()) {
            role = (Role) rolesItr.next();

            DataSecurityDTO dataSecurityDTO = new DataSecurityDTO();
            dataSecurityDTO.setRoleId(role.getId());
            dataSecurityDTO.setRoleName(role.getName());
            String dataSecurityMapping = role.getDataSecurityMapping();
            List<String> dataSecurityTypeList = new ArrayList<>();
            List<HashMap<String, Object>> obscureFields = new ArrayList<HashMap<String, Object>>();
            if (Objects.nonNull(dataSecurityMapping)) {
                String[] data = dataSecurityMapping.split(",");
                for (int i = 0; i < data.length; i++) {
                    dataSecurityTypeList.add(data[i]);
                }
            }

            getAllDataSecurityTypes().forEach(dataType -> {
                HashMap<String, Object> response = new HashMap<>();
                if (dataSecurityTypeList.contains(dataType.name())) {
                    response.put("key", dataType.name());
                    response.put("value", true);
                    response.put("displayName", dataType.getValue());
                } else {
                    response.put("key", dataType.name());
                    response.put("value", false);
                    response.put("displayName", dataType.getValue());
                }
                obscureFields.add(response);
            });

            dataSecurityDTO.setObscureFields(obscureFields);

            dataSecurityDTOList.add(dataSecurityDTO);
        }
        return dataSecurityDTOList;
    }

    public EnumSet<DataSecurityType> getAllDataSecurityTypes() {

        EnumSet<DataSecurityType> dataSecurityTypes = EnumSet.allOf(DataSecurityType.class);
        return dataSecurityTypes;
    }

    public Set<String> getAllEntityForRole(String id) throws Exception {
        Set<String> entities = new HashSet<>();
        if (Objects.nonNull(id)) {
            Role r = (Role) dataAccessService.read(Role.class, id);
            if (Objects.isNull(r))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role Not Found for ID : " + id);

            entities = r.getEntity();
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role ID cannot be null");
        }

        return entities;
    }

    public void deleteRoleById(String id) throws Exception {
        deleteRole(id);
        getAllRole();
    }

    @Transactional
    public void deleteRole(String id) throws Exception {
        if (Objects.nonNull(id)) {
            Role role = (Role) dataAccessService.read(Role.class, id);
            if (Objects.isNull(role))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role does not exist with id " + id);

            if ((role.getName().equals(SYSTEM_ADMIN) || role.getName().equals(END_USER)))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "SYSTEM_ADMIN and USER Role cannot be deleted");

            HashMap<String, Object> queryParam = new HashMap<>();
            queryParam.put("role", role);
            List count = (List) this.dataAccessService.read(UserRole.class, "select count(ur) from UserRole ur WHERE role =:role", queryParam);
            if (Objects.nonNull(count) && !count.isEmpty()) {
                long userCount = ((Long) count.get(0)).longValue();
                if (userCount > 0L) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role cannot be deleted, User is associated with it.");
                }
                HashMap<String, Object> queryParams = new HashMap<>();
                queryParams.put("role_id", id);
                dataAccessService.deleteUpdateNative(DELETE_ALL_ROLE_ENTITY_MAPPING, queryParams);
                dataAccessService.deleteUpdateNative(DELETE_ROLE_MENU_MAPPING, queryParams);
                cacheService.delete(GROUP_ADMIN, ROLE);
                cacheService.delete(SYSTEM_ADMIN, ROLE);
                cacheService.delete(id, NAVIGATION);
                dataAccessService.delete(Role.class, id);
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role id cannot be null");
        }
    }

    public List<String> getPermissions() {
        return Arrays.stream(Permission.values()).map(Permission::getValue).collect(Collectors.toList());
    }

    public List<String> getEntitlement(String roleId) throws Exception {
        if (Objects.isNull(roleId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role Id cannot be null");
        Role role = (Role) dataAccessService.read(Role.class, roleId);
        if (Objects.isNull(role))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role cannot be found with id " + roleId);

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("roleId", roleId);
        List<String> mapping = dataAccessService.readNative(NavigationSQL.GET_ALL_ENTITY_FOR_ROLE, queryParams);
        if (Objects.isNull(mapping))
            new ArrayList<>();
        return mapping;
    }

    private void validateUserRequest(UserRequestDTO userRequest) throws Exception {
        if (Objects.isNull(userRequest.getRoleIds()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any role for users");

        if (Objects.isNull(userRequest.getCompartmentIds()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any compartment for users");

        if (userRequest.getRoleIds() != null) {
            Iterator<String> itr = userRequest.getRoleIds().iterator();
            while (itr.hasNext()) {
                String roleId = itr.next();
                Role role = (Role) dataAccessService.read(Role.class, roleId);
                if (Objects.isNull(role)) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role not found with id :: " + roleId);
                }
            }
        }

        if (userRequest.getCompartmentIds() != null) {
            Iterator<String> itr = userRequest.getCompartmentIds().iterator();
            while (itr.hasNext()) {
                String compartmentId = itr.next();
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, compartmentId);
                if (Objects.isNull(compartment))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Compartment not found with id :: " + compartmentId);
            }
        }
    }

    private void validateCreateUserRequest(CreateUserRequestDTO userRequest) throws Exception {

        if (Objects.isNull(userRequest.getRoleIds()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any role for users");

        if (Objects.isNull(userRequest.getCompartmentIds()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any compartment for users");

        if (userRequest.getRoleIds() != null) {
            Iterator<String> itr = userRequest.getRoleIds().iterator();
            while (itr.hasNext()) {
                String roleId = itr.next();
                Role role = (Role) dataAccessService.read(Role.class, roleId);
                if (Objects.isNull(role)) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Role not found with id :: " + roleId);
                }
            }
        }

        if (userRequest.getCompartmentIds() != null) {
            Iterator<String> itr = userRequest.getCompartmentIds().iterator();
            while (itr.hasNext()) {
                String compartmentId = itr.next();
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, compartmentId);
                if (Objects.isNull(compartment))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Compartment not found with id :: " + compartmentId);
            }
        }
    }

    public UserResponseDTO createUser(CreateUserRequestDTO userRequest) throws Exception {
        if (Objects.nonNull(userRequest.getRoleIds())) {
            userRequest.getRoleIds().removeAll(Collections.singleton(null));
        }

        if (Objects.nonNull(userRequest.getCompartmentIds())) {
            userRequest.getCompartmentIds().removeAll(Collections.singleton(null));
        }

        if (Objects.nonNull(userRequest.getRoleIds()) && !userRequest.getRoleIds().isEmpty()) {
            if (userRequest.getRoleIds().size() > 1)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User cannot have multiple role");

            if (Objects.nonNull(userRequest.getCompartmentIds()) && !userRequest.getCompartmentIds().isEmpty()) {

                userRequest.getCompartmentIds().removeAll(Collections.singleton(null));

                if (userRequest.getCompartmentIds().size() > 1)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User cannot be assigned to multiple compartments");

                validateCreateUserRequest(userRequest);

                if (!CommonUtils.isSysAdmin()) {
                    if (!userRequest.getCompartmentIds().iterator().next().equals(CommonUtils.getGroupIdOfLoggedInUser())) {
                        throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
                    }
                }

                HashMap<String, Object> queryParam = new HashMap<>();
                queryParam.put("email", userRequest.getEmail());
                List<User> users = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_EMAIL, queryParam);

                if (!users.isEmpty()) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The Email Id you have entered is already registered");
                }
                User u = new User();
                u.setId(CommonUtils.generateUUID());
                Set<UserRole> roles = new HashSet<>();
                if (userRequest.getRoleIds() != null) {
                    Iterator<String> itr = userRequest.getRoleIds().iterator();
                    while (itr.hasNext()) {
                        Role role = (Role) dataAccessService.read(Role.class, itr.next());
                        if ("GROUP_ADMIN".equals(role.getName())) {
                            if (userRequest.getCompartmentIds() != null) {
                                Iterator<String> compartmentItr = userRequest.getCompartmentIds().iterator();
                                while (compartmentItr.hasNext()) {
                                    Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, compartmentItr.next());
                                    if ("DEFAULT".equals(compartment.getName())) {
                                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group Admin cannot be assigned to Default Group");
                                    }
                                }
                            }
                        }
                        UserRole userRole = new UserRole();
                        userRole.setId(CommonUtils.generateUUID());
                        userRole.setRole(role);
                        CommonUtils.setCreateEntityFields(userRole);
                        userRole.setGrantBy(UserRole.RoleType.DIRECT.name());
                        roles.add(userRole);
                    }
                }
                if (!roles.isEmpty()) {
                    u.setUserRole(roles);
                }

                Set<Compartment> compartments = new HashSet<>();
                if (userRequest.getCompartmentIds() != null) {
                    Iterator<String> itr = userRequest.getCompartmentIds().iterator();
                    while (itr.hasNext()) {
                        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, itr.next());
                        compartments.add(compartment);
                    }
                }
                if (!compartments.isEmpty()) {
                    u.setCompartment(compartments);
                }

                String password = CommonUtils.generateRandomPassword();
                u.setPassword(encoder.encode(password));
                CreateUserRequestDTO.generateFrom(userRequest, u);
                CommonUtils.setCreateEntityFields(u);

                u.setInternalUser(true);
                u.setTitle(INTERNAL_USER);
                u.setVerify((Objects.isNull(userRequest.getVerify()) || !userRequest.getVerify()) ? false : true);
                u.setSkipEmailVerification(u.isVerify());
                if (Objects.isNull(userRequest.getVerify()) || !userRequest.getVerify())
                    sendAutoGeneratedPassword(u.getEmail(), password, u.getFirstName() + SPACE + u.getLastName());
                HashMap<String, Object> userMap = esServiceImpl.prepareDataForES(u, false);

                try {
                    persistUser(u);
                    esService.insert(USER_INDEX, userMap, u.getId(), null);
                    return UserResponseDTO.mapFromUser(u);
                } catch (Exception e) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Failed to update User details!");
                }
            } else {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any compartment for users");
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any role for users");
        }
    }

    @Transactional
    private void persistUser(User user) throws Exception {
        dataAccessService.create(User.class, user);

        String query = SubscriberSQL.COUNT_DUPLICATE_LOGIN_DETAIL_BY_EMAIL;
        HashMap<String, String> qParams = new HashMap<>();
        qParams.put("email", user.getEmail());
        List<Object> count = (List<Object>) dataAccessService.readNative(query, qParams);
        if (Integer.valueOf(count.get(0).toString()) == 0) {
            LoginDetails loginDetails = new LoginDetails();
            loginDetails.setId(user.getId());
            loginDetails.setEmail(user.getEmail());
            loginDetails.setSubscriber(false);
            loginDetails.setActive(true);
            loginDetails.setToken(null);
            loginDetails.setTokenCreatedAt(null);
            loginDetails.setTokenUpdatedAt(null);
            loginDetails.setTokenType(INTERNAL_USER);
            CommonUtils.setCreateEntityFields(loginDetails);
            dataAccessService.create(LoginDetails.class, loginDetails);
        }
    }

    private void sendAutoGeneratedPassword(String email, String password, String name) throws Exception {
        try {
            HashMap<String, String> valuesMap = new HashMap<>();
            valuesMap.put("PASSWORD", password);
            valuesMap.put("USER", name);
            valuesMap.put("LINK", EMPTY_STRING);
            mailService.sendAutoGeneratedPassword(email, valuesMap);
        } catch (Exception e) {
            LOG.error("Error while sending email :: " + e.getMessage());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid email / Error while sending email.");
        }
    }

    public HashMap getUsersCountStats() throws Exception {
        HashMap<String, Integer> data = new HashMap<>();

        HashMap<String, Object> param = new HashMap<>();
        int totalUser = 0;
        int totalSubscriber = 0;
        if (CommonUtils.isSysAdmin()) {
            totalUser = Integer.valueOf(dataAccessService.read(User.class, UserSQL.COUNT_OF_GET_PAGINATED_USER, param).iterator().next().toString());
            totalSubscriber = Integer.valueOf(dataAccessService.readNative(SubscriberSQL.GET_SUBSCRIBER_COUNT, param).iterator().next().toString());

            data.put("totalUser", totalUser);
            data.put("subscriberOnly", totalSubscriber);
            data.put("userOnly", totalUser - totalSubscriber);
        } else {
            param.put("id", CommonUtils.getGroupIdOfLoggedInUser());
            totalUser = Integer.valueOf(dataAccessService.readNative(UserSQL.COUNT_USER_BY_COMPARTMENT_ID, param).iterator().next().toString());
            totalSubscriber = Integer.valueOf(dataAccessService.readNative(SubscriberSQL.GET_SUBSCRIBER_COUNT_BY_COMPARTMENT, param).iterator().next().toString());

            data.put("totalUser", totalUser);
            data.put("subscriberOnly", totalSubscriber);
            data.put("userOnly", totalUser - totalSubscriber);
        }
        return data;
    }

    public void deleteUserById(String id) throws Exception {
        manageCommonService.deleteUserTokenFromHazelcast(id);
        esService.delete(USER_INDEX, id);
        deleteUser(id);
    }

    @Transactional
    private void deleteUser(String id) throws Exception {
        if ( ADMIN_USER_ID.equals(id) ||  NO_REPLY_USER.equals(id)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Admin and Reply User cannot be deleted.");
        }
        User user = (User) dataAccessService.read(User.class, id);
        if (Objects.isNull(user))
            return;
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User Not found with id :: " + id);

        if (!CommonUtils.isSysAdmin()) {
            String compartmentId = user.getCompartment().iterator().next().getId();
            if (!CommonUtils.getGroupIdOfLoggedInUser().equals(compartmentId)) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }


        String userId = ExecutionContext.get().getUsercontext().getId();
        if (id.equals(userId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You are not authorized to delete yourself");

        String query;
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", id);
        query = UserSQL.DELETE_USER_COMPARTMENT_BY_USER_ID;
        dataAccessService.deleteUpdateNative(query, params);
        query = UserSQL.DELETE_USER_ROLE_BY_ID;
        dataAccessService.deleteUpdateNative(query, params);
        dataAccessService.delete(User.class, id);
        dataAccessService.delete(LoginDetails.class, id);
    }

    public UserDetailDTO getUserById(String id) throws Exception {
        UserDetailDTO userDetailDTO = new UserDetailDTO();
        UserDetailDTO.UserDetail userDetail = userDetailDTO.new UserDetail();
        if (Objects.nonNull(id)) {
            User user = (User) dataAccessService.read(User.class, id);
            if (Objects.isNull(user))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User Not found with id :: " + id);

            if (!CommonUtils.isSysAdmin()) {
                if (!CommonUtils.getGroupIdOfLoggedInUser().equals(user.getCompartment().iterator().next().getId())) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You are not authorized to access this Resource.");
                }
            }

            userDetail.setId(user.getId());
            userDetail.setFirstName(user.getFirstName());
            userDetail.setLastName(user.getLastName());
            userDetail.setEmail(user.getEmail());
            userDetail.setVerify(user.isVerify());
            userDetail.setSkipEmailVerification(user.isSkipEmailVerification());
            userDetail.setRoleId(user.getUserRole().iterator().next().getRole().getId());
            userDetail.setRoleName(user.getUserRole().iterator().next().getRole().getName());
            userDetail.setCompartmentId(user.getCompartment().iterator().next().getId());
            userDetailDTO.setData(userDetail);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User ID cannot be null");
        }
        return userDetailDTO;
    }

    private void updateUserCluster(String clusterName, String userId, boolean isRemove) throws Exception {
        HashMap<String, Object> param = new HashMap<>();
        param.put("name", clusterName);


        List<ClusterInfo> clusterInfoList = (List<ClusterInfo>) dataAccessService.read(ClusterInfo.class, ClusterInfoSQL.GET_CLUSTER_INFO_BY_NAME, param);

        if (!clusterInfoList.isEmpty() && clusterInfoList.size() == 1) {
            ClusterInfo clusterInfo = clusterInfoList.get(0);
            HashMap<String, Object> queryParams = new HashMap<>();
            if (isRemove) {
                queryParams.put("clusterId", clusterInfo.getId());
                queryParams.put("userId", userId);
                dataAccessService.deleteUpdateNative(ClusterInfoSQL.DELETE_USER_AP_FROM_CLUSTER, queryParams);
            } else {
                queryParams.put("clusterId", clusterInfo.getId());
                queryParams.put("userId", userId);
                dataAccessService.deleteUpdateNative(ClusterInfoSQL.MAPPING_OF_CLUSTER_AND_AP, queryParams);
            }
        }
    }

    public void updateUserById(String id, UserRequestDTO userRequest) throws Exception {
        try {
            HashMap<String, Object> response = updateUserDetailsById(id, userRequest);
            if (Objects.nonNull(response)) {
                User user = (User) response.get("user");
                if (Objects.nonNull(user)) {
                    esService.updateById(USER_INDEX, esServiceImpl.prepareDataForES(user, false), user.getId(), null);
                }
            }
        } catch (ValidationException vex) {
            throw vex;
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Failed to update User details!");
        }
    }

    @Transactional
    private HashMap<String, Object> updateUserDetailsById(String id, UserRequestDTO userRequest) throws Exception {
        if ( ADMIN_USER_ID.equals(id) ||  NO_REPLY_USER.equals(id)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " Admin and Reply User cannot be updated.");
        }
        if (Objects.nonNull(userRequest.getRoleIds())) {
            userRequest.getRoleIds().removeAll(Collections.singleton(null));
        }

        if (Objects.nonNull(userRequest.getCompartmentIds())) {
            userRequest.getCompartmentIds().removeAll(Collections.singleton(null));
        }

        if (Objects.isNull(userRequest.getRoleIds()) || userRequest.getRoleIds().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any role to user");
        }

        if (Objects.isNull(userRequest.getCompartmentIds()) || userRequest.getCompartmentIds().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please assign any groups to user");
        }

        if (userRequest.getRoleIds().size() > 1)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User cannot have multiple role");

        if (Objects.nonNull(userRequest.getPassword()) && !userRequest.getPassword().equals(EMPTY_STRING)) {
            if(!Pattern.matches("((?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%?=*&]).{10,64})",String.valueOf(userRequest.getPassword())))
                throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "Password, Invalid characters Found. Rule, At least one lowercase, At least one uppercase, At least one digit, At least one special character, At least it should have 10-64 characters long.");
            if (CommonUtils.isEndUser())
                throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "You don't have password updation permission for any user");
            if (userRequest.getPassword().length() < 10 || userRequest.getPassword().length() > 64)
                throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "Password length must be between 10 and 64 characters");
        }

        validateUserRequest(userRequest);

        User user = (User) dataAccessService.read(User.class, id);
        if (Objects.isNull(user))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User does not exists");

        if (Objects.nonNull(userRequest.getPassword()) && !userRequest.getPassword().trim().equals(EMPTY_STRING) && !user.isInternalUser())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You cannot change Password for External User");

        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("email", userRequest.getEmail());
        List<User> users = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_EMAIL, queryParam);

        if (!users.isEmpty() && !users.get(0).getId().equals(user.getId())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "The Email Id you have entered is already registered");
        }
        String oldPassword = Objects.isNull(user.getPassword()) ? "" : user.getPassword();
        String newPassword = Objects.isNull(userRequest.getPassword()) ? "" : userRequest.getPassword();
        if(!oldPassword.equals(newPassword)){
            user.setPasswordAttempt("0");
            user.setPasswordUpdatedAt(DateUtils.getCurrentDate());
        }
        boolean isDeleteToken = false;
        boolean isUserCompartmentUpdated = false;
        boolean isUserRoleUpdated = false;
        UserRequestDTO.generateFrom(userRequest, user);
        CommonUtils.setUpdateEntityFields(user);

        Set<Compartment> compartments = user.getCompartment();
        if (Objects.nonNull(compartments) && !compartments.isEmpty()) {
            String compartmentId = String.valueOf(compartments.iterator().next().getId());
            if (!compartmentId.equals(userRequest.getCompartmentIds().get(0))) {
                Set<Compartment> compartmentDetail = new HashSet<>();
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, userRequest.getCompartmentIds().get(0));
                compartmentDetail.add(compartment);
                user.setCompartment(compartmentDetail);

                isUserCompartmentUpdated = true;

                isDeleteToken = true;
            }
        }

        //Cannot be enable disable user for verification
        /*if (Objects.nonNull(userRequest.getVerify()) && (!user.isVerify() && userRequest.getVerify()))
            user.setVerify(userRequest.getVerify());*/

        if (Objects.nonNull(userRequest.getPassword()) && !userRequest.getPassword().equals(EMPTY_STRING)) {
            if (userRequest.getPassword().length() < 10 || userRequest.getPassword().length() > 64)
                throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "Password length must be between 10 and 64 characters");
            else {
                user.setPassword(encoder.encode(userRequest.getPassword()));
                if (!user.isSkipEmailVerification()) {
                    HashMap<String, String> values = new HashMap<>();
                    values.put("username", user.getFirstName() + SPACE + user.getLastName());
                    mailService.sendPasswordChangedNotificationEmailByAdmin(user.getEmail(), values);
                }

                isDeleteToken = true;
            }
        }
        if (Objects.nonNull(userRequest.getRoleIds()) && !userRequest.getRoleIds().get(0).equals(user.getUserRole().iterator().next().getRole().getId())) {
            isUserRoleUpdated = true;
            isDeleteToken = true;
        }

        HashMap<String, Object> response = new HashMap<>();

        try {

            if (isUserRoleUpdated) {
                Role role = (Role) dataAccessService.read(Role.class, userRequest.getRoleIds().get(0));
                Set<UserRole> userRoles = user.getUserRole();
                UserRole userRole = userRoles.iterator().next();
                userRole.setRole(role);
                userRoles.add(userRole);
                user.setUserRole(userRoles);
            }

            dataAccessService.update(User.class, user);


            if (isUserCompartmentUpdated) {
                HashMap<String, String> param = new HashMap<>();
                param.put("user_id", id);
                param.put("compartment_id", userRequest.getCompartmentIds().get(0));
                dataAccessService.deleteUpdateNative(UserSQL.UPDATE_USER_COMPARTMENT, param);
            }

            response.put("user", user);
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Failed to update User details!");
        }

        if (isDeleteToken)
            manageCommonService.deleteUserTokenFromHazelcast(id);

        return response;
    }

    private Object getUsersFromElasticSearch(Integer max, Integer offset, String order, String sortBy) {
        SortOrder sortOrder = order.equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
        ESRequest elasticSearchRequest = new ESRequest();
        elasticSearchRequest.setMax(max).setOffset(offset).setOrder(sortOrder).setSortBy(sortBy).setIndex(USER_INDEX);
        if (!CommonUtils.isSysAdmin()) {
            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
            elasticSearchRequest.setQueryParams(queryParams);
        }
        ElasticSearchDTO elasticSearchDTO = esService.getPaginatedData(elasticSearchRequest);
        if (Objects.nonNull(elasticSearchDTO)) {
            if (elasticSearchDTO.getTotalCount() == 0) {
                UserListDTO userListDTO = new UserListDTO();
                ArrayList<UserListDTO.UserData.UserModel> userListDataArrayList = new ArrayList<>();
                UserListDTO.UserData.UserModel user = userListDTO.new UserData().new UserModel();
                userListDataArrayList.add(user);
                return userListDTO.getData();
            } else {
                return elasticSearchDTO;
            }

        } else {
            return null;
        }
    }

    public void changePassword(ChangePasswordRequest changePasswordRequest) throws Exception {
        String userId = CommonUtils.getUserIdOfLoggedInUser();
        User userFromSql = (User) dataAccessService.read(User.class, userId);
        if (Objects.nonNull(userFromSql)) {
            boolean flag = encoder.matches(changePasswordRequest.getOldPassword(), userFromSql.getPassword());
            if (flag) {
                if(!Pattern.matches("((?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%?=*&]).{10,64})",String.valueOf(changePasswordRequest.getNewPassword())))
                    throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "The password must be between 10 and 64 characters in length, and it must include at least one uppercase letter, one lowercase letter, one number, and one special character.");

                if (changePasswordRequest.getNewPassword().length() < 10 || changePasswordRequest.getNewPassword().length() > 64)
                    throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "The password length must be between 10 and 64 characters");

                userFromSql.setPassword(encoder.encode(changePasswordRequest.getNewPassword()));
                userFromSql.setVerify(true);
                dataAccessService.update(User.class, userFromSql);

                HashMap<String, String> valuesMap = new HashMap<>();
                valuesMap.put("username", userFromSql.getFirstName() + SPACE + userFromSql.getLastName());
                valuesMap.put("LINK", changePasswordRequest.getPortalUrl());
                mailService.sendPasswordChangedNotificationEmail(userFromSql.getEmail(), valuesMap);

                manageCommonService.deleteUserTokenFromHazelcast(userId);
            } else {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Old password does not match");
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User Not Found");
        }
    }

    public void resetPassword(PasswordChangeRequest passwordChangeRequest) throws Exception {
        HashMap<String, Object> query = new HashMap<>();
        query.put("tokenId", passwordChangeRequest.getTokenId());

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("id", 0);

        DBObject dbObject = mongoService.findOne(query, appendableParams, USER_PASSWORD_RESET_TOKEN, TIMESTAMP, DESC, fieldsToRemove);
        if (Objects.nonNull(dbObject) && Objects.nonNull(dbObject.get("expiry")) && Objects.nonNull(dbObject.get("token"))) {
            String dbToken = dbObject.get("token").toString();
            if (!dbToken.equals(passwordChangeRequest.getToken()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Token");
            checkTokenValidity(dbObject);
            String userId = String.valueOf(dbObject.get("userId"));
            User userFromSql = (User) dataAccessService.read(User.class, userId);
            if (Objects.nonNull(userFromSql)) {
                userFromSql.setPassword(encoder.encode(passwordChangeRequest.getNewPassword()));
                userFromSql.setVerify(true);
                dataAccessService.update(User.class, userFromSql);
            } else {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User Not Found");
            }

            //Expire the token after password reset
            long expiry = Calendar.getInstance().getTimeInMillis() - 60 * 60 * 1000;
            DBObject where = new BasicDBObject();
            where.put("tokenId", passwordChangeRequest.getTokenId());
            DBObject update = new BasicDBObject().append("$set", new BasicDBObject().append("token", dbToken).append("expiry", expiry));
            mongoService.update(where, update, true, false, USER_PASSWORD_RESET_TOKEN);

            String user_id = String.valueOf(dbObject.get("userId"));
            manageCommonService.deleteUserTokenFromHazelcast(user_id);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Request");
        }
    }

    public void sendEmailToResetPassword(ResetPasswordRequest resetPasswordRequest, HttpServletRequest httpServletRequest) throws Exception {
        if (Objects.nonNull(resetPasswordRequest) && Objects.nonNull(resetPasswordRequest.getUsername()) && Objects.nonNull(resetPasswordRequest.getBaseUrl())) {
            HashMap<String, Object> query = new HashMap<>();
            query.put("email", resetPasswordRequest.getUsername().trim());

            List<User> user = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_EMAIL, query);
            if (Objects.isNull(user) || user.isEmpty())
                return;
//                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This email is not registered with us :: " + resetPasswordRequest.getUsername());

            if (!user.get(0).isInternalUser())
                return;
//                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You cannot reset Password from Optim portal");

            String userId = user.get(0).getId();
            String token = CommonUtils.generateUUID() + CommonUtils.generateUUID() + CommonUtils.generateUUID();
            Calendar calendar = Calendar.getInstance();
            long expiry = calendar.getTimeInMillis() + 15 * 60 * 1000;

            query.clear();
            query.put("userId", userId);

            HashMap<String, Object> appendableParams = new HashMap<>();
            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("id", 0);

            DBObject dbObject = mongoService.findOne(query, appendableParams, USER_PASSWORD_RESET_TOKEN, TIMESTAMP, DESC, fieldsToRemove);
            String tokenId = CommonUtils.generateUUID();
            if (Objects.isNull(dbObject)) {

                HashMap<String, Object> item = new HashMap<>();
                item.put("userId", userId);
                item.put("token", token);
                item.put("expiry", expiry);
                item.put("tokenId", tokenId);

                mongoService.create(USER_PASSWORD_RESET_TOKEN, item);
            } else {
                DBObject where = new BasicDBObject();
                where.put("userId", userId);

                DBObject update = new BasicDBObject().append("$set", new BasicDBObject().append("token", token).append("expiry", expiry).append("tokenId", tokenId));

                mongoService.update(where, update, true, true, USER_PASSWORD_RESET_TOKEN);
            }

            String resetPasswordUrl = resetPasswordRequest.getBaseUrl() + (resetPasswordRequest.getBaseUrl().endsWith("/") ? "" : "/") + "reset/password?id=" + tokenId + "&token=" + token;

            HashMap<String, String> valuesMap = new HashMap<>();
            valuesMap.put("username", user.get(0).getFirstName() + SPACE + user.get(0).getLastName());
            valuesMap.put("url", resetPasswordUrl);
            mailService.sendPasswordResetLinkEmail(resetPasswordRequest.getUsername(), valuesMap);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Request");
        }
    }

    public void isTokenValid(String tokenId, String tokenRec) throws Exception {
        HashMap<String, Object> query = new HashMap<>();
        query.put("tokenId", tokenId);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("id", 0);
        DBObject dbObject = mongoService.findOne(query, appendableParams, USER_PASSWORD_RESET_TOKEN, TIMESTAMP, DESC, fieldsToRemove);
        if (Objects.nonNull(dbObject) && Objects.nonNull(dbObject.get("expiry")) && Objects.nonNull(dbObject.get("token"))) {
            String dbToken = dbObject.get("token").toString();
            if (!dbToken.equals(tokenRec))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Token");
            checkTokenValidity(dbObject);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Request");
        }
    }

    private void checkTokenValidity(DBObject dbObject) {
        long expiryTime = Long.valueOf(dbObject.get("expiry").toString());
        Calendar calendar = Calendar.getInstance();
        long now = calendar.getTimeInMillis();
        if (expiryTime < now)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Token Expired, Try again to Reset Password");
    }

    private boolean isIllegalCharacterExist(UserCompositeSearchDTO userCompositeSearchDTO) {
        if (Objects.nonNull(userCompositeSearchDTO.getName()) && !ValidationUtil.isValidSearchingCharacter(userCompositeSearchDTO.getName()))
            return true;
        else if (Objects.nonNull(userCompositeSearchDTO.getGroupName()) && !ValidationUtil.isValidSearchingCharacter(userCompositeSearchDTO.getGroupName()))
            return true;
        else if (Objects.nonNull(userCompositeSearchDTO.getRoleName()) && !ValidationUtil.isValidSearchingCharacter(userCompositeSearchDTO.getRoleName()))
            return true;
        else if (Objects.nonNull(userCompositeSearchDTO.getEmail()) && !ValidationUtil.isValidSearchingCharacter(userCompositeSearchDTO.getEmail()))
            return true;
        else
            return false;
    }

    private boolean isSearchEnabled(UserCompositeSearchDTO userCompositeSearchDTO) {
        return Objects.nonNull(userCompositeSearchDTO.getName()) || Objects.nonNull(userCompositeSearchDTO.getEmail()) || Objects.nonNull(userCompositeSearchDTO.getGroupName()) || Objects.nonNull(userCompositeSearchDTO.getRoleName()) || Objects.nonNull(userCompositeSearchDTO.isInternalUser());
    }

    public Object getUsersList(UserCompositeSearchDTO userCompositeSearchDTO, Boolean exact) throws Exception {
        if (userCompositeSearchDTO.getMax() > 500)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max cannot be greater than 500");

        if (userCompositeSearchDTO.getOrder().name().equals("ASC") || userCompositeSearchDTO.getOrder().name().equals("DESC")) {
            if (userCompositeSearchDTO.getOffset() < 0)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Offset cannot be negative");

            List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

            ArrayList<UserListDTO.UserData.UserModel> userListDataArrayList = new ArrayList<>();
            UserListDTO userListDTO = new UserListDTO();
            UserListDTO.UserData userData = userListDTO.new UserData();

            if (isIllegalCharacterExist(userCompositeSearchDTO)) {
                UserListDTO.UserData.UserModel user = userData.new UserModel();
                userListDataArrayList.add(user);

                userData.setObjects(userListDataArrayList);
                userData.setTotalCount(0L);

                return userData;
            }

            List<Object[]> users = new ArrayList<>();
            HashMap<String, Object> queryParam = new HashMap<>();
            String queries = EMPTY_STRING;

            if (isSearchEnabled(userCompositeSearchDTO)) {

                SortOrder sortOrder = userCompositeSearchDTO.getOrder().equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
                ESRequest elasticSearchRequest = new ESRequest();
                elasticSearchRequest.setMax(userCompositeSearchDTO.getMax()).setOffset(userCompositeSearchDTO.getOffset()).setSortBy(userCompositeSearchDTO.getSortBy()).setOrder(sortOrder).setIndex(USER_INDEX);

                HashMap<String, Object> esQueryParams = new HashMap<>();
                esQueryParams.put("name", userCompositeSearchDTO.getName());
                esQueryParams.put("email", userCompositeSearchDTO.getEmail());
                esQueryParams.put("groupName", userCompositeSearchDTO.getGroupName());
                esQueryParams.put("roleName", userCompositeSearchDTO.getRoleName());


                if (!CommonUtils.isSysAdmin()) {
                    esQueryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
                }
                elasticSearchRequest.setQueryParams(esQueryParams);
                ElasticSearchDTO response = esService.searchWithFilter(elasticSearchRequest, "internalUser", userCompositeSearchDTO.isInternalUser(), exact);
                if (Objects.nonNull(response)) {
                    if (response.getObjects().size() > 0) {
                        response.getObjects().forEach(u -> {
                            UserESDTO userESDTO = objectMapper.convertValue(u, UserESDTO.class);
                            UserListDTO.UserData.UserModel user = userData.new UserModel();
                            user.setEmail(userESDTO.getEmail());
                            user.setGroupName(userESDTO.getGroupName());
                            user.setId(userESDTO.getId());
                            user.setRoleName(userESDTO.getRoleName());
                            user.setSubscriber(userESDTO.isSubscriber());
                            user.setName(userESDTO.getName());
                            user.setInternalUser(userESDTO.isInternalUser());
                            userListDataArrayList.add(user);
                        });
                        userData.setObjects(userListDataArrayList);
                        userData.setTotalCount(Long.valueOf(response.getTotalCount()));
                    } else {
                        UserListDTO.UserData.UserModel user = userData.new UserModel();
                        user.setEmail(null);
                        user.setGroupName(null);
                        user.setId(null);
                        user.setRoleName(null);
                        user.setSubscriber(false);
                        user.setName(null);
                        user.setInternalUser(false);
                        userListDataArrayList.add(user);
                        userData.setObjects(userListDataArrayList);
                        userData.setTotalCount(Long.valueOf(0));
                    }
                    return userData;
                }

                ArrayList<String> queryParams = new ArrayList<>();
                StringBuffer sb = new StringBuffer("");
                if (Objects.nonNull(userCompositeSearchDTO.getName())) {
                    queryParams.add("CONCAT( u.firstName, ' ', u.lastName ) LIKE '%" + userCompositeSearchDTO.getName() + "%'");
                }

                if (Objects.nonNull(userCompositeSearchDTO.getEmail())) {
                    queryParams.add("u.email LIKE '%" + userCompositeSearchDTO.getEmail() + "%'");
                }

                if (Objects.nonNull(userCompositeSearchDTO.getGroupName())) {
                    queryParams.add("LOWER(c.name) LIKE '%" + userCompositeSearchDTO.getGroupName().toLowerCase() + "%'");
                }

                if (Objects.nonNull(userCompositeSearchDTO.getRoleName())) {
                    queryParams.add("LOWER(r.name) LIKE '%" + userCompositeSearchDTO.getRoleName().toLowerCase() + "%'");
                }

                if (Objects.nonNull(userCompositeSearchDTO.isInternalUser())) {
                    queryParams.add("internalUser = " + userCompositeSearchDTO.isInternalUser());
                }

                if (!queryParams.isEmpty()) {
                    if (!CommonUtils.isSysAdmin()) {
                        sb.append("where uc.compartment_id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "' and ");
                    } else {
                        sb.append("Where ");
                    }

                    int i = 0;
                    for (String condition : queryParams) {
                        if (i > 0)
                            sb.append(" and ");
                        sb.append(condition);
                        i++;
                    }
                }
                queries = String.format(UserSQL.GET_COUNT_OF_SEARCH_USERS, EMPTY_STRING, sb.toString());
                sb.append(/*" Order By " + orderBy + */" LIMIT " + (Objects.nonNull(userCompositeSearchDTO.getOffset()) ? userCompositeSearchDTO.getOffset() : 0) + ", " + (Objects.nonNull(userCompositeSearchDTO.getMax()) ? userCompositeSearchDTO.getMax() : 10));

                users = dataAccessService.readNative(String.format(UserSQL.GET_USERS, EMPTY_STRING, sb), new HashMap<>());
            } else {
                Object usersList = getUsersFromElasticSearch(userCompositeSearchDTO.getMax(), userCompositeSearchDTO.getOffset(), userCompositeSearchDTO.getOrder().name(), userCompositeSearchDTO.getSortBy());
                if (Objects.nonNull(usersList)) return usersList;

                String replaceWith = /*"Order By " + sortBy + " " + order + */" LIMIT " + (Objects.nonNull(userCompositeSearchDTO.getOffset()) ? userCompositeSearchDTO.getOffset() : 0) + ", " + (Objects.nonNull(userCompositeSearchDTO.getMax()) ? userCompositeSearchDTO.getMax() : 10);
                String query = String.format(UserSQL.GET_USERS, ((!CommonUtils.isSysAdmin()) ? ("where uc.compartment_id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : EMPTY_STRING), replaceWith);
                users = dataAccessService.readNative(query, new HashMap<>());

                queries = UserSQL.COUNT_TOTAL_USERS;
                if (!CommonUtils.isSysAdmin()) {
                    queries = queries.replaceAll("###", "u inner join user_compartment uc on (u.id=uc.user_id) where uc.compartment_id='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
                } else {
                    queries = queries.replaceAll("###", EMPTY_STRING);
                }
            }

            if (Objects.nonNull(users) && !users.isEmpty()) {
                for (Object userList[] : users) {
                    UserListDTO.UserData.UserModel user = userData.new UserModel();
                    user.setName(String.valueOf(userList[0]) + SPACE + String.valueOf(userList[1]));
                    user.setEmail(String.valueOf(userList[2]));
                    user.setGroupName(String.valueOf(userList[3]));
                    user.setRoleName(String.valueOf(userList[4]));
                    user.setId(String.valueOf(userList[5]));
                    boolean isSubscriber = false;
                    if (Objects.nonNull(userList[6]) && Integer.valueOf(userList[6].toString()) == 1) {
                        isSubscriber = true;
                    }
                    if (Objects.nonNull(userList[8]) && Integer.valueOf(userList[8].toString()) == 1) {
                        user.setInternalUser(true);
                    }
                    user.setSubscriber(isSubscriber);
                    userListDataArrayList.add(user);

                }
            }

            if (userListDataArrayList.isEmpty()) {
                UserListDTO.UserData.UserModel user = userData.new UserModel();
                userListDataArrayList.add(user);
            }


            List<Object> count = (List<Object>) dataAccessService.readNative(queries, queryParam);

            userData.setObjects(userListDataArrayList);
            userData.setTotalCount(Long.valueOf(Long.valueOf(Objects.nonNull(count) ? count.get(0).toString() : "0")));
            userListDTO.setData(userData);

            return userListDTO.getData();
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Allowed values for order - ASC,DESC");
        }
    }

    private boolean isValidImageContentType(String contentType) {
        return contentType != null && contentType.equalsIgnoreCase("image/png");
//                contentType.equalsIgnoreCase("image/png") ||
//                        contentType.equalsIgnoreCase("image/jpeg") ||
//                        contentType.equalsIgnoreCase("image/jpg") ||
//                        contentType.equalsIgnoreCase("image/gif") ||
//                        contentType.equalsIgnoreCase("image/svg+xml")
//        );
    }

    public String uploadLogo(MultipartFile logo, String type) {
        if (!(DASHBOARD_LOGO.equals(type) || SPLASH_LOGO.equals(type))) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid logo type.");
        }

        if (logo == null || logo.isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Logo file is empty.");
        }

        String contentType = logo.getContentType();
        if (!isValidImageContentType(contentType)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Only image file PNG allowed.");
        }

        try {
            HashMap<String, String> url = s3Service.uploadFile(logo, LOGO_INITIAL_PATH + type);
            String logoUrl = url.get("secure_url");
            CacheUtils.logoNearCache.put(type, logoUrl);
            return logoUrl;
        } catch (Exception e) {
            LOG.error("Error while uploading logo :: ", e);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error while uploading logo");
        }
    }
}
