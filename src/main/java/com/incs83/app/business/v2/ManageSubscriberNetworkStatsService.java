package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.common.v2.AgentVersion;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.entities.Equipment;
import com.incs83.app.responsedto.v2.Subscriber.*;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.CalendarUtils;
import com.incs83.app.utils.EquipmentUtils;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class ManageSubscriberNetworkStatsService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private ManageSpeedTestUrlServices manageSpeedTestUrlServices;

    @Autowired
    private RPCUtilityService rpcUtilityService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SimpleRpcService cpeRpcService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private EquipmentUtils equipmentUtils;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

    private List<Map<String, Object>> getApWifiInsightPerMinuteDataForUser(Equipment userEquipment) throws Exception {
        List<Map<String, Object>> actualData = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        BasicDBObject sort = new BasicDBObject();
        sort.put("dateHour", DESC);

        String serial = manageCommonService.getGatewaySerialByUserId(userEquipment.getRgwSerial()).orElseGet(()->userEquipment.getRgwSerial());
        params.put("userId", userEquipment.getRgwSerial());
        params.put("serialNumber", serial);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        DBObject wifiInsightPerHour = mongoService.findOne(params, AP_WIFI_INSIGHTS_PER_MINUTE, sort, mongoFieldOptions);
        if (Objects.nonNull(wifiInsightPerHour)) {
            if (Objects.nonNull(wifiInsightPerHour.get("hourlyData"))) {
                Map<String, Object> dataByDay = (Map<String, Object>) wifiInsightPerHour.get("hourlyData");
                String maxDate = Collections.max(dataByDay.keySet());
                List<Map<String, Object>> apWifiInsightData = (List<Map<String, Object>>) dataByDay.get(maxDate);
                if (Objects.nonNull(apWifiInsightData) && !apWifiInsightData.isEmpty()) {
                    apWifiInsightData.sort((q1, q2) -> Long.compare((Long) q2.get("timestamp"), (Long) q1.get("timestamp")));
                    actualData.add(apWifiInsightData.get(0));
                }
            }
        }

        return actualData;
    }

    /*public WifiMeterDataDTO getWifiHealthScore(String serialNumberOrSubscriberId) throws Exception {
        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId))
            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
        UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberId);
        manageCommonService.subscriberDataExistInMongo(userAP);
        manageCommonService.subscriberGroupMisMatch(userAP);

        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userAP.getApId());

        BasicDBObject grouping = new BasicDBObject();
        grouping.put("userId", "$userId");
        grouping.put("timestamp", "$timestamp");


        BasicDBObject groupParam = new BasicDBObject();
        groupParam.put("_id", grouping);
        groupParam.put("avg", new BasicDBObject("$avg", "$score"));

        BasicDBObject match = new BasicDBObject();
        match.put("$match", matchParam);

        BasicDBObject group = new BasicDBObject();
        group.put("$group", groupParam);

        BasicDBObject sort = new BasicDBObject();
        sort.put("$sort", new BasicDBObject("timestamp", DESC));

        BasicDBObject limit = new BasicDBObject();
        limit.put("$limit", 1);

        List<BasicDBObject> pipeline = new ArrayList<>();
        pipeline.add(match);
        pipeline.add(group);
        pipeline.add(sort);
        pipeline.add(limit);

        List<DBObject> dbObjectList = mongoService.aggregationPipeline(pipeline, AP_WIFI_INSIGHTS_PER_MINUTE);

        WifiMeterDataDTO wifiMeterDataDTO = new WifiMeterDataDTO();
        double data = 0.0;
        try {
            if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty())
                data = (Objects.nonNull(dbObjectList.get(0)) && Objects.nonNull(dbObjectList.get(0).get("avg"))) ? Double.valueOf(TWO_DECIMAL_PLACE.format(dbObjectList.get(0).get("avg"))) : 0;
        } catch (Exception e) {
            LOG.error("NumberFormatException occured for getWifiHealthScore");
        }


        wifiMeterDataDTO.setData(data);
        return wifiMeterDataDTO;
    }*/

    public WifiMeterDataDTO getWifiHealthScore(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<Map<String, Object>> actualData = getApWifiInsightPerMinuteDataForUser(userEquipment);

        WifiMeterDataDTO wifiMeterDataDTO = new WifiMeterDataDTO();
        double data = 0.0;
        try {
            if (!actualData.isEmpty())
                data = actualData.stream().collect(Collectors.averagingDouble(p -> p.get("wifiHealthScore") == null ? 0.0 : Double.valueOf(String.valueOf(p.get("wifiHealthScore")))));
        } catch (Exception e) {
            LOG.error("NumberFormatException occured for getWifiHealthScore");
        }

        wifiMeterDataDTO.setData(data);
        return wifiMeterDataDTO;
    }

    public WifiMeterDataDTO getAvgWifiDownlinkRate(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<Map<String, Object>> actualData = getApWifiInsightPerMinuteDataForUser(userEquipment);

        WifiMeterDataDTO wifiMeterDataDTO = new WifiMeterDataDTO();
        double data = 0.0;
        double lastDataDownlinkRate = 0.0;
        long staCount = 0;
        try {
            if (!actualData.isEmpty()) {
                lastDataDownlinkRate = actualData.stream().collect(Collectors.averagingDouble(p -> p.get("avgDataDownlinkRate") == null ? 0.0 : Double.valueOf(String.valueOf(p.get("avgDataDownlinkRate")))));
                staCount = actualData.stream().collect(Collectors.summingLong(p -> (Objects.isNull(p.get("staCount")) ? 0 : Long.valueOf(String.valueOf(p.get("staCount"))))));

                if (lastDataDownlinkRate == 0.0 || staCount == 0)
                    data = 0.0;
                else {
                    data = Double.valueOf(TWO_DECIMAL_PLACE.format((lastDataDownlinkRate / 1000F)));
                }
            }
        } catch (Exception e) {
            LOG.error("NumberFormatException occured for getAvgWifiDownlinkRate");
        }

        wifiMeterDataDTO.setData(data);
        return wifiMeterDataDTO;
    }

    /*public WifiMeterDataDTO getSubscriberAvgUplinkRate(String serialNumberOrSubscriberId) throws Exception {
        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId))
            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
        UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberId);
        manageCommonService.subscriberDataExistInMongo(userAP);
        manageCommonService.subscriberGroupMisMatch(userAP);

        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userAP.getApId());

        BasicDBObject grouping = new BasicDBObject();
        grouping.put("userId", "$userId");
        grouping.put("timestamp", "$timestamp");


        BasicDBObject groupParam = new BasicDBObject();
        groupParam.put("_id", grouping);
        groupParam.put("lastDataUplinkRate", new BasicDBObject("$sum", "$lastDataUplinkRate"));
        groupParam.put("staCount", new BasicDBObject("$sum", "staCount"));

        BasicDBObject match = new BasicDBObject();
        match.put("$match", matchParam);

        BasicDBObject group = new BasicDBObject();
        group.put("$group", groupParam);

        BasicDBObject sort = new BasicDBObject();
        sort.put("$sort", new BasicDBObject("timestamp", DESC));

        BasicDBObject limit = new BasicDBObject();
        limit.put("$limit", 1);

        List<BasicDBObject> pipeline = new ArrayList<>();
        pipeline.add(match);
        pipeline.add(group);
        pipeline.add(sort);
        pipeline.add(limit);

        List<DBObject> dbObjectList = mongoService.aggregationPipeline(pipeline, AP_WIFI_INSIGHTS_PER_MINUTE);

        WifiMeterDataDTO wifiMeterDataDTO = new WifiMeterDataDTO();
        double data = 0.0;
        try {
            if (Objects.isNull(dbObjectList) || dbObjectList.isEmpty() || Objects.isNull(dbObjectList.get(0).get("lastDataUplinkRate")) || Double.valueOf(dbObjectList.get(0).get("lastDataUplinkRate").toString()) == 0 || Objects.isNull(dbObjectList.get(0).get("staCount")) || Long.valueOf(dbObjectList.get(0).get("staCount").toString()) == 0)
                data = 0;
            else {
                Double lastDataUplinkRate = Double.valueOf(dbObjectList.get(0).get("lastDataUplinkRate").toString());
                Long staCount = Long.valueOf(dbObjectList.get(0).get("staCount").toString());
                data = Double.valueOf(TWO_DECIMAL_PLACE.format((lastDataUplinkRate / 1000F / staCount)));
            }
        } catch (Exception e) {
            LOG.error("NumberFormatException occured for getAvgWifiDownlinkRate");
        }


        wifiMeterDataDTO.setData(data);
        return wifiMeterDataDTO;
    }*/

    public WifiMeterDataDTO getSubscriberAvgUplinkRate(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<Map<String, Object>> actualData = getApWifiInsightPerMinuteDataForUser(userEquipment);

        WifiMeterDataDTO wifiMeterDataDTO = new WifiMeterDataDTO();
        double data = 0.0;
        double lastDataUplinkRate = 0.0;
        long staCount = 0;
        try {

            if (!actualData.isEmpty()) {
                lastDataUplinkRate = actualData.stream().collect(Collectors.averagingDouble(p -> p.get("avgDataUplinkRate") == null ? 0.0 : Double.valueOf(String.valueOf(p.get("avgDataUplinkRate")))));
                staCount = actualData.stream().collect(Collectors.summingLong(p -> (Objects.isNull(p.get("staCount")) ? 0 : Long.valueOf(String.valueOf(p.get("staCount"))))));

                if (lastDataUplinkRate == 0.0 || staCount == 0)
                    data = 0.0;
                else {
                    data = Double.valueOf(TWO_DECIMAL_PLACE.format((lastDataUplinkRate / 1000F)));
                }
            }
        } catch (Exception e) {
            LOG.error("NumberFormatException occured for getAvgWifiDownlinkRate");
        }

        wifiMeterDataDTO.setData(data);
        return wifiMeterDataDTO;
    }

    /*public double getAvgInterNetUtilizationForDuration(long minutes, UserAP userAP) {
        double internetUtilization = 0.0;
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        HashMap<String, Object> params = new HashMap<>();
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();
        long dateHour = nowMillSec / 1000 - 60 * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);
        params.put("userId", userAP.getApId());
        params.put("dateHour", dateCriteria);
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        List<DBObject> internetUtilizationTableData = (List<DBObject>) mongoService.findList(params, new HashMap<>(), INTERNET_UTILIZATION_SLIDING_DATA, mongoFieldOptions);
        List<Map<String, Object>> actualSlidingData = new ArrayList<>();
        if (Objects.nonNull(internetUtilizationTableData) && !internetUtilizationTableData.isEmpty()) {
            for (DBObject dbObject : internetUtilizationTableData) {
                List<Map<String, Object>> slidingData = (List<Map<String, Object>>) dbObject.get("slidingData");
                if (Objects.nonNull(slidingData) && !slidingData.isEmpty()) {
                    Calendar criteriaCalendar = Calendar.getInstance();
                    criteriaCalendar.setTime(new Date(new Date().getTime() - (60 * 60L * 1000L)));
                    Long currTimeStamp = criteriaCalendar.getTimeInMillis();
                    slidingData = slidingData.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0.0) > currTimeStamp).collect(Collectors.toList());
                    actualSlidingData.addAll(slidingData);
                }
            }
        }
        if (!actualSlidingData.isEmpty()) {
            actualSlidingData.sort((q1, q2) -> Long.compare((Long) q2.get("timestamp"), (Long) q1.get("timestamp")));
            actualSlidingData = actualSlidingData.size() < minutes ? actualSlidingData.subList(0, actualSlidingData.size()) : actualSlidingData.subList(0, (int) minutes);
            //TODO will be removed latter due to Agent side Bug
            actualSlidingData = actualSlidingData.stream().filter(q -> Objects.nonNull(q.get("downloadSpeed")) ? (Double.valueOf(q.get("downloadSpeed").toString()) <= (50000000)) : true).collect(Collectors.toList());
            DoubleSummaryStatistics doubleSummaryStatistics = actualSlidingData.stream().mapToDouble(q -> (Objects.nonNull(q.get("downloadSpeed")) ? ((Double) q.get("downloadSpeed")) : 0.0)).summaryStatistics();
            internetUtilization = doubleSummaryStatistics.getAverage();
//            internetUtilization = actualSlidingData.stream().collect(Collectors.averagingDouble(p -> (Objects.isNull(p.get("downloadSpeed"))) ? 0.0 : (Double.valueOf(String.valueOf(p.get("downloadSpeed"))))));
        }
        return internetUtilization;

    }*/

    public double getAvgInterNetUtilizationForDuration(long minutes, Equipment userEquipment) {
        double internetUtilization = 0.0;

        List<Map<String, Object>> actualSlidingData = null;
        try {
            actualSlidingData = prepareDataForInternetUtilizationSlidingData(userEquipment, minutes);
        } catch (Exception e) {
            LOG.error("Error while fetching InternetUtilizationSlidingData");
        }

        if (!actualSlidingData.isEmpty()) {
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (60 * 60L * 1000L)));
            Long currTimeStamp = criteriaCalendar.getTimeInMillis();
            actualSlidingData = actualSlidingData.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0.0) > currTimeStamp).collect(Collectors.toList());
        }

        if (!actualSlidingData.isEmpty()) {
            actualSlidingData.sort((q1, q2) -> Long.compare((Long) q2.get("timestamp"), (Long) q1.get("timestamp")));
            actualSlidingData = actualSlidingData.size() < minutes ? actualSlidingData.subList(0, actualSlidingData.size()) : actualSlidingData.subList(0, (int) minutes);
            //TODO will be removed latter due to Agent side BugactualSlidingData
            actualSlidingData = actualSlidingData.stream().filter(q -> Objects.nonNull(q.get("downloadSpeed"))).collect(Collectors.toList());
            DoubleSummaryStatistics doubleSummaryStatistics = actualSlidingData.stream().mapToDouble(q -> (Objects.nonNull(q.get("downloadSpeed")) ? ((Double) q.get("downloadSpeed")) : 0.0)).summaryStatistics();
            internetUtilization = doubleSummaryStatistics.getAverage();
//            internetUtilization = actualSlidingData.stream().collect(Collectors.averagingDouble(p -> (Objects.isNull(p.get("downloadSpeed"))) ? 0.0 : (Double.valueOf(String.valueOf(p.get("downloadSpeed"))))));
        }
        return internetUtilization;

    }

    public Map<String, Double> getEquipAvgInterNetUtilizationForDuration(long minutes, Equipment userEquipment) {
        double internetUtilization = 0.0;
        Map<String, Double> utilizationMap = null;
        List<Map<String, Object>> actualSlidingData = null;
        try {
            actualSlidingData = prepareDataForInternetUtilizationSlidingData(userEquipment, minutes);
        } catch (Exception e) {
            LOG.error("Error while fetching InternetUtilizationSlidingData");
        }

        if (!actualSlidingData.isEmpty()) {
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (60 * 60L * 1000L)));
            Long currTimeStamp = criteriaCalendar.getTimeInMillis();
            actualSlidingData = actualSlidingData.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0.0) > currTimeStamp).collect(Collectors.toList());
        }

        if (!actualSlidingData.isEmpty()) {
            utilizationMap = new HashMap<String, Double>(2);
            actualSlidingData.sort((q1, q2) -> Long.compare((Long) q2.get("timestamp"), (Long) q1.get("timestamp")));
            actualSlidingData = actualSlidingData.size() < minutes ? actualSlidingData.subList(0, actualSlidingData.size()) : actualSlidingData.subList(0, (int) minutes);
            //TODO will be removed latter due to Agent side BugactualSlidingData
            actualSlidingData = actualSlidingData.stream().filter(q -> Objects.nonNull(q.get("downloadSpeed"))).collect(Collectors.toList());
            DoubleSummaryStatistics doubleSummaryStatistics = actualSlidingData.stream().mapToDouble(q -> (Objects.nonNull(q.get("downloadSpeed")) ? ((Double) q.get("downloadSpeed")) : 0.0)).summaryStatistics();
            internetUtilization = doubleSummaryStatistics.getAverage();
            utilizationMap.put("downLoadAvg", internetUtilization);

            actualSlidingData = actualSlidingData.stream().filter(q -> Objects.nonNull(q.get("uploadSpeed"))).collect(Collectors.toList());
            doubleSummaryStatistics = actualSlidingData.stream().mapToDouble(q -> (Objects.nonNull(q.get("uploadSpeed")) ? ((Double) q.get("uploadSpeed")) : 0.0)).summaryStatistics();
            internetUtilization = doubleSummaryStatistics.getAverage();
            utilizationMap.put("upLoadAvg", internetUtilization);
        }
        return utilizationMap;

    }

    private List<Map<String, Object>> prepareDataForInternetUtilizationSlidingData(Equipment userEquipment, long minutes) throws Exception {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        HashMap<String, Object> params = new HashMap<>();

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));
        params.put("userId", userEquipment.getRgwSerial());
        params.put("dateHour", dateCriteria);
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        List<DBObject> internetUtilizationTableData = (List<DBObject>) mongoService.findList(params, new HashMap<>(), INTERNET_UTILIZATION_SLIDING_DATA, mongoFieldOptions);

        List<Map<String, Object>> actualSlidingData = new ArrayList<>();

        if (Objects.nonNull(internetUtilizationTableData) && !internetUtilizationTableData.isEmpty()) {
            for (DBObject dbObject : internetUtilizationTableData) {
                String today = String.valueOf(dbObject.get("date"));
                //TODO Remove below code for Release36
                List<Map<String, Object>> slidingData = (List<Map<String, Object>>) dbObject.get("slidingData");
                if (Objects.nonNull(slidingData)) {
                    if (!slidingData.isEmpty()) {
                        actualSlidingData.addAll(slidingData);
                    }
                }

                //TODO END

                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        List<Map<String, Object>> slidingDataMap = (List<Map<String, Object>>) dataByDay.get(key);
                        if (!slidingDataMap.isEmpty()) {
                            actualSlidingData.addAll(slidingDataMap);
                        }
                    }
                }
            }
        }

        return actualSlidingData;
    }

    public InternetDetailDTO getInternetDetails(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

//        List<WANSpeedTest> wanSpeedTests = mongoService.read(WANSpeedTest.class, new Query().addCriteria(Criteria.where("UserId").is(userAP.getApId())).addCriteria(Criteria.where("date").exists(true)).with(new Sort(Sort.Direction.DESC, DATE)));

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("date", new BasicDBObject("$exists", true));

        List<DBObject> wanSpeedTests = mongoService.findList(WAN_SPEED_TEST, query, DATE, DESC);

        InternetDetailDTO internetDetailDTO = new InternetDetailDTO();
        InternetDetailDTO.InternetDetailData internetDetailData = internetDetailDTO.new InternetDetailData();
        SpeedStats speedStats = new SpeedStats();
        if (!wanSpeedTests.isEmpty() && Objects.nonNull(wanSpeedTests.get(0).get("date"))) {
            speedStats.setDate(Long.valueOf(((Date) wanSpeedTests.get(0).get("date")).getTime()));
            speedStats.setDownloadSpeed(Objects.isNull(wanSpeedTests.get(0).get("downloadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeedTests.get(0).get("downloadSpeed"))));
            speedStats.setLatency(Objects.isNull(wanSpeedTests.get(0).get("latency")) ? 0 : Long.valueOf(wanSpeedTests.get(0).get("latency").toString()));
            speedStats.setUploadSpeed(Objects.isNull(wanSpeedTests.get(0).get("uploadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeedTests.get(0).get("uploadSpeed"))));
        } else {
            speedStats.setDate(0);
            speedStats.setDownloadSpeed(0);
            speedStats.setLatency(0);
            speedStats.setUploadSpeed(0);
        }

        double subscriberBandwidth = userEquipment.getDownLinkRate();
        if (subscriberBandwidth == 0) {
            subscriberBandwidth = 1000;
        }

        internetDetailData.setSpeedStats(speedStats);
        internetDetailData.setSubscriberRate(userEquipment.getDownLinkRate() > 10000 || userEquipment.getDownLinkRate() <= 0 ? 1000.0 : userEquipment.getDownLinkRate());
        internetDetailData.setDownLinkRate(userEquipment.getDownLinkRate() > 10000 || userEquipment.getDownLinkRate() <= 0 ? 1000.0 : userEquipment.getDownLinkRate());
        internetDetailData.setUpLinkRate(userEquipment.getUpLinkRate() > 10000 || userEquipment.getUpLinkRate() <= 0 ? 1000.0 : userEquipment.getUpLinkRate());
        double utilization = getAvgInterNetUtilizationForDuration(60, userEquipment);
        utilization = (utilization * 8) / (1000F * 1000F); // Converting to Mbps
        internetDetailData.setInternetUtilization(Double.parseDouble(TWO_DECIMAL_PLACE.format((utilization * 100 / subscriberBandwidth))));

        internetDetailDTO.setData(internetDetailData);


        return internetDetailDTO;
    }

    /*public InternetUtilizationDTO getInternetUtilizationLineChartData(String serialNumberOrSubscriberId, long duration) throws Exception {
        manageCommonService.isDurationValid(duration);
        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId))
            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
        UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberId);
        manageCommonService.subscriberDataExistInMongo(userAP);
        manageCommonService.subscriberGroupMisMatch(userAP);
        HashMap<String, Object> params = new HashMap<>();
        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        long minutes = duration;

        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        params.put("userId", userAP.getApId());
        params.put("dateHour", dateCriteria);

        List<DBObject> internetUtilization = (List<DBObject>) mongoService.findList(params, appendableParams, INTERNET_UTILIZATION_SLIDING_DATA, mongoFieldOptions);

        InternetUtilizationDTO internetUtilizationDTO = new InternetUtilizationDTO();
        ArrayList<InternetUtilizationDTO.InternetUtilizationData> internetUtilizationDataList = new ArrayList<>();

        if (Objects.isNull(internetUtilization) || internetUtilization.isEmpty()) {
            internetUtilizationDTO.setData(internetUtilizationDataList);
            return internetUtilizationDTO;
        }

        List<BasicDBObject> actualSlidingDataList = new ArrayList<>();

        for (DBObject dbObject : internetUtilization) {
            List<BasicDBObject> slidingDataList = (List<BasicDBObject>) dbObject.get("slidingData");
            if (Objects.nonNull(slidingDataList) && !slidingDataList.isEmpty()) {
                Calendar criteriaCalendar = Calendar.getInstance();
                criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
                long currTimeStamp = criteriaCalendar.getTimeInMillis();
                slidingDataList = slidingDataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
                actualSlidingDataList.addAll(slidingDataList);
            }
        }


        for (int i = 0; i < actualSlidingDataList.size();*//*i += CommonUtils.getSkipFactorForGraphs(actualSlidingDataList)*//*i++) {
            InternetUtilizationDTO.InternetUtilizationData internetUtilizationData = internetUtilizationDTO.new InternetUtilizationData();
            HashMap<String, Object> obj = actualSlidingDataList.get(i);
            internetUtilizationData.setDownloadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format((Objects.isNull(obj.get("downloadSpeed")) ? 0 : Double.valueOf(obj.get("downloadSpeed").toString())) / 1000F / 1000F * 8)));
            internetUtilizationData.setUploadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format((Objects.isNull(obj.get("uploadSpeed")) ? 0 : Double.valueOf(obj.get("uploadSpeed").toString())) / 1000F / 1000F * 8)));
            internetUtilizationData.setTimestamp(Long.valueOf(Objects.isNull(obj.get("timestamp")) ? "0" : obj.get("timestamp").toString()));
            internetUtilizationDataList.add(internetUtilizationData);
        }

        if (internetUtilizationDataList.isEmpty()) {
            InternetUtilizationDTO.InternetUtilizationData internetUtilizationData = internetUtilizationDTO.new InternetUtilizationData();
            internetUtilizationDataList.add(internetUtilizationData);
        } else {
            internetUtilizationDataList.sort(Comparator.comparing(InternetUtilizationDTO.InternetUtilizationData::getTimestamp));
        }
        internetUtilizationDTO.setData(internetUtilizationDataList);

        return internetUtilizationDTO;
    }*/

    public InternetUtilizationDTO getInternetUtilizationLineChartData(String equipmentIdOrSerialOrSTN, long duration) throws Exception {
        manageCommonService.isDurationValid(duration);
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN))
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<Map<String, Object>> internetUtilization = prepareDataForInternetUtilizationSlidingData(userEquipment, duration);

        InternetUtilizationDTO internetUtilizationDTO = new InternetUtilizationDTO();

        ArrayList<InternetUtilizationDTO.InternetUtilizationData> internetUtilizationDataList = new ArrayList<>();

        if (Objects.isNull(internetUtilization) || internetUtilization.isEmpty()) {
            internetUtilizationDTO.setData(internetUtilizationDataList);
            return internetUtilizationDTO;
        }

        if (Objects.nonNull(internetUtilization) && !internetUtilization.isEmpty()) {
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() -  duration * 60L * 1000L));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            internetUtilization = internetUtilization.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
        }

//        Double totalDownloadSpeed = 0.0;
//        Double totalUploadSpeed = 0.0;
        for (int i = 0; i < internetUtilization.size();/*i += CommonUtils.getSkipFactorForGraphs(actualSlidingDataList)*/i++) {
            InternetUtilizationDTO.InternetUtilizationData internetUtilizationData = internetUtilizationDTO.new InternetUtilizationData();
            Map<String, Object> obj = internetUtilization.get(i);
            if ((Objects.nonNull(obj.get("downloadSpeed")) && Objects.nonNull(obj.get("uploadSpeed")))) {
                double downLinkRate = userEquipment.getDownLinkRate() > 10000 || userEquipment.getDownLinkRate() <= 0 ? 1000.0 : userEquipment.getDownLinkRate();
                double upLinkRate = userEquipment.getUpLinkRate() > 10000 || userEquipment.getUpLinkRate() <= 0 ? 1000.0 : userEquipment.getUpLinkRate();
                internetUtilizationData.setDownloadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format(Double.valueOf(obj.get("downloadSpeed").toString()) / 1000F / 1000F * 8)));
                internetUtilizationData.setUploadSpeed(Double.valueOf(TWO_DECIMAL_PLACE.format(Double.valueOf(obj.get("uploadSpeed").toString()) / 1000F / 1000F * 8)));
                internetUtilizationData.setTimestamp(Long.valueOf(Objects.isNull(obj.get("timestamp")) ? "0" : obj.get("timestamp").toString()));
                internetUtilizationData.setUpLinkRate(upLinkRate);
                internetUtilizationData.setDownLinkRate(downLinkRate);
                internetUtilizationData.setDownloadPercentage(Double.valueOf(TWO_DECIMAL_PLACE.format(((Double.valueOf(obj.get("downloadSpeed").toString()) / 1000F / 1000F * 8) / downLinkRate) * 100)));
                internetUtilizationData.setUploadPercentage(Double.valueOf(TWO_DECIMAL_PLACE.format(((Double.valueOf(obj.get("uploadSpeed").toString()) / 1000F / 1000F * 8) / upLinkRate) * 100)));
//                totalDownloadSpeed += internetUtilizationData.getDownloadSpeed();
//                totalUploadSpeed += internetUtilizationData.getUploadSpeed();
                internetUtilizationDataList.add(internetUtilizationData);
            }

        }


        if (internetUtilizationDataList.isEmpty()) {
            InternetUtilizationDTO.InternetUtilizationData internetUtilizationData = internetUtilizationDTO.new InternetUtilizationData();
            internetUtilizationDataList.add(internetUtilizationData);
        } else {
            internetUtilizationDataList.sort(Comparator.comparing(InternetUtilizationDTO.InternetUtilizationData::getTimestamp));
        }
        internetUtilizationDTO.setData(internetUtilizationDataList);
//        internetUtilizationDTO.setTotalDownloadSpeed(totalDownloadSpeed);
//        internetUtilizationDTO.setTotalUploadSpeed(totalUploadSpeed);

        return internetUtilizationDTO;
    }

    private void sendWanSpeedTestRpc30(String tid, String userId, String serial, Map vmqResponse) throws Exception {
        String rpcUri = RpcConstants.NETWORK_ACTION_URI;

        Map<String, String> payloadMap = new HashMap<>();
        payloadMap.put("action", RpcConstants.ACTION_WAN_SPEED_TEST);
        payloadMap.put("testServer", manageSpeedTestUrlServices.getDefaultWanSpeedTest() == null ? "null" : manageSpeedTestUrlServices.getDefaultWanSpeedTest());
        String payload = objectMapper.writeValueAsString(payloadMap);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));
        tid = cpeRpcService.sendRpc(tid, userId, serial, rpcUri, "POST", payload);

        Map<String, Object> result = null;
        for (int i = 0; i < maxTries; i++) {
            try {
                Thread.sleep(THREAD_TO_SLEEP);
            } catch (InterruptedException e) {
                LOG.error("waitingRpcResult interrupted. tid:[ " + tid + "]");
                throw e;
            }

            result = cpeRpcService.readRpcResult(tid);
            if (result != null) {
                break;
            }
        }

        if (result != null) {
            if (StringUtils.equals(String.valueOf(result.get("code")), "200")) {
                HashMap<String, Object> dataMap = new HashMap<>();
                dataMap.put("_id", tid);
                dataMap.put("userId", userId);
                dataMap.put("rpcVer", "3.0");
                dataMap.put("vmqStatus", vmqResponse);
                dataMap.put("date", new Date());

                if (result.get("payload") != null) {
                    List<Object> respPayloadList = (List) result.get("payload");
                    Map<String, Object> respPayloadMap = (Map) respPayloadList.get(0);

                    dataMap.put("latency", Integer.valueOf(respPayloadMap.get("latency").toString()));
                    dataMap.put("downloadSpeed", Double.valueOf(respPayloadMap.get("download").toString()) / 1000.0);
                    dataMap.put("uploadSpeed", Double.valueOf(respPayloadMap.get("upload").toString()) / 1000.0);
                    dataMap.put("testServer", String.valueOf(respPayloadMap.get("testServer")));
                    dataMap.put("result", String.valueOf(respPayloadMap.get("result")));
                } else {
                    dataMap.put("latency", 0);
                    dataMap.put("downloadSpeed", 0);
                    dataMap.put("uploadSpeed", 0);
                    dataMap.put("testServer", null);
                    dataMap.put("result", EXECUTE_OK);
                }

                mongoService.create(WAN_SPEED_TEST_COLLECTION, dataMap);
            } else {
                LOG.error("serial: [{}] tid: [{}] response: [{}]", serial, tid, result.toString());

                HashMap<String, Object> dataMap = new HashMap<>();
                dataMap.put("_id", tid);
                dataMap.put("userId", userId);
                dataMap.put("latency", 0);
                dataMap.put("downloadSpeed", 0);
                dataMap.put("uploadSpeed", 0);
                dataMap.put("testServer", null);
                dataMap.put("result", EXECUTE_FAIL.toUpperCase());
                dataMap.put("rpcVer", "3.0");
                dataMap.put("vmqStatus", vmqResponse);
                dataMap.put("date", new Date());
                mongoService.create(WAN_SPEED_TEST_COLLECTION, dataMap);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request failed, RPC result :: " + result.get("code"));
            }
        } else {
            HashMap<String, Object> dataMap = new HashMap<>();
            dataMap.put("_id", tid);
            dataMap.put("userId", userId);
            dataMap.put("latency", 0);
            dataMap.put("downloadSpeed", 0);
            dataMap.put("uploadSpeed", 0);
            dataMap.put("testServer", null);
            dataMap.put("result", TIME_OUT);
            dataMap.put("rpcVer", "3.0");
            dataMap.put("vmqStatus", vmqResponse);
            dataMap.put("date", new Date());
            mongoService.create(WAN_SPEED_TEST_COLLECTION, dataMap);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
        }
    }

    private void sendWanSpeedTestRpc20(String tid, String userId, String serial, Map vmqResponse) throws Exception {

        HashMap<String, Object> dataMap = new HashMap<>();
        dataMap.put("date", new Date());
        dataMap.put("rpcVer", "2.0");
        dataMap.put("vmqStatus", vmqResponse);
        dataMap.put("userId", userId);
        dataMap.put("_id", tid);
        mongoService.create(WAN_SPEED_TEST_COLLECTION, dataMap);

        HashMap<String, String> publishParams = new HashMap<>();
        publishParams.put("-USER_ID-", userId);
        publishParams.put("-TEST_URL-", manageSpeedTestUrlServices.getDefaultWanSpeedTest() == null ? "null" : manageSpeedTestUrlServices.getDefaultWanSpeedTest());
        publishParams.put("-S_ID-", serial);
        publishParams.put("-ID-", String.valueOf(Calendar.getInstance().getTimeInMillis()));
        publishParams.put("-TID-", tid);
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));
        rpcUtilityService.publishToTopic(publishParams, MqttTemplate.WAN_SPEED_TEST_TEMPLATE_FOR_USER, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

        boolean resultReceived = false;
        Boolean isTimeout = false;
        int maxTries = 0;
        while (!resultReceived) {
            try {
                Thread.sleep(THREAD_TO_SLEEP);
            } catch (InterruptedException e) {
                LOG.error("Error in thread sleep during perform speed test for user", e);
            }
            maxTries++;
            if (maxTries == max_Tries) {
                isTimeout = true;
                LOG.error("Operation Timed Out...please retry....Setting Values To 0");
                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("date", new Date());
                dataToUpdate.put("downloadSpeed", 0);
                dataToUpdate.put("uploadSpeed", 0);
                dataToUpdate.put("latency", 0);
                dataToUpdate.put("testServer", null);
                dataToUpdate.put("result", TIME_OUT);

                BasicDBObject query = new BasicDBObject();
                query.put("_id", tid);

                BasicDBObject update = new BasicDBObject();
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, WAN_SPEED_TEST_COLLECTION);
                break;
            }
            BasicDBObject query = new BasicDBObject();
            query.put("_id", tid);

            DBObject wanSpeedTest = mongoService.findOne(WAN_SPEED_TEST_COLLECTION, query);
            if (Objects.isNull(wanSpeedTest) || Objects.isNull(wanSpeedTest.get("result"))) {
                continue;
            } else {
                resultReceived = true;
            }
        }

        BasicDBObject query = new BasicDBObject();
        query.put("_id", tid);
        DBObject wanSpeedTest = mongoService.findOne(WAN_SPEED_TEST_COLLECTION, query);
        if (Objects.nonNull(wanSpeedTest)) {
            if (!RPC_RESULT.equals(wanSpeedTest.get("result"))) {
                if (isTimeout)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
                else
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request failed, RPC result :: " + wanSpeedTest.get("result"));
            }
        }
    }

    @Auditable(operation = AuditorConstants.EQUIPMENT_SPEED_TEST, method = RequestMethod.GET)
    public SpeedTestHistoryDTO performSpeedTestForUser(String equipmentIdOrSerialOrSTN, boolean isSpeedTest, HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        SpeedTestHistoryDTO speedTestHistoryDTO = new SpeedTestHistoryDTO();
        String tid = CommonUtils.generateUUID();

        if (isSpeedTest) {
            String serial = manageCommonService.getGatewaySerialByUserId(userEquipment.getRgwSerial()).orElseGet(()->userEquipment.getRgwSerial());

            String vmqResponse = manageEquipmentService.getStatusFromVmq(serial);
            Map<String, Object> vmqRawResponse = new HashMap<>();
            if(vmqResponse != null) {
                vmqRawResponse = objectMapper.readValue(vmqResponse, HashMap.class);
            }

            // bypassing offline check for testing OC-5344
//            manageCommonService.checkEquipmentOffline(userEquipment.getRgwSerial(), serial);
            AgentVersion agentVersion = equipmentUtils.getAgentVersion(userEquipment.getRgwSerial(), serial);
            if(agentVersion.getMajorVersion() > 3) {
                sendWanSpeedTestRpc30(tid, userEquipment.getRgwSerial(), serial, vmqRawResponse);
            } else {
                sendWanSpeedTestRpc20(tid, userEquipment.getRgwSerial(), serial, vmqRawResponse);
            }
            speedTestHistoryDTO.setTid(tid);
        }

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("date", new BasicDBObject("$exists", true));

        if (isSpeedTest)
            query.put("_id", tid);

        List<DBObject> wanSpeedTestHistorical = mongoService.findList(WAN_SPEED_TEST_COLLECTION, query, DATE, DESC);
        if (isSpeedTest && !wanSpeedTestHistorical.isEmpty() && Objects.nonNull(wanSpeedTestHistorical.get(0).get("date"))) {
            ArrayList<SpeedStats> speedStatsList = new ArrayList<>();
            SpeedStats speedStats = new SpeedStats();
            speedStats.setDate(Long.valueOf(((Date) wanSpeedTestHistorical.get(0).get("date")).getTime()));
            speedStats.setDownloadSpeed(Objects.isNull(wanSpeedTestHistorical.get(0).get("downloadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeedTestHistorical.get(0).get("downloadSpeed"))));
            speedStats.setLatency(Objects.isNull(wanSpeedTestHistorical.get(0).get("latency")) ? 0 : Long.valueOf(wanSpeedTestHistorical.get(0).get("latency").toString()));
            speedStats.setUploadSpeed(Objects.isNull(wanSpeedTestHistorical.get(0).get("uploadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeedTestHistorical.get(0).get("uploadSpeed"))));
            speedStats.setResult(Objects.isNull(wanSpeedTestHistorical.get(0).get("result")) ? ((speedStats.getDownloadSpeed() == 0 && speedStats.getUploadSpeed() == 0) ? TIME_OUT : "OK") : wanSpeedTestHistorical.get(0).get("result").toString());
            speedStats.setUrl(Objects.isNull(wanSpeedTestHistorical.get(0).get("testServer")) ? null : String.valueOf(wanSpeedTestHistorical.get(0).get("testServer")));
            speedStatsList.add(speedStats);
            speedTestHistoryDTO.setData(speedStatsList.isEmpty() ? null : speedStatsList);

            return speedTestHistoryDTO;
        } else if (!wanSpeedTestHistorical.isEmpty()) {
            final ArrayList<SpeedStats> speedStatsList = new ArrayList<>();
            wanSpeedTestHistorical.forEach(wanSpeed -> {
                if (Objects.nonNull(wanSpeed.get("date"))) {
                    try {
                        SpeedStats speedStats = new SpeedStats();
                        speedStats.setDate(Long.valueOf(((Date) wanSpeed.get("date")).getTime()));
                        speedStats.setDownloadSpeed(Objects.isNull(wanSpeed.get("downloadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeed.get("downloadSpeed"))));
                        speedStats.setLatency(Objects.isNull(wanSpeed.get("latency")) ? 0 : Long.valueOf(wanSpeed.get("latency").toString()));
                        speedStats.setUploadSpeed(Objects.isNull(wanSpeed.get("uploadSpeed")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(wanSpeed.get("uploadSpeed"))));
                        speedStats.setResult(Objects.isNull(wanSpeed.get("result")) ? ((speedStats.getDownloadSpeed() == 0 && speedStats.getUploadSpeed() == 0) ? TIME_OUT : "OK") : wanSpeed.get("result").toString());
                        speedStats.setUrl(Objects.isNull(wanSpeed.get("testServer")) ? null : String.valueOf(wanSpeed.get("testServer")));
                        speedStatsList.add(speedStats);
                    } catch (Exception e) {

                    }
                }
            });

            if (speedStatsList.isEmpty()) {
                SpeedStats speedStats = new SpeedStats();
                speedStatsList.add(speedStats);
            }
            speedStatsList.sort(Comparator.comparing(SpeedStats::getDate).reversed());
            List<SpeedStats> speedStatsArrayList = speedStatsList.subList(0, speedStatsList.size() <= 50 ? speedStatsList.size() : 50);
            speedTestHistoryDTO.setData(speedStatsList.isEmpty() ? null : speedStatsArrayList);
            return speedTestHistoryDTO;
        } else {
            SpeedStats speedStats = new SpeedStats();
            ArrayList<SpeedStats> speedStatsList = new ArrayList<>();
            speedStatsList.add(speedStats);
            speedTestHistoryDTO.setData(speedStatsList);

            return speedTestHistoryDTO;
        }
    }

    private long fetchEquipmentUptime(Equipment equipment) {
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", equipment.getRgwSerial());
        queryParams.put("serialNumber", equipment.getServiceTelephoneNo());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        long uptime = Long.parseLong(aPDetails.get("uptime").toString());
        long currentTimestamp = System.currentTimeMillis() / 1000;

        return currentTimestamp - uptime;
    }

    public WifiHealthHistoryDTO getWifiHealth(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        long uptimeInSeconds = fetchEquipmentUptime(userEquipment);
        long secondsInDay = 24 * 60 * 60;
        long days7InSeconds = 7L * secondsInDay;
        long days30InSeconds = 30L * secondsInDay;
        long days90InSeconds = 90L * secondsInDay;

        LOG.info("Uptime (in seconds): " + uptimeInSeconds);
        LOG.info("Uptime (in days): " + uptimeInSeconds / secondsInDay);

        HashMap<String, String> aggregationParams = new HashMap<>();
        aggregationParams.put("outputParams", "sumScore");
        aggregationParams.put("label", "avgWifiHealth");
        aggregationParams.put("operation", "$avg");
        aggregationParams.put("keyToAggregate", "$statsData.v.wifiHealthScore");

        HashMap<String, String> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());

        DBObject wifiHealthToday = mongoService.aggregateDataForAllOrResourceIdV3(aggregationWhereClause, null, aggregationParams, STATS_PER_USER_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));

        DBObject wifiHealthLast7Days = null;
        if(uptimeInSeconds >= days7InSeconds) {
            wifiHealthLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggregationWhereClause, null, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
        }

        DBObject wifiHealthLast30Days = null;
        if(uptimeInSeconds >= days30InSeconds) {
            wifiHealthLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggregationWhereClause, null, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));

        }

        DBObject wifiHealthLast90Days = null;
        if(uptimeInSeconds >= days90InSeconds) {
            wifiHealthLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggregationWhereClause, null, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));

        }

        WifiHealthHistoryDTO wifiHealthHistoryDTO = new WifiHealthHistoryDTO();
        ArrayList<WifiHealthHistoryDTO.WifiHealthHistoryData> wifiHealthHistoryDataList = new ArrayList<>();
        try {
            String outputParams = "avgWifiHealth";
            List<HashMap<String, Object>> wifiHealthList = mongoService.generateHistoricalGraphDataFromDataForColumnChart(outputParams,
                    Objects.isNull(wifiHealthToday) ? 0.0d : Double.valueOf(wifiHealthToday.get("sumScore").toString()),
                    Objects.isNull(wifiHealthLast7Days) ? 0.0d : Double.valueOf(wifiHealthLast7Days.get("sumScore").toString()),
                    Objects.isNull(wifiHealthLast30Days) ? 0.0d : Double.valueOf(wifiHealthLast30Days.get("sumScore").toString()),
                    Objects.isNull(wifiHealthLast90Days) ? 0.0d : Double.valueOf(wifiHealthLast90Days.get("sumScore").toString()));

            for (HashMap<String, Object> data : wifiHealthList) {
                WifiHealthHistoryDTO.WifiHealthHistoryData wifiHealthHistoryData = wifiHealthHistoryDTO.new WifiHealthHistoryData();
                wifiHealthHistoryData.setAvgWifiHealth(Double.valueOf(Objects.isNull(data.get("avgWifiHealth")) ? "0.0" : data.get("avgWifiHealth").toString()));
                wifiHealthHistoryData.setDay(Objects.isNull(data.get("Day")) ? null : data.get("Day").toString());
                wifiHealthHistoryDataList.add(wifiHealthHistoryData);
            }
        } catch (NumberFormatException e) {

        }

        if (wifiHealthHistoryDataList.isEmpty()) {
            WifiHealthHistoryDTO.WifiHealthHistoryData wifiHealthHistoryData = wifiHealthHistoryDTO.new WifiHealthHistoryData();
            wifiHealthHistoryDataList.add(wifiHealthHistoryData);
        }
        wifiHealthHistoryDTO.setData(wifiHealthHistoryDataList);
        return wifiHealthHistoryDTO;
    }

    /*public WifiHealthPerMinuteDTO getWifiHealthPerMinute(String serialNumberOrSubscriberId) throws Exception {
        if (ValidationUtil.validateMAC(serialNumberOrSubscriberId))
            serialNumberOrSubscriberId = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberId);
        UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrSubscriberId);
        manageCommonService.subscriberDataExistInMongo(userAP);
        manageCommonService.subscriberGroupMisMatch(userAP);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        long t1 = Calendar.getInstance().getTimeInMillis();

        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userAP.getApId());
        matchParam.put("timestamp", new BasicDBObject("$gt", t1 - HOURLY_DATA * 60L * 1000L));

        BasicDBObject grouping = new BasicDBObject();
        grouping.put("userId", "$userId");
        grouping.put("timestamp", "$timestamp");


        BasicDBObject groupParam = new BasicDBObject();
        groupParam.put("_id", grouping);
        groupParam.put("score", new BasicDBObject("$avg", "$score"));

        BasicDBObject match = new BasicDBObject();
        match.put("$match", matchParam);

        BasicDBObject group = new BasicDBObject();
        group.put("$group", groupParam);

        BasicDBObject project = new BasicDBObject();
        project.put("score", 1);
        project.put("timestamp", "$_id.timestamp");

        BasicDBObject projection = new BasicDBObject();
        projection.put("$project", project);

        BasicDBObject sort = new BasicDBObject();
        sort.put("$sort", new BasicDBObject("timestamp", DESC));

        List<BasicDBObject> pipeline = new ArrayList<>();
        pipeline.add(match);
        pipeline.add(group);
        pipeline.add(projection);
        pipeline.add(sort);

        List<DBObject> dbObjectList = mongoService.aggregationPipeline(pipeline, AP_WIFI_INSIGHTS_PER_MINUTE);

        ListIterator<DBObject> listItr = dbObjectList.listIterator(dbObjectList.size());
        WifiHealthPerMinuteDTO wifiHealthPerMinuteDTO = new WifiHealthPerMinuteDTO();
        ArrayList<WifiHealthPerMinuteDTO.WifiHealthPerMinuteData> wifiHealthPerMinuteDTOList = new ArrayList<>();
        while (listItr.hasPrevious()) {
            DBObject wifiHealthRecord = listItr.previous();
            WifiHealthPerMinuteDTO.WifiHealthPerMinuteData wifiHealthPerMinuteData = wifiHealthPerMinuteDTO.new WifiHealthPerMinuteData();
            wifiHealthPerMinuteData.setValue(Objects.isNull(wifiHealthRecord.get("score")) ? 0.0d : Double.valueOf(wifiHealthRecord.get("score").toString()));
            wifiHealthPerMinuteData.setTimestamp(Long.valueOf(Objects.isNull(wifiHealthRecord.get("timestamp")) ? "0" : wifiHealthRecord.get("timestamp").toString()));
            wifiHealthPerMinuteDTOList.add(wifiHealthPerMinuteData);
        }
        if (wifiHealthPerMinuteDTOList.isEmpty()) {
            WifiHealthPerMinuteDTO.WifiHealthPerMinuteData wifiHealthPerMinuteData = wifiHealthPerMinuteDTO.new WifiHealthPerMinuteData();
            wifiHealthPerMinuteDTOList.add(wifiHealthPerMinuteData);
        }
        wifiHealthPerMinuteDTO.setData(wifiHealthPerMinuteDTOList);
        return wifiHealthPerMinuteDTO;
    }*/

   /* private List<DBObject> getTimeSeriesDataForWifiHealthPerMinute(UserAP userAP, long minutes) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);

        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();
        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userAP.getApId());
        matchParam.put("dateHour", dateCriteria);

        BasicDBObject match = new BasicDBObject();
        match.put("$match", matchParam);

        BasicDBObject unwind = new BasicDBObject();
        unwind.put("$unwind", "$data");

        BasicDBObject timestampCriteria = new BasicDBObject();
        timestampCriteria.put("data.timestamp", new BasicDBObject("$gt", t1 - HOURLY_DATA * 60L * 1000L));

        BasicDBObject matchTimeStamp = new BasicDBObject();
        matchTimeStamp.put("$match", timestampCriteria);

        BasicDBObject groupParam = new BasicDBObject();
        groupParam.put("_id", new BasicDBObject("timestamp", "$data.timestamp"));
        groupParam.put("score", new BasicDBObject("$avg", "$data.score"));

        BasicDBObject group = new BasicDBObject();
        group.put("$group", groupParam);

        BasicDBObject project = new BasicDBObject();
        project.put("score", 1);
        project.put("timestamp", "$_id.timestamp");
        project.put("_id", 0);

        BasicDBObject projection = new BasicDBObject();
        projection.put("$project", project);

        BasicDBObject sort = new BasicDBObject();
        sort.put("$sort", new BasicDBObject("timestamp", ASC));

        List<BasicDBObject> pipeline = new ArrayList<>();
        pipeline.add(match);
        pipeline.add(unwind);
        pipeline.add(matchTimeStamp);
        pipeline.add(group);
        pipeline.add(projection);
        pipeline.add(sort);

        List<DBObject> dbObjectList = mongoService.aggregationPipeline(pipeline, AP_WIFI_INSIGHTS_PER_MINUTE);

        return dbObjectList;
    }*/

    private List<Map<String, Object>> getTimeSeriesDataForWifiHealthPerMinute(Equipment userEquipment, long minutes) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        Calendar now = Calendar.getInstance();
        now.set(Calendar.HOUR_OF_DAY, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();
        long dateHour = nowMillSec / 1000;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        BasicDBObject matchParam = new BasicDBObject();
        matchParam.put("userId", userEquipment.getRgwSerial());
        matchParam.put("dateHour", dateCriteria);

        List<Map<String, Object>> finalList = new ArrayList<>();

        List<DBObject> dbObjectList = mongoService.findList(matchParam, null, AP_WIFI_INSIGHTS_PER_MINUTE, mongoFieldOptions);
        List<Map<String, Object>> combinedList = new ArrayList<>();
        if (!dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                if (Objects.nonNull(dbObject.get("hourlyData"))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get("hourlyData");
                    for (String key : dataByDay.keySet()) {
                        List<Map<String, Object>> apWifiData = (List<Map<String, Object>>) dataByDay.get(key);
                        if (Objects.nonNull(apWifiData)) {
                            for (Map<String, Object> dataObject : apWifiData) {
                                if ((Long) dataObject.get("timestamp") > (t1 - minutes * 60L * 1000L))
                                    combinedList.add(dataObject);
                            }
                        }
                    }
                }
            }


            Map<String, Double> data = combinedList.stream().collect(Collectors.groupingBy(element -> String.valueOf(element.get("timestamp")), Collectors.averagingDouble(q -> (Double) (q.get("wifiHealthScore")))));
            for (Map.Entry<String, Double> object : data.entrySet()) {
                Map<String, Object> dataObj = new HashMap<>();
                dataObj.put("timestamp", object.getKey());
                dataObj.put("score", object.getValue());
                finalList.add(dataObj);
            }

        }
        return finalList;
    }

    public WifiHealthPerMinuteDTO getWifiHealthPerMinute(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        WifiHealthPerMinuteDTO wifiHealthPerMinuteDTO = new WifiHealthPerMinuteDTO();
        ArrayList<WifiHealthPerMinuteDTO.WifiHealthPerMinuteData> wifiHealthPerMinuteDTOList = new ArrayList<>();

        /*HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        long minutes = HOURLY_DATA;

        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", dateHour);

        params.put("userId", userAP.getApId());
        params.put("dateHour", dateCriteria);

        WifiHealthPerMinuteDTO wifiHealthPerMinuteDTO = new WifiHealthPerMinuteDTO();
        ArrayList<WifiHealthPerMinuteDTO.WifiHealthPerMinuteData> wifiHealthPerMinuteDTOList = new ArrayList<>();
        List<DBObject> dbObjectList = mongoService.findList(params, null, AP_WIFI_INSIGHTS_PER_MINUTE, mongoFieldOptions);*/
        List<Map<String, Object>> dbObjectList = getTimeSeriesDataForWifiHealthPerMinute(userEquipment, HOURLY_DATA);
        if (!dbObjectList.isEmpty()) {
            ListIterator<Map<String, Object>> listItr = dbObjectList.listIterator(dbObjectList.size());
            while (listItr.hasPrevious()) {
                Map<String, Object> wifiHealthRecord = listItr.previous();
                if (Objects.nonNull(wifiHealthRecord.get("timestamp"))) {
                    WifiHealthPerMinuteDTO.WifiHealthPerMinuteData wifiHealthPerMinuteData = wifiHealthPerMinuteDTO.new WifiHealthPerMinuteData();
                    wifiHealthPerMinuteData.setValue(Objects.isNull(wifiHealthRecord.get("score")) ? 0.0d : Double.valueOf(wifiHealthRecord.get("score").toString()));
                    wifiHealthPerMinuteData.setTimestamp(Long.valueOf(wifiHealthRecord.get("timestamp").toString()));
                    wifiHealthPerMinuteDTOList.add(wifiHealthPerMinuteData);
                }
            }
            wifiHealthPerMinuteDTOList.sort(Comparator.comparingLong(WifiHealthPerMinuteDTO.WifiHealthPerMinuteData::getTimestamp));
        }
        if (wifiHealthPerMinuteDTOList.isEmpty()) {
            WifiHealthPerMinuteDTO.WifiHealthPerMinuteData wifiHealthPerMinuteData = wifiHealthPerMinuteDTO.new WifiHealthPerMinuteData();
            wifiHealthPerMinuteDTOList.add(wifiHealthPerMinuteData);
        }


        wifiHealthPerMinuteDTO.setData(wifiHealthPerMinuteDTOList);
        return wifiHealthPerMinuteDTO;
    }
}
