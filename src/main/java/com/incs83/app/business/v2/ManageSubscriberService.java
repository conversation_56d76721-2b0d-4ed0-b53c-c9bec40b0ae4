/*
package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.Transactional;
import com.incs83.app.common.v2.CreateSubscriberRequest;
import com.incs83.app.common.v2.EditEquipmentRequest;
import com.incs83.app.common.v2.RGWPaginationRequest;
import com.incs83.app.common.v3.TechnicianDashboardDTO;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.queries.ClusterInfoSQL;
import com.incs83.app.constants.queries.GroupISPSQL;
import com.incs83.app.constants.queries.UserAPSQL;
import com.incs83.app.constants.queries.UserSQL;
import com.incs83.app.entities.*;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.Equipment.RGWDisconnectedDTO;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberDetailDTO;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberListDTO;
import com.incs83.app.responsedto.v2.serviceStats.ResourceSpecification.DeviceDetailInfoDTO;
import com.incs83.app.service.components.HttpServiceImpl;
import com.incs83.app.service.components.MailService;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.business.ESService;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.context.ExecutionContext;
import com.incs83.dto.ESRequest;
import com.incs83.dto.ElasticSearchDTO;
import com.incs83.dto.SubscriberESDTO;
import com.incs83.enums.Gender;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.pubsub.MQTTService;
import com.incs83.service.CommonService;
import com.incs83.service.ESServiceImpl;
import com.incs83.service.S3Service;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.PersistenceException;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.Constants.ESConstants.*;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.END_USER_ROLE_ID;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.misc.ApplicationConstants.SPACE;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.queries.ClusterInfoSQL.MAPPING_OF_CLUSTER_AND_AP;
import static com.incs83.app.constants.queries.ClusterInfoSQL.MAPPING_OF_CLUSTER_AND_EQUIPMENT;
import static com.incs83.app.constants.queries.EquipmentSQL.*;
import static com.incs83.constants.ApplicationCommonConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.DEFAULT;


Created by Jayant on 29/1/18.



@Service
public class ManageSubscriberService {

    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private IAMServices IAMServices;
    @Autowired
    private MailService mailService;

    @Autowired
    private ManageProfileService manageProfileService;

    @Autowired
    private BCryptPasswordEncoder encoder;

    @Autowired
    private ESService esService;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private ESServiceImpl esServiceImpl;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private MQTTService mqttService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private MongoServiceImpl mongoServiceimpl;

    @Value("${elastic-search.enable}")
    private Boolean elasticSearchEnable;

    private ObjectMapper objectMapper = new ObjectMapper();

    {
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private String isNullString(String data) {
        return data.equals("null") ? null : data;
    }

    public long getEquipmentLastReportedAt(String apId) {
        long lastReportedAt = 0;
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", apId);
        queryParams.put("serialNumber", apId);

        DBObject aPDetails = mongoServiceimpl.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(aPDetails)) {
            lastReportedAt = Objects.isNull(aPDetails.get("timestamp")) ? 0 : Long.valueOf(aPDetails.get("timestamp").toString());
        }

        return lastReportedAt;
    }

    @Deprecated
    public SubscriberListDTO.SubscriberData getAllSubscribers(Integer max, Integer offset, String order, String sortBy, boolean isManageSubscriber) throws Exception {

        GetSubscribersFromES getSubscribersFromES = new GetSubscribersFromES(max, offset, order, sortBy).invoke();
        SubscriberListDTO response = getSubscribersFromES.getSubscriberListDTO();
        if (getSubscribersFromES.is() && response.getData().getObjects().size() > 0) return response.getData();

        if (order.equals("ASC") || order.equals("DESC")) {

            if (offset < 0)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");

            String replaceWith = "Order By name ASC" + " LIMIT " + (Objects.nonNull(offset) ? offset : 0) + ", " + (Objects.nonNull(max) ? max : 10);

            String query;
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            if (!isManageSubscriber && !CommonUtils.isSysAdmin() && compartment.isProcessed()) {
                query = String.format(UserAPSQL.GET_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), "Where ua.processed is true " + replaceWith);
            } else {
                query = String.format(UserAPSQL.GET_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), replaceWith);
            }
            List<Object[]> subscribers = dataAccessService.readNative(query, new HashMap<>());

            SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
            SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();
            ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();

            if (Objects.nonNull(subscribers) && !subscribers.isEmpty()) {
                for (Object subscriberDetail[] : subscribers) {
                    SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();

                    subscriberData.setName(isNullString(String.valueOf(subscriberDetail[0])) + SPACE + isNullString(String.valueOf(subscriberDetail[1])));
                    subscriberData.setEmail(isNullString(String.valueOf(subscriberDetail[2])));
//                    subscriberData.setAlexaVoiceSubscribed(Boolean.valueOf(String.valueOf(subscriberDetail[3])));
//                    subscriberData.setGoogleHomeVoiceSubscribed(Boolean.valueOf(String.valueOf(subscriberDetail[4])));
//                    subscriberData.setMobileSubscribed(Boolean.valueOf(String.valueOf(subscriberDetail[5])));
                    subscriberData.setDownLinkRate(Double.valueOf(String.valueOf(subscriberDetail[6])));
//                    subscriberData.setStaticRGW(Boolean.valueOf(String.valueOf(subscriberDetail[7])));
                    subscriberData.setPhoneNo(isNullString(String.valueOf(subscriberDetail[8])));
                    subscriberData.setGlobalAccountNo(isNullString(String.valueOf(subscriberDetail[9])));
//                    subscriberData.setAlexaEmail(isNullString(String.valueOf(subscriberDetail[10])));
//                    subscriberData.setGoogleHomeEmail(isNullString(String.valueOf(subscriberDetail[11])));
                    subscriberData.setRgwMAC(isNullString(String.valueOf(subscriberDetail[12])));
                    subscriberData.setSubscriberId(isNullString(String.valueOf(subscriberDetail[13])));
                    subscriberData.setSerialNumber(isNullString(String.valueOf(subscriberDetail[14])));
                    subscriberData.setCamsAccountNo(isNullString(String.valueOf(subscriberDetail[15])));
//                    subscriberData.setLastReportedAt(getEquipmentLastReportedAt(subscriberData.getSerialNumber()));
                    subscriberDataList.add(subscriberData);
                }
            }
            if (subscriberDataList.isEmpty()) {
                SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();
                subscriberDataList.add(subscriberData);
            }

            HashMap<String, Object> queryParam = new HashMap<>();
            String queries = UserAPSQL.COUNT_TOTAL_SUBSCRIBER;
            if (!CommonUtils.isSysAdmin() && compartment.isProcessed()) {
                queries = queries.replaceAll("###", "Where processed is true and groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
            } else if (!CommonUtils.isSysAdmin()) {
                queries = queries.replaceAll("###", "Where groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
            } else {
                queries = queries.replaceAll("###", "");
            }

            List<Object> count = (List<Object>) dataAccessService.readNative(queries, queryParam);

            Collections.sort(subscriberDataList);
            data.setObjects(subscriberDataList);
            data.setTotalCount(Long.valueOf(Objects.nonNull(count) ? count.get(0).toString() : "0"));
            subscriberListDTO.setData(data);

            return subscriberListDTO.getData();
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Allowed values for order - ASC,DESC");
        }
    }


    public SubscriberDetailDTO.SubscriberDetail findSubscriberByIdOrSerial(String idOrSerial) throws Exception {
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        if (ValidationUtil.validateMAC(idOrSerial))
            idOrSerial = manageCommonService.getAPIDFromMAC(idOrSerial);
        UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(idOrSerial);

        String actualISP = manageCommonService.getIspOfSubscriber(userAP.getApId());

        User user = (User) dataAccessService.read(User.class, userAP.getUserId());

        SubscriberDetailDTO subscriberDetailDTO = new SubscriberDetailDTO();
        SubscriberDetailDTO.SubscriberDetail subscriberDetail = subscriberDetailDTO.new SubscriberDetail();

        subscriberDetail.setSubscriberId(user.getId());
        subscriberDetail.setSerialNumber(userAP.getApId());
        subscriberDetail.setEmail(isNullString(user.getEmail()));
        subscriberDetail.setName((Objects.nonNull(isNullString(user.getFirstName())) ? user.getFirstName() : EMPTY_STRING) + SPACE + (Objects.nonNull(isNullString(user.getLastName())) ? user.getLastName() : EMPTY_STRING));
        subscriberDetail.setPhoneNo(dataSecurityMapping.contains(DataSecurityType.phoneNumber.name()) ? manageCommonService.encrypt() : userAP.getPhoneNo());
        subscriberDetail.setGlobalAccountNo(dataSecurityMapping.contains(DataSecurityType.globalAccount.name()) ? manageCommonService.encrypt() : userAP.getGlobalAccountNo());
        subscriberDetail.setCamsAccountNo(dataSecurityMapping.contains(DataSecurityType.camsAccount.name()) ? manageCommonService.encrypt() : userAP.getCamsAccountNo());
        subscriberDetail.setRgwMAC((dataSecurityMapping.contains(DataSecurityType.macAddress.name())) ? manageCommonService.encrypt() : userAP.getRgwMAC());
        subscriberDetail.setDownLinkRate(userAP.getDownLinkRate());
        subscriberDetail.setUpLinkRate(userAP.getUpLinkRate());
        subscriberDetail.setLastReportedAt(getEquipmentLastReportedAt(userAP.getApId()));
        subscriberDetail.setIsp(actualISP);

        return subscriberDetail;
    }

    public ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> findSubscriberByIdOrSerial(List<String> idsOrSerials, boolean isId) throws Exception {
        ArrayList<String> userIdList = new ArrayList<>();
        HashMap<String, Object> userApMap = new HashMap<>();
        idsOrSerials.forEach(idOrSerial -> {
            try {
                UserAP userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(idOrSerial);
                userIdList.add(userAP.getUserId());
                userApMap.put(userAP.getUserId(), userAP);
            } catch (Exception e) {
                LOG.error("No Data Found...");
            }
        });

        List<User> users = new ArrayList<>();
        if (!userIdList.isEmpty()) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("userIds", userIdList);
            users = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_USER_IDS, params);

            if (!CommonUtils.isSysAdmin()) {
                users = users.parallelStream().filter(user -> CommonUtils.getGroupIdOfLoggedInUser().equals(user.getCompartment().iterator().next().getId())).collect(Collectors.toList());
            } else if (CommonUtils.isEndUser()) {
                users = users.parallelStream().filter(user -> CommonUtils.getUserIdOfLoggedInUser().equals(user.getId())).collect(Collectors.toList());
            }
        }

        if (users.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Subscriber found with " + (isId ? "Ids " : "Serials ") + idsOrSerials);

        SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
        SubscriberListDTO.SubscriberData subscriberData = subscriberListDTO.new SubscriberData();
        ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();

        users.parallelStream().forEach(records -> {
            UserAP userAP = (UserAP) userApMap.get(records.getId());
            if (Objects.nonNull(userAP)) {
                SubscriberListDTO.SubscriberData.SubscriberModel subscriberModel = subscriberData.new SubscriberModel();

                subscriberModel.setSerialNumber(userAP.getApId());
                subscriberModel.setRgwMAC(userAP.getRgwMAC());
                subscriberModel.setSubscriberId(records.getId());
                subscriberModel.setEmail(records.getEmail());
                if (Objects.nonNull(userAP.getPhoneNo())) {
                    subscriberModel.setPhoneNo(userAP.getPhoneNo());
                } else {
                    subscriberModel.setPhoneNo(null);
                }
                if (Objects.nonNull(userAP.getGlobalAccountNo()) && !String.valueOf(userAP.getGlobalAccountNo()).equals(EMPTY_STRING)) {
                    subscriberModel.setGlobalAccountNo(userAP.getGlobalAccountNo());
                } else {
                    subscriberModel.setGlobalAccountNo(null);
                }
                if (Objects.nonNull(userAP.getCamsAccountNo()) && !String.valueOf(userAP.getCamsAccountNo()).equals(EMPTY_STRING)) {
                    subscriberModel.setCamsAccountNo(userAP.getCamsAccountNo());
                } else {
                    subscriberModel.setCamsAccountNo(null);
                }
                subscriberModel.setName(records.getFirstName() + SPACE + records.getLastName());
                subscriberDataList.add(subscriberModel);
            }
        });
        if (subscriberDataList.isEmpty()) {
            SubscriberListDTO.SubscriberData.SubscriberModel subscriberModel = subscriberData.new SubscriberModel();
            subscriberDataList.add(subscriberModel);
        }

        return subscriberDataList;
    }

    public void createSubscriber(CreateSubscriberRequest createSubscriberRequest) throws Exception {
        if (Objects.nonNull(createSubscriberRequest.getEmail()) && (EMPTY_STRING.equals(createSubscriberRequest.getEmail().trim()) || !createSubscriberRequest.getEmail().trim().contains("@")))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Email address :: " + createSubscriberRequest.getEmail());

        if (Objects.isNull(createSubscriberRequest.getPhoneNo()) || EMPTY_STRING.equals(createSubscriberRequest.getPhoneNo().trim())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Phone Number");
        }

        if (Objects.nonNull(createSubscriberRequest.getSerialNumber()) && createSubscriberRequest.getSerialNumber().trim().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial Number cannot be empty");
        }

        if (Objects.nonNull(createSubscriberRequest.getRgwMAC()) && createSubscriberRequest.getRgwMAC().trim().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "MAC address cannot be empty");
        }

        String ispId = manageCommonService.getIspByName(createSubscriberRequest.getIsp());
        if (Objects.isNull(ispId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), createSubscriberRequest.getIsp() + " does not exist in the system.");

        Set<Compartment> compartmentSet = new HashSet<>();
        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", ispId);
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);
        if (Objects.nonNull(compartmentList) && !compartmentList.isEmpty())
            compartmentSet.add(compartmentList.get(0));
        if (compartmentSet.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group does not exists for the ISP : " + createSubscriberRequest.getIsp());

        String[] isp = createSubscriberRequest.getIsp().split(HYPHEN_STRING);

        if (Objects.nonNull(isp[0]) && !EMPTY_STRING.equals(isp[0].trim()) && !WINDSTREAM.equalsIgnoreCase(isp[0])) {
            if ((Objects.nonNull(createSubscriberRequest.getGlobalAccountNo()) && !EMPTY_STRING.equals(createSubscriberRequest.getGlobalAccountNo().trim())) || (Objects.nonNull(createSubscriberRequest.getCamsAccountNo())) && !EMPTY_STRING.equals(createSubscriberRequest.getCamsAccountNo().trim())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Global AccountNo and Cams AccountN is not valid for the subscriber");
            }
        }

        HashMap<String, Object> query = new HashMap<>();

        if (Objects.nonNull(createSubscriberRequest.getSerialNumber())) {
            query.clear();
            query.put("apId", createSubscriberRequest.getSerialNumber());
            Iterable<UserAP> userAPs = dataAccessService.read(UserAP.class, UserAPSQL.GET_PLATFORM_USER_ID_FROM_AP_ID, query);
            if (userAPs.iterator().hasNext()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Subscriber already exists with Serial Number : %s", createSubscriberRequest.getSerialNumber()));
            }
        }

        if (Objects.nonNull(createSubscriberRequest.getRgwMAC())) {
            query.clear();
            query.put("rgwMAC", createSubscriberRequest.getRgwMAC());
            Iterable<UserAP> userAPs = dataAccessService.read(UserAP.class, UserAPSQL.GET_USER_AP_FROM_RGW_MAC, query);
            if (userAPs.iterator().hasNext()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Subscriber already exists with MAC address : %s", createSubscriberRequest.getRgwMAC()));
            }
        }

        if (WINDSTREAM.equalsIgnoreCase(isp[0]) && (Objects.isNull(createSubscriberRequest.getGlobalAccountNo()) || createSubscriberRequest.getGlobalAccountNo().isEmpty())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Global Account Number cannot be empty or null"));
        }


        if (Objects.nonNull(createSubscriberRequest.getGlobalAccountNo())) {
            if (createSubscriberRequest.getGlobalAccountNo().trim().equals(EMPTY_STRING))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Global Account Number cannot be empty"));
            query.clear();
            query.put("globalAccountNo", createSubscriberRequest.getGlobalAccountNo());
            Iterable<UserAP> userAPs = dataAccessService.read(UserAP.class, UserAPSQL.GET_USER_AP_BY_ACCOUNT_NUMBER, query);
            if (userAPs.iterator().hasNext()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Subscriber already exists with Global Account Number : %s", createSubscriberRequest.getGlobalAccountNo()));
            }
        }


        if (Objects.nonNull(createSubscriberRequest.getPhoneNo())) {
            query.clear();
            query.put("phoneNo", createSubscriberRequest.getPhoneNo());
            Iterable<UserAP> userAPs = dataAccessService.read(UserAP.class, UserAPSQL.GET_USER_AP_BY_PHONE_NUMBER, query);
            if (userAPs.iterator().hasNext()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Subscriber already exists with Phone No : %s", createSubscriberRequest.getPhoneNo()));
            }
        }

        if (Objects.nonNull(createSubscriberRequest.getEmail()) && !createSubscriberRequest.getEmail().equals(EMPTY_STRING)) {
            query.clear();
            query.put("email", createSubscriberRequest.getEmail());
            List<User> userList = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_EMAIL, query);
            if (Objects.nonNull(userList) && !userList.isEmpty()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Subscriber/User already exists with email : %s", createSubscriberRequest.getEmail()));
            }
        }

        User user = generateUserInstance(createSubscriberRequest, compartmentSet);
        UserAP userAP = generateUserApInstance(createSubscriberRequest, user);

        ClusterInfo clusterInfo = user.getCompartment().iterator().next().getCluster().stream().filter(cluster -> cluster.isDefaultCluster()).findAny().orElse(null);
        if (Objects.nonNull(clusterInfo)) {
            HashMap<String, Object> param = new HashMap<>();
            param.put("clusterId", clusterInfo.getId());
            param.put("userId", user.getId());

            dataAccessService.createSubscriber(MAPPING_OF_CLUSTER_AND_AP, param, user, userAP);
        } else {
            if (dataAccessService.create(user)) {
                dataAccessService.create(userAP);
            }
        }

        if (elasticSearchEnable) {
            try {
                HashMap<String, Object> data = esServiceImpl.prepareDataForES(user, true);
                esService.insert(USER_INDEX, data, String.valueOf(data.get("id")), null);
                data = esServiceImpl.prepareDataForES(userAP, user);
                esService.insert(USERAP_INDEX, data, String.valueOf(data.get("id")), null);
            } catch (Exception e) {
                LOG.error("Error in >>>>ELASTIC SEARCH<<<<< insert (insertToMysqlAndElastic).");
            }
        }

        HashMap<String, Object> apDetail = new HashMap<>();
        apDetail.put("userId", createSubscriberRequest.getSerialNumber());
        apDetail.put("serialNumber", createSubscriberRequest.getSerialNumber());
        apDetail.put("macAddress", createSubscriberRequest.getRgwMAC());
        apDetail.put("type", GATEWAY);
        apDetail.put("isp", createSubscriberRequest.getIsp());
        apDetail.put("mysqlProcessed", true);
        apDetail.put("esProcessed", true);
        apDetail.put("processed", true);

        mongoServiceimpl.create(AP_DETAIL, apDetail);
    }


    public void createSubscriber(CreateSubscriberRequest createSubscriberRequest) throws Exception {

        if (Objects.nonNull(createSubscriberRequest.getEmail()) && (EMPTY_STRING.equals(createSubscriberRequest.getEmail().trim()) || !createSubscriberRequest.getEmail().trim().contains("@")))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Email address :: " + createSubscriberRequest.getEmail());

        if (Objects.isNull(createSubscriberRequest.getPhoneNo()) || EMPTY_STRING.equals(createSubscriberRequest.getPhoneNo().trim())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Phone Number");
        }

        if (Objects.nonNull(createSubscriberRequest.getSerialNumber()) && createSubscriberRequest.getSerialNumber().trim().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial Number cannot be empty");
        }

        if (Objects.nonNull(createSubscriberRequest.getRgwMAC()) && createSubscriberRequest.getRgwMAC().trim().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "MAC address cannot be empty");
        }

        String ispId = manageCommonService.getIspByName(createSubscriberRequest.getIsp());
        if (Objects.isNull(ispId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), createSubscriberRequest.getIsp() + " does not exist in the system.");

        Set<Compartment> compartmentSet = new HashSet<>();
        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", ispId);
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);
        if (Objects.nonNull(compartmentList) && !compartmentList.isEmpty())
            compartmentSet.add(compartmentList.get(0));
        if (compartmentSet.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group does not exists for the ISP : " + createSubscriberRequest.getIsp());

        String[] isp = createSubscriberRequest.getIsp().split(HYPHEN_STRING);

        if (Objects.nonNull(isp[0]) && !EMPTY_STRING.equals(isp[0].trim()) && !WINDSTREAM.equalsIgnoreCase(isp[0])) {
            if ((Objects.nonNull(createSubscriberRequest.getGlobalAccountNo()) && !EMPTY_STRING.equals(createSubscriberRequest.getGlobalAccountNo().trim())) || (Objects.nonNull(createSubscriberRequest.getCamsAccountNo())) && !EMPTY_STRING.equals(createSubscriberRequest.getCamsAccountNo().trim())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Global AccountNo and Cams AccountN is not valid for the subscriber");
            }
        }

        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", 0);
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("serialNumber", createSubscriberRequest.getSerialNumber());
        queryParams.put("userID", createSubscriberRequest.getSerialNumber());
        List<DBObject> subscriberList = mongoServiceimpl.findList(queryParams, AP_DETAIL, projection);

        if (Objects.isNull(subscriberList) || (Objects.nonNull(subscriberList) && subscriberList.isEmpty())) {
            HashMap<String, Object> apDetail = new HashMap<>();
            apDetail.put("userId", createSubscriberRequest.getSerialNumber());
            apDetail.put("serialNumber", createSubscriberRequest.getSerialNumber());
            apDetail.put("macAddress", createSubscriberRequest.getRgwMAC());
            apDetail.put("type", GATEWAY);
            apDetail.put("isp", createSubscriberRequest.getIsp());
            apDetail.put("processed", true);   // doubt . it should may be in next section
            mongoServiceimpl.create(AP_DETAIL, apDetail);
        }

        Subscriber subscriber = generateSubscriberInstance(createSubscriberRequest, compartmentSet);
        Equipment equipment = generateEquipmentInstance(createSubscriberRequest, subscriber);
        subscriber.setEquipment(new HashSet<>(Arrays.asList(equipment)));

        try {
            if (dataAccessService.create(Subscriber.class, subscriber)) {
                ClusterInfo clusterInfo = subscriber.getCompartment().iterator().next().getCluster().stream().filter(cluster -> cluster.isDefaultCluster()).findAny().orElse(null);
                if (Objects.nonNull(clusterInfo)) {
                    HashMap<String, Object> param = new HashMap<>();
                    param.put("clusterId", clusterInfo.getId());
                    param.put("equipmentId", subscriber.getId());
                    dataAccessService.createSubscriber(MAPPING_OF_CLUSTER_AND_EQUIPMENT, param, subscriber, equipment);
                }

                BasicDBObject record = new BasicDBObject();
                record.put("mysqlProcessed", true);
                DBObject update = new BasicDBObject("$set", record);
                DBObject query = new BasicDBObject("userId", createSubscriberRequest.getSerialNumber());
                try {
                    mongoServiceimpl.update(query, update, true, false, ApplicationCommonConstants.AP_DETAIL);
                } catch (Exception e) {
                    LOG.error("Error in updating mysqlProcessed true. AP ID ::" + createSubscriberRequest.getSerialNumber());
                }

                if (elasticSearchEnable) {
                    try {
                        HashMap<String, Object> equipmentMap = prepareDataForES2(equipment);
                        esService.insert(USERAP_INDEX, equipmentMap, String.valueOf(equipmentMap.get("id")), null);
                        record = new BasicDBObject();
                        record.put("esProcessed", true);
                        update = new BasicDBObject("$set", record);
                        query = new BasicDBObject("userId", createSubscriberRequest.getSerialNumber());
                        mongoServiceimpl.update(query, update, true, false, ApplicationCommonConstants.AP_DETAIL);
                    } catch (Exception e) {
                        LOG.error("Error in updating esProcessed true. AP ID ::" + equipment.getRgwSerial());
                    }
                }
            }
        } catch (PersistenceException e) {
            LOG.error("SQL Constraint Violation. RGW :: " + equipment.getRgwSerial());
        } catch (Exception e) {
            LOG.error("Error in creating subscriber (insertToMysqlAndElastic) :: " + e.getMessage());
        }

    }


    private HashMap<String, Object> prepareDataForES2(Equipment equipment) {
        List<String> clusters = new ArrayList<>();
        HashMap<String, Object> userAPMap = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());
            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster() && cluster.getName().equals(DEFAULT + compartment.getName())).findAny().orElse(null);
            if (Objects.nonNull(clusterInfo)) {
                clusters.add(clusterInfo.getId());
            }

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", equipment.getRgwSerial());
            HashMap<String, Object> projection = new HashMap<>();
            projection.put("type", 1);
            projection.put("serialNumber", 1);
            projection.put("macAddress", 1);
            List<HashMap<String, String>> apDetails = mongoServiceimpl.findList(queryParams, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, projection);
            List<Map<String, Object>> extInfo = new ArrayList<>();
            apDetails.stream().filter(ap -> !ApplicationCommonConstants.GATEWAY.equals(ap.get("type"))).forEach(elem -> {
                Map<String, Object> extender = new HashMap<>();
                extender.put(EXT_MAC, elem.get("macAddress"));
                extender.put(EXT_SERIAL, elem.get("serialNumber"));
                extInfo.add(extender);
            });

            userAPMap = new HashMap<>();
            userAPMap.put("id", equipment.getId());
            userAPMap.put("name", equipment.getRgwSerial() + ApplicationCommonConstants.EMPTY_STRING + equipment.getRgwMAC());
            userAPMap.put("createdAt", equipment.getCreatedAt());
            userAPMap.put("createdBy", equipment.getCreatedBy());
            userAPMap.put("email", equipment.getRgwSerial() + "@optim.com");
            userAPMap.put("subscriberId", equipment.getServiceTelephoneNo());
            userAPMap.put("mobileSubscribed", false);
            userAPMap.put("downLinkRate", equipment.getDownLinkRate());
            userAPMap.put("upLinkRate", equipment.getDownLinkRate());
            userAPMap.put("upLinkRate", equipment.getUpLinkRate());
            userAPMap.put("rgwMAC", equipment.getRgwMAC());
            userAPMap.put("apId", equipment.getRgwSerial());
            userAPMap.put("serialNumber", equipment.getRgwSerial());
            userAPMap.put("groupId", equipment.getGroupId());
            userAPMap.put("phoneNo", equipment.getServiceTelephoneNo());
            userAPMap.put("globalAccountNo", null);
            userAPMap.put("camsAccountNo", null);
            userAPMap.put("extInfo", extInfo);
            userAPMap.put("clusters", clusters);
            userAPMap.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
        } catch (Exception e) {
            LOG.error("ERROR in prepare data for ES.");
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }
        return userAPMap;
    }


    private Subscriber generateSubscriberInstance(CreateSubscriberRequest createSubscriberRequest, Set<Compartment> compartmentSet) throws Exception {

        Subscriber subscriber = new Subscriber();
        subscriber.setId(CommonUtils.generateUUID());
        if (Objects.nonNull(createSubscriberRequest.getFirstName()) && !EMPTY_STRING.equals(createSubscriberRequest.getFirstName())) {
            subscriber.setFirstName(createSubscriberRequest.getFirstName());
        } else {
            subscriber.setFirstName(createSubscriberRequest.getSerialNumber());
        }

        if (Objects.nonNull(createSubscriberRequest.getLastName()) && !EMPTY_STRING.equals(createSubscriberRequest.getLastName())) {
            subscriber.setLastName(createSubscriberRequest.getLastName());
        } else {
            subscriber.setLastName(createSubscriberRequest.getRgwMAC());
        }

        if (Objects.nonNull(createSubscriberRequest.getEmail())) {
            subscriber.setEmail(createSubscriberRequest.getEmail());
        } else {
            subscriber.setEmail(createSubscriberRequest.getSerialNumber() + "@optim.com");
        }

        if (Objects.nonNull(createSubscriberRequest.getGlobalAccountNo()) && !createSubscriberRequest.getGlobalAccountNo().trim().isEmpty())
            subscriber.setGlobalAccountNo(createSubscriberRequest.getGlobalAccountNo());
        if (Objects.nonNull(createSubscriberRequest.getCamsAccountNo()) && !createSubscriberRequest.getCamsAccountNo().trim().isEmpty())
            subscriber.setCamsAccountNo(createSubscriberRequest.getCamsAccountNo());
        if (Objects.nonNull(createSubscriberRequest.getPhoneNo()) && !createSubscriberRequest.getPhoneNo().isEmpty())
            subscriber.setPhoneNo(createSubscriberRequest.getPhoneNo());

        subscriber.setCompartment(compartmentSet);
        subscriber.setSubscriberRole(getEndSubscriberRole());
        subscriber.setPassword(USER_PASSWORD);
        CommonUtils.setCreateEntityFields(subscriber);
        return subscriber;
    }

    private Set<SubscriberRole> getEndSubscriberRole() throws Exception {
        Role role = (Role) dataAccessService.read(Role.class, END_USER_ROLE_ID);
        SubscriberRole subscriberRole = new SubscriberRole();
        subscriberRole.setId(CommonUtils.generateUUID());
        subscriberRole.setRole(role);
        subscriberRole.setGrantBy(UserRole.RoleType.DIRECT.name());
        CommonUtils.setCreateEntityFields(subscriberRole);

        Set<SubscriberRole> subscriberRoleMapping = new HashSet<>();
        subscriberRoleMapping.add(subscriberRole);

        return subscriberRoleMapping;
    }

    private Set<UserRole> getEndUserRole() throws Exception {
        Role role = (Role) dataAccessService.read(Role.class, END_USER_ROLE_ID);
        UserRole userRole = new UserRole();
        userRole.setId(CommonUtils.generateUUID());
        userRole.setRole(role);
        userRole.setGrantBy(UserRole.RoleType.DIRECT.name());
        CommonUtils.setCreateEntityFields(userRole);

        Set<UserRole> userRoleMapping = new HashSet<>();
        userRoleMapping.add(userRole);

        return userRoleMapping;
    }

    private User generateUserInstance(CreateSubscriberRequest createSubscriberRequest, Set<Compartment> compartmentSet) throws Exception {
        User user = new User();
        user.setId(CommonUtils.generateUUID());
        if (Objects.nonNull(createSubscriberRequest.getFirstName()) && !EMPTY_STRING.equals(createSubscriberRequest.getFirstName())) {
            user.setFirstName(createSubscriberRequest.getFirstName());
        } else {
            user.setFirstName(createSubscriberRequest.getSerialNumber());
        }

        if (Objects.nonNull(createSubscriberRequest.getLastName()) && !EMPTY_STRING.equals(createSubscriberRequest.getLastName())) {
            user.setLastName(createSubscriberRequest.getLastName());
        } else {
            user.setLastName(createSubscriberRequest.getRgwMAC());
        }

        if (Objects.nonNull(createSubscriberRequest.getEmail())) {
            user.setEmail(createSubscriberRequest.getEmail());
        } else {
            user.setEmail(createSubscriberRequest.getSerialNumber() + "@optim.com");
        }

        user.setCompartment(compartmentSet);
        user.setUserRole(getEndUserRole());

        user.setCompany(createSubscriberRequest.getIsp());
        user.setGender(Gender.MALE);
        user.setTitle("SUBSCRIBER");
        user.setPassword(USER_PASSWORD);

        user.setMobilePhone(null);
        user.setVerify(true);
        user.setInternalUser(true);
        user.setAccountNonExpired(true);
        user.setAccountNonLocked(true);
        user.setCredNonExpired(true);
        user.setEnabled(true);
        user.setAlexaEmail(null);
        user.setGoogleHomeEmail(null);

        CommonUtils.setCreateEntityFields(user);

        return user;
    }

    private Equipment generateEquipmentInstance(CreateSubscriberRequest createSubscriberRequest, Subscriber subscriber) {
        Equipment equipment = new Equipment();
        if (Objects.nonNull(createSubscriberRequest.getRgwMAC()) && !createSubscriberRequest.getRgwMAC().trim().isEmpty())
            equipment.setRgwMAC(createSubscriberRequest.getRgwMAC());
        if (Objects.nonNull(createSubscriberRequest.getSerialNumber()) && !createSubscriberRequest.getSerialNumber().trim().isEmpty())
            equipment.setRgwSerial(createSubscriberRequest.getSerialNumber());
        equipment.setServiceTelephoneNo(equipment.getServiceTelephoneNo());
        equipment.setGroupId(subscriber.getCompartment().iterator().next().getId());
        equipment.setDownLinkRate(1000);
        equipment.setUpLinkRate(1000);
        CommonUtils.setCreateEntityFields(equipment);
        return equipment;
    }


    public void updateSubscriber(String subscriberId, EditEquipmentRequest editSubscriberRequest) throws Exception {
        if (Objects.nonNull(editSubscriberRequest.getEmail()) && (EMPTY_STRING.equals(editSubscriberRequest.getEmail().trim()) || !editSubscriberRequest.getEmail().trim().contains("@")))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Email address :: " + editSubscriberRequest.getEmail());

        if (Objects.nonNull(editSubscriberRequest.getPhoneNo()) && EMPTY_STRING.equals(editSubscriberRequest.getPhoneNo().trim())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "PhoneNo cannot be empty");
        }

        if (ValidationUtil.validateMAC(subscriberId))
            subscriberId = manageCommonService.getAPIDFromMAC(subscriberId);
        UserAP userAP = manageCommonService.getUserAPFromSubscriberId(subscriberId);
        String actualISP = manageCommonService.getIspOfSubscriber(userAP.getApId());
        String[] isp = actualISP.split(HYPHEN_STRING);
        if (Objects.nonNull(isp[0]) && !EMPTY_STRING.equals(isp[0].trim()) && !WINDSTREAM.equals(isp[0])) {
            if ((Objects.nonNull(editSubscriberRequest.getGlobalAccountNo()) && !EMPTY_STRING.equals(editSubscriberRequest.getGlobalAccountNo().trim())) || (Objects.nonNull(editSubscriberRequest.getCamsAccountNo())) && !EMPTY_STRING.equals(editSubscriberRequest.getCamsAccountNo().trim())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Global AccountNo and Cams AccountN is not valid for the subscriber");
            }
        }


        HashMap<String, Object> query = new HashMap<>();
        if (Objects.nonNull(editSubscriberRequest.getPhoneNo()) && !editSubscriberRequest.getPhoneNo().equals(userAP.getPhoneNo())) {
            query.clear();
            query.put("phoneNo", editSubscriberRequest.getPhoneNo());
            Iterable<UserAP> userAPs = dataAccessService.read(UserAP.class, UserAPSQL.GET_USER_AP_BY_PHONE_NUMBER, query);
            if (userAPs.iterator().hasNext() && !userAPs.iterator().next().getApId().equals(userAP.getApId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Subscriber already exists with Phone No : %s", editSubscriberRequest.getPhoneNo()));
            }
        }

        User user = (User) dataAccessService.read(User.class, userAP.getUserId());

        if (Objects.nonNull(editSubscriberRequest.getEmail()) && !editSubscriberRequest.getEmail().equals(EMPTY_STRING) && !editSubscriberRequest.getEmail().equals(user.getEmail())) {
            query.clear();
            query.put("email", editSubscriberRequest.getEmail());
            List<User> userList = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_EMAIL, query);
            if (Objects.nonNull(userList) && !userList.isEmpty()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), String.format("Subscriber/User already exists with email : %s", editSubscriberRequest.getEmail()));
            }
            user.setEmail(editSubscriberRequest.getEmail());
        }

        if (Objects.nonNull(editSubscriberRequest.getCamsAccountNo()) && !EMPTY_STRING.equals(editSubscriberRequest.getCamsAccountNo().trim())) {
            userAP.setCamsAccountNo(editSubscriberRequest.getCamsAccountNo());
        } else if (Objects.nonNull(editSubscriberRequest.getCamsAccountNo()) && EMPTY_STRING.equals(editSubscriberRequest.getCamsAccountNo().trim())) {
            userAP.setCamsAccountNo(null);
        }
        if (Objects.nonNull(editSubscriberRequest.getGlobalAccountNo()) && !EMPTY_STRING.equals(editSubscriberRequest.getGlobalAccountNo().trim())) {
            userAP.setGlobalAccountNo(editSubscriberRequest.getGlobalAccountNo());
        } else if (Objects.nonNull(editSubscriberRequest.getGlobalAccountNo()) && EMPTY_STRING.equals(editSubscriberRequest.getGlobalAccountNo().trim())) {
            userAP.setGlobalAccountNo(null);
        }
        if (Objects.nonNull(editSubscriberRequest.getPhoneNo()) && !EMPTY_STRING.equals(editSubscriberRequest.getPhoneNo().trim())) {
            userAP.setPhoneNo(editSubscriberRequest.getPhoneNo());
        }

        if (Objects.nonNull(editSubscriberRequest.getDownLinkRate())) {
            userAP.setDownLinkRate(editSubscriberRequest.getDownLinkRate());
        }

        if (Objects.nonNull(editSubscriberRequest.getUpLinkRate())) {
            userAP.setUpLinkRate(editSubscriberRequest.getUpLinkRate());
        }

        if (Objects.nonNull(editSubscriberRequest.getFirstName()) && !EMPTY_STRING.equals(editSubscriberRequest.getFirstName())) {
            user.setFirstName(editSubscriberRequest.getFirstName());
        } else if (Objects.nonNull(editSubscriberRequest.getFirstName()) && EMPTY_STRING.equals(editSubscriberRequest.getFirstName())) {
            editSubscriberRequest.setFirstName(null);
        }

        if (Objects.nonNull(editSubscriberRequest.getLastName()) && !EMPTY_STRING.equals(editSubscriberRequest.getLastName())) {
            user.setLastName(editSubscriberRequest.getLastName());
        } else if (Objects.nonNull(editSubscriberRequest.getLastName()) && EMPTY_STRING.equals(editSubscriberRequest.getLastName())) {
            user.setLastName(null);
        }

        dataAccessService.update(User.class, user);
        userAP = (UserAP) dataAccessService.update(UserAP.class, userAP);
        esService.updateById(USERAP_INDEX, esServiceImpl.prepareDataForES(userAP, user), userAP.getUserId(), null);
        esService.updateById(USER_INDEX, esServiceImpl.prepareDataForES(user, true), user.getId(), null);
    }

    @Transactional
    public void deleteSubscriber(String subscriberId) throws Exception {
        String userAlexaEmail = null;
        String userGoogleHomeEmail = null;

        if (ValidationUtil.validateMAC(subscriberId))
            subscriberId = manageCommonService.getAPIDFromMAC(subscriberId);

        UserAP userAP = manageCommonService.getUserAPFromSubscriberId(subscriberId);

        if (subscriberId.equals(CommonUtils.getUserIdOfLoggedInUser()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "You are not authorized to delete yourself");

        User user = (User) dataAccessService.read(User.class, subscriberId);
        if (Objects.nonNull(user)) {
            userAlexaEmail = user.getAlexaEmail();
            userGoogleHomeEmail = user.getGoogleHomeEmail();
        }

        if (userAP.getAlexaVoiceSubscribed() && Objects.nonNull(userAlexaEmail) && !userAlexaEmail.equals(EMPTY_STRING)) {
            HashMap<String, String> params = new HashMap<>();
            params.put("userName", user.getFirstName() + SPACE + user.getLastName());
            mailService.sendAlexaUnSubscriptionEmail(user.getAlexaEmail(), params);
        }

        if (userAP.getGoogleHomeVoiceSubscribed() && Objects.nonNull(userGoogleHomeEmail) && !userGoogleHomeEmail.equals(EMPTY_STRING)) {
            HashMap<String, String> params = new HashMap<>();
            params.put("userName", user.getFirstName() + SPACE + user.getLastName());
            mailService.sendGoogleHomeUnSubscriptionEmail(user.getGoogleHomeEmail(), params);
        }

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userAP.getUserId());
        dataAccessService.deleteUpdateNative(ClusterInfoSQL.DELETE_USER_AP_FROM_CLUSTER_BY_USER_ID, query);

        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userAP.getUserId());
        dataAccessService.deleteUpdateNative(UserAPSQL.DELETE_USER_AP, params);

        deleteUserById(userAP.getUserId());

        Runnable deleteSubscriberAllData = () -> {
            try {
                deleteSubscriberProfile(userAP.getUserId());
                manageCommonService.disableSubscriberToAlexaAndGoogleHomeFeatures(userAP.getUserId());
                deleteSubscriberAPData(userAP.getApId());

            } catch (Exception e) {
                LOG.error("Error processing delete operation", e);
            }
        };
        new Thread(deleteSubscriberAllData).start();
        esService.delete(USERAP_INDEX, subscriberId);
    }

    private void deleteSubscriberProfile(String subscriberId) throws Exception {
        BasicDBObject projection = new BasicDBObject();
        projection.put("_class", 0);
        projection.put("_id", 0);
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("subscriberId", subscriberId);
        List<DBObject> profileObjList = mongoServiceimpl.findList(queryParams, USER_PROFILE, projection);
        if (Objects.nonNull(profileObjList) && !profileObjList.isEmpty()) {
            for (DBObject profile : profileObjList) {
                if (Objects.nonNull(profile.get("devices"))) {
                    Set<String> profileDevices = new HashSet<>((List<String>) profile.get("devices"));
                    if (!profileDevices.isEmpty()) {
                        DBObject profileObject = new BasicDBObject();

                        profileObject.put("subscriberId", subscriberId);
                        profileObject.put("devices", profileDevices);
                        if (!profile.get("currentState").toString().equalsIgnoreCase(PAUSE) ? false : true) {
                            manageProfileService.performProfilePauseUnPause(profile, UNPAUSE);
                        }
                        queryParams.clear();
                        queryParams.put("id", profile.get("id").toString());
                        mongoServiceimpl.deleteOne(queryParams, USER_PROFILE);
                    }
                }
            }
        }
    }

    private void deleteUserById(String userId) throws Exception {
        String query;
        HashMap<String, String> params = new HashMap<>();
        params.put("userId", userId);

        query = UserSQL.DELETE_USER_COMPARTMENT_BY_USER_ID;
        dataAccessService.deleteUpdateNative(query, params);

        query = UserSQL.DELETE_USER_ROLE_BY_ID;
        dataAccessService.deleteUpdateNative(query, params);

        User user = (User) dataAccessService.read(User.class, userId);
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("user", user);

        query = UserSQL.TICKET_USER_BY_USER;
        List<TicketUser> ticketUsers = (List<TicketUser>) dataAccessService.read(TicketUser.class, query, queryParam);
        if (!ticketUsers.isEmpty()) {
            for (TicketUser ticketUserDetails : ticketUsers) {
                ticketUserDetails.setAssignedBy(null);
                ticketUserDetails.setAssignedTo(null);

                dataAccessService.update(TicketUser.class, ticketUserDetails);
            }
        }

        dataAccessService.delete(User.class, userId);
        esService.delete(USER_INDEX, userId);
    }

    private void deleteSubscriberAPData(String userId) throws Exception {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", userId);

        mongoServiceimpl.deleteOne(queryParams, AP_DETAIL);
    }

    public void deleteSubscriber(List<String> subscriberIds) throws Exception {
        subscriberIds.forEach(subscriberId -> {
            try {
//                manageCommonService.checkUserAp(subscriberId);
                //TODO add permission to delete subscriber
                IAMServices.deleteUserById(subscriberId);
                HashMap<String, Object> params = new HashMap<>();
                params.put("userId", subscriberId);
                dataAccessService.deleteUpdateNative(UserAPSQL.DELETE_USER_AP, params);
            } catch (Exception e) {
                LOG.error("Error during delete subscriber", e);
            }
        });
    }

    private void checkActualISPMapToGroup(String ispName) throws Exception {
        String ispId = manageCommonService.getIspByName(ispName);
        if (Objects.isNull(ispId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP of this Equipment is not configured in the system. Please configure it first before creating a subscriber.");

        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
        if (Objects.nonNull(compartment)) {
            if (!ispId.equals(compartment.getIspId())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This Equipment does not belong to the ISP configured with your Group, hence cannot create Subscriber.");
            }
        }
    }

    public void subscriberBulkFileUpload(MultipartFile file, String isp) throws Exception {
        if (Objects.isNull(file) || file.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File cannot be empty");
        if (!manageCommonService.isCSVFile(file)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Content is not valid CSV");
        }

        int fileLength = file.getOriginalFilename().length();
        if (file.getOriginalFilename().indexOf(DOT) > -1)
            fileLength = file.getOriginalFilename().indexOf(DOT);

        String humanReadableFileName = file.getOriginalFilename().substring(0, fileLength) + UNDER_SCORE_STRING + Calendar.getInstance().getTimeInMillis() + ".csv";

        String fileNameAndPath = BULK_UPLOAD_SUB_DIR + "humanReadableFileName/" + humanReadableFileName;
        HashMap<String, String> uploadData = s3Service.uploadFile(file, fileNameAndPath);
        if (Objects.isNull(uploadData) || Objects.isNull(uploadData.get("secure_url")) || uploadData.get("secure_url").isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in upload file");
        }
        HashMap<String, Object> data = new HashMap<>();
        data.put("status", _SUBSCRIBER_BULK_STATUS_NEW);
        data.put("name", humanReadableFileName);
        data.put("url", uploadData.get("secure_url"));
        data.put("size", file.getSize());
        data.put("timestamp", CommonUtils.getCurrentTimeInMillis());
        data.put("isp", isp);
        data.put("createdBy", CommonUtils.getUserIdOfLoggedInUser());
        data.put("type", _FILE_TYPE);
        data.put("id", CommonUtils.generateUUID());
        data.put("totalRecord", manageCommonService.countNoOfRecord(file));
        data.put("noOfRecordUpdated", 0L);
        data.put("noOfRecordScanned", 0L);
        mongoServiceimpl.create(SUBSCRIBERS_DATA_FILE_DETAILS, data);
    }

    public List<DeviceDetailInfoDTO> getSubscriberStatus(String rgwSerialNumber) throws Exception {

        if (Objects.isNull(rgwSerialNumber) || rgwSerialNumber.trim().isEmpty()) {
            throw new ValidationException((HttpStatus.BAD_REQUEST.value()), "Invalid RGW Serial Number");
        }

        HashMap<String, String> headerInfo = new HashMap<>();

        HashMap<String, Object> queryParam = new HashMap<>();

        List<DeviceDetailInfoDTO> deviceDetailInfoDTO = new ArrayList<>();

        ObjectMapper mapper = new ObjectMapper();

        List<String> vmqUrlList = mqttService.getVmqUrls();
        if (vmqUrlList.isEmpty())
            return deviceDetailInfoDTO;

        String url = "http://" + vmqUrlList.get(0) + SUBSCRIBER_STATUS_API_PATH + rgwSerialNumber;

        String httpResponse = EMPTY_STRING;
        try {
            httpResponse = httpService.doGet(url, headerInfo, queryParam);
        } catch (Exception e) {
            LOG.error("Error while Fetching Resource Specification");
        }
        if (Objects.isNull(httpResponse) || httpResponse.trim().isEmpty()) {
            LOG.error("Invalid Response");
            throw new ValidationException((HttpStatus.BAD_REQUEST.value()), "Invalid URL");
        }

        HashMap response = mapper.readValue(httpResponse, HashMap.class);
        HashMap output = (HashMap) response.get("output");

        if (Objects.nonNull(output) && !output.isEmpty()) {
            if (Objects.nonNull(output.get("DeviceDetail"))) {
                if (output.get("DeviceDetail") instanceof String) {
                    return deviceDetailInfoDTO;
                }

                List<HashMap<String, String>> deviceDetailList = (List<HashMap<String, String>>) output.get("DeviceDetail");
                for (HashMap deviceDetail : deviceDetailList) {
                    DeviceDetailInfoDTO deviceDetailInfo = new DeviceDetailInfoDTO();
                    deviceDetailInfo.setNode((String) deviceDetail.get("node"));
                    deviceDetailInfo.setIsOnline((String) deviceDetail.get("is_online"));
                    deviceDetailInfo.setPeerHost((String) deviceDetail.get("peer_host"));
                    deviceDetailInfoDTO.add(deviceDetailInfo);
                }
            }
        }

        return deviceDetailInfoDTO;
    }

    public Object getAllSubscriberList2(TechnicianDashboardDTO technicianDashboardDTO) throws Exception {
        if (technicianDashboardDTO.getMax() > 500)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max cannot be greater than 500");

        if (technicianDashboardDTO.getOrder().name().equals("ASC") || technicianDashboardDTO.getOrder().name().equals("DESC")) {
            if (technicianDashboardDTO.getOffset() < 0)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");

            SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
            SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();
            ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();
            SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();

            if (isIllegalCharacterExist(technicianDashboardDTO)) {
                subscriberDataList.add(subscriberData);
                data.setObjects(subscriberDataList);
                data.setTotalCount(0L);
                subscriberListDTO.setData(data);

                return subscriberListDTO.getData();
            } else {
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
                ClusterInfo clusterInfo = null;
                if (Objects.nonNull(technicianDashboardDTO.getClusterId()) && !technicianDashboardDTO.getClusterId().equals(ZERO)) {
                    clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, technicianDashboardDTO.getClusterId());
                } else if (Objects.isNull(technicianDashboardDTO.getClusterId()) && !CommonUtils.isSysAdmin()) {
                    clusterInfo = compartment.getCluster().stream().filter(q -> q.isDefaultCluster()).findAny().orElse(null);
                }

                long count = 0;
                String countQuery;

                List<Object[]> subscribers;
                String replaceWith = " LIMIT " + (Objects.nonNull(technicianDashboardDTO.getOffset()) ? technicianDashboardDTO.getOffset() : 0) + ", " + (Objects.nonNull(technicianDashboardDTO.getMax()) ? technicianDashboardDTO.getMax() : 10);

                List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();


            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Allowed values for order - ASC,DESC");
        }
    }




    public Object getAllSubscriberList(TechnicianDashboardDTO technicianDashboardDTO) throws Exception {
        if (technicianDashboardDTO.getMax() > 500)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max cannot be greater than 500");

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        if (technicianDashboardDTO.getOrder().name().equals("ASC") || technicianDashboardDTO.getOrder().name().equals("DESC")) {

            if (technicianDashboardDTO.getOffset() < 0)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");


            if (isIllegalCharacterExist(technicianDashboardDTO)) {
                SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
                SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();

                ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();

                SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();
                subscriberDataList.add(subscriberData);
                data.setObjects(subscriberDataList);
                data.setTotalCount(0L);
                subscriberListDTO.setData(data);

                return subscriberListDTO.getData();
            }

            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            ClusterInfo clusterInfo = null;
            if (Objects.nonNull(technicianDashboardDTO.getClusterId()) && !technicianDashboardDTO.getClusterId().equals("0")) {
                clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, technicianDashboardDTO.getClusterId());
            } else if (Objects.isNull(technicianDashboardDTO.getClusterId()) && !CommonUtils.isSysAdmin()) {
                clusterInfo = compartment.getCluster().stream().filter(q -> q.isDefaultCluster()).findAny().orElse(null);
            }


            long count = 0;
            String countQuery;

            List<Object[]> subscribers;
            String replaceWith = " LIMIT " + (Objects.nonNull(technicianDashboardDTO.getOffset()) ? technicianDashboardDTO.getOffset() : 0) + ", " + (Objects.nonNull(technicianDashboardDTO.getMax()) ? technicianDashboardDTO.getMax() : 10);

            if (isSearchEnabled(technicianDashboardDTO)) {
                ESRequest elasticSearchRequest = new ESRequest();
                elasticSearchRequest.setMax(technicianDashboardDTO.getMax()).setOffset(technicianDashboardDTO.getOffset()).setSortBy(technicianDashboardDTO.getSortBy()).setIndex(USERAP_INDEX);
                HashMap<String, Object> esQueryParams = new HashMap<>();
                esQueryParams.put("name", technicianDashboardDTO.getName());
                esQueryParams.put("email", technicianDashboardDTO.getEmail());
                esQueryParams.put("phoneNo", Objects.nonNull(technicianDashboardDTO.getPhoneNo()) ? dataSecurityMapping.contains(DataSecurityType.phoneNumber.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getPhoneNo() : technicianDashboardDTO.getPhoneNo());
                esQueryParams.put("globalAccountNo", Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo()) ? dataSecurityMapping.contains(DataSecurityType.globalAccount.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getGlobalAccountNo() : technicianDashboardDTO.getGlobalAccountNo());
                esQueryParams.put("camsAccountNo", Objects.nonNull(technicianDashboardDTO.getCamsAccountNo()) ? dataSecurityMapping.contains(DataSecurityType.camsAccount.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getCamsAccountNo() : technicianDashboardDTO.getCamsAccountNo());
                esQueryParams.put("rgwMAC", Objects.nonNull(technicianDashboardDTO.getRgwMAC()) ? dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getRgwMAC() : technicianDashboardDTO.getRgwMAC());
                esQueryParams.put("serialNumber", technicianDashboardDTO.getSerialNumber());

                if (!CommonUtils.isSysAdmin()) {
                    esQueryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
                }
                elasticSearchRequest.setQueryParams(esQueryParams);
                ElasticSearchDTO response = esService.searchWithFilter(elasticSearchRequest, "clusters", Objects.nonNull(clusterInfo) ? clusterInfo.getId() : null);
                if (Objects.nonNull(response)) {

                    List<Map<String, Object>> emptySubscriberList = new ArrayList<>();
                    SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
                    SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();
                    if (response.getObjects().size() > 0) {
                        return mapElasticToSubscriberDTO(response, data);
                    }
                    SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();
                    ObjectMapper objectMapper = new ObjectMapper();
                    emptySubscriberList.add(objectMapper.convertValue(subscriberData, HashMap.class));
                    response.setObjects(emptySubscriberList);
                    response.setTotalCount(0);
                    return response;
                }


                StringBuffer sb = getSearchQuery(technicianDashboardDTO, compartment);

                if (Objects.nonNull(clusterInfo)) {
                    String clusterSql = "(select * from userAp ap inner join cluster_userAp c On (ap.userId=c.userAp_userId) where c.cluster_id='" + clusterInfo.getId() + "')";
                    countQuery = "Select count(u.id) from user u inner join " + clusterSql + " as ua on(u.id = ua.userId) " + sb.toString();

                    sb.append(" and c.cluster_id='" + clusterInfo.getId() + "'  LIMIT " + (Objects.nonNull(technicianDashboardDTO.getOffset()) ? technicianDashboardDTO.getOffset() : 0) + ", " + (Objects.nonNull(technicianDashboardDTO.getMax()) ? technicianDashboardDTO.getMax() : 10));
                    String sqlQuery = String.format(UserAPSQL.GET_SUBSCRIBERS_BY_CLUSTER, sb.toString());
                    subscribers = dataAccessService.readNative(sqlQuery, new HashMap<>());
                } else {
                    countQuery = String.format(UserAPSQL.GET_COUNT_OF_SEARCH_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), sb.toString());

                    sb.append(" LIMIT " + (Objects.nonNull(technicianDashboardDTO.getOffset()) ? technicianDashboardDTO.getOffset() : 0) + ", " + (Objects.nonNull(technicianDashboardDTO.getMax()) ? technicianDashboardDTO.getMax() : 10));

                    String query;
                    query = String.format(UserAPSQL.GET_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), sb.toString());

                    subscribers = dataAccessService.readNative(query, new HashMap<>());
                }

                List<Object> totalSubscriber = (List<Object>) dataAccessService.readNative(countQuery, new HashMap<>());
                count = Long.valueOf(Objects.nonNull(totalSubscriber) ? totalSubscriber.get(0).toString() : "0");
            } else {
                if (Objects.nonNull(clusterInfo)) {
                    ESRequest elasticSearchRequest = new ESRequest();
                    elasticSearchRequest.setMax(technicianDashboardDTO.getMax()).setOffset(technicianDashboardDTO.getOffset()).setSortBy(technicianDashboardDTO.getSortBy()).setIndex(USERAP_INDEX);
                    ElasticSearchDTO elasticSearchDTO = esService.searchWithFilter(elasticSearchRequest, "clusters", clusterInfo.getId());
                    if (Objects.nonNull(elasticSearchDTO)) {
                        List<Map<String, Object>> emptySubscriberList = new ArrayList<>();
                        SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
                        SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();
                        if (elasticSearchDTO.getObjects().size() > 0) {
                            return mapElasticToSubscriberDTO(elasticSearchDTO, data);
                        }
                        SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();
                        ObjectMapper objectMapper = new ObjectMapper();
                        emptySubscriberList.add(objectMapper.convertValue(subscriberData, HashMap.class));
                        elasticSearchDTO.setObjects(emptySubscriberList);
                        elasticSearchDTO.setTotalCount(0);
                        return elasticSearchDTO;
                    }

                    HashMap<String, Object> queryParam = new HashMap<>();
                    queryParam.put("clusterId", clusterInfo.getId());
                    count = Long.valueOf(dataAccessService.readNative(ClusterInfoSQL.GET_COUNT_USER_AP_BY_CLUSTER_ID, queryParam).iterator().next().toString());
                    String sqlQuery = String.format(UserAPSQL.GET_SUBSCRIBERS_BY_CLUSTER, "WHERE c.cluster_id='" + clusterInfo.getId() + "'" + replaceWith);
                    subscribers = dataAccessService.readNative(sqlQuery, new HashMap<>());
                } else {

                    GetSubscribersFromES getSubscribersFromES = new GetSubscribersFromES(technicianDashboardDTO.getMax(), technicianDashboardDTO.getOffset(), technicianDashboardDTO.getOrder().name(), technicianDashboardDTO.getSortBy()).invoke();
                    SubscriberListDTO subscriberListDTO = getSubscribersFromES.getSubscriberListDTO();
                    if (getSubscribersFromES.is())
                        return subscriberListDTO.getData();

                    String query;
                    if (!CommonUtils.isSysAdmin() && compartment.isProcessed()) {
                        query = String.format(UserAPSQL.GET_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), "Where ua.processed is true " + replaceWith);
                    } else {
                        query = String.format(UserAPSQL.GET_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), replaceWith);
                    }
                    subscribers = dataAccessService.readNative(query, new HashMap<>());

                    String queries = UserAPSQL.COUNT_TOTAL_SUBSCRIBER;
                    if (!CommonUtils.isSysAdmin() && compartment.isProcessed()) {
                        queries = queries.replaceAll("###", "Where processed is true and groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
                    } else if (!CommonUtils.isSysAdmin()) {
                        queries = queries.replaceAll("###", "Where groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
                    } else {
                        queries = queries.replaceAll("###", "");
                    }

                    List<Object> countData = (List<Object>) dataAccessService.readNative(queries, new HashMap<>());
                    count = Long.valueOf(Objects.nonNull(count) ? countData.get(0).toString() : "0");
                }
            }


            SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
            SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();

            ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();

            if (Objects.nonNull(subscribers) && !subscribers.isEmpty()) {
                for (Object subscriberDetail[] : subscribers) {
                    SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();

                    subscriberData.setName(isNullString(String.valueOf(subscriberDetail[0])) + ApplicationConstants.SPACE + isNullString(String.valueOf(subscriberDetail[1])));
                    subscriberData.setEmail(isNullString(String.valueOf(subscriberDetail[2])));
                    subscriberData.setDownLinkRate(Double.valueOf(String.valueOf(subscriberDetail[6])));
                    subscriberData.setPhoneNo(isNullString(String.valueOf(subscriberDetail[8])));
                    subscriberData.setGlobalAccountNo(isNullString(String.valueOf(subscriberDetail[9])));
                    subscriberData.setRgwMAC(isNullString(String.valueOf(subscriberDetail[12])));
                    subscriberData.setSubscriberId(isNullString(String.valueOf(subscriberDetail[13])));
                    subscriberData.setSerialNumber(isNullString(String.valueOf(subscriberDetail[14])));
                    subscriberData.setCamsAccountNo(isNullString(String.valueOf(subscriberDetail[15])));

                    subscriberDataList.add(subscriberData);
                }
            }
            if (subscriberDataList.isEmpty()) {
                SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();
                subscriberDataList.add(subscriberData);
            }


//            Collections.sort(subscriberDataList);
            data.setObjects(subscriberDataList);
            data.setTotalCount(count);
            subscriberListDTO.setData(data);

            return subscriberListDTO.getData();
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Allowed values for order - ASC,DESC");
        }
    }

    private boolean isIllegalCharacterExist(TechnicianDashboardDTO technicianDashboardDTO) {
        if (Objects.nonNull(technicianDashboardDTO.getName()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getName()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getRgwMAC()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getRgwMAC()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getSerialNumber()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getSerialNumber()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getGlobalAccountNo()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getCamsAccountNo()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getCamsAccountNo()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getEmail()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getEmail()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getPhoneNo()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getPhoneNo()))
            return true;
        else
            return false;

    }

    private boolean isSearchEnabled(TechnicianDashboardDTO technicianDashboardDTO) {
        return Objects.nonNull(technicianDashboardDTO.getSerialNumber()) || Objects.nonNull(technicianDashboardDTO.getName()) || Objects.nonNull(technicianDashboardDTO.getEmail()) || Objects.nonNull(technicianDashboardDTO.getRgwMAC()) || Objects.nonNull(technicianDashboardDTO.getPhoneNo()) || Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo()) || Objects.nonNull(technicianDashboardDTO.getCamsAccountNo());
    }

    private Object mapElasticToSubscriberDTO(ElasticSearchDTO response, SubscriberListDTO.SubscriberData data) {
        ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        response.getObjects().forEach(subs -> {
            SubscriberListDTO.SubscriberData.SubscriberModel subscriberModel = data.new SubscriberModel();
            SubscriberESDTO subscriberESDTO = objectMapper.convertValue(subs, SubscriberESDTO.class);
            subscriberModel.setCamsAccountNo(dataSecurityMapping.contains(DataSecurityType.camsAccount.name()) ? manageCommonService.encrypt() : subscriberESDTO.getCamsAccountNo());
            subscriberModel.setGlobalAccountNo(dataSecurityMapping.contains(DataSecurityType.globalAccount.name()) ? manageCommonService.encrypt() : subscriberESDTO.getGlobalAccountNo());
            subscriberModel.setPhoneNo(dataSecurityMapping.contains(DataSecurityType.phoneNumber.name()) ? manageCommonService.encrypt() : subscriberESDTO.getPhoneNo());
            subscriberModel.setEmail(subscriberESDTO.getEmail());
            subscriberModel.setName(subscriberESDTO.getName());
            subscriberModel.setRgwMAC(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : subscriberESDTO.getRgwMAC());
            subscriberModel.setSerialNumber(subscriberESDTO.getSerialNumber());
            subscriberModel.setSubscriberId(subscriberESDTO.getSubscriberId());
            subscriberModel.setDownLinkRate(subscriberESDTO.getDownLinkRate());
            subscriberModel.setUpLinkRate(subscriberESDTO.getUpLinkRate());
            subscriberDataList.add(subscriberModel);
        });
        data.setObjects(subscriberDataList);
        data.setTotalCount(response.getTotalCount());
        return data;
    }

    private StringBuffer getSearchQuery(TechnicianDashboardDTO technicianDashboardDTO, Compartment compartment) {
        ArrayList<String> queryParams = new ArrayList<>();

        StringBuffer sb = new StringBuffer(EMPTY_STRING);

        if (Objects.nonNull(technicianDashboardDTO.getName())) {
            queryParams.add("CONCAT( subs.firstName, ' ', subs.lastName ) LIKE '%" + technicianDashboardDTO.getName() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getEmail())) {
            queryParams.add("subs.email LIKE '%" + technicianDashboardDTO.getEmail() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getSerialNumber())) {
            queryParams.add("eq.rgwSerial LIKE '%" + technicianDashboardDTO.getSerialNumber() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getPhoneNo())) {
            queryParams.add("eq.serviceTelephoneNo LIKE '%" + technicianDashboardDTO.getPhoneNo() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo())) {
            queryParams.add("subs.globalAccountNo LIKE '%" + technicianDashboardDTO.getGlobalAccountNo() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getCamsAccountNo())) {
            queryParams.add("subs.camsAccountNo LIKE '%" + technicianDashboardDTO.getCamsAccountNo() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getRgwMAC())) {
            queryParams.add("eq.rgwMAC LIKE '%" + technicianDashboardDTO.getRgwMAC() + "%'");
        }

        if (!queryParams.isEmpty()) {
            sb.append("Where ");
            int i = 0;
            for (String condition : queryParams) {
                if (i > 0)
                    sb.append(" and ");
                sb.append(condition);
                i++;
            }
        }

        return sb;
    }

    private Object getSubscriberFromES() {
        SortOrder sortOrder = order.equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
        ESRequest elasticSearchRequest = new ESRequest();
        elasticSearchRequest.setMax(max).setOffset(offset).setOrder(sortOrder).setSortBy(sortBy).setIndex(USERAP_INDEX);
    }

    private class GetSubscribersFromES {

        private boolean myResult;
        private Integer max;
        private Integer offset;
        private String order;
        private String sortBy;
        private SubscriberListDTO subscriberListDTO;

        public GetSubscribersFromES(Integer max, Integer offset, String order, String sortBy) {
            this.max = max;
            this.offset = offset;
            this.order = order;
            this.sortBy = sortBy;
        }

        boolean is() {
            return myResult;
        }

        public SubscriberListDTO getSubscriberListDTO() {
            return subscriberListDTO;
        }

        public GetSubscribersFromES invoke() {

            List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

            SortOrder sortOrder = order.equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
            ESRequest elasticSearchRequest = new ESRequest();
            elasticSearchRequest.setMax(max).setOffset(offset).setOrder(sortOrder).setSortBy(sortBy).setIndex(EQUIPMENT_INDEX);
            if (!CommonUtils.isSysAdmin()) {
                HashMap<String, Object> queryParams = new HashMap<>();
                queryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
                elasticSearchRequest.setQueryParams(queryParams);
            }
            ElasticSearchDTO elasticSearchDTO = esService.getPaginatedData(elasticSearchRequest);
            if (Objects.nonNull(elasticSearchDTO)) {
                subscriberListDTO = new SubscriberListDTO();
                SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();
                ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();

                elasticSearchDTO.getObjects().forEach(subscriberDetail -> {
                    SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();
                    try {
                        subscriberData.setName(isNullString(String.valueOf(subscriberDetail.get("name"))));
                        subscriberData.setEmail(isNullString(String.valueOf(subscriberDetail.get("email"))));

//                    subscriberData.setAlexaVoiceSubscribed(Boolean.valueOf(String.valueOf(subscriberDetail.get("alexaVoiceSubscribed"))));
//                    subscriberData.setGoogleHomeVoiceSubscribed(Boolean.valueOf(String.valueOf(subscriberDetail.get("googleHomeVoiceSubscribed"))));
//                    subscriberData.setMobileSubscribed(Boolean.valueOf(String.valueOf(subscriberDetail.get("mobileSubscribed"))));
                        subscriberData.setDownLinkRate(Double.valueOf(String.valueOf(subscriberDetail.get("downLinkRate"))));
                        subscriberData.setUpLinkRate(Double.valueOf(String.valueOf(subscriberDetail.get("upLinkRate"))));
//                    subscriberData.setStaticRGW(Boolean.valueOf(String.valueOf(subscriberDetail.get("staticRGW"))));
                        subscriberData.setPhoneNo(dataSecurityMapping.contains(DataSecurityType.phoneNumber.name()) ? manageCommonService.encrypt() : isNullString(String.valueOf(subscriberDetail.get("phoneNo"))));
                        subscriberData.setGlobalAccountNo(dataSecurityMapping.contains(DataSecurityType.globalAccount.name()) ? manageCommonService.encrypt() : isNullString(String.valueOf(subscriberDetail.get("globalAccountNo"))));
                        subscriberData.setCamsAccountNo(dataSecurityMapping.contains(DataSecurityType.camsAccount.name()) ? manageCommonService.encrypt() : isNullString(String.valueOf(subscriberDetail.get("camsAccountNo"))));
//                    subscriberData.setAlexaEmail(isNullString(String.valueOf(subscriberDetail.get("alexaEmail"))));
//                    subscriberData.setGoogleHomeEmail(isNullString(String.valueOf(subscriberDetail.get("googleHomeEmail"))))
                        subscriberData.setRgwMAC(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : isNullString(String.valueOf(subscriberDetail.get(RGW_MAC))));
                        subscriberData.setSubscriberId(isNullString(String.valueOf(subscriberDetail.get("subscriberId"))));
                        subscriberData.setSerialNumber(isNullString(String.valueOf(subscriberDetail.get("apId"))));
//                    subscriberData.setLastReportedAt(getEquipmentLastReportedAt(subscriberData.getSerialNumber()));
//                        subscriberData.setIsp(manageCommonService.getIspOfSubscriber(subscriberData.getSerialNumber()));
                    } catch (Exception e) {

                    }
                    subscriberDataList.add(subscriberData);
                });
                if (subscriberDataList.isEmpty()) {
                    SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();
                    subscriberDataList.add(subscriberData);
                }
                data.setObjects(subscriberDataList);
//                Collections.sort(subscriberDataList);
                data.setTotalCount(Long.valueOf(String.valueOf(elasticSearchDTO.getTotalCount())));
                subscriberListDTO.setData(data);
                myResult = true;
                return this;
            }
            myResult = false;
            return this;
        }

    }

    public RGWDisconnectedDTO getRGWDisconnectedList(RGWPaginationRequest rgwPaginationRequest) throws Exception {
        Integer max = rgwPaginationRequest.getMax();
        Integer offset = rgwPaginationRequest.getOffset();
        Long fromDate = rgwPaginationRequest.getFromDate();
        Long toDate = rgwPaginationRequest.getToDate();
        HashMap<String, String> comConfig = commonService.read(COMMON_CONFIG);
        int equipmentOfflineTime = 7;
        if (Objects.nonNull(comConfig) && Objects.nonNull(comConfig.get(COMMON_DEVICE_STATUS_RETENTION_TIME))) {
            try {
                equipmentOfflineTime = Integer.valueOf(comConfig.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
            } catch (Exception e) {
            }
        }
        long timeDiff = CommonUtils.getCurrentTimeInMillis() - equipmentOfflineTime * 60 * 1000;

        if (Objects.nonNull(fromDate) && Objects.isNull(toDate)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide to date");
        }
        if (Objects.isNull(fromDate) && Objects.nonNull(toDate)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide from date");
        }
        BasicDBObject params = new BasicDBObject();
        params.put("type", GATEWAY);
        if (Objects.nonNull(fromDate) && Objects.nonNull(toDate)) {
            BasicDBObject timeCriteria = new BasicDBObject();
            timeCriteria.put("$lt", timeDiff);
            timeCriteria.put("$gte", fromDate);
            timeCriteria.put("$lte", toDate);
            params.put("sendingTime", timeCriteria);
        } else {
            params.put("sendingTime", new BasicDBObject("$lt", timeDiff));
        }
        BasicDBObject projection = new BasicDBObject();
        projection.put("serialNumber", 1);
        projection.put("isp", 1);
        projection.put("macAddress", 1);
        projection.put("timestamp", 1);
        projection.put("etlVersion", 1);
        projection.put("fwVersion", 1);
        Long count = mongoServiceimpl.count(params, new HashMap<>(), AP_DETAIL);
        List<BasicDBObject> disconnectedRGWList = mongoServiceimpl.aggregationQueryForPagination(params, AP_DETAIL, projection, TIMESTAMP, DESC, max, offset);
        RGWDisconnectedDTO rgwDisconnectedDTO = new RGWDisconnectedDTO();
        RGWDisconnectedDTO.RGWDisconnectedData rgwDisconnectedData = rgwDisconnectedDTO.new RGWDisconnectedData();
        ArrayList<RGWDisconnectedDTO.RGWDisconnectedData.RGWDisconnectedModel> rgwDisconnectedList = new ArrayList<>();
        if (Objects.nonNull(disconnectedRGWList) && !disconnectedRGWList.isEmpty()) {
            for (BasicDBObject rgwDisconnected : disconnectedRGWList) {
                if (Objects.nonNull(rgwDisconnected)) {
                    RGWDisconnectedDTO.RGWDisconnectedData.RGWDisconnectedModel rgwDisconnectedModel = rgwDisconnectedData.new RGWDisconnectedModel();
                    rgwDisconnectedModel.setSerialNumber(Objects.isNull(rgwDisconnected.get("serialNumber")) ? "NA" : rgwDisconnected.get("serialNumber").toString());
                    rgwDisconnectedModel.setIsp(Objects.isNull(rgwDisconnected.get("isp")) ? "NA" : rgwDisconnected.get("isp").toString());
                    rgwDisconnectedModel.setMacAddress(Objects.isNull(rgwDisconnected.get("macAddress")) ? "NA" : rgwDisconnected.get("macAddress").toString());
                    rgwDisconnectedModel.setLastReportedAt(Objects.isNull(rgwDisconnected.get("timestamp")) ? 0 : Long.valueOf(rgwDisconnected.get("timestamp").toString()));
                    rgwDisconnectedModel.setVersion(Objects.isNull(rgwDisconnected.get("etlVersion")) ? "NA" : rgwDisconnected.get("etlVersion").toString());
                    rgwDisconnectedModel.setFwVersion(Objects.isNull(rgwDisconnected.get("fwVersion")) ? "NA" : rgwDisconnected.get("fwVersion").toString());
                    rgwDisconnectedList.add(rgwDisconnectedModel);
                }
            }
        }
        rgwDisconnectedData.setObjects(rgwDisconnectedList);
        rgwDisconnectedData.setTotalCount(count);
        rgwDisconnectedDTO.setData(rgwDisconnectedData);
        return rgwDisconnectedDTO;
    }

}
*/
