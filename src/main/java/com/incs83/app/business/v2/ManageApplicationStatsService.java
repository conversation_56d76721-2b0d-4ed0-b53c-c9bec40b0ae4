package com.incs83.app.business.v2;


import com.amazonaws.services.cloudwatch.model.Datapoint;
import com.amazonaws.services.cloudwatch.model.GetMetricStatisticsResult;
import com.amazonaws.services.ec2.model.Volume;
import com.cloudera.api.swagger.TimeSeriesResourceApi;
import com.cloudera.api.swagger.client.ApiClient;
import com.cloudera.api.swagger.client.ApiException;
import com.cloudera.api.swagger.client.Configuration;
import com.cloudera.api.swagger.model.ApiTimeSeries;
import com.cloudera.api.swagger.model.ApiTimeSeriesData;
import com.cloudera.api.swagger.model.ApiTimeSeriesResponse;
import com.cloudera.api.swagger.model.ApiTimeSeriesResponseList;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.common.v2.*;
import com.incs83.app.config.SparkConfig;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.queries.TokenSQL;
import com.incs83.app.constants.queries.UserAPSQL;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.User;
import com.incs83.app.enums.SDAOperations;
import com.incs83.app.enums.ServiceNotifications;
import com.incs83.app.enums.Severity;
import com.incs83.app.enums.SparkJobType;
import com.incs83.app.enums.auditor.AuditorEnum;
import com.incs83.app.responsedto.v2.applicationNotification.ApplicationNotificationDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.isp.ISPModel;
import com.incs83.app.responsedto.v2.kafka.KafkaTimeSeries;
import com.incs83.app.responsedto.v2.onBoardingStats.OnBoardingStats;
import com.incs83.app.responsedto.v2.rgwStats.EquipmentStats;
import com.incs83.app.responsedto.v2.serviceStats.*;
import com.incs83.app.responsedto.v2.serviceStats.ResourceSpecification.*;
import com.incs83.app.responsedto.v2.serviceStats.ServiceNotification.ETLFileConfigRequest;
import com.incs83.app.responsedto.v2.serviceStats.ServiceNotification.ServiceNotificationDTO;
import com.incs83.app.responsedto.v2.serviceStats.ServiceNotification.ServiceNotificationRequest;
import com.incs83.app.service.components.AWSCloudWatchService;
import com.incs83.app.service.components.HttpServiceImpl;
import com.incs83.app.service.components.ses.AmazonEC2Service;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ServiceNameUtils;
import com.incs83.cloudera.KafkaMessageReceived;
import com.incs83.config.ClouderaConfig;
import com.incs83.config.JwtConfig;
import com.incs83.context.ExecutionContext;
import com.incs83.dao.Page;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.pubsub.MQTTService;
import com.incs83.service.CommonService;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.http.entity.StringEntity;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class ManageApplicationStatsService {

    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private JwtConfig jwtConfig;
    @Autowired
    private MongoServiceImpl mongoServiceImpl;
    @Autowired
    private CommonService commonService;
    @Autowired
    private Environment environment;
    @Autowired
    private HttpServiceImpl httpService;
    @Autowired
    private ClouderaConfig clouderaConfig;
    @Autowired
    private HazelcastService cacheService;
    @Autowired
    private SparkConfig sparkConfig;
//    @Autowired
//    private AWSCloudWatchService cloudWatchService;
    @Autowired
    private ISPService ispService;
    @Autowired
    private ManageCommonService manageCommonService;
//    @Autowired
//    private AmazonEC2Service amazonEC2Service;
    @Autowired
    private KafkaMessageReceived kafkaMessageReceived;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private MQTTService mqttService;

    //    @Value("${etlVersionCount}")
//    private String etlVersionCount ;
    private static boolean isSDevelop() {
        if (ExecutionContext.get() == null) {
            return false;
        }
        return ExecutionContext.get().getUsercontext().getAuthorities().stream()
                .anyMatch(p -> p.getName().equals("83INCS_DEVOPS"));
    }

    public HashMap getAllLoggedInUsers() throws Exception {
        Integer tokenExpirationTime = 0;
        tokenExpirationTime = jwtConfig.getTokenExpirationTime();

        HashMap<String, Object> result = new HashMap<>();
        HashMap<String, Object> query = new HashMap<>();
        query.put("tokenExpiration", tokenExpirationTime);
        String sqlQuery;
        if (!CommonUtils.isSysAdmin() && !isSDevelop()) {
            sqlQuery = TokenSQL.GET_TOTAL_LOGGED_IN_USER.replace("###", "AND uc.compartment_id='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
        } else {
            sqlQuery = TokenSQL.GET_TOTAL_LOGGED_IN_USER_SUBSCRIBER;
        }

        Long loggedInUsers = Long.valueOf(dataAccessService.readNative(sqlQuery, query).iterator().next().toString());

        result.put("totalUser", loggedInUsers);

        return result;
    }

    public ArrayList<HashMap<String, Object>> getStatsForOnBoardingOfSubscriber() throws Exception {
        String query = UserAPSQL.GET_AP_COUNT_GROUP_BY_DATE;
        if (!CommonUtils.isSysAdmin()) {
            query = String.format(UserAPSQL.GET_AP_COUNT_GROUP_BY_DATE_BY_COMPARTMENT, "'" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
        }

        List<Object[]> list = dataAccessService.readNative(query, new HashMap<>());
        ArrayList<HashMap<String, Object>> data = new ArrayList<>();
        if (Objects.nonNull(list) && !list.isEmpty()) {
            for (Object[] object : list) {
                HashMap<String, Object> result = new HashMap<>();
                result.put("count", String.valueOf(object[0]));
                String stringDate = String.valueOf(object[1]);

                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                try {
                    Date d = format.parse(stringDate);
                    long date = d.getTime();
                    result.put("date", date);
                } catch (ParseException e) {
                    LOG.error("Error in parsing", e);
                }

                data.add(result);
            }
        }
        return data;
    }

    public List<DBObject> getRgwStats(long minutes, String isp) {
        isDurationValid(minutes);
        return getStatsDataForCollection(RGW_CONNECTIVITY_STATS, minutes, (Objects.isNull(isp) ? getISPOfUserLoggedIn() : isp));
    }

    public List getRgwStatsPerDayReport(long minutes, String isp) {
        isDurationValid(minutes);
        if (Objects.isNull(isp) || isp.trim().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid isp :: " + isp);
        BasicDBObject groupBy;
        List<BasicDBObject> pipeline = new ArrayList<>();

        Calendar now = Calendar.getInstance();
        HashMap<String, Object> matchCriteria = new HashMap<>();
//        matchCriteria.put("timestamp", new BasicDBObject("$gte", now.getTimeInMillis() - (minutes * 60 * 1000)));
        matchCriteria.put("isp", isp);

        BasicDBObject match = new BasicDBObject("$match", matchCriteria);
        pipeline.add(match);
        BasicDBObject sort = new BasicDBObject("$sort", new BasicDBObject("timestamp", DESC));
        pipeline.add(sort);
        BasicDBObject limit = new BasicDBObject("$limit", 50);
        pipeline.add(limit);
        groupBy = new BasicDBObject().append("$group", new BasicDBObject("_id", "$isp").append("data", new BasicDBObject("$push", new BasicDBObject("gateway", "$gateway").append("extender", "$extender").append("timestamp", "$timestamp"))));

        pipeline.add(groupBy);
        List<BasicDBObject> results = mongoServiceImpl.aggregationPipeline(pipeline, RGW_CONNECTIVITY_STATS_PER_DAY);
        if (Objects.nonNull(results) && !results.isEmpty()) {
            return Objects.isNull(results.get(0).get("data")) ? new ArrayList() : ((List) results.get(0).get("data"));
        }

        return new ArrayList();
    }

    public List<DBObject> getDeviceStats(long minutes, String isp) {
        isDurationValid(minutes);

        return getStatsDataForCollection(DEVICE_CONNECTIVITY_STATS, minutes, (Objects.isNull(isp) ? getISPOfUserLoggedIn() : isp));
    }

    private void isDurationValid(long minutes) {
        if (minutes < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration value passed");
    }


    private String getISPOfUserLoggedIn() {
        String isp = null;
        if (!CommonUtils.isSysAdmin()) {
            try {
                isp = getISPNameById(manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser()));
            } catch (Exception e) {

            }
        }

        return isp;
    }

    private List<DBObject> getStatsDataForCollection(String collection, long minutes, String isp) {
        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("_id", ZERO);
        mongoFieldOptions.put("_class", ZERO);

        HashMap<String, Object> query = new HashMap<>();
        query.put("timestamp", new BasicDBObject("$gte", now.getTimeInMillis() - (minutes * 60 * 1000)));
        if (Objects.nonNull(isp) && !isp.isEmpty())
            query.put("isp", isp);

        List<DBObject> rgwStats = mongoServiceImpl.findList(query, collection, mongoFieldOptions);

        Set<EquipmentStats> equipmentStatsList = new HashSet<>();
        if (Objects.nonNull(rgwStats) && !rgwStats.isEmpty()) {
            for (DBObject dbObject : rgwStats) {
                EquipmentStats equipmentStats = new EquipmentStats();
                equipmentStats.setConnected(Objects.nonNull(dbObject.get("connected")) ? Long.valueOf(dbObject.get("connected").toString()) : 0L);
                equipmentStats.setDisconnected(Objects.nonNull(dbObject.get("disconnected")) ? Long.valueOf(dbObject.get("disconnected").toString()) : 0L);
                equipmentStats.setIsp(String.valueOf(dbObject.get("isp")));
                equipmentStats.setTimestamp(Objects.nonNull(dbObject.get("timestamp")) ? Long.valueOf(dbObject.get("timestamp").toString()) : 0L);

                equipmentStatsList.add(equipmentStats);
            }
        } else {
            List<DBObject> data = new ArrayList<>();
            return data;
        }

        List<DBObject> list = new ArrayList<>();
        if (Objects.isNull(isp)) {
            Set<String> timeStampList = equipmentStatsList.stream().filter(q -> q.getTimestamp() > 0).map(q -> String.valueOf(q.getTimestamp())).collect(Collectors.toSet());

            for (String timestamp : timeStampList) {
                Set<EquipmentStats> equipmentStatsListTemp = equipmentStatsList.stream().filter(element -> timestamp.equals(String.valueOf(element.getTimestamp()))).collect(Collectors.toSet());
                long disconnected = 0;
                long connected = 0;
                DBObject dataByTimeStamp = new BasicDBObject();
                for (EquipmentStats dbObject : equipmentStatsListTemp) {
                    if (Objects.nonNull(dbObject.getDisconnected())) {
                        disconnected += dbObject.getDisconnected();
                    }
                    if (Objects.nonNull(dbObject.getConnected())) {
                        connected += dbObject.getConnected();
                    }
                }

                dataByTimeStamp.put("disconnected", disconnected);
                dataByTimeStamp.put("connected", connected);
                dataByTimeStamp.put("timestamp", Long.valueOf(timestamp));
                list.add(dataByTimeStamp);
            }

            list.sort(Comparator.comparing(o -> (Long) o.get("timestamp")));

            return list;
        } else {
            List<EquipmentStats> statsList = new ArrayList<>(equipmentStatsList);
            statsList.sort(Comparator.comparing(o -> o.getTimestamp()));
            for (EquipmentStats equipmentStats : statsList) {
                DBObject dataByTimeStamp = new BasicDBObject();
                dataByTimeStamp.put("disconnected", equipmentStats.getDisconnected());
                dataByTimeStamp.put("connected", equipmentStats.getConnected());
                dataByTimeStamp.put("timestamp", equipmentStats.getTimestamp());
                list.add(dataByTimeStamp);
            }
            return list;
        }
    }

    private String getISPNameById(String ispId) throws Exception {
        String ispName = null;
        HashMap<String, Object> queryParams = new HashMap<>();

        if (Objects.nonNull(ispId)) {
            queryParams.clear();
            queryParams.put("id", ispId);

            HashMap<String, Object> appendableParams = new HashMap<>();
            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("_id", ZERO);

            DBObject dbObject = mongoServiceImpl.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
            if (Objects.nonNull(dbObject)) {
                ispName = String.valueOf(dbObject.get("name"));
            }
        }

        return ispName;
    }

    private DBObject getDeviceConnectivityStatusByCollection(String collection, String isp) {
        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("_id", ZERO);
        mongoFieldOptions.put("_class", ZERO);

        HashMap<String, Object> query = new HashMap<>();
        query.put("isp", isp);

        DBObject dbObject = mongoServiceImpl.findOne(query, new HashMap<>(), collection, TIMESTAMP, DESC, mongoFieldOptions);

        return dbObject;
    }

    private HashMap<String, Object> createDeviceConnectivityStatusInCache(String isp) {
        HashMap<String, Object> response = new HashMap<>();

        DBObject stationDetail = getDeviceConnectivityStatusByCollection(DEVICE_CONNECTIVITY_STATS, isp);
        HashMap<String, Object> staConnectivityCount = new HashMap<>();
        Long noOfDisconnectedDevice = 0L;
        Long noOfConnectedDevice = 0L;
        if (Objects.nonNull(stationDetail)) {
            noOfConnectedDevice = Objects.nonNull(stationDetail.get("connected")) ? Long.valueOf(stationDetail.get("connected").toString()) : 0L;
            noOfDisconnectedDevice = Objects.nonNull(stationDetail.get("disconnected")) ? Long.valueOf(stationDetail.get("disconnected").toString()) : 0L;
        }
        staConnectivityCount.put("noOfDisconnectedDevice", noOfDisconnectedDevice);
        staConnectivityCount.put("noOfConnectedDevice", noOfConnectedDevice);
        cacheService.create(isp, staConnectivityCount, STA_COUNT);

        DBObject rgw = getDeviceConnectivityStatusByCollection(RGW_CONNECTIVITY_STATS, isp);
        HashMap<String, Object> rgwConnectivityCount = new HashMap<>();
        Long noOfDisconnectedRGW = 0L;
        Long noOfConnectedRGW = 0L;
        if (Objects.nonNull(rgw)) {
            noOfConnectedRGW = Objects.nonNull(rgw.get("connected")) ? Long.valueOf(rgw.get("connected").toString()) : 0L;
            noOfDisconnectedRGW = Objects.nonNull(rgw.get("disconnected")) ? Long.valueOf(rgw.get("disconnected").toString()) : 0L;
        }
        rgwConnectivityCount.put("noOfDisconnectedRGW", noOfDisconnectedRGW);
        rgwConnectivityCount.put("noOfConnectedRGW", noOfConnectedRGW);
        cacheService.create(isp, rgwConnectivityCount, RGW_COUNT);

        response.put("noOfDisconnectedRGW", noOfDisconnectedRGW);
        response.put("noOfConnectedRGW", noOfConnectedRGW);
        response.put("noOfDisconnectedDevice", noOfDisconnectedDevice);
        response.put("noOfConnectedDevice", noOfConnectedDevice);

        return response;
    }

    public HashMap<String, Object> getDeviceConnectivityStatus(String ispName) throws Exception {
        HashMap<String, Object> response = new HashMap<>();

        if (!CommonUtils.isSysAdmin()) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            if (Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId())) {
                String isp = getISPNameById(compartment.getIspId());
                if (Objects.nonNull(isp)) {
                    return createDeviceConnectivityStatusInCache(isp);
                }
            }
        } else if (CommonUtils.isSysAdmin() && Objects.nonNull(ispName) && !ispName.isEmpty()) {
            return createDeviceConnectivityStatusInCache(ispName);
        } else {
            ISPDTOList ispdtoList = ispService.getAllISPs();

            long noOfDisconnectedRGW = 0L;
            long noOfConnectedRGW = 0L;
            long noOfDisconnectedDevice = 0L;
            long noOfConnectedDevice = 0L;

            ArrayList<ISPModel> ispModels = ispdtoList.getData();

            for (ISPModel ispModel : ispModels) {
                HashMap<String, Object> resp = createDeviceConnectivityStatusInCache(ispModel.getName());

                noOfDisconnectedRGW = noOfDisconnectedRGW + (Objects.nonNull(resp.get("noOfDisconnectedRGW")) ? Long.valueOf(resp.get("noOfDisconnectedRGW").toString()) : 0L);
                noOfConnectedRGW = noOfConnectedRGW + (Objects.nonNull(resp.get("noOfConnectedRGW")) ? Long.valueOf(resp.get("noOfConnectedRGW").toString()) : 0L);
                noOfDisconnectedDevice = noOfDisconnectedDevice + (Objects.nonNull(resp.get("noOfDisconnectedDevice")) ? Long.valueOf(resp.get("noOfDisconnectedDevice").toString()) : 0L);
                noOfConnectedDevice = noOfConnectedDevice + (Objects.nonNull(resp.get("noOfConnectedDevice")) ? Long.valueOf(resp.get("noOfConnectedDevice").toString()) : 0L);
            }

            response.put("noOfDisconnectedRGW", noOfDisconnectedRGW);
            response.put("noOfConnectedRGW", noOfConnectedRGW);
            response.put("noOfDisconnectedDevice", noOfDisconnectedDevice);
            response.put("noOfConnectedDevice", noOfConnectedDevice);
        }

        /*List<BasicDBObject> data = getAggregationResultForConnectivity(STATION_DETAIL, queryParams);
        if (Objects.nonNull(data) && !data.isEmpty()) {
            for (BasicDBObject ap : data) {
                String attr = ap.getString("_id");
                if (Objects.nonNull(attr) && attr.equals("Connected")) {
                    Long count = ap.getLong("stationCount");
                    response.put("noOfConnectedDevice", count);
                } else if (Objects.nonNull(attr) && attr.equals("Disconnected")) {
                    Long count = ap.getLong("stationCount");
                    response.put("noOfDisconnectedDevice", count);
                }
            }
            if (data.size() == 1) {
                if (response.containsKey("noOfConnectedDevice")) {
                    response.put("noOfDisconnectedDevice", 0);
                } else {
                    response.put("noOfConnectedDevice", 0);
                }
            }
        } else {
            response.put("noOfDisconnectedDevice", 0);
            response.put("noOfConnectedDevice", 0);
        }

        data.clear();
        data = getAggregationResultForConnectivity(ActiontecConstants.AP_DETAIL, queryParams);
        if (Objects.nonNull(data) && !data.isEmpty()) {
            for (BasicDBObject ap : data) {
                String attr = ap.getString("_id");
                if (Objects.nonNull(attr) && attr.equals("Connected")) {
                    Long count = ap.getLong("dutCount");
                    response.put("noOfConnectedRGW", count);
                } else if (Objects.nonNull(attr) && attr.equals("Disconnected")) {
                    Long count = ap.getLong("dutCount");
                    response.put("noOfDisconnectedRGW", count);
                }
            }
            if (data.size() == 1) {
                if (response.containsKey("noOfConnectedRGW")) {
                    response.put("noOfDisconnectedRGW", 0);
                } else {
                    response.put("noOfConnectedRGW", 0);
                }
            }
        } else {
            response.put("noOfDisconnectedRGW", 0);
            response.put("noOfConnectedRGW", 0);
        }*/

        return response;
    }

    public HashMap<String, Object> getDeviceConnectivityStatusUsingCache(String ispName) throws Exception {
        HashMap<String, Object> response = new HashMap<>();
        HashMap<String, Object> deviceConnectivityCount = null;
        HashMap<String, Object> rgwConnectivityCount = null;
        ISPDTOList ispdtoList;

        if (!CommonUtils.isSysAdmin()) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            if (Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId())) {
                String isp = getISPNameById(compartment.getIspId());
                if (Objects.nonNull(isp)) {
                    deviceConnectivityCount = (HashMap<String, Object>) cacheService.read(isp, STA_COUNT);
                    rgwConnectivityCount = (HashMap<String, Object>) cacheService.read(isp, RGW_COUNT);
                }
            }
        } else if (CommonUtils.isSysAdmin() && Objects.nonNull(ispName) && !ispName.isEmpty()) {
            deviceConnectivityCount = (HashMap<String, Object>) cacheService.read(ispName, STA_COUNT);
            rgwConnectivityCount = (HashMap<String, Object>) cacheService.read(ispName, RGW_COUNT);
        } else {
            ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
            if (Objects.nonNull(ispdtoList)) {
                long noOfDisconnectedDevice = 0L;
                long noOfConnectedDevice = 0L;
                long noOfDisconnectedRGW = 0L;
                long noOfConnectedRGW = 0L;

                boolean flag = true;
                for (ISPModel ispModel : ispdtoList.getData()) {
                    HashMap<String, Object> deviceCount = (HashMap<String, Object>) cacheService.read(ispModel.getName(), STA_COUNT);
                    HashMap<String, Object> rgwCount = (HashMap<String, Object>) cacheService.read(ispModel.getName(), RGW_COUNT);
                    if (Objects.nonNull(deviceCount) && Objects.nonNull(rgwCount)) {
                        noOfDisconnectedDevice += Objects.isNull(deviceCount.get("noOfDisconnectedDevice")) ? 0L : Long.parseLong(String.valueOf(deviceCount.get("noOfDisconnectedDevice")));
                        noOfConnectedDevice += Objects.isNull(deviceCount.get("noOfConnectedDevice")) ? 0L : Long.parseLong(String.valueOf(deviceCount.get("noOfConnectedDevice")));
                        noOfDisconnectedRGW += Objects.isNull(rgwCount.get("noOfDisconnectedRGW")) ? 0L : Long.parseLong(String.valueOf(rgwCount.get("noOfDisconnectedRGW")));
                        noOfConnectedRGW += Objects.isNull(rgwCount.get("noOfConnectedRGW")) ? 0L : Long.parseLong(String.valueOf(rgwCount.get("noOfConnectedRGW")));
                    } else {
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    deviceConnectivityCount = new HashMap<>();
                    rgwConnectivityCount = new HashMap<>();

                    deviceConnectivityCount.put("noOfDisconnectedDevice", noOfDisconnectedDevice);
                    deviceConnectivityCount.put("noOfConnectedDevice", noOfConnectedDevice);
                    rgwConnectivityCount.put("noOfDisconnectedRGW", noOfDisconnectedRGW);
                    rgwConnectivityCount.put("noOfConnectedRGW", noOfConnectedRGW);
                }
            }
        }


        if (Objects.nonNull(deviceConnectivityCount) && Objects.nonNull(rgwConnectivityCount)) {
            response.put("noOfDisconnectedDevice", Objects.isNull(deviceConnectivityCount.get("noOfDisconnectedDevice")) ? 0L : deviceConnectivityCount.get("noOfDisconnectedDevice"));
            response.put("noOfConnectedDevice", Objects.isNull(deviceConnectivityCount.get("noOfConnectedDevice")) ? 0L : deviceConnectivityCount.get("noOfConnectedDevice"));
            response.put("noOfDisconnectedRGW", Objects.isNull(rgwConnectivityCount.get("noOfDisconnectedRGW")) ? 0L : rgwConnectivityCount.get("noOfDisconnectedRGW"));
            response.put("noOfConnectedRGW", Objects.isNull(rgwConnectivityCount.get("noOfConnectedRGW")) ? 0L : rgwConnectivityCount.get("noOfConnectedRGW"));
        } else {
            response = getDeviceConnectivityStatus(ispName);
        }
        return response;
    }

    public ArrayList<String> getAllKafkaTopics() throws Exception {
        ArrayList<String> kafkaTopics = new ArrayList<>();

        HashMap<String, Object> filter = new BasicDBObject();
        filter.put("type", KAFKA_TOPICS_KEY);

        BasicDBObject sort = new BasicDBObject();

        BasicDBObject fieldsToRemove = new BasicDBObject();

        DBObject dbObject = mongoServiceImpl.findOne(filter, ETL_METADATA, sort, fieldsToRemove);
        if (Objects.nonNull(dbObject) && Objects.nonNull(dbObject.get("data"))) {
            kafkaTopics.addAll((ArrayList) dbObject.get("data"));
            Collections.sort(kafkaTopics);
        }

        return kafkaTopics;
    }

    public KafkaTimeSeries getBytesReceivedTimeSeriesData(Long minutes, String kafkaTopic) throws Exception {
        isDurationValid(minutes);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic(kafkaTopic);
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_bytes_received_rate_across_kafka_broker_topics WHERE entityName = kafka:" + kafkaTopic + " AND category = KAFKA_TOPIC";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);

            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);

        return kafkaTimeSeries;
    }


    public KafkaTimeSeries getKafkaBytesFetchedTimeSeriesData(Long minutes, String kafkaTopic) throws Exception {
        isDurationValid(minutes);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic(kafkaTopic);
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_bytes_fetched_rate_across_kafka_broker_topics WHERE entityName = kafka:" + kafkaTopic + " AND category = KAFKA_TOPIC";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);
            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);

        return kafkaTimeSeries;
    }

    public KafkaTimeSeries getKafkaMessagesReceivedTimeSeriesData(Long minutes, String kafkaTopic) throws Exception {
        isDurationValid(minutes);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic(kafkaTopic);
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_messages_received_rate_across_kafka_broker_topics WHERE entityName = kafka:" + kafkaTopic + " AND category = KAFKA_TOPIC";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);
            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);

        return kafkaTimeSeries;
    }

    public com.incs83.cloudera.KafkaTimeSeries getKafkaMessagesReceivedAcrossService(Long minutes) throws Exception {
        isDurationValid(minutes);

      /*  Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic("Total Messages Received Across Kafka Brokers");
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_messages_received_rate_across_kafka_brokers WHERE entityName = kafka AND category = SERVICE";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);
            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);*/

        com.incs83.cloudera.KafkaTimeSeries kafkaTimeSeries = kafkaMessageReceived.getKafkaMessagesReceivedAcrossService(minutes);
        return kafkaTimeSeries;
    }

    public KafkaTimeSeries getTotalPartitionsAcrossKafkaBrokers(Long minutes) throws Exception {
        isDurationValid(minutes);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic("Total Partitions Across Kafka Brokers");
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_partitions_across_kafka_brokers WHERE entityName = kafka AND category = SERVICE";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);
            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);

        return kafkaTimeSeries;
    }

    public KafkaTimeSeries getOfflinePartitionsAcrossKafkaBrokers(Long minutes) throws Exception {
        isDurationValid(minutes);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic("Total Offline Partitions Across Kafka Brokers");
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_offline_partitions_across_kafka_brokers WHERE entityName = kafka AND category = SERVICE";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);
            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);

        return kafkaTimeSeries;
    }

    public KafkaTimeSeries getLeaderReplicasAcrossKafkaBrokers(Long minutes) throws Exception {
        isDurationValid(minutes);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic("Total Leader Replicas Across Kafka Brokers");
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_leader_replicas_across_kafka_brokers WHERE entityName = \"kafka\" AND category = SERVICE";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);
            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);

        return kafkaTimeSeries;
    }

    public KafkaTimeSeries getUnderReplicatedPartitionsAcrossKafkaBrokers(Long minutes) throws Exception {
        isDurationValid(minutes);

        Calendar now = Calendar.getInstance();
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        Long startTime = now.getTimeInMillis() - (minutes * 60 * 1000);

        KafkaTimeSeries kafkaTimeSeries = new KafkaTimeSeries();
        kafkaTimeSeries.setTopic("Total Under Replicated Partitions Across Kafka Brokers");
        ArrayList<KafkaTimeSeries.TimeSeries> data = new ArrayList<>();
        try {
            ApiClient cmClient = Configuration.getDefaultApiClient();
            String baseUrl = "http://" + clouderaConfig.getHost() + ":" + clouderaConfig.getPort() + "/api/v19";
            cmClient.setBasePath(baseUrl);
            cmClient.setUsername(clouderaConfig.getUsername());
            cmClient.setPassword(clouderaConfig.getPassword());

            cmClient.setVerifyingSsl(false);
            TimeSeriesResourceApi apiInstance = new TimeSeriesResourceApi(cmClient);

            String contentType = "application/json";
            String desiredRollup = "RAW";
            Timestamp stamp = new Timestamp(startTime);
            Date date = new Date(stamp.getTime());


            TimeZone tz = TimeZone.getTimeZone("UTC");
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm'Z'");
            df.setTimeZone(tz);
            String from = df.format(date);

            Boolean mustUseDesiredRollup = false;
            String query = "SELECT total_kafka_under_replicated_partitions_across_kafka_brokers WHERE entityName = kafka AND category = SERVICE";
            //String to = String.valueOf(Calendar.getInstance().getTimeInMillis());
            try {
                ApiTimeSeriesResponseList result = apiInstance.queryTimeSeries(contentType, desiredRollup, from, mustUseDesiredRollup, query, null);
                if (Objects.nonNull(result) && Objects.nonNull(result.getItems()) && !result.getItems().isEmpty())
                    generateTimeSeriesData(result.getItems(), data, kafkaTimeSeries);
            } catch (ApiException e) {
                LOG.error("Exception when calling TimeSeriesResourceApi#queryTimeSerie");
            }

        } catch (Exception e) {
            LOG.error("Error while connecting to Cloudera :: " + e.getMessage());
        }
        kafkaTimeSeries.setData(data);

        return kafkaTimeSeries;
    }

    private void generateTimeSeriesData(List<ApiTimeSeriesResponse> response, ArrayList<KafkaTimeSeries.TimeSeries> data, KafkaTimeSeries kafkaTimeSeries) {
        for (ApiTimeSeriesResponse seriesResponse : response) {
            List<ApiTimeSeries> apiTimeSeries = seriesResponse.getTimeSeries();
            if (!apiTimeSeries.isEmpty()) {
                List<ApiTimeSeriesData> apiTimeSeriesData = apiTimeSeries.get(0).getData();
                for (ApiTimeSeriesData seriesData : apiTimeSeriesData) {
                    if (Objects.nonNull(seriesData.getTimestamp())) {
                        KafkaTimeSeries.TimeSeries timeSeries = kafkaTimeSeries.new TimeSeries();
                        timeSeries.setValue(Objects.nonNull(seriesData.getValue()) ? seriesData.getValue() : new BigDecimal("0.0"));
                        timeSeries.setTimestamp(convertToTimeStamp(seriesData.getTimestamp()));

                        data.add(timeSeries);
                    }
                }
            }
        }
    }

    private long convertToTimeStamp(String date) {
        long timestamp = 0;
        try {
            DateTimeFormatter timeFormatter = DateTimeFormatter.ISO_DATE_TIME;
            TemporalAccessor accessor = timeFormatter.parse(date);

            Date from = Date.from(Instant.from(accessor));
            timestamp = from.getTime();
        } catch (Exception e) {
            LOG.error("Error while converting String to Date :: " + e.getMessage());
        }
        return timestamp;
    }

    public Page getOnBoardingStats(Integer offset, Integer max) throws Exception {
        if (offset < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");
        Set<OnBoardingStats> listOfOnBoardingStats = new HashSet<>();
        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", 0);
        List<DBObject> dbObjectList = mongoServiceImpl.findList(ON_BOARDING_SUBSCRIBER_STATS, new HashMap<>(), ApplicationConstants.TIMESTAMP, DESC, projection, max, offset);
        dbObjectList.forEach(element -> {
            if (Objects.nonNull(element.get("timestamp"))) {
                OnBoardingStats onBoardingStats = new OnBoardingStats();
                onBoardingStats.setId(String.valueOf(element.get("id")));
                onBoardingStats.setRgw(String.valueOf(element.get("rgw")));
                onBoardingStats.setTimestamp(Long.valueOf(element.get("timestamp").toString()));
                onBoardingStats.setRemark(String.valueOf(element.get("remark")));
                onBoardingStats.setStatus(String.valueOf(element.get("status")));
                listOfOnBoardingStats.add(onBoardingStats);
            }
        });
        Page page = new Page();
        page.setObjects(new ArrayList(listOfOnBoardingStats));
        page.setTotalCount((int) mongoServiceImpl.distinctCount(new HashMap<>(), new HashMap<>(), ON_BOARDING_SUBSCRIBER_STATS, "rgw"));
        return page;
    }

    public HashMap<String, Object> getSparkExecutors(String type) {
        HashMap<String, Object> result = new HashMap<>();
        result.put("executors", new ArrayList<>());
        result.put("id", "");
        result.put("startTime", "");

        boolean isValidType = Arrays.stream(SparkJobType.values())
                .anyMatch(t -> t.name().equalsIgnoreCase(type));
        if (!isValidType) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Spark job type: " + type);
        }

        Optional<String> matchedHostOpt = Arrays.stream(sparkConfig.getHost().split(","))
                .filter(host -> host.toLowerCase().contains(type.toLowerCase()))
                .findFirst();

        if (!matchedHostOpt.isPresent()) {
            LOG.warn("No Spark host found for type: {}", type);
            return result;
        }

        String host = matchedHostOpt.get();
        String baseUrl = String.format("http://%s:%s", host, sparkConfig.getPort());
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            String appsUrl = baseUrl + "/api/v1/applications";
            String appsResponse = httpService.doGet(appsUrl, new HashMap<>(), new HashMap<>());
            List<Map<String, Object>> applications = objectMapper.readValue(appsResponse, List.class);

            if (applications == null || applications.isEmpty()) {
                LOG.warn("No applications found on Spark host: {}", host);
                return result;
            }

            Map<String, Object> firstApp = applications.get(0);
            String appId = String.valueOf(firstApp.get("id"));
            result.put("id", appId);

            List<Map<String, Object>> attempts = (List<Map<String, Object>>) firstApp.get("attempts");
            if (attempts != null && !attempts.isEmpty()) {
                Object startTimeEpoch = attempts.get(0).get("startTimeEpoch");
                if (startTimeEpoch != null) {
                    result.put("startTime", Long.parseLong(startTimeEpoch.toString()));
                }
            }

            String executorsUrl = baseUrl + "/api/v1/applications/" + appId + "/allexecutors";
            String executorsResponse = httpService.doGet(executorsUrl, new HashMap<>(), new HashMap<>());
            List<?> executorList = objectMapper.readValue(executorsResponse, List.class);
            Collections.reverse(executorList);
            result.put("executors", executorList);

        } catch (Exception e) {
            LOG.error("Failed to retrieve spark executor info from host: {}", host, e);
        }

        return result;
    }

    public String getResponseForServiceHealthURL(String URL) throws Exception {
        HashMap<String, String> headerInfo = new HashMap<>();

        HashMap<String, Object> queryParam = new HashMap<>();

        String url = environment.getProperty(INTERNAL_SERVICE_STATS_DNS) + URL;

        String httpResponse;
        try {
            httpResponse = httpService.doGet(url, headerInfo, queryParam);
        } catch (Exception e) {
            LOG.error("Error while Fetching " + URL);
            throw e;
        }

        return httpResponse;
    }

    public ApplicationServiceHealth getApplicationServiceStats() throws Exception {
        ApplicationServiceHealth applicationServiceHealth = new ApplicationServiceHealth();

        List<LinkedHashMap<String, Object>> serviceHealthStats = new ArrayList<>();

        ObjectMapper mapper = new ObjectMapper();

        String httpResponse;
        HashMap response = new HashMap();
        try {
            httpResponse = getResponseForServiceHealthURL(SERVICE_STATS_API);
            if (!httpResponse.isEmpty())
                response = mapper.readValue(httpResponse, HashMap.class);
        } catch (Exception e) {
            applicationServiceHealth.setServiceHealthStats(serviceHealthStats);

            return applicationServiceHealth;
        }

        String[] serviceNameList = {"cds", "externalRoutingTier", "internalRoutingTier", "jobRpcServer", "jobStatsServer", "kafkaConnect", "platform", "vmq", "ui", "mongo", "template", "nagios", "manager", "master", "gateway", "zkafka", "cache", "cassandra", "elastic-search", "mongo-shard", "worker_datanode", "vmqMetrics"};
        if (Objects.nonNull(response) && !(response.get("Health_Status") instanceof String)) {
            List<HashMap<String, Object>> result = (List<HashMap<String, Object>>) response.get("Health_Status");
            Set<String> healthStatusIP = new HashSet<>();
            if (Objects.nonNull(result) && !result.isEmpty()) {
                LinkedHashMap<String, Object> serviceDiscoveryAgent = new LinkedHashMap<>();
                serviceDiscoveryAgent.put("serviceName", "SERVICE_DISCOVERY_AGENT");
                List<LinkedHashMap<String, Object>> serviceDiscoveryAgentDetails = new ArrayList<>();
                for (String serviceName : serviceNameList) {
                    List<HashMap<String, Object>> serviceHealth = result.parallelStream().filter(q -> serviceName.equals(q.get("Service"))).collect(Collectors.toList());
                    LinkedHashMap<String, Object> service = new LinkedHashMap<>();
                    service.put("serviceName", ServiceNameUtils.getServiceName(serviceName));
                    service.put("status", true);
                    service.put("actions", getActions(serviceName));

                    List<LinkedHashMap<String, Object>> serviceDetailList = new ArrayList<>();

                    if (Objects.nonNull(serviceHealth) && !serviceHealth.isEmpty()) {
                        HashMap<String, Object> serviceStatus = serviceHealth.parallelStream().filter(q -> Objects.nonNull(q.get("Status")) && "False".equalsIgnoreCase(String.valueOf(q.get("Status")))).findAny().orElse(null);
                        if (Objects.nonNull(serviceStatus)) {
                            service.put("status", false);
                        }
                        for (HashMap<String, Object> serviceKeys : serviceHealth) {
                            LinkedHashMap<String, Object> serviceDetails = new LinkedHashMap<>();
                            serviceDetails.put("status", Objects.nonNull(serviceKeys.get("Status")) && "True".equalsIgnoreCase(serviceKeys.get("Status").toString()));
                            serviceDetails.put("ip", serviceKeys.get("PrivateIP"));
                            healthStatusIP.add(String.valueOf(serviceKeys.get("PrivateIP")));
                            serviceDetails.put("name", serviceKeys.get("Name"));
                            serviceDetails.put("service", serviceKeys.get("Service"));
                            serviceDetails.put("port", serviceKeys.get("Port"));
                            serviceDetails.put("region", serviceKeys.get("Region"));
                            serviceDetails.put("instanceId", serviceKeys.get("InstanceId"));
                            serviceDetails.put("instanceType", serviceKeys.get("InstanceType"));
                            serviceDetails.put("volumeIds", serviceKeys.get("VolumeIds"));
                            serviceDetails.put("prod", serviceKeys.get("Prod"));
                            if (serviceDetails.get("service").equals("master") && serviceDetails.get("port").equals("8032") && !Boolean.valueOf(serviceDetails.get("status").toString())) {
                                continue;
                            }
                            serviceDetailList.add(serviceDetails);
                        }

                        if (service.get("serviceName").equals("MASTER")) {
                            if (serviceDetailList.stream().filter(q -> q.get("service").equals("master") && q.get("port").equals("8032")).count() == 2) {
                                serviceDetailList = serviceDetailList.stream().filter(q -> q.get("service").equals("master") && q.get("port").equals("8032") && Boolean.valueOf(q.get("status").toString())).collect(Collectors.toList());
                            }
                        }

                        serviceDiscoveryAgentDetails.addAll(serviceDetailList.parallelStream().filter(q -> "4545".equals(q.get("port"))).collect(Collectors.toList()));
                        serviceDetailList = serviceDetailList.parallelStream().filter(q -> !"4545".equals(q.get("port"))).collect(Collectors.toList());
                        service.put("details", serviceDetailList);


                        if (serviceName.equals("zkafka")) {
                            LinkedHashMap<String, Object> zookeeperService = new LinkedHashMap<>();
                            zookeeperService.put("serviceName", "ZOOKEEPER");
                            zookeeperService.put("status", true);
                            zookeeperService.put("actions", getActions(serviceName));

                            List<LinkedHashMap<String, Object>> zookeeperServiceDetailList = serviceDetailList.parallelStream().filter(q -> "2181".equals(String.valueOf(q.get("port")))).collect(Collectors.toList());
                            HashMap<String, Object> zookeeperServiceStatus = zookeeperServiceDetailList.parallelStream().filter(q -> Objects.nonNull(q.get("Status")) && "False".equalsIgnoreCase(String.valueOf(q.get("Status")))).findAny().orElse(null);
                            if (Objects.nonNull(zookeeperServiceStatus)) {
                                zookeeperService.put("status", false);
                            }
                            zookeeperService.put("details", zookeeperServiceDetailList);
                            serviceHealthStats.add(zookeeperService);

                            LinkedHashMap<String, Object> kafkaService = new LinkedHashMap<>();
                            kafkaService.put("serviceName", "KAFKA");
                            kafkaService.put("status", true);
                            kafkaService.put("actions", getActions(serviceName));

                            List<LinkedHashMap<String, Object>> kafkaServiceDetailList = serviceDetailList.parallelStream().filter(q -> !"2181".equals(String.valueOf(q.get("port")))).collect(Collectors.toList());
                            HashMap<String, Object> kafkaServiceStatus = kafkaServiceDetailList.parallelStream().filter(q -> Objects.nonNull(q.get("Status")) && "False".equalsIgnoreCase(String.valueOf(q.get("Status")))).findAny().orElse(null);
                            if (Objects.nonNull(kafkaServiceStatus)) {
                                kafkaService.put("status", false);
                            }
                            kafkaService.put("details", kafkaServiceDetailList);
                            serviceHealthStats.add(kafkaService);
                        } else if (serviceName.equals("worker_datanode")) {
                            LinkedHashMap<String, Object> workerService = new LinkedHashMap<>();
                            workerService.put("serviceName", "WORKER");
                            workerService.put("status", true);
                            workerService.put("actions", getActions(serviceName));

                            List<LinkedHashMap<String, Object>> workerDetailList = serviceDetailList.parallelStream().filter(q -> "8041".equals(String.valueOf(q.get("port")))).collect(Collectors.toList());
                            HashMap<String, Object> workerServiceStatus = workerDetailList.parallelStream().filter(q -> Objects.nonNull(q.get("Status")) && "False".equalsIgnoreCase(String.valueOf(q.get("Status")))).findAny().orElse(null);
                            if (Objects.nonNull(workerServiceStatus)) {
                                workerService.put("status", false);
                            }
                            workerService.put("details", workerDetailList);
                            serviceHealthStats.add(workerService);

                            LinkedHashMap<String, Object> dataNodeService = new LinkedHashMap<>();
                            dataNodeService.put("serviceName", "DATA_NODE");
                            dataNodeService.put("status", true);
                            dataNodeService.put("actions", getActions(serviceName));

                            List<LinkedHashMap<String, Object>> dataNodeServiceDetailList = serviceDetailList.parallelStream().filter(q -> !"50010".equals(String.valueOf(q.get("port")))).collect(Collectors.toList());
                            HashMap<String, Object> dataNodeServiceStatus = dataNodeServiceDetailList.parallelStream().filter(q -> Objects.nonNull(q.get("Status")) && "False".equalsIgnoreCase(String.valueOf(q.get("Status")))).findAny().orElse(null);
                            if (Objects.nonNull(dataNodeServiceStatus)) {
                                dataNodeService.put("status", false);
                            }
                            dataNodeService.put("details", dataNodeServiceDetailList);
                            serviceHealthStats.add(dataNodeService);
                        } else {
                            serviceHealthStats.add(service);
                        }
                    }
                }

                if (!serviceDiscoveryAgentDetails.isEmpty()) {
                    HashMap<String, Object> serviceDiscoveryStatus = serviceDiscoveryAgentDetails.parallelStream().filter(q -> Objects.nonNull(q.get("Status")) && "False".equalsIgnoreCase(String.valueOf(q.get("Status")))).findAny().orElse(null);
                    if (Objects.nonNull(serviceDiscoveryStatus)) {
                        serviceDiscoveryAgent.put("status", false);
                    } else {
                        serviceDiscoveryAgent.put("status", true);
                    }
                    serviceDiscoveryAgent.put("details", serviceDiscoveryAgentDetails);
                    serviceHealthStats.add(serviceDiscoveryAgent);
                }
            }
        }

        serviceHealthStats.sort(Comparator.comparing(q -> q.get("serviceName").toString()));

        applicationServiceHealth.setServiceHealthStats(serviceHealthStats);

        return applicationServiceHealth;
    }

    public ApplicationServiceHealth getLoadBalancerInfo() throws Exception {
        ApplicationServiceHealth applicationServiceHealth = new ApplicationServiceHealth();

        List<LinkedHashMap<String, Object>> serviceHealthStats = new ArrayList<>();

        HashMap<String, String> headerInfo = new HashMap<>();

        ObjectMapper mapper = new ObjectMapper();
        HashMap<String, Object> queryParam = new HashMap<>();
        String url = environment.getProperty(INTERNAL_SERVICE_STATS_DNS) + ELB_INFO_API;

        String httpResponse = EMPTY_STRING;
        try {
            httpResponse = httpService.doGet(url, headerInfo, queryParam);
        } catch (Exception e) {
            LOG.error("Error while Fetching Service Health");
            applicationServiceHealth.setServiceHealthStats(serviceHealthStats);

            return applicationServiceHealth;
        }
        HashMap response = mapper.readValue(httpResponse, HashMap.class);
        if (Objects.nonNull(response)) {
            List<HashMap<String, Object>> networkELBResult = (List<HashMap<String, Object>>) response.get("network-elb");
            prepareELBData(networkELBResult, serviceHealthStats, "network-elb");
            List<HashMap<String, Object>> classicELBResult = (List<HashMap<String, Object>>) response.get("classic-elb");
            prepareELBData(classicELBResult, serviceHealthStats, "classic-elb");
        }
        applicationServiceHealth.setServiceHealthStats(serviceHealthStats);

        return applicationServiceHealth;
    }

    private void prepareELBData(List<HashMap<String, Object>> result, List<LinkedHashMap<String, Object>> serviceHealthStats, String serviceName) {
        if (Objects.nonNull(result) && !result.isEmpty()) {
            LinkedHashMap<String, Object> service = new LinkedHashMap<>();
            service.put("serviceName", ServiceNameUtils.getServiceName(serviceName));
            List<LinkedHashMap<String, Object>> serviceDetailList = new ArrayList<>();
            for (HashMap<String, Object> networkElbInfo : result) {
                if (Objects.nonNull(networkElbInfo) && !networkElbInfo.isEmpty()) {
                    LinkedHashMap<String, Object> serviceDetails = new LinkedHashMap<>();
                    serviceDetails.put("name", networkElbInfo.get("Name"));
                    serviceDetails.put("scheme", networkElbInfo.get("Scheme"));
                    serviceDetails.put("type", networkElbInfo.get("Type"));
                    serviceDetails.put("loadBalancer", ((HashMap) networkElbInfo.get("Dimensions")).get("LoadBalancer"));

                    serviceDetailList.add(serviceDetails);
                }
            }
            service.put("details", serviceDetailList);
            serviceHealthStats.add(service);
        }
    }

//    public HashMap<String, Object> getCloudWatchMetricsForCPUUtilization(CloudTimeSeriesRequest cloudTimeSeriesRequest) throws Exception {
//        HashMap<String, Object> timeSeriesData = new HashMap<>();
//        HashMap<String, Object> response = new HashMap<>();
//        for (String instanceId : cloudTimeSeriesRequest.getInstanceId()) {
//            List<HashMap<String, Object>> statistics = new ArrayList<>();
//            GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForEC2(instanceId, CPU_UTILIZATION, cloudTimeSeriesRequest.getStartTime(), cloudTimeSeriesRequest.getPeriod(), cloudTimeSeriesRequest.getStatistics().name());
//            if (Objects.nonNull(metricStatisticsResult)) {
//                List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//                if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                    prepareDataForMetrics(dataPoints, cloudTimeSeriesRequest.getStatistics().name(), statistics);
//                }
//            }
//            timeSeriesData.put(instanceId, statistics);
//        }
//
//        response.put("keys", timeSeriesData.keySet());
//        List<HashMap<String, Object>> timeSeriesDataPoints = new ArrayList<>();
//
//        List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) timeSeriesData.get(cloudTimeSeriesRequest.getInstanceId().get(0));
//        for (HashMap<String, Object> timeSeries : data) {
//            HashMap<String, Object> dataPoints = new HashMap<>();
//            Long timestamp = Long.valueOf(timeSeries.get("timestamp").toString());
//            dataPoints.put("timestamp", timestamp);
//            for (String key : timeSeriesData.keySet()) {
//                HashMap<String, Object> values = ((List<HashMap<String, Object>>) timeSeriesData.get(key)).stream().filter(q -> timestamp.equals(Long.valueOf(q.get("timestamp").toString()))).findAny().orElse(null);
//                if (Objects.nonNull(values)) {
//                    dataPoints.put(key, values.get("value"));
//                }
//            }
//
//            timeSeriesDataPoints.add(dataPoints);
//        }
//        response.put("dataPoints", timeSeriesDataPoints);
//
//        if (timeSeriesDataPoints.isEmpty())
//            response = null;
//
//        return response;
//    }

//    public HashMap<String, Object> getCloudWatchMetricsForNetworkPacketsIn(CloudTimeSeriesRequest cloudTimeSeriesRequest) throws Exception {
//        HashMap<String, Object> timeSeriesData = new HashMap<>();
//        HashMap<String, Object> response = new HashMap<>();
//        for (String instanceId : cloudTimeSeriesRequest.getInstanceId()) {
//            List<HashMap<String, Object>> statistics = new ArrayList<>();
//            GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForEC2(instanceId, NETWORK_PACKETS_IN, cloudTimeSeriesRequest.getStartTime(), cloudTimeSeriesRequest.getPeriod(), cloudTimeSeriesRequest.getStatistics().name());
//            if (Objects.nonNull(metricStatisticsResult)) {
//                List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//                if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                    prepareDataForMetrics(dataPoints, cloudTimeSeriesRequest.getStatistics().name(), statistics);
//                }
//            }
//            timeSeriesData.put(instanceId, statistics);
//        }
//
//        response.put("keys", timeSeriesData.keySet());
//        List<HashMap<String, Object>> timeSeriesDataPoints = new ArrayList<>();
//
//        List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) timeSeriesData.get(cloudTimeSeriesRequest.getInstanceId().get(0));
//        for (HashMap<String, Object> timeSeries : data) {
//            HashMap<String, Object> dataPoints = new HashMap<>();
//            Long timestamp = Long.valueOf(timeSeries.get("timestamp").toString());
//            dataPoints.put("timestamp", timestamp);
//            for (String key : timeSeriesData.keySet()) {
//                HashMap<String, Object> values = ((List<HashMap<String, Object>>) timeSeriesData.get(key)).stream().filter(q -> timestamp.equals(Long.valueOf(q.get("timestamp").toString()))).findAny().orElse(null);
//                if (Objects.nonNull(values)) {
//                    dataPoints.put(key, values.get("value"));
//                }
//            }
//
//            timeSeriesDataPoints.add(dataPoints);
//        }
//        response.put("dataPoints", timeSeriesDataPoints);
//
//        if (timeSeriesDataPoints.isEmpty())
//            response = null;
//
//        return response;
//    }

//    public HashMap<String, Object> getCloudWatchMetricsForNetworkPacketsOut(CloudTimeSeriesRequest cloudTimeSeriesRequest) throws Exception {
//        HashMap<String, Object> timeSeriesData = new HashMap<>();
//        HashMap<String, Object> response = new HashMap<>();
//        for (String instanceId : cloudTimeSeriesRequest.getInstanceId()) {
//            List<HashMap<String, Object>> statistics = new ArrayList<>();
//            GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForEC2(instanceId, NETWORK_PACKETS_OUT, cloudTimeSeriesRequest.getStartTime(), cloudTimeSeriesRequest.getPeriod(), cloudTimeSeriesRequest.getStatistics().name());
//            if (Objects.nonNull(metricStatisticsResult)) {
//                List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//                if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                    prepareDataForMetrics(dataPoints, cloudTimeSeriesRequest.getStatistics().name(), statistics);
//                }
//            }
//            timeSeriesData.put(instanceId, statistics);
//        }
//
//        response.put("keys", timeSeriesData.keySet());
//        List<HashMap<String, Object>> timeSeriesDataPoints = new ArrayList<>();
//
//        List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) timeSeriesData.get(cloudTimeSeriesRequest.getInstanceId().get(0));
//        for (HashMap<String, Object> timeSeries : data) {
//            HashMap<String, Object> dataPoints = new HashMap<>();
//            Long timestamp = Long.valueOf(timeSeries.get("timestamp").toString());
//            dataPoints.put("timestamp", timestamp);
//            for (String key : timeSeriesData.keySet()) {
//                HashMap<String, Object> values = ((List<HashMap<String, Object>>) timeSeriesData.get(key)).stream().filter(q -> timestamp.equals(Long.valueOf(q.get("timestamp").toString()))).findAny().orElse(null);
//                if (Objects.nonNull(values)) {
//                    dataPoints.put(key, values.get("value"));
//                }
//            }
//
//            timeSeriesDataPoints.add(dataPoints);
//        }
//        response.put("dataPoints", timeSeriesDataPoints);
//
//        if (timeSeriesDataPoints.isEmpty())
//            response = null;
//
//        return response;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBActiveFlowCount(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), ACTIVE_FLOW_COUNT, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBActiveFlowCountTLS(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), ACTIVE_FLOW_COUNT_TLS, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBConsumedLCUs(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), CONSUMED_LCUS, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBNewFlowCount(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), NEW_FLOW_COUNT, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBNewFlowCountTLS(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), NEW_FLOW_COUNT_TLS, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBTCPClientResetCount(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), TCP_CLIENT_RESET_COUNT, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBTCPELBResetCount(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), TCP_ELB_RESET_COUNT, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForNetworkELBTCPTargetResetCount(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForNetworkELB(cloudMetricsRequest.getInstanceId(), TCP_TARGET_RESET_COUNT, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForClassicLatency(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForClassicELB(cloudMetricsRequest.getInstanceId(), LATENCY, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForClassicRequestCount(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForClassicELB(cloudMetricsRequest.getInstanceId(), REQUEST_COUNT, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForClassicElbError500(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForClassicELB(cloudMetricsRequest.getInstanceId(), HTTP_CODE_ELB_5XX, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForClassicBackendError400(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForClassicELB(cloudMetricsRequest.getInstanceId(), HTTP_CODE_BACKEND_4XX, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public List<HashMap<String, Object>> getCloudWatchMetricsForClassicBackendError500(CloudMetricsRequest cloudMetricsRequest) throws Exception {
//        List<HashMap<String, Object>> statistics = new ArrayList<>();
//        GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForClassicELB(cloudMetricsRequest.getInstanceId(), HTTP_CODE_BACKEND_5XX, cloudMetricsRequest.getStartTime(), cloudMetricsRequest.getPeriod(), cloudMetricsRequest.getStatistics().name());
//        if (Objects.nonNull(metricStatisticsResult)) {
//            List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//            if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                prepareDataForMetrics(dataPoints, cloudMetricsRequest.getStatistics().name(), statistics);
//            }
//        }
//
//        return statistics;
//    }

//    public HashMap<String, Object> getCloudWatchMetricsForVolumeReadOps(VolumeMetricsRequest volumeMetricsRequest) throws Exception {
//        HashMap<String, Object> timeSeriesData = new HashMap<>();
//        HashMap<String, Object> response = new HashMap<>();
//        for (String volumeId : volumeMetricsRequest.getVolumeIds()) {
//            List<HashMap<String, Object>> statistics = new ArrayList<>();
//            GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForVolume(volumeId, VOLUME_READ_OPS, volumeMetricsRequest.getStartTime(), volumeMetricsRequest.getPeriod(), volumeMetricsRequest.getStatistics().name());
//            if (Objects.nonNull(metricStatisticsResult)) {
//                List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//                if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                    prepareDataForMetrics(dataPoints, volumeMetricsRequest.getStatistics().name(), statistics);
//                }
//            }
//            timeSeriesData.put(volumeId, statistics);
//        }
//
//        response.put("keys", timeSeriesData.keySet());
//        List<HashMap<String, Object>> timeSeriesDataPoints = new ArrayList<>();
//
//        List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) timeSeriesData.get(volumeMetricsRequest.getVolumeIds().get(0));
//        for (HashMap<String, Object> timeSeries : data) {
//            HashMap<String, Object> dataPoints = new HashMap<>();
//            Long timestamp = Long.valueOf(timeSeries.get("timestamp").toString());
//            dataPoints.put("timestamp", timestamp);
//            for (String key : timeSeriesData.keySet()) {
//                HashMap<String, Object> values = ((List<HashMap<String, Object>>) timeSeriesData.get(key)).stream().filter(q -> timestamp.equals(Long.valueOf(q.get("timestamp").toString()))).findAny().orElse(null);
//                if (Objects.nonNull(values)) {
//                    Double value = (Double) values.get("value") / (volumeMetricsRequest.getPeriod() * 60);
//                    dataPoints.put(key, value);
//                }
//            }
//
//            timeSeriesDataPoints.add(dataPoints);
//        }
//        response.put("dataPoints", timeSeriesDataPoints);
//
//        return response;
//    }

//    public HashMap<String, Object> getCloudWatchMetricsForVolumeWriteOps(VolumeMetricsRequest volumeMetricsRequest) throws Exception {
//        HashMap<String, Object> timeSeriesData = new HashMap<>();
//        HashMap<String, Object> response = new HashMap<>();
//        for (String volumeId : volumeMetricsRequest.getVolumeIds()) {
//            List<HashMap<String, Object>> statistics = new ArrayList<>();
//            GetMetricStatisticsResult metricStatisticsResult = cloudWatchService.getCloudWatchMetricsForVolume(volumeId, VOLUME_WRITE_OPS, volumeMetricsRequest.getStartTime(), volumeMetricsRequest.getPeriod(), volumeMetricsRequest.getStatistics().name());
//            if (Objects.nonNull(metricStatisticsResult)) {
//                List<Datapoint> dataPoints = metricStatisticsResult.getDatapoints();
//                if (Objects.nonNull(dataPoints) && !dataPoints.isEmpty()) {
//                    prepareDataForMetrics(dataPoints, volumeMetricsRequest.getStatistics().name(), statistics);
//                }
//            }
//            timeSeriesData.put(volumeId, statistics);
//        }
//
//        response.put("keys", timeSeriesData.keySet());
//        List<HashMap<String, Object>> timeSeriesDataPoints = new ArrayList<>();
//
//        List<HashMap<String, Object>> data = (List<HashMap<String, Object>>) timeSeriesData.get(volumeMetricsRequest.getVolumeIds().get(0));
//        for (HashMap<String, Object> timeSeries : data) {
//            HashMap<String, Object> dataPoints = new HashMap<>();
//            Long timestamp = Long.valueOf(timeSeries.get("timestamp").toString());
//            dataPoints.put("timestamp", timestamp);
//            for (String key : timeSeriesData.keySet()) {
//                HashMap<String, Object> values = ((List<HashMap<String, Object>>) timeSeriesData.get(key)).stream().filter(q -> timestamp.equals(Long.valueOf(q.get("timestamp").toString()))).findAny().orElse(null);
//                if (Objects.nonNull(values)) {
//                    Double value = (Double) values.get("value") / (volumeMetricsRequest.getPeriod() * 60);
//                    dataPoints.put(key, value);
//                }
//            }
//
//            timeSeriesDataPoints.add(dataPoints);
//        }
//        response.put("dataPoints", timeSeriesDataPoints);
//
//        return response;
//    }

    private void prepareDataForMetrics(List<Datapoint> dataPoints, String statisticsType, List<HashMap<String, Object>> statistics) {
        dataPoints.forEach(dataPoint -> {
            HashMap<String, Object> data = new HashMap<>();
            data.put("timestamp", dataPoint.getTimestamp().getTime());
            switch (statisticsType) {
                case "Average":
                    data.put("value", Double.valueOf(FOUR_DECIMAL_PLACE.format(dataPoint.getAverage())));
                    break;

                case "Sum":
                    data.put("value", Double.valueOf(FOUR_DECIMAL_PLACE.format(dataPoint.getSum())));
                    break;

                case "Minimum":
                    data.put("value", Double.valueOf(FOUR_DECIMAL_PLACE.format(dataPoint.getMinimum())));
                    break;

                case "Maximum":
                    data.put("value", Double.valueOf(FOUR_DECIMAL_PLACE.format(dataPoint.getMaximum())));
                    break;
            }
            statistics.add(data);
        });

        if (!statistics.isEmpty()) {
            statistics.sort((q1, q2) -> Long.compare((Long) q1.get("timestamp"), (Long) q2.get("timestamp")));
        }
    }

    public AppStatsResourceSpecDTO getAppStatsResourceSpecification(AppStatsResourceSpecification appStatsResourceSpecification) throws Exception {
        String ipAddress = appStatsResourceSpecification.getIpAddress();
        String serviceName = appStatsResourceSpecification.getServiceName();

        if (Objects.isNull(ipAddress) || ipAddress.trim().isEmpty()) {
            throw new ValidationException((HttpStatus.BAD_REQUEST.value()), "Invalid IP Address");
        }
        if (Objects.isNull(serviceName) || serviceName.trim().isEmpty()) {
            throw new ValidationException((HttpStatus.BAD_REQUEST.value()), "Service name is invalid");
        }

        AppStatsResourceSpecDTO appStatsResourceSpecDTO = null;

        HashMap<String, String> headerInfo = new HashMap<>();

        HashMap<String, Object> queryParam = new HashMap<>();

        ObjectMapper mapper = new ObjectMapper();

        String url = "http://" + ipAddress + RESOURCE_SPECIFICATION_API_PATH + "/all";

        String httpResponse;
        try {
            httpResponse = httpService.doGet(url, headerInfo, queryParam);
        } catch (Exception e) {
            LOG.error("Error while Fetching Resource Specification");
            return appStatsResourceSpecDTO;
        }

        if (Objects.isNull(httpResponse) || httpResponse.trim().isEmpty()) {
            LOG.error("Invalid Response");
            return appStatsResourceSpecDTO;
        }

        HashMap response = mapper.readValue(httpResponse, HashMap.class);
        HashMap data = (HashMap) response.get("metrics");
        if (Objects.nonNull(data)) {
            appStatsResourceSpecDTO = new AppStatsResourceSpecDTO();

            ROMInfoDTO romInfoDTO = new ROMInfoDTO();
            if (Objects.isNull(data.get("rom_info")) || data.get("rom_info") instanceof String) {
                romInfoDTO = null;
            } else {
                HashMap romInfo = (HashMap) data.get("rom_info");
                if (Objects.nonNull(romInfo) && !romInfo.isEmpty()) {
                    romInfoDTO.setAvail((String) romInfo.get("Avail"));
                    romInfoDTO.setConsumed((String) romInfo.get("Used"));
                    romInfoDTO.setUsePrcnt((String) romInfo.get("Use%"));
                    romInfoDTO.setFileSystem((String) romInfo.get("Filesystem"));
                    romInfoDTO.setSize((String) romInfo.get("Size"));
                }
            }

            RAMInfoDTO ramInfoDTO = new RAMInfoDTO();
            if (Objects.isNull(data.get("ram_info")) || data.get("ram_info") instanceof String) {
                ramInfoDTO = null;
            } else {
                HashMap ramInfo = (HashMap) data.get("ram_info");
                if (Objects.nonNull(ramInfo) && !ramInfo.isEmpty()) {
                    ramInfoDTO.setMemoryCached((String) ramInfo.get("mem_cached"));
                    ramInfoDTO.setActiveMemory((String) ramInfo.get("mem_active"));
                    ramInfoDTO.setMemoryFree((String) ramInfo.get("mem_free"));
                    ramInfoDTO.setSwapFree((String) ramInfo.get("swap_free"));
                    ramInfoDTO.setSwapTotal((String) ramInfo.get("swap_total"));
                    ramInfoDTO.setTotalMemory((String) ramInfo.get("mem_total"));
                }
            }

            LoadAverageInfoDTO loadAverageInfoDTO = new LoadAverageInfoDTO();
            if (Objects.isNull(data.get("load_avg")) || data.get("load_avg") instanceof String) {
                loadAverageInfoDTO = null;
            } else {
                HashMap loadAverage = (HashMap) data.get("load_avg");
                if (Objects.nonNull(loadAverage) && !loadAverage.isEmpty()) {
                    loadAverageInfoDTO.setOneMinAverage((String) loadAverage.get("1_min_avg"));
                    loadAverageInfoDTO.setFiveMinAverage((String) loadAverage.get("5_min_avg"));
                    loadAverageInfoDTO.setFifteenMinAverage((String) loadAverage.get("15_min_avg"));
                }
            }

            NetworkInfoDTO networkInfoDTO = new NetworkInfoDTO();
            NetworkInfoDTO.EthernetReceive ethernetReceiveDTO = networkInfoDTO.new EthernetReceive();
            if (Objects.isNull(data.get("network_info")) || data.get("network_info") instanceof String) {
                networkInfoDTO = null;
            } else {
                HashMap<String, Object> networkInfo = (HashMap<String, Object>) data.get("network_info");
                Set<String> keys = networkInfo.keySet();
                keys.remove("lo");
                HashMap eth0 = (HashMap) networkInfo.get(new ArrayList<String>(keys).get(0));

                HashMap receive = (HashMap) eth0.get("Receive");
                if (Objects.nonNull(receive) && !receive.isEmpty()) {
                    ethernetReceiveDTO.setBytes((String) receive.get("bytes"));
                    ethernetReceiveDTO.setDrop((String) receive.get("drop"));
                    ethernetReceiveDTO.setPackets((String) receive.get("packets"));
                    networkInfoDTO.setEthernetReceive(ethernetReceiveDTO);
                }

                NetworkInfoDTO.EthernetTransmit ethernetTransmitDTO = networkInfoDTO.new EthernetTransmit();
                HashMap transmit = (HashMap) eth0.get("Transmit");
                if (Objects.nonNull(transmit) && !transmit.isEmpty()) {
                    ethernetTransmitDTO.setBytes((String) transmit.get("bytes"));
                    ethernetTransmitDTO.setDrop((String) transmit.get("drop"));
                    ethernetTransmitDTO.setPackets((String) transmit.get("packets"));
                    networkInfoDTO.setEthernetTransmit(ethernetTransmitDTO);
                }
            }

            List<TCPStatsInfoDTO> tcpStatsInfo = new ArrayList<>();
            if (Objects.nonNull(data.get("tcp_stats"))) {
                if (data.get("tcp_stats") instanceof String) {
                    tcpStatsInfo = null;
                } else if (Objects.nonNull(data.get("tcp_stats"))) {
                    for (LinkedHashMap tcpStats : (List<LinkedHashMap>) data.get("tcp_stats")) {
                        TCPStatsInfoDTO tcpStatsInfoDTO = new TCPStatsInfoDTO();
                        tcpStatsInfoDTO.setLocalName(serviceName);
                        tcpStatsInfoDTO.setLocalPort(String.valueOf(tcpStats.get("LocalPort")));
                        tcpStatsInfoDTO.setLocalIP(String.valueOf(tcpStats.get("LocalIP")));
                        tcpStatsInfoDTO.setForeignIP(String.valueOf(tcpStats.get("ForeignIP")));
                        tcpStatsInfoDTO.setForeignName(String.valueOf(tcpStats.get("ForeignName")));
                        tcpStatsInfoDTO.setForeignPort(String.valueOf(tcpStats.get("ForeignPort")));
                        tcpStatsInfoDTO.setStatus(String.valueOf(tcpStats.get("Status")));
                        tcpStatsInfo.add(tcpStatsInfoDTO);
                    }
                }
            }

            TCPInfoDTO tcpInfoDTO = new TCPInfoDTO();
            if (Objects.isNull(data.get("tcp_info")) || data.get("tcp_info") instanceof String) {
                tcpInfoDTO = null;
            } else {
                HashMap tcpInfo = (HashMap) data.get("tcp_info");
                if (Objects.nonNull(tcpInfo) && !tcpInfo.isEmpty()) {
                    tcpInfoDTO.setClosed((String) tcpInfo.get("closed"));
                    tcpInfoDTO.setEstab((String) tcpInfo.get("estab"));
                    tcpInfoDTO.setOrphaned((String) tcpInfo.get("orphaned"));
                    tcpInfoDTO.setSynrecv((String) tcpInfo.get("synrecv"));
                    tcpInfoDTO.setTimewait((String) tcpInfo.get("timewait"));
                }
            }

            if (serviceName.equals("KAFKA_CONNECT")) {
                KafkaConnectDTO kafkaConnectDTO = new KafkaConnectDTO();
                if (Objects.isNull(data.get("kafkaConnectMetrics")) || data.get("kafkaConnectMetrics") instanceof String) {
                    kafkaConnectDTO = null;
                } else {
                    HashMap kafkaConnectMetrics = (HashMap) data.get("kafkaConnectMetrics");
                    if (Objects.nonNull(kafkaConnectMetrics) && !kafkaConnectMetrics.isEmpty()) {
                        KafkaConnectDTO.Connectors connectorsDTO = kafkaConnectDTO.new Connectors();
                        List<String> connectors = (List<String>) kafkaConnectMetrics.get("connectors");

                        connectorsDTO.setConnectorsList(connectors);
                        kafkaConnectDTO.setConnectors(connectorsDTO);

                        appStatsResourceSpecDTO.setKafkaConnectInfo(kafkaConnectDTO);
                    }
                }
            }

            if (serviceName.equals("UI") || serviceName.equals("INTERNAL_ROUTING_TIER") || serviceName.equals("EXTERNAL_ROUTING_TIER")) {
                NGINXInfoDTO nginxInfoDTO = new NGINXInfoDTO();
                if (Objects.isNull(data.get("nginxStats")) || data.get("nginxStats") instanceof String) {
                } else {
                    HashMap nginxStats = (HashMap) data.get("nginxStats");

                    NGINXInfoDTO.ServerInfo serverInfoDTO = nginxInfoDTO.new ServerInfo();
                    HashMap serverInfo = (HashMap) nginxStats.get("serverInfo");
                    serverInfoDTO.setAccepts((String) serverInfo.get("accepts"));
                    serverInfoDTO.setHandled((String) serverInfo.get("handled"));
                    serverInfoDTO.setRequests((String) serverInfo.get("requests"));

                    nginxInfoDTO.setServerInfo(serverInfoDTO);

                    nginxInfoDTO.setActiveConnections((String) nginxStats.get("activeConnections"));

                    appStatsResourceSpecDTO.setNginxStats(nginxInfoDTO);
                }
            }

            if (serviceName.equals("VERNE_MQ")) {
                VMQStatsDTO vmqStatsDTO = new VMQStatsDTO();
                if (Objects.isNull(data.get("vmqStats")) || data.get("vmqStats") instanceof String) {
                    vmqStatsDTO = null;
                } else {
                    HashMap vmqStats = (HashMap) data.get("vmqStats");
                    if (Objects.nonNull(vmqStats) && !vmqStats.isEmpty()) {
                        HashMap<String, Object> vmqMetricsInfo = (HashMap<String, Object>) vmqStats.get("metrics");
                        List<HashMap> vmqMetrics = new ArrayList<>();
                        for (Map.Entry<String, Object> vmqOneMetric : vmqMetricsInfo.entrySet()) {
                            HashMap vmqmetric = new HashMap();
                            vmqmetric.put("key", vmqOneMetric.getKey());
                            vmqmetric.put("value", vmqOneMetric.getValue());
                            vmqMetrics.add(vmqmetric);
                        }

                        vmqStatsDTO.setVmqMetrics(vmqMetrics);

                        List<VMQStatsDTO.VMQCluster> vmqClusters = (List<VMQStatsDTO.VMQCluster>) vmqStats.get("cluster");
                        vmqStatsDTO.setVmqClusters(vmqClusters);

                        appStatsResourceSpecDTO.setVmqStats(vmqStatsDTO);
                    }
                }
            }
            appStatsResourceSpecDTO.setROMInfo(romInfoDTO);
            appStatsResourceSpecDTO.setLoadAverageInfo(loadAverageInfoDTO);
            appStatsResourceSpecDTO.setRAMInfo(ramInfoDTO);
            appStatsResourceSpecDTO.setNetworkInfo(networkInfoDTO);
            appStatsResourceSpecDTO.setTcpStats(tcpStatsInfo);
            appStatsResourceSpecDTO.setTcpInfo(tcpInfoDTO);

            return appStatsResourceSpecDTO;
        } else {
            LOG.error("Error while fetching metrics");
            return appStatsResourceSpecDTO;
        }
    }

    public HashMap<String, Object> getDiskInfoResourceSpecifications(ResourceSpecificationInfo resourceSpecificationInfo) throws Exception {
        HashMap<String, String> headerInfo = new HashMap<>();
        HashMap<String, Object> queryParam = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();

        HashMap<String, Object> resourceSpecifications = new HashMap<>();

        if (Objects.isNull(resourceSpecificationInfo.getIpAddress()) || resourceSpecificationInfo.getIpAddress().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No IP address Provided");
        }

        ExecutorService executorService = Executors.newFixedThreadPool(resourceSpecificationInfo.getIpAddress().size());
        List<Callable<String>> futureList = new ArrayList<>();

        for (String ipAddress : resourceSpecificationInfo.getIpAddress()) {
            Callable<String> resourceHttpAsyncCall = () -> {
                String url = "http://" + ipAddress + RESOURCE_SPECIFICATION_API_PATH + "/rom_info";
                String httpResponse;
                try {
                    httpResponse = httpService.doGet(url, headerInfo, queryParam);
                    if (Objects.nonNull(httpResponse) || !httpResponse.trim().isEmpty()) {
                        HashMap response = mapper.readValue(httpResponse, HashMap.class);
                        if (Objects.nonNull(response)) {
                            if (!(response.get("rom_info") instanceof String)) {
                                HashMap romInfo = (HashMap) response.get("rom_info");
                                if (Objects.nonNull(romInfo) && !romInfo.isEmpty()) {
                                    ROMInfoDTO romInfoDTO = new ROMInfoDTO();
                                    romInfoDTO.setAvail((String) romInfo.get("Avail"));
                                    romInfoDTO.setConsumed((String) romInfo.get("Used"));
                                    romInfoDTO.setUsePrcnt((String) romInfo.get("Use%"));
                                    romInfoDTO.setFileSystem((String) romInfo.get("Filesystem"));
                                    romInfoDTO.setSize((String) romInfo.get("Size"));
                                    resourceSpecifications.put(ipAddress, romInfoDTO);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Error while Fetching Resource Specification");
                    resourceSpecifications.put(ipAddress, null);
                }
                return ipAddress;
            };
            futureList.add(resourceHttpAsyncCall);
        }

        List<String> successIPs = new ArrayList<>();
        try {
            List<Future<String>> threadResult = executorService.invokeAll(futureList, 1L, TimeUnit.SECONDS);
            for (Future<String> result : threadResult) {
                if (!result.isCancelled())
                    successIPs.add(result.get());
            }
        } catch (Exception err) {
            LOG.error("Error while Fetching Resource Specification");
        }
        executorService.shutdown();

        resourceSpecificationInfo.getIpAddress().removeAll(successIPs);
        for (String failedIPs : resourceSpecificationInfo.getIpAddress())
            resourceSpecifications.put(failedIPs, null);

        return resourceSpecifications;
    }


    public HashMap<String, Object> getHistoricalDataForMemoryInfo(HistoricalResourceSpecifications historicalResourceSpecifications) throws Exception {
        if (Objects.isNull(historicalResourceSpecifications.getMinutes()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration");
        HashMap<String, Object> response = new HashMap<>();
        Set<String> keys = new HashSet<>();

        List<HashMap<String, Object>> dataPointList = new ArrayList<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("timestamp", new BasicDBObject("$gte", CommonUtils.getCurrentTimeInMillis() - historicalResourceSpecifications.getMinutes() * 60 * 1000));

        List<DBObject> objectList = mongoServiceImpl.findList(TELEMETRY_DETAIL, query, TIMESTAMP, DESC);
        for (DBObject dbObject : objectList) {
            List<Map<String, Object>> ramInfo = (List<Map<String, Object>>) dbObject.get("ram_info");
            HashMap<String, Object> dataPoints = new HashMap<>();
            if (Objects.nonNull(ramInfo) && !ramInfo.isEmpty()) {
                ramInfo.remove(null);
                boolean flag = false;
                for (Map<String, Object> memoryInfo : ramInfo) {
                    if (Objects.nonNull(historicalResourceSpecifications.getInstanceIds()) && historicalResourceSpecifications.getInstanceIds().contains(String.valueOf(memoryInfo.get("instanceId")))) {
                        keys.add(String.valueOf(memoryInfo.get("instanceId")));
                        dataPoints.put(String.valueOf(memoryInfo.get("instanceId")), Objects.nonNull(memoryInfo.get("memUsed")) ? Double.valueOf(String.valueOf(memoryInfo.get("memUsed"))) : 0);
                        flag = true;
                    }
                }
                if (flag) {
                    dataPoints.put("timestamp", dbObject.get("timestamp"));
                    dataPointList.add(dataPoints);
                }
            }
        }

        if (!dataPointList.isEmpty()) {
            response.put("keys", keys);
            dataPointList.sort((q1, q2) -> Long.compare((Long) q1.get("timestamp"), (Long) q2.get("timestamp")));
            response.put("dataPoints", dataPointList);
        } else {
            response = null;
        }

        return response;
    }

    public HashMap<String, Object> getHistoricalDataForTcpInfo(HistoricalResourceSpecifications historicalResourceSpecifications) throws Exception {
        if (Objects.isNull(historicalResourceSpecifications.getMinutes()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration");
        HashMap<String, Object> response = new HashMap<>();
        Set<String> keys = new HashSet<>();

        List<HashMap<String, Object>> dataPointList = new ArrayList<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("timestamp", new BasicDBObject("$gte", CommonUtils.getCurrentTimeInMillis() - historicalResourceSpecifications.getMinutes() * 60 * 1000));

        List<DBObject> objectList = mongoServiceImpl.findList(TELEMETRY_DETAIL, query, TIMESTAMP, DESC);
        for (DBObject dbObject : objectList) {
            List<Map<String, Object>> tcpInfo = (List<Map<String, Object>>) dbObject.get("tcp_info");
            HashMap<String, Object> dataPoints = new HashMap<>();
            if (Objects.nonNull(tcpInfo) && !tcpInfo.isEmpty()) {
                tcpInfo.remove(null);
                boolean flag = false;
                for (Map<String, Object> tcp : tcpInfo) {
                    if (Objects.nonNull(historicalResourceSpecifications.getInstanceIds()) && historicalResourceSpecifications.getInstanceIds().contains(String.valueOf(tcp.get("instanceId")))) {
                        keys.add(String.valueOf(tcp.get("instanceId")));
                        dataPoints.put(String.valueOf(tcp.get("instanceId")), Objects.nonNull(tcp.get("tcpEstab")) ? Double.valueOf(String.valueOf(tcp.get("tcpEstab"))) : 0);
                        flag = true;
                    }
                }

                if (flag) {
                    dataPoints.put("timestamp", dbObject.get("timestamp"));
                    dataPointList.add(dataPoints);
                }
            }
        }

        if (!dataPointList.isEmpty()) {
            response.put("keys", keys);
            dataPointList.sort((q1, q2) -> Long.compare((Long) q1.get("timestamp"), (Long) q2.get("timestamp")));
            response.put("dataPoints", dataPointList);
        } else {
            response = null;
        }

        return response;
    }

    public HashMap<String, Object> getHistoricalDataForDiskInfo(HistoricalResourceSpecifications historicalResourceSpecifications) throws Exception {
        if (Objects.isNull(historicalResourceSpecifications.getMinutes()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration");
        HashMap<String, Object> response = new HashMap<>();
        Set<String> keys = new HashSet<>();

        List<HashMap<String, Object>> dataPointList = new ArrayList<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("timestamp", new BasicDBObject("$gte", CommonUtils.getCurrentTimeInMillis() - historicalResourceSpecifications.getMinutes() * 60 * 1000));

        List<DBObject> objectList = mongoServiceImpl.findList(TELEMETRY_DETAIL, query, TIMESTAMP, DESC);
        for (DBObject dbObject : objectList) {
            List<Map<String, Object>> romInfo = (List<Map<String, Object>>) dbObject.get("rom_info");
            HashMap<String, Object> dataPoints = new HashMap<>();
            if (Objects.nonNull(romInfo) && !romInfo.isEmpty()) {
                romInfo.remove(null);
                boolean flag = false;
                for (Map<String, Object> diskInfo : romInfo) {
                    if (Objects.nonNull(historicalResourceSpecifications.getInstanceIds()) && historicalResourceSpecifications.getInstanceIds().contains(String.valueOf(diskInfo.get("instanceId")))) {
                        keys.add(String.valueOf(diskInfo.get("instanceId")));
                        dataPoints.put(String.valueOf(diskInfo.get("instanceId")), Objects.nonNull(diskInfo.get("diskUsed")) ? Double.valueOf(String.valueOf(diskInfo.get("diskUsed")).replaceAll("[^\\d.]", EMPTY_STRING)) : 0);
                        flag = true;
                    }
                }

                if (flag) {
                    dataPoints.put("timestamp", dbObject.get("timestamp"));
                    dataPointList.add(dataPoints);
                }
            }
        }

        if (!dataPointList.isEmpty()) {
            response.put("keys", keys);
            dataPointList.sort((q1, q2) -> Long.compare((Long) q1.get("timestamp"), (Long) q2.get("timestamp")));
            response.put("dataPoints", dataPointList);
        } else {
            response = null;
        }

        return response;
    }

    public HashMap<String, Object> getMemoryInfoResourceSpecifications(ResourceSpecificationInfo resourceSpecificationInfo) throws Exception {
        HashMap<String, String> headerInfo = new HashMap<>();
        HashMap<String, Object> queryParam = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();

        HashMap<String, Object> resourceSpecifications = new HashMap<>();

        if (Objects.isNull(resourceSpecificationInfo.getIpAddress()) || resourceSpecificationInfo.getIpAddress().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No IP address Provided");
        }

        ExecutorService executorService = Executors.newFixedThreadPool(resourceSpecificationInfo.getIpAddress().size());
        List<Callable<String>> futureList = new ArrayList<>();

        for (String ipAddress : resourceSpecificationInfo.getIpAddress()) {
            Callable<String> resourceHttpAsyncCall = () -> {
                String url = "http://" + ipAddress + RESOURCE_SPECIFICATION_API_PATH + "/ram_info";
                String httpResponse;
                try {
                    httpResponse = httpService.doGet(url, headerInfo, queryParam);
                    if (Objects.nonNull(httpResponse) || !httpResponse.trim().isEmpty()) {
                        HashMap response = mapper.readValue(httpResponse, HashMap.class);
                        if (Objects.nonNull(response)) {
                            if (!(response.get("ram_info") instanceof String)) {
                                HashMap ramInfo = (HashMap) response.get("ram_info");
                                if (Objects.nonNull(ramInfo) && !ramInfo.isEmpty()) {
                                    RAMInfoDTO ramInfoDTO = new RAMInfoDTO();
                                    ramInfoDTO.setMemoryCached((String) ramInfo.get("mem_cached"));
                                    ramInfoDTO.setActiveMemory((String) ramInfo.get("mem_active"));
                                    ramInfoDTO.setMemoryFree((String) ramInfo.get("mem_free"));
                                    ramInfoDTO.setSwapFree((String) ramInfo.get("swap_free"));
                                    ramInfoDTO.setSwapTotal((String) ramInfo.get("swap_total"));
                                    ramInfoDTO.setTotalMemory((String) ramInfo.get("mem_total"));
                                    resourceSpecifications.put(ipAddress, ramInfoDTO);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Error while Fetching Resource Specification");
                    resourceSpecifications.put(ipAddress, null);
                }
                return ipAddress;
            };
            futureList.add(resourceHttpAsyncCall);
        }

        List<String> successIPs = new ArrayList<>();
        try {
            List<Future<String>> threadResult = executorService.invokeAll(futureList, 1L, TimeUnit.SECONDS);
            for (Future<String> result : threadResult) {
                if (!result.isCancelled())
                    successIPs.add(result.get());
            }
        } catch (Exception err) {
            LOG.error("Error while Fetching Resource Specification");
        }
        executorService.shutdown();

        resourceSpecificationInfo.getIpAddress().removeAll(successIPs);
        for (String failedIPs : resourceSpecificationInfo.getIpAddress())
            resourceSpecifications.put(failedIPs, null);

        return resourceSpecifications;
    }

    public HashMap<String, Object> getTcpInfoResourceSpecifications(ResourceSpecificationInfo resourceSpecificationInfo) throws Exception {
        HashMap<String, String> headerInfo = new HashMap<>();
        HashMap<String, Object> queryParam = new HashMap<>();
        ObjectMapper mapper = new ObjectMapper();

        HashMap<String, Object> resourceSpecifications = new HashMap<>();

        if (Objects.isNull(resourceSpecificationInfo.getIpAddress()) || resourceSpecificationInfo.getIpAddress().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No IP address Provided");
        }

        ExecutorService executorService = Executors.newFixedThreadPool(resourceSpecificationInfo.getIpAddress().size());
        List<Callable<String>> futureList = new ArrayList<>();

        for (String ipAddress : resourceSpecificationInfo.getIpAddress()) {
            Callable<String> resourceHttpAsyncCall = () -> {
                String url = "http://" + ipAddress + RESOURCE_SPECIFICATION_API_PATH + "/tcp_info";
                String httpResponse;
                try {
                    httpResponse = httpService.doGet(url, headerInfo, queryParam);
                    if (Objects.nonNull(httpResponse) || !httpResponse.trim().isEmpty()) {
                        HashMap response = mapper.readValue(httpResponse, HashMap.class);
                        if (Objects.nonNull(response)) {
                            if (!(response.get("tcp_info") instanceof String)) {
                                HashMap tcpInfo = (HashMap) response.get("tcp_info");
                                if (Objects.nonNull(tcpInfo) && !tcpInfo.isEmpty()) {
                                    TCPInfoDTO tcpInfoDTO = new TCPInfoDTO();
                                    tcpInfoDTO.setClosed((String) tcpInfo.get("closed"));
                                    tcpInfoDTO.setEstab((String) tcpInfo.get("estab"));
                                    tcpInfoDTO.setOrphaned((String) tcpInfo.get("orphaned"));
                                    tcpInfoDTO.setSynrecv((String) tcpInfo.get("synrecv"));
                                    tcpInfoDTO.setTimewait((String) tcpInfo.get("timewait"));
                                    resourceSpecifications.put(ipAddress, tcpInfoDTO);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    TCPInfoDTO tcpInfoDTO = new TCPInfoDTO();
                    tcpInfoDTO.setClosed("0");
                    tcpInfoDTO.setEstab("0");
                    tcpInfoDTO.setOrphaned("0");
                    tcpInfoDTO.setSynrecv("0");
                    tcpInfoDTO.setTimewait("0");
                    resourceSpecifications.put(ipAddress, tcpInfoDTO);
                    LOG.error("Error while Fetching Resource Specification ip == >" + ipAddress);
                }
                return ipAddress;
            };
            futureList.add(resourceHttpAsyncCall);
        }

        List<String> successIPs = new ArrayList<>();
        try {
            List<Future<String>> threadResult = executorService.invokeAll(futureList, 1L, TimeUnit.SECONDS);
            for (Future<String> result : threadResult) {
                if (!result.isCancelled())
                    successIPs.add(result.get());
            }
        } catch (Exception err) {
            LOG.error("Error while Fetching Resource Specification");
        }
        executorService.shutdown();

        resourceSpecificationInfo.getIpAddress().removeAll(successIPs);
        for (String failedIPs : resourceSpecificationInfo.getIpAddress()) {
            TCPInfoDTO tcpInfoDTO = new TCPInfoDTO();
            tcpInfoDTO.setClosed("0");
            tcpInfoDTO.setEstab("0");
            tcpInfoDTO.setOrphaned("0");
            tcpInfoDTO.setSynrecv("0");
            tcpInfoDTO.setTimewait("0");
            resourceSpecifications.put(failedIPs, tcpInfoDTO);
        }

        return resourceSpecifications;
    }

    public void createServiceNotification(List<String> serviceNotificationsList) {
        for (String serviceNotifications : serviceNotificationsList) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("id", CommonUtils.generateUUID());
            params.put("propertyName", serviceNotifications);
            params.put("email", new ArrayList<>());
            params.put("active", false);

            boolean flag = mongoServiceImpl.create(SERVICE_NOTIFICATION, params);
            if (!flag) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request failed.");
            }
        }

    }

    public void updateServiceNotification(List<ServiceNotificationRequest> serviceNotificationRequest) {
        for (ServiceNotificationRequest serviceNotification : serviceNotificationRequest) {
            HashMap queryParam = new HashMap();
            queryParam.put("id", serviceNotification.getId());

            HashMap appendableParams = new HashMap();

            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("_id", ZERO);

            DBObject configParam = mongoServiceImpl.findOne(queryParam, appendableParams, SERVICE_NOTIFICATION, fieldsToRemove);
            if (Objects.isNull(configParam))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Record not found with id :: " + serviceNotification.getId());

            ArrayList<String> emailList = serviceNotification.getEmails();
            boolean active = serviceNotification.isActive();

            DBObject query = new BasicDBObject().append("id", serviceNotification.getId());

            BasicDBObject updateParams = new BasicDBObject();
            updateParams.put("email", emailList);
            updateParams.put("active", active);

            DBObject update = new BasicDBObject().append("$set", updateParams);

            mongoServiceImpl.update(query, update, true, false, SERVICE_NOTIFICATION);
        }
    }


    public ArrayList<ServiceNotificationDTO> getServiceNotification() {
        List<String> serviceNotificationsList = Stream.of(ServiceNotifications.values())
                .map(ServiceNotifications::name)
                .collect(Collectors.toList());
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        List<DBObject> configList = mongoServiceImpl.findList(new HashMap<>(), SERVICE_NOTIFICATION, fieldsToRemove);
        if (Objects.isNull(configList) || configList.isEmpty()) {
            createServiceNotification(serviceNotificationsList);
            configList = mongoServiceImpl.findList(new HashMap<>(), SERVICE_NOTIFICATION, fieldsToRemove);
        }
        if (ServiceNotifications.values().length > configList.size()) {
            serviceNotificationsList.removeAll(configList.stream().map(e -> e.get("propertyName")).collect(Collectors.toList()));
            createServiceNotification(serviceNotificationsList);
            configList = mongoServiceImpl.findList(new HashMap<>(), SERVICE_NOTIFICATION, fieldsToRemove);
        }

        HashMap<String, String> diagonosticProps = commonService.read(DIAGONOSTIC_CONFIG);
        ArrayList<ServiceNotificationDTO> response = new ArrayList<>();
        for (DBObject dbObject : configList) {
            ServiceNotificationDTO serviceNotificationDTO = new ServiceNotificationDTO();
            serviceNotificationDTO.setEmails((ArrayList<String>) dbObject.get("email"));
            serviceNotificationDTO.setEventName((String) dbObject.get("propertyName"));
            serviceNotificationDTO.setActive((boolean) dbObject.get("active"));
            serviceNotificationDTO.setId((String) dbObject.get("id"));
            Set<ServiceNotifications> enumList = EnumSet.allOf(ServiceNotifications.class);
            ServiceNotifications serviceNotifications = enumList.stream().filter(q -> q.name().equals(String.valueOf(dbObject.get("propertyName")))).findAny().orElse(null);
            if (Objects.nonNull(serviceNotifications)) {
                serviceNotificationDTO.setThreshold(Objects.isNull(diagonosticProps.get(serviceNotifications.getValue())) ? "NA" : diagonosticProps.get(serviceNotifications.getValue()));
                serviceNotificationDTO.setConfigName(serviceNotifications.getValue());
                response.add(serviceNotificationDTO);
            }
        }

        return response;
    }

    public void createNotification(HashMap<String, Object> content) {
        Long startTime = 0L;
        Long endTime = 0L;
        try {
            startTime = Long.valueOf("" + content.get("startTime"));
            endTime = Long.valueOf("" + content.get("endTime"));
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid start time or end time.");
        }
        Long currentTime = CommonUtils.getCurrentTimeInMillis();
        if (startTime >= endTime) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Start time cannot be greater than end time.");
        }
        Severity severity = Objects.nonNull(content.get("severity")) ? Severity.valueOf("" + content.get("severity")) : Severity.INFO;
        BasicDBObject dataToSave = new BasicDBObject();
        dataToSave.put("startTime", startTime);
        dataToSave.put("endTime", endTime);
        dataToSave.put("content", content.get("content"));
        dataToSave.put("active", false);
        dataToSave.put("severity", severity.getValue());
        dataToSave.put("comment", content.get("comment"));
        dataToSave.put("createdAt", currentTime);
        dataToSave.put("createdBy", CommonUtils.getUserIdOfLoggedInUser());
        dataToSave.put("id", CommonUtils.generateUUID());
        mongoServiceImpl.create(APPLICATION_NOTIFICATION, dataToSave);
    }

    public ApplicationNotificationDTO getNotificationsList(NotificationRequest notificationRequest) {
        ApplicationNotificationDTO applicationNotificationDTO = new ApplicationNotificationDTO();

        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", 0);

        HashMap<String, Object> query = new HashMap<>();
        query.put("startTime", new BasicDBObject("$gte", CommonUtils.getCurrentTimeInMillis()));

        List<DBObject> dbObjectList = mongoServiceImpl.findList(APPLICATION_NOTIFICATION, query, notificationRequest.getSortBy(), ASC, projection, 5, 0);
        List<HashMap<String, Object>> upcomingEventList = populateData(dbObjectList);
        applicationNotificationDTO.setUpcoming(upcomingEventList);

        query.clear();
        Long time = CommonUtils.getCurrentTimeInMillis();
        query.put("startTime", new BasicDBObject("$lte", time));
        query.put("endTime", new BasicDBObject("$gte", time));
        dbObjectList = mongoServiceImpl.findList(APPLICATION_NOTIFICATION, query, notificationRequest.getSortBy(), ASC);

        List<HashMap<String, Object>> currentEventList = populateData(dbObjectList);
        applicationNotificationDTO.setCurrent(currentEventList);

        query.clear();
        query.put("endTime", new BasicDBObject("$lt", CommonUtils.getCurrentTimeInMillis()));
        dbObjectList = mongoServiceImpl.findList(APPLICATION_NOTIFICATION, query, notificationRequest.getSortBy(), DESC, projection, 10, 0);
        List<HashMap<String, Object>> pastEventList = populateData(dbObjectList);
        applicationNotificationDTO.setHistory(pastEventList);

        return applicationNotificationDTO;
    }

    private List<HashMap<String, Object>> populateData(List<DBObject> dbObjectList) {
        List<HashMap<String, Object>> upcomingEventList = new ArrayList<>();
        dbObjectList.forEach(q -> {
            HashMap<String, Object> upcoming = new HashMap<>();
            upcoming.put("id", q.get("id"));
            upcoming.put("startTime", q.get("startTime"));
            upcoming.put("endTime", q.get("endTime"));
            upcoming.put("content", q.get("content"));
            upcoming.put("active", q.get("active"));
            upcoming.put("severity", q.get("severity"));
            upcoming.put("comment", q.get("comment"));
            upcoming.put("createdAt", q.get("createdAt"));
            upcoming.put("createdBy", getUserDetails(String.valueOf(q.get("createdBy"))));
            upcoming.put("isEnabled", !(Objects.isNull(q.get("endTime")) || Long.valueOf(q.get("endTime").toString()) < CommonUtils.getCurrentTimeInMillis()));

            upcomingEventList.add(upcoming);
        });

        return upcomingEventList;
    }

    private String getUserDetails(String userId) {
        String name = userId;
        try {
            User user = (User) dataAccessService.read(User.class, userId);
            name = user.getFirstName() + SPACE + user.getLastName();
        } catch (Exception e) {
            LOG.error("Error while fetching user details");
        }

        return name;
    }

    public void updateNotification(String notificationId, Boolean isActive) {
        HashMap<String, Object> filter = new HashMap<>();
        filter.put("id", notificationId);
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        DBObject dbObject = mongoServiceImpl.findOne(filter, new HashMap<>(), APPLICATION_NOTIFICATION, fieldsToRemove);
        if (Objects.isNull(dbObject))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Record doesn't exist with id : " + notificationId);

        if (Objects.isNull(dbObject.get("endTime")) || Long.valueOf(dbObject.get("endTime").toString()) < CommonUtils.getCurrentTimeInMillis())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This Notification event is past, Hence cannot be updated");

        BasicDBObject query = new BasicDBObject("id", notificationId);
        BasicDBObject dataToUpdate = new BasicDBObject("$set", new BasicDBObject("active", isActive));
        try {
            mongoServiceImpl.update(query, dataToUpdate, false, false, APPLICATION_NOTIFICATION);
            Long startTime = Objects.nonNull(dbObject.get("startTime")) ? Long.valueOf(dbObject.get("startTime").toString()) : 0;
            Long endTime = Objects.nonNull(dbObject.get("endTime")) ? Long.valueOf(dbObject.get("endTime").toString()) : 0;
            Long time = CommonUtils.getCurrentTimeInMillis();
            if (isActive && (startTime <= time && endTime >= time)) {
                HashMap<String, String> content = new HashMap<>();
                content.put("severity", String.valueOf(dbObject.get("severity")));
                content.put("content", String.valueOf(dbObject.get("content")));
                mqttService.publishToMqtt(new HashMap<>(), JSON.serialize(content), NOTIFICATION_TOPIC);
            }
        } catch (Exception e) {
            LOG.error("Error in updating notification. ", e);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some error occured.");
        }
    }

    public String getNotificationMqttInfo() throws Exception {
        String vmqNotificationPassword = environment.getProperty(VMQ_NOTIFICATION_PASSWORD);
        String passwordPrefix = RandomStringUtils.randomAlphanumeric(8);
        String passwordSuffix = RandomStringUtils.randomAlphanumeric(6);

        String toEncrypt = passwordPrefix + vmqNotificationPassword + passwordSuffix;
        String encryptedValue = Base64.getEncoder().encodeToString(toEncrypt.getBytes());

        return encryptedValue;
    }

//    public HashMap<String, Object> getCloudWatchMetricsForVolumeStats(VolumeStatisticsRequest volumeStatisticsRequest) throws Exception {
//        HashMap<String, Object> volumeStats = new HashMap<>();
//        if (Objects.nonNull(volumeStatisticsRequest)) {
//            HashMap<String, Object> ipList = getIPFromInstanceId();
//            for (String instanceId : volumeStatisticsRequest.getInstanceId()) {
//                String ip = String.valueOf(ipList.get(instanceId));
//                List<Volume> response = amazonEC2Service.getStatsForInstanceId(instanceId);
//                if (Objects.nonNull(response)) {
//                    List<HashMap<String, Object>> volumeForInst = new ArrayList<>();
//                    for (Volume volume : response) {
//                        HashMap res = new HashMap();
//                        res.put("volumeId", volume.getVolumeId());
//                        res.put("iops", volume.getIops());
//                        res.put("volumeType", volume.getVolumeType());
//                        res.put("size", volume.getSize());
//                        res.put("state", volume.getState());
//                        res.put("encrypted", volume.getEncrypted());
//
//                        volumeForInst.add(res);
//                    }
//
//                    if (!volumeForInst.isEmpty())
//                        volumeForInst.sort(Comparator.comparing(q -> Integer.valueOf(q.get("size").toString())));
//
//                    volumeStats.put(ip, volumeForInst);
//                }
//            }
//        }
//
//        return volumeStats;
//    }

    public HashMap<String, Object> getIPFromInstanceId() throws Exception {
        List<LinkedHashMap<String, Object>> appStats = getApplicationServiceStats().getServiceHealthStats();
        HashMap<String, Object> data = new HashMap<>();
        appStats.stream().forEach(ele -> {
            List<HashMap> details = (List<HashMap>) ele.get("details");
            if (Objects.nonNull(details) && !details.isEmpty()) {
                for (HashMap serviceDetail : details) {
                    data.put(String.valueOf(serviceDetail.get("instanceId")), serviceDetail.get("ip"));
                }
            }
        });

        return data;
    }

    public HashMap<String, Object> getHistoricalStatsForRPS(HistoricalResourceSpecifications historicalResourceSpecifications) {
        if (Objects.isNull(historicalResourceSpecifications.getMinutes()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration");
        HashMap<String, Object> response = new HashMap<>();
        Set<String> keys = new HashSet<>();

        List<HashMap<String, Object>> dataPointList = new ArrayList<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("timestamp", new BasicDBObject("$gte", CommonUtils.getCurrentTimeInMillis() - historicalResourceSpecifications.getMinutes() * 60 * 1000));

        List<DBObject> objectList = mongoServiceImpl.findList(NGINX_STATS, query, TIMESTAMP, DESC);

        for (DBObject object : objectList) {
            boolean flag = false;
            List<HashMap<String, Object>> requestPerSecList = (List<HashMap<String, Object>>) object.get("requestPerSec");
            HashMap<String, Object> dataPoints = new HashMap<>();
            if (Objects.nonNull(requestPerSecList) && !requestPerSecList.isEmpty()) {
                for (HashMap<String, Object> reqPerSec : requestPerSecList) {
                    if (Objects.nonNull(reqPerSec) && historicalResourceSpecifications.getInstanceIds().contains(String.valueOf(reqPerSec.get("instanceId")))) {
                        keys.add(String.valueOf(reqPerSec.get("instanceId")));
                        dataPoints.put(String.valueOf(reqPerSec.get("instanceId")), reqPerSec.get("rps"));
                        flag = true;
                    }
                }
            }

            if (flag) {
                dataPoints.put("timestamp", object.get("timestamp"));
                dataPointList.add(dataPoints);
            }
        }

        if (!dataPointList.isEmpty()) {
            response.put("keys", keys);
            dataPointList.sort(Comparator.comparingLong(q -> (Long) q.get("timestamp")));
            response.put("dataPoints", dataPointList);
        } else {
            response = null;
        }
        return response;
    }


    public HashMap<String, Object> getHistoricalStatsForCPS(HistoricalResourceSpecifications historicalResourceSpecifications) {
        if (Objects.isNull(historicalResourceSpecifications.getMinutes()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration");
        HashMap<String, Object> response = new HashMap<>();
        Set<String> keys = new HashSet<>();

        List<HashMap<String, Object>> dataPointList = new ArrayList<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("timestamp", new BasicDBObject("$gte", CommonUtils.getCurrentTimeInMillis() - historicalResourceSpecifications.getMinutes() * 60 * 1000));

        List<DBObject> objectList = mongoServiceImpl.findList(NGINX_STATS, query, TIMESTAMP, DESC);

        for (DBObject object : objectList) {
            boolean flag = false;
            List<HashMap<String, Object>> connectionPerSecList = (List<HashMap<String, Object>>) object.get("connectionPerSec");
            HashMap<String, Object> dataPoints = new HashMap<>();
            if (Objects.nonNull(connectionPerSecList) && !connectionPerSecList.isEmpty()) {
                for (HashMap<String, Object> connPerSec : connectionPerSecList) {
                    if (Objects.nonNull(connPerSec) && historicalResourceSpecifications.getInstanceIds().contains(String.valueOf(connPerSec.get("instanceId")))) {
                        keys.add(String.valueOf(connPerSec.get("instanceId")));
                        dataPoints.put(String.valueOf(connPerSec.get("instanceId")), connPerSec.get("cps"));
                        flag = true;
                    }
                }
            }

            if (flag) {
                dataPoints.put("timestamp", object.get("timestamp"));
                dataPointList.add(dataPoints);
            }
        }

        if (!dataPointList.isEmpty()) {
            response.put("keys", keys);
            dataPointList.sort(Comparator.comparingLong(q -> (Long) q.get("timestamp")));
            response.put("dataPoints", dataPointList);
        } else {
            response = null;
        }
        return response;
    }

    public HashMap<String, Object> getHistoricalStatsForRPC(HistoricalResourceSpecifications historicalResourceSpecifications) {
        if (Objects.isNull(historicalResourceSpecifications.getMinutes()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid duration");
        HashMap<String, Object> response = new HashMap<>();
        Set<String> keys = new HashSet<>();

        List<HashMap<String, Object>> dataPointList = new ArrayList<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("timestamp", new BasicDBObject("$gte", CommonUtils.getCurrentTimeInMillis() - historicalResourceSpecifications.getMinutes() * 60 * 1000));

        List<DBObject> objectList = mongoServiceImpl.findList(NGINX_STATS, query, TIMESTAMP, DESC);

        for (DBObject object : objectList) {
            boolean flag = false;
            List<HashMap<String, Object>> requestPerConnectionList = (List<HashMap<String, Object>>) object.get("requestPerConnection");
            HashMap<String, Object> dataPoints = new HashMap<>();
            if (Objects.nonNull(requestPerConnectionList) && !requestPerConnectionList.isEmpty()) {
                for (HashMap<String, Object> reqPerCon : requestPerConnectionList) {
                    if (Objects.nonNull(reqPerCon) && historicalResourceSpecifications.getInstanceIds().contains(String.valueOf(reqPerCon.get("instanceId")))) {
                        keys.add(String.valueOf(reqPerCon.get("instanceId")));
                        dataPoints.put(String.valueOf(reqPerCon.get("instanceId")), reqPerCon.get("rpc"));
                        flag = true;
                    }
                }
            }

            if (flag) {
                dataPoints.put("timestamp", object.get("timestamp"));
                dataPointList.add(dataPoints);
            }
        }

        if (!dataPointList.isEmpty()) {
            response.put("keys", keys);
            dataPointList.sort(Comparator.comparingLong(q -> (Long) q.get("timestamp")));
            response.put("dataPoints", dataPointList);
        } else {
            response = null;
        }
        return response;
    }

    public Object getETLFileUploadConfig(String type) {
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        BasicDBObject queryParam = new BasicDBObject();
        queryParam.put("type", type);

        return mongoServiceImpl.findList(queryParam, ETL_UPLOAD_CONFIG, fieldsToRemove);
    }

    public void updateETLFileConfig(List<ETLFileConfigRequest> etlFileConfigRequestList) {
        for (ETLFileConfigRequest etlFileConfigRequest : etlFileConfigRequestList) {
            HashMap queryParam = new HashMap();
            queryParam.put("id", etlFileConfigRequest.getId());

            HashMap appendableParams = new HashMap();

            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("_id", ZERO);

            DBObject configParam = mongoServiceImpl.findOne(queryParam, appendableParams, ETL_UPLOAD_CONFIG, fieldsToRemove);
            if (Objects.isNull(configParam))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Record not found with id :: " + etlFileConfigRequest.getId());

            boolean active = etlFileConfigRequest.isActive();

            DBObject query = new BasicDBObject().append("id", etlFileConfigRequest.getId());

            BasicDBObject updateParams = new BasicDBObject();
            updateParams.put("active", active);

            DBObject update = new BasicDBObject().append("$set", updateParams);

            mongoServiceImpl.update(query, update, true, false, ETL_UPLOAD_CONFIG);
        }
    }

    public HashMap<String, Object> performSdaAction(SDAActions sdaActions) throws Exception {
        HashMap<String, String> headerInfo = new HashMap<>();
        HashMap<String, Object> queryParams = new HashMap<>();
        String url = null;
        HashMap<String, Object> response = new HashMap<>();
        String operation = sdaActions.getOperation();
        if (sdaActions.getIpAddress().isEmpty() || Objects.isNull(sdaActions.getIpAddress())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "IP address list cannot be empty");
        }

        if (!sdaActions.getOperation().equals(SDAOperations.RUN_SHELL_SCRIPT.name())) {
            for (String ipAddress : sdaActions.getIpAddress()) {
                try {
                    String res = null;
                    url = "http://" + ipAddress + SDAOperations.valueOf(operation).getValue();
                    res = httpService.doGet(url, headerInfo, queryParams);
                    HashMap convertedRes = objectMapper.readValue(res, HashMap.class);
                    response.put(ipAddress, convertedRes);
                } catch (Exception e) {
                    HashMap res = new HashMap();
                    res.put("status", "success");
                    res.put("output", "");
                    response.put(ipAddress, res);
                }
            }
        } else {
            for (String ipAddress : sdaActions.getIpAddress()) {
                String res = null;
                HashMap<String, Object> body = new HashMap<>();

                HashMap<String, Object> filter = new HashMap<>();
                filter.put("service", sdaActions.getService());

                DBObject filteredService = mongoServiceImpl.findOne(SERVICE_ACTIONS, filter);

                if (Objects.nonNull(filteredService.get("action"))) {
                    DBObject actionObject = (DBObject) filteredService.get("action");
                    body.put("script", actionObject.get(sdaActions.getAction()));
                }

                url = "http://" + ipAddress + SDAOperations.valueOf(operation).getValue();

                String requestString = objectMapper.writeValueAsString(body);
                StringEntity stringEntity = new StringEntity(requestString);

                res = httpService.doPost(stringEntity, url, headerInfo);
                HashMap convertedRes = objectMapper.readValue(res, HashMap.class);
                response.put(ipAddress, convertedRes);
            }
        }
        return response;
    }

    private Set<String> getActions(String serviceName) {
        Set<String> keys = new HashSet<>();
        HashMap<String, Object> filter = new HashMap<>();
        filter.put("service", serviceName);
        DBObject service = mongoServiceImpl.findOne(SERVICE_ACTIONS, filter);
        if (Objects.nonNull(service)) {
            DBObject action = (DBObject) service.get("action");
            if (Objects.nonNull(action)) {
                keys = action.keySet();
            }
        }
        return keys;
    }

    public DBObject performRpcStats(String operation, Long duration) {
        Long currentTime = System.currentTimeMillis();
        HashMap<String, Object> rpcStatsMap = new HashMap<>();
        if (Objects.isNull(operation) || operation.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Operation Must Not Be Empty");

        if (!Stream.of(AuditorEnum.values()).map(AuditorEnum::getValue).filter(q -> q.contains("RPC")).sorted().collect(Collectors.toList()).contains(operation))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This Operation does not exist");

        rpcStatsMap.put("operation", operation);
        return processRpcStatsAggregation(rpcStatsMap, duration, currentTime);
    }

    public DBObject performRpcStats(Long duration) {
        Long currentTime = System.currentTimeMillis();
        HashMap<String, Object> rpcStatsMap = new HashMap<>();
        return processRpcStatsAggregation(rpcStatsMap, duration, currentTime);
    }

    private DBObject processRpcStatsAggregation(HashMap<String, Object> rpcStatsMap, Long duration, Long currentTime) {
        BasicDBObject service;
        if (duration > LAST_SEVEN_DAYS_MIN)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Duration Must Not Be Greater Than 7 Days ( 10080 Minutes )");
        duration = duration * 60 * 1000;
        rpcStatsMap.put("fromTime", currentTime - duration);
        rpcStatsMap.put("toTime", currentTime);

        try {
            service = mongoServiceImpl.aggregateDataForRPCStats(rpcStatsMap, RPC_STATS);
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Unable To Process the Query In Mongo :  " + e.toString());
        }

        if (Objects.nonNull(service) && !service.isEmpty()) {
            DecimalFormat df = new DecimalFormat("###.##");
            service.put("successRate", df.format(Double.valueOf(service.get("successRate").toString())));
            service.put("failureRate", df.format(Double.valueOf(service.get("failureRate").toString())));
            service.put("avgProcessingTime", df.format(Double.valueOf(service.get("avgProcessingTime").toString())));
        } else {
            service = new BasicDBObject();
            if (Objects.nonNull(rpcStatsMap.get("operation")))
                service.put("operation", rpcStatsMap.get("operation"));
            service.put("totalCount", 0);
            service.put("success", 0);
            service.put("failure", 0);
            service.put("successRate", 0);
            service.put("failureRate", 0);
            service.put("avgProcessingTime", 0);

        }
        return service;
    }

    public List totalUsersInfo(Long duration) {

        long currentTime = CommonUtils.getCurrentTimeInMillis();
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("timestamp", new BasicDBObject("$gte", currentTime - duration * 60 * 1000));

        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", ZERO);
        projection.put("timestamp", ONE);
        projection.put("dateCreated", ONE);
        projection.put("totalLoggedInUsers", ONE);

        if (!CommonUtils.isSysAdmin()) {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            queryParams.put("totalUsersByCompartment.compartmentId", groupId);
            projection.put("totalUsersByCompartment", new BasicDBObject("$elemMatch", new BasicDBObject("compartmentId", groupId)));
        } else {
            projection.put("totalUsersByCompartment", ONE);
        }

        return mongoServiceImpl.findList(TOTAL_USER_INFO, queryParams, TIMESTAMP, ASC, projection);
    }


//    public DBObject getETLVersionCounts() {
//
//        BasicDBObject query = new BasicDBObject();
//        BasicDBObject response = new BasicDBObject();
//
//        if(!etlVersionCount.trim().isEmpty()) {
//            String[] etlVersions = etlVersionCount.split(",");
//            int totalCount = etlVersions.length;
//
//            for (int i = 0; i < totalCount; i++) {
//                query.put("etlVersion", etlVersions[i]);
//                long count = mongoServiceImpl.count(query, null, "apDetail");
//                response.put(etlVersions[i], count);
//            }
//        }
//        return  response ;
//    }


}
