package com.incs83.app.business.v2;

import com.incs83.app.common.v3.LoginUserInfoRequestDTO;
import com.incs83.app.constants.queries.AuditLogsSQL;
import com.incs83.app.constants.queries.LoginAnalysisSQL;
import com.incs83.app.entities.AuditLogs;
import com.incs83.app.responsedto.v2.loginAnalysis.LoginAnalysisResponseDTO;
import com.incs83.app.responsedto.v2.loginAnalysis.LoginUserInfoResponseDTO;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.CommonService;
import org.apache.logging.log4j.LogManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.constants.ApplicationCommonConstants.*;

@Service
public class ManageLoginAnalysisService {

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private CommonService commonService;


    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger("org");

    public LoginAnalysisResponseDTO getLoginAnalysis() throws Exception {
        Long totalSuccessIAM = 0l;
        Long totalSuccessOAUTH = 0l;
        Long totalFailIAM = 0l;
//        Long totalFailOauth = 0l;
        Long attemptsLimit = 0L;

        HashMap<String, String> param = new HashMap<>();
        param.put("operation", "User Login (IAM)");
        totalSuccessIAM = Long.valueOf(dataAccessService.read(AuditLogs.class, LoginAnalysisSQL.GET_LOGIN_ANALYSIS_BY_OPERATION, param).iterator().next().toString());

        param.clear();
        param.put("operation", "User Login (Oauth)");
        totalSuccessOAUTH = Long.valueOf(dataAccessService.read(AuditLogs.class, LoginAnalysisSQL.GET_LOGIN_ANALYSIS_BY_OPERATION, param).iterator().next().toString());

        param.clear();
        param.put("operation", "Failed User Login (IAM)");
        totalFailIAM = Long.valueOf(dataAccessService.read(AuditLogs.class, LoginAnalysisSQL.GET_LOGIN_ANALYSIS_BY_OPERATION, param).iterator().next().toString());

//        param.clear();
//        param.put("op1", "OAUTH2 AUTHENTICATION EXCEPTION");
//        param.put("op2", "OAUTH2 INVALID CODE EXCEPTION");
//        param.put("op3", "OAUTH2 CONFIG EXCEPTION");
//        totalFailOauth = Long.valueOf(dataAccessService.read(AuditLogs.class, LoginAnalysisSQL.GET_LOGIN_ANALYSIS_BY_OPERATION_FAIL_OAUTH, param).iterator().next().toString());

        LoginAnalysisResponseDTO loginAnalysisResponseDTO = new LoginAnalysisResponseDTO();
        loginAnalysisResponseDTO.setTotalSuccessIAM(totalSuccessIAM);
        loginAnalysisResponseDTO.setTotalSuccessOAUTH(totalSuccessOAUTH);
        loginAnalysisResponseDTO.setTotalFailIAM(totalFailIAM);
//        loginAnalysisResponseDTO.setTotalFAILOAUTH(totalFailOauth);
        attemptsLimit = Long.valueOf(commonService.read(COMMON_CONFIG).get(LOGIN_ATTEMPTS));
        loginAnalysisResponseDTO.setAttemptsLimit(attemptsLimit);

        return loginAnalysisResponseDTO;
    }

    public LoginUserInfoResponseDTO getLoginUserInfo(LoginUserInfoRequestDTO loginUserInfoRequestDTO) {
        LOG.info("Request called");
        String loginUserInfo = LoginAnalysisSQL.GET_LOGIN_USER_INFO;
        StringBuilder query = new StringBuilder(loginUserInfo);
        StringBuilder countQuery = new StringBuilder(LoginAnalysisSQL.GET_COUNT_LOGIN_USER_INFO);

        if (Objects.nonNull(loginUserInfoRequestDTO)) {
            boolean flag = false;
            if (Objects.nonNull(loginUserInfoRequestDTO.getName()) && !loginUserInfoRequestDTO.getName().isEmpty()) {
                String likeNameCondition = " where ( u.firstName like '%" + loginUserInfoRequestDTO.getName().toLowerCase() + "%' or u.lastName like '%" + loginUserInfoRequestDTO.getName().toLowerCase() + "%' )";
                query.append(likeNameCondition);
                String likeNameConditionForCount = " and ( u.firstName like '%" + loginUserInfoRequestDTO.getName().toLowerCase() + "%' or u.lastName like '%" + loginUserInfoRequestDTO.getName().toLowerCase() + "%' )";
                countQuery.append(likeNameConditionForCount);
                flag = true;
            }

            if (Objects.nonNull(loginUserInfoRequestDTO.getRole()) && !loginUserInfoRequestDTO.getRole().isEmpty()) {
                String likeRoleCondition = "";
                if (!flag) {
                    likeRoleCondition = " where u.role like '%" + loginUserInfoRequestDTO.getRole().toUpperCase() + "%' ";
                } else {
                    likeRoleCondition = " and u.role like '%" + loginUserInfoRequestDTO.getRole().toUpperCase() + "%' ";
                }
                query.append(likeRoleCondition);
                String likeRoleConditionforCount = " and r.name like '%" + loginUserInfoRequestDTO.getRole().toUpperCase() + "%' ";
                countQuery.append(likeRoleConditionforCount);
            }


            if (loginUserInfoRequestDTO.isActionable()) {
                Long attemptsLimit = Long.valueOf(commonService.read(COMMON_CONFIG).get(LOGIN_ATTEMPTS));
                String actionableCondition = " where u.passwordAttempt >= '" + attemptsLimit + "' and u.email <> '" + ROOT_ADMIN_EMAIL + "' ";
                query.append(actionableCondition);
                actionableCondition = " and u.passwordAttempt >= '" + attemptsLimit + "' and u.email <> '" + ROOT_ADMIN_EMAIL + "' ";
                countQuery.append(actionableCondition);
            }


            if (Objects.nonNull(loginUserInfoRequestDTO.getSortBy())) {
                String sortBy = loginUserInfoRequestDTO.getSortBy();
                String order = "ASC";
                if (Objects.nonNull(loginUserInfoRequestDTO.getOrder()))
                    order = loginUserInfoRequestDTO.getOrder();
                String sorted = " order by " + sortBy + " " + order;
                query.append(sorted);
            }

            int offset = 0;
            int max = 10;
            if (Objects.nonNull(loginUserInfoRequestDTO.getOffset()))
                offset = loginUserInfoRequestDTO.getOffset();
            if (Objects.nonNull(loginUserInfoRequestDTO.getMax()))
                max = loginUserInfoRequestDTO.getMax();

            String paginated = " limit " + offset + "," + max + " ;";
            query.append(paginated);
        }
        LOG.info(query.toString());

        List<Object[]> userInfoList = null;
        Long countUserinfo = 0l;

        try {
            userInfoList = dataAccessService.readNative(query.toString(), new HashMap<String, Object>());
            countUserinfo = Long.valueOf(dataAccessService.readNative(countQuery.toString(), new HashMap<String, Object>()).iterator().next().toString());
        } catch (Exception e) {
            LOG.error("Error in processing query " + e.toString());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), e.toString());
        }

        LoginUserInfoResponseDTO loginUserInfoResponse = new LoginUserInfoResponseDTO();
        LoginUserInfoResponseDTO.LoginUserInfoResponseData loginUserInfoResponseData1 = loginUserInfoResponse.new LoginUserInfoResponseData();
        List<LoginUserInfoResponseDTO.LoginUserInfoResponseData> loginUserInfoResponseDTOList = new ArrayList<>();
        if (Objects.nonNull(userInfoList) && !userInfoList.isEmpty()) {
            for (Object[] userInfo : userInfoList) {
                LoginUserInfoResponseDTO loginUserInfoResponseDTO = new LoginUserInfoResponseDTO();
                LoginUserInfoResponseDTO.LoginUserInfoResponseData loginUserInfoResponseData = loginUserInfoResponseDTO.new LoginUserInfoResponseData();
                loginUserInfoResponseData.setRole(Objects.nonNull(userInfo[0]) ? userInfo[0].toString() : null);
                loginUserInfoResponseData.setPasswordAttempt(Objects.nonNull(userInfo[1]) ? userInfo[1].toString() : null);
                loginUserInfoResponseData.setPasswordAge(Objects.nonNull(userInfo[2]) ? Long.valueOf(userInfo[2].toString()) : null);
                loginUserInfoResponseData.setLastLogin(Objects.nonNull(userInfo[3]) ? userInfo[3].toString() : null);
                loginUserInfoResponseData.setName(Objects.nonNull(userInfo[4]) ? userInfo[4].toString() : null);
                loginUserInfoResponseData.setIp(Objects.nonNull(userInfo[5]) ? userInfo[5].toString() : null);
                loginUserInfoResponseData.setId(Objects.nonNull(userInfo[6]) ? userInfo[6].toString() : null);
                loginUserInfoResponseDTOList.add(loginUserInfoResponseData);
            }
            loginUserInfoResponse.setLoginUserInfoResponseDataList(loginUserInfoResponseDTOList);
            loginUserInfoResponse.setCount(countUserinfo);
        } else {
            loginUserInfoResponse.setLoginUserInfoResponseDataList(new ArrayList<>());
            loginUserInfoResponse.setCount(0l);
        }
        return loginUserInfoResponse;
    }




}
