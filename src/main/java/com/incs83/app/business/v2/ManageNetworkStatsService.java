package com.incs83.app.business.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.entities.Compartment;
import com.incs83.app.responsedto.v2.NetworkStats.AirTimeUtlzDTO;
import com.incs83.app.responsedto.v2.NetworkStats.AirTimeUtlzDist;
import com.incs83.app.responsedto.v2.NetworkStats.ChannelDistDTO;
import com.incs83.app.responsedto.v2.NetworkStats.CommonDistDTO;
import com.incs83.app.responsedto.v2.NetworkStats.DlSpeedDTO;
import com.incs83.app.responsedto.v2.NetworkStats.HealthDTO;
import com.incs83.app.responsedto.v2.NetworkStats.HealthDistDTO;
import com.incs83.app.responsedto.v2.NetworkStats.SignalStrengthDistDTO;
import com.incs83.app.responsedto.v2.NetworkStats.UlSpeedDTO;
import com.incs83.app.responsedto.v2.Subscriber.WifiMeterDataDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.CalendarUtils;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.mt.DataAccessService;
import com.incs83.mt.MongoTenantTemplate;
import com.incs83.util.CommonUtils;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.STATS_PER_ISP_PER_DAY;
import static com.incs83.app.constants.misc.ActiontecConstants.STATS_PER_ISP_PER_HOUR;
import static com.incs83.app.constants.misc.ActiontecConstants.STATS_PER_USER_PER_DAY;
import static com.incs83.app.constants.misc.ActiontecConstants.STATS_PER_USER_PER_HOUR;
import static com.incs83.app.constants.misc.ActiontecConstants.wifiAirTimeUtlz;
import static com.incs83.app.constants.misc.ActiontecConstants.wifiDlSpeed;
import static com.incs83.app.constants.misc.ActiontecConstants.wifiHealth;
import static com.incs83.app.constants.misc.ActiontecConstants.wifiUlSpeed;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class ManageNetworkStatsService {
    private static final Logger logger = LogManager.getLogger(ManageNetworkStatsService.class);

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    MongoTenantTemplate mongoTemplate;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private ISPService ispService;

    private String getISPNameByCompId(String compartmentId) {
        String ispName = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, compartmentId);
            if (Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId())) {
                ISPDTO ispdto = ispService.getISPById(compartment.getIspId());
                ispName = ispdto.getData().getName();
            }
        } catch (Exception e) {
        }
        return ispName;
    }

    public WifiMeterDataDTO getMeterData(String id, String type) throws Exception {
        WifiMeterDataDTO wifiMeterDataDTO = new WifiMeterDataDTO();
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        HashMap<String, Object> queryParams = new HashMap<>();
        HashMap<String, Object> appendableParams = new HashMap<>();
        HashMap<String, String> aggregationParams = new HashMap<>();
        if (id.equals(String.valueOf(ZERO))) {
            DBObject wifiInsightPerHour = mongoService.getLastStatsOneRecord(null, STATS_PER_ISP_PER_HOUR);
            if (Objects.nonNull(wifiInsightPerHour)) {
                queryParams.clear();
                List<Date> dates = new ArrayList<>();
                dates.add((Date) wifiInsightPerHour.get("statsDateTime"));
                queryParams.put("statsDateTime", dates);
                queryParams.put("hour", ((BasicDBObject) wifiInsightPerHour.get("statsData")).get("k"));
            } else {
                ZonedDateTime today = ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS);
                queryParams.clear();
                List<Date> dates = new ArrayList<>();
                dates.add(Date.from(today.toInstant()));
                queryParams.put("statsDateTime", dates);
                queryParams.put("hour", today.getHour() - 1);
            }

            try {
                if (type.equals(wifiHealth)) {
                    aggregationParams.clear();
                    aggregationParams.put("outputParams", "sumScore");
                    aggregationParams.put("label", "wifiHealth");
                    aggregationParams.put("operation", "$avg");
                    aggregationParams.put("keyToAggregate", "$statsData.v.wifiHealthScore");

                    DBObject wifiHealthStats = mongoService.aggregateDataForLastData(queryParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR);
                    wifiMeterDataDTO.setData(Objects.isNull(wifiHealthStats) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.isNull(wifiHealthStats.get("sumScore")) ? "0.0" : wifiHealthStats.get("sumScore").toString()))));
                }

                if (type.equals(wifiDlSpeed)) {
                    aggregationParams.clear();
                    aggregationParams.put("outputParams", "sumLastDataDownlinkRate");
                    aggregationParams.put("label", "wifiDlRate");
                    aggregationParams.put("operation", "$avg");
                    aggregationParams.put("keyToAggregate", "$statsData.v.avgDataDownlinkRate");

                    DBObject wifiDlUlStats = mongoService.aggregateDataForLastData(queryParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR);
                    wifiMeterDataDTO.setData(Objects.isNull(wifiDlUlStats) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.isNull(wifiDlUlStats.get("sumLastDataDownlinkRate")) ? "0" : wifiDlUlStats.get("sumLastDataDownlinkRate").toString()))) / 1000F);
                }

                if (type.equals(wifiUlSpeed)) {
                    aggregationParams.clear();
                    aggregationParams.put("outputParams", "sumLastDataUplinkRate");
                    aggregationParams.put("label", "wifiUlRate");
                    aggregationParams.put("operation", "$avg");
                    aggregationParams.put("keyToAggregate", "$statsData.v.avgDataUplinkRate");

                    DBObject wifiDlUlStats = mongoService.aggregateDataForLastData(queryParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR);
                    wifiMeterDataDTO.setData(Objects.isNull(wifiDlUlStats) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.isNull(wifiDlUlStats.get("sumLastDataUplinkRate")) ? "0.0" : wifiDlUlStats.get("sumLastDataUplinkRate").toString()))) / 1000F);
                }

            } catch (NumberFormatException e) {
                wifiMeterDataDTO.setData(0);
            }


        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                DBObject wifiDlUlStats;
                DBObject wifiHealthStats;
                HashMap<String, Object> inParams = new HashMap<>();
                if (manageCommonService.isDefaultCluster(id)) {
                    String ispName = getISPNameByCompId(groupId);
                    queryParams.clear();
                    queryParams.put("isp", ispName);
                    appendableParams.clear();

                    try {
                        if (type.equals(wifiUlSpeed) || type.equals(wifiDlSpeed)) {
                            wifiDlUlStats = mongoService.getLastStatsOneRecord(queryParams, STATS_PER_ISP_PER_HOUR);

                            if (Objects.nonNull(wifiDlUlStats)) {
                                DBObject sData = ((BasicDBObject) ((BasicDBObject) wifiDlUlStats.get("statsData")).get("v"));
                                if (type.equals(wifiUlSpeed)) {
                                    wifiMeterDataDTO.setData(Objects.isNull(sData) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format((Double.valueOf(Objects.isNull(sData.get("avgDataUplinkRate")) ? "0.0" : sData.get("avgDataUplinkRate").toString())) / 1000F)));
                                }

                                if (type.equals(wifiDlSpeed)) {
                                    wifiMeterDataDTO.setData(Objects.isNull(sData) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format((Double.valueOf(Objects.isNull(sData.get("avgDataDownlinkRate")) ? "0.0" : sData.get("avgDataDownlinkRate").toString())) / 1000F)));
                                }
                            } else {
                                wifiMeterDataDTO.setData(0);
                            }
                        } else if (type.equals(wifiHealth)) {
                            wifiHealthStats = mongoService.getLastStatsOneRecord(queryParams, STATS_PER_ISP_PER_HOUR);
                            if (Objects.nonNull(wifiHealthStats)) {
                                DBObject sData = ((BasicDBObject) ((BasicDBObject) wifiHealthStats.get("statsData")).get("v"));
                                wifiMeterDataDTO.setData(Objects.isNull(wifiHealthStats) ? 0 : (Objects.isNull(sData.get("wifiHealthScore")) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.isNull(sData.get("wifiHealthScore")) ? "0.0" : sData.get("wifiHealthScore").toString())))));
                            } else {
                                wifiMeterDataDTO.setData(0);
                            }
                        }
                    } catch (NumberFormatException e) {
                        wifiMeterDataDTO.setData(0.0);
                    }

                } else {
                    Set<String> associatedAps = manageCommonService.getAssociatedApsByClusterId(id);
                    mongoFieldOptions.clear();
                    mongoFieldOptions.put("_id", 0);
                    HashMap<String, Object> inClause = new HashMap<>();
                    inClause.put("resourceName", "userId");
                    inClause.put("resource", associatedAps);
                    inParams.put("in", inClause);

                    DBObject userWifiInsightPerHour = mongoService.getLastStatsOneRecord(queryParams, STATS_PER_USER_PER_HOUR);;
                    if (Objects.nonNull(userWifiInsightPerHour)) {
                        queryParams.clear();
                        List<Date> dates = new ArrayList<>();
                        dates.add((Date) userWifiInsightPerHour.get("statsDateTime"));
                        queryParams.put("statsDateTime", dates);
                        queryParams.put("hour", ((BasicDBObject) userWifiInsightPerHour.get("statsData")).get("k"));
                    } else {
                        ZonedDateTime today = ZonedDateTime.now(ZoneOffset.UTC).truncatedTo(ChronoUnit.DAYS);
                        queryParams.clear();
                        List<Date> dates = new ArrayList<>();
                        dates.add(Date.from(today.toInstant()));
                        queryParams.put("statsDateTime", dates);
                        queryParams.put("hour", today.getHour() - 1);
                    }


                    try {
                        if (type.equals(wifiUlSpeed) || type.equals(wifiDlSpeed)) {

                            aggregationParams.clear();
                            aggregationParams.put("outputParams", "sumLastDataUplinkRate,sumLastDataDownlinkRate");
                            aggregationParams.put("label", "uldlRate");
                            aggregationParams.put("operation", "$avg");
                            aggregationParams.put("keyToAggregate", "$statsData.v.avgDataUplinkRate," +
                                                                    "$statsData.v.avgDataDownlinkRate");
                            wifiDlUlStats = mongoService.aggregateDataForLastData(queryParams, inParams, aggregationParams, STATS_PER_USER_PER_HOUR);

                            if (Objects.nonNull(wifiDlUlStats)) {
                                if (type.equals(wifiUlSpeed)) {
                                    wifiMeterDataDTO.setData(Objects.isNull(wifiDlUlStats) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format((Double.valueOf(Objects.isNull(wifiDlUlStats.get("sumLastDataUplinkRate")) ? "0.0" : wifiDlUlStats.get("sumLastDataUplinkRate").toString())) / 1000F)));
                                }

                                if (type.equals(wifiDlSpeed)) {
                                    wifiMeterDataDTO.setData(Objects.isNull(wifiDlUlStats) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format((Double.valueOf(Objects.isNull(wifiDlUlStats.get("sumLastDataDownlinkRate")) ? "0.0" : wifiDlUlStats.get("sumLastDataDownlinkRate").toString())) / 1000F)));
                                }
                            } else {
                                wifiMeterDataDTO.setData(0);
                            }
                        } else if (type.equals(wifiHealth)) {
                            aggregationParams.clear();
                            aggregationParams.put("outputParams", "sumScore");
                            aggregationParams.put("label", "healthScore");
                            aggregationParams.put("operation", "$avg");
                            aggregationParams.put("keyToAggregate", "$statsData.v.wifiHealthScore");
                            wifiHealthStats = mongoService.aggregateDataForLastData(queryParams, inParams, aggregationParams, STATS_PER_USER_PER_HOUR);

                            if (Objects.nonNull(wifiHealthStats)) {
                                wifiMeterDataDTO.setData(Objects.isNull(wifiHealthStats) ? 0 : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.valueOf(Objects.isNull(wifiHealthStats.get("sumScore")) ? "0.0" : wifiHealthStats.get("sumScore").toString()))));
                            } else {
                                wifiMeterDataDTO.setData(0);
                            }
                        }
                    } catch (NumberFormatException e) {
                        wifiMeterDataDTO.setData(0);
                    }
                }
            } else {
                wifiMeterDataDTO.setData(0);
            }
        }

        //================================================Bytes Conversion to Mbps================================================//

        return wifiMeterDataDTO;
    }

    public CommonDistDTO getUploadSpeedDist(String id) throws Exception {
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
        HashMap<String, String> aggregationParams = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();

        DBObject wifiUlDistToday = null;
        DBObject wifiUlDistLast7Days = null;
        DBObject wifiUllDistLast30Days = null;
        DBObject wifiUllDistLast90Days = null;
        String outputParams;

        aggregationParams.clear();
        aggregationParams.put("outputParams", "Poor,Fair,Good,Very Good,Excellent");
        aggregationParams.put("label", "wifiUlSpeedDist");
        aggregationParams.put("operation", "$avg");
        aggregationParams.put("keyToAggregate", "$statsData.v.metricLevels.uploadSpeed.lt6," +
                                                "$statsData.v.metricLevels.uploadSpeed.gte6lt12," +
                                                "$statsData.v.metricLevels.uploadSpeed.gte12lt30," +
                                                "$statsData.v.metricLevels.uploadSpeed.gte30lt60," +
                                                "$statsData.v.metricLevels.uploadSpeed.gte60");

        if (id.equals(String.valueOf(ZERO))) {
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            wifiUlDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            wifiUlDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            wifiUllDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            wifiUllDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                HashMap<String, Object> inParams = new HashMap<>();
                HashMap<String, Object> aggParams = new HashMap<>();
                if (manageCommonService.isDefaultCluster(id)) {
                    String ispName = getISPNameByCompId(groupId);
                    aggParams.put("isp", ispName);

                    wifiUlDistToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiUlDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiUllDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiUllDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                } else {
                    Set<String> associatedAps = manageCommonService.getAssociatedApsByClusterId(id);
                    mongoFieldOptions.clear();
                    mongoFieldOptions.put("_id", 0);
                    HashMap<String, Object> inClause = new HashMap<>();
                    inClause.put("resourceName", "userId");
                    inClause.put("resource", associatedAps);
                    inParams.put("in", inClause);

                    wifiUlDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiUlDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiUllDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiUllDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                }
            }
        }

        outputParams = "Poor,Fair,Good,Very Good,Excellent";
        List<HashMap<String, Object>> uploadSpeedDistList = mongoService.generateHistoricalGraphDataFromDataV2(outputParams, wifiUlDistToday, wifiUlDistLast7Days, wifiUllDistLast30Days, wifiUllDistLast90Days);
        CommonDistDTO commonDistDTO = new CommonDistDTO();
        ArrayList<CommonDistDTO.CommonDistData> commonDistList = new ArrayList<>();
        for (HashMap<String, Object> uploadSpeedData : uploadSpeedDistList) {
            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
            commonDist.setDay(String.valueOf(uploadSpeedData.get("Day")));
            commonDist.setExcellent(Double.valueOf(Objects.isNull(uploadSpeedData.get("Excellent")) ? "0.0" : uploadSpeedData.get("Excellent").toString()));
            commonDist.setFair(Double.valueOf(Objects.isNull(uploadSpeedData.get("Fair")) ? "0.0" : uploadSpeedData.get("Fair").toString()));
            commonDist.setGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Good")) ? "0.0" : uploadSpeedData.get("Good").toString()));
            commonDist.setPoor(Double.valueOf(Objects.isNull(uploadSpeedData.get("Poor")) ? "0.0" : uploadSpeedData.get("Poor").toString()));
            commonDist.setVeryGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Very Good")) ? "0.0" : uploadSpeedData.get("Very Good").toString()));
            commonDistList.add(commonDist);
        }

        if (commonDistList.isEmpty()) {
            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
            commonDistList.add(commonDist);
        }

        commonDistDTO.setData(commonDistList);

        return commonDistDTO;
    }

    public CommonDistDTO getDownloadSpeedDist(String id) throws Exception {
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
        HashMap<String, String> aggregationParams = new HashMap<>();

        DBObject wifiDlDistToday = null;
        DBObject wifiDlDistLast7Days = null;
        DBObject wifiDllDistLast30Days = null;
        DBObject wifiDllDistLast90Days = null;
        String outputParams;

        aggregationParams.clear();
        aggregationParams.put("outputParams", "Poor,Fair,Good,Very Good,Excellent");
        aggregationParams.put("label", "wifiDlSpeedDist");
        aggregationParams.put("operation", "$avg");
        aggregationParams.put("keyToAggregate", "$statsData.v.metricLevels.downloadSpeed.lt50," +
                                                "$statsData.v.metricLevels.downloadSpeed.gte50lt100," +
                                                "$statsData.v.metricLevels.downloadSpeed.gte100lt250," +
                                                "$statsData.v.metricLevels.downloadSpeed.gte250lt500," +
                                                "$statsData.v.metricLevels.downloadSpeed.gte500");

        if (id.equals(String.valueOf(ZERO))) {

            wifiDlDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            wifiDlDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            wifiDllDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            wifiDllDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                HashMap<String, Object> inParams = new HashMap<>();
                HashMap<String, Object> aggParams = new HashMap<>();
                if (manageCommonService.isDefaultCluster(id)) {
                    String ispName = getISPNameByCompId(groupId);
                    aggParams.put("isp", ispName);

                    wifiDlDistToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiDlDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiDllDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiDllDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                } else {
                    Set<String> associatedAps = manageCommonService.getAssociatedApsByClusterId(id);
                    HashMap<String, Object> inClause = new HashMap<>();
                    inClause.put("resourceName", "userId");
                    inClause.put("resource", associatedAps);
                    inParams.put("in", inClause);

                    wifiDlDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiDlDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiDllDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiDllDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                }
            }
        }

        outputParams = "Poor,Fair,Good,Very Good,Excellent";
        List<HashMap<String, Object>> uploadSpeedDistList = mongoService.generateHistoricalGraphDataFromDataV2(outputParams, wifiDlDistToday, wifiDlDistLast7Days, wifiDllDistLast30Days, wifiDllDistLast90Days);
        CommonDistDTO commonDistDTO = new CommonDistDTO();
        ArrayList<CommonDistDTO.CommonDistData> commonDistList = new ArrayList<>();
        for (HashMap<String, Object> uploadSpeedData : uploadSpeedDistList) {
            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
            commonDist.setDay(String.valueOf(uploadSpeedData.get("Day")));
            commonDist.setExcellent(Double.valueOf(Objects.isNull(uploadSpeedData.get("Excellent")) ? "0.0" : uploadSpeedData.get("Excellent").toString()));
            commonDist.setFair(Double.valueOf(Objects.isNull(uploadSpeedData.get("Fair")) ? "0.0" : uploadSpeedData.get("Fair").toString()));
            commonDist.setGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Good")) ? "0.0" : uploadSpeedData.get("Good").toString()));
            commonDist.setPoor(Double.valueOf(Objects.isNull(uploadSpeedData.get("Poor")) ? "0.0" : uploadSpeedData.get("Poor").toString()));
            commonDist.setVeryGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Very Good")) ? "0.0" : uploadSpeedData.get("Very Good").toString()));
            commonDistList.add(commonDist);
        }

        if (commonDistList.isEmpty()) {
            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
            commonDistList.add(commonDist);
        }
        commonDistDTO.setData(commonDistList);

        return commonDistDTO;
    }

    public HealthDistDTO getHealthDist(String id) throws Exception {
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
        HashMap<String, String> aggregationParams = new HashMap<>();
        String outputParams;

        DBObject wifiHealthDistToday = null;
        DBObject wifiHealthDistLast7Days = null;
        DBObject wifiHealthDistLast30Days = null;
        DBObject wifiHealthDistLast90Days = null;

        aggregationParams.clear();
        aggregationParams.put("outputParams", "Lt 60,Lt 70,Lt 80,Lt 90,Gt 90");
        aggregationParams.put("label", "wifiHealthDist");
        aggregationParams.put("operation", "$avg");
        aggregationParams.put("keyToAggregate", "$statsData.v.metricLevels.wifiHealthScore.lt60," +
                                                "$statsData.v.metricLevels.wifiHealthScore.gte60lt70," +
                                                "$statsData.v.metricLevels.wifiHealthScore.gte70lt80," +
                                                "$statsData.v.metricLevels.wifiHealthScore.gte80lt90," +
                                                "$statsData.v.metricLevels.wifiHealthScore.gte90");

        if (id.equals(String.valueOf(ZERO))) {

            wifiHealthDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            wifiHealthDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            wifiHealthDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            wifiHealthDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));

        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                HashMap<String, Object> inParams = new HashMap<>();
                HashMap<String, Object> aggParams = new HashMap<>();
                if (manageCommonService.isDefaultCluster(id)) {
                    String ispName = getISPNameByCompId(groupId);
                    aggParams.put("isp", ispName);

                    wifiHealthDistToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiHealthDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiHealthDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiHealthDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                } else {
                    Set<String> associatedAps = manageCommonService.getAssociatedApsByClusterId(id);
                    HashMap<String, Object> inClause = new HashMap<>();
                    inClause.put("resourceName", "userId");
                    inClause.put("resource", associatedAps);
                    inParams.put("in", inClause);

                    wifiHealthDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiHealthDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiHealthDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiHealthDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                }
            }
        }

        outputParams = "Lt 60,Lt 70,Lt 80,Lt 90,Gt 90";
        HealthDistDTO healthDistDTO = new HealthDistDTO();
        ArrayList<HealthDistDTO.HealthDistData> healthDistDataList = new ArrayList<>();
        List<HashMap<String, Object>> wifiHealthDistList = mongoService.generateHistoricalGraphDataFromDataV2(outputParams, wifiHealthDistToday, wifiHealthDistLast7Days, wifiHealthDistLast30Days, wifiHealthDistLast90Days);
        for (HashMap<String, Object> healthDistData : wifiHealthDistList) {
            HealthDistDTO.HealthDistData healthDist = healthDistDTO.new HealthDistData();
            healthDist.setDay(String.valueOf(healthDistData.get("Day")));
            healthDist.setLt60(Double.valueOf(Objects.isNull(healthDistData.get("Lt 60")) ? "0.0" : healthDistData.get("Lt 60").toString()));
            healthDist.setLt70(Double.valueOf(Objects.isNull(healthDistData.get("Lt 70")) ? "0.0" : healthDistData.get("Lt 70").toString()));
            healthDist.setLt80(Double.valueOf(Objects.isNull(healthDistData.get("Lt 80")) ? "0.0" : healthDistData.get("Lt 80").toString()));
            healthDist.setLt90(Double.valueOf(Objects.isNull(healthDistData.get("Lt 90")) ? "0.0" : healthDistData.get("Lt 90").toString()));
            healthDist.setGt90(Double.valueOf(Objects.isNull(healthDistData.get("Gt 90")) ? "0.0" : healthDistData.get("Gt 90").toString()));
            healthDistDataList.add(healthDist);
        }

        if (healthDistDataList.isEmpty()) {
            HealthDistDTO.HealthDistData healthDist = healthDistDTO.new HealthDistData();
            healthDistDataList.add(healthDist);
        }

        healthDistDTO.setData(healthDistDataList);

        return healthDistDTO;
    }

    public AirTimeUtlzDist getAirTimeUtilizationDist(String id, String type) throws Exception {
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }

        HashMap<String, String> aggregationParams = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        String outputParams;
        String band;

        DBObject airTimeUtilizationDistToday = null;
        DBObject airTimeUtilizationDistLast7Days = null;
        DBObject airTimeUtilizationDistLast30Days = null;
        DBObject airTimeUtilizationDistLast90Days = null;

        band = type.equals(BAND_24G) ? "2g" :
                type.equals(BAND_5G) ? "5g" : "6g";

        aggregationParams.clear();
        aggregationParams.put("outputParams", "Very Low,Low,Medium,High,Very High");
        aggregationParams.put("label", "airTimeUtilizationDist");
        aggregationParams.put("operation", "$avg");
        aggregationParams.put("keyToAggregate", "$statsData.v.metricLevels.perRadioOccupancy." + band + ".lt20," +
                                                "$statsData.v.metricLevels.perRadioOccupancy." + band + ".gte20lt40," +
                                                "$statsData.v.metricLevels.perRadioOccupancy." + band + ".gte40lt60," +
                                                "$statsData.v.metricLevels.perRadioOccupancy." + band + ".gte60lt80," +
                                                "$statsData.v.metricLevels.perRadioOccupancy." + band + ".gte80");

        if (id.equals(String.valueOf(ZERO))) {
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            airTimeUtilizationDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            airTimeUtilizationDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            airTimeUtilizationDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            airTimeUtilizationDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                HashMap<String, Object> inParams = new HashMap<>();
                HashMap<String, Object> aggParams = new HashMap<>();
                if (manageCommonService.isDefaultCluster(id)) {

                    String ispName = getISPNameByCompId(groupId);
                    aggParams.put("isp", ispName);

                    airTimeUtilizationDistToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    airTimeUtilizationDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    airTimeUtilizationDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    airTimeUtilizationDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                } else {

                    Set<String> associatedAps = manageCommonService.getAssociatedApsByClusterId(id);
                    mongoFieldOptions.clear();
                    mongoFieldOptions.put("_id", 0);
                    HashMap<String, Object> inClause = new HashMap<>();
                    inClause.put("resourceName", "userId");
                    inClause.put("resource", associatedAps);
                    inParams.put("in", inClause);

                    airTimeUtilizationDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    airTimeUtilizationDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    airTimeUtilizationDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    airTimeUtilizationDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                }
            }
        }
        outputParams = "Very Low,Low,Medium,High,Very High";
        AirTimeUtlzDist airTimeUtlzDist = new AirTimeUtlzDist();
        ArrayList<AirTimeUtlzDist.AirTimeUtlzDistData> airTimeUtlzDistDataList = new ArrayList<>();
        List<HashMap<String, Object>> airTimeUtlzList = mongoService.generateHistoricalGraphDataFromDataV2(outputParams, airTimeUtilizationDistToday, airTimeUtilizationDistLast7Days, airTimeUtilizationDistLast30Days, airTimeUtilizationDistLast90Days);
        for (HashMap<String, Object> airTimeUtlz : airTimeUtlzList) {
            AirTimeUtlzDist.AirTimeUtlzDistData airTimeUtlzDistData = airTimeUtlzDist.new AirTimeUtlzDistData();
            airTimeUtlzDistData.setDay(String.valueOf(airTimeUtlz.get("Day")));
            airTimeUtlzDistData.setHigh(Double.valueOf(Objects.isNull(airTimeUtlz.get("High")) ? "0.0" : airTimeUtlz.get("High").toString()));
            airTimeUtlzDistData.setVeryHigh(Double.valueOf(Objects.isNull(airTimeUtlz.get("Very High")) ? "0.0" : airTimeUtlz.get("Very High").toString()));
            airTimeUtlzDistData.setLow(Double.valueOf(Objects.isNull(airTimeUtlz.get("Low")) ? "0.0" : airTimeUtlz.get("Low").toString()));
            airTimeUtlzDistData.setVeryLow(Double.valueOf(Objects.isNull(airTimeUtlz.get("Very Low")) ? "0.0" : airTimeUtlz.get("Very Low").toString()));
            airTimeUtlzDistData.setMedium(Double.valueOf(Objects.isNull(airTimeUtlz.get("Medium")) ? "0.0" : airTimeUtlz.get("Medium").toString()));

            airTimeUtlzDistDataList.add(airTimeUtlzDistData);
        }
        if (airTimeUtlzDistDataList.isEmpty()) {
            AirTimeUtlzDist.AirTimeUtlzDistData airTimeUtlzDistData = airTimeUtlzDist.new AirTimeUtlzDistData();
            airTimeUtlzDistDataList.add(airTimeUtlzDistData);
        }

        airTimeUtlzDist.setData(airTimeUtlzDistDataList);
        return airTimeUtlzDist;
    }

    public SignalStrengthDistDTO getSignalStrengthDist(String id) throws Exception {
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
        HashMap<String, String> aggregationParams = new HashMap<>();
        String outputParams;

        DBObject wifiSignalStrengthDistToday = null;
        DBObject wifiSignalStrengthLast7Days = null;
        DBObject wifiSignalStrengthLast30Days = null;
        DBObject wifiSignalStrengthLast90Days = null;

        aggregationParams.clear();
        aggregationParams.put("outputParams", "Poor,Good,Very Good,Excellent");
        aggregationParams.put("label", "wifiSignalStrength");
        aggregationParams.put("operation", "$avg");
        aggregationParams.put("keyToAggregate", "$ltMin80,$gteMin80ltMin60,$gteMin60ltMin40,$gtMin40");
        aggregationParams.put("keyToAggregate", "$statsData.v.metricLevels.wifiSignalStrength.ltMinus80," +
                                                "$statsData.v.metricLevels.wifiSignalStrength.gteMinus80ltMinus60," +
                                                "$statsData.v.metricLevels.wifiSignalStrength.gteMinus60ltMinus40," +
                                                "$statsData.v.metricLevels.wifiSignalStrength.gteMinus40");

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        if (id.equals(String.valueOf(ZERO))) {
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            wifiSignalStrengthDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            wifiSignalStrengthLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            wifiSignalStrengthLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            wifiSignalStrengthLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                HashMap<String, Object> inParams = new HashMap<>();
                HashMap<String, Object> aggParams = new HashMap<>();
                if (manageCommonService.isDefaultCluster(id)) {
                    String ispName = getISPNameByCompId(groupId);
                    aggParams.put("isp", ispName);

                    wifiSignalStrengthDistToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiSignalStrengthLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiSignalStrengthLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiSignalStrengthLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                } else {
                    Set<String> associatedAps = manageCommonService.getAssociatedApsByClusterId(id);
                    mongoFieldOptions.clear();
                    mongoFieldOptions.put("_id", 0);
                    HashMap<String, Object> inClause = new HashMap<>();
                    inClause.put("resourceName", "userId");
                    inClause.put("resource", associatedAps);
                    inParams.put("in", inClause);

                    wifiSignalStrengthDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    wifiSignalStrengthLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    wifiSignalStrengthLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    wifiSignalStrengthLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                }
            }
        }
        outputParams = "Poor,Good,Very Good,Excellent";
        List<HashMap<String, Object>> wifiSignalStrengthList = mongoService.generateHistoricalGraphDataFromDataV2(outputParams, wifiSignalStrengthDistToday, wifiSignalStrengthLast7Days, wifiSignalStrengthLast30Days, wifiSignalStrengthLast90Days);
        SignalStrengthDistDTO signalStrengthDistDTO = new SignalStrengthDistDTO();
        ArrayList<SignalStrengthDistDTO.SignalStrength> signalStrengthsList = new ArrayList<>();
        for (HashMap<String, Object> signalStrength : wifiSignalStrengthList) {
            SignalStrengthDistDTO.SignalStrength signalStrengthData = signalStrengthDistDTO.new SignalStrength();
            signalStrengthData.setDay(String.valueOf(signalStrength.get("Day")));
            signalStrengthData.setExcellent(Double.valueOf(Objects.isNull(signalStrength.get("Excellent")) ? "0.0" : signalStrength.get("Excellent").toString()));
            signalStrengthData.setGood(Double.valueOf(Objects.isNull(signalStrength.get("Good")) ? "0.0" : signalStrength.get("Good").toString()));
            signalStrengthData.setPoor(Double.valueOf(Objects.isNull(signalStrength.get("Poor")) ? "0.0" : signalStrength.get("Poor").toString()));
            signalStrengthData.setVeryGood(Double.valueOf(Objects.isNull(signalStrength.get("Very Good")) ? "0.0" : signalStrength.get("Very Good").toString()));
            signalStrengthsList.add(signalStrengthData);
        }

        if (signalStrengthsList.isEmpty()) {
            SignalStrengthDistDTO.SignalStrength signalStrengthData = signalStrengthDistDTO.new SignalStrength();
            signalStrengthsList.add(signalStrengthData);
        }

        signalStrengthDistDTO.setData(signalStrengthsList);

        return signalStrengthDistDTO;
    }

    public ApiResponseDTO getClumnChartData(String id, String type) throws Exception {
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        HashMap<String, String> aggregationParams = new HashMap<>();

        aggregationParams.clear();
        aggregationParams.put("outputParams", "sumScore,sumUlSpeed,sumDlSpeed,sumStaCount,sumRadioOccupancy24G,sumRadioOccupancy5G,sumRadioOccupancy6G");
        aggregationParams.put("label", "columnChartData");
        aggregationParams.put("operation", "$avg");
        aggregationParams.put("keyToAggregate", "$statsData.v.wifiHealthScore," +
                                                "$statsData.v.avgDataUplinkRate," +
                                                "$statsData.v.avgDataDownlinkRate," +
                                                "$statsData.v.staCount," +
                                                "$statsData.v.radio.2g.occupancy," +
                                                "$statsData.v.radio.5g.occupancy," +
                                                "$statsData.v.radio.6g.occupancy");

        DBObject columnChartDataToday = null;
        DBObject columnChartData7DaysObject = null;
        DBObject columnChartData30DaysObject = null;
        DBObject columnChartData90DaysObject = null;

        if (id.equals(String.valueOf(ZERO))) {
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            columnChartDataToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            columnChartData7DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            columnChartData30DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            columnChartData90DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                HashMap<String, Object> inParams = new HashMap<>();
                HashMap<String, Object> aggParams = new HashMap<>();
                if (manageCommonService.isDefaultCluster(id)) {
                    String ispName = getISPNameByCompId(groupId);
                    aggParams.put("isp", ispName);

                    columnChartDataToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    columnChartData7DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    columnChartData30DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    columnChartData90DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                } else {
                    Set<String> associatedAps = manageCommonService.getAssociatedApsByClusterId(id);
                    mongoFieldOptions.clear();
                    mongoFieldOptions.put("_id", 0);
                    HashMap<String, Object> inClause = new HashMap<>();
                    inClause.put("resourceName", "userId");
                    inClause.put("resource", associatedAps);
                    inParams.put("in", inClause);

                    columnChartDataToday = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    columnChartData7DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    columnChartData30DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    columnChartData90DaysObject = mongoService.aggregateDataForAllOrResourceIdV3(null, inParams, aggregationParams, STATS_PER_USER_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                }
            }
        }

        Map<String, Double> chartDataToday = manageCommonService.convertDBObjectToChartData(columnChartDataToday);
        Map<String, Double> chartDataLast7Days = manageCommonService.convertDBObjectToChartData(columnChartData7DaysObject);
        Map<String, Double> chartDataLast30Days = manageCommonService.convertDBObjectToChartData(columnChartData30DaysObject);
        Map<String, Double> chartDataLast90Days = manageCommonService.convertDBObjectToChartData(columnChartData90DaysObject);

        String outputParams = "";

        if (type.equals(wifiDlSpeed)) {
            DlSpeedDTO dlSpeedDTO = new DlSpeedDTO();
            ArrayList<DlSpeedDTO.DlSpeedData> DlSpeedDataList = new ArrayList<>();
            outputParams = "avgDlSpeed";
            List<HashMap<String, Object>> dlSpeedList = mongoService.generateHistoricalGraphDataFromDataForColumnChartV2(outputParams, chartDataToday.get("wifiDl"), chartDataLast7Days.get("wifiDl"), chartDataLast30Days.get("wifiDl"), chartDataLast90Days.get("wifiDl"));
            for (HashMap<String, Object> dlSpeed : dlSpeedList) {
                DlSpeedDTO.DlSpeedData speedData = dlSpeedDTO.new DlSpeedData();
                speedData.setDay(String.valueOf(dlSpeed.get("Day")));
                speedData.setAvgDlSpeed(Double.valueOf(Objects.isNull(dlSpeed.get("avgDlSpeed")) ? "0.0" : dlSpeed.get("avgDlSpeed").toString()));
                DlSpeedDataList.add(speedData);
            }
            if (DlSpeedDataList.isEmpty()) {
                DlSpeedDTO.DlSpeedData speedData = dlSpeedDTO.new DlSpeedData();
                DlSpeedDataList.add(speedData);
            }
            dlSpeedDTO.setData(DlSpeedDataList);

            return dlSpeedDTO;
        }

        if (type.equals(wifiUlSpeed)) {
            UlSpeedDTO ulSpeedDTO = new UlSpeedDTO();
            ArrayList<UlSpeedDTO.UlSpeedData> ulSpeedDataList = new ArrayList<>();

            outputParams = "avgUlSpeed";
            List<HashMap<String, Object>> ulSpeedList = mongoService.generateHistoricalGraphDataFromDataForColumnChartV2(outputParams, chartDataToday.get("wifiUl"), chartDataLast7Days.get("wifiUl"), chartDataLast30Days.get("wifiUl"), chartDataLast90Days.get("wifiUl"));
            for (HashMap<String, Object> ulSpeed : ulSpeedList) {
                UlSpeedDTO.UlSpeedData ulSpeedData = ulSpeedDTO.new UlSpeedData();
                ulSpeedData.setAvgUlSpeed(Double.valueOf(Objects.isNull(ulSpeed.get("avgUlSpeed")) ? "0.0" : ulSpeed.get("avgUlSpeed").toString()));
                ulSpeedData.setDay(String.valueOf(ulSpeed.get("Day")));
                ulSpeedDataList.add(ulSpeedData);
            }

            if (ulSpeedDataList.isEmpty()) {
                UlSpeedDTO.UlSpeedData ulSpeedData = ulSpeedDTO.new UlSpeedData();
                ulSpeedDataList.add(ulSpeedData);
            }
            ulSpeedDTO.setData(ulSpeedDataList);

            return ulSpeedDTO;
        }

        if (type.equals(wifiHealth)) {
            HealthDTO healthDTO = new HealthDTO();
            ArrayList<HealthDTO.HealthData> healthDataList = new ArrayList<>();
            outputParams = "avgWifiHealth";
            List<HashMap<String, Object>> wifiHealthList = mongoService.generateHistoricalGraphDataFromDataForColumnChartV2(outputParams, chartDataToday.get("wifiHealth"), chartDataLast7Days.get("wifiHealth"), chartDataLast30Days.get("wifiHealth"), chartDataLast90Days.get("wifiHealth"));
            for (HashMap<String, Object> wifiHealth : wifiHealthList) {
                HealthDTO.HealthData healthData = healthDTO.new HealthData();
                healthData.setAvgWifiHealth(Double.valueOf(Objects.isNull(wifiHealth.get("avgWifiHealth")) ? "0.0" : wifiHealth.get("avgWifiHealth").toString()));
                healthData.setDay(String.valueOf(wifiHealth.get("Day")));
                healthDataList.add(healthData);
            }
            if (healthDataList.isEmpty()) {
                HealthDTO.HealthData healthData = healthDTO.new HealthData();
                healthDataList.add(healthData);
            }
            healthDTO.setData(healthDataList);

            return healthDTO;
        }

        if (type.equals(wifiAirTimeUtlz)) {
            AirTimeUtlzDTO airTimeUtlzDTO = new AirTimeUtlzDTO();
            ArrayList<AirTimeUtlzDTO.AirTimeUtlzData> airTimeUtlzDataList = new ArrayList<>();
            outputParams = "avgRadioOccupancy6G,avgRadioOccupancy5G,avgRadioOccupancy24G";
            List<HashMap<String, Object>> airTimeUtlzList = manageCommonService.prepareAirTimeUtzChartDatav2(outputParams, chartDataToday, chartDataLast7Days, chartDataLast30Days, chartDataLast90Days);
            for (HashMap<String, Object> airTimeUtlz : airTimeUtlzList) {
                AirTimeUtlzDTO.AirTimeUtlzData airTimeUtlzData = airTimeUtlzDTO.new AirTimeUtlzData();
                airTimeUtlzData.setAvgRadioOccupancy6G(Double.valueOf(Objects.isNull(airTimeUtlz.get("avgRadioOccupancy6G")) ? "0.0" : airTimeUtlz.get("avgRadioOccupancy6G").toString()));
                airTimeUtlzData.setAvgRadioOccupancy5G(Double.valueOf(Objects.isNull(airTimeUtlz.get("avgRadioOccupancy5G")) ? "0.0" : airTimeUtlz.get("avgRadioOccupancy5G").toString()));
                airTimeUtlzData.setAvgRadioOccupancy24G(Double.valueOf(Objects.isNull(airTimeUtlz.get("avgRadioOccupancy24G")) ? "0.0" : airTimeUtlz.get("avgRadioOccupancy24G").toString()));
                airTimeUtlzData.setDay(String.valueOf(airTimeUtlz.get("Day")));

                airTimeUtlzDataList.add(airTimeUtlzData);
            }
            if (airTimeUtlzDataList.isEmpty()) {
                AirTimeUtlzDTO.AirTimeUtlzData airTimeUtlzData = airTimeUtlzDTO.new AirTimeUtlzData();
                airTimeUtlzDataList.add(airTimeUtlzData);
            }
            airTimeUtlzDTO.setData(airTimeUtlzDataList);

            return airTimeUtlzDTO;
        }
        return new ApiResponseDTO();
    }

    private void convertAggChanells(HashMap<String, String> aggregationParams, String type) {
        aggregationParams.put("keyToAggregate", "$statsData.v.channelDistribution." + type + ".2g.1," +
                "$statsData.v.channelDistribution." + type + ".2," +
                "$statsData.v.channelDistribution." + type + ".2g.3," +
                "$statsData.v.channelDistribution." + type + ".2g.4," +
                "$statsData.v.channelDistribution." + type + ".2g.5," +
                "$statsData.v.channelDistribution." + type + ".2g.6," +
                "$statsData.v.channelDistribution." + type + ".2g.7," +
                "$statsData.v.channelDistribution." + type + ".2g.8," +
                "$statsData.v.channelDistribution." + type + ".2g.9," +
                "$statsData.v.channelDistribution." + type + ".2g.10," +
                "$statsData.v.channelDistribution." + type + ".2g.11," +
                "$statsData.v.channelDistribution." + type + ".5g.36," +
                "$statsData.v.channelDistribution." + type + ".5g.40," +
                "$statsData.v.channelDistribution." + type + ".5g.44," +
                "$statsData.v.channelDistribution." + type + ".5g.48," +
                "$statsData.v.channelDistribution." + type + ".5g.52," +
                "$statsData.v.channelDistribution." + type + ".5g.56," +
                "$statsData.v.channelDistribution." + type + ".5g.60," +
                "$statsData.v.channelDistribution." + type + ".5g.64" +
                "$statsData.v.channelDistribution." + type + ".5g.100," +
                "$statsData.v.channelDistribution." + type + ".5g.104," +
                "$statsData.v.channelDistribution." + type + ".5g.108," +
                "$statsData.v.channelDistribution." + type + ".5g.112," +
                "$statsData.v.channelDistribution." + type + ".5g.116," +
                "$statsData.v.channelDistribution." + type + ".5g.120," +
                "$statsData.v.channelDistribution." + type + ".5g.124," +
                "$statsData.v.channelDistribution." + type + ".5g.128," +
                "$statsData.v.channelDistribution." + type + ".5g.132," +
                "$statsData.v.channelDistribution." + type + ".5g.136," +
                "$statsData.v.channelDistribution." + type + ".5g.140," +
                "$statsData.v.channelDistribution." + type + ".5g.144" +
                "$statsData.v.channelDistribution." + type + ".5g.149," +
                "$statsData.v.channelDistribution." + type + ".5g.153," +
                "$statsData.v.channelDistribution." + type + ".5g.157," +
                "$statsData.v.channelDistribution." + type + ".5g.161," +
                "$statsData.v.channelDistribution." + type + ".5g.165");
    }

    public ChannelDistDTO getChannelSelectionDist(String id) throws Exception {
        if (id.equals("0")) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }
        HashMap<String, String> aggregationParams = new HashMap<>();
        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> aggParams = new HashMap<>();
        String outputParams;

        DBObject rgwChannelDistToday = null;
        DBObject rgwChannelDistLast7Days = null;
        DBObject rgwChannelDistLast30Days = null;
        DBObject rgwChannelDistLast90Days = null;
        DBObject extChannelDistToday = null;
        DBObject extChannelDistLast7Days = null;
        DBObject extChannelDistLast30Days = null;
        DBObject extChannelDistLast90Days = null;

        aggregationParams.clear();
        aggregationParams.put("outputParams", "ch1,ch2,ch3,ch4,ch5,ch6,ch7,ch8,ch9,ch10,ch11," +
                                                "ch36,ch40,ch44,ch48,ch52,ch56,ch60,ch64" +
                                                "ch100,ch104,ch108,ch112,ch116,ch120,ch124,ch128,ch132,ch136,ch140,ch144" +
                                                "ch149,ch153,ch157,ch161,ch165");
        aggregationParams.put("label", "channelSelectionDist");
        aggregationParams.put("operation", "$sum");

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        if (id.equals(String.valueOf(ZERO))) {
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            convertAggChanells(aggregationParams, "RGW");
            rgwChannelDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            rgwChannelDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            rgwChannelDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            rgwChannelDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));

            convertAggChanells(aggregationParams, "EXT");
            extChannelDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
            extChannelDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
            extChannelDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
            extChannelDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));

        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                if (manageCommonService.isDefaultCluster(id)) {
                    String ispName = getISPNameByCompId(groupId);

                    aggParams.clear();
                    aggParams.put("isp", ispName);
                    convertAggChanells(aggregationParams, "RGW");
                    rgwChannelDistToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    rgwChannelDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    rgwChannelDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    rgwChannelDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));

                    aggParams.clear();
                    aggParams.put("isp", ispName);
                    convertAggChanells(aggregationParams, "EXT");
                    extChannelDistToday = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    extChannelDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    extChannelDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    extChannelDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(aggParams, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                } else {
                    convertAggChanells(aggregationParams, "RGW");
                    rgwChannelDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    rgwChannelDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    rgwChannelDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    rgwChannelDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));

                    convertAggChanells(aggregationParams, "EXT");
                    extChannelDistToday = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_HOUR, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
                    extChannelDistLast7Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
                    extChannelDistLast30Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
                    extChannelDistLast90Days = mongoService.aggregateDataForAllOrResourceIdV3(null, null, aggregationParams, STATS_PER_ISP_PER_DAY, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));
                }
            }
        }

        ChannelDistDTO channelDistDTO = new ChannelDistDTO();
        ArrayList<ChannelDistDTO.ChannelData> channelDataList = new ArrayList<>();

        outputParams = "ch1,ch2,ch3,ch4,ch5,ch6,ch7,ch8,ch9,ch10,ch11," +
                        "ch36,ch40,ch44,ch48,ch52,ch56,ch60,ch64" +
                        "ch100,ch104,ch108,ch112,ch116,ch120,ch124,ch128,ch132,ch136,ch140,ch144" +
                        "ch149,ch153,ch157,ch161,ch165";
        List<HashMap<String, Object>> rgwChannelList = mongoService.generateHistoricalGraphDataFromDataV3(outputParams, "RG", rgwChannelDistToday, rgwChannelDistLast7Days, rgwChannelDistLast30Days, rgwChannelDistLast90Days);
        List<HashMap<String, Object>> extChannelList = mongoService.generateHistoricalGraphDataFromDataV3(outputParams, "EXT", extChannelDistToday, extChannelDistLast7Days, extChannelDistLast30Days, extChannelDistLast90Days);

        List<HashMap<String, Object>> allChannelList = new ArrayList<>();
        allChannelList.addAll(rgwChannelList);
        allChannelList.addAll(extChannelList);

        for (HashMap<String, Object> channel : allChannelList) {
            ChannelDistDTO.ChannelData channelData = channelDistDTO.new ChannelData();
            channelData.setCh1(Long.valueOf(Objects.isNull(channel.get("ch1")) ? "0" : channel.get("ch1").toString()));
            channelData.setCh2(Long.valueOf(Objects.isNull(channel.get("ch2")) ? "0" : channel.get("ch2").toString()));
            channelData.setCh3(Long.valueOf(Objects.isNull(channel.get("ch3")) ? "0" : channel.get("ch3").toString()));
            channelData.setCh4(Long.valueOf(Objects.isNull(channel.get("ch4")) ? "0" : channel.get("ch4").toString()));
            channelData.setCh5(Long.valueOf(Objects.isNull(channel.get("ch5")) ? "0" : channel.get("ch5").toString()));
            channelData.setCh6(Long.valueOf(Objects.isNull(channel.get("ch6")) ? "0" : channel.get("ch6").toString()));
            channelData.setCh7(Long.valueOf(Objects.isNull(channel.get("ch7")) ? "0" : channel.get("ch7").toString()));
            channelData.setCh8(Long.valueOf(Objects.isNull(channel.get("ch8")) ? "0" : channel.get("ch8").toString()));
            channelData.setCh9(Long.valueOf(Objects.isNull(channel.get("ch9")) ? "0" : channel.get("ch9").toString()));
            channelData.setCh10(Long.valueOf(Objects.isNull(channel.get("ch10")) ? "0" : channel.get("ch10").toString()));
            channelData.setCh11(Long.valueOf(Objects.isNull(channel.get("ch11")) ? "0" : channel.get("ch11").toString()));

            channelData.setCh36(Long.valueOf(Objects.isNull(channel.get("ch36")) ? "0" : channel.get("ch36").toString()));
            channelData.setCh40(Long.valueOf(Objects.isNull(channel.get("ch40")) ? "0" : channel.get("ch40").toString()));
            channelData.setCh44(Long.valueOf(Objects.isNull(channel.get("ch44")) ? "0" : channel.get("ch44").toString()));
            channelData.setCh48(Long.valueOf(Objects.isNull(channel.get("ch48")) ? "0" : channel.get("ch48").toString()));

            channelData.setCh52(Long.valueOf(Objects.isNull(channel.get("ch52")) ? "0" : channel.get("ch52").toString()));
            channelData.setCh56(Long.valueOf(Objects.isNull(channel.get("ch56")) ? "0" : channel.get("ch56").toString()));
            channelData.setCh60(Long.valueOf(Objects.isNull(channel.get("ch60")) ? "0" : channel.get("ch60").toString()));
            channelData.setCh64(Long.valueOf(Objects.isNull(channel.get("ch64")) ? "0" : channel.get("ch64").toString()));

            channelData.setCh100(Long.valueOf(Objects.isNull(channel.get("ch100")) ? "0" : channel.get("ch100").toString()));
            channelData.setCh104(Long.valueOf(Objects.isNull(channel.get("ch104")) ? "0" : channel.get("ch104").toString()));
            channelData.setCh108(Long.valueOf(Objects.isNull(channel.get("ch108")) ? "0" : channel.get("ch108").toString()));
            channelData.setCh112(Long.valueOf(Objects.isNull(channel.get("ch112")) ? "0" : channel.get("ch112").toString()));
            channelData.setCh116(Long.valueOf(Objects.isNull(channel.get("ch116")) ? "0" : channel.get("ch116").toString()));
            channelData.setCh120(Long.valueOf(Objects.isNull(channel.get("ch120")) ? "0" : channel.get("ch120").toString()));
            channelData.setCh124(Long.valueOf(Objects.isNull(channel.get("ch124")) ? "0" : channel.get("ch124").toString()));
            channelData.setCh128(Long.valueOf(Objects.isNull(channel.get("ch128")) ? "0" : channel.get("ch128").toString()));
            channelData.setCh132(Long.valueOf(Objects.isNull(channel.get("ch132")) ? "0" : channel.get("ch132").toString()));
            channelData.setCh136(Long.valueOf(Objects.isNull(channel.get("ch136")) ? "0" : channel.get("ch136").toString()));
            channelData.setCh140(Long.valueOf(Objects.isNull(channel.get("ch140")) ? "0" : channel.get("ch140").toString()));
            channelData.setCh144(Long.valueOf(Objects.isNull(channel.get("ch144")) ? "0" : channel.get("ch144").toString()));

            channelData.setCh149(Long.valueOf(Objects.isNull(channel.get("ch149")) ? "0" : channel.get("ch149").toString()));
            channelData.setCh153(Long.valueOf(Objects.isNull(channel.get("ch153")) ? "0" : channel.get("ch153").toString()));
            channelData.setCh157(Long.valueOf(Objects.isNull(channel.get("ch157")) ? "0" : channel.get("ch157").toString()));
            channelData.setCh161(Long.valueOf(Objects.isNull(channel.get("ch161")) ? "0" : channel.get("ch161").toString()));
            channelData.setCh165(Long.valueOf(Objects.isNull(channel.get("ch165")) ? "0" : channel.get("ch165").toString()));
            channelData.setEquipment(String.valueOf(channel.get("EquipmentType")));
            channelData.setDay(String.valueOf(channel.get("Day")));

            channelDataList.add(channelData);
        }

        if(!channelDataList.isEmpty()) {
            channelDistDTO.setData(channelDataList);
        }

        return channelDistDTO;
    }

    public static List<BasicDBObject> getAggregationPipeline(String ispName, HashMap<String, Object> timeParams) {

        BasicDBObject matchCondition = new BasicDBObject("statsDateTime", new BasicDBObject("$in", timeParams.get("dates")));
        if (ispName != null) {
            matchCondition.append("isp", ispName);
        }

        ZonedDateTime startTimestamp = (ZonedDateTime) timeParams.get("ldtTime");
        BasicDBObject timeMatchCriteria = new BasicDBObject("statsData.v.recordDateTime", new BasicDBObject("$gte", Date.from(startTimestamp.toInstant())));

        return Arrays.asList(
                new BasicDBObject("$match", matchCondition),

                new BasicDBObject("$project", new BasicDBObject("statsData",
                        new BasicDBObject("$objectToArray", "$statsData"))),

                new BasicDBObject("$unwind", "$statsData"),

                new BasicDBObject("$match", timeMatchCriteria),

                new BasicDBObject("$project", new BasicDBObject("channelDistribution", "$statsData.v.channelDistribution")),

                new BasicDBObject("$project", new BasicDBObject("RGW",
                        new BasicDBObject("$objectToArray", "$channelDistribution.RGW"))
                        .append("EXT", new BasicDBObject("$objectToArray", "$channelDistribution.EXT"))),

                new BasicDBObject("$unwind", "$RGW"),
                new BasicDBObject("$unwind", "$EXT"),

                new BasicDBObject("$project", new BasicDBObject("band_RGW", "$RGW.k")
                        .append("channels_RGW", new BasicDBObject("$objectToArray", "$RGW.v"))
                        .append("band_EXT", "$EXT.k")
                        .append("channels_EXT", new BasicDBObject("$objectToArray", "$EXT.v"))),

                new BasicDBObject("$unwind", "$channels_RGW"),
                new BasicDBObject("$unwind", "$channels_EXT"),

                new BasicDBObject("$group", new BasicDBObject("_id", new BasicDBObject("band_RGW", "$band_RGW")
                        .append("channel_RGW", "$channels_RGW.k")
                        .append("band_EXT", "$band_EXT")
                        .append("channel_EXT", "$channels_EXT.k"))
                        .append("RGW_total", new BasicDBObject("$sum", "$channels_RGW.v"))
                        .append("EXT_total", new BasicDBObject("$sum", "$channels_EXT.v"))),

                new BasicDBObject("$group", new BasicDBObject("_id", "$_id.band_RGW")
                        .append("RGW_channels", new BasicDBObject("$push",
                                new BasicDBObject("k", "$_id.channel_RGW").append("v", "$RGW_total")))
                        .append("EXT_channels", new BasicDBObject("$push",
                                new BasicDBObject("k", "$_id.channel_EXT").append("v", "$EXT_total")))),

                new BasicDBObject("$project", new BasicDBObject("band", "$_id")
                        .append("_id", 0)
                        .append("RGW_channels", new BasicDBObject("$arrayToObject", "$RGW_channels"))
                        .append("EXT_channels", new BasicDBObject("$arrayToObject", "$EXT_channels"))),

                new BasicDBObject("$group", new BasicDBObject("_id", null)
                        .append("RGW", new BasicDBObject("$push",
                                new BasicDBObject("k", "$band").append("v", "$RGW_channels")))
                        .append("EXT", new BasicDBObject("$push",
                                new BasicDBObject("k", "$band").append("v", "$EXT_channels")))),

                new BasicDBObject("$project", new BasicDBObject("_id", 0)
                        .append("RGW", new BasicDBObject("$arrayToObject", "$RGW"))
                        .append("EXT", new BasicDBObject("$arrayToObject", "$EXT")))
        );
    }

    private DBObject getAggregateChannelDist(String collection, String ispName, HashMap<String, Object> timeParams) {

        List<BasicDBObject> pipeline = getAggregationPipeline(ispName, timeParams);
        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(pipeline);

        return Optional.ofNullable(aggregationResult.results())
                .filter(results -> results.iterator().hasNext())
                .map(results -> {
                    DBObject lastResult = null;
                    for (DBObject obj : results) {
                        lastResult = obj;
                    }
                    return lastResult;
                })
                .orElse(new BasicDBObject());
    }

    private static List<Integer> generateChannels(int start, int end, int step) {
        List<Integer> channels = new ArrayList<>();
        for (int i = start; i <= end; i += step) {
            channels.add(i);
        }
        return channels;
    }

    public static List<Map<String, Object>> transformAggregateResults(DBObject aggregateResult, String day) {
        List<Map<String, Object>> transformedResults = new ArrayList<>();

        Map<String, String> radioMapping = new HashMap<>();
        radioMapping.put("2g", "2.4G");
        radioMapping.put("5g", "5G");
        radioMapping.put("6g", "6G");

        Set<Integer> channels2G = new LinkedHashSet<>(generateChannels(1, 11, 1));
        Set<Integer> channels5G = new LinkedHashSet<>(generateChannels(36, 165, 4));
        Set<Integer> channels6G = new LinkedHashSet<>(generateChannels(1, 233, 4));

        Map<String, Set<Integer>> channelRange = new HashMap<>();
        channelRange.put("2g", channels2G);
        channelRange.put("5g", channels5G);
        channelRange.put("6g", channels6G);

        List<String> equipmentTypes = Arrays.asList("RGW", "EXT");

        for (String equipment : equipmentTypes) {
            DBObject equipmentData = (DBObject) (aggregateResult != null ? aggregateResult.get(equipment) : null);

            for (String band : radioMapping.keySet()) {
                DBObject bandData = (equipmentData != null) ? (DBObject) equipmentData.get(band) : null;

                Map<String, Object> transformedData = new HashMap<>();
                Map<String, Integer> channelData = new LinkedHashMap<>();

                for (Integer ch : channelRange.get(band)) {
                    channelData.put("ch" + ch, 0);
                }

                if (bandData != null) {
                    for (String channelStr : bandData.keySet()) {
                        try {
                            int channel = Integer.parseInt(channelStr);
                            if (channelRange.get(band).contains(channel)) {
                                int count = ((Number) bandData.get(channelStr)).intValue();
                                channelData.put("ch" + channel, count);
                            }
                        } catch (NumberFormatException e) {
                            logger.warn("Wrong channel: " + channelStr);
                        }
                    }
                }

                transformedData.put("channel", channelData);
                transformedData.put("equipment", equipment.equals("RGW") ? "RG" : "EXT");
                transformedData.put("radio", radioMapping.get(band));
                transformedData.put("day", day);

                transformedResults.add(transformedData);
            }
        }

        return transformedResults;
    }

    public List<Map<String, Object>> getChannelSelectionDistV2(String id) throws Exception {
        String ispName = null;

        if (id.equals(String.valueOf(ZERO))) {
            if (!CommonUtils.isSysAdmin()) {
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        } else {
            String groupId = manageCommonService.getGroupIdFromClusterId(id);
            if (Objects.nonNull(groupId)) {
                if (manageCommonService.isDefaultCluster(id)) {
                    ispName = getISPNameByCompId(groupId);
                }
            }
        }

        DBObject ChannelDistToday = getAggregateChannelDist(STATS_PER_ISP_PER_HOUR, ispName, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_DAY, DATE));
        DBObject ChannelDistLast7Days = getAggregateChannelDist(STATS_PER_ISP_PER_DAY, ispName, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_7_DAYS, MONTH));
        DBObject ChannelDistLast30Days = getAggregateChannelDist(STATS_PER_ISP_PER_DAY, ispName, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_30_DAYS, MONTH));
        DBObject ChannelDistLast90Days = getAggregateChannelDist(STATS_PER_ISP_PER_DAY, ispName, CalendarUtils.getDaysForCriteria(LOCALDATETIME_CRITERIA_LAST_90_DAYS, MONTH));

        List<Map<String, Object>> transformedToDay = transformAggregateResults(ChannelDistToday, "Last 24 Hours");
        List<Map<String, Object>> transformedTo7Days = transformAggregateResults(ChannelDistLast7Days, "Last 7 days");
        List<Map<String, Object>> transformedTo30Days = transformAggregateResults(ChannelDistLast30Days, "Last 30 days");
        List<Map<String, Object>> transformedTo90Days = transformAggregateResults(ChannelDistLast90Days, "Last 90 days");

        List<Map<String, Object>> allChannelList = new ArrayList<>();
        allChannelList.addAll(transformedToDay);
        allChannelList.addAll(transformedTo7Days);
        allChannelList.addAll(transformedTo30Days);
        allChannelList.addAll(transformedTo90Days);

        return allChannelList;
    }
}
