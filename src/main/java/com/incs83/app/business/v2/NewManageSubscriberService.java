package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.common.v2.RGWPaginationRequest;
import com.incs83.app.common.v2.SubscriberSearchDTO;
import com.incs83.app.common.v2.SubscriberSearchESDTO;
import com.incs83.app.common.v3.DetachedEquipmentDTO;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.queries.EquipmentSQL;
import com.incs83.app.constants.queries.GroupISPSQL;
import com.incs83.app.constants.queries.SubscriberSQL;
import com.incs83.app.entities.*;
import com.incs83.app.responsedto.v2.Equipment.DetachedEquipmentListDTO;
import com.incs83.app.responsedto.v2.Equipment.EditSubscriberRequest;
import com.incs83.app.responsedto.v2.Equipment.RGWDisconnectedDTO;
import com.incs83.app.responsedto.v2.Subscriber.CreateSubscriberRequestDTO;
import com.incs83.app.responsedto.v2.UsersDTO.SubscriberListDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.app.responsedto.v2.serviceStats.ResourceSpecification.DeviceDetailInfoDTO;
import com.incs83.app.service.components.HttpServiceImpl;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.business.ESService;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.context.ExecutionContext;
import com.incs83.dto.ESRequest;
import com.incs83.dto.ElasticSearchDTO;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.ConflictException;
import com.incs83.exceptions.handler.UrlNotFoundException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.pubsub.MQTTService;
import com.incs83.service.CommonService;
import com.incs83.service.S3Service;
import com.incs83.util.CommonUtils;
import com.incs83.util.DateUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.PersistenceException;
import java.util.*;

import static com.incs83.Constants.ESConstants.EXT_MAC;
import static com.incs83.Constants.ESConstants.EXT_SERIAL;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.BULK_UPLOAD_SUB_DIR;
import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.SPACE;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.queries.EquipmentSQL.*;
import static com.incs83.app.constants.queries.LoginDetailsSQLImpl.DELETE_LOGIN_DETAILS_BY_EMAIL;
import static com.incs83.constants.ApplicationCommonConstants.DEFAULT;
import static com.incs83.constants.ApplicationCommonConstants.END_USER_ROLE_ID;
import static com.incs83.constants.ApplicationCommonConstants.GATEWAY;
import static com.incs83.constants.ApplicationCommonConstants.*;

@Service
public class NewManageSubscriberService {
    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private MongoServiceImpl mongoServiceImpl;
    @Autowired
    private ESService esService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ISPService ispService;
    @Autowired
    private NewManageEquipmentService manageEquipmentService;
    @Autowired
    private MQTTService mqttService;
    @Autowired
    private HttpServiceImpl httpService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private S3Service s3Service;

    @Value("${elastic-search.enable}")
    Boolean elasticSearchEnable;

    public Subscriber createSubscriber(CreateSubscriberRequestDTO createSubscriberRequest) throws Exception {
//        Set<Equipment> equipmentSet = new HashSet<>();
        String ispId = null;
        String ispName = null;

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("name", createSubscriberRequest.getIsp());

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        DBObject dbObject = mongoServiceImpl.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
        if (Objects.isNull(dbObject))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP doesn't exist");

        Set<Compartment> compartmentSet = new HashSet<>();
        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", String.valueOf(dbObject.get("id")));   // for development
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);
        if (Objects.nonNull(compartmentList) && !compartmentList.isEmpty())
            compartmentSet.add(compartmentList.get(0));
        if (compartmentSet.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group does not exists for the ISP : " + ispName);


//        String[] isp = ispName.split(HYPHEN_STRING);

        /*if (Objects.nonNull(isp[0]) && !EMPTY_STRING.equals(isp[0].trim()) && !WINDSTREAM.equalsIgnoreCase(isp[0])) {
            if ((Objects.nonNull(createSubscriberRequest.getGlobalAccountNo()) && !EMPTY_STRING.equals(createSubscriberRequest.getGlobalAccountNo().trim())) || (Objects.nonNull(createSubscriberRequest.getCamsAccountNo())) && !EMPTY_STRING.equals(createSubscriberRequest.getCamsAccountNo().trim())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Global AccountNo and Cams Account is not valid for the subscriber");
            }
        }*/
        /*try {
            for (String apId : createSubscriberRequest.getApId()) {
                HashMap<String, Object> param = new HashMap<>();
                param.put("id", apId);
               Equipment equipment = (Equipment) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_SERIAL_NUMBER, param).iterator().next();
                if (Objects.isNull(equipment))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment doesn't exist for AP Id :: " + apId);

                equipmentSet.add(equipment);
            }
        } catch (Exception e) {
            LOG.error("Equipment doesn't exist for AP Id.");
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment doesn't exist for AP Id");
        }*/

        Subscriber subscriber = generateSubscriberInstance(createSubscriberRequest, compartmentSet);

        if (Objects.nonNull(subscriber.getEmail()) && !subscriber.getEmail().isEmpty()) {

//            HashMap<String, String> queryParams = new HashMap<>();
//            queryParams.put("gan", editSubscriberRequest.getGlobalAccountNo());
//            List<Object> count = (List<Object>) dataAccessService.readNative(queries, queryParams);

//            String query = SubscriberSQL.COUNT_DUPLICATE_SUBCRIBER_BY_EMAIL;
//            HashMap<String, String> qParams = new HashMap<>();
//            qParams.put("email", subscriber.getEmail());
//            List<Object> count = (List<Object>) dataAccessService.readNative(query, qParams);
//            if (Integer.valueOf(count.get(0).toString()) > 0) {
//                throw new ConflictException(HttpStatus.CONFLICT.value(), " Email already exists.  Cannot create subscriber.");
//            }
        }

        if (Objects.nonNull(subscriber.getGlobalAccountNo()) && !subscriber.getGlobalAccountNo().isEmpty()) {
            String query = SubscriberSQL.COUNT_DUPLICATE_SUBCRIBER_BY_GAN;
            HashMap<String, String> qParams = new HashMap<>();
            qParams.put("gan", subscriber.getGlobalAccountNo());
            List<Object> count = (List<Object>) dataAccessService.readNative(query, qParams);
            if (Integer.valueOf(count.get(0).toString()) > 0) {
                throw new ConflictException(HttpStatus.CONFLICT.value(), "Global Account Number already exists. Cannot create subscriber");
            }
        }

        subscriber.setActive(true);

        try {
            if (dataAccessService.create(Subscriber.class, subscriber)) {
                /*for (Equipment equipment : equipmentSet) {
                    BasicDBObject projection = new BasicDBObject();
                    projection.put("_id", 0);
                    HashMap<String, Object> queryParams = new HashMap<>();
                    queryParams.put("serialNumber", equipment.getRgwSerial());
                    queryParams.put("userId", equipment.getRgwSerial());
                    List<DBObject> subscriberList = mongoServiceImpl.findList(queryParams, AP_DETAIL, projection);

                    queryParams.clear();
                    queryParams.put("subscriberId", subscriber.getId());
                    queryParams.put("equipmentId", equipment.getId());

                    dataAccessService.update(Equipment.class, UPDATE_SUBSCRIBER_ID_FOR_EQUIPMENT, queryParams);

                    BasicDBObject record;
                    DBObject update;
                    DBObject query;

                    if (elasticSearchEnable) {
                        try {
                            HashMap<String, Object> equipmentMap = prepareDataForES2(equipment, subscriber);
                            esService.updateById(EQUIPMENT_INDEX, equipmentMap, String.valueOf(equipmentMap.get("id")), null);
                            record = new BasicDBObject();
                            record.put("esProcessed", true);
                            update = new BasicDBObject("$set", record);
                            query = new BasicDBObject("userId", equipment.getRgwSerial());
                            mongoServiceImpl.update(query, update, true, false, ApplicationCommonConstants.AP_DETAIL);
                        } catch (Exception e) {
                            LOG.error("Error in updating esProcessed true. AP ID ::" + equipment.getRgwSerial());
                        }
                    }
                }*/

                if (elasticSearchEnable) {
                    try {
                        HashMap<String, Object> subscriberMap = prepareDataForESSubscriber(subscriber);
                        esService.insert(SUBSCRIBER_INDEX, subscriberMap, String.valueOf(subscriberMap.get("id")), null);
                    } catch (Exception e) {
                        LOG.error("Error in inserting subscriber in ES");
                    }
                }
                String query = SubscriberSQL.COUNT_DUPLICATE_LOGIN_DETAIL_BY_EMAIL;
                HashMap<String, String> qParams = new HashMap<>();
                qParams.put("email", subscriber.getEmail());
                List<Object> count = (List<Object>) dataAccessService.readNative(query, qParams);
                if (Integer.valueOf(count.get(0).toString()) == 0) {
                    LoginDetails loginDetails = new LoginDetails();
                    loginDetails.setId(subscriber.getId());
                    loginDetails.setEmail(subscriber.getEmail());
                    loginDetails.setSubscriber(true);
                    loginDetails.setActive(true);
                    loginDetails.setToken(null);
                    loginDetails.setTokenCreatedAt(null);
                    loginDetails.setTokenUpdatedAt(null);
                    loginDetails.setTokenType(SUBSCRIBER);
                    CommonUtils.setCreateEntityFields(loginDetails);
                    dataAccessService.create(LoginDetails.class, loginDetails);
                }
            }
        } catch (PersistenceException e) {
            LOG.error("SQL Constraint Violation. RGW :: ");
            String message = e.getCause().getCause().getMessage();
            if (message.contains("UK_ewxd6mo4yfd3pmkcfoaec1sun"))
                throw new ConflictException(HttpStatus.CONFLICT.value(), "Duplicate entry for email");
            else
                throw new ConflictException(HttpStatus.CONFLICT.value(), "Duplicate entry for global account no");

        } catch (ConflictException e) {
            throw new ConflictException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LOG.error("Error in creating subscriber (insertToMysqlAndElastic) :: " + e.getMessage());
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in creating subscriber");
        }

        return subscriber;
    }

    private HashMap<String, Object> prepareDataForESSubscriber(Subscriber subscriber) {
        HashMap<String, Object> subscriberMap = new HashMap<>();
        subscriberMap.put("id", subscriber.getId());
        subscriberMap.put("camsAccountNo", subscriber.getCamsAccountNo());
        subscriberMap.put("globalAccountNo", subscriber.getGlobalAccountNo());
        subscriberMap.put("phoneNo", subscriber.getPhoneNo());
        subscriberMap.put("createdAt", subscriber.getCreatedAt());
        subscriberMap.put("createdBy", subscriber.getCreatedBy());
        subscriberMap.put("email", subscriber.getEmail());
        subscriberMap.put("name", subscriber.getFirstName() + " " + subscriber.getLastName());
        subscriberMap.put("groupId", subscriber.getCompartment().iterator().next().getId());
        subscriberMap.put("groupName", subscriber.getCompartment().iterator().next().getName());
        subscriberMap.put("imageUrl", subscriber.getImageUrl());
        subscriberMap.put("isActive", subscriber.isActive());
        return subscriberMap;
    }

    private Subscriber generateSubscriberInstance(CreateSubscriberRequestDTO createSubscriberRequest, Set<Compartment> compartmentSet) throws Exception {
        Subscriber subscriber = new Subscriber();
        subscriber.setId(CommonUtils.generateUUID());
        if (Objects.nonNull(createSubscriberRequest.getFirstName()) && !EMPTY_STRING.equals(createSubscriberRequest.getFirstName())) {
            subscriber.setFirstName(createSubscriberRequest.getFirstName());
        }

        if (Objects.nonNull(createSubscriberRequest.getLastName()) && !EMPTY_STRING.equals(createSubscriberRequest.getLastName())) {
            subscriber.setLastName(createSubscriberRequest.getLastName());
        }

        if (Objects.nonNull(createSubscriberRequest.getEmail())) {
            subscriber.setEmail(createSubscriberRequest.getEmail());
        } else {
            subscriber.setEmail(createSubscriberRequest.getGlobalAccountNo() + "@optim.com");
        }

        if (Objects.nonNull(createSubscriberRequest.getGlobalAccountNo()) && !createSubscriberRequest.getGlobalAccountNo().trim().isEmpty())
            subscriber.setGlobalAccountNo(createSubscriberRequest.getGlobalAccountNo());
        if (Objects.nonNull(createSubscriberRequest.getCamsAccountNo()) && !createSubscriberRequest.getCamsAccountNo().trim().isEmpty())
            subscriber.setCamsAccountNo(createSubscriberRequest.getCamsAccountNo());
        if (Objects.nonNull(createSubscriberRequest.getPhoneNumber()) && !createSubscriberRequest.getPhoneNumber().isEmpty())
            subscriber.setPhoneNo(createSubscriberRequest.getPhoneNumber());

        subscriber.setCompartment(compartmentSet);
        subscriber.setSubscriberRole(getEndSubscriberRole());
        subscriber.setPassword(USER_PASSWORD);
        CommonUtils.setCreateEntityFields(subscriber);
        return subscriber;
    }

    public Set<SubscriberRole> getEndSubscriberRole() throws Exception {
        Role role = (Role) dataAccessService.read(Role.class, END_USER_ROLE_ID);
        SubscriberRole subscriberRole = new SubscriberRole();
        subscriberRole.setId(CommonUtils.generateUUID());
        subscriberRole.setRole(role);
        subscriberRole.setGrantBy(UserRole.RoleType.DIRECT.name());
        CommonUtils.setCreateEntityFields(subscriberRole);

        Set<SubscriberRole> subscriberRoleMapping = new HashSet<>();
        subscriberRoleMapping.add(subscriberRole);

        return subscriberRoleMapping;
    }

    private HashMap<String, Object> prepareDataForES2(Equipment equipment, Subscriber subscriber) {
        List<String> clusters = new ArrayList<>();
        HashMap<String, Object> userAPMap = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());
            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster() && cluster.getName().equals(DEFAULT + compartment.getName())).findAny().orElse(null);
            if (Objects.nonNull(clusterInfo)) {
                clusters.add(clusterInfo.getId());
            }

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", equipment.getRgwSerial());
            HashMap<String, Object> projection = new HashMap<>();
            projection.put("type", 1);
            projection.put("serialNumber", 1);
            projection.put("macAddress", 1);
            List<HashMap<String, String>> apDetails = mongoServiceImpl.findList(queryParams, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, projection);
            List<Map<String, Object>> extInfo = new ArrayList<>();
            apDetails.stream().filter(ap -> !GATEWAY.equals(ap.get("type"))).forEach(elem -> {
                Map<String, Object> extender = new HashMap<>();
                extender.put(EXT_MAC, elem.get("macAddress"));
                extender.put(EXT_SERIAL, elem.get("serialNumber"));
                extInfo.add(extender);
            });

            userAPMap = new HashMap<>();
            userAPMap.put("id", equipment.getId());
            userAPMap.put("name", Objects.nonNull(subscriber) ? subscriber.getFirstName() + " " + subscriber.getLastName() : equipment.getRgwSerial() + " " + equipment.getRgwMAC());
            userAPMap.put("createdAt", equipment.getCreatedAt());
            userAPMap.put("createdBy", equipment.getCreatedBy());
            userAPMap.put("email", subscriber.getEmail());
            userAPMap.put("subscriberId", subscriber.getId());
            userAPMap.put("mobileSubscribed", false);
            userAPMap.put("downLinkRate", equipment.getDownLinkRate());
            userAPMap.put("upLinkRate", equipment.getUpLinkRate());
            userAPMap.put("rgwMAC", equipment.getRgwMAC());
            userAPMap.put("apId", equipment.getRgwSerial());
            userAPMap.put("serialNumber", equipment.getRgwSerial());
            userAPMap.put("groupId", equipment.getGroupId());
            userAPMap.put("phoneNo", subscriber.getPhoneNo());
            userAPMap.put("globalAccountNo", subscriber.getGlobalAccountNo());
            userAPMap.put("camsAccountNo", subscriber.getCamsAccountNo());
            userAPMap.put("extInfo", extInfo);
            userAPMap.put("clusters", clusters);
            userAPMap.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
        } catch (Exception e) {
            LOG.error("ERROR in prepare data for ES.");
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }
        return userAPMap;
    }

    public Object getAllSubscribers(SubscriberSearchDTO subscriberSearchDTO, Boolean exact) throws Exception {
        List<Object[]> users = new ArrayList<>();
        String queries = EMPTY_STRING;
        HashMap<String, Object> queryParam = new HashMap<>();
        ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberModelArrayList = new ArrayList<>();
        SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
        SubscriberListDTO.SubscriberData subscriberData = subscriberListDTO.new SubscriberData();

        if (isSearchEnabled(subscriberSearchDTO)) {
            SortOrder sortOrder = subscriberSearchDTO.getOrder().equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
            ESRequest elasticSearchRequest = new ESRequest();
            elasticSearchRequest.setMax(subscriberSearchDTO.getMax()).setOffset(subscriberSearchDTO.getOffset()).setSortBy(subscriberSearchDTO.getSortBy()).setOrder(sortOrder).setIndex(SUBSCRIBER_INDEX);

            HashMap<String, Object> esQueryParams = new HashMap<>();
            esQueryParams.put("name", subscriberSearchDTO.getName());
            esQueryParams.put("email", subscriberSearchDTO.getEmail());
            esQueryParams.put("globalAccountNo", subscriberSearchDTO.getGlobalAccountNo());
            esQueryParams.put("camsAccountNo", subscriberSearchDTO.getCamsAccountNo());
            esQueryParams.put("groupName", subscriberSearchDTO.getGroupName());
            esQueryParams.put("phoneNo", subscriberSearchDTO.getPhoneNo());
            esQueryParams.put("groupId", subscriberSearchDTO.getGroupId());

            elasticSearchRequest.setQueryParams(esQueryParams);
            ElasticSearchDTO response = esService.searchWithFilter(elasticSearchRequest, null, null, exact);
            if (Objects.nonNull(response)) {
                if (response.getObjects().size() > 0) {
                    response.getObjects().forEach(u -> {
                        SubscriberSearchESDTO subscriberESDTO = objectMapper.convertValue(u, SubscriberSearchESDTO.class);
                        SubscriberListDTO.SubscriberData.SubscriberModel subscriberModel = subscriberData.new SubscriberModel();
                        subscriberModel.setEmail(subscriberESDTO.getEmail());
                        subscriberModel.setName(subscriberESDTO.getName());
                        subscriberModel.setCamsAccountNo(subscriberESDTO.getCamsAccountNo());
                        subscriberModel.setGlobalAccountNo(subscriberESDTO.getGlobalAccountNo());
                        subscriberModel.setId(subscriberESDTO.getId());
                        subscriberModel.setImageUrl(subscriberESDTO.getImageUrl());
                        subscriberModel.setPhoneNo(subscriberESDTO.getPhoneNo());
                        subscriberModel.setActive(subscriberESDTO.isActive());
                        subscriberModelArrayList.add(subscriberModel);
                    });
                    subscriberData.setObjects(subscriberModelArrayList);
                    subscriberData.setTotalCount(Long.valueOf(response.getTotalCount()));
                } else {
                    subscriberData.setObjects(new ArrayList<>());
                    subscriberData.setTotalCount(Long.valueOf(0));
                }
                return subscriberData;
            }

            ArrayList<String> queryParams = new ArrayList<>();
            StringBuffer sb = new StringBuffer("");
            if (Objects.nonNull(subscriberSearchDTO.getName())) {
                queryParams.add("CONCAT( s.firstName, ' ', s.lastName ) LIKE '%" + subscriberSearchDTO.getName() + "%'");
            }

            if (Objects.nonNull(subscriberSearchDTO.getEmail())) {
                queryParams.add("s.email LIKE '%" + subscriberSearchDTO.getEmail() + "%'");
            }

            if (Objects.nonNull(subscriberSearchDTO.getGroupName())) {
                queryParams.add("LOWER(c.name) LIKE '%" + subscriberSearchDTO.getGroupName().toLowerCase() + "%'");
            }

            if (Objects.nonNull(subscriberSearchDTO.getGlobalAccountNo())) {
                queryParams.add("LOWER(s.globalAccountNo) LIKE '%" + subscriberSearchDTO.getGlobalAccountNo().toLowerCase() + "%'");
            }

            if (Objects.nonNull(subscriberSearchDTO.getCamsAccountNo())) {
                queryParams.add("LOWER(s.camsAccountNo) LIKE '%" + subscriberSearchDTO.getCamsAccountNo().toLowerCase() + "%'");
            }

            if (Objects.nonNull(subscriberSearchDTO.getPhoneNo())) {
                queryParams.add("LOWER(s.phoneNo) LIKE '%" + subscriberSearchDTO.getPhoneNo().toLowerCase() + "%'");
            }

            if (!queryParams.isEmpty()) {
                if (!CommonUtils.isSysAdmin()) {
                    sb.append("where sc.compartment_id = '" + subscriberSearchDTO.getGroupId() + "' and ");
                } else {
                    sb.append("Where ");
                }

                int i = 0;
                for (String condition : queryParams) {
                    if (i > 0)
                        sb.append(" and ");
                    sb.append(condition);
                    i++;
                }
            }
            queries = SubscriberSQL.COUNT_OF_SEARCH_SUBSCRIBERS.replace("###", sb.toString());
            String replaceWith = " LIMIT " + (Objects.nonNull(subscriberSearchDTO.getOffset()) ? subscriberSearchDTO.getOffset() : 0) + ", " + (Objects.nonNull(subscriberSearchDTO.getMax()) ? subscriberSearchDTO.getMax() : 10);
            String query = SubscriberSQL.GET_ALL_SUBSCRIBER.replace("###", sb.toString()) + replaceWith;
            users = dataAccessService.readNative(query, new HashMap<>());

        } else {
            Object usersList = getSubscriberFromElasticSearch(subscriberSearchDTO.getMax(), subscriberSearchDTO.getOffset(), subscriberSearchDTO.getOrder().name(), subscriberSearchDTO.getSortBy());
            if (Objects.nonNull(usersList)) return usersList;


            String replaceWith = " LIMIT " + (Objects.nonNull(subscriberSearchDTO.getOffset()) ? subscriberSearchDTO.getOffset() : 0) + ", " + (Objects.nonNull(subscriberSearchDTO.getMax()) ? subscriberSearchDTO.getMax() : 10);
            String query = !CommonUtils.isSysAdmin() ? (SubscriberSQL.GET_ALL_SUBSCRIBER.replace("###", "where sc.compartment_id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'")) : SubscriberSQL.GET_ALL_SUBSCRIBER.replace("###", EMPTY_STRING);
            users = dataAccessService.readNative(query + replaceWith, new HashMap<>());

            queries = SubscriberSQL.COUNT_TOTAL_SUBSCRIBERS;
            if (!CommonUtils.isSysAdmin()) {
                queries = queries.replaceAll("###", "u inner join subscriber_compartment sc on (u.id=sc.subscriber_id) where sc.compartment_id='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
            } else {
                queries = queries.replaceAll("###", EMPTY_STRING);
            }
        }

        if (Objects.nonNull(users) && !users.isEmpty()) {
            for (Object userList[] : users) {
                SubscriberListDTO.SubscriberData.SubscriberModel subscriberModel = subscriberData.new SubscriberModel();
                subscriberModel.setId(String.valueOf(userList[0]));
                subscriberModel.setName(String.valueOf(userList[1]) + SPACE + String.valueOf(userList[2]));
                subscriberModel.setEmail(String.valueOf(userList[3]));
                subscriberModel.setGlobalAccountNo(String.valueOf(userList[4]));
                subscriberModel.setImageUrl(String.valueOf(userList[5]));
                subscriberModel.setCamsAccountNo(String.valueOf(userList[6]));
                subscriberModel.setPhoneNo(String.valueOf(userList[7]));
                subscriberModel.isActive(Boolean.valueOf(userList[8].toString()));
                subscriberModel.setGroupName(String.valueOf(userList[9]));
                subscriberModel.setGroupId(String.valueOf(userList[10]));
                subscriberModelArrayList.add(subscriberModel);

            }
        }

        if (subscriberModelArrayList.isEmpty()) {
            SubscriberListDTO.SubscriberData.SubscriberModel subscriberModel = subscriberData.new SubscriberModel();
            subscriberModelArrayList.add(subscriberModel);
        }

        List<Object> count = (List<Object>) dataAccessService.readNative(queries, queryParam);

        subscriberData.setObjects(subscriberModelArrayList);
        subscriberData.setTotalCount(Long.valueOf(Long.valueOf(Objects.nonNull(count) ? count.get(0).toString() : "0")));
        subscriberListDTO.setData(subscriberData);

        return subscriberListDTO.getData();
    }

    private Object getSubscriberFromElasticSearch(Integer max, Integer offset, String order, String sortBy) {
        SortOrder sortOrder = order.equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
        ESRequest elasticSearchRequest = new ESRequest();
        elasticSearchRequest.setMax(max).setOffset(offset).setOrder(sortOrder).setSortBy(sortBy).setIndex(SUBSCRIBER_INDEX);
        if (!CommonUtils.isSysAdmin()) {
            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
            elasticSearchRequest.setQueryParams(queryParams);
        }
        ElasticSearchDTO elasticSearchDTO = esService.getPaginatedData(elasticSearchRequest);
        if (Objects.nonNull(elasticSearchDTO)) {
            if (elasticSearchDTO.getTotalCount() == 0) {
                SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
                SubscriberListDTO.SubscriberData subscriberData = subscriberListDTO.new SubscriberData();
                ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberModelArrayList = new ArrayList<>();
                subscriberData.setObjects(new ArrayList<>());
                subscriberData.setTotalCount(Long.valueOf(0));
                return subscriberData;
            } else {
                return elasticSearchDTO;
            }

        } else {
            return null;
        }
    }

    private boolean isSearchEnabled(SubscriberSearchDTO subscriberSearchDTO) {
        return Objects.nonNull(subscriberSearchDTO.getName()) || Objects.nonNull(subscriberSearchDTO.getGroupId()) || Objects.nonNull(subscriberSearchDTO.getEmail()) || Objects.nonNull(subscriberSearchDTO.getPhoneNo()) || Objects.nonNull(subscriberSearchDTO.getCamsAccountNo()) || Objects.nonNull(subscriberSearchDTO.getGlobalAccountNo());
    }

    public void editSubscriber(String subscriberIdOrGAN, EditSubscriberRequest editSubscriberRequest) throws Exception {
        try {
            if (Objects.nonNull(subscriberIdOrGAN) && !subscriberIdOrGAN.isEmpty()) {
                Subscriber subscriber = null;
                HashMap<String, Object> param = new HashMap<>();
                param.put("id", subscriberIdOrGAN);
                subscriber = manageCommonService.getSubscriberFromSubscriberIdOrGAN(subscriberIdOrGAN);

                if (Objects.nonNull(subscriber)) {
                    if (Objects.nonNull(editSubscriberRequest.getFirstName()) && !(Objects.isNull(subscriber.getFirstName()) ? "" : subscriber.getFirstName()).equals(editSubscriberRequest.getFirstName())) {
                        if (editSubscriberRequest.getFirstName().isEmpty())
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "First name can not be empty");

                        subscriber.setFirstName(editSubscriberRequest.getFirstName());
                    }

                    if (Objects.nonNull(editSubscriberRequest.getLastName()) && !(Objects.isNull(subscriber.getLastName()) ? "" : subscriber.getLastName()).equals(editSubscriberRequest.getLastName())) {
                        if (editSubscriberRequest.getLastName().isEmpty())
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Last name can not be empty");

                        subscriber.setLastName(editSubscriberRequest.getLastName());
                    }

                    if (Objects.nonNull(editSubscriberRequest.getCamsAccountNo()) && !(Objects.isNull(subscriber.getCamsAccountNo()) ? "" : subscriber.getCamsAccountNo()).equals(editSubscriberRequest.getCamsAccountNo())) {
                        if (editSubscriberRequest.getCamsAccountNo().isEmpty())
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cams Account No can not be empty");

                        subscriber.setCamsAccountNo(editSubscriberRequest.getCamsAccountNo());
                    }

                    if (Objects.nonNull(editSubscriberRequest.getGlobalAccountNo()) && !(Objects.isNull(subscriber.getGlobalAccountNo()) ? "" : subscriber.getGlobalAccountNo()).equals(editSubscriberRequest.getGlobalAccountNo())) {
                        if (editSubscriberRequest.getGlobalAccountNo().isEmpty())
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Global Account No can not be empty");

                        String queries = SubscriberSQL.COUNT_DUPLICATE_SUBCRIBER_BY_GAN;
                        HashMap<String, String> queryParams = new HashMap<>();
                        queryParams.put("gan", editSubscriberRequest.getGlobalAccountNo());
                        List<Object> count = (List<Object>) dataAccessService.readNative(queries, queryParams);
                        if (Integer.valueOf(count.get(0).toString()) > 0) {
                            throw new ConflictException(HttpStatus.CONFLICT.value(), "Global Account Number already exists. Cannot update subscriber");
                        }
                        subscriber.setGlobalAccountNo(editSubscriberRequest.getGlobalAccountNo());
                    }

                    boolean isEmailChanged = false;
                    if (Objects.nonNull(editSubscriberRequest.getEmail()) && !(Objects.isNull(subscriber.getEmail()) ? "" : subscriber.getEmail()).equals(editSubscriberRequest.getEmail())) {
                        if (editSubscriberRequest.getEmail().isEmpty())
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Email can not be empty");

//                        String queries = SubscriberSQL.COUNT_DUPLICATE_SUBCRIBER_BY_EMAIL;
//                        HashMap<String, String> queryParams = new HashMap<>();
//                        queryParams.put("email", editSubscriberRequest.getEmail());
//                        List<Object> count = (List<Object>) dataAccessService.readNative(queries, queryParams);
//                        if (Integer.valueOf(count.get(0).toString()) > 0) {
//                            throw new ConflictException(HttpStatus.CONFLICT.value(), "Email already exists.  Cannot update subscriber.");
//                        }
                        subscriber.setEmail(editSubscriberRequest.getEmail());
                        isEmailChanged = true;
                    }


                    if (Objects.nonNull(editSubscriberRequest.getPhoneNumber()) && !(Objects.isNull(subscriber.getPhoneNo()) ? "" : subscriber.getPhoneNo()).equals(editSubscriberRequest.getPhoneNumber())) {
                        if (editSubscriberRequest.getPhoneNumber().isEmpty())
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Phone number can not be empty");

                        subscriber.setPhoneNo(editSubscriberRequest.getPhoneNumber());

                    }


                    /*if (Objects.nonNull(editSubscriberRequest.getApId())) {
                        List<Equipment> equipmentList = manageEquipmentService.getAllEquipment(subscriberId);
                        for (Equipment equipment : equipmentList)
                            manageEquipmentService.detachEquipment(equipment.getId());

                        if(!editSubscriberRequest.getApId().isEmpty()) {
                            AttachEquipmentRequest attachEquipmentRequest = new AttachEquipmentRequest();
                            attachEquipmentRequest.setSubscriberId(subscriberId);
                            attachEquipmentRequest.setApId(new ArrayList<>(editSubscriberRequest.getApId()));
                            manageEquipmentService.attachEquipment(attachEquipmentRequest);
                        }
                    }*/

                    CommonUtils.setUpdateEntityFields(subscriber);

                    dataAccessService.update(Subscriber.class, subscriber);

                    if (isEmailChanged) {
                        LoginDetails loginDetails = (LoginDetails) dataAccessService.read(LoginDetails.class, subscriber.getId());
                        if(Objects.nonNull(loginDetails)) {
                            loginDetails.setEmail(subscriber.getEmail());
                            CommonUtils.setUpdateEntityFields(loginDetails);
                            dataAccessService.update(LoginDetails.class, loginDetails);
                        }
                    }


                    esService.updateById(SUBSCRIBER_INDEX, prepareDataForESSubscriber(subscriber), subscriber.getId(), null);

                    // update global and cams account for all the attached equipments

                    List<Equipment> equipmentList = manageEquipmentService.getAllEquipment(subscriber.getId());
                    for (Equipment equipment : equipmentList) {
                        esService.updateById(EQUIPMENT_INDEX, prepareDataForES2(equipment, subscriber), equipment.getId(), null);
                    }
                } else {

                    throw new UrlNotFoundException(HttpStatus.NOT_FOUND.value(), "Subscriber doesn't exists");
                }

            }
        } catch (ValidationException e) {
            throw new ValidationException(e.getCode(), e.getMessage());
        } catch (UrlNotFoundException e) {
            throw new UrlNotFoundException(HttpStatus.NOT_FOUND.value(), "Subscriber doesn't exists");
        } catch (ConflictException e) {
            throw new ConflictException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            LOG.error("Unable to update subscriber : ", e);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Unable to update subscriber");
        }
    }

    /*public Object getSubscriberById(DetachedEquipmentDTO equipmentDTO, String subscriberId) throws Exception {
        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
        ClusterInfo clusterInfo = null;
        if (Objects.nonNull(equipmentDTO.getClusterId()) && !equipmentDTO.getClusterId().equals(ZERO)) {
            clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, equipmentDTO.getClusterId());
        } else if (!CommonUtils.isSysAdmin()) {
            clusterInfo = compartment.getCluster().stream().filter(q -> q.isDefaultCluster()).findAny().orElse(null);
        }

        HashMap<String, Object> response = new HashMap<>();
        Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, subscriberId);
        if (Objects.nonNull(subscriber)) {
            response.put("firstName", subscriber.getFirstName());
            response.put("lastName", subscriber.getLastName());
            response.put("email", subscriber.getEmail());
            response.put("phoneNumber", subscriber.getPhoneNo());
            response.put("globalAccountNo", subscriber.getGlobalAccountNo());
            response.put("camsAccountNo", subscriber.getCamsAccountNo());
            response.put("groupId", subscriber.getCompartment().iterator().next().getId());

            if (elasticSearchEnable) {
                equipmentDTO.setSortBy("subscriberId");
                response.put("equipmentList", getEquipmentForSubscriberES(equipmentDTO, clusterInfo, subscriberId));
            } else {
                response.put("equipmentList", getForSubscriberFromMysql(equipmentDTO, clusterInfo, subscriberId));
            }

            List<String> attachedEquipId = new ArrayList<>();
           List<Equipment> attachedEquipList = manageEquipmentService.getAllEquipment(subscriberId);
            if(!attachedEquipList.isEmpty()){
                attachedEquipId = attachedEquipList.stream().map(q->q.getId()).collect(Collectors.toList());
            }

            response.put("apId",attachedEquipId);

        }
        return response;
    }*/

    public void checkUserAccessForSubscriber(String subscriberId) {
        if(!CommonUtils.isSysAdmin()) {
            if(CommonUtils.isEndUser()) {
                String _subscriber = ExecutionContext.get().getUsercontext().getId();
                if(!_subscriber.equals(subscriberId)) {
                    throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
                }
            }
        }
    }

    public Object getSubscriberById(String subscriberIdOrGAN) throws Exception {
        HashMap<String, Object> response = new HashMap<>();
        Subscriber subscriber = manageCommonService.getSubscriberFromSubscriberIdOrGAN(subscriberIdOrGAN);
        if (Objects.nonNull(subscriber)) {
            checkUserAccessForSubscriber(subscriber.getId());
            manageCommonService.checkForGroupAccess(subscriber.getCompartment().iterator().next().getId());
            response.put("id", Objects.nonNull(subscriber.getId()) ? subscriber.getId() : "");
            response.put("firstName", Objects.nonNull(subscriber.getFirstName()) ? subscriber.getFirstName() : "");
            response.put("lastName", Objects.nonNull(subscriber.getLastName()) ? subscriber.getLastName() : "");
            response.put("name", (Objects.nonNull(subscriber.getFirstName()) ? subscriber.getFirstName() : "") + (Objects.nonNull(subscriber.getLastName()) ? subscriber.getLastName() : ""));
            response.put("globalAccountNo", Objects.nonNull(subscriber.getGlobalAccountNo()) ? subscriber.getGlobalAccountNo() : "");
            response.put("imageUrl", Objects.nonNull(subscriber.getImageUrl()) ? subscriber.getImageUrl() : "");
            response.put("camsAccountNo", Objects.nonNull(subscriber.getCamsAccountNo()) ? subscriber.getCamsAccountNo() : "");
            response.put("phoneNumber", Objects.nonNull(subscriber.getPhoneNo()) ? subscriber.getPhoneNo() : "");
            response.put("email", Objects.nonNull(subscriber.getEmail()) ? subscriber.getEmail() : "");
            response.put("groupName", Objects.nonNull(subscriber.getCompartment().iterator().next().getName()) ? subscriber.getCompartment().iterator().next().getName() : null);
            response.put("groupId", Objects.nonNull(subscriber.getCompartment().iterator().next().getId()) ? subscriber.getCompartment().iterator().next().getId() : null);
            response.put("active", Objects.nonNull(subscriber.isActive()) ? subscriber.isActive() : null);
            response.put("isp", Objects.nonNull(subscriber.getCompartment().iterator().next().getId()) ? ispService.getISPById(subscriber.getCompartment().iterator().next().getIspId()).getData().getName() : "");
        } else {
            throw new UrlNotFoundException(HttpStatus.NOT_FOUND.value(), "Subscriber doesn't exists");
        }
        return response;
    }

    private Object getEquipmentForSubscriberES(DetachedEquipmentDTO detachedEquipmentDTO, ClusterInfo clusterInfo, String subscriberId) {
        HashMap<String, Object> response = new HashMap<>();
        List<HashMap<String, Object>> listOfSubscriber = new ArrayList<>();
        ElasticSearchDTO esResponse = null;
        if (isDetachedEqSearchEnabled(detachedEquipmentDTO)) {
            ESRequest elasticSearchRequest = new ESRequest();
            elasticSearchRequest.setMax(detachedEquipmentDTO.getMax()).setOffset(detachedEquipmentDTO.getOffset()).setSortBy(detachedEquipmentDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
            HashMap<String, Object> esQueryParams = new HashMap<>();
            esQueryParams.put("serviceTelephoneNo", detachedEquipmentDTO.getServiceTelephoneNo());
            esQueryParams.put("rgwMAC", detachedEquipmentDTO.getMacAddress());
            esQueryParams.put("apId", detachedEquipmentDTO.getSerialNumber());

            HashMap<String, Object> shouldParams = new HashMap<>();
            shouldParams.put("must_not", "subscriberId");
            shouldParams.put("subscriberId", subscriberId);

            if (!CommonUtils.isSysAdmin()) {
                esQueryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
            }
            elasticSearchRequest.setQueryParams(esQueryParams);
            elasticSearchRequest.setMustNotExistValue(null);
            elasticSearchRequest.setShouldParams(shouldParams);
            if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()), false);
            } else {
                esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", null, false);
            }

        } else {
            if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                ESRequest elasticSearchRequest = new ESRequest();
                HashMap<String, Object> esQueryParams = new HashMap<>();
                List<String> mustNotList = new ArrayList();
                mustNotList.add("subscriberId");

                elasticSearchRequest.setQueryParams(esQueryParams);
                elasticSearchRequest.setMustNotExistValue(mustNotList);

                elasticSearchRequest.setMax(detachedEquipmentDTO.getMax()).setOffset(detachedEquipmentDTO.getOffset()).setSortBy(detachedEquipmentDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
                if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                    esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()), false);
                } else {
                    esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", null, false);
                }
            } else {
                SortOrder sortOrder = detachedEquipmentDTO.getOrder().name().equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
                ESRequest elasticSearchRequest = new ESRequest();
                HashMap<String, Object> esQueryParams = new HashMap<>();

                HashMap<String, Object> shouldParams = new HashMap<>();
                shouldParams.put("must_not", "subscriberId");
                shouldParams.put("subscriberId", subscriberId);

                elasticSearchRequest.setMustNotExistValue(null);
                elasticSearchRequest.setShouldParams(shouldParams);
                elasticSearchRequest.setQueryParams(esQueryParams);
                elasticSearchRequest.setMax(detachedEquipmentDTO.getMax()).setOffset(detachedEquipmentDTO.getOffset()).setOrder(sortOrder).setSortBy(detachedEquipmentDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
                if (!CommonUtils.isSysAdmin()) {
                    HashMap<String, Object> queryParams = new HashMap<>();
                    queryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
                    elasticSearchRequest.setQueryParams(queryParams);
                }
                esResponse = esService.getPaginatedData(elasticSearchRequest);
            }
        }

        if (Objects.nonNull(esResponse) && esResponse.getObjects().size() > 0) {
            esResponse.getObjects().forEach(subscriber -> {
                HashMap<String, Object> subscriberModel = new HashMap<>();
                subscriberModel.put("serialNumber", subscriber.get("serialNumber"));
                subscriberModel.put("macAddress", subscriber.get("rgwMAC"));
                subscriberModel.put("serviceTelephoneNo", subscriber.get("serviceTelephoneNo"));
                subscriberModel.put("equipmentId", subscriber.get("id"));

                listOfSubscriber.add(subscriberModel);
            });
        }
        response.put("objects", listOfSubscriber);
        response.put("totalCount", Objects.isNull(esResponse) ? 0 : esResponse.getTotalCount());
        return response;
    }

    private Object getForSubscriberFromMysql(DetachedEquipmentDTO equipmentDTO, ClusterInfo clusterInfo, String subscriberId) throws Exception {
        long count = 0;
        String countQuery;

        List<Object[]> equipmentList;

        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("subscriberId", subscriberId);

        String limitSql = " ORDER BY subscriberId DESC LIMIT " + (Objects.nonNull(equipmentDTO.getOffset()) ? equipmentDTO.getOffset() : 0) + COMMA + (Objects.nonNull(equipmentDTO.getMax()) ? equipmentDTO.getMax() : 10);
        if (isDetachedEqSearchEnabled(equipmentDTO)) {
            StringBuffer sb = getDetachedEquipmentSearchQuery(equipmentDTO);

            if (Objects.nonNull(clusterInfo) || Objects.nonNull(equipmentDTO.getClusterId())) {
                String equipmentByClusterSql = GET_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + sb.toString() + " AND cluster.cluster_id='" + (Objects.nonNull(equipmentDTO.getClusterId()) ? equipmentDTO.getClusterId() : clusterInfo.getId()) + "' " + limitSql;
                countQuery = COUNT_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + sb.toString() + " AND cluster.cluster_id='" + (Objects.nonNull(equipmentDTO.getClusterId()) ? equipmentDTO.getClusterId() : clusterInfo.getId()) + "' ";

                equipmentList = dataAccessService.readNative(equipmentByClusterSql, queryParam);
            } else {
                String equipmentSql;
                if (!CommonUtils.isSysAdmin()) {
                    equipmentSql = GET_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + sb.toString() + " AND eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'" + limitSql;
                    countQuery = COUNT_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + sb.toString() + " AND eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'";
                } else {
                    equipmentSql = GET_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + sb.toString() + limitSql;
                    countQuery = COUNT_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + sb.toString();
                }

                equipmentList = dataAccessService.readNative(equipmentSql, queryParam);
            }
        } else {
            if (Objects.nonNull(clusterInfo) || Objects.nonNull(equipmentDTO.getClusterId())) {
                String equipmentByClusterSql = GET_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + " Where cluster.cluster_id='" + (Objects.nonNull(equipmentDTO.getClusterId()) ? equipmentDTO.getClusterId() : clusterInfo.getId()) + "' " + limitSql;
                countQuery = COUNT_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + " Where cluster.cluster_id='" + (Objects.nonNull(equipmentDTO.getClusterId()) ? equipmentDTO.getClusterId() : clusterInfo.getId()) + "' ";
                equipmentList = dataAccessService.readNative(equipmentByClusterSql, queryParam);
            } else {
                String equipmentSql;
                if (!CommonUtils.isSysAdmin()) {
                    equipmentSql = GET_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + " Where eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'" + limitSql;
                    countQuery = COUNT_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + " Where eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'";
                } else {
                    equipmentSql = GET_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER + limitSql;
                    countQuery = COUNT_ATTACHED_AND_DETACHED_FOR_SUBSCRIBER;
                }
                equipmentList = dataAccessService.readNative(equipmentSql, queryParam);
            }
        }

        List<Object> totalEquipment = (List<Object>) dataAccessService.readNative(countQuery, queryParam);
        count = Long.valueOf(Objects.nonNull(totalEquipment) ? totalEquipment.get(0).toString() : "0");

        DetachedEquipmentListDTO subscriberListDTO = new DetachedEquipmentListDTO();
        DetachedEquipmentListDTO.DetachedEquipmentListData data = subscriberListDTO.new DetachedEquipmentListData();

        ArrayList<DetachedEquipmentListDTO.DetachedEquipmentListData.DetachedEquipmentModel> subscriberDataList = new ArrayList<>();

        if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty()) {
            for (Object subscriberDetail[] : equipmentList) {
                DetachedEquipmentListDTO.DetachedEquipmentListData.DetachedEquipmentModel subscriberData = data.new DetachedEquipmentModel();

                subscriberData.setEquipmentId(isNullString(String.valueOf(subscriberDetail[0])));
                subscriberData.setSerialNumber(isNullString(String.valueOf(subscriberDetail[1])));
                subscriberData.setMacAddress(isNullString(String.valueOf(subscriberDetail[2])));
                subscriberData.setServiceTelephoneNo(isNullString(String.valueOf(subscriberDetail[3])));

                subscriberDataList.add(subscriberData);
            }
        }

        data.setObjects(subscriberDataList);
        data.setTotalCount(count);
        subscriberListDTO.setData(data);

        return subscriberListDTO.getData();
    }

    private boolean isDetachedEqSearchEnabled(DetachedEquipmentDTO detachedEquipmentDTO) {
        return Objects.nonNull(detachedEquipmentDTO.getSerialNumber()) || Objects.nonNull(detachedEquipmentDTO.getMacAddress()) || Objects.nonNull(detachedEquipmentDTO.getServiceTelephoneNo());
    }

    private String isNullString(String data) {
        return data.equals("null") ? null : data;
    }

    private StringBuffer getDetachedEquipmentSearchQuery(DetachedEquipmentDTO detachedEquipmentDTO) {
        ArrayList<String> queryParams = new ArrayList<>();

        StringBuffer sb = new StringBuffer(ApplicationConstants.EMPTY_STRING);

        if (Objects.nonNull(detachedEquipmentDTO.getSerialNumber())) {
            queryParams.add("eq.rgwSerial LIKE '%" + detachedEquipmentDTO.getSerialNumber() + "%'");
        }

        if (Objects.nonNull(detachedEquipmentDTO.getMacAddress())) {
            queryParams.add("eq.rgwMAC LIKE '%" + detachedEquipmentDTO.getMacAddress() + "%'");
        }

        if (Objects.nonNull(detachedEquipmentDTO.getServiceTelephoneNo())) {
            queryParams.add("eq.serviceTelephoneNo LIKE '%" + detachedEquipmentDTO.getServiceTelephoneNo() + "%'");
        }

        if (!queryParams.isEmpty()) {
//            sb.append("Where ");
            int i = 0;
            for (String condition : queryParams) {
                sb.append(" and ");
                sb.append(condition);
                i++;
            }
        }

        return sb;
    }

    public void deleteSubscriberById(String subscriberIdOrGAN) throws Exception {
        Subscriber subscriber = manageCommonService.getSubscriberFromSubscriberIdOrGAN(subscriberIdOrGAN);
        if (Objects.nonNull(subscriber)) {
            HashMap<String, Object> query = new HashMap<>();
            query.put("subscriberId", subscriber.getId());

            List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_FOR_SUBSCRIBER, query);
            if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty()) {
                for (Equipment equipment : equipmentList) {
                    manageEquipmentService.deleteEquipmentById(equipment.getId());
                }
            }
            dataAccessService.delete(Subscriber.class, subscriber.getId());

            query.clear();
            query.put("email", subscriber.getEmail());

            dataAccessService.deleteUpdateNative(DELETE_LOGIN_DETAILS_BY_EMAIL, query);
            esService.delete(SUBSCRIBER_INDEX, subscriber.getId());
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Subscriber doesn't exists");
        }
    }

    public List<DeviceDetailInfoDTO> getSubscriberStatus(String rgwSerialNumber) throws Exception {

        if (Objects.isNull(rgwSerialNumber) || rgwSerialNumber.trim().isEmpty()) {
            throw new ValidationException((HttpStatus.BAD_REQUEST.value()), "Invalid RGW Serial Number");
        }

        HashMap<String, String> headerInfo = new HashMap<>();

        HashMap<String, Object> queryParam = new HashMap<>();

        List<DeviceDetailInfoDTO> deviceDetailInfoDTO = new ArrayList<>();

        ObjectMapper mapper = new ObjectMapper();

        List<String> vmqUrlList = mqttService.getVmqUrls();
        if (vmqUrlList.isEmpty())
            return deviceDetailInfoDTO;

        String url = "http://" + vmqUrlList.get(0) + SUBSCRIBER_STATUS_API_PATH + rgwSerialNumber;

        String httpResponse = EMPTY_STRING;
        try {
            httpResponse = httpService.doGet(url, headerInfo, queryParam);
        } catch (Exception e) {
            LOG.error("Error while Fetching Resource Specification");
        }
        if (Objects.isNull(httpResponse) || httpResponse.trim().isEmpty()) {
            LOG.error("Invalid Response");
            throw new ValidationException((HttpStatus.BAD_REQUEST.value()), "Invalid URL");
        }

        HashMap response = mapper.readValue(httpResponse, HashMap.class);
        HashMap output = (HashMap) response.get("output");

        if (Objects.nonNull(output) && !output.isEmpty()) {
            if (Objects.nonNull(output.get("DeviceDetail"))) {
                if (output.get("DeviceDetail") instanceof String) {
                    return deviceDetailInfoDTO;
                }

                List<HashMap<String, String>> deviceDetailList = (List<HashMap<String, String>>) output.get("DeviceDetail");
                for (HashMap deviceDetail : deviceDetailList) {
                    DeviceDetailInfoDTO deviceDetailInfo = new DeviceDetailInfoDTO();
                    deviceDetailInfo.setNode((String) deviceDetail.get("node"));
                    deviceDetailInfo.setIsOnline((String) deviceDetail.get("is_online"));
                    deviceDetailInfo.setPeerHost((String) deviceDetail.get("peer_host"));
                    deviceDetailInfoDTO.add(deviceDetailInfo);
                }
            }
        }

        return deviceDetailInfoDTO;
    }

    private Boolean checkEquipmentForGroupAccess(String serial) throws Exception {

        if (CommonUtils.isSysAdmin() || CommonUtils.isGroupAdmin()) return true;

        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("rgwSerial", serial);
        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_LIST_BY_RGW_SERIAL, queryParams);

        if(equipmentList != null && equipmentList.isEmpty()) {
            Equipment equipment = equipmentList.get(0);
            return CommonUtils.isSameGroup(equipment.getGroupId());
        }

        return false;
    }

    private String getIspNameByGroupId(String groupId) throws Exception {
        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, groupId);
        if (compartment == null || compartment.getIspId() == null) {
            return null;
        }

        ISPDTO ispdto = ispService.getISPById(compartment.getIspId());
        if (ispdto == null || ispdto.getData() == null) {
            return null;
        }

        return ispdto.getData().getName();
    }

    public RGWDisconnectedDTO getRGWDisconnectedList(RGWPaginationRequest rgwPaginationRequest) throws Exception {

        Integer max = rgwPaginationRequest.getMax();
        Integer offset = rgwPaginationRequest.getOffset();
        Long fromDate = rgwPaginationRequest.getFromDate();
        Long toDate = rgwPaginationRequest.getToDate();
        HashMap<String, String> comConfig = commonService.read(COMMON_CONFIG);

        int equipmentOfflineTime = 7;
        if (Objects.nonNull(comConfig) && Objects.nonNull(comConfig.get(COMMON_DEVICE_STATUS_RETENTION_TIME))) {
            try {
                equipmentOfflineTime = Integer.valueOf(comConfig.get(COMMON_DEVICE_STATUS_RETENTION_TIME));
            } catch (Exception e) {
                LOG.error("Failed to get equipment offline minutes", e);
            }
        }

        if (Objects.nonNull(fromDate) && Objects.isNull(toDate)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide to date");
        }

        if (Objects.isNull(fromDate) && Objects.nonNull(toDate)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please provide from date");
        }

        String ispName = null;
        if (CommonUtils.isGroupAdmin()) {
            String groupId = CommonUtils.getGroupIdOfLoggedInUser();
            if (groupId == null) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group ID for the logged-in user is null");
            }
            ispName = getIspNameByGroupId(groupId);
            if (Objects.isNull(ispName)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP name not found for the group ID: " + groupId);
            }
        }

        long timeDiff = CommonUtils.getCurrentTimeInMillis() - equipmentOfflineTime * 60 * 1000;

        BasicDBObject params = new BasicDBObject();
        params.put("type", ApplicationConstants.GATEWAY);
        if (CommonUtils.isGroupAdmin()) {
            params.put("isp", ispName);
        }
        if (Objects.nonNull(fromDate) && Objects.nonNull(toDate)) {
            BasicDBObject timeCriteria = new BasicDBObject();
            timeCriteria.put("$lt", timeDiff);
            timeCriteria.put("$gte", fromDate);
            timeCriteria.put("$lte", toDate);
            params.put("sendingTime", timeCriteria);
        } else {
            params.put("sendingTime", new BasicDBObject("$lt", timeDiff));
        }
        BasicDBObject projection = new BasicDBObject();
        projection.put("serialNumber", 1);
        projection.put("isp", 1);
        projection.put("macAddress", 1);
        projection.put("timestamp", 1);
        projection.put("etlVersion", 1);
        projection.put("fwVersion", 1);
        Long count = mongoServiceImpl.count(params, new HashMap<>(), AP_DETAIL);
        List<BasicDBObject> disconnectedRGWList = mongoServiceImpl.aggregationQueryForPagination(params, AP_DETAIL, projection, TIMESTAMP, DESC, max, offset);
        RGWDisconnectedDTO rgwDisconnectedDTO = new RGWDisconnectedDTO();
        RGWDisconnectedDTO.RGWDisconnectedData rgwDisconnectedData = rgwDisconnectedDTO.new RGWDisconnectedData();
        ArrayList<RGWDisconnectedDTO.RGWDisconnectedData.RGWDisconnectedModel> rgwDisconnectedList = new ArrayList<>();
        if (Objects.nonNull(disconnectedRGWList) && !disconnectedRGWList.isEmpty()) {
            for (BasicDBObject rgwDisconnected : disconnectedRGWList) {
                if (Objects.nonNull(rgwDisconnected)) {
                    String serial = rgwDisconnected.get("serialNumber") != null ? String.valueOf(rgwDisconnected.get("serialNumber")) : null;
                    if(checkEquipmentForGroupAccess(serial)) {
                        RGWDisconnectedDTO.RGWDisconnectedData.RGWDisconnectedModel rgwDisconnectedModel = rgwDisconnectedData.new RGWDisconnectedModel();
                        rgwDisconnectedModel.setSerialNumber(Objects.isNull(rgwDisconnected.get("serialNumber")) ? "NA" : rgwDisconnected.get("serialNumber").toString());
                        rgwDisconnectedModel.setIsp(Objects.isNull(rgwDisconnected.get("isp")) ? "NA" : rgwDisconnected.get("isp").toString());
                        rgwDisconnectedModel.setMacAddress(Objects.isNull(rgwDisconnected.get("macAddress")) ? "NA" : rgwDisconnected.get("macAddress").toString());
                        rgwDisconnectedModel.setLastReportedAt(Objects.isNull(rgwDisconnected.get("timestamp")) ? 0 : Long.valueOf(rgwDisconnected.get("timestamp").toString()));
                        rgwDisconnectedModel.setVersion(Objects.isNull(rgwDisconnected.get("etlVersion")) ? "NA" : rgwDisconnected.get("etlVersion").toString());
                        rgwDisconnectedModel.setFwVersion(Objects.isNull(rgwDisconnected.get("fwVersion")) ? "NA" : rgwDisconnected.get("fwVersion").toString());
                        rgwDisconnectedList.add(rgwDisconnectedModel);
                    }
                }
            }
        }
        rgwDisconnectedData.setObjects(rgwDisconnectedList);
        rgwDisconnectedData.setTotalCount(count);
        rgwDisconnectedDTO.setData(rgwDisconnectedData);
        return rgwDisconnectedDTO;
    }

    public void subscriberBulkFileUpload(MultipartFile file, String isp) throws Exception {
        if (Objects.isNull(file) || file.isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File cannot be empty");
        if (!manageCommonService.isCSVFile(file)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Content is not valid CSV");
        }

        int fileLength = file.getOriginalFilename().length();
        if (file.getOriginalFilename().indexOf(DOT) > -1)
            fileLength = file.getOriginalFilename().indexOf(DOT);

        String humanReadableFileName = file.getOriginalFilename().substring(0, fileLength) + UNDER_SCORE_STRING + Calendar.getInstance().getTimeInMillis() + ".csv";

        String fileNameAndPath = BULK_UPLOAD_SUB_DIR + "humanReadableFileName/" + humanReadableFileName;
        HashMap<String, String> uploadData = s3Service.uploadFile(file, fileNameAndPath);
        if (Objects.isNull(uploadData) || Objects.isNull(uploadData.get("secure_url")) || uploadData.get("secure_url").isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in upload file");
        }
        HashMap<String, Object> data = new HashMap<>();
        data.put("status", _SUBSCRIBER_BULK_STATUS_NEW);
        data.put("name", humanReadableFileName);
        data.put("url", uploadData.get("secure_url"));
        data.put("size", file.getSize());
        data.put("timestamp", CommonUtils.getCurrentTimeInMillis());
        data.put("isp", isp);
        data.put("createdBy", CommonUtils.getUserIdOfLoggedInUser());
        data.put("type", _FILE_TYPE);
        data.put("id", CommonUtils.generateUUID());
        data.put("totalRecord", manageCommonService.countNoOfRecord(file));
        data.put("noOfRecordUpdated", 0L);
        data.put("noOfRecordScanned", 0L);
        data.put("invalidRecord", 0L);
        mongoServiceImpl.create(SUBSCRIBERS_DATA_FILE_DETAILS, data);
    }
}

