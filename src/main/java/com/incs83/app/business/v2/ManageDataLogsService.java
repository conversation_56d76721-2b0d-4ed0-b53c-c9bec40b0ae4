package com.incs83.app.business.v2;

import com.incs83.app.entities.cassandra.DataLogs;
import com.incs83.app.service.repository.CassandraRepository;
import com.incs83.exceptions.handler.ValidationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Service("v2.DataLogsService")
public class ManageDataLogsService {

    @Autowired
    CassandraRepository cassandraRepository;

    public List<HashMap<String, Object>> getDataLogs(String rgwSerialNumber) throws Exception {
        if (Objects.isNull(rgwSerialNumber) || rgwSerialNumber.trim().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please enter a Valid RGW SerialNumber");
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("userId", rgwSerialNumber.trim());

        HashMap<String, Object> rangeParam = new HashMap<>();
        rangeParam.put("key", "timestamp");
        rangeParam.put("operand", "gt");

        List<String> projection = new ArrayList<>();

        List<HashMap<String, Object>> response = new ArrayList<>();

        List<DataLogs> dataLogs = cassandraRepository.read(DataLogs.class, param, rangeParam, null, projection, 180);
        if (Objects.nonNull(dataLogs)) {
            dataLogs.forEach(dataLog -> {
                HashMap<String, Object> userData = new HashMap<>();
                userData.put("userId", dataLog.getUserId());
                userData.put("timestamp", dataLog.getTimestamp());
                userData.put("recordList", dataLog.getData());
                response.add(userData);
            });
        }
        return response;
    }
}
