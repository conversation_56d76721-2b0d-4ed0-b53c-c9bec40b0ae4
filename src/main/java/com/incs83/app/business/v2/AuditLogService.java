package com.incs83.app.business.v2;


import com.incs83.app.common.v3.AuditLogRequest;
import com.incs83.app.constants.queries.AuditLogsSQL;
import com.incs83.app.responsedto.v2.auditLogs.AuditLogsDTO;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.incs83.app.constants.queries.AuditLogsSQL.*;

@Service
public class AuditLogService {

    @Autowired
    private DataAccessService dataAccessService;


    public HashMap<String, Object> getAuditLogs(AuditLogRequest auditLogRequest) throws Exception {

        if (!(auditLogRequest.getOrder().equals("ASC") || auditLogRequest.getOrder().equals("DESC")))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Allowed values for order - ASC,DESC");

        if (Objects.nonNull(auditLogRequest.getSortBy()) && (!auditLogRequest.getSortBy().equalsIgnoreCase("firstName") && !auditLogRequest.getSortBy().equalsIgnoreCase("lastName") && !auditLogRequest.getSortBy().equalsIgnoreCase("method") && !auditLogRequest.getSortBy().equalsIgnoreCase("email") && !auditLogRequest.getSortBy().equalsIgnoreCase("url") && !auditLogRequest.getSortBy().equalsIgnoreCase("time") && !auditLogRequest.getSortBy().equalsIgnoreCase("ip")))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "sortBy value is not allowed");

        if (Objects.nonNull(auditLogRequest.getOffset()) && auditLogRequest.getOffset() < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Offset cannot be negative");

        return processAuditLogsForUserAndSubscriber(auditLogRequest);

    }

    public HashMap<String, Object> exportAllAuditLogs() throws Exception {

        List<Object[]> auditLogs = null;
        ArrayList<String> queryParams = new ArrayList<>();
        StringBuffer sbQuery = new StringBuffer("");

        String orderBy = "time" + " " + "DESC";

        sbQuery.append(" Order By " + orderBy);

        String queryString = String.format(AuditLogsSQL.GET_AUDIT_LOGS_FOR_USER, sbQuery.toString());
        auditLogs = dataAccessService.readNative(queryString, new HashMap<>());

        List<AuditLogsDTO> auditLogsDTOList = new ArrayList<>();
        if (!Objects.isNull(auditLogs) && !auditLogs.isEmpty()) {
            for (Object auditLog[] : auditLogs) {
                AuditLogsDTO auditLogsDTO = new AuditLogsDTO();
                auditLogsDTO.setFirstName(String.valueOf(auditLog[0]));
                auditLogsDTO.setLastName(String.valueOf(auditLog[1]));
                auditLogsDTO.setEmail(String.valueOf(auditLog[2]));
                auditLogsDTO.setMethod(String.valueOf(auditLog[3]));
                auditLogsDTO.setUrl(String.valueOf(auditLog[4]));
                auditLogsDTO.setTime(String.valueOf(auditLog[5]));
                auditLogsDTO.setIp(String.valueOf(auditLog[6]));
                auditLogsDTO.setOperation(String.valueOf(auditLog[7]));
                auditLogsDTO.setPayload(String.valueOf(auditLog[8]));
                auditLogsDTO.setApId(String.valueOf(auditLog[9]));
                auditLogsDTO.setResponseCode(String.valueOf(auditLog[10]));
                auditLogsDTO.setResponseMessage(String.valueOf(auditLog[11]));
                auditLogsDTOList.add(auditLogsDTO);
            }
        }

        HashMap<String, Object> mapOfAuditLogs = new HashMap<>();
        mapOfAuditLogs.put("object", auditLogsDTOList);
        return mapOfAuditLogs;
    }

    private HashMap<String, Object> processAuditLogsForUserAndSubscriber(AuditLogRequest auditLogRequest) throws Exception {

        List<Object[]> auditLogs;
        ArrayList<String> queryParams = new ArrayList<>();
        String orderBy = auditLogRequest.getSortBy() + " " + auditLogRequest.getOrder();
        StringBuffer sbQuery = new StringBuffer("");
        Object auditCount;
        List<AuditLogsDTO> auditLogsDTOList = new ArrayList<>();

        fillUpConditonsForQuery(auditLogRequest, queryParams, sbQuery);

        String countString = String.format(AUDIT_LOGS_COUNT_FOR_USER_SUBSCRIBER_NEW, sbQuery.toString());
        sbQuery.append(" Order By ").append(" time DESC ").append(" LIMIT ").append(Objects.nonNull(auditLogRequest.getOffset()) ? auditLogRequest.getOffset() : 0).append(", ").append(Objects.nonNull(auditLogRequest.getMax()) ? auditLogRequest.getMax() : 10);
        String queryString = String.format(AUDIT_LOGS_DATA_FOR_USER_SUBSCRIBER_NEW, sbQuery.toString());
        auditLogs = dataAccessService.readNative(queryString, new HashMap<>());
        auditCount = dataAccessService.readNative(countString, new HashMap<>());
        Integer totalLogsCount = Integer.valueOf(Objects.nonNull(auditCount) ? ((List) auditCount).get(0).toString() : "0");

        return makeAuditLogsDTO(auditLogs, auditLogsDTOList, totalLogsCount);
    }

    private void fillUpConditonsForQuery(AuditLogRequest auditLogRequest, ArrayList<String> queryParams, StringBuffer sbQueryUser) {

        if (Objects.nonNull(auditLogRequest.getEmail()) && !auditLogRequest.getEmail().isEmpty()) {
            queryParams.add("( u.email= " + '"' + auditLogRequest.getEmail() + '"' + " or s.email= " + '"' + auditLogRequest.getEmail() + '"' + " )");
        }

        if (Objects.nonNull(auditLogRequest.getOperation()) && !auditLogRequest.getOperation().isEmpty()) {
            String query = '"' + StringUtils.join(auditLogRequest.getOperation(), "\", \"") + '"';
            queryParams.add("al.operation IN " + '(' + query + ')');
        }

        if (Objects.nonNull(auditLogRequest.getResponseCode()) && !auditLogRequest.getResponseCode().isEmpty()) {
            String query = '"' + StringUtils.join(auditLogRequest.getResponseCode(), "\", \"") + '"';
            queryParams.add("al.responseCode IN " + '(' + query + ')');
        }


        if (Objects.nonNull(auditLogRequest.getFrom()) && Objects.nonNull(auditLogRequest.getTo())) {
            if (Long.parseLong(auditLogRequest.getFrom()) > Long.parseLong(auditLogRequest.getTo()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "from value must be less than to value");
            queryParams.add("al.time BETWEEN " + auditLogRequest.getFrom() + " AND " + auditLogRequest.getTo());

        }

        if (Objects.isNull(auditLogRequest.getFrom()) && Objects.nonNull(auditLogRequest.getTo())) {
            if (System.currentTimeMillis() < Long.parseLong(auditLogRequest.getTo()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "to value must be less than current time");
            queryParams.add("al.time BETWEEN " + Long.toString(System.currentTimeMillis() - 86400000L) + " AND " + auditLogRequest.getTo());

        }

        if (Objects.isNull(auditLogRequest.getTo()) && Objects.nonNull(auditLogRequest.getFrom())) {
            if (System.currentTimeMillis() < Long.parseLong(auditLogRequest.getFrom()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "from value must be less than current time");
            queryParams.add("al.time BETWEEN " + auditLogRequest.getFrom() + " AND " + Long.toString(System.currentTimeMillis()));
        }

        if (Objects.isNull(auditLogRequest.getTo()) && Objects.isNull(auditLogRequest.getFrom())) {
            queryParams.add("al.time BETWEEN " + Long.toString(System.currentTimeMillis() - 86400000L) + " AND " + Long.toString(System.currentTimeMillis()));
        }

        if (Objects.nonNull(auditLogRequest.getRgwSerial()) && !auditLogRequest.getRgwSerial().isEmpty()) {
            queryParams.add("al.apId= " + '"' + auditLogRequest.getRgwSerial() + '"');
        }

        if (Objects.nonNull(auditLogRequest.getIp()) && !auditLogRequest.getIp().isEmpty()) {
            queryParams.add("al.ip= " + '"' + auditLogRequest.getIp() + '"');
        }


        if (Objects.nonNull(auditLogRequest.getFirstName()) && !auditLogRequest.getFirstName().isEmpty()) {
            queryParams.add("( u.firstName= " + '"' + auditLogRequest.getFirstName() + '"' + " or s.firstName= " + '"' + auditLogRequest.getFirstName() + '"' + " )");
        }

        if (Objects.nonNull(auditLogRequest.getLastName()) && !auditLogRequest.getLastName().isEmpty()) {
            queryParams.add("( u.lastName= " + '"' + auditLogRequest.getLastName() + '"' + " or s.lastName= " + '"' + auditLogRequest.getLastName() + '"' + " )");
        }

        if (!queryParams.isEmpty()) {
            for (String condition : queryParams) {
                sbQueryUser.append(" and ");
                sbQueryUser.append(condition);
            }
        }

    }

    private HashMap<String, Object> makeAuditLogsDTO(List<Object[]> auditLogs, List<AuditLogsDTO> auditLogsDTOList, Integer totalLogsCount) {

        if (Objects.nonNull(auditLogs) && !auditLogs.isEmpty()) {
            for (Object[] auditLog : auditLogs) {
                AuditLogsDTO auditLogsDTO = new AuditLogsDTO();
                auditLogsDTO.setFirstName(String.valueOf(auditLog[0]));
                auditLogsDTO.setLastName(String.valueOf(auditLog[1]));
                auditLogsDTO.setEmail(String.valueOf(auditLog[2]));
                auditLogsDTO.setMethod(String.valueOf(auditLog[3]));
                auditLogsDTO.setUrl(String.valueOf(auditLog[4]));
                auditLogsDTO.setTime(String.valueOf(auditLog[5]));
                auditLogsDTO.setIp(String.valueOf(auditLog[6]));
                auditLogsDTO.setOperation(String.valueOf(auditLog[7]));
                auditLogsDTO.setPayload(String.valueOf(auditLog[8]));
                auditLogsDTO.setApId(String.valueOf(auditLog[9]));
                auditLogsDTO.setResponseCode(String.valueOf(auditLog[10]));
                auditLogsDTO.setResponseMessage(String.valueOf(auditLog[11]));
                auditLogsDTOList.add(auditLogsDTO);
            }
        }

        HashMap<String, Object> mapOfAuditLogs = new HashMap<>();
        mapOfAuditLogs.put("object", auditLogsDTOList);
        mapOfAuditLogs.put("count", totalLogsCount);

        return mapOfAuditLogs;

    }


}



