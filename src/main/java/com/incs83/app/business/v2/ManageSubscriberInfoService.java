package com.incs83.app.business.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.common.v2.FileRequest;
import com.incs83.app.common.v2.SubscriberInfoRequest;
import com.incs83.app.responsedto.v2.fileUpload.FileUploadDetails;
import com.incs83.app.responsedto.v2.subscriberInfo.SubscriberInfoResponse;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.config.MongoConfig;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.S3Service;
import com.incs83.service.SubscribersBulkUpdation;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

import static com.incs83.app.constants.misc.ActiontecConstants.BULK_UPLOAD_SUB_DIR;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.*;

@Service
public class ManageSubscriberInfoService {
    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private S3Service s3Service;

    @Autowired
    private MongoConfig mongoConfig;

    @Autowired
    private SubscribersBulkUpdation subscribersBulkUpdation;

    /*@Autowired
    private ESService esService;

    @Autowired
    private ESServiceImpl esServiceImpl;*/

    public List<FileUploadDetails> getFileUploadDetails() throws Exception {
        List<FileUploadDetails> fileUploadDetails = new ArrayList<>();
        List<DBObject> objectList;
        if (!CommonUtils.isSysAdmin()) {
            String isp = manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser());
            HashMap<String, Object> query = new HashMap<>();
            query.put("isp", isp);
            objectList = mongoService.findList(SUBSCRIBERS_DATA_FILE_DETAILS, query, TIMESTAMP, DESC);
        } else {
            objectList = mongoService.findList(SUBSCRIBERS_DATA_FILE_DETAILS, new HashMap<>(), TIMESTAMP, DESC);
        }
        objectList.forEach(q -> {
            FileUploadDetails uploadDetails = new FileUploadDetails();
            uploadDetails.setFileId(String.valueOf(q.get("id")));
            uploadDetails.setName(String.valueOf(q.get("name")));
            uploadDetails.setSize(String.valueOf(q.get("size")));
            uploadDetails.setUrl(String.valueOf(q.get("url")));
            uploadDetails.setUpdateOn(Objects.nonNull(q.get("timestamp")) ? (Long.valueOf(q.get("timestamp").toString())) : 0);
            Long failedRecordCount = countNoOfRecordFailed(uploadDetails.getFileId());
            Long pendingRecordCount = countNoOfRecordPending(Long.valueOf(q.get("noOfRecordUpdated").toString()), failedRecordCount, Long.valueOf(q.get("invalidRecord").toString()), Long.valueOf(q.get("totalRecord").toString()));
            Long totalRecord = Objects.nonNull(q.get("totalRecord")) ? Long.valueOf(q.get("totalRecord").toString()) : 0L;
            String status = getStatusOfFile(Long.valueOf(q.get("noOfRecordUpdated").toString()), failedRecordCount, Long.valueOf(q.get("invalidRecord").toString()), Long.valueOf(q.get("totalRecord").toString()), uploadDetails.getFileId(),String.valueOf(q.get("status")));
            uploadDetails.setStatus(status);
            uploadDetails.setFailedRecord(failedRecordCount);
            uploadDetails.setTotalRecord(totalRecord);
            uploadDetails.setPending(pendingRecordCount);
            uploadDetails.setInvalidRecord(Objects.nonNull(q.get("invalidRecord")) ? Long.valueOf(q.get("invalidRecord").toString()) : 0L);
            uploadDetails.setNoOfRecordUpdated(Objects.nonNull(q.get("noOfRecordUpdated")) ? Long.valueOf(q.get("noOfRecordUpdated").toString()) : 0L);
            uploadDetails.setTotalRecordProcessed(Objects.nonNull(q.get("noOfRecordScanned")) ? Long.valueOf(q.get("noOfRecordScanned").toString()) : 0L);
            uploadDetails.setUrlForInvalidRecord(String.valueOf(q.get("urlForInvalidRecord")));
            if (Objects.nonNull(q.get("processStarted")) && Objects.nonNull(q.get("processCompleted")))
                uploadDetails.setTimeTaken(manageCommonService.getTimeTakenToProcess((Long.valueOf(q.get("processCompleted").toString()) - Long.valueOf(q.get("processStarted").toString())) / 1000));
            else
                uploadDetails.setTimeTaken(HYPHEN_STRING);
            fileUploadDetails.add(uploadDetails);
        });

        return fileUploadDetails;
    }

    private String getStatusOfFile(Long recordsUpdated, Long failedRecords, Long invalidRecords, Long totalRecords, String fileId,String status) {
        if(!status.equals("NEW")) {
            if (!status.equals(_SUBSCRIBER_BULK_STATUS_COMPLETED)) {
                String processingStatus = _SUBSCRIBER_BULK_STATUS_INPROGRESS;
                BasicDBObject recordToUpdate = new BasicDBObject();
                BasicDBObject update = new BasicDBObject();
                if ((recordsUpdated + failedRecords + invalidRecords) == totalRecords)
                    processingStatus = _SUBSCRIBER_BULK_STATUS_COMPLETED;

                recordToUpdate.put("status", processingStatus);
                recordToUpdate.put("processCompleted", Calendar.getInstance().getTimeInMillis());

                update.put("$set", recordToUpdate);
                mongoService.update(new BasicDBObject("id", fileId), update, false, false, SUBSCRIBERS_DATA_FILE_DETAILS);
                return processingStatus;
            }else
                return _SUBSCRIBER_BULK_STATUS_COMPLETED;
        }else
            return "NEW";
    }

    private Long countNoOfRecordFailed(String fileId) {
        Long count = 0L;
        HashMap<String, Object> query = new HashMap<>();
        query.put("fileId", fileId);
        query.put("status", _SUBSCRIBER_BULK_STATUS_FAIL);
        count = mongoService.count(query, null, SUBSCRIBERS_DATA_UPDATION_RECORD);

        return count;
    }

    private Long countNoOfRecordPending(Long recordsUpdated, Long failedRecords, Long invalidRecords, Long totalRecords) {
        Long count = 0L;
        count = totalRecords - (recordsUpdated + failedRecords + invalidRecords);
        return count;
    }

    public void deleteFileFromS3Bucket(String fileId) throws Exception {
        HashMap<String, Object> query = new HashMap<>();
        query.put("id", fileId);
        DBObject file = mongoService.findOne(SUBSCRIBERS_DATA_FILE_DETAILS, query);
        if (Objects.isNull(file))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File details does not exist ");
        if (!CommonUtils.isSysAdmin() && Objects.nonNull(file.get("isp")) && !file.get("isp").toString().equals(manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser())))
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        if (!_SUBSCRIBER_BULK_STATUS_NEW.equals(String.valueOf(file.get("status")))) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File cannot be deleted.");
        }
        s3Service.deleteFile(BULK_UPLOAD_SUB_DIR + "/" + String.valueOf(file.get("name")));
        mongoService.deleteOne(query, SUBSCRIBERS_DATA_FILE_DETAILS);
    }

    public void processBulkSubscriberDetails(FileRequest fileRequest) throws Exception {
        HashMap<String, Object> query = new HashMap<>();
        query.put("id", fileRequest.getFileId());

        DBObject file = mongoService.findOne(SUBSCRIBERS_DATA_FILE_DETAILS, query);

        if (Objects.isNull(file))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File details does not exist ");

        if (!CommonUtils.isSysAdmin() && Objects.nonNull(file.get("isp")) && !file.get("isp").toString().equals(manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser())))
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);

        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("status", _SUBSCRIBER_BULK_STATUS_INPROGRESS);
        List inProgressFiles = mongoService.findList(SUBSCRIBERS_DATA_FILE_DETAILS, queryParam, TIMESTAMP, DESC);
        if (Objects.nonNull(inProgressFiles) && inProgressFiles.size() > ZERO) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Some files are already in progress. Please try after some time.");
        }

        manageCommonService.publishSubscriber(file);

        /*Runnable processBulkUpdationOfSubscribers = () -> {
            try {
                manageCommonService.publishSubscriber(file);
            } catch (Exception e) {
                LOG.error("Error during get tenant DB", e);
            }
        };
        BasicDBObject update = new BasicDBObject();
        update.put("$set", new BasicDBObject("status", _SUBSCRIBER_BULK_STATUS_INPROGRESS).append("processStarted", Calendar.getInstance().getTimeInMillis()));
        mongoService.update(new BasicDBObject("id", fileRequest.getFileId()), update, false, false, SUBSCRIBERS_DATA_FILE_DETAILS);
        new Thread(processBulkUpdationOfSubscribers).start();*/
    }

    public List<DBObject> getAllFailedRecordsForFile(String fileId) throws Exception {
        HashMap<String, Object> query = new HashMap<>();
        query.put("id", fileId);

        DBObject file = mongoService.findOne(SUBSCRIBERS_DATA_FILE_DETAILS, query);

        if (Objects.isNull(file))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "File details does not exist ");

        if (!CommonUtils.isSysAdmin() && Objects.nonNull(file.get("isp")) && !file.get("isp").toString().equals(manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser())))
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);

        query.clear();
        query.put("fileId", fileId);
        query.put("status", _SUBSCRIBER_BULK_STATUS_FAIL);

        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        List<DBObject> failedRecords = mongoService.findList(query, SUBSCRIBERS_DATA_UPDATION_RECORD, fieldsToRemove);
        return failedRecords;
    }


    public SubscriberInfoResponse getSubscriberFailedRecords(SubscriberInfoRequest subscriberInfoRequest) throws Exception {
        SubscriberInfoResponse subscriberInfoResponse = new SubscriberInfoResponse();

        HashMap<String, Object> query = new HashMap<>();
        query.put("id", subscriberInfoRequest.getFileId());

        DBObject file = mongoService.findOne(SUBSCRIBERS_DATA_FILE_DETAILS, query);
        if (Objects.isNull(file)) {
            subscriberInfoResponse.setTotalCount(0L);
            subscriberInfoResponse.setObjects(new ArrayList<>());

            return subscriberInfoResponse;
        }

        if (!CommonUtils.isSysAdmin() && Objects.nonNull(file.get("isp")) && !file.get("isp").toString().equals(manageCommonService.getIspByGroupId(CommonUtils.getGroupIdOfLoggedInUser())))
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);

        query.clear();
        query.put("fileId", subscriberInfoRequest.getFileId());
        query.put("status", _SUBSCRIBER_BULK_STATUS_FAIL);

        if (Objects.nonNull(subscriberInfoRequest.getSerialNumber()) && !subscriberInfoRequest.getSerialNumber().isEmpty())
            query.put("serialNumber", Pattern.compile(Pattern.quote(subscriberInfoRequest.getSerialNumber())));

        if (Objects.nonNull(subscriberInfoRequest.getMacAddress()) && !subscriberInfoRequest.getMacAddress().isEmpty())
            query.put("macAddress", Pattern.compile(Pattern.quote(subscriberInfoRequest.getMacAddress())));

        BasicDBObject projection = new BasicDBObject();
        projection.put("fileId", 1);
        projection.put("serialNumber", 1);
        projection.put("remark", 1);
        projection.put("macAddress", 1);
        projection.put("status", 1);

        List<BasicDBObject> objectList = mongoService.aggregationQueryForPagination(query, SUBSCRIBERS_DATA_UPDATION_RECORD, projection, subscriberInfoRequest.getSortBy(), DESC, subscriberInfoRequest.getMax(), subscriberInfoRequest.getOffset());
        if (Objects.isNull(objectList) || objectList.isEmpty()) {
            subscriberInfoResponse.setTotalCount(0L);
            subscriberInfoResponse.setObjects(new ArrayList<>());

            return subscriberInfoResponse;
        }

        subscriberInfoResponse.setTotalCount(countNoOfRecordFailed(subscriberInfoRequest.getFileId()));
        subscriberInfoResponse.setObjects(objectList);

        return subscriberInfoResponse;
    }
}
