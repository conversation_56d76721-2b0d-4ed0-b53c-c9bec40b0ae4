package com.incs83.app.business.v2;

import com.incs83.app.common.v2.VMQPaginationRequest;
import com.incs83.app.responsedto.v2.VMQResponse.VMQResponseDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.exceptions.handler.OAUTH2AuthenticationException;
import com.incs83.exceptions.handler.ValidationException;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.VMQ_METRICS;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;

@Service
public class ManageVMQMetricsService {
    @Autowired
    private MongoServiceImpl mongoService;

    public VMQResponseDTO getAllMetrics(VMQPaginationRequest vmqPaginationRequest) {

        Integer offset = 0 ;
        Integer max = 500 ;
        Long currentDate = System.currentTimeMillis();
        Long fromDate = currentDate - 86400000L ;
        Long toDate = System.currentTimeMillis();
        BasicDBObject queryParams = new BasicDBObject();

        if (Objects.nonNull(vmqPaginationRequest.getOffset())) {
            offset = vmqPaginationRequest.getOffset();
        }

        if (Objects.nonNull(vmqPaginationRequest.getMax())) {
            max = vmqPaginationRequest.getMax();
        }

        if (Objects.nonNull(vmqPaginationRequest.getFromDate())) {
            fromDate = vmqPaginationRequest.getFromDate();
        }
        if (Objects.nonNull(vmqPaginationRequest.getToDate())) {
            toDate = vmqPaginationRequest.getToDate();
        }

        if (currentDate < toDate) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " toDate should not be greater than current Date ");
        }
        if (currentDate < fromDate) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " fromDate should not be greater than current Date ");
        }
        if (fromDate > toDate) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), " fromDate should not be greater than toDate ");
        }

        queryParams.put("timestamp", new BasicDBObject("$gte", fromDate).append("$lte", toDate));

        if (Objects.nonNull(vmqPaginationRequest.getUserId()) && !vmqPaginationRequest.getUserId().isEmpty()) {
            queryParams.put("userId", vmqPaginationRequest.getUserId());
        }
        if (Objects.nonNull(vmqPaginationRequest.getVmqHook()) && !vmqPaginationRequest.getVmqHook().isEmpty()) {
            queryParams.put("vmqHook", vmqPaginationRequest.getVmqHook());
        }
        if (Objects.nonNull(vmqPaginationRequest.getEndpoint()) && !vmqPaginationRequest.getEndpoint().isEmpty()) {
            queryParams.put("endpoint", vmqPaginationRequest.getEndpoint());
        }
        if (Objects.nonNull(vmqPaginationRequest.getStatus()) && !vmqPaginationRequest.getStatus().isEmpty()) {
            queryParams.put("status", vmqPaginationRequest.getStatus());
        }
        if (Objects.nonNull(vmqPaginationRequest.getTopic()) && !vmqPaginationRequest.getTopic().isEmpty()) {
            queryParams.put("topic", vmqPaginationRequest.getTopic());
        }

        BasicDBObject projection = new BasicDBObject();
        projection.clear();
        projection.put("_id",0);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        List<DBObject> vmqMetricsList =mongoService.aggregationQueryForPagination(queryParams, VMQ_METRICS, projection, TIMESTAMP, DESC, max, offset);

        List<Map<String, Object>> combinedData = new ArrayList<>();
        if(Objects.nonNull(vmqMetricsList)&& !vmqMetricsList.isEmpty()){
            vmqMetricsList.forEach( vmqMetrics -> {
                String day =  String.valueOf(vmqMetrics.get("date"));
                Map<String, Object> dataByDay =  (Map<String, Object>) vmqMetrics.get(day);
                        if(Objects.nonNull(dataByDay)){
                            for( String byHour : dataByDay.keySet()) {
                             List<Map<String,Object>>  data = ((List<Map<String,Object>>)  dataByDay.get(byHour)).subList(0,500)  ;
                             if(combinedData.size() <= 500)
                              combinedData.addAll(data);
                 }
            }
            });
        }
        VMQResponseDTO vmqResponseDTO = new VMQResponseDTO();
        vmqResponseDTO.setObjects(combinedData);
        return vmqResponseDTO;
    }
}
