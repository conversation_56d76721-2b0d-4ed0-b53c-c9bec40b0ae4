package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.entities.Equipment;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.service.MongoService;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.incs83.app.constants.misc.ActiontecConstants.EQUIPMENT_DIAGNOSTIC_CONFIG;
import static com.incs83.app.constants.misc.ActiontecConstants.HOSTNAME_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.WAN_SPEED_TEST_RPC_POLL_COUNT;
import static com.incs83.app.constants.misc.ApplicationConstants.THREAD_TO_SLEEP;

@Service
public class DeviceInternetAccessService {
    private static final Logger logger = LogManager.getLogger(com.incs83.app.business.v2.DeviceInternetAccessService.class);

    @Value("${device.internetAccess.defaultBlockDurationInSec:900}")
    private long defaultBlockDuration = 900;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private RPCUtilityService rpcUtilityService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SimpleRpcService cpeRpcService;

    @Autowired
    private ObjectMapper objectMapper;

    public Object updateInternetAccess(String equipmentIdOrSerialOrSTN, String deviceMac, Map<String,Object> payloadMap) throws Exception {
        logger.debug("updateInternetAccess equipmentId:[{}] deviceMac:[{}] payloadMap:[{}]", equipmentIdOrSerialOrSTN, deviceMac, payloadMap);

        //0. get basic info
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (equipment.getRgwSerial().isEmpty()) {
            logger.error("updateInternetAccess equipment.rgwSerial is empty, equipmentId:[{}]", equipmentIdOrSerialOrSTN);
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "internal error");
        }

        //1. send rpc
        String userId = equipment.getRgwSerial();
        String serial = manageCommonService.getGatewaySerialByUserId(userId).orElseGet(()->userId);

        String rpcUri = "/cpe-api/devices/" + deviceMac + "/internetAccess/action";
        String action = ((String) payloadMap.get("action")).toUpperCase();
        if (StringUtils.equals(action, "BLOCK")) {
            if (payloadMap.get("blockDuration") == null) {
                logger.warn("no block duration");
                payloadMap.put("blockDuration", defaultBlockDuration);
            }
        }
        String payload = objectMapper.writeValueAsString(payloadMap);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userId, serial, rpcUri, "POST", payload, maxTries, THREAD_TO_SLEEP);

        //2. check rpc result and update mongo
        if (StringUtils.equals(String.valueOf(rpcResult.get("code")), "200")) {
            try {
                if (rpcResult.get("payload") != null) {
                    List<Object> respPayloadList = (List) rpcResult.get("payload");
                    Map<String, Object> respPayloadMap= (Map)respPayloadList.get(0);
                    logger.warn("internetAccessBlocked serial[{}] blocked[{}] blockStartTime[{}] blockDuration[{}]",
                            serial, respPayloadMap.get("internetAccessBlocked"), respPayloadMap.get("internetAccessStartTime"), respPayloadMap.get("internetAccessDuration"));
                }
            } catch (Exception e) {
                logger.error("updateInternetAccess error. serial:[{}]", serial, e);
            }

            DBObject queryDbObj = new BasicDBObject();
            queryDbObj.put("userId", userId);
            queryDbObj.put("serialNumber", serial);
            queryDbObj.put("macAddress", deviceMac);

            BasicDBObject dataToUpdate = new BasicDBObject();
            BasicDBObject updateDbObj = new BasicDBObject();
            updateDbObj.put("$set", dataToUpdate);

            //TODO: parse rpc result and fetch action from it.
            if (StringUtils.equals(action, "ALLOW")) {
                dataToUpdate.put("internetAccessBlocked", false);
                dataToUpdate.put("internetAccessBlockStartTime", 0);
                dataToUpdate.put("internetAccessBlockDuration", 0);
                mongoService.update(queryDbObj, updateDbObj, false, false, HOSTNAME_DETAIL);
            } else if (StringUtils.equals(action, "BLOCK")) {
                dataToUpdate.put("internetAccessBlocked", true);
                if (payloadMap.get("blockStartTime") != null) {
                    dataToUpdate.put("internetAccessBlockStartTime", payloadMap.get("blockStartTime"));
                }
                if (payloadMap.get("blockDuration") != null) {
                    dataToUpdate.put("internetAccessBlockDuration", payloadMap.get("blockDuration"));
                }
                mongoService.update(queryDbObj, updateDbObj, false, false, HOSTNAME_DETAIL);
            } else {
                // should not happen
                throw new ApiException("invalid action [" + action + "]");
            }

            return rpcResult.get("payload");
        } else {
            throw new ApiException("internet access rpc failed");
        }
    }

    public Object readInternetAccess(String equipmentIdOrSerialOrSTN, String deviceMac) throws Exception {
        logger.debug("readInternetAccess equipmentId:[{}] deviceMac:[{}]", equipmentIdOrSerialOrSTN, deviceMac);

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (equipment.getRgwSerial().isEmpty()) {
            logger.error("putSteering equipment.rgwSerial is empty, equipmentId:[{}]", equipmentIdOrSerialOrSTN);
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "internal error");
        }

        String userId = equipment.getRgwSerial();
        String serial = manageCommonService.getGatewaySerialByUserId(userId).orElseGet(()->userId);
        String rpcUri = "/cpe-api/devices/" + deviceMac + "/internetAccess";
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userId, serial, rpcUri, "GET", "\"\"", maxTries, THREAD_TO_SLEEP);

        if (rpcResult.get("payload") != null) {
            List<Object> respPayloadList = (List) rpcResult.get("payload");
            Map<String, Object> respPayloadMap= (Map)respPayloadList.get(0);
            return respPayloadMap;
        } else {
            return null;
        }
    }

}
