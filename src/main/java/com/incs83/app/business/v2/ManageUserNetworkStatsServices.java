//package com.incs83.app.business.v2;
//
//import com.incs83.app.entities.Equipment;
//import com.incs83.app.responsedto.v2.NetworkStats.*;
//import com.incs83.app.service.data.MongoServiceImpl;
//import com.incs83.app.utils.CalendarUtils;
//import com.incs83.app.utils.ValidationUtil;
//import com.incs83.dto.ApiResponseDTO;
//import com.incs83.util.CommonUtils;
//import com.mongodb.BasicDBObject;
//import com.mongodb.DBObject;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//
//import static com.incs83.app.constants.misc.ActiontecConstants.*;
//import static com.incs83.app.constants.misc.ApplicationConstants.*;
//
//@Service
//public class ManageUserNetworkStatsServices {
//
//    @Autowired
//    private MongoServiceImpl mongoService;
//
//    @Autowired
//    private ManageCommonService manageCommonService;
//
//
//    public CommonDistDTO getUploadSpeedDist(String serialNumberOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        List<String> userIds = new ArrayList<>();
//        userIds.add(userEquipment.getRgwSerial());
//
//        HashMap<String, String> aggregationParams = new HashMap<>();
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//
//        DBObject wifiUlDistToday;
//        DBObject wifiUlDistLast7Days;
//        DBObject wifiUllDistLast30Days;
//        DBObject wifiUllDistLast90Days;
//        String outputParams;
//
//        aggregationParams.clear();
//        aggregationParams.put("outputParams", "Poor,Fair,Good,Very Good,Excellent");
//        aggregationParams.put("label", "wifiUlSpeedDist");
//        aggregationParams.put("operation", "$avg");
//        aggregationParams.put("keyToAggregate", "$lt6,$gte6lt12,$gte12lt30,$gte30lt60,$gte60");
//        aggregationParams.put("operand", "$gte");
//
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//        HashMap<String, Object> inParams = new HashMap<>();
//        HashMap<String, Object> inClause = new HashMap<>();
//        inClause.put("resourceName", "userId");
//        inClause.put("resource", userIds);
//        inParams.put("in", inClause);
//
//        wifiUlDistToday = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_UPLOAD_SPEED_DIST_PER_HOUR, CalendarUtils.getCalendarInstanceForToday(), aggregationParams, DATE);
//        wifiUlDistLast7Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_UPLOAD_SPEED_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_7_DAYS), aggregationParams, DATE);
//        wifiUllDistLast30Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_UPLOAD_SPEED_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_30_DAYS), aggregationParams, DATE);
//        wifiUllDistLast90Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_UPLOAD_SPEED_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_90_DAYS), aggregationParams, DATE);
//
//        outputParams = "Poor,Fair,Good,Very Good,Excellent";
//        List<HashMap<String, Object>> uploadSpeedDistList = mongoService.generateHistoricalGraphDataFromData(outputParams, wifiUlDistToday, wifiUlDistLast7Days, wifiUllDistLast30Days, wifiUllDistLast90Days);
//        CommonDistDTO commonDistDTO = new CommonDistDTO();
//        ArrayList<CommonDistDTO.CommonDistData> commonDistList = new ArrayList<>();
//        for (HashMap<String, Object> uploadSpeedData : uploadSpeedDistList) {
//            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
//            commonDist.setDay(String.valueOf(uploadSpeedData.get("Day")));
//            commonDist.setExcellent(Double.valueOf(Objects.isNull(uploadSpeedData.get("Excellent")) ? "0.0" : uploadSpeedData.get("Excellent").toString()));
//            commonDist.setFair(Double.valueOf(Objects.isNull(uploadSpeedData.get("Fair")) ? "0.0" : uploadSpeedData.get("Fair").toString()));
//            commonDist.setGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Good")) ? "0.0" : uploadSpeedData.get("Good").toString()));
//            commonDist.setPoor(Double.valueOf(Objects.isNull(uploadSpeedData.get("Poor")) ? "0.0" : uploadSpeedData.get("Poor").toString()));
//            commonDist.setVeryGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Very Good")) ? "0.0" : uploadSpeedData.get("Very Good").toString()));
//            commonDistList.add(commonDist);
//        }
//
//        if (commonDistList.isEmpty()) {
//            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
//            commonDistList.add(commonDist);
//        }
//
//        commonDistDTO.setData(commonDistList);
//
//        return commonDistDTO;
//    }
//
//    public CommonDistDTO getDownloadSpeedDist(String serialNumberOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        List<String> userIds = new ArrayList<>();
//        userIds.add(userEquipment.getRgwSerial());
//
//        HashMap<String, String> aggregationParams = new HashMap<>();
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//
//        DBObject wifiDlDistToday;
//        DBObject wifiDlDistLast7Days;
//        DBObject wifiDllDistLast30Days;
//        DBObject wifiDllDistLast90Days;
//        String outputParams;
//
//        aggregationParams.clear();
//        aggregationParams.put("outputParams", "Poor,Fair,Good,Very Good,Excellent");
//        aggregationParams.put("label", "wifiDlSpeedDist");
//        aggregationParams.put("operation", "$avg");
//        aggregationParams.put("keyToAggregate", "$lt50,$gte50lt100,$gte100lt250,$gte250lt500,$gte500");
//        aggregationParams.put("operand", "$gte");
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//        HashMap<String, Object> inParams = new HashMap<>();
//        HashMap<String, Object> inClause = new HashMap<>();
//        inClause.put("resourceName", "userId");
//        inClause.put("resource", userIds);
//        inParams.put("in", inClause);
//
//        wifiDlDistToday = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_DOWNLOAD_SPEED_DIST_PER_HOUR, CalendarUtils.getCalendarInstanceForToday(), aggregationParams, DATE);
//        wifiDlDistLast7Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_DOWNLOAD_SPEED_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_7_DAYS), aggregationParams, DATE);
//        wifiDllDistLast30Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_DOWNLOAD_SPEED_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_30_DAYS), aggregationParams, DATE);
//        wifiDllDistLast90Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_DOWNLOAD_SPEED_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_90_DAYS), aggregationParams, DATE);
//
//        outputParams = "Poor,Fair,Good,Very Good,Excellent";
//        List<HashMap<String, Object>> uploadSpeedDistList = mongoService.generateHistoricalGraphDataFromData(outputParams, wifiDlDistToday, wifiDlDistLast7Days, wifiDllDistLast30Days, wifiDllDistLast90Days);
//        CommonDistDTO commonDistDTO = new CommonDistDTO();
//        ArrayList<CommonDistDTO.CommonDistData> commonDistList = new ArrayList<>();
//        for (HashMap<String, Object> uploadSpeedData : uploadSpeedDistList) {
//            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
//            commonDist.setDay(String.valueOf(uploadSpeedData.get("Day")));
//            commonDist.setExcellent(Double.valueOf(Objects.isNull(uploadSpeedData.get("Excellent")) ? "0.0" : uploadSpeedData.get("Excellent").toString()));
//            commonDist.setFair(Double.valueOf(Objects.isNull(uploadSpeedData.get("Fair")) ? "0.0" : uploadSpeedData.get("Fair").toString()));
//            commonDist.setGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Good")) ? "0.0" : uploadSpeedData.get("Good").toString()));
//            commonDist.setPoor(Double.valueOf(Objects.isNull(uploadSpeedData.get("Poor")) ? "0.0" : uploadSpeedData.get("Poor").toString()));
//            commonDist.setVeryGood(Double.valueOf(Objects.isNull(uploadSpeedData.get("Very Good")) ? "0.0" : uploadSpeedData.get("Very Good").toString()));
//            commonDistList.add(commonDist);
//        }
//
//        if (commonDistList.isEmpty()) {
//            CommonDistDTO.CommonDistData commonDist = commonDistDTO.new CommonDistData();
//            commonDistList.add(commonDist);
//        }
//        commonDistDTO.setData(commonDistList);
//
//        return commonDistDTO;
//    }
//
//    public HealthDistDTO getHealthDist(String serialNumberOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEqipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEqipment);
//        manageCommonService.subscriberGroupMisMatch(userEqipment);
//
//        List<String> userIds = new ArrayList<>();
//        userIds.add(userEqipment.getRgwSerial());
//
//        HashMap<String, String> aggregationParams = new HashMap<>();
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//        String outputParams;
//
//        DBObject wifiHealthDistToday = null;
//        DBObject wifiHealthDistLast7Days = null;
//        DBObject wifiHealthDistLast30Days = null;
//        DBObject wifiHealthDistLast90Days = null;
//
//        aggregationParams.clear();
//        aggregationParams.put("outputParams", "Lt 60,Lt 70,Lt 80,Lt 90,Gt 90");
//        aggregationParams.put("label", "wifiHealthDist");
//        aggregationParams.put("operation", "$avg");
//        aggregationParams.put("keyToAggregate", "$lt60,$gte60lt70,$gte70lt80,$gte80lt90,$gte90");
//        aggregationParams.put("operand", "$gte");
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//        HashMap<String, Object> inParams = new HashMap<>();
//        HashMap<String, Object> inClause = new HashMap<>();
//        inClause.put("resourceName", "userId");
//        inClause.put("resource", userIds);
//        inParams.put("in", inClause);
//
//        wifiHealthDistToday = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_HEALTH_SCORE_DIST_PER_HOUR, CalendarUtils.getCalendarInstanceForToday(), aggregationParams, DATE);
//        wifiHealthDistLast7Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_HEALTH_SCORE_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_7_DAYS), aggregationParams, DATE);
//        wifiHealthDistLast30Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_HEALTH_SCORE_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_30_DAYS), aggregationParams, DATE);
//        wifiHealthDistLast90Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_HEALTH_SCORE_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_90_DAYS), aggregationParams, DATE);
//
//        outputParams = "Lt 60,Lt 70,Lt 80,Lt 90,Gt 90";
//        HealthDistDTO healthDistDTO = new HealthDistDTO();
//        ArrayList<HealthDistDTO.HealthDistData> healthDistDataList = new ArrayList<>();
//        List<HashMap<String, Object>> wifiHealthDistList = mongoService.generateHistoricalGraphDataFromData(outputParams, wifiHealthDistToday, wifiHealthDistLast7Days, wifiHealthDistLast30Days, wifiHealthDistLast90Days);
//        for (HashMap<String, Object> healthDistData : wifiHealthDistList) {
//            HealthDistDTO.HealthDistData healthDist = healthDistDTO.new HealthDistData();
//            healthDist.setDay(String.valueOf(healthDistData.get("Day")));
//            healthDist.setLt60(Double.valueOf(Objects.isNull(healthDistData.get("Lt 60")) ? "0.0" : healthDistData.get("Lt 60").toString()));
//            healthDist.setLt70(Double.valueOf(Objects.isNull(healthDistData.get("Lt 70")) ? "0.0" : healthDistData.get("Lt 70").toString()));
//            healthDist.setLt80(Double.valueOf(Objects.isNull(healthDistData.get("Lt 80")) ? "0.0" : healthDistData.get("Lt 80").toString()));
//            healthDist.setLt90(Double.valueOf(Objects.isNull(healthDistData.get("Lt 90")) ? "0.0" : healthDistData.get("Lt 90").toString()));
//            healthDist.setGt90(Double.valueOf(Objects.isNull(healthDistData.get("Gt 90")) ? "0.0" : healthDistData.get("Gt 90").toString()));
//            healthDistDataList.add(healthDist);
//        }
//
//        if (healthDistDataList.isEmpty()) {
//            HealthDistDTO.HealthDistData healthDist = healthDistDTO.new HealthDistData();
//            healthDistDataList.add(healthDist);
//        }
//
//        healthDistDTO.setData(healthDistDataList);
//
//        return healthDistDTO;
//    }
//
//    public AirTimeUtlzDist getAirTimeUtilizationDist(String serialNumberOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        List<String> userIds = new ArrayList<>();
//        userIds.add(userEquipment.getRgwSerial());
//
//        HashMap<String, String> aggregationParams = new HashMap<>();
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//        String outputParams;
//
//        DBObject airTimeUtilizationDistToday;
//        DBObject airTimeUtilizationDistLast7Days;
//        DBObject airTimeUtilizationDistLast30Days;
//        DBObject airTimeUtilizationDistLast90Days;
//
//        aggregationParams.clear();
//        aggregationParams.put("outputParams", "Very Low,Low,Medium,High,Very High");
//        aggregationParams.put("label", "airTimeUtilizationDist");
//        aggregationParams.put("operation", "$avg");
//        aggregationParams.put("keyToAggregate", "$lt20,$gte20lt40,$gte40lt60,$gte60lt80,$gte80");
//        aggregationParams.put("operand", "$gte");
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//        HashMap<String, Object> inParams = new HashMap<>();
//        HashMap<String, Object> inClause = new HashMap<>();
//        inClause.put("resourceName", "userId");
//        inClause.put("resource", userIds);
//        inParams.put("in", inClause);
//
//        airTimeUtilizationDistToday = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_AIR_TIME_UTILIZATION_DIST_PER_HOUR, CalendarUtils.getCalendarInstanceForToday(), aggregationParams, DATE);
//        airTimeUtilizationDistLast7Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_AIR_TIME_UTILIZATION_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_7_DAYS), aggregationParams, DATE);
//        airTimeUtilizationDistLast30Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_AIR_TIME_UTILIZATION_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_30_DAYS), aggregationParams, DATE);
//        airTimeUtilizationDistLast90Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_AIR_TIME_UTILIZATION_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_90_DAYS), aggregationParams, DATE);
//
//        outputParams = "Very Low,Low,Medium,High,Very High";
//        AirTimeUtlzDist airTimeUtlzDist = new AirTimeUtlzDist();
//        ArrayList<AirTimeUtlzDist.AirTimeUtlzDistData> airTimeUtlzDistDataList = new ArrayList<>();
//        List<HashMap<String, Object>> airTimeUtlzList = mongoService.generateHistoricalGraphDataFromData(outputParams, airTimeUtilizationDistToday, airTimeUtilizationDistLast7Days, airTimeUtilizationDistLast30Days, airTimeUtilizationDistLast90Days);
//        for (HashMap<String, Object> airTimeUtlz : airTimeUtlzList) {
//            AirTimeUtlzDist.AirTimeUtlzDistData airTimeUtlzDistData = airTimeUtlzDist.new AirTimeUtlzDistData();
//            airTimeUtlzDistData.setDay(String.valueOf(airTimeUtlz.get("Day")));
//            airTimeUtlzDistData.setHigh(Double.valueOf(Objects.isNull(airTimeUtlz.get("High")) ? "0.0" : airTimeUtlz.get("High").toString()));
//            airTimeUtlzDistData.setVeryHigh(Double.valueOf(Objects.isNull(airTimeUtlz.get("Very High")) ? "0.0" : airTimeUtlz.get("Very High").toString()));
//            airTimeUtlzDistData.setLow(Double.valueOf(Objects.isNull(airTimeUtlz.get("Low")) ? "0.0" : airTimeUtlz.get("Low").toString()));
//            airTimeUtlzDistData.setVeryLow(Double.valueOf(Objects.isNull(airTimeUtlz.get("Very Low")) ? "0.0" : airTimeUtlz.get("Very Low").toString()));
//            airTimeUtlzDistData.setMedium(Double.valueOf(Objects.isNull(airTimeUtlz.get("Medium")) ? "0.0" : airTimeUtlz.get("Medium").toString()));
//
//            airTimeUtlzDistDataList.add(airTimeUtlzDistData);
//        }
//        if (airTimeUtlzDistDataList.isEmpty()) {
//            AirTimeUtlzDist.AirTimeUtlzDistData airTimeUtlzDistData = airTimeUtlzDist.new AirTimeUtlzDistData();
//            airTimeUtlzDistDataList.add(airTimeUtlzDistData);
//        }
//
//        airTimeUtlzDist.setData(airTimeUtlzDistDataList);
//        return airTimeUtlzDist;
//    }
//
//    public SignalStrengthDistDTO getSignalStrengthDist(String serialNumberOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        List<String> userIds = new ArrayList<>();
//        userIds.add(userEquipment.getRgwSerial());
//
//        HashMap<String, String> aggregationParams = new HashMap<>();
//        String outputParams;
//
//        DBObject wifiSignalStrengthDistToday = null;
//        DBObject wifiSignalStrengthLast7Days = null;
//        DBObject wifiSignalStrengthLast30Days = null;
//        DBObject wifiSignalStrengthLast90Days = null;
//
//        aggregationParams.clear();
//        aggregationParams.put("outputParams", "Poor,Good,Very Good,Excellent");
//        aggregationParams.put("label", "wifiSignalStrength");
//        aggregationParams.put("operation", "$avg");
//        aggregationParams.put("keyToAggregate", "$ltMin80,$gteMin80ltMin60,$gteMin60ltMin40,$gtMin40");
//        aggregationParams.put("operand", "$gte");
//
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//        HashMap<String, Object> inParams = new HashMap<>();
//        HashMap<String, Object> inClause = new HashMap<>();
//        inClause.put("resourceName", "userId");
//        inClause.put("resource", userIds);
//        inParams.put("in", inClause);
//
//        wifiSignalStrengthDistToday = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_SIGNAL_STRENGTH_DIST_PER_HOUR, CalendarUtils.getCalendarInstanceForToday(), aggregationParams, DATE);
//        wifiSignalStrengthLast7Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_SIGNAL_STRENGTH_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_7_DAYS), aggregationParams, DATE);
//        wifiSignalStrengthLast30Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_SIGNAL_STRENGTH_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_30_DAYS), aggregationParams, DATE);
//        wifiSignalStrengthLast90Days = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_SIGNAL_STRENGTH_DIST_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_90_DAYS), aggregationParams, DATE);
//
//        outputParams = "Poor,Good,Very Good,Excellent";
//        List<HashMap<String, Object>> wifiSignalStrengthList = mongoService.generateHistoricalGraphDataFromData(outputParams, wifiSignalStrengthDistToday, wifiSignalStrengthLast7Days, wifiSignalStrengthLast30Days, wifiSignalStrengthLast90Days);
//        SignalStrengthDistDTO signalStrengthDistDTO = new SignalStrengthDistDTO();
//        ArrayList<SignalStrengthDistDTO.SignalStrength> signalStrengthsList = new ArrayList<>();
//        for (HashMap<String, Object> signalStrength : wifiSignalStrengthList) {
//            SignalStrengthDistDTO.SignalStrength signalStrengthData = signalStrengthDistDTO.new SignalStrength();
//            signalStrengthData.setDay(String.valueOf(signalStrength.get("Day")));
//            signalStrengthData.setExcellent(Double.valueOf(Objects.isNull(signalStrength.get("Excellent")) ? "0.0" : signalStrength.get("Excellent").toString()));
//            signalStrengthData.setGood(Double.valueOf(Objects.isNull(signalStrength.get("Good")) ? "0.0" : signalStrength.get("Good").toString()));
//            signalStrengthData.setPoor(Double.valueOf(Objects.isNull(signalStrength.get("Poor")) ? "0.0" : signalStrength.get("Poor").toString()));
//            signalStrengthData.setVeryGood(Double.valueOf(Objects.isNull(signalStrength.get("Very Good")) ? "0.0" : signalStrength.get("Very Good").toString()));
//            signalStrengthsList.add(signalStrengthData);
//        }
//
//        if (signalStrengthsList.isEmpty()) {
//            SignalStrengthDistDTO.SignalStrength signalStrengthData = signalStrengthDistDTO.new SignalStrength();
//            signalStrengthsList.add(signalStrengthData);
//        }
//
//        signalStrengthDistDTO.setData(signalStrengthsList);
//
//        return signalStrengthDistDTO;
//    }
//
//    public ApiResponseDTO getClumnChartData(String serialNumberOrRGWMAC, String type) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEqipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEqipment);
//        manageCommonService.subscriberGroupMisMatch(userEqipment);
//
//        List<String> userIds = new ArrayList<>();
//        userIds.add(userEqipment.getRgwSerial());
//
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//        HashMap<String, String> aggregationParams = new HashMap<>();
//
//        aggregationParams.clear();
//        aggregationParams.put("outputParams", "sumScore,sumUserCount,sumUlSpeed,sumDlSpeed,sumStaCount,sumRadioOccupancy24G,sumRadioOccupancy5G");
//        aggregationParams.put("label", "columnChartData");
//        aggregationParams.put("operation", "$sum");
//        aggregationParams.put("keyToAggregate", "$score,$userCount,$lastDataUplinkRate,$lastDataDownlinkRate,$staCount,$radioOccupancy24g,$radioOccupancy5g");
//        aggregationParams.put("operand", "$gte");
//
//        DBObject columnChartDataToday;
//        DBObject columnChartData7DaysObject;
//        DBObject columnChartData30DaysObject;
//        DBObject columnChartData90DaysObject;
//
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//        HashMap<String, Object> inParams = new HashMap<>();
//        HashMap<String, Object> inClause = new HashMap<>();
//        inClause.put("resourceName", "userId");
//        inClause.put("resource", userIds);
//        inParams.put("in", inClause);
//
//        columnChartDataToday = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_INSIGHTS_PER_HOUR, CalendarUtils.getCalendarInstanceForToday(), aggregationParams, DATE);
//        columnChartData7DaysObject = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_INSIGHTS_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_7_DAYS), aggregationParams, DATE);
//        columnChartData30DaysObject = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_INSIGHTS_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_30_DAYS), aggregationParams, DATE);
//        columnChartData90DaysObject = mongoService.aggregateDataForAllOrResourceId(null, inParams, USER_WIFI_INSIGHTS_PER_DAY, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_90_DAYS), aggregationParams, DATE);
//
//        Map<String, Double> chartDataToday = manageCommonService.convertDBObjectToChartData(columnChartDataToday);
//        Map<String, Double> chartDataLast7Days = manageCommonService.convertDBObjectToChartData(columnChartData7DaysObject);
//        Map<String, Double> chartDataLast30Days = manageCommonService.convertDBObjectToChartData(columnChartData30DaysObject);
//        Map<String, Double> chartDataLast90Days = manageCommonService.convertDBObjectToChartData(columnChartData90DaysObject);
//
//        String outputParams = "";
//
//        if (type.equals(wifiDlSpeed)) {
//            DlSpeedDTO dlSpeedDTO = new DlSpeedDTO();
//            ArrayList<DlSpeedDTO.DlSpeedData> DlSpeedDataList = new ArrayList<>();
//            outputParams = "avgDlSpeed";
//            List<HashMap<String, Object>> dlSpeedList = mongoService.generateHistoricalGraphDataFromDataForColumnChart(outputParams, chartDataToday.get("wifiDl"), chartDataLast7Days.get("wifiDl"), chartDataLast30Days.get("wifiDl"), chartDataLast90Days.get("wifiDl"));
//            for (HashMap<String, Object> dlSpeed : dlSpeedList) {
//                DlSpeedDTO.DlSpeedData speedData = dlSpeedDTO.new DlSpeedData();
//                speedData.setDay(String.valueOf(dlSpeed.get("Day")));
//                speedData.setAvgDlSpeed(Double.valueOf(Objects.isNull(dlSpeed.get("avgDlSpeed")) ? "0.0" : dlSpeed.get("avgDlSpeed").toString()));
//                DlSpeedDataList.add(speedData);
//            }
//            if (DlSpeedDataList.isEmpty()) {
//                DlSpeedDTO.DlSpeedData speedData = dlSpeedDTO.new DlSpeedData();
//                DlSpeedDataList.add(speedData);
//            }
//            dlSpeedDTO.setData(DlSpeedDataList);
//
//            return dlSpeedDTO;
//        }
//
//        if (type.equals(wifiUlSpeed)) {
//            UlSpeedDTO ulSpeedDTO = new UlSpeedDTO();
//            ArrayList<UlSpeedDTO.UlSpeedData> ulSpeedDataList = new ArrayList<>();
//
//            outputParams = "avgUlSpeed";
//            List<HashMap<String, Object>> ulSpeedList = mongoService.generateHistoricalGraphDataFromDataForColumnChart(outputParams, chartDataToday.get("wifiUl"), chartDataLast7Days.get("wifiUl"), chartDataLast30Days.get("wifiUl"), chartDataLast90Days.get("wifiUl"));
//            for (HashMap<String, Object> ulSpeed : ulSpeedList) {
//                UlSpeedDTO.UlSpeedData ulSpeedData = ulSpeedDTO.new UlSpeedData();
//                ulSpeedData.setAvgUlSpeed(Double.valueOf(Objects.isNull(ulSpeed.get("avgUlSpeed")) ? "0.0" : ulSpeed.get("avgUlSpeed").toString()));
//                ulSpeedData.setDay(String.valueOf(ulSpeed.get("Day")));
//                ulSpeedDataList.add(ulSpeedData);
//            }
//
//            if (ulSpeedDataList.isEmpty()) {
//                UlSpeedDTO.UlSpeedData ulSpeedData = ulSpeedDTO.new UlSpeedData();
//                ulSpeedDataList.add(ulSpeedData);
//            }
//            ulSpeedDTO.setData(ulSpeedDataList);
//
//            return ulSpeedDTO;
//        }
//
//        if (type.equals(wifiHealth)) {
//            HealthDTO healthDTO = new HealthDTO();
//            ArrayList<HealthDTO.HealthData> healthDataList = new ArrayList<>();
//            outputParams = "avgWifiHealth";
//            List<HashMap<String, Object>> wifiHealthList = mongoService.generateHistoricalGraphDataFromDataForColumnChart(outputParams, chartDataToday.get("wifiHealth"), chartDataLast7Days.get("wifiHealth"), chartDataLast30Days.get("wifiHealth"), chartDataLast90Days.get("wifiHealth"));
//            for (HashMap<String, Object> wifiHealth : wifiHealthList) {
//                HealthDTO.HealthData healthData = healthDTO.new HealthData();
//                healthData.setAvgWifiHealth(Double.valueOf(Objects.isNull(wifiHealth.get("avgWifiHealth")) ? "0.0" : wifiHealth.get("avgWifiHealth").toString()));
//                healthData.setDay(String.valueOf(wifiHealth.get("Day")));
//                healthDataList.add(healthData);
//            }
//            if (healthDataList.isEmpty()) {
//                HealthDTO.HealthData healthData = healthDTO.new HealthData();
//                healthDataList.add(healthData);
//            }
//            healthDTO.setData(healthDataList);
//
//            return healthDTO;
//        }
//
//        if (type.equals(wifiAirTimeUtlz)) {
//            AirTimeUtlzDTO airTimeUtlzDTO = new AirTimeUtlzDTO();
//            ArrayList<AirTimeUtlzDTO.AirTimeUtlzData> airTimeUtlzDataList = new ArrayList<>();
//            outputParams = "avgRadioOccupancy5G,avgRadioOccupancy24G";
//            List<HashMap<String, Object>> airTimeUtlzList = manageCommonService.prepareAirTimeUtzChartData(outputParams, chartDataToday, chartDataLast7Days, chartDataLast30Days, chartDataLast90Days);
//            for (HashMap<String, Object> airTimeUtlz : airTimeUtlzList) {
//                AirTimeUtlzDTO.AirTimeUtlzData airTimeUtlzData = airTimeUtlzDTO.new AirTimeUtlzData();
//                airTimeUtlzData.setAvgRadioOccupancy5G(Double.valueOf(Objects.isNull(airTimeUtlz.get("avgRadioOccupancy5G")) ? "0.0" : airTimeUtlz.get("avgRadioOccupancy5G").toString()));
//                airTimeUtlzData.setAvgRadioOccupancy24G(Double.valueOf(Objects.isNull(airTimeUtlz.get("avgRadioOccupancy24G")) ? "0.0" : airTimeUtlz.get("avgRadioOccupancy24G").toString()));
//                airTimeUtlzData.setDay(String.valueOf(airTimeUtlz.get("Day")));
//
//                airTimeUtlzDataList.add(airTimeUtlzData);
//            }
//            if (airTimeUtlzDataList.isEmpty()) {
//                AirTimeUtlzDTO.AirTimeUtlzData airTimeUtlzData = airTimeUtlzDTO.new AirTimeUtlzData();
//                airTimeUtlzDataList.add(airTimeUtlzData);
//            }
//            airTimeUtlzDTO.setData(airTimeUtlzDataList);
//
//            return airTimeUtlzDTO;
//        }
//        return new ApiResponseDTO();
//    }
//}
