package com.incs83.app.business.v2;

import com.actiontec.optim.service.CpeRpcService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.common.v2.AgentVersion;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.entities.Equipment;
import com.incs83.app.responsedto.v2.Equipment.DiagonosticsRPCResponseDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.EquipmentUtils;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.pubsub.KafkaPublisher;
import com.incs83.pubsub.MQTTService;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.RPC_RESULT;
import static com.incs83.app.constants.misc.ApplicationConstants.TIME_OUT;

/**
 * Created By AMAN on Monday, 11-February-2019
 */
@Service
public class RPCUtilityService {

    private static final Logger LOG = LogManager.getLogger("org");
    @Value("${kafka.rpcTopic}")
    String rpcTopic;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private KafkaPublisher kafkaPublisher;
    @Autowired
    private MQTTService mqttService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ObjectMapper mapper ;
    @Autowired
    private SimpleRpcService cpeRpcService;
    @Autowired
    private EquipmentUtils equipmentUtils;

    private void sendSetBurstModeRPC(String userId, String serial, Long burstDuration, Long collectionInterval, Long reportInterval) throws Exception {
        String rpcUri = RpcConstants.BURST_MODE_URI;
        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("burstDuration", burstDuration);
        payloadMap.put("collectionInterval", collectionInterval);
        payloadMap.put("reportInterval", reportInterval);
        String payload = mapper.writeValueAsString(payloadMap);

        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userId, serial, rpcUri, "PUT", payload, RPC_DEFAULT_MAX_TRIES, RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
        if (!String.valueOf(rpcResult.get("code")).equals("200")) {
            LOG.error("response: {}", rpcResult.toString());
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, rpcUri + " RPC Failed");
        }
    }

    public void publishToTopic(HashMap<String, String> publishParams, String pubMsgTemplate, String topicTemplate, Integer pollCount) {
       LOG.info( "publishParams => "+publishParams + "  \n pubmsgtemplate  => " + pubMsgTemplate + " \n topicTemplate => " + topicTemplate );
        if (!"RPC_REQUEST".equals(rpcTopic)) {
            mqttService.publishToMqtt(publishParams, pubMsgTemplate, topicTemplate);
            return;
        }
        try {
            Map.Entry entry;
            for (Iterator param = publishParams.entrySet().iterator(); param.hasNext(); topicTemplate = topicTemplate.replaceAll((String) entry.getKey(), (String) entry.getValue())) {
                entry = (Map.Entry) param.next();
                if (Objects.nonNull(entry.getValue())) {
                    pubMsgTemplate = pubMsgTemplate.replaceAll((String) entry.getKey(), ((String) entry.getValue()).replaceAll("\\$", "\\\\\\$").replaceAll(Pattern.quote("\\\\"), Matcher.quoteReplacement("\\\\\\\\")).replaceAll("\"", "\\\\\""));
                } else {
                    pubMsgTemplate = pubMsgTemplate.replaceAll((String) entry.getKey(), (String) entry.getValue());
                }
            }
            LOG.info(" topic for kafka ==> " + topicTemplate);
            HashMap<String, Object> dataToPublish = new HashMap<>();
            dataToPublish.put("message", pubMsgTemplate);
            dataToPublish.put("topic", topicTemplate);
            dataToPublish.put("timeout", pollCount);
            dataToPublish.put("sugActType", "rpcRequest");
            kafkaPublisher.publishToKafka(dataToPublish, rpcTopic);
            LOG.info(" published successfullly ");
        } catch (Exception e) {
            LOG.error("Error while preparing data for RPC publish to kafka : " + e.getMessage());
        }
    }

    public HashMap<String,Object> registrationPutRPC(HashMap<String, Object> publishParams, String pubMsgTemplate) {
        HashMap<String, Object> obj = null;
        try {
            Map.Entry entry;

            for(Iterator param = publishParams.entrySet().iterator(); param.hasNext() ; ) {
                entry = (Map.Entry)param.next();
                if (Objects.nonNull(entry.getValue())) {
                    pubMsgTemplate = pubMsgTemplate.replaceAll((String)entry.getKey(), ((String)entry.getValue()).replaceAll("\\$", "\\\\\\$").replaceAll(Pattern.quote("\\\\"), Matcher.quoteReplacement("\\\\\\\\")).replaceAll("\"", "\\\\\""));
                } else {
                    pubMsgTemplate = pubMsgTemplate.replaceAll((String)entry.getKey(), (String)entry.getValue());
                }
            }

            obj =  mapper.readValue(pubMsgTemplate,HashMap.class );

        } catch (Exception e) {
            LOG.error(" Error while converting the String into hashMap Object : " + e.getMessage());
        }

        return obj ;
    }

    public DiagonosticsRPCResponseDTO sendRPC(HashMap<String, Object> input) {
        String userId = String.valueOf(input.get("userId"));
        String serialNumber = String.valueOf(input.get("serialNumber"));
        String type = String.valueOf(input.get("type"));
        boolean diagnosEnabled = (boolean) input.get("diagnosEnabled");
        long duration = 0L;
        if (diagnosEnabled)
            duration = Long.valueOf(String.valueOf(input.get("duration")));
        long colInterval = Long.valueOf(String.valueOf(input.get("colInterval")));
        long repInterval = Long.valueOf(String.valueOf(input.get("repInterval")));

        DiagonosticsRPCResponseDTO responseDTO = new DiagonosticsRPCResponseDTO();
        responseDTO.setUserId(userId);
        responseDTO.setSerialNumber(serialNumber);
        responseDTO.setType(type);

        AgentVersion agentVersion = equipmentUtils.getAgentVersion(userId, serialNumber);

        try {
            if(agentVersion.getMajorVersion() > 3) {
                sendSetBurstModeRPC(userId, serialNumber, duration, Long.valueOf(colInterval), Long.valueOf(repInterval));
            } else {
                String tid = CommonUtils.generateUUID();
                HashMap<String, Object> data = new HashMap<>();
                data.put("userId", userId);
                data.put("_id", tid);

                mongoService.create(RPC_RESULT_INFO, data);
                HashMap<String, String> publishParams = new HashMap<>();
                publishParams.put("-USER_ID-", userId);
                publishParams.put("-S_ID-", responseDTO.getSerialNumber());
                publishParams.put("-ID-", String.valueOf(CommonUtils.getCurrentTimeInMillis()));
                publishParams.put("-TID-", tid);
                publishParams.put("-DURATION-", String.valueOf(duration));
                publishParams.put("-COLL_INTERVAL-", String.valueOf(colInterval));
                publishParams.put("-REP_INTERVAL-", String.valueOf(repInterval));
                publishParams.put("-OBJECT-", ActiontecConstants.OBJECT_TYPE_DIAGNOSTICS);

                try {
                    publishToTopic(publishParams, MqttTemplate.ENABLE_DISABLE_DIAGNOSTICS_TEMPLATE_FOR_USER, MqttTemplate.TOPIC_FOR_RPC_CALL, RPC_DEFAULT_MAX_TRIES);
                } catch (ValidationException e) {
                    responseDTO.setStatus(ERROR);
                    responseDTO.setMessage("Error in publishing to mqtt.");
                    return responseDTO;
                } catch (Exception e) {
                    responseDTO.setStatus(ERROR);
                    responseDTO.setMessage("Some error occured in processing.");
                    return responseDTO;
                }

                BasicDBObject query = new BasicDBObject();
                query.put("_id", tid);

                boolean resultReceived = false;
                int maxTries = 0;
                Boolean isTimeout = false;
                while (!resultReceived) {

                    try {
                        LOG.info(Thread.currentThread().getName() + " is sleeping... maxTries = " + maxTries);
                        Thread.sleep(RPC_DEFUALT_RETRY_INTERVAL_MILLIS);
                    } catch (InterruptedException e) {
                        LOG.error("Error during thread sleep", e);
                        responseDTO.setStatus(ERROR);
                        responseDTO.setMessage("Some error occured in processing.");
                        return responseDTO;
                    }

                    maxTries++;

                    if (maxTries == RPC_DEFAULT_MAX_TRIES) {
                        isTimeout = true;
                        LOG.error("Operation Timed Out...please retry");
                        BasicDBObject dataToUpdate = new BasicDBObject();
                        dataToUpdate.put("result", TIME_OUT);
                        dataToUpdate.put("date", new Date());

                        BasicDBObject update = new BasicDBObject();
                        update.put("$set", dataToUpdate);
                        mongoService.update(query, update, false, false, RPC_RESULT_INFO);
                        break;
                    }

                    DBObject channelOptimization = mongoService.findOne(RPC_RESULT_INFO, query);
                    if (Objects.isNull(channelOptimization) || Objects.isNull(channelOptimization.get("date"))) {
                        continue;
                    } else {
                        resultReceived = true;
                    }
                }

                DBObject channelOptimization = mongoService.findOne(RPC_RESULT_INFO, query);
                if (Objects.nonNull(channelOptimization)) {
                    if (!RPC_RESULT.equals(channelOptimization.get("result"))) {
                        if (isTimeout) {
                            responseDTO.setStatus(ERROR);
                            responseDTO.setMessage("Request Timed Out.");
                            return responseDTO;
                        } else {
                            responseDTO.setStatus(ERROR);
                            responseDTO.setMessage("Request failed, RPC result :: " + String.valueOf(channelOptimization.get("result")));
                            return responseDTO;
                        }
                    }
                }
            }
        } catch (TimeoutException e) {
            e.printStackTrace();
            LOG.error(e);
            responseDTO.setStatus(ERROR);
            if (type.equals("GATEWAY"))
                responseDTO.setMessage(e.getMessage());
            else
                responseDTO.setMessage("Request Timed Out.");
            return  responseDTO;
        } catch (Exception e) {
            e.printStackTrace();
            LOG.error(e);
            responseDTO.setStatus(ERROR);
            responseDTO.setMessage(e.getMessage());
            return  responseDTO;
        }

        //RPC Ends
        DBObject aPDetails = new BasicDBObject();
        aPDetails.put("diagnosEnabled", diagnosEnabled);
        BasicDBObject updateData = new BasicDBObject();
        updateData.put("$set", aPDetails);
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userId);
        query.put("serialNumber", serialNumber);

        mongoService.update(query, updateData, false, false, AP_DETAIL);

        responseDTO.setStatus(SUCCESS);
        responseDTO.setMessage("Monitor mode successfully " + (diagnosEnabled ? "enabled." : "disabled."));
        return responseDTO;
    }
}
