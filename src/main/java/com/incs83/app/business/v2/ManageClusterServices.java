package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.Transactional;
import com.incs83.app.common.v2.ClusterRequest;
import com.incs83.app.common.v2.EditCluster;
import com.incs83.app.common.v3.TechnicianDashboardDTO;
import com.incs83.app.constants.queries.ClusterInfoSQL;
import com.incs83.app.constants.queries.ScheduleJobSQL;
import com.incs83.app.entities.*;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberListDTO;
import com.incs83.app.responsedto.v2.cluster.ClusterDTO;
import com.incs83.app.responsedto.v2.clusters.ClusterSearch;
import com.incs83.app.responsedto.v2.clusters.ClusterSubscriber;
import com.incs83.app.responsedto.v2.clusters.ClusterSubscriberResponseDTO;
import com.incs83.app.responsedto.v2.clusters.ClustersDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.isp.ISPModel;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.business.ESService;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.service.ESServiceImpl;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.INTERNET_SERVICE_PROVIDER;
import static com.incs83.app.constants.misc.ActiontecConstants.NETWORK_POLICY;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.queries.ClusterInfoSQL.GET_EQUIPMENT_BY_CLUSTER_AND_RGW_SERIAL;
import static com.incs83.app.constants.queries.EquipmentSQL.*;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_INDEX;

@Service
public class ManageClusterServices {

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private HazelcastService cacheService;

    @Autowired
    private ESService esService;

    @Autowired
    private ESServiceImpl esServiceImpl;

    @Autowired
    private NewManageEquipmentService manageEquipmentService;

    @Value("${elastic-search.enable}")
    private Boolean elasticSearchEnable;

    private ObjectMapper objectMapper = new ObjectMapper();

    private static final Logger LOG = LogManager.getLogger("org");

    public ClusterDTO getClusterInfoListForNetworkDashBoard(boolean isDefaultCluster) throws Exception {
        ClusterDTO clusterDTO = new ClusterDTO();
        ArrayList<ClusterDTO.ClusterData> clusterDataList = new ArrayList<>();
        List<ClusterInfo> clusterInfoList;
        if (CommonUtils.isSysAdmin()) {
            clusterInfoList = (List<ClusterInfo>) dataAccessService.read(ClusterInfo.class);
            ClusterDTO.ClusterData globalCluster = clusterDTO.new ClusterData();
            globalCluster.setId("0");
            globalCluster.setName("Global");
            globalCluster.setIsp("global");
            clusterDataList.add(globalCluster);
            if (isDefaultCluster)
                clusterInfoList = clusterInfoList.parallelStream().filter(q -> q.isDefaultCluster()).collect(Collectors.toList());
        } else {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            clusterInfoList = new ArrayList<>(compartment.getCluster());
            if (isDefaultCluster)
                clusterInfoList = clusterInfoList.parallelStream().filter(q -> q.isDefaultCluster()).collect(Collectors.toList());
        }

        clusterInfoList.forEach(clusterInfo -> {
            try {
                ClusterDTO.ClusterData cluster = clusterDTO.new ClusterData();
                cluster.setId(String.valueOf(clusterInfo.getId()));
                cluster.setName(String.valueOf(clusterInfo.getName()));
                cluster.setIsp(getIspNameForDefaultCluster(clusterInfo.getId()));
                clusterDataList.add(cluster);
            } catch (Exception e) {
                LOG.error("Error while fetching isp by cluster");
            }
        });

        if (clusterDataList.isEmpty()) {
            ClusterDTO.ClusterData cluster = clusterDTO.new ClusterData();
            clusterDataList.add(cluster);
            clusterDTO.setData(clusterDataList);
        } else
            clusterDTO.setData(clusterDataList);
        return clusterDTO;
    }

    public String getIspNameForDefaultCluster(String clusterId) throws Exception {
        String ispName = EMPTY_STRING;
        HashMap<String, Object> query = new HashMap<>();
        query.clear();
        query.put("clusterId", clusterId);

        List<String> groupList = dataAccessService.readNative(ClusterInfoSQL.GET_GROUP_ID_BY_CLUSTER_ID, query);
        if (Objects.nonNull(groupList) && !groupList.isEmpty()) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, groupList.get(0));

            ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
            if (Objects.nonNull(ispdtoList) && Objects.nonNull(compartment)) {
                List<ISPModel> ispModels = ispdtoList.getData().stream().filter(q -> compartment.getIspId().equals(q.getId())).collect(Collectors.toList());
                if (!ispModels.isEmpty())
                    ispName = ispModels.get(0).getName();
            } else if (Objects.nonNull(compartment)) {
                HashMap<String, Object> queryParams = new HashMap<>();
                queryParams.put("id", compartment.getIspId());

                HashMap<String, Object> appendableParams = new HashMap<>();
                BasicDBObject fieldsToRemove = new BasicDBObject();
                fieldsToRemove.put("_id", ZERO);

                DBObject dbObject = mongoService.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
                if (Objects.nonNull(dbObject))
                    ispName = String.valueOf(dbObject.get("name"));
            }
        }

        return ispName;
    }

    public ClustersDTO getClusterInfoList() throws Exception {
        ClustersDTO clusterDTO = new ClustersDTO();
        HashMap<String, Object> params = new HashMap<>();
        ArrayList<ClustersDTO.ClustersDetail> clusterDataList = new ArrayList<>();
        if (!CommonUtils.isSysAdmin()) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            Set<ClusterInfo> clusterInfoList = compartment.getCluster();

            params.clear();
//            long count = Long.valueOf(dataAccessService.read(UserAP.class, GET_COUNT_AP_BY_GROUP.replaceAll("###", "where groupId='" + compartment.getId() + "'"), params).iterator().next().toString());
            long count = 0;
            long totalUser = 0;
            for (ClusterInfo clusterInfo : clusterInfoList) {
                //Cache is not available

                /*if (cacheService.isMapExist(DASHBOARD_COUNTS) && Objects.nonNull(cacheService.read(clusterInfo.getId(), DASHBOARD_COUNTS))) {
                    HashMap<String, Object> data = (HashMap<String, Object>) cacheService.read(clusterInfo.getId(), DASHBOARD_COUNTS);
                    totalUser = Objects.nonNull(data.get("subscriberCount")) ? Long.valueOf(data.get("subscriberCount").toString()) : 0;
                    count = totalUser;
                } else {
                    count = Long.valueOf(dataAccessService.read(UserAP.class, GET_COUNT_AP_BY_GROUP.replaceAll("###", "where groupId='" + compartment.getId() + "'"), params).iterator().next().toString());
                    params.clear();
                    params.put("clusterId", clusterInfo.getId());
                    totalUser = Long.valueOf(dataAccessService.readNative(ClusterInfoSQL.GET_COUNT_USER_AP_BY_CLUSTER_ID, params).iterator().next().toString());
                }*/

                params.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
                count = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_COMPARTMENT, params).iterator().next().toString());

                params.clear();
                params.put("cluster_id", clusterInfo.getId());
                totalUser = Long.valueOf(dataAccessService.readNative(GET_EQUIPMENT_COUNT_BY_CLUSTER_ID, params).iterator().next().toString());

                ClustersDTO.ClustersDetail cluster = clusterDTO.new ClustersDetail();
                cluster.setId(clusterInfo.getId());
                cluster.setName(clusterInfo.getName());
                cluster.setDescription(clusterInfo.getDescription());
                cluster.setSelected(totalUser);
                cluster.setTotal(count);
                cluster.setUnSelected((count - totalUser) < 0 ? 0 : (count - totalUser));
                cluster.setDefaultCluster(clusterInfo.isDefaultCluster());

                clusterDataList.add(cluster);
            }
        } else {
            List<ClusterInfo> clusterInfoList = (List<ClusterInfo>) dataAccessService.read(ClusterInfo.class);
//            long count = Long.valueOf(dataAccessService.read(UserAP.class, UserAPSQL.GET_COUNT_AP_BY_ALL.replaceAll("###", EMPTY_STRING), params).iterator().next().toString());

            long count = 0;
            /*if (cacheService.isMapExist(DASHBOARD_COUNTS) && Objects.nonNull(cacheService.read(GLOBAL, DASHBOARD_COUNTS)) && Objects.nonNull(((HashMap<String, Object>) cacheService.read(GLOBAL, DASHBOARD_COUNTS)).get("subscriberCount"))) {
                count = Long.valueOf(((HashMap<String, Object>) cacheService.read(GLOBAL, DASHBOARD_COUNTS)).get("subscriberCount").toString());
            } else {
                count = Long.valueOf(dataAccessService.read(UserAP.class, UserAPSQL.GET_COUNT_AP_BY_ALL.replaceAll("###", EMPTY_STRING), params).iterator().next().toString());
            }*/
            long totalUser = 0;
            for (ClusterInfo clusterInfo : clusterInfoList) {
                /*if (cacheService.isMapExist(DASHBOARD_COUNTS) && Objects.nonNull(cacheService.read(clusterInfo.getId(), DASHBOARD_COUNTS)) && Objects.nonNull(((HashMap<String, Object>) cacheService.read(clusterInfo.getId(), DASHBOARD_COUNTS)).get("subscriberCount"))) {
                    totalUser = Long.valueOf(((HashMap<String, Object>) cacheService.read(clusterInfo.getId(), DASHBOARD_COUNTS)).get("subscriberCount").toString());
                } else {
                    params.clear();
                    params.put("clusterId", clusterInfo.getId());
                    totalUser = Long.valueOf(dataAccessService.readNative(ClusterInfoSQL.GET_COUNT_USER_AP_BY_CLUSTER_ID, params).iterator().next().toString());
                }*/

                params.clear();
                count = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_HQL, params).iterator().next().toString());

                params.clear();
                params.put("cluster_id", clusterInfo.getId());
                totalUser = Long.valueOf(dataAccessService.readNative(GET_EQUIPMENT_COUNT_BY_CLUSTER_ID, params).iterator().next().toString());

                ClustersDTO.ClustersDetail cluster = clusterDTO.new ClustersDetail();
                cluster.setId(clusterInfo.getId());
                cluster.setName(clusterInfo.getName());
                cluster.setDescription(clusterInfo.getDescription());
                cluster.setSelected(totalUser);
                cluster.setTotal(count);
                cluster.setUnSelected((count - totalUser) < 0 ? 0 : (count - totalUser));
                cluster.setDefaultCluster(clusterInfo.isDefaultCluster());
                clusterDataList.add(cluster);
            }
        }

        if (clusterDataList.isEmpty()) {
            ClustersDTO.ClustersDetail cluster = clusterDTO.new ClustersDetail();
            clusterDataList.add(cluster);
            clusterDTO.setData(clusterDataList);
        } else
            clusterDTO.setData(clusterDataList);
        return clusterDTO;
    }

    /*private boolean isSearchEnabled(ClusterSearch clusterSearch) {
        return Objects.nonNull(clusterSearch.getSerialNumber()) || Objects.nonNull(clusterSearch.getName()) || Objects.nonNull(clusterSearch.getEmail()) || Objects.nonNull(clusterSearch.getRgwMAC()) || Objects.nonNull(clusterSearch.getGlobalAccountNo()) || Objects.nonNull(clusterSearch.getCamsAccountNo());
    }*/

    public ClusterSubscriberResponseDTO getEquipmentByCluster(String clusterId, ClusterSearch clusterSearch, Boolean exact) throws Exception {
        if (clusterSearch.getMax() > 500)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max cannot be greater than 500");

        if (Objects.isNull(clusterId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster ID cannot be Null");

        ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, clusterId);

        if (Objects.isNull(clusterInfo))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + clusterId);

        if (!CommonUtils.isSysAdmin()) {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            Set<ClusterInfo> clusterList = compartment.getCluster().stream().filter(cluster -> cluster.getId().equals(clusterInfo.getId())).collect(Collectors.toSet());
            if (clusterList.isEmpty())
                throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        }

        if (clusterSearch.getOffset() < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        ClusterSubscriberResponseDTO clusterResponseDetails = new ClusterSubscriberResponseDTO();

        ClusterSubscriberResponseDTO.ClusterInfoDetail clusterInfoDetail = clusterResponseDetails.new ClusterInfoDetail();
        clusterInfoDetail.setId(clusterInfo.getId());
        clusterInfoDetail.setName(clusterInfo.getName());
        clusterInfoDetail.setDescription(clusterInfo.getDescription());
        clusterInfoDetail.setDefaultCluster(clusterInfo.isDefaultCluster());
        clusterInfoDetail.setIsp(getIspNameForDefaultCluster(clusterInfo.getId()));

        TechnicianDashboardDTO technicianDashboardDTO = new TechnicianDashboardDTO();
        technicianDashboardDTO.setOffset(clusterSearch.getOffset());
        technicianDashboardDTO.setMax(clusterSearch.getMax());
        technicianDashboardDTO.setCamsAccountNo(clusterSearch.getCamsAccountNo());
        technicianDashboardDTO.setGlobalAccountNo(clusterSearch.getGlobalAccountNo());
        technicianDashboardDTO.setEmail(clusterSearch.getEmail());
        technicianDashboardDTO.setName(clusterSearch.getName());
        technicianDashboardDTO.setSerialNumber(clusterSearch.getSerialNumber());
        technicianDashboardDTO.setRgwMAC(clusterSearch.getRgwMAC());
        technicianDashboardDTO.setOrder(clusterSearch.getOrder());
        technicianDashboardDTO.setSortBy(clusterSearch.getSortBy());
        if (clusterSearch.isSelected())
            technicianDashboardDTO.setClusterId(clusterInfo.getId());

        HashMap<String, Object> responseFromEs;
        if (elasticSearchEnable) {
            responseFromEs = (HashMap<String, Object>) manageEquipmentService.getEquipmentFromES(technicianDashboardDTO, dataSecurityMapping, clusterInfo, exact);
        } else {
            SubscriberListDTO.SubscriberData data = (SubscriberListDTO.SubscriberData) manageEquipmentService.getEquipmentFromMysql(technicianDashboardDTO, dataSecurityMapping, clusterInfo);
            if (Objects.nonNull(data)) {
                responseFromEs = objectMapper.convertValue(data, HashMap.class);
            } else {
                responseFromEs = null;
            }
        }

        ClusterSubscriber clusterSubscriber = new ClusterSubscriber();

        if (Objects.nonNull(responseFromEs) && Objects.nonNull(responseFromEs.get("objects"))) {
            List<HashMap<String, Object>> listOfSubscriber = (List<HashMap<String, Object>>) responseFromEs.get("objects");
            if (Objects.nonNull(listOfSubscriber) && !listOfSubscriber.isEmpty()) {
                ArrayList<ClusterSubscriber.UserDetail> userDetailList;
                if (clusterSearch.isSelected()) {
                    userDetailList = populateClusterSubscriber(listOfSubscriber);
                } else {
                    userDetailList = populateAllSubscriberForCluster(listOfSubscriber, clusterInfo.getId());
                }

                clusterSubscriber.setTotalCount(Long.valueOf(responseFromEs.get("totalCount").toString()));
                clusterSubscriber.setObjects(userDetailList);
            } else {
                clusterSubscriber.setTotalCount(0);
                clusterSubscriber.setObjects(new ArrayList<>());
            }
        } else {
            clusterSubscriber.setTotalCount(0);
            clusterSubscriber.setObjects(new ArrayList<>());
        }

        clusterInfoDetail.setUsers(clusterSubscriber);

        clusterResponseDetails.setData(clusterInfoDetail);

        return clusterResponseDetails;
    }

    private ArrayList<ClusterSubscriber.UserDetail> populateClusterSubscriber(List<HashMap<String, Object>> listOfSubscriber) {
        ClusterSubscriber clusterSubscriber = new ClusterSubscriber();
        ArrayList<ClusterSubscriber.UserDetail> userDetailList = new ArrayList<>();
        for (HashMap<String, Object> subscriber : listOfSubscriber) {
            ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
            userDetail.setSerialNumber(Objects.isNull(subscriber.get("serialNumber")) ? null : subscriber.get("serialNumber").toString());
            userDetail.setEmail(Objects.isNull(subscriber.get("email")) ? null : subscriber.get("email").toString());
            userDetail.setName(Objects.isNull(subscriber.get("name")) ? null : subscriber.get("name").toString());
            userDetail.setRgwMAC(Objects.isNull(subscriber.get("rgwMAC")) ? null : subscriber.get("rgwMAC").toString());
            userDetail.setEquipmentId(Objects.isNull(subscriber.get("equipmentId")) ? null : subscriber.get("equipmentId").toString());
            userDetail.setGlobalAccountNo(Objects.isNull(subscriber.get("globalAccountNo")) ? null : subscriber.get("globalAccountNo").toString());
            userDetail.setCamsAccountNo(Objects.isNull(subscriber.get("globalAccountNo")) ? null : subscriber.get("globalAccountNo").toString());
            userDetail.setSelected(true);
            userDetailList.add(userDetail);
        }

        return userDetailList;
    }

    private ArrayList<ClusterSubscriber.UserDetail> populateAllSubscriberForCluster(List<HashMap<String, Object>> listOfSubscriber, String clusterId) throws Exception {
        ClusterSubscriber clusterSubscriber = new ClusterSubscriber();
        HashMap<String, Object> query = new HashMap<>();
        query.put("cluster_id", clusterId);
        ArrayList<ClusterSubscriber.UserDetail> userDetailList = new ArrayList<>();
        for (HashMap<String, Object> subscriber : listOfSubscriber) {
            ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
            userDetail.setSerialNumber(Objects.isNull(subscriber.get("serialNumber")) ? null : subscriber.get("serialNumber").toString());
            userDetail.setEmail(Objects.isNull(subscriber.get("email")) ? null : subscriber.get("email").toString());
            userDetail.setName(Objects.isNull(subscriber.get("name")) ? null : subscriber.get("name").toString());
            userDetail.setRgwMAC(Objects.isNull(subscriber.get("rgwMAC")) ? null : subscriber.get("rgwMAC").toString());
            userDetail.setEquipmentId(Objects.isNull(subscriber.get("equipmentId")) ? null : subscriber.get("equipmentId").toString());
            userDetail.setGlobalAccountNo(Objects.isNull(subscriber.get("globalAccountNo")) ? null : subscriber.get("globalAccountNo").toString());
            userDetail.setCamsAccountNo(Objects.isNull(subscriber.get("globalAccountNo")) ? null : subscriber.get("globalAccountNo").toString());

            query.put("equipment_id", userDetail.getEquipmentId());
            Long count = Long.valueOf(dataAccessService.readNative(GET_EQUIPMENT_BY_CLUSTER_AND_RGW_SERIAL, query).iterator().next().toString());
            if (count > 0)
                userDetail.setSelected(true);
            else
                userDetail.setSelected(false);
            userDetailList.add(userDetail);
        }

        return userDetailList;
    }

    /*public ClusterSubscriberResponseDTO getSelectedSubscriberForCluster(String clusterId, ClusterSearch clusterSearch) throws Exception {
        if (clusterSearch.getMax() > 500)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max cannot be greater than 500");

        if (Objects.isNull(clusterId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster ID cannot be Null");

        ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, clusterId);

        if (Objects.isNull(clusterInfo))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + clusterId);

        if (clusterSearch.getOffset() < 0)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");

        ClusterSubscriberResponseDTO clusterResponseDetails = new ClusterSubscriberResponseDTO();

        ClusterSubscriberResponseDTO.ClusterInfoDetail clusterInfoDetail = clusterResponseDetails.new ClusterInfoDetail();
        clusterInfoDetail.setId(clusterInfo.getId());
        clusterInfoDetail.setName(clusterInfo.getName());
        clusterInfoDetail.setDescription(clusterInfo.getDescription());
        clusterInfoDetail.setDefaultCluster(clusterInfo.isDefaultCluster());
        clusterInfoDetail.setIsp(getIspNameForDefaultCluster(clusterInfo.getId()));

        ClusterSubscriber clusterSubscriber = new ClusterSubscriber();
        List<ClusterSubscriber.UserDetail> userDetailArrayList = new ArrayList<>();
        long count = 0;

        List<Object[]> subscribers = null;

        if (isSearchEnabled(clusterSearch)) {
            Page page = searchSubscriberByCluster(clusterSearch, clusterId);

            if (page.getObjects().isEmpty()) {
                clusterSubscriber.setTotalCount(0);
                clusterSubscriber.setObjects(new ArrayList<>());
            } else {
                clusterSubscriber.setTotalCount(page.getTotalCount());
                ArrayList<ClusterSubscriber.UserDetail> userDetailList = new ArrayList<>();
                userDetailArrayList = (List<ClusterSubscriber.UserDetail>) page.getObjects();
                userDetailList.addAll(userDetailArrayList.parallelStream().filter(element -> element.isSelected()).collect(Collectors.toList()));
                userDetailList.addAll(userDetailArrayList.parallelStream().filter(element -> !element.isSelected()).collect(Collectors.toList()));
                clusterSubscriber.setObjects(userDetailList);
            }

            clusterInfoDetail.setUsers(clusterSubscriber);
            clusterResponseDetails.setData(clusterInfoDetail);

            return clusterResponseDetails;
        } else {
            HashMap<String, Object> queryParam = new HashMap<>();
            queryParam.put("clusterId", clusterInfo.getId());

            if (clusterSearch.isSelected() || clusterInfo.isDefaultCluster()) {
                String replaceWith = " LIMIT " + (Objects.nonNull(clusterSearch.getOffset()) ? clusterSearch.getOffset() : 0) + ", " + (Objects.nonNull(clusterSearch.getMax()) ? clusterSearch.getMax() : 10);
                String sqlQuery = String.format(ClusterInfoSQL.GET_PAGINATED_USER_AP_ID_BY_CLUSTER_ID, replaceWith);
                subscribers = dataAccessService.readNative(sqlQuery, queryParam);
                if (Objects.nonNull(subscribers) && !subscribers.isEmpty()) {
                    for (Object[] user : subscribers) {
                        if (Objects.nonNull(ValidationUtil.isNullString(String.valueOf(user[0])))) {
                            ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
                            userDetail.setSerialNumber(ValidationUtil.isNullString(String.valueOf(user[0])));
                            userDetail.setEmail(ValidationUtil.isNullString(String.valueOf(user[1])));
                            String firstName = ValidationUtil.isNullString(String.valueOf(user[2]));
                            String lastName = ValidationUtil.isNullString(String.valueOf(user[3]));
                            userDetail.setName((Objects.isNull(firstName) ? EMPTY_STRING : firstName) + SPACE + (Objects.isNull(lastName) ? EMPTY_STRING : lastName));
                            userDetail.setRgwMAC(ValidationUtil.isNullString(String.valueOf(user[4])));
                            userDetail.setSubscriberId(ValidationUtil.isNullString(String.valueOf(user[5])));
                            userDetail.setGlobalAccountNo(isNullString(String.valueOf(user[6])));
                            userDetail.setCamsAccountNo(isNullString(String.valueOf(user[7])));
                            userDetail.setSelected(true);
                            userDetailArrayList.add(userDetail);
                        }
                    }
                    count = Long.valueOf(dataAccessService.readNative(ClusterInfoSQL.GET_COUNT_USER_AP_BY_CLUSTER_ID, queryParam).iterator().next().toString());
                }
            } else {
                String replaceField =
                        "Order By name ASC" +
                                " LIMIT " + (Objects.nonNull(clusterSearch.getOffset()) ? clusterSearch.getOffset() : 0) + ", " + (Objects.nonNull(clusterSearch.getMax()) ? clusterSearch.getMax() : 10);

                String query;
                query = String.format(UserAPSQL.GET_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : EMPTY_STRING), replaceField);
                subscribers = dataAccessService.readNative(query, new HashMap<>());
                if (Objects.nonNull(subscribers) && !subscribers.isEmpty()) {
                    for (Object subscriberDetail[] : subscribers) {
                        if (Objects.nonNull(ValidationUtil.isNullString(String.valueOf(subscriberDetail[13])))) {
                            queryParam.put("userId", String.valueOf(subscriberDetail[13]));
                            List<Object[]> usersDetailList = dataAccessService.readNative(GET_USER_AP_BY_CLUSTER_ID_AND_USER_ID, queryParam);
                            if (Objects.nonNull(usersDetailList) && !usersDetailList.isEmpty()) {
                                ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
                                userDetail.setSerialNumber(ValidationUtil.isNullString(String.valueOf(subscriberDetail[14])));
                                userDetail.setEmail(ValidationUtil.isNullString(String.valueOf(subscriberDetail[2])));
                                String firstName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[0]));
                                String lastName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[1]));
                                userDetail.setName((Objects.isNull(firstName) ? EMPTY_STRING : firstName) + SPACE + (Objects.isNull(lastName) ? EMPTY_STRING : lastName));
                                userDetail.setRgwMAC(ValidationUtil.isNullString(String.valueOf(subscriberDetail[12])));
                                userDetail.setSubscriberId(ValidationUtil.isNullString(String.valueOf(subscriberDetail[13])));
                                userDetail.setGlobalAccountNo(isNullString(String.valueOf(subscriberDetail[9])));
                                userDetail.setCamsAccountNo(isNullString(String.valueOf(subscriberDetail[15])));
                                userDetail.setSelected(true);
                                userDetailArrayList.add(userDetail);
                            } else {
                                ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
                                userDetail.setSerialNumber(ValidationUtil.isNullString(String.valueOf(subscriberDetail[14])));
                                userDetail.setEmail(ValidationUtil.isNullString(String.valueOf(subscriberDetail[2])));
                                String firstName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[0]));
                                String lastName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[1]));
                                userDetail.setName((Objects.isNull(firstName) ? EMPTY_STRING : firstName) + SPACE + (Objects.isNull(lastName) ? EMPTY_STRING : lastName));
                                userDetail.setRgwMAC(ValidationUtil.isNullString(String.valueOf(subscriberDetail[12])));
                                userDetail.setSubscriberId(ValidationUtil.isNullString(String.valueOf(subscriberDetail[13])));
                                userDetail.setGlobalAccountNo(isNullString(String.valueOf(subscriberDetail[9])));
                                userDetail.setCamsAccountNo(isNullString(String.valueOf(subscriberDetail[15])));
                                userDetail.setSelected(false);
                                userDetailArrayList.add(userDetail);
                            }
                        }
                    }

                    String queries = UserAPSQL.COUNT_TOTAL_SUBSCRIBER;
                    if (!CommonUtils.isSysAdmin()) {
                        queries = queries.replaceAll("###", "Where groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'");
                    } else {
                        queries = queries.replaceAll("###", EMPTY_STRING);
                    }
                    List<Object> totalSubscriber = (List<Object>) dataAccessService.readNative(queries, new HashMap<>());
                    count = Integer.valueOf(Objects.nonNull(totalSubscriber) ? totalSubscriber.get(0).toString() : "0");
                }
            }
        }


        if (userDetailArrayList.isEmpty()) {
            clusterSubscriber.setTotalCount(0);
            clusterSubscriber.setObjects(new ArrayList<>());
        } else {
            clusterSubscriber.setTotalCount(count);
            ArrayList<ClusterSubscriber.UserDetail> userDetailList = new ArrayList<>();
            userDetailList.addAll(userDetailArrayList.parallelStream().filter(element -> element.isSelected()).collect(Collectors.toList()));
            userDetailList.addAll(userDetailArrayList.parallelStream().filter(element -> !element.isSelected()).collect(Collectors.toList()));
            clusterSubscriber.setObjects(userDetailList);
        }

        clusterInfoDetail.setUsers(clusterSubscriber);
        clusterResponseDetails.setData(clusterInfoDetail);

        return clusterResponseDetails;
    }*/

    /*private Page mapElasticToSubscriberDTO(ElasticSearchDTO response, Page page, ClusterInfo clusterInfo, ClusterSubscriber clusterSubscriber, List<ClusterSubscriber.UserDetail> userDetailList) {

        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("clusterId", clusterInfo.getId());

        if (Objects.nonNull(response)) {

            response.getObjects().forEach(q -> {
                SubscriberESDTO subscriberESDTO = objectMapper.convertValue(q, SubscriberESDTO.class);
                try {
                    queryParam.put("userId", subscriberESDTO.getId());
                    List<Object[]> usersDetailList = dataAccessService.readNative(GET_USER_AP_BY_CLUSTER_ID_AND_USER_ID, queryParam);

                    if (Objects.nonNull(usersDetailList) && !usersDetailList.isEmpty()) {
                        ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
                        userDetail.setSerialNumber(subscriberESDTO.getSerialNumber());
                        userDetail.setEmail(subscriberESDTO.getEmail());
                        userDetail.setName(subscriberESDTO.getName());
                        userDetail.setRgwMAC(subscriberESDTO.getRgwMAC());
                        userDetail.setSubscriberId(subscriberESDTO.getId());
                        userDetail.setGlobalAccountNo(subscriberESDTO.getGlobalAccountNo());
                        userDetail.setCamsAccountNo(subscriberESDTO.getCamsAccountNo());
                        userDetail.setSelected(true);
                        userDetailList.add(userDetail);
                    } else {
                        ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
                        userDetail.setSerialNumber(subscriberESDTO.getSerialNumber());
                        userDetail.setEmail(subscriberESDTO.getEmail());
                        userDetail.setName(subscriberESDTO.getName());
                        userDetail.setRgwMAC(subscriberESDTO.getRgwMAC());
                        userDetail.setSubscriberId(subscriberESDTO.getId());
                        userDetail.setGlobalAccountNo(subscriberESDTO.getGlobalAccountNo());
                        userDetail.setCamsAccountNo(subscriberESDTO.getCamsAccountNo());
                        userDetail.setSelected(false);
                        userDetailList.add(userDetail);
                    }
                } catch (Exception e) {
                    LOG.error("Error while fetching userAp by clusterId");
                }
            });
        }

        page.setObjects(userDetailList);
        page.setTotalCount(Integer.valueOf(String.valueOf(userDetailList.size())));
        return page;
    }*/

    /*private long getEquipmentLastReportedAt(String apId) {
        long lastReportedAt = 0;
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", apId);
        queryParams.put("serialNumber", apId);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(aPDetails)) {
            lastReportedAt = Objects.isNull(aPDetails.get("timestamp")) ? 0 : Long.valueOf(aPDetails.get("timestamp").toString());
        }

        return lastReportedAt;
    }*/

    /*public Page searchSubscriberByCluster(ClusterSearch clusterSearch, String clusterId) throws Exception {
        if (Objects.isNull(clusterId))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster ID cannot be Null");

        ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, clusterId);

        if (Objects.isNull(clusterInfo))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + clusterId);

        ClusterSubscriber clusterSubscriber = new ClusterSubscriber();
        List<ClusterSubscriber.UserDetail> userDetailArrayList = new ArrayList<>();

        ESRequest elasticSearchRequest = new ESRequest();
        elasticSearchRequest.setMax(clusterSearch.getMax()).setOffset(clusterSearch.getOffset()).setSortBy(clusterSearch.getSortBy()).setIndex(USERAP_INDEX);
        HashMap<String, Object> esQueryParams = new HashMap<>();
        esQueryParams.put("name", clusterSearch.getName());
        esQueryParams.put("email", clusterSearch.getEmail());
        esQueryParams.put("rgwMAC", clusterSearch.getRgwMAC());
        esQueryParams.put("globalAccountNo", clusterSearch.getGlobalAccountNo());
        esQueryParams.put("camsAccountNo", clusterSearch.getCamsAccountNo());
        esQueryParams.put("serialNumber", clusterSearch.getSerialNumber());

        if (!CommonUtils.isSysAdmin()) {
            esQueryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
        }
        elasticSearchRequest.setQueryParams(esQueryParams);
        ElasticSearchDTO response = esService.searchWithFilter(elasticSearchRequest, "clusters", (Objects.nonNull(clusterInfo) && clusterSearch.isSelected()) ? clusterInfo.getId() : null);
        if (Objects.nonNull(response)) {
            Page responsePage = new Page();

            if (response.getObjects().size() > 0) {
                return mapElasticToSubscriberDTO(response, responsePage, clusterInfo, clusterSubscriber, userDetailArrayList);
            }
            List<ClusterSubscriber.UserDetail> userDetailList = new ArrayList<>();
            responsePage.setObjects(userDetailList);
            responsePage.setTotalCount(0);
            return responsePage;
        }

        ArrayList<String> queryParams = new ArrayList<>();

        StringBuffer sb = new StringBuffer("");

        if (Objects.nonNull(clusterSearch.getName())) {
            queryParams.add("CONCAT( u.firstName, ' ', u.lastName ) LIKE '%" + clusterSearch.getName() + "%'");
        }

        if (Objects.nonNull(clusterSearch.getEmail())) {
            queryParams.add("u.email LIKE '%" + clusterSearch.getEmail() + "%'");
        }

        if (Objects.nonNull(clusterSearch.getSerialNumber())) {
            queryParams.add("ua.apId LIKE '%" + clusterSearch.getSerialNumber() + "%'");
        }

        if (Objects.nonNull(clusterSearch.getRgwMAC())) {
            queryParams.add("ua.rgwMAC LIKE '%" + clusterSearch.getRgwMAC() + "%'");
        }

        if (Objects.nonNull(clusterSearch.getGlobalAccountNo())) {
            queryParams.add("ua.globalAccountNo LIKE '%" + clusterSearch.getGlobalAccountNo() + "%'");
        }

        if (Objects.nonNull(clusterSearch.getCamsAccountNo())) {
            queryParams.add("ua.camsAccountNo LIKE '%" + clusterSearch.getCamsAccountNo() + "%'");
        }

        if (!queryParams.isEmpty()) {
            sb.append("Where ");
            int i = 0;
            for (String condition : queryParams) {
                if (i > 0)
                    sb.append(" and ");
                sb.append(condition);
                i++;
            }
        }
        String countQuery;

        if (clusterSearch.isSelected() || clusterInfo.isDefaultCluster()) {
            String clusterSql = "(select * from userAp ap inner join cluster_userAp c On (ap.userId=c.userAp_userId) where c.cluster_id='" + clusterId + "')";
            countQuery = "Select count(u.id) from user u inner join " + clusterSql + " as ua on(u.id = ua.userId) " + sb.toString();
        } else {
            countQuery = String.format(UserAPSQL.GET_COUNT_OF_SEARCH_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), sb.toString());
        }
        sb.append(" LIMIT " + (Objects.nonNull(clusterSearch.getOffset()) ? clusterSearch.getOffset() : 0) + ", " + (Objects.nonNull(clusterSearch.getMax()) ? clusterSearch.getMax() : 10));

        String query;
        query = String.format(UserAPSQL.GET_SUBSCRIBERS, ((!CommonUtils.isSysAdmin()) ? ("and c.id = '" + CommonUtils.getGroupIdOfLoggedInUser() + "'") : ""), sb.toString());

        List<Object[]> subscribers = dataAccessService.readNative(query, new HashMap<>());

        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("clusterId", clusterInfo.getId());

        if (Objects.nonNull(subscribers) && !subscribers.isEmpty()) {
            for (Object subscriberDetail[] : subscribers) {
                if (Objects.nonNull(ValidationUtil.isNullString(String.valueOf(subscriberDetail[13])))) {
                    queryParam.put("userId", String.valueOf(subscriberDetail[13]));
                    List<Object[]> usersDetailList = dataAccessService.readNative(GET_USER_AP_BY_CLUSTER_ID_AND_USER_ID, queryParam);
                    if (Objects.nonNull(usersDetailList) && !usersDetailList.isEmpty()) {
                        ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
                        userDetail.setSerialNumber(ValidationUtil.isNullString(String.valueOf(subscriberDetail[14])));
                        userDetail.setEmail(ValidationUtil.isNullString(String.valueOf(subscriberDetail[2])));
                        String firstName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[0]));
                        String lastName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[1]));
                        userDetail.setName((Objects.isNull(firstName) ? EMPTY_STRING : firstName) + SPACE + (Objects.isNull(lastName) ? EMPTY_STRING : lastName));
                        userDetail.setRgwMAC(ValidationUtil.isNullString(String.valueOf(subscriberDetail[12])));
                        userDetail.setSubscriberId(ValidationUtil.isNullString(String.valueOf(subscriberDetail[13])));
                        userDetail.setGlobalAccountNo(isNullString(String.valueOf(subscriberDetail[9])));
                        userDetail.setCamsAccountNo(isNullString(String.valueOf(subscriberDetail[15])));
                        userDetail.setSelected(true);
                        userDetailArrayList.add(userDetail);
                    } else {
                        ClusterSubscriber.UserDetail userDetail = clusterSubscriber.new UserDetail();
                        userDetail.setSerialNumber(ValidationUtil.isNullString(String.valueOf(subscriberDetail[14])));
                        userDetail.setEmail(ValidationUtil.isNullString(String.valueOf(subscriberDetail[2])));
                        String firstName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[0]));
                        String lastName = ValidationUtil.isNullString(String.valueOf(subscriberDetail[1]));
                        userDetail.setName((Objects.isNull(firstName) ? EMPTY_STRING : firstName) + SPACE + (Objects.isNull(lastName) ? EMPTY_STRING : lastName));
                        userDetail.setRgwMAC(ValidationUtil.isNullString(String.valueOf(subscriberDetail[12])));
                        userDetail.setSubscriberId(ValidationUtil.isNullString(String.valueOf(subscriberDetail[13])));
                        userDetail.setGlobalAccountNo(isNullString(String.valueOf(subscriberDetail[9])));
                        userDetail.setCamsAccountNo(isNullString(String.valueOf(subscriberDetail[15])));
                        userDetail.setSelected(false);
                        userDetailArrayList.add(userDetail);
                    }
                }
            }
        }

        long count = 0;

        if (clusterSearch.isSelected() || clusterInfo.isDefaultCluster()) {
            userDetailArrayList = userDetailArrayList.parallelStream().filter(element -> element.isSelected()).collect(Collectors.toList());
        }

        List<Object> totalSubscriber = (List<Object>) dataAccessService.readNative(countQuery, new HashMap<>());
        if (!userDetailArrayList.isEmpty())
            count = Long.valueOf(Objects.nonNull(totalSubscriber) ? totalSubscriber.get(0).toString() : "0");

        Page responsePage = new Page();
        responsePage.setObjects(userDetailArrayList);
        responsePage.setTotalCount((int) count);
        return responsePage;
    }*/
/*
    public ClustersDTO getClusterListForUi(String id) throws Exception {
        List<BasicDBObject> clusterResponse;
        ClustersDTO clusterDTO = new ClustersDTO();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);
        fieldsToRemove.put("_class", ZERO);
        fieldsToRemove.put("timestamp", ZERO);
        HashMap<String, Object> params = new HashMap<>();
        clusterResponse = mongoService.findList(params, CLUSTER_INFO, fieldsToRemove);
        if (Objects.isNull(clusterResponse) || clusterResponse.isEmpty()) {
            return clusterDTO;
        }

        if (Objects.nonNull(id))
            clusterResponse = clusterResponse.stream().filter(cluster -> cluster.get("id").equals(id)).collect(Collectors.toList());

        ArrayList<ClustersDTO.ClustersDetail> clusterDataList = new ArrayList<>();
        if (CommonUtils.isSysAdmin()) {
            for (BasicDBObject dbObject : clusterResponse) {
                ClustersDTO.ClustersDetail cluster = clusterDTO.new ClustersDetail();
                cluster.setId(String.valueOf(dbObject.get("id")));
                cluster.setName(Objects.isNull(dbObject.get("name")) ? null : dbObject.get("name").toString());
                cluster.setDescription(Objects.isNull(dbObject.get("description")) ? null : dbObject.get("description").toString());

                params.clear();
                long count = Long.valueOf(dataAccessService.read(UserAP.class, UserAPSQL.GET_COUNT_AP_BY_ALL.replaceAll("###", EMPTY_STRING), params).iterator().next().toString());
                cluster.setTotal(count);
                cluster.setSelected(0);
                cluster.setUnSelected(count - cluster.getSelected());
                clusterDataList.add(cluster);
            }
        } else {
            List<String> compartments = new ArrayList<>();
            compartments.add(ExecutionContext.get().getUsercontext().getGroupId());
            for (BasicDBObject dbObject : clusterResponse) {
                List<String> compartmentsFromDB = (List<String>) dbObject.get("compartments");
                if (compartmentsFromDB.containsAll(compartments)) {
                    ClustersDTO.ClustersDetail cluster = clusterDTO.new ClustersDetail();
                    cluster.setId(String.valueOf(dbObject.get("id")));
                    cluster.setName(String.valueOf(dbObject.get("name")));
                    cluster.setDescription(Objects.isNull(dbObject.get("description")) ? null : dbObject.get("description").toString());

                    params.clear();
                    long count = Long.valueOf(dataAccessService.read(UserAP.class, GET_COUNT_AP_BY_GROUP.replaceAll("###", "where groupId='" + ExecutionContext.get().getUsercontext().getGroupId() + "'"), params).iterator().next().toString());
                    cluster.setTotal(count);
                    cluster.setSelected(0);
                    cluster.setUnSelected(count - cluster.getSelected());
                    clusterDataList.add(cluster);
                }
            }
        }

        if (clusterDataList.isEmpty()) {
            ClustersDTO.ClustersDetail cluster = clusterDTO.new ClustersDetail();
            clusterDataList.add(cluster);
            clusterDTO.setData(clusterDataList);
        } else
            clusterDTO.setData(clusterDataList);
        return clusterDTO;
    }*/


    public void createCluster(ClusterRequest clusterRequest) throws Exception {
        if (Objects.nonNull(clusterRequest.getClusterName()) && clusterRequest.getClusterName().startsWith(DEFAULT_COMPARTMENT)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster Name cannot be started with DEFAULT");
        }

        if (Objects.nonNull(clusterRequest.getDescription()) && clusterRequest.getDescription().length() >= 255) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster description length cannot be more than 255 Character");
        }

        if (Objects.nonNull(clusterRequest.getAddedAps()) && !clusterRequest.getAddedAps().isEmpty()) {

            HashMap<String, Object> param = new HashMap<>();
            param.put("name", clusterRequest.getClusterName());

            List<ClusterInfo> clusterInfoList = (List<ClusterInfo>) dataAccessService.read(ClusterInfo.class, ClusterInfoSQL.GET_CLUSTER_INFO_BY_NAME, param);
            if (!clusterInfoList.isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster already exist with same name :: " + clusterRequest.getClusterName());


            ClusterInfo clusterInfo = new ClusterInfo();
            clusterInfo.setId(CommonUtils.generateUUID());
            clusterInfo.setName(clusterRequest.getClusterName());
            clusterInfo.setDescription(clusterRequest.getDescription());
            clusterInfo.setDefaultCluster(false);

            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
            if (Objects.isNull(compartment))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group does not exist for the LoggedIn user.");

            compartment.getCluster().add(clusterInfo);

            CommonUtils.setUpdateEntityFields(compartment);
            CommonUtils.setCreateEntityFields(clusterInfo);
            dataAccessService.update(Compartment.class, compartment);

            HashMap<String, Object> queryParams = new HashMap<>();
            clusterRequest.getAddedAps().forEach(item -> {
                try {
                    Equipment equipment = manageCommonService.getEquipmentByEquipmentId(item);
                    if (Objects.nonNull(equipment)) {
                        queryParams.clear();
                        queryParams.put("clusterId", clusterInfo.getId());
                        queryParams.put("equipmentId", equipment.getId());

                        dataAccessService.deleteUpdateNative(MAPPING_OF_CLUSTER_AND_AP, queryParams);

                        Map<String, Object> equipmentData = esService.getById(EQUIPMENT_INDEX, equipment.getId(), null);
                        if (Objects.nonNull(equipmentData)) {
                            List<String> clusterList = (List<String>) equipmentData.get("clusters");
                            clusterList.add(clusterInfo.getId());
                            HashMap<String, Object> preparedData = manageCommonService.prepareDataForES(equipment);
                            preparedData.put("clusters", clusterList);
                            esService.updateById(EQUIPMENT_INDEX, preparedData, equipment.getId(), null);
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Error Mapping with Cluster_userAp", e);
                }
            });
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No users is selected for cluster.Please select any users.");
        }
    }


    public void updateClusterInfoForESMysql(String id, EditCluster editCluster) throws Exception {

        if (Objects.isNull(id))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster ID cannot be Null");

        ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, id);

        if (Objects.isNull(clusterInfo))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + id);

        if (Objects.nonNull(editCluster.getDescription()) && editCluster.getDescription().length() >= 255) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster description length cannot be more than 255 Character");
        }

        if ((DEFAULT_COMPARTMENT.equals(clusterInfo.getName()) || clusterInfo.isDefaultCluster()))
            throw new ValidationException(HttpStatus.UNPROCESSABLE_ENTITY.value(), "Default Cluster cannot be edited");

        if (Objects.nonNull(editCluster.getClusterName())) {

            if (editCluster.getClusterName().isEmpty()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster name cannot be empty");
            }
            HashMap<String, Object> query = new HashMap<>();
            query.put("name", editCluster.getClusterName());

            List<ClusterInfo> clusterInfoList = (List<ClusterInfo>) dataAccessService.read(ClusterInfo.class, ClusterInfoSQL.GET_CLUSTER_INFO_BY_NAME, query);
            if (!clusterInfoList.isEmpty() && !clusterInfoList.get(0).getId().equals(clusterInfo.getId()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster already exist with same name :: " + editCluster.getClusterName());
        }

        try {
            editClusterInfo(clusterInfo, editCluster);
            if (!clusterInfo.isDefaultCluster()) {
                if (Objects.nonNull(editCluster.getRemovedAps()) && !editCluster.getRemovedAps().isEmpty()) {
                    HashMap<String, Object> queryParams = new HashMap<>();
                    editCluster.getRemovedAps().forEach(element -> {
                        try {
                            Equipment equipment = manageCommonService.getEquipmentByEquipmentId(element);
                            if (Objects.nonNull(equipment)) {
                                Map<String, Object> equipmentData = esService.getById(EQUIPMENT_INDEX, equipment.getId(), null);
                                if (Objects.nonNull(equipmentData)) {
                                    List<String> clusterList = (List<String>) equipmentData.get("clusters");
                                    clusterList.remove(clusterInfo.getId());
                                    HashMap<String, Object> preparedData = manageCommonService.prepareDataForES(equipment);
                                    preparedData.put("clusters", clusterList);
                                    esService.updateById(EQUIPMENT_INDEX, preparedData, equipment.getId(), null);
                                }
                            }
                        } catch (Exception e) {
                            LOG.error("Error while removing user from cluster");
                        }

                    });
                }

                if (Objects.nonNull(editCluster.getAddedAps()) && !editCluster.getAddedAps().isEmpty()) {
                    HashMap<String, Object> queryParams = new HashMap<>();
                    editCluster.getAddedAps().forEach(element -> {
                        try {
                            Equipment equipment = manageCommonService.getEquipmentByEquipmentId(element);  //remove for large data
                            if (Objects.nonNull(equipment)) {
                                Map<String, Object> equipmentData = esService.getById(EQUIPMENT_INDEX, equipment.getId(), null);
                                if (Objects.nonNull(equipmentData)) {
                                    List<String> clusterList = (List<String>) equipmentData.get("clusters");
                                    clusterList.add(clusterInfo.getId());
                                    HashMap<String, Object> preparedData = manageCommonService.prepareDataForES(equipment);
                                    preparedData.put("clusters", clusterList);
                                    esService.updateById(EQUIPMENT_INDEX, preparedData, equipment.getId(), null);
                                }
                            }
                        } catch (Exception e) {
                            LOG.error("Error while adding user on cluster", e);
                        }
                    });
                }
            }
        } catch (ValidationException e) {
            throw e;
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.UNPROCESSABLE_ENTITY.value(), "Unable to update cluster");
        }
    }

    /*private void updateSubscriberDetailsOnES(UserAP userAP, User user, List<String> clusterList) {
        HashMap<String, Object> preparedData = esServiceImpl.prepareDataForES(userAP, user);
        preparedData.put("clusters", clusterList);
        try {
            esService.updateById(USERAP_INDEX, preparedData, user.getId(), null);
        } catch (Exception e) {
            LOG.error("Error while updating Subscriber");
        }
    }*/


    @Transactional
    public void editClusterInfo(ClusterInfo clusterInfo, EditCluster editCluster) throws Exception {
        if (!clusterInfo.isDefaultCluster()) {
            if (Objects.nonNull(editCluster.getRemovedAps()) && !editCluster.getRemovedAps().isEmpty()) {
                editCluster.getRemovedAps().forEach(element -> {
                    try {
                        HashMap<String, Object> param = new HashMap<>();
                        param.put("clusterId", clusterInfo.getId());
                        param.put("userId", element);
                        dataAccessService.deleteUpdateNative(ClusterInfoSQL.DELETE_USER_AP_FROM_CLUSTER, param);
                    } catch (Exception e) {
                        LOG.error("Error while removing user from cluster");
                    }

                });
            }

            if (Objects.nonNull(editCluster.getAddedAps()) && !editCluster.getAddedAps().isEmpty()) {
                editCluster.getAddedAps().forEach(element -> {
                    try {
                        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(element);  //remove for large data
                        if (Objects.nonNull(equipment)) {
                            HashMap<String, Object> queryParams = new HashMap<>();
                            queryParams.put("clusterId", clusterInfo.getId());
                            queryParams.put("userId", equipment.getId());
                            dataAccessService.deleteUpdateNative(ClusterInfoSQL.MAPPING_OF_CLUSTER_AND_AP, queryParams);

                            queryParams.clear();
                        }
                    } catch (Exception e) {
                        LOG.error("Error while adding user on cluster", e);
                    }
                });
            }
        }


        if (Objects.nonNull(editCluster.getClusterName()) && !editCluster.getClusterName().trim().isEmpty())
            clusterInfo.setName(editCluster.getClusterName());
        if (Objects.nonNull(editCluster.getDescription()))
            clusterInfo.setDescription(editCluster.getDescription());


        dataAccessService.update(ClusterInfo.class, clusterInfo);
    }

    @Transactional
    public void deleteClusterInfo(String id, boolean isGroupDelete) throws Exception {
        if (Objects.isNull(id))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster ID cannot be Null");

        ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, id);

        if (Objects.isNull(clusterInfo))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist with cluster Id :: " + id);

        String clusterName = clusterInfo.getName();
        if (!isGroupDelete && (clusterName.startsWith(DEFAULT_COMPARTMENT) || clusterInfo.isDefaultCluster())) {
            throw new ValidationException(HttpStatus.UNPROCESSABLE_ENTITY.value(), "DEFAULT Cluster can not be deleted");
        }

        HashMap<String, Object> params = new HashMap<>();
        params.put("clusterId", id);
        List<Job> job = (List<Job>) dataAccessService.read(Job.class, ScheduleJobSQL.GET_SCHEDULE_JOB_BY_CLUSTER_ID, params);
        if (!job.isEmpty() && !isGroupDelete)
            throw new ValidationException(HttpStatus.UNPROCESSABLE_ENTITY.value(), "Cluster is linked to Schedule, hence cannot be deleted.");

        HashMap<String, Object> param = new HashMap<>();
        param.put("clusterId", clusterInfo.getId());
        dataAccessService.deleteUpdateNative(ClusterInfoSQL.DELETE_ALL_USER_AP_BY_CLUSTER_ID, param);
        dataAccessService.delete(ClusterInfo.class, id);

        /*List<String> userId = dataAccessService.readNative(ClusterInfoSQL.GET_USER_ID_BY_CLUSTER_ID, param);
        Iterator iterator = userId.iterator();
        while (iterator.hasNext()) {
            UserAP userAP = (UserAP) dataAccessService.read(UserAP.class, (String) iterator.next());
            if (Objects.nonNull(userAP)) {
                User user = (User) dataAccessService.read(User.class, userAP.getUserId());
                Map<String, Object> subscriberData = esService.getById(USERAP_INDEX, userAP.getUserId(), null);
                if (Objects.nonNull(subscriberData)) {
                    List<String> clusterList = (List<String>) subscriberData.get("clusters");
                    clusterList.remove(clusterInfo.getId());
                    HashMap<String, Object> preparedData = esServiceImpl.prepareDataForES(userAP, user);
                    preparedData.put("clusters", clusterList);
                    esService.updateById(USERAP_INDEX, preparedData, user.getId(), null);
                }
            }
        }

        //TODO remove cluster ID from Policy

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        List<DBObject> policies = mongoService.findList(null, null, NETWORK_POLICY, TIMESTAMP, 0, DESC, mongoFieldOptions);
        final String clusterIdToFilter = id;
        for (DBObject policy : policies) {
            if (Objects.nonNull(policy.get("applyToCluster"))) {
                String clusterIdExist = ((List<String>) policy.get("applyToCluster")).stream().filter(cluster -> cluster.equals(clusterIdToFilter)).findAny().orElse(null);
                if (Objects.nonNull(clusterIdExist)) {
                    HashSet<String> applyTo = new HashSet<>();
                    List<String> applyToClusterList = (List<String>) policy.get("applyToCluster");
                    applyToClusterList.remove(id);
                    for (String clusterId : applyToClusterList) {
                        ClustersDTO clustersDTO = getClusterListForUi(clusterId);
                        ArrayList<ClustersDTO.ClustersDetail> clustersDetailArrayList = clustersDTO.getData();
                        if (Objects.nonNull(clustersDetailArrayList) && !clustersDetailArrayList.isEmpty())
                            applyTo.addAll(new ArrayList<>());
                    }

                    DBObject where = new BasicDBObject().append("id", policy.get("id"));
                    DBObject updateMaps = new BasicDBObject().append("$set", new BasicDBObject().append("applyTo", applyTo).append("applyToCluster", applyToClusterList));
                    mongoService.update(where, updateMaps, true, false, NETWORK_POLICY);
                }
            }
        }

*/
    }
}
