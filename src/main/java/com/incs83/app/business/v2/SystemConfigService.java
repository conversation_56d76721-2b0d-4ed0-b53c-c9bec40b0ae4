package com.incs83.app.business.v2;

import com.incs83.app.entities.SystemConfig;
import com.incs83.app.responsedto.v2.System.SystemConfigDTO;
import com.incs83.mt.DataAccessService;
import com.incs83.request.SystemConfigRequest;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class SystemConfigService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private HazelcastService cacheService;

    public SystemConfigDTO createSystemConfig(SystemConfigRequest systemConfigRequest) {
        SystemConfig sc;
        sc = SystemConfig.setSystemConfigVariables(systemConfigRequest);
        try {
            CommonUtils.setCreateEntityFields(sc);
            CommonUtils.setUpdateEntityFields(sc);
            dataAccessService.create(SystemConfig.class, sc);
        } catch (Exception e) {
            LOG.error("Error while trying to save a system configuration");
        }
        cacheService.delete(SYSTEM_CONFIG_KEY, SYSTEM_CONFIG);
        return SystemConfigDTO.mapToConfigPropertyDto(sc);
    }

    public SystemConfig systemConfigById(String systemConfigId) {
        SystemConfig systemConfig = null;
        try {
            systemConfig = (SystemConfig) dataAccessService.read(SystemConfig.class, systemConfigId);
        } catch (Exception e) {
            LOG.error("Error while trying to get system configuration by id");
        }

        return systemConfig;
    }

    public List<SystemConfigDTO> getAllSystemConfig() {
        HashMap<String, String> cacheParams = new HashMap<>();
        cacheParams.put(KEY, SYSTEM_CONFIG_KEY);
        cacheParams.put(MAP, SYSTEM_CONFIG);
        List<SystemConfig> systemConfigs = null;
        try {
            systemConfigs = (List<SystemConfig>) dataAccessService.read(cacheParams, SystemConfig.class);
        } catch (Exception e) {
            LOG.error("Error while trying to get all system configuration");
        }

        List<SystemConfigDTO> scDTO = SystemConfigDTO.systemConfigList(systemConfigs);
        Collections.sort(scDTO);

        return scDTO;
    }

    public SystemConfigDTO getSystemConfigById(String systemConfigId) {
        return SystemConfigDTO.mapToConfigPropertyDto(this.systemConfigById(systemConfigId));
    }

    public SystemConfigDTO updateSystemConfig(SystemConfigRequest systemConfigRequest, String systemConfigId) {

        SystemConfig systemConfig = null;
        try {
            systemConfig = SystemConfig.setSystemConfigVariables(systemConfigRequest, this.systemConfigById(systemConfigId));
            if (Objects.nonNull(systemConfig)) {
                CommonUtils.setUpdateEntityFields(systemConfig);
                systemConfig = (SystemConfig) dataAccessService.update(SystemConfig.class, systemConfig);
            } else {
                LOG.error("Record does not exists");
            }
        } catch (Exception e) {
            LOG.error("Error while trying to update system configuration");
        }
        cacheService.delete(SYSTEM_CONFIG_KEY, SYSTEM_CONFIG);
        return SystemConfigDTO.mapToConfigPropertyDto(systemConfig);
    }

    public void deleteSystemConfig(String systemConfigId) {
        try {
            dataAccessService.delete(SystemConfig.class, systemConfigId);
            cacheService.delete(SYSTEM_CONFIG_KEY, SYSTEM_CONFIG);
        } catch (Exception e) {
            LOG.error("Error during delete operation", e);
        }
    }
}
