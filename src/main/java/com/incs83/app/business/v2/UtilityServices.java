package com.incs83.app.business.v2;

import au.com.bytecode.opencsv.CSVReader;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.authResources.MQTTClient;
import com.incs83.app.common.v2.DeviceFriendlyNameRequest;
import com.incs83.app.common.v2.ElasticSearchRequest;
import com.incs83.app.common.v2.EquipmentFriendlyNameRequest;
import com.incs83.app.common.v2.FriendlyNameRequest;
import com.incs83.app.common.v2.IndexTTLRequest;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.queries.ClusterInfoSQL;
import com.incs83.app.entities.ClusterInfo;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.LoginDetails;
import com.incs83.app.entities.Subscriber;
import com.incs83.app.entities.User;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.isp.ISPDetail;
import com.incs83.app.responsedto.v2.isp.ISPModel;
import com.incs83.app.service.components.S3ServiceImpl;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.business.ESService;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.AuthEntityNotAllowedException;
import com.incs83.exceptions.handler.AuthMethodNotSupportedException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.security.tokenFactory.Authority;
import com.incs83.security.tokenFactory.Permission;
import com.incs83.security.tokenFactory.UserContext;
import com.incs83.service.CommonService;
import com.incs83.service.HttpService;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.BasicBSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileReader;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.actiontec.optim.util.MathUtil;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.COLLECTION_METADATA;
import static com.incs83.app.constants.misc.ActiontecConstants.ETL_DEBUG_MINUTES;
import static com.incs83.app.constants.misc.ActiontecConstants.ETL_METADATA;
import static com.incs83.app.constants.misc.ActiontecConstants.ETL_QUERY_PROGRESS;
import static com.incs83.app.constants.misc.ActiontecConstants.INTERNET_SERVICE_PROVIDER;
import static com.incs83.app.constants.misc.ActiontecConstants.MAC_ID_PARENT;
import static com.incs83.app.constants.misc.ActiontecConstants.STATION_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.TTL_EXPIRY_ACTION_HISTORY;
import static com.incs83.app.constants.misc.ApplicationConstants.BUILD_VERSION_INFO;
import static com.incs83.app.constants.misc.ApplicationConstants.CLOSE_BRACKET;
import static com.incs83.app.constants.misc.ApplicationConstants.CLOSE_BRACKET_CURLY;
import static com.incs83.app.constants.misc.ApplicationConstants.CLOSE_BRACKET_SQUARE;
import static com.incs83.app.constants.misc.ApplicationConstants.COLLECTIONS;
import static com.incs83.app.constants.misc.ApplicationConstants.COLLECTIONS_KEY;
import static com.incs83.app.constants.misc.ApplicationConstants.DASHBOARD_COUNTS;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.ETL_PROPERTIES;
import static com.incs83.app.constants.misc.ApplicationConstants.ETL_TYPE_HISTORY;
import static com.incs83.app.constants.misc.ApplicationConstants.ETL_TYPE_RPC;
import static com.incs83.app.constants.misc.ApplicationConstants.ETL_TYPE_STREAM;
import static com.incs83.app.constants.misc.ApplicationConstants.EXCLAMATION;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.misc.ApplicationConstants.GLOBAL;
import static com.incs83.app.constants.misc.ApplicationConstants.GREATER_THAN;
import static com.incs83.app.constants.misc.ApplicationConstants.LESS_THAN;
import static com.incs83.app.constants.misc.ApplicationConstants.MAC_VENDOR_DETAIL;
import static com.incs83.app.constants.misc.ApplicationConstants.MQTT_CONNECTION_NAME;
import static com.incs83.app.constants.misc.ApplicationConstants.NETWORK_INFO_KEY;
import static com.incs83.app.constants.misc.ApplicationConstants.NETWORK_INFO_MAP_NAME;
import static com.incs83.app.constants.misc.ApplicationConstants.OPEN_BRACKET;
import static com.incs83.app.constants.misc.ApplicationConstants.OPEN_BRACKET_CURLY;
import static com.incs83.app.constants.misc.ApplicationConstants.OPEN_BRACKET_SQUARE;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_20;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_21;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_27;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_28;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_29;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_3C;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_3E;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_3F;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_5B;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_5D;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_7B;
import static com.incs83.app.constants.misc.ApplicationConstants.PERCENT_SYMBOL_7D;
import static com.incs83.app.constants.misc.ApplicationConstants.QUESTION_MARK;
import static com.incs83.app.constants.misc.ApplicationConstants.SINGLE_QUOTE;
import static com.incs83.app.constants.misc.ApplicationConstants.SPACE;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.VMQ_DEBUG_PASSWORD;
import static com.incs83.app.constants.misc.ApplicationConstants.VMQ_NOTIFICATION_PASSWORD;
import static com.incs83.app.constants.misc.ApplicationConstants.WIRE_SHARK_URL;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_COUNT_BY_GROUP;
import static com.incs83.app.constants.queries.SubscriberEquipmentSQL.GET_SUBSCRIBER_ID_BY_EQUIPMENT_ID;
import static com.incs83.constants.ApplicationCommonConstants.DEBUG_CONFIG;
import static com.incs83.constants.ApplicationCommonConstants.MONGO_DEBUG_MS;
import static com.incs83.constants.ApplicationCommonConstants.MYSQL_DEBUG_MS;
import static com.incs83.constants.ApplicationCommonConstants.TOKEN;
import static com.incs83.constants.ApplicationCommonConstants.USER_INDEX;

@Service
public class UtilityServices {

    private static final Logger LOG = LogManager.getLogger("org");
    @Value("${ouiUrl}")
    private String ouiUrl;

    @Autowired
    private MongoServiceImpl mongoServiceImpl;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private ManageDeviceService manageDeviceService;
    @Autowired
    private ManageEquipmentService manageEquipmentService;
    @Autowired
    private IAMServices IAMServices;
    @Autowired
    private HttpService httpService;
    @Autowired
    private Environment environment;
    @Autowired
    private ISPService ispService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ManageApplicationStatsService applicationStatsService;
    @Autowired
    private ESService esService;
    @Autowired
    private HazelcastService cacheService;
    @Autowired
    private S3ServiceImpl s3Service;
    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;

    public long getQueryMaxTimeMongo() {
        return Long.valueOf(commonService.read(DEBUG_CONFIG).get(MONGO_DEBUG_MS));
    }

    public long getQueryMaxTimeMysql() {
        return Long.valueOf(commonService.read(DEBUG_CONFIG).get(MYSQL_DEBUG_MS));
    }

    public HashMap<String, Object> getSubscriberDetails() throws Exception {
        HashMap<String, Object> subscriberDetails = new HashMap<>();
        String subscriberId = ExecutionContext.get().getUsercontext().getId();
        subscriberDetails.put("subscriberId", subscriberId);

        HashMap<String, Object> userData = (HashMap<String, Object>) esService.getById(USER_INDEX, subscriberId, null);

        String name;

        if (Objects.isNull(userData) || userData.isEmpty()) {
            User user = (User) dataAccessService.read(User.class, subscriberId);
            name = Objects.nonNull(user) ? (user.getFirstName() + SPACE + user.getLastName()) : EMPTY_STRING;
        } else {
            name = Objects.nonNull(userData.get("name")) ? userData.get("name").toString() : EMPTY_STRING;
        }
        subscriberDetails.put("name", name);

        Calendar calendar = Calendar.getInstance();
        long lastLogin = calendar.getTimeInMillis();
        HashMap<String, Object> token = (HashMap<String, Object>) cacheService.read(subscriberId, TOKEN);
        if (Objects.isNull(token)) {
            LoginDetails loginDetails = (LoginDetails) dataAccessService.read(LoginDetails.class, subscriberId);
            if (Objects.nonNull(loginDetails) && Objects.nonNull(loginDetails.getTokenCreatedAt())) {
                lastLogin = loginDetails.getTokenCreatedAt().getTime();
            }
        } else if (!token.isEmpty() && Objects.nonNull(token.get("createdAt"))) {
            lastLogin = Long.valueOf(token.get("createdAt").toString());
        }

        subscriberDetails.put("lastLogin", lastLogin);

        return subscriberDetails;
    }

    public void upFriendlyNamesBulk(MultipartFile file, String type) throws Exception {
        List<FriendlyNameRequest> friendlyNameRequestList = new ArrayList<>();
        if (null == file || file.isEmpty()) {
            throw new ApiException(ApiResponseCode.BAD_REQUEST);
        }
        File fileOnDisk = new File("/tmp/" + Calendar.getInstance().getTimeInMillis() + "_" + file.getOriginalFilename());
        FileUtils.writeByteArrayToFile(fileOnDisk, file.getBytes());
        CSVReader csvReader = new CSVReader(new FileReader(fileOnDisk));
        if (type.equals("device")) {
            String[] row;
            boolean firstRow = true;
            while ((row = csvReader.readNext()) != null) {
                if (firstRow) {
                    firstRow = false;
                    continue;
                } else {
                    for (int i = 0; i < row.length; i++) {
                        ObjectMapper mapper = new ObjectMapper();
                        Map<String, Object> map;
                        map = mapper.readValue(row[row.length - 1], new TypeReference<Map<String, String>>() {
                        });
                        map.forEach((key, value) -> {
                            FriendlyNameRequest friendlyNameRequest = new FriendlyNameRequest();
                            friendlyNameRequest.setFriendlyName(value.toString()
                                    .replaceAll(PERCENT_SYMBOL_20, SPACE)
                                    .replaceAll(PERCENT_SYMBOL_27, SINGLE_QUOTE)
                                    .replaceAll(PERCENT_SYMBOL_28, OPEN_BRACKET)
                                    .replaceAll(PERCENT_SYMBOL_29, CLOSE_BRACKET)
                                    .replaceAll(PERCENT_SYMBOL_21, EXCLAMATION)
                                    .replaceAll(PERCENT_SYMBOL_3C, LESS_THAN)
                                    .replaceAll(PERCENT_SYMBOL_5D, CLOSE_BRACKET_SQUARE)
                                    .replaceAll(PERCENT_SYMBOL_7D, CLOSE_BRACKET_CURLY)
                                    .replaceAll(PERCENT_SYMBOL_7B, OPEN_BRACKET_CURLY)
                                    .replaceAll(PERCENT_SYMBOL_5B, OPEN_BRACKET_SQUARE)
                                    .replaceAll(PERCENT_SYMBOL_3E, GREATER_THAN)
                                    .replaceAll(PERCENT_SYMBOL_3F, QUESTION_MARK)
                            );
                            friendlyNameRequest.setMacAddress(key);
                            friendlyNameRequestList.add(friendlyNameRequest);
                        });
                    }
                }
            }
            fileOnDisk.delete();
            friendlyNameRequestList.forEach(item -> {
                DeviceFriendlyNameRequest deviceFriendlyNameRequest = new DeviceFriendlyNameRequest();
                deviceFriendlyNameRequest.setMacAddress(item.getMacAddress());
                deviceFriendlyNameRequest.setFriendlyName(item.getFriendlyName());
                try {
                    manageDeviceService.updateFriendlyNameForUserDevice(deviceFriendlyNameRequest, null);
                } catch (Exception e) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
                }

            });
        } else if (type.equals("equipment")) {
            String[] row;
            boolean firstRow = true;
            while ((row = csvReader.readNext()) != null) {
                if (firstRow) {
                    firstRow = false;
                    continue;
                } else {
                    if (!row[3].toString().equals(EMPTY_STRING)) {
                        FriendlyNameRequest friendlyNameRequest = new FriendlyNameRequest();
                        friendlyNameRequest.setFriendlyName(row[3].toString()
                                .replaceAll(PERCENT_SYMBOL_20, SPACE)
                                .replaceAll(PERCENT_SYMBOL_27, SINGLE_QUOTE)
                                .replaceAll(PERCENT_SYMBOL_28, OPEN_BRACKET)
                                .replaceAll(PERCENT_SYMBOL_29, CLOSE_BRACKET)
                                .replaceAll(PERCENT_SYMBOL_21, EXCLAMATION)
                                .replaceAll(PERCENT_SYMBOL_3C, LESS_THAN)
                                .replaceAll(PERCENT_SYMBOL_5D, CLOSE_BRACKET_SQUARE)
                                .replaceAll(PERCENT_SYMBOL_7D, CLOSE_BRACKET_CURLY)
                                .replaceAll(PERCENT_SYMBOL_7B, OPEN_BRACKET_CURLY)
                                .replaceAll(PERCENT_SYMBOL_5B, OPEN_BRACKET_SQUARE)
                                .replaceAll(PERCENT_SYMBOL_3E, GREATER_THAN)
                                .replaceAll(PERCENT_SYMBOL_3F, QUESTION_MARK)
                        );
                        friendlyNameRequest.setSerialNumber(row[1]);
                        friendlyNameRequestList.add(friendlyNameRequest);
                    }
                }
            }
            fileOnDisk.delete();
            friendlyNameRequestList.forEach(item -> {
                EquipmentFriendlyNameRequest equipmentFriendlyNameRequest = new EquipmentFriendlyNameRequest();
                equipmentFriendlyNameRequest.setFriendlyName(item.getFriendlyName());
                equipmentFriendlyNameRequest.setSerialNumber(item.getSerialNumber());

                try {
                    manageEquipmentService.updateFriendlyNameForEquipment(item.getSerialNumber(), equipmentFriendlyNameRequest);
                } catch (Exception e) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), e.getMessage());
                }
            });
        }
    }

    /*public Object getEquipmentDetails(String serialNumber) throws Exception {
        if (CommonUtils.isEndUser()) {
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        }

        HashMap<String, Object> equipmentDetail = new HashMap<>();
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();

        if (ValidationUtil.validateMAC(serialNumber)) {
            queryParams.put("macAddress", serialNumber);
        } else {
            queryParams.put("userId", serialNumber);
            queryParams.put("serialNumber", serialNumber);
        }

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("moca", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("bssid24G", 0);
        mongoFieldOptions.put("bssid5G", 0);
        mongoFieldOptions.put("mocaDevices", 0);


        DBObject aPDetails = mongoServiceImpl.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment not found with RGW Serial/MAC " + serialNumber);

        if (!CommonUtils.isSysAdmin()) {
            String compartmentId = CommonUtils.getGroupIdOfLoggedInUser();
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, compartmentId);

            if (Objects.nonNull(compartment)) {
                ISPDTO ispdto = ispService.getISPById(compartment.getIspId());
                ISPModel ispModel = ispdto.getData();
                if (!ispModel.getName().equalsIgnoreCase(aPDetails.get("isp").toString()))
                    throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }


        if (GATEWAY.equals(String.valueOf(aPDetails.get("type")))) {
            queryParams.clear();
            queryParams.put("userId", aPDetails.get("userId").toString());

            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);
            mongoFieldOptions.put("wan", 0);
            mongoFieldOptions.put("moca", 0);
            mongoFieldOptions.put("ethPorts", 0);
            mongoFieldOptions.put("bssid24G", 0);
            mongoFieldOptions.put("bssid5G", 0);
            mongoFieldOptions.put("mocaDevices", 0);

            List<DBObject> apDetailList = mongoServiceImpl.findList(queryParams, AP_DETAIL, mongoFieldOptions);
            apDetailList = apDetailList.stream().filter(apDetail -> !GATEWAY.equals(String.valueOf(apDetail.get("type")))).collect(Collectors.toList());

            equipmentDetail.put("connectedExtender", apDetailList.size());
        }

        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "serialNumber");
        inClause.put("resource", Arrays.asList(String.valueOf(aPDetails.get("serialNumber"))));
        inParams.put("in", inClause);

        HashMap<String, Object> where = new HashMap<>();
        where.put("userId", aPDetails.get("userId"));

        long connectedDevices = mongoServiceImpl.distinctCount(where, inParams, STATION_DETAIL, "macAddress");

        aPDetails.put("connectivityStatus", manageCommonService.getConnectivityStatusForEquipment(aPDetails));
        aPDetails.put("uptime", CommonUtils.getHistoricalTimeFromDate(Long.parseLong(Objects.isNull(aPDetails.get("uptime")) ? "0" : aPDetails.get("uptime").toString()) * 1000));

        equipmentDetail.put("connectedDevice", connectedDevices);
        equipmentDetail.put("equipmentInfo", aPDetails);

        try {
            Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(aPDetails.get("userId").toString());
            HashMap<String, String> query = new HashMap<>();
            query.put("equipmentId", userEquipment.getId());

            List<Object[]> list = dataAccessService.readNative(GET_SUBSCRIBER_ID_BY_EQUIPMENT_ID, query);

            String subscriberId = (Objects.nonNull(list) && !list.isEmpty()) ? list.iterator().next().toString() : null;
            Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, subscriberId);
            if (Objects.nonNull(subscriber) && !subscriber.getCompartment().isEmpty()) {
                String groupName = EMPTY_STRING;
                DBObject ispInfo = new BasicDBObject();

                Compartment compartment = subscriber.getCompartment().iterator().next();
                if (Objects.nonNull(compartment)) {
                    groupName = compartment.getName();

                    ISPDTO ispdto = ispService.getISPById(compartment.getIspId());
                    ISPModel ispModel = ispdto.getData();
                    ispInfo.put("configuredISP", ispModel.getName());
                }
                ispInfo.put("actualISP", aPDetails.get("isp"));

                DBObject subscriberRgw = new BasicDBObject();
                subscriberRgw.put("email", subscriber.getEmail());
                subscriberRgw.put("name", subscriber.getFirstName() + EMPTY_STRING + subscriber.getLastName());
                subscriberRgw.put("group", groupName);
                subscriberRgw.put("mqttInfo", MQTT_CONNECTION_NAME);
                subscriberRgw.put("subscriberId", subscriber.getId());//TODO checks subscriberId for UI
                subscriberRgw.put("serialNumber", userEquipment.getRgwSerial());

                equipmentDetail.put("subscriberInfo", subscriber);
                equipmentDetail.put("ispInfo", ispInfo);

            }
        } catch (Exception e) {
            equipmentDetail.put("subscriberInfo", null);
            equipmentDetail.put("ispInfo", null);
        }

        return equipmentDetail;
    }*/

    public Object searchEquipmentDetails(ElasticSearchRequest equipmentSearchDTO) throws Exception {

        if (CommonUtils.isEndUser()) {
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        }

        HashMap<String, Object> equipmentDetail = new HashMap<>();
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();

        if (Objects.nonNull(equipmentSearchDTO.getRgwMAC()) && Objects.nonNull(equipmentSearchDTO.getApId())) {
            queryParams.put("userId", equipmentSearchDTO.getApId());
            queryParams.put("serialNumber", equipmentSearchDTO.getApId());
        } else if (Objects.nonNull(equipmentSearchDTO.getApId()) && Objects.nonNull(equipmentSearchDTO.getExtSerial())) {
            queryParams.put("userId", equipmentSearchDTO.getApId());
            queryParams.put("serialNumber", equipmentSearchDTO.getExtSerial());
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Record found for search result");
        }

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("moca", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("bssid24G", 0);
        mongoFieldOptions.put("bssid5G", 0);
        mongoFieldOptions.put("mocaDevices", 0);


        DBObject aPDetails = mongoServiceImpl.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.isNull(aPDetails))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Record found for search result");

        if (!CommonUtils.isSysAdmin()) {
            String compartmentId = CommonUtils.getGroupIdOfLoggedInUser();
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, compartmentId);

            if (Objects.nonNull(compartment)) {
                ISPDTO ispdto = ispService.getISPById(compartment.getIspId());
                ISPModel ispModel = ispdto.getData();
                if (!ispModel.getName().equalsIgnoreCase(aPDetails.get("isp").toString()))
                    throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
            }
        }


        if (GATEWAY.equals(String.valueOf(aPDetails.get("type")))) {
            queryParams.clear();
            queryParams.put("userId", aPDetails.get("userId").toString());

            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);
            mongoFieldOptions.put("wan", 0);
            mongoFieldOptions.put("moca", 0);
            mongoFieldOptions.put("ethPorts", 0);
            mongoFieldOptions.put("bssid24G", 0);
            mongoFieldOptions.put("bssid5G", 0);
            mongoFieldOptions.put("mocaDevices", 0);

            List<DBObject> apDetailList = mongoServiceImpl.findList(queryParams, AP_DETAIL, mongoFieldOptions);
            apDetailList = apDetailList.stream().filter(apDetail -> !GATEWAY.equals(String.valueOf(apDetail.get("type")))).collect(Collectors.toList());

            equipmentDetail.put("connectedExtender", apDetailList.size());
        }

        HashMap<String, Object> inParams = new HashMap<>();
        HashMap<String, Object> inClause = new HashMap<>();
        inClause.put("resourceName", "serialNumber");
        inClause.put("resource", Arrays.asList(String.valueOf(aPDetails.get("serialNumber"))));
        inParams.put("in", inClause);

        HashMap<String, Object> where = new HashMap<>();
        where.put("userId", aPDetails.get("userId"));

        long connectedDevices = mongoServiceImpl.distinctCount(where, inParams, STATION_DETAIL, "macAddress");

        aPDetails.put("connectivityStatus", manageCommonService.getConnectivityStatusForEquipment(aPDetails));
        aPDetails.put("uptime", CommonUtils.getHistoricalTimeFromDate(Long.parseLong(Objects.isNull(aPDetails.get("uptime")) ? "0" : aPDetails.get("uptime").toString()) * 1000));

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        equipmentDetail.put("connectedDevice", connectedDevices);

        if (dataSecurityMapping.contains(DataSecurityType.macAddress.name())) {
            aPDetails.put("macAddress", manageCommonService.encrypt());
        } else {
            aPDetails.put("macAddress", aPDetails.get("macAddress"));
        }
        equipmentDetail.put("equipmentInfo", aPDetails);
        try {
            Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(aPDetails.get("userId").toString());
            if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

            HashMap<String, String> query = new HashMap<>();
            query.put("id", userEquipment.getId());

            String subscriberId = dataAccessService.readNative(GET_SUBSCRIBER_ID_BY_EQUIPMENT_ID, query).iterator().next().toString();

            Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, subscriberId);
            if (Objects.nonNull(subscriber) && !subscriber.getCompartment().isEmpty()) {
                String groupName = EMPTY_STRING;
                DBObject ispInfo = new BasicDBObject();

                Compartment compartment = subscriber.getCompartment().iterator().next();
                if (Objects.nonNull(compartment)) {
                    groupName = compartment.getName();

                    ISPDTO ispdto = ispService.getISPById(compartment.getIspId());
                    ISPModel ispModel = ispdto.getData();
                    ispInfo.put("configuredISP", ispModel.getName());
                }
                ispInfo.put("actualISP", aPDetails.get("isp"));

                DBObject subscriberRgw = new BasicDBObject();
                subscriberRgw.put("email", subscriber.getEmail());
                subscriberRgw.put("name", subscriber.getFirstName() + " " + subscriber.getLastName());
                subscriberRgw.put("group", groupName);
                subscriberRgw.put("mqttInfo", MQTT_CONNECTION_NAME);
                subscriberRgw.put("subscriberId", subscriber.getId());
                subscriberRgw.put("serialNumber", userEquipment.getRgwSerial());

                equipmentDetail.put("subscriberInfo", subscriberRgw);
                equipmentDetail.put("ispInfo", ispInfo);

            }
        } catch (Exception e) {
            equipmentDetail.put("subscriberInfo", null);
            equipmentDetail.put("ispInfo", null);
        }

        return equipmentDetail;
    }


    private String getISPByCompartmentID(String compartmentId) throws Exception {
        String ispName = null;
        HashMap<String, Object> queryParams = new HashMap<>();
        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, compartmentId);
        if (Objects.nonNull(compartment)) {
            queryParams.clear();
            queryParams.put("id", compartment.getIspId());

            HashMap<String, Object> appendableParams = new HashMap<>();
            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("_id", ZERO);

            DBObject dbObject = mongoServiceImpl.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
            if (Objects.nonNull(dbObject)) {
                ispName = String.valueOf(dbObject.get("name"));
            }
        }

        return ispName;
    }

    public HashMap<String, Object> getNetworkInfoForCluster() throws Exception {
        HashMap<String, Object> data = new HashMap<>();

        HashMap<String, Object> response = (HashMap<String, Object>) cacheService.read(NETWORK_INFO_KEY, NETWORK_INFO_MAP_NAME);

        if (Objects.isNull(response)) {
            HashMap<String, Object> query = new HashMap<>();
            ArrayList<ISPDetail> ispDetailDataList = new ArrayList<>();
            ISPDTOList ispdtoList = ispService.getAllISPs();
            if (Objects.nonNull(ispdtoList) && !ispdtoList.getData().isEmpty()) {
                ispdtoList.getData().forEach(isp -> {
                    ISPDetail ispDetail = new ISPDetail();
                    ispDetail.setIspName(isp.getName());

                    query.clear();
                    query.put("isp", isp.getName());
                    query.put("type", GATEWAY);
                    long totalAp = mongoServiceImpl.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                    query.clear();
                    query.put("isp", isp.getName());
                    query.put("type", GATEWAY);
                    query.put("mysqlProcessed", new BasicDBObject("$exists", true));
                    query.put("processed", new BasicDBObject("$exists", true));
                    long totalSubscriber = mongoServiceImpl.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                    query.clear();
                    query.put("isp", isp.getName());
                    query.put("type", GATEWAY);
                    query.put("processed", new BasicDBObject("$exists", true));
                    long totalScannedRecord = mongoServiceImpl.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                    ispDetail.setMapped(totalSubscriber);
                    ispDetail.setUnMapped(totalAp - totalScannedRecord);
                    ispDetail.setFailedRecord(totalScannedRecord - totalSubscriber);

                    ispDetailDataList.add(ispDetail);
                });
            }

            data.put("ispDetails", ispDetailDataList);
            query.clear();
            query.put("type", GATEWAY);
            long rgwCount = mongoServiceImpl.count(query, new HashMap<>(), AP_DETAIL);
            data.put("totalRGW", rgwCount);

        } else {
            if (!CommonUtils.isSysAdmin()) {
                String compartmentId = CommonUtils.getGroupIdOfLoggedInUser();
                String isp = getISPByCompartmentID(compartmentId);
                data.put("totalRGW", response.get("totalRGW"));
                ArrayList<ISPDetail> ispDetailDataList = (ArrayList<ISPDetail>) response.get("ispDetails");
                for (ISPDetail ispDetail : ispDetailDataList) {
                    if (ispDetail.getIspName().equals(isp)) {
                        ArrayList<ISPDetail> ispDetails = new ArrayList<>();
                        ispDetails.add(ispDetail);
                        data.put("ispDetails", ispDetails);
                        break;
                    }
                }
            } else {
                data = response;
            }
        }

        return data;
    }

    /*public HashMap<String, Object> getSubscriberDetailsBySerialNumber(String serialNumber) {
        List<UserAP> userApList = new ArrayList<>();
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, Object> resp = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("serialNumber", serialNumber);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        DBObject aPDetails = mongoServiceImpl.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(aPDetails) && Objects.nonNull(aPDetails.get("userId"))) {
            String userId = aPDetails.get("userId").toString();
            queryParams.clear();
            queryParams.put("apId", userId);
            try {
                userApList = (List<UserAP>) dataAccessService.read(UserAP.class, UserAPSQL.GET_PLATFORM_USER_ID_FROM_AP_ID, queryParams);
            } catch (Exception e) {
                LOG.error("Error during fetch result from DB", e);
            }
            if (userApList.isEmpty()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial Number: " + serialNumber + " does not belong to any Subscriber");
            } else {
                resp.put("subscriberId", userApList.get(0).getUserId());
                try {
                    UserDetailDTO userDetailDTO = IAMServices.getUserById(userApList.get(0).getUserId());
                    UserDetailDTO.UserDetail user = userDetailDTO.getData();
                    if (Objects.nonNull(user)) {
                        resp.put("name", user.getFirstName() + " " + user.getLastName());
                    }
                } catch (Exception e) {
                    resp.put("name", "N/A");
                }

            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial Number: " + serialNumber + " does not exist");
        }

        return resp;
    }*/

    public List<DBObject> getAllVoceSubscriber() {
        BasicDBObject fieldsToremove = new BasicDBObject();
        fieldsToremove.put("_id", 0);
        return mongoServiceImpl.findList(new HashMap<>(), ActiontecConstants.ALEX_AND_GOOGLEHOME_SUBSCRIBERS, fieldsToremove);
    }

    public long getVendorCount() {
        return mongoServiceImpl.count(new HashMap<>(), new HashMap<>(), MAC_ID_PARENT);
    }

    public boolean updateOuiToCache(String resp) throws Exception {
        try {
            List<HashMap<String, Object>> macIdParentList = new ArrayList<>();
            String[] st = resp.split("\n");
            for (int i = 0; i < st.length; i++) {
                String line = st[i];
                StringTokenizer stringTokenizer = new StringTokenizer(line, "\t");
                while (stringTokenizer.hasMoreTokens()) {
                    String mac = EMPTY_STRING;
                    String name = EMPTY_STRING;
                    String vendor = EMPTY_STRING;
                    if (stringTokenizer.hasMoreTokens())
                        mac = stringTokenizer.nextToken();
                    if (stringTokenizer.hasMoreTokens())
                        name = stringTokenizer.nextToken();
                    if (stringTokenizer.hasMoreTokens())
                        vendor = stringTokenizer.nextToken();

                    if (!mac.isEmpty()) {
                        HashMap<String, Object> macIdParent = new HashMap<>();
                        macIdParent.put("mac", mac);
                        macIdParent.put("name", name);
                        macIdParent.put("vendor", vendor);

                        macIdParentList.add(macIdParent);
                    }
                }
            }
            if (!macIdParentList.isEmpty()) {
                for (HashMap<String, Object> macDetails : macIdParentList) {
                    BasicDBObject dataToUpdate = new BasicDBObject();
                    dataToUpdate.put("vendor", macDetails.get("vendor").toString().isEmpty() ? macDetails.get("name") : macDetails.get("vendor"));
                    dataToUpdate.put("date", Calendar.getInstance().getTime());

                    cacheService.create(macDetails.get("mac").toString().toLowerCase(), dataToUpdate.get("vendor"), MAC_VENDOR_DETAIL);
                }
            }
        } catch (Exception e) {
            LOG.error("Error update vendor details for station", e);
        }
        return true;
    }

    public void updateOui() throws Exception {
        HashMap<String, String> headerInfo = new HashMap<>();
        HashMap<String, Object> queryParam = new HashMap<>();

        String httpResponse;
        try {
            httpResponse = httpService.doGet(ouiUrl, headerInfo, queryParam);
        } catch (Exception e) {
            throw new RuntimeException("Something went wrong, Failed to Fetch details");
        }

        if (Objects.nonNull(httpResponse) && !httpResponse.trim().isEmpty()) {
            final String resp = httpResponse.replaceAll("\\#.*", "").trim();
            int splitLen = resp.length() / 5;
            int begin = 0;
            int end = 0;
            do {
                end = StringUtils.indexOf(resp, "\n", begin + splitLen);

                String subStr = StringUtils.substring(resp, begin, end);
                manageLongWaitAPI.toFuture(()-> updateOuiToCache(subStr));
                begin = end;
            } while (begin + splitLen < resp.length());

            if (end < resp.length()) {
                String subStr = StringUtils.substring(resp, end);
                manageLongWaitAPI.toFuture(()-> updateOuiToCache(subStr));
            }
        }
    }

    public void updateVendorDetailsForStation() throws Exception {
        HashMap<String, Object> filter = new BasicDBObject();
        BasicDBObject sort = new BasicDBObject();
        sort.put("date", -1);

        BasicDBObject fieldsToRemove = new BasicDBObject();
        DBObject dbObject = mongoServiceImpl.findOne(filter, MAC_ID_PARENT, sort, fieldsToRemove);
        if (Objects.nonNull(dbObject) && Objects.nonNull(dbObject.get("date"))) {
            if (dbObject.get("date") instanceof Date) {
                Date dbDate = (Date) dbObject.get("date");
                Date currentDate = Calendar.getInstance().getTime();

                long lastDay = 24 * 60 * 60 * 1000;

                long diff = currentDate.getTime() - dbDate.getTime();
                if (diff < lastDay)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "This action can be performed only once a day. Please try again tomorrow.");

            }
        }

        HashMap<String, String> headerInfo = new HashMap<>();

        HashMap<String, Object> queryParam = new HashMap<>();
        String url = ouiUrl;

        String httpResponse;
        try {
            httpResponse = httpService.doGet(url, headerInfo, queryParam);
        } catch (Exception e) {
            throw new RuntimeException("Something went wrong, Failed to Fetch details");
        }
        if (Objects.nonNull(httpResponse) && !httpResponse.trim().isEmpty()) {
            final String resp = httpResponse;
            Runnable updateMacDetails = () -> {
                try {
                    List<HashMap<String, Object>> macIdParentList = new ArrayList<>();
                    String result = resp.replaceAll("\\#.*", "").trim();
                    String[] st = result.split("\n");
                    for (int i = 0; i < st.length; i++) {
                        String line = st[i];
                        StringTokenizer stringTokenizer = new StringTokenizer(line, "\t");
                        while (stringTokenizer.hasMoreTokens()) {
                            String mac = EMPTY_STRING;
                            String name = EMPTY_STRING;
                            String vendor = EMPTY_STRING;
                            if (stringTokenizer.hasMoreTokens())
                                mac = stringTokenizer.nextToken();
                            if (stringTokenizer.hasMoreTokens())
                                name = stringTokenizer.nextToken();
                            if (stringTokenizer.hasMoreTokens())
                                vendor = stringTokenizer.nextToken();

                            if (!mac.isEmpty()) {
                                HashMap<String, Object> macIdParent = new HashMap<>();
                                macIdParent.put("mac", mac);
                                macIdParent.put("name", name);
                                macIdParent.put("vendor", vendor);

                                macIdParentList.add(macIdParent);
                            }
                        }
                    }
                    if (!macIdParentList.isEmpty()) {
                        for (HashMap<String, Object> macDetails : macIdParentList) {

                            BasicDBObject query = new BasicDBObject();
                            query.put("macIdOctet", macDetails.get("mac").toString().toLowerCase());

                            BasicDBObject dataToUpdate = new BasicDBObject();
                            dataToUpdate.put("vendor", macDetails.get("vendor").toString().isEmpty() ? macDetails.get("name") : macDetails.get("vendor"));
                            dataToUpdate.put("date", Calendar.getInstance().getTime());

                            BasicDBObject update = new BasicDBObject();
                            update.put("$set", dataToUpdate);

                            mongoServiceImpl.update(query, update, false, true, MAC_ID_PARENT);
                            cacheService.create(String.valueOf(query.get("macIdOctet")), dataToUpdate.get("vendor"), MAC_VENDOR_DETAIL);
                        }
                    }
                } catch (Exception e) {
                    LOG.error("Error update vendor details for station", e);
                }
            };

            new Thread(updateMacDetails).start();
        }
    }

    public List<LinkedHashMap<String, Object>> getAllCollections() {
        String cacheKey = COLLECTIONS_KEY;
        String cacheMap = COLLECTIONS;
        List<LinkedHashMap<String, Object>> response = (List<LinkedHashMap<String, Object>>) cacheService.read(cacheKey, cacheMap);
        if (Objects.isNull(response) || response.isEmpty()) {
            List<LinkedHashMap<String, Object>> mapList = new ArrayList<>();
            try {
                Set<String> results = mongoServiceImpl.getAllCollectionList();
                results.remove("etlQueryProgress");
                results.forEach(value -> {
                    if (StringUtils.startsWith(value, "system.")) {
                        return;
                    }
                    if (Objects.nonNull(mongoServiceImpl.findCollectionIndex(value))) {
                        List<LinkedHashMap> indexes = mongoServiceImpl.getIndexesForCollection(value);
                        if (!indexes.isEmpty()) {
                            LinkedHashMap<String, Object> processedResults = new LinkedHashMap<>();
                            processedResults.put("collectionName", value);
                            processedResults.put("indexes", indexes);
                            mapList.add(processedResults);
                        }
                    }
                });

                mapList.sort((q, q1) -> Long.compare((long) (((LinkedHashMap) ((List) q.get("indexes")).get(0)).get("expireAfterDays")), (long) (((LinkedHashMap) ((List) q1.get("indexes")).get(0)).get("expireAfterDays"))));
                cacheService.create(cacheKey, mapList, cacheMap);

                return mapList;
            } catch (Exception e) {
                LOG.error("Error during fetch result from DB", e);
            }
        }

        response.sort((q, q1) -> Long.compare((long) (((LinkedHashMap) ((List) q.get("indexes")).get(0)).get("expireAfterDays")), (long) (((LinkedHashMap) ((List) q1.get("indexes")).get(0)).get("expireAfterDays"))));

        return response;

    }

    public List<LinkedHashMap<String, Object>> getAllCollectionsWithDesc() {
        return mongoServiceImpl.findList(new HashMap(), COLLECTION_METADATA, new BasicDBObject("_id", ZERO));
    }

    public void updateTTLForIndex(List<IndexTTLRequest> indexTTLRequest) {
        indexTTLRequest.forEach(index -> {
            long daysMillis = TimeUnit.DAYS.toSeconds(index.getTtl());
            long days = index.getTtl();
            if (days >= 1 && days <= 365) {
                Optional<LinkedHashMap> indexInfoOp = mongoServiceImpl.getTtlIndexFromKey(index.getCollection(), index.getIndexField());
                long currentTime = (new Date().getTime()) + (24 * 60 * 60 * 1000);
                if (Objects.isNull(mongoServiceImpl.findCollectionIndex(TTL_EXPIRY_ACTION_HISTORY))) {
                    if (indexInfoOp.isPresent()) {
                        if (index.getTtl() != MathUtil.toLong(indexInfoOp.get().get("expireAfterDays"))) {
                            mongoServiceImpl.updateTTLIndex(index.getCollection(), index.getIndexField(), MathUtil.toInt(indexInfoOp.get().get(index.getIndexField())), daysMillis);
                            DBObject dbObject = new BasicDBObject();
                            HashMap<String, Object> queryObject = new HashMap<>();
                            queryObject.put("collection", index.getCollection());
                            BasicDBObject insertTtlField = new BasicDBObject();
                            insertTtlField.put("timestamp", (new Date().getTime()) + 26 * 60 * 60 * 1000);
                            BasicDBObject updatedDBObject = new BasicDBObject();
                            updatedDBObject.put("$set", insertTtlField);
                            mongoServiceImpl.findAndModify(queryObject, TTL_EXPIRY_ACTION_HISTORY, TIMESTAMP, updatedDBObject);
                            dbObject.put("collection", index.getCollection());
                            dbObject.put("timestamp", currentTime);
                            mongoServiceImpl.insertDataInCollection(TTL_EXPIRY_ACTION_HISTORY, dbObject);
                        }
                    } else {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "TTL only Supports for date field :: " + index.getIndexField());
                    }
                } else {
                    HashMap<String, Object> queryObject = new HashMap<>();
                    queryObject.put("collection", index.getCollection());
                    DBObject dbObject = mongoServiceImpl.findOne(TTL_EXPIRY_ACTION_HISTORY, queryObject);
                    if (!Objects.isNull(dbObject)) {
                        long previousTime = (long) dbObject.get("timestamp");
                        if (currentTime > previousTime) {
                            if (indexInfoOp.isPresent()) {
                                if (index.getTtl() != MathUtil.toLong(indexInfoOp.get().get("expireAfterDays"))) {
                                    mongoServiceImpl.updateTTLIndex(index.getCollection(), index.getIndexField(), MathUtil.toInt(indexInfoOp.get().get(index.getIndexField())), daysMillis);
                                    BasicDBObject insertTtlField = new BasicDBObject();
                                    insertTtlField.put("timestamp", (new Date().getTime()) + 26 * 60 * 60 * 1000);
                                    BasicDBObject updatedDBObject = new BasicDBObject();
                                    updatedDBObject.put("$set", insertTtlField);
                                    mongoServiceImpl.findAndModify(queryObject, TTL_EXPIRY_ACTION_HISTORY, TIMESTAMP, updatedDBObject);
                                }
                            } else {
                                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "TTL only Supports for date field :: " + index.getIndexField());
                            }
                        } else
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "TTL can be update only once in a day ");
                    } else {
                        if (indexInfoOp.isPresent() && (index.getTtl() != MathUtil.toLong(indexInfoOp.get().get("expireAfterDays")))) {
                            mongoServiceImpl.updateTTLIndex(index.getCollection(), index.getIndexField(), MathUtil.toInt(indexInfoOp.get().get(index.getIndexField())), daysMillis);
                            BasicDBObject insertedDBField = new BasicDBObject();
                            insertedDBField.put("collection", index.getCollection());
                            insertedDBField.put("timestamp", new Date().getTime());
                            mongoServiceImpl.insertDataInCollection(TTL_EXPIRY_ACTION_HISTORY, insertedDBField);
                        }
                    }
                }
            } else
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Mongo Document TTL must be greater than 1 Days and less than 365 Days. Invalid TTL Supplied - " + index.getIndexField() + " ttl  (" + index.getTtl() + ")");
        });
        cacheService.delete(COLLECTIONS_KEY, COLLECTIONS);
        getAllCollections();
    }

    public void removeTTlForIndex(IndexTTLRequest indexTTLRequest) throws Exception {
        if (mongoServiceImpl.findCollection(indexTTLRequest.getCollection())) {
            DBObject dbObject = mongoServiceImpl.findCollectionIndex(indexTTLRequest.getCollection());
            if (Objects.nonNull(dbObject) && Objects.nonNull(dbObject.get(indexTTLRequest.getIndexField()))) {
                mongoServiceImpl.removeTTLForIndex(indexTTLRequest.getCollection(), indexTTLRequest.getIndexField());
                cacheService.delete(COLLECTIONS_KEY, COLLECTIONS);
                getAllCollections();
            } else
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Index Not Found in Collection  :: " + indexTTLRequest.getIndexField());
        } else
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Collection Not Found " + indexTTLRequest.getCollection());

    }

    public HashMap<String, Object> getETLQueryProgress(String name, String type) {
        Long minutes = Long.valueOf(commonService.read(DEBUG_CONFIG).get(ETL_DEBUG_MINUTES));
        HashMap<String, Object> etlQueryMap = new HashMap<>();
        etlQueryMap.put("duration", minutes);
        List<String> queryNameList = new ArrayList<>();
        BasicDBObject queryState = new BasicDBObject();
        List<HashMap<String, Object>> listOfETLQueryProgress = new ArrayList<>();
        BasicDBObject projection = new BasicDBObject();
        projection.put("data", 1);
        projection.put("state", 1);
        projection.put("_id", 0);
        List<DBObject> propertyList = mongoServiceImpl.findList(new BasicBSONObject("type", type), ETL_PROPERTIES, new BasicDBObject());
        List<DBObject> metaDataList = mongoServiceImpl.findList(new BasicBSONObject("type", type), ETL_METADATA, projection);
        if (ValidationUtil.nonNullOrEmpty(metaDataList)) {
            queryNameList = (List<String>) metaDataList.get(0).get("data");
            queryState = (BasicDBObject) metaDataList.get(0).get("state");
        }
        if (ValidationUtil.nonNullOrEmpty(name)) {
            HashMap<String, Object> etlQueryProgress = new HashMap();
            HashMap<String, Object> queryParam = new HashMap<>();
            queryParam.put("name", name);
            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);
            mongoFieldOptions.put("timestamp", 1);
            mongoFieldOptions.put("durationMs.triggerExecution", 1);
            mongoFieldOptions.put("numInputRows", 1);
            mongoFieldOptions.put("inputRowsPerSecond", 1);
            mongoFieldOptions.put("processedRowsPerSecond", 1);
            etlQueryProgress.put("name", name);
            List<DBObject> data = mongoServiceImpl.findListByTimestamp(queryParam, ETL_QUERY_PROGRESS, TIMESTAMP, minutes, DESC, mongoFieldOptions);
            IntSummaryStatistics stats = data.stream()
                    .mapToInt((x) -> ((BasicDBObject) x.get("durationMs")).getInt("triggerExecution"))
                    .summaryStatistics();
            IntSummaryStatistics statsNumInputRows = data.stream()
                    .mapToInt((x) -> (int) x.get("numInputRows"))
                    .summaryStatistics();
            Collections.reverse(data);
            etlQueryProgress.put("metrics", data);
            etlQueryProgress.put("stats", stats);
            etlQueryProgress.put("statsNumInputRows", statsNumInputRows);
            etlQueryProgress.put("isRunning", (Objects.nonNull(queryState.getString(name)) && queryState.getString(name).equals("running")) ? true : false);
            etlQueryMap.put("queryData", etlQueryProgress);
            HashMap<String, Object> etlProperty = (HashMap<String, Object>) propertyList.stream().filter(th -> th.get("name").equals(name)).findAny().orElse(null);
            if (Objects.isNull(etlProperty)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ETL properties not found.");
            }
            etlQueryMap.put("etlProperty", etlProperty);

            return etlQueryMap;
        }
        if (!ETL_TYPE_STREAM.equals(type) && !ETL_TYPE_HISTORY.equals(type)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid ETL type.");
        }
        if (Objects.nonNull(metaDataList) && !metaDataList.isEmpty()) {
            BasicDBObject finalQueryState = queryState;
            queryNameList.forEach(nameField ->
            {
                HashMap<String, Object> etlQueryProgress = new HashMap();
                HashMap<String, Object> queryParam = new HashMap<>();
                queryParam.put("name", nameField);
                BasicDBObject mongoFieldOptions = new BasicDBObject();
                mongoFieldOptions.clear();
                mongoFieldOptions.put("_id", 0);
                mongoFieldOptions.put("timestamp", 1);
                mongoFieldOptions.put("durationMs.triggerExecution", 1);
                mongoFieldOptions.put("numInputRows", 1);
                mongoFieldOptions.put("inputRowsPerSecond", 1);
                mongoFieldOptions.put("processedRowsPerSecond", 1);
                etlQueryProgress.put("name", nameField);
                List<DBObject> data = mongoServiceImpl.findListByTimestamp(queryParam, ETL_QUERY_PROGRESS, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                IntSummaryStatistics stats = data.stream()
                        .mapToInt((x) -> ((BasicDBObject) x.get("durationMs")).getInt("triggerExecution"))
                        .summaryStatistics();
                IntSummaryStatistics statsNumInputRows = data.stream()
                        .mapToInt((x) -> (int) x.get("numInputRows"))
                        .summaryStatistics();
                Collections.reverse(data);
                etlQueryProgress.put("metrics", data);
                etlQueryProgress.put("stats", stats);
                etlQueryProgress.put("statsNumInputRows", statsNumInputRows);
                etlQueryProgress.put("isRunning", (Objects.nonNull(finalQueryState.getString(nameField)) && finalQueryState.getString(nameField).equals("running")) ? true : false);
                HashMap<String, Object> etlProperty = (HashMap<String, Object>) propertyList.stream().filter(th -> th.get("name").equals(nameField)).findAny().orElse(null);
                if (Objects.isNull(etlProperty)) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ETL properties not found.");
                }
                etlQueryProgress.put("etlProperty", etlProperty);
                listOfETLQueryProgress.add(etlQueryProgress);
            });
        }
        listOfETLQueryProgress.sort((q1, q2) -> Double.compare(((IntSummaryStatistics) q2.get("stats")).getAverage(), ((IntSummaryStatistics) q1.get("stats")).getAverage()));
        etlQueryMap.put("queryData", listOfETLQueryProgress);
        return etlQueryMap;
    }

    public HashMap<String, List<String>> getETLNames() {
        HashMap<String, List<String>> etlNamesGroupByType = new HashMap<>();
        BasicDBObject projection = new BasicDBObject();
        projection.put("data", 1);
        projection.put("type", 1);
        projection.put("_id", 0);
        List<DBObject> metaDataList = mongoServiceImpl.findList(new BasicBSONObject(), ETL_METADATA, projection);
        if (ValidationUtil.nonNullOrEmpty(metaDataList)) {
            for (DBObject metadata : metaDataList) {
                String type = String.valueOf(metadata.get("type"));
                if (ETL_TYPE_STREAM.equals(type) || ETL_TYPE_HISTORY.equals(type) || ETL_TYPE_RPC.equals(type))
                    etlNamesGroupByType.put(String.valueOf(metadata.get("type")), (List<String>) metadata.get("data"));
            }
        }
        return etlNamesGroupByType;
    }

    public List<HashMap<String, Object>> getSlowPerformingETLs() {
        Long minutes = Long.valueOf(commonService.read(DEBUG_CONFIG).get(ETL_DEBUG_MINUTES));
        List<HashMap<String, Object>> listOfETLQueryProgress = new ArrayList<>();
        BasicDBObject projection = new BasicDBObject();
        projection.put("data", 1);
        projection.put("state", 1);
        projection.put("_id", 0);
        List<String> etlType = Arrays.asList("STREAM_QUERY_NAME", "HISTORY_QUERY_NAME");
        for (String type : etlType) {
            List<DBObject> metaDataList = mongoServiceImpl.findList(new BasicBSONObject("type", type), ETL_METADATA, projection);
            List<DBObject> propertyList = mongoServiceImpl.findList(new BasicBSONObject("type", type), ETL_PROPERTIES, new BasicDBObject());
            if (Objects.nonNull(metaDataList) && !metaDataList.isEmpty()) {
                List<String> queryNameList = (List<String>) metaDataList.get(0).get("data");
                BasicDBObject queryState = (BasicDBObject) metaDataList.get(0).get("state");
                queryState = Objects.isNull(queryState) ? new BasicDBObject() : queryState;
                queryNameList = Objects.isNull(queryNameList) ? new ArrayList<>() : queryNameList;
                for (String nameField : queryNameList) {
                    Boolean isPaused = (Objects.isNull(queryState.getString(nameField)) || queryState.getString(nameField).equals("pause")) ? true : false;
                    if (isPaused) continue;
                    HashMap<String, Object> etlQueryProgress = new HashMap();
                    HashMap<String, Object> queryParam = new HashMap<>();
                    queryParam.put("name", nameField);
                    BasicDBObject mongoFieldOptions = new BasicDBObject();
                    mongoFieldOptions.clear();
                    mongoFieldOptions.put("_id", 0);
                    mongoFieldOptions.put("timestamp", 1);
                    mongoFieldOptions.put("durationMs.triggerExecution", 1);
                    mongoFieldOptions.put("numInputRows", 1);
                    mongoFieldOptions.put("inputRowsPerSecond", 1);
                    mongoFieldOptions.put("processedRowsPerSecond", 1);
                    etlQueryProgress.put("name", nameField);
                    List<DBObject> data = mongoServiceImpl.findListByTimestamp(queryParam, ETL_QUERY_PROGRESS, TIMESTAMP, minutes, DESC, mongoFieldOptions);
                    IntSummaryStatistics stats = data.stream()
                            .mapToInt((x) -> ((BasicDBObject) x.get("durationMs")).getInt("triggerExecution"))
                            .summaryStatistics();
                    Double avg = stats.getAverage() >= 0 ? stats.getAverage() : 0;
                    if (avg.equals(0)) continue;
                    HashMap<String, Object> etlProperty = (HashMap<String, Object>) propertyList.stream().filter(th -> th.get("name").equals(nameField)).findAny().orElse(null);
                    if (Objects.isNull(etlProperty)) {
                        LOG.error("ETL properties not found.");
                        continue;
                    }
                    long threshold = Long.valueOf(EMPTY_STRING + etlProperty.get("threshold"));
                    if (avg > threshold) {
                        etlQueryProgress.put("name", nameField);
                        etlQueryProgress.put("threshold", threshold);
                        etlQueryProgress.put("avg", avg);
                        etlQueryProgress.put("isRunning", !isPaused);
                        listOfETLQueryProgress.add(etlQueryProgress);
                    }
                }
            }
        }

        listOfETLQueryProgress.sort(Comparator.comparing(q -> q.get("avg").toString()));
        Collections.reverse(listOfETLQueryProgress);

        return listOfETLQueryProgress;
    }

    public List<DBObject> getEtlPropertiesCSV() {
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);
        return mongoServiceImpl.findList(ETL_PROPERTIES, new HashMap<>(), "type", 1, fieldsToRemove);

    }

    public HashMap<String, String> getEquipmentInfoBySerial(String subscriberId, String equipmentSerial) throws Exception {
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(subscriberId);
        if(Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        HashMap<String, String> dataMap = new HashMap<>();

        if (Objects.nonNull(equipmentSerial) && equipmentSerial.equals(equipment.getRgwSerial())) {
            dataMap.put("serialNumber", equipment.getRgwSerial());
            return dataMap;
        }

        HashMap<String, Object> filter = new BasicDBObject();
        filter.put("userId", equipment.getRgwSerial());
        filter.put("serialNumber", equipmentSerial);

        BasicDBObject sort = new BasicDBObject();
        sort.put("timestamp", DESC);

        BasicDBObject projection = new BasicDBObject();
        projection.put("userId", 1);
        projection.put("serialNumber", 1);

        DBObject dbObject = mongoServiceImpl.findOne(filter, AP_DETAIL, sort, projection);
        if (Objects.isNull(dbObject))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No information found for equipment serial :: " + equipmentSerial);

        dataMap.put("serialNumber", String.valueOf(dbObject.get("userId")));

        return dataMap;
    }

    public String getEncryptedPassword(String type) throws Exception {
        if (!(type.equals("NOTIFICATION") || type.equals("DEBUG"))) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid type");
        }
        if (!type.equals("NOTIFICATION"))
            checkUserAccess();
        String encryptedValue = null;
        String passwordPrefix = null;
        String passwordSuffix = null;
        String toEncrypt = null;

        switch (type) {
            case "DEBUG":
                String vmqPassword = environment.getProperty(VMQ_DEBUG_PASSWORD);
                passwordPrefix = RandomStringUtils.randomAlphanumeric(7);
                passwordSuffix = RandomStringUtils.randomAlphanumeric(9);

                toEncrypt = passwordPrefix + vmqPassword + passwordSuffix;
                encryptedValue = Base64.getEncoder().encodeToString(toEncrypt.getBytes());
                break;

            case "NOTIFICATION":
                String vmqNotificationPassword = environment.getProperty(VMQ_NOTIFICATION_PASSWORD);
                passwordPrefix = RandomStringUtils.randomAlphanumeric(8);
                passwordSuffix = RandomStringUtils.randomAlphanumeric(6);

                toEncrypt = passwordPrefix + vmqNotificationPassword + passwordSuffix;
                encryptedValue = Base64.getEncoder().encodeToString(toEncrypt.getBytes());
                break;

        }

        return encryptedValue;
    }

    private void checkUserAccess() throws Exception {
        UserContext userContext = ExecutionContext.get().getUsercontext();
        List<Authority> authorities = userContext.getAuthorities();
        List<com.incs83.security.tokenFactory.Entitlement> entitlements = new ArrayList<>();

        authorities.forEach(authority ->
                entitlements.addAll(authority.getEntitlements().stream().filter(p ->
                        p.getResource().equals("ALL") || p.getResource().equalsIgnoreCase(MQTTClient.class.getSimpleName()))
                        .collect(Collectors.toList())));
        if (entitlements.isEmpty()) {
            throw new AuthEntityNotAllowedException(ApiResponseCode.RESOURCE_NOT_ALLOWED);
        }
        Permission requiredPermission = Permission.READ;
        List<String> permissions = new ArrayList<>();
        entitlements.forEach(entitlement ->
                permissions.addAll(entitlement.getPermissions().stream().filter(p ->
                        p.equals("ALL") || p.equalsIgnoreCase(requiredPermission.getValue()))
                        .collect(Collectors.toList())));
        String permission = permissions.stream().filter(p -> p.equals("ALL") || p.equalsIgnoreCase(requiredPermission.getValue())).findFirst().orElse(null);
        if (Objects.isNull(permission)) {
            throw new AuthMethodNotSupportedException(ApiResponseCode.RESOURCE_PERMISSION_DENIED);
        }
    }

    public HashMap<String, Object> countOfDeviceOnlineStatus(String clusterId) throws Exception {
        if (cacheService.isMapExist(DASHBOARD_COUNTS)) {
            HashMap<String, Object> data;

            if (!clusterId.equals("0")) {
                data = (HashMap<String, Object>) cacheService.read(clusterId, DASHBOARD_COUNTS);
            } else {
                data = (HashMap<String, Object>) cacheService.read(GLOBAL, DASHBOARD_COUNTS);
            }

            if (data != null) {
                long subscriberRgwCount = Optional.ofNullable((Long) data.get("subscriberRgwCount")).orElse(0L);
                long unassignedRgwCount = Optional.ofNullable((Long) data.get("unassignedRgwCount")).orElse(0L);
                long unassignedStnCount = Optional.ofNullable((Long) data.get("unassignedStnCount")).orElse(0L);
                long apCount = Optional.ofNullable((Long) data.get("apCount")).orElse(0L);
                long rgwCount = Optional.ofNullable((Long) data.get("rgw")).orElse(0L);

                HashMap<String, Object> response = new HashMap<>();
                response.put("subscriberRgwCount", subscriberRgwCount);
                response.put("unassignedRgwCount", unassignedRgwCount);
                response.put("unassignedStnCount", unassignedStnCount);
                response.put("rgwCount", subscriberRgwCount + unassignedRgwCount);
                response.put("extCount", apCount - rgwCount);
                response.put("rgwSSCount", Objects.isNull(data.get("rgwSSCount")) ? 0 : data.get("rgwSSCount"));
                response.put("connectedDevices", data.get("station"));
                response.put("subscriberCount", subscriberRgwCount);  // backward compatibility

                return response;
            }
        }

        return getCountOfDeviceOnlineStatus(clusterId);
    }

    public HashMap<String, Object> getCountOfDeviceOnlineStatus(String clusterId) throws Exception {
        HashMap<String, String> paramsMap = new HashMap<>();
        HashMap<String, Object> response = new HashMap<>();
        HashMap<String, Object> queryParams = new HashMap<>();
        String isp = null;
        String SUBSCRIBERID_SN_NOT_NULL = "subscriberId IS NOT NULL AND rgwSerial IS NOT NULL";
        String SUBSCRIBERID_IS_NULL = "subscriberId IS NULL";
        String SUBSCRIBERID_NOT_NULL_SN_IS_NULL = "subscriberId IS NOT NULL AND rgwSerial IS NULL";
        long subscriberRgwCount;
        long unassignedRgwCount;
        long unassignedStnCount;

        //TODO subscriberCount===Total Onboard equipment
        //TODO SubscriberCount===actual Subscriber count from subscriber table

        if (!clusterId.equals("0")) {
            String groupId;
            Compartment compartment;
            if (!CommonUtils.isSysAdmin()) {
                compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
                groupId = CommonUtils.getGroupIdOfLoggedInUser();
            } else {
                ClusterInfo clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, clusterId);
                if (Objects.isNull(clusterInfo))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not exist");

                HashMap<String, Object> query = new HashMap<>();

                query.clear();
                query.put("clusterId", clusterInfo.getId());

                List<String> groupList = dataAccessService.readNative(ClusterInfoSQL.GET_GROUP_ID_BY_CLUSTER_ID, query);

                if (Objects.isNull(groupList) && groupList.isEmpty())
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cluster does not map to any Groups");

                groupId = groupList.get(0);
                compartment = (Compartment) dataAccessService.read(Compartment.class, groupList.get(0));

            }

            subscriberRgwCount = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_GROUP.replaceAll("###", "where groupId='" + groupId + "'" + " AND " + SUBSCRIBERID_SN_NOT_NULL), new HashMap<>()).iterator().next().toString());
            unassignedRgwCount = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_GROUP.replaceAll("###", "where groupId='" + groupId + "'" + " AND " + SUBSCRIBERID_IS_NULL), new HashMap<>()).iterator().next().toString());
            unassignedStnCount = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_GROUP.replaceAll("###", "where groupId='" + groupId + "'" + " AND " + SUBSCRIBERID_NOT_NULL_SN_IS_NULL), new HashMap<>()).iterator().next().toString());

            if (Objects.nonNull(compartment)) {
                queryParams.clear();
                queryParams.put("id", compartment.getIspId());

                HashMap<String, Object> appendableParams = new HashMap<>();
                BasicDBObject fieldsToRemove = new BasicDBObject();
                fieldsToRemove.put("_id", ZERO);

                DBObject dbObject = mongoServiceImpl.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
                if (Objects.nonNull(dbObject)) {
                    queryParams.clear();
                    String ispName = String.valueOf(dbObject.get("name"));
                    isp = ispName;
                    queryParams.put("isp", ispName);
                }
            }

        } else {
            subscriberRgwCount = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_GROUP.replaceAll("###", "where " + SUBSCRIBERID_SN_NOT_NULL), new HashMap<>()).iterator().next().toString());
            unassignedRgwCount = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_GROUP.replaceAll("###", "where " + SUBSCRIBERID_IS_NULL), new HashMap<>()).iterator().next().toString());
            unassignedStnCount = Long.valueOf(dataAccessService.read(Equipment.class, GET_EQUIPMENT_COUNT_BY_GROUP.replaceAll("###", "where " + SUBSCRIBERID_NOT_NULL_SN_IS_NULL), new HashMap<>()).iterator().next().toString());
        }

        response.put("subscriberCount", subscriberRgwCount);
        response.put("subscriberRgwCount", subscriberRgwCount);
        response.put("unassignedRgwCount", unassignedRgwCount);
        response.put("unassignedStnCount", unassignedStnCount);
        
        long apCount;
        long rgwCount;
        long connectedDevices;


        queryParams.put("type", new BasicDBObject("$exists", true));
        apCount = mongoServiceImpl.count(queryParams, new HashMap<>(), AP_DETAIL);

        HashMap<String, Object> connectivityStatus = applicationStatsService.getDeviceConnectivityStatusUsingCache(isp);
        if (Objects.nonNull(connectivityStatus) && !connectivityStatus.isEmpty() && !(Long.valueOf(connectivityStatus.get("noOfConnectedRGW").toString()) == 0 && Long.valueOf(connectivityStatus.get("noOfDisconnectedRGW").toString()) == 0)) {
            connectedDevices = Long.valueOf(connectivityStatus.get("noOfDisconnectedDevice").toString()) + Long.valueOf(connectivityStatus.get("noOfConnectedDevice").toString());
            rgwCount = Long.valueOf(connectivityStatus.get("noOfConnectedRGW").toString()) + Long.valueOf(connectivityStatus.get("noOfDisconnectedRGW").toString());
        } else {
            queryParams.clear();
            queryParams.put("type", GATEWAY);
            if (Objects.nonNull(isp)) {
                queryParams.put("isp", isp);
            }
            rgwCount = mongoServiceImpl.count(queryParams, new HashMap<>(), AP_DETAIL);
            queryParams.remove("type");
            connectedDevices = mongoServiceImpl.count(queryParams, new HashMap<>(), STATION_DETAIL);
        }

        response.put("connectedDevices", connectedDevices);
        response.put("rgwCount", subscriberRgwCount + unassignedRgwCount);
        response.put("extCount", apCount - rgwCount);

        queryParams.clear();
        if (Objects.nonNull(isp))
            queryParams.put("isp", isp);
            queryParams.put("type", GATEWAY);
//        queryParams.put("timestamp", new BasicDBObject("$gte", Calendar.getInstance().getTimeInMillis() - 24 * 60 * 60 * 1000));

//        long rgwSSCount = mongoServiceImpl.distinctCount(queryParams, new HashMap<>(), DEVICE_STEERING, "userId");

        ArrayList<DBObject> list = new ArrayList<DBObject>();
        BasicDBObject or1 = new BasicDBObject("wifiConf.SmartSteering.enable", true);
        BasicDBObject or2 = new BasicDBObject("wifiConf.SmartSteering.Enable", 1);
        BasicDBObject or3 = new BasicDBObject().append("wifiConf.SmartSteering", new BasicDBObject().append("$exists", false)).append("smartSteeringEnable", 1);
        list.add(or1);
        list.add(or2);
        list.add(or3);
        queryParams.put("$or", list);
        long smartSteeringCount = mongoServiceImpl.count(queryParams, null, AP_DETAIL);
        response.put("rgwSSCount", smartSteeringCount);

        return response;
    }

    private String getBuildVersion() {
        return s3Service.getBuildVersionInfo(BUILD_VERSION_INFO);
    }

    public Map<String, String> getBuildVersionMap() {
        String verString = getBuildVersion();
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> buildVerMap = new HashMap<String, String>();
        try {
            buildVerMap = objectMapper.readValue(verString, new TypeReference<HashMap<String, String>>() {});
        } catch (Exception e) {
            LOG.error("parse build version failed. verString[{}]", verString);
        }
        return buildVerMap;
    }
}
