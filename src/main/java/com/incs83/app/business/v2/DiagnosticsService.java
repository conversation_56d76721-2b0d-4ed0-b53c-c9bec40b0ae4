package com.incs83.app.business.v2;

import com.incs83.app.annotation.Auditable;
import com.incs83.app.common.v2.EtlResetRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.exception.AeiTimeoutException;
import com.incs83.app.responsedto.v2.Equipment.DiagonosticsRPCResponseDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.pubsub.KafkaPublisher;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeoutException;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

/**
 * Created By AMAN on Monday, 24-December-2018
 */
@Service
public class DiagnosticsService {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private RPCUtilityService RPCUtilityService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private CommonService commonService;

    @Autowired
    private KafkaPublisher kafkaPublisher;
    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.RGW_MONITOR_MODE)
    public HashMap<String, Object> enableDisableDiagnos(String equipmentIdOrSerialOrSTN, Boolean diagnosEnabled,HttpServletRequest request) throws Exception {
        LOG.info("<<<SERVICE ENTRY>>>");
        HashMap<String, Object> response = new HashMap<>();
        List<DiagonosticsRPCResponseDTO> responseDTOS = new ArrayList<>();
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN))
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
        LOG.info("<<<EQUIP CHECK>>>");
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        LOG.info("<<<EQUIP CHECK COMPLETE>>>");
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        BasicDBObject queryParams = new BasicDBObject();
        queryParams.put("userId", userEquipment.getRgwSerial());
        ArrayList<DBObject> apDetailList = (ArrayList<DBObject>) mongoService.findList(AP_DETAIL, queryParams, TIMESTAMP, DESC);
        if (Objects.isNull(apDetailList) || apDetailList.isEmpty()) {
            throw new com.incs83.exceptions.handler.ValidationException(HttpStatus.BAD_REQUEST.value(), "AP not found.");
        }

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Long duration = Long.valueOf(equipmentProps.get(DIAGNOSTIC_DURATION_SECONDS));
        String repInterval = equipmentProps.get(DIAGNOSTIC_REPORTING_INTERVAL_SECONDS);
        String colInterval = equipmentProps.get(DIAGNOSTIC_COLLECTION_INTERVAL_SECONDS);
        Boolean isAnyRPCSuccess = false;

        List<CompletableFuture<DiagonosticsRPCResponseDTO>> futureList = new ArrayList<>();

        for (DBObject aPDetails : apDetailList) {
            HashMap<String, Object> map = new HashMap<>();
            map.put("userId", userEquipment.getRgwSerial());
            map.put("serialNumber", String.valueOf(aPDetails.get("serialNumber")));
            map.put("type", String.valueOf(aPDetails.get("type")));
            map.put("diagnosEnabled", diagnosEnabled);
            map.put("duration", duration);
            map.put("repInterval", repInterval);
            map.put("colInterval", colInterval);
            CompletableFuture<DiagonosticsRPCResponseDTO> f =  manageLongWaitAPI.toFuture(()-> RPCUtilityService.sendRPC(map));
            futureList.add(f);
        }

        responseDTOS.addAll(manageLongWaitAPI.awaitAll(futureList).get());
        Optional<DiagonosticsRPCResponseDTO> dtoOptional= responseDTOS.stream().filter(p->p!=null && p.getType().equals("GATEWAY") && p.getStatus().equals(ERROR)).findFirst();
        if (dtoOptional.isPresent())
            throw new AeiTimeoutException("Serial:[" + dtoOptional.get().getSerialNumber() + "] " + dtoOptional.get().getMessage());

        isAnyRPCSuccess = responseDTOS.stream().filter(p->p!=null && p.getStatus().equals(SUCCESS)).count() > 0?diagnosEnabled : !diagnosEnabled;
        response.put("rpcResult", responseDTOS);
        response.put("diagnosticsEnabled", isAnyRPCSuccess);
        LOG.info("<<<SERVICE EXIT>>>");
        return response;
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.ETL_RESTART)
    public void etlReset(EtlResetRequest etlResetRequest, HttpServletRequest httpServletRequest) {
        if (!ETL_TYPE_STREAM.equals(etlResetRequest.getType()) && !ETL_TYPE_HISTORY.equals(etlResetRequest.getType()) && !ETL_TYPE_RPC.equals(etlResetRequest.getType())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid ETL type.");
        }

        if (!ETL_OPERATION.contains(etlResetRequest.getAction())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid ETL action.");
        }
        if (!etlResetRequest.getAction().equals(UPDATE_ALL_CONFIG)&&Objects.isNull(etlResetRequest.getEtlName())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ETL Name Cannot be Null or Empty");
        }
        BasicDBObject projection = new BasicDBObject();
        projection.put("id", 1);
        projection.put("date", 1);
        HashMap<String, Object> queryP = new HashMap<>();
        queryP.put("etlName", etlResetRequest.getEtlName());
        queryP.put("action", etlResetRequest.getAction());
        queryP.put("type",etlResetRequest.getType());
        DBObject dbObject = mongoService.findOne(queryP, ETL_RPC_RESULT_INFO, new BasicDBObject("date", DESC), projection);

        Long minutes = Long.valueOf(commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG).get(ETL_CONFIG_LINGER_MINUTES));
        if (Objects.nonNull(dbObject)) {
            Long lastRPCTime = (Long) dbObject.get("date");
            if (Objects.nonNull(lastRPCTime) && CommonUtils.getCurrentTimeInMillis() - lastRPCTime < minutes*60L*1000L) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ETL " + etlResetRequest.getAction() + " already in progress, please try again after " +minutes+" minutes.");
            }
        }
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));

        String tid = CommonUtils.generateUUID();
        HashMap<String, Object> data = new HashMap<>();
        data.put("id", tid);
        data.put("etlName", etlResetRequest.getEtlName());
        data.put("type",etlResetRequest.getType());
        data.put("dateCreated", new Date());

        mongoService.create(ETL_RPC_RESULT_INFO, data);

        HashMap<String, Object> publishParams = new HashMap<>();
        publishParams.put("timestamp", String.valueOf(CommonUtils.getCurrentTimeInMillis()));
        publishParams.put("id", tid);
        publishParams.put("name", etlResetRequest.getEtlName());
        publishParams.put("type", etlResetRequest.getType());
        publishParams.put("action", etlResetRequest.getAction());
        if (Objects.nonNull(etlResetRequest.getOffset()) && Objects.nonNull(etlResetRequest.getOffset() != 0))
            publishParams.put("maxOffset", etlResetRequest.getOffset());
        if (Objects.nonNull(etlResetRequest.getInterval()) && Objects.nonNull(etlResetRequest.getInterval() != 0))
            publishParams.put("triggerInterval", etlResetRequest.getInterval());
        if (Objects.nonNull(etlResetRequest.getThreshold()) && Objects.nonNull(etlResetRequest.getThreshold() != 0))
            publishParams.put("threshold", etlResetRequest.getThreshold());

        BasicDBObject query = new BasicDBObject();
        query.put("id", tid);

        boolean resultReceived = false;
        int maxTries = 0;
        Boolean isTimeout = false;
        kafkaPublisher.publishToKafka(publishParams, ETL_RESTART_TOPIC);
        while (!resultReceived) {
            try {
                Thread.sleep(THREAD_TO_SLEEP);
            } catch (InterruptedException e) {
                LOG.error("Error during thread sleep", e);
            }
            maxTries++;
            if (maxTries == max_Tries) {
                isTimeout = true;
                LOG.error("Operation Timed Out...please retry");
                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("result", TIME_OUT);
                dataToUpdate.put("date", CommonUtils.getCurrentTimeInMillis());

                BasicDBObject update = new BasicDBObject();
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, ETL_RPC_RESULT_INFO);
                break;
            }
            DBObject rpcResult = mongoService.findOne(ETL_RPC_RESULT_INFO, query);
            if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.get("date"))) {
                continue;
            } else {
                resultReceived = true;
            }
        }
        DBObject rpcResult = mongoService.findOne(ETL_RPC_RESULT_INFO, query);
        if (Objects.nonNull(rpcResult)) {
            if (!RPC_RESULT.equals(rpcResult.get("result"))) {
                if (isTimeout)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
                else
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Failed");
            }
        }

    }

    public HashMap<String, Object> getETLProperties(String etlName) {
        if (Objects.isNull(etlName) || etlName.isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "etlName must not be null or empty");
        }
        HashMap<String, String> etlProperties = new HashMap<>();
        etlProperties.put("name", etlName);

        HashMap<String, Object> properties = new HashMap<>();
        DBObject etlProperty = mongoService.findOne(ETL_PROPERTIES, etlProperties);

        if (Objects.isNull(etlProperty)) {
            properties.put("offset", 0);
            properties.put("interval", 0);
            properties.put("threshold", 0);
            return properties;
        }
        properties.put("offset", etlProperty.get("maxOffset"));
        properties.put("interval", etlProperty.get("triggerInterval"));
        properties.put("threshold", etlProperty.get("threshold"));
        return properties;

    }

}
