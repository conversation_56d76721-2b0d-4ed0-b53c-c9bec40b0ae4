package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.common.v2.ChangeNetworkCredsRequest;
import com.incs83.app.common.v2.ChangeNetworkState;
import com.incs83.app.common.v2.WiFiNetwork;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.StationSpeedTest;
import com.incs83.app.entities.cassandra.DeviceCapability;
import com.incs83.app.enums.APDetailLookType;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.Device.DeviceDetailDTO;
import com.incs83.app.responsedto.v2.Network.EquipmentDetail;
import com.incs83.app.responsedto.v2.Network.ExtenderDetail;
import com.incs83.app.responsedto.v2.Network.NetworkListDTO;
import com.incs83.app.responsedto.v2.Network.TopologyDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.service.repository.CassandraRepository;
import com.incs83.app.utils.DeviceUtilityULDL;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.*;

/**
 * Created by Jayant on 29/1/18.
 */
@Service
public class ManageNetworkService {

    @Autowired
    private RPCUtilityService rpcUtilityService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

    @Autowired
    private ManageDeviceService manageDeviceService;

    @Autowired
    private CommonService commonService;

    @Autowired
    CassandraRepository cassandraRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SimpleRpcService cpeRpcService;

    private static final Logger LOG = LogManager.getLogger("org");

    private WiFiNetwork getWiFiNetwork(String type) {
        WiFiNetwork wiFiNetwork = null;

        if(type.equals("1")) {
            wiFiNetwork = new WiFiNetwork("2.4G", "Primary");
        } else if(type.equals("2")) {
            wiFiNetwork = new WiFiNetwork("2.4G", "Guest");
        } else if(type.equals("5")) {
            wiFiNetwork = new WiFiNetwork("5G", "Primary");
        } else if(type.equals("6")) {
            wiFiNetwork = new WiFiNetwork("5G", "Guest");
        }

        return wiFiNetwork;
    }

    private List<WiFiNetwork> getWiFiNetworks(String type, boolean smartSteeringEnabled) {
        List<WiFiNetwork> wiFiNetworkList = new ArrayList<>();
        ArrayList<String> types = new ArrayList<String>(Arrays.asList(type.split(",")));

        if(smartSteeringEnabled) {
            if (type.equals("1") || type.equals("5")) {
                WiFiNetwork wifi_2g = new WiFiNetwork("2.4G", "Primary");
                wiFiNetworkList.add(wifi_2g);
                WiFiNetwork wifi_5g = new WiFiNetwork("5G", "Primary");
                wiFiNetworkList.add(wifi_5g);
            } else if (type.equals("2") || type.equals("6")) {
                WiFiNetwork wifi_2g_guest = new WiFiNetwork("2.4G", "Guest");
                wiFiNetworkList.add(wifi_2g_guest);
                WiFiNetwork wifi_5g_guest = new WiFiNetwork("5G", "Guest");
                wiFiNetworkList.add(wifi_5g_guest);
            } else {
                for(String t : types) {
                    WiFiNetwork wiFiNetwork = getWiFiNetwork(t);
                    if(wiFiNetwork != null) {
                        wiFiNetworkList.add(wiFiNetwork);
                    }
                }
            }
        } else {
            for(String t : types) {
                WiFiNetwork wiFiNetwork = getWiFiNetwork(t);
                if(wiFiNetwork != null) {
                    wiFiNetworkList.add(wiFiNetwork);
                }
            }
        }

        return wiFiNetworkList;
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.NETWORK_CHANGE_UPDATE)
    public void changeNetworkState(ChangeNetworkState changeNetworkState, String equipmentIdOrSerialOrSTN, HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        String serial = manageCommonService.getControllerSerialByUserId(userEquipment.getRgwSerial()).orElseGet(()->userEquipment.getRgwSerial());
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("serialNumber", serial);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        String isp = String.valueOf(aPDetails.get("isp"));

        boolean smartSteeringEnabled = manageCommonService.isSmartSteeringEnabledForDeviceMac(userEquipment.getRgwSerial(), serial);
        List<WiFiNetwork> wiFiNetworks = getWiFiNetworks(changeNetworkState.getType(), smartSteeringEnabled);
        List<Object> requestList = new ArrayList<>();
        for(WiFiNetwork wifi : wiFiNetworks) {
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("enabled", changeNetworkState.isEnabled());

            HashMap<String, Object> data = new HashMap<>();
            data.put("uri", String.format(RpcConstants.SSID_ACTION_URI, wifi.getRadioKey(), wifi.getSsidKey()));
            data.put("method", "PUT");
            data.put("payload", payloadMap);

            requestList.add(data);
        }
        String request = objectMapper.writeValueAsString(requestList);
        String tid = CommonUtils.generateUUID();

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(LONG_RPC_POLL_COUNT));
        cpeRpcService.sendRpc31AndWaitResult(tid, userEquipment.getRgwSerial(), serial, isp, request, max_Tries, THREAD_TO_SLEEP);
    }

    private HashMap<String, String> populateParamsAsPerType(HashMap<String, String> publishParams, String type, boolean smartSteeringEnabled) {
        if (smartSteeringEnabled) {
            if (type.equals("1") || type.equals("5")) {
                publishParams.put("-TYPE-", "1,5");
            } else if (type.equals("2") || type.equals("6")) {
                publishParams.put("-TYPE-", "2,6");
            } else {
                publishParams.put("-TYPE-", type);
            }
        } else {
            publishParams.put("-TYPE-", type);
        }
        return publishParams;
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.NETWORK_CREDS_UPDATE)
    public void modifyWifi(ChangeNetworkCredsRequest changeNetworkCredsRequest, String equipmentIdOrSerialOrSTN, HttpServletRequest httpServletRequest) throws Exception {
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        if (dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) || dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Not Authorized to update credentials");
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        if (Objects.nonNull(changeNetworkCredsRequest.getSsid())) {
            if (!ValidationUtil.isContainSpecialCharacter(changeNetworkCredsRequest.getSsid())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Illegal Character found in SSID");
            }
        }
        if (Objects.nonNull(changeNetworkCredsRequest.getPassword())) {
            if (!ValidationUtil.isValidPassword(changeNetworkCredsRequest.getPassword())) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Illegal Character found in password");
            }
        }
        if (Objects.isNull(changeNetworkCredsRequest.getSerialNumber()) || changeNetworkCredsRequest.getSerialNumber().isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial number cannot be blank.");
        }

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("serialNumber", changeNetworkCredsRequest.getSerialNumber().trim());

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if(Objects.nonNull(aPDetails) && aPDetails.get("type").toString().equals("EXTENDER")) {
            throw new ValidationException(HttpStatus.SERVICE_UNAVAILABLE.value(), "Feature is not supported by extender");
        }

        String isp = String.valueOf(aPDetails.get("isp"));

        boolean smartSteeringEnabled = manageCommonService.isSmartSteeringEnabledForDeviceMac(equipment.getRgwSerial(), equipmentIdOrSerialOrSTN);
        List<WiFiNetwork> wiFiNetworks = getWiFiNetworks(changeNetworkCredsRequest.getType(), smartSteeringEnabled);
        List<Object> requestList = new ArrayList<>();
        for(WiFiNetwork wifi : wiFiNetworks) {
            Map<String, Object> payloadMap = new HashMap<>();
            payloadMap.put("ssid", changeNetworkCredsRequest.getSsid());
            payloadMap.put("password", changeNetworkCredsRequest.getPassword());

            HashMap<String, Object> data = new HashMap<>();
            data.put("uri", String.format(RpcConstants.SSID_ACTION_URI, wifi.getRadioKey(), wifi.getSsidKey()));
            data.put("method", "PUT");
            data.put("payload", payloadMap);

            requestList.add(data);
        }
        String request = objectMapper.writeValueAsString(requestList);
        String tid = CommonUtils.generateUUID();
        String serial = manageCommonService.getControllerSerialByUserId(equipment.getRgwSerial()).orElseGet(()->equipment.getRgwSerial());

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(LONG_RPC_POLL_COUNT));
        cpeRpcService.sendRpc31AndWaitResult(tid, equipment.getRgwSerial(), serial, isp, request, max_Tries, THREAD_TO_SLEEP);
    }

    public NetworkListDTO getWifiNetworksList(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(equipment.getRgwSerial()) || equipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(equipment);

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        NetworkListDTO networkListDTO = new NetworkListDTO();
        LinkedHashSet<NetworkListDTO.NetworkListData> networkListDataList = new LinkedHashSet<>();
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", equipment.getRgwSerial());
//        params.put("type", GATEWAY); //ODI-1150
        BasicDBObject projection = new BasicDBObject();
        projection.put("bssid24G", 1);
        projection.put("bssid5G", 1);
        projection.put("smartSteeringEnable", 1);
        projection.put("modelName", 1);
        projection.put("macAddress", 1);
        projection.put("friendlyName", 1);
        projection.put("type", 1);
        projection.put("serialNumber", 1);
        projection.put("wifiConf", 1);
        List<DBObject> userSerialNumberDetails = mongoService.findList(params, new HashMap<>(), AP_DETAIL, projection);

        if (Objects.isNull(userSerialNumberDetails) || userSerialNumberDetails.isEmpty()) {
            networkListDTO.setData(networkListDataList);
            return networkListDTO;
        }

        boolean isSmartSteeringEnabled;
        List<DBObject> gateWay = userSerialNumberDetails.stream().filter(ap -> String.valueOf(ap.get("type")).equals(GATEWAY)).collect(Collectors.toList());

        if (Objects.nonNull(gateWay) && !gateWay.isEmpty() &&
                (Objects.nonNull(gateWay.get(0).get("smartSteeringEnable")) || Objects.nonNull(gateWay.get(0).get("wifiConf")))) {
            DBObject wifiConf = (DBObject) gateWay.get(0).get("wifiConf");
            if (Objects.nonNull(wifiConf) && Objects.nonNull(wifiConf.get("SmartSteering"))) {
                DBObject smartSteering = (DBObject) wifiConf.get("SmartSteering");
                isSmartSteeringEnabled =  Objects.nonNull(smartSteering.get("enable"))? Boolean.valueOf(smartSteering.get("enable").toString()):
                        Objects.isNull(smartSteering.get("Enable"))? false: "1".equals(smartSteering.get("Enable").toString());
            } else {
                isSmartSteeringEnabled = gateWay.get(0).get("smartSteeringEnable").toString().equals("1") ? true : false;
            }
        } else {
            String serial = manageCommonService.getControllerSerialByUserId(equipment.getRgwSerial()).orElseGet(()->equipment.getRgwSerial());

            params.clear();
            params.put("userId", equipment.getRgwSerial());
            params.put("serialNumber", serial);
            params.put("timestamp", new BasicDBObject("$gte", Calendar.getInstance().getTimeInMillis() - 24 * 60 * 60 * 1000));

            isSmartSteeringEnabled = mongoService.count(params, new HashMap<>(), DEVICE_STEERING) > 0 ? true : false;

        }


        if (!isSmartSteeringEnabled) {
            userSerialNumberDetails.forEach(userEquipment -> {
                if (!((List<DBObject>) userEquipment.get("bssid24G")).isEmpty() && !((List<DBObject>) userEquipment.get("bssid5G")).isEmpty()) {
                    List<DBObject> data24G = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> element.get("SSIDKey") != null).collect(Collectors.toList());
                    List<DBObject> data5G = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> element.get("SSIDKey") != null).collect(Collectors.toList());
                    if ((Objects.nonNull(data24G) && !data24G.isEmpty()) && (Objects.nonNull(data5G) && !data5G.isEmpty())) {
                        DBObject primary24g = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Primary")).findAny().orElse(null);
                        if (Objects.nonNull(primary24g) && Objects.nonNull(primary24g.get("ssid"))) {
                            NetworkListDTO.NetworkListData primaryNetwork24g = networkListDTO.new NetworkListData();
                            primaryNetwork24g.setEnabled(String.valueOf(primary24g.get("enabled")).equals("1") ? true : false);
                            primaryNetwork24g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(primary24g.get("ssid")));
                            primaryNetwork24g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(primary24g.get("password")));
                            primaryNetwork24g.setType(String.valueOf(primary24g.get("SSIDKey")).toLowerCase());
                            primaryNetwork24g.setBand(String.valueOf(primary24g.get("radioKey")));
                            primaryNetwork24g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            primaryNetwork24g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(primaryNetwork24g);
                        }

                        DBObject primary5g = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Primary")).findAny().orElse(null);
                        if (Objects.nonNull(primary5g) && Objects.nonNull(primary5g.get("ssid"))) {
                            NetworkListDTO.NetworkListData primaryNetwork5g = networkListDTO.new NetworkListData();
                            primaryNetwork5g.setEnabled(String.valueOf(primary5g.get("enabled")).equals("1") ? true : false);
                            primaryNetwork5g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(primary5g.get("ssid")));
                            primaryNetwork5g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(primary5g.get("password")));
                            primaryNetwork5g.setType(String.valueOf(primary5g.get("SSIDKey")).toLowerCase());
                            primaryNetwork5g.setBand(String.valueOf(primary5g.get("radioKey")));
                            primaryNetwork5g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            primaryNetwork5g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(primaryNetwork5g);
                        }

                        DBObject guest24g = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Guest")).findAny().orElse(null);
                        if (Objects.nonNull(guest24g) && Objects.nonNull(guest24g.get("ssid"))) {
                            NetworkListDTO.NetworkListData guestNetwork24g = networkListDTO.new NetworkListData();
                            guestNetwork24g.setEnabled(String.valueOf(guest24g.get("enabled")).equals("1") ? true : false);
                            guestNetwork24g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(guest24g.get("ssid")));
                            guestNetwork24g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(guest24g.get("password")));
                            guestNetwork24g.setType(String.valueOf(guest24g.get("SSIDKey")).toLowerCase());
                            guestNetwork24g.setBand(String.valueOf(guest24g.get("radioKey")));
                            guestNetwork24g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            guestNetwork24g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(guestNetwork24g);
                        }

                        DBObject guest5g = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Guest")).findAny().orElse(null);
                        if (Objects.nonNull(guest5g) && Objects.nonNull(guest5g.get("ssid"))) {
                            NetworkListDTO.NetworkListData guestNetwork5g = networkListDTO.new NetworkListData();
                            guestNetwork5g.setEnabled(String.valueOf(guest5g.get("enabled")).equals("1") ? true : false);
                            guestNetwork5g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(guest5g.get("ssid")));
                            guestNetwork5g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(guest5g.get("password")));
                            guestNetwork5g.setType(String.valueOf(guest5g.get("SSIDKey")).toLowerCase());
                            guestNetwork5g.setBand(String.valueOf(guest5g.get("radioKey")));
                            guestNetwork5g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            guestNetwork5g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(guestNetwork5g);
                        }

                        DBObject video24g = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Video")).findAny().orElse(null);
                        if (Objects.nonNull(video24g) && Objects.nonNull(video24g.get("ssid"))) {
                            NetworkListDTO.NetworkListData videoNetwork24g = networkListDTO.new NetworkListData();
                            videoNetwork24g.setEnabled(String.valueOf(video24g.get("enabled")).equals("1") ? true : false);
                            videoNetwork24g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(video24g.get("ssid")));
                            videoNetwork24g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(video24g.get("password")));
                            videoNetwork24g.setType(String.valueOf(video24g.get("SSIDKey")).toLowerCase());
                            videoNetwork24g.setBand(String.valueOf(video24g.get("radioKey")));
                            videoNetwork24g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            videoNetwork24g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(videoNetwork24g);
                        }

                        DBObject video5g = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Video")).findAny().orElse(null);
                        if (Objects.nonNull(video5g) && Objects.nonNull(video5g.get("ssid"))) {
                            NetworkListDTO.NetworkListData videoNetwork5g = networkListDTO.new NetworkListData();
                            videoNetwork5g.setEnabled(String.valueOf(video5g.get("enabled")).equals("1") ? true : false);
                            videoNetwork5g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(video5g.get("ssid")));
                            videoNetwork5g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(video5g.get("password")));
                            videoNetwork5g.setType(String.valueOf(video5g.get("SSIDKey")).toLowerCase());
                            videoNetwork5g.setBand(String.valueOf(video5g.get("radioKey")));
                            videoNetwork5g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            videoNetwork5g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(videoNetwork5g);
                        }
                    } else {
                        DBObject primary24g = ((List<DBObject>) userEquipment.get("bssid24G")).get(0);
                        if (Objects.nonNull(primary24g.get("ssid"))) {
                            NetworkListDTO.NetworkListData primaryNetwork24g = networkListDTO.new NetworkListData();
                            primaryNetwork24g.setEnabled(String.valueOf(primary24g.get("enabled")).equals("1") ? true : false);
                            primaryNetwork24g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(primary24g.get("ssid")));
                            primaryNetwork24g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(primary24g.get("password")));
                            primaryNetwork24g.setType("primary");
                            primaryNetwork24g.setBand("2.4G");
                            primaryNetwork24g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            primaryNetwork24g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(primaryNetwork24g);
                        }

                        DBObject primary5g = ((List<DBObject>) userEquipment.get("bssid5G")).get(0);
                        if (Objects.nonNull(primary5g.get("ssid"))) {
                            NetworkListDTO.NetworkListData primaryNetwork5g = networkListDTO.new NetworkListData();
                            primaryNetwork5g.setEnabled(String.valueOf(primary5g.get("enabled")).equals("1") ? true : false);
                            primaryNetwork5g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(primary5g.get("ssid")));
                            primaryNetwork5g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(primary5g.get("password")));
                            primaryNetwork5g.setType("primary");
                            primaryNetwork5g.setBand("5G");
                            primaryNetwork5g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            primaryNetwork5g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(primaryNetwork5g);
                        }

                        DBObject guest24g = ((List<DBObject>) userEquipment.get("bssid24G")).get(1);
                        if (Objects.nonNull(guest24g.get("ssid"))) {
                            NetworkListDTO.NetworkListData guestNetwork24g = networkListDTO.new NetworkListData();
                            guestNetwork24g.setEnabled(String.valueOf(guest24g.get("enabled")).equals("1") ? true : false);
                            guestNetwork24g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(guest24g.get("ssid")));
                            guestNetwork24g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(guest24g.get("password")));
                            guestNetwork24g.setType("guest");
                            guestNetwork24g.setBand("2.4G");
                            guestNetwork24g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            guestNetwork24g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(guestNetwork24g);
                        }


                        DBObject guest5g = ((List<DBObject>) userEquipment.get("bssid5G")).get(1);
                        if (Objects.nonNull(guest5g.get("ssid"))) {
                            NetworkListDTO.NetworkListData guestNetwork5g = networkListDTO.new NetworkListData();
                            guestNetwork5g.setEnabled(String.valueOf(guest5g.get("enabled")).equals("1") ? true : false);
                            guestNetwork5g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(guest5g.get("ssid")));
                            guestNetwork5g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(guest5g.get("password")));
                            guestNetwork5g.setType("guest");
                            guestNetwork5g.setBand("5G");
                            guestNetwork5g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                            guestNetwork5g.setApType(String.valueOf(userEquipment.get("type")));
                            networkListDataList.add(guestNetwork5g);
                        }

                        if (((List<DBObject>) userEquipment.get("bssid5G")).size() >= 2) {
                            DBObject video5g = ((List<DBObject>) userEquipment.get("bssid5G")).get(2);
                            if (Objects.nonNull(video5g.get("ssid"))) {
                                NetworkListDTO.NetworkListData videoNetwork5g = networkListDTO.new NetworkListData();
                                videoNetwork5g.setEnabled(String.valueOf(video5g.get("enabled")).equals("1") ? true : false);
                                videoNetwork5g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(video5g.get("ssid")));
                                videoNetwork5g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(video5g.get("password")));
                                videoNetwork5g.setType("video");
                                videoNetwork5g.setBand("5G");
                                videoNetwork5g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                                videoNetwork5g.setApType(String.valueOf(userEquipment.get("type")));
                                networkListDataList.add(videoNetwork5g);
                            }
                        }

                        if (((List<DBObject>) userEquipment.get("bssid24G")).size() >= 2) {
                            DBObject video24g = ((List<DBObject>) userEquipment.get("bssid24G")).get(2);
                            if (Objects.nonNull(video24g.get("ssid"))) {
                                NetworkListDTO.NetworkListData videoNetwork24g = networkListDTO.new NetworkListData();
                                videoNetwork24g.setEnabled(String.valueOf(video24g.get("enabled")).equals("1") ? true : false);
                                videoNetwork24g.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : String.valueOf(video24g.get("ssid")));
                                videoNetwork24g.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : String.valueOf(video24g.get("password")));
                                videoNetwork24g.setType("video");
                                videoNetwork24g.setBand("2.4G");
                                videoNetwork24g.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                                videoNetwork24g.setApType(String.valueOf(userEquipment.get("type")));
                                networkListDataList.add(videoNetwork24g);
                            }
                        }
                    }
                }
            });
        } else {
            userSerialNumberDetails.forEach(userEquipment -> {

                if (!((List<DBObject>) userEquipment.get("bssid24G")).isEmpty() && !((List<DBObject>) userEquipment.get("bssid5G")).isEmpty()) {
                    LinkedHashSet<String> networks = new LinkedHashSet<>();

                    List<DBObject> data24G = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> element.get("SSIDKey") != null).collect(Collectors.toList());
                    List<DBObject> data5G = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> element.get("SSIDKey") != null).collect(Collectors.toList());
                    if ((Objects.nonNull(data24G) && !data24G.isEmpty()) && (Objects.nonNull(data5G) && !data5G.isEmpty())) {
                        DBObject primary24g = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Primary")).findAny().orElse(null);
                        DBObject guest24g = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Guest")).findAny().orElse(null);
                        DBObject primary5g = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Primary")).findAny().orElse(null);
                        DBObject guest5g = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Guest")).findAny().orElse(null);
                        DBObject video5g = ((List<DBObject>) userEquipment.get("bssid5G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Video")).findAny().orElse(null);
                        DBObject video24g = ((List<DBObject>) userEquipment.get("bssid24G")).stream().filter(element -> Objects.nonNull(element.get("SSIDKey")) && element.get("SSIDKey").toString().equalsIgnoreCase("Video")).findAny().orElse(null);

                        try {
                            if (Objects.nonNull(primary24g) && Objects.nonNull(primary24g.get("ssid")))
                                networks.add(String.valueOf(primary24g.get("enabled")) + LOG_DASH_SEPARATOR + primary24g.get("ssid") + LOG_DASH_SEPARATOR + primary24g.get("password") + LOG_DASH_SEPARATOR + "primary" + LOG_DASH_SEPARATOR + (String.valueOf(primary24g.get("ssid")).equals(String.valueOf(primary5g.get("ssid"))) ? "2.4G + 5G" : "2.4G")/* : ""*/);
                            if (Objects.nonNull(guest24g) && Objects.nonNull(guest24g.get("ssid")))
                                networks.add(String.valueOf(guest24g.get("enabled")) + LOG_DASH_SEPARATOR + guest24g.get("ssid") + LOG_DASH_SEPARATOR + guest24g.get("password") + LOG_DASH_SEPARATOR + "guest" + LOG_DASH_SEPARATOR + (String.valueOf(guest24g.get("ssid")).equals(Objects.toString(guest5g.get("ssid"))) ? "2.4G + 5G" : "2.4G")/* : ""*/);
                            if (Objects.nonNull(primary5g) && Objects.nonNull(primary5g.get("ssid")))
                                networks.add(String.valueOf(primary5g.get("enabled")) + LOG_DASH_SEPARATOR + primary5g.get("ssid") + LOG_DASH_SEPARATOR + primary5g.get("password") + LOG_DASH_SEPARATOR + "primary" + LOG_DASH_SEPARATOR + (String.valueOf(primary24g.get("ssid")).equals(String.valueOf(primary5g.get("ssid"))) ? "2.4G + 5G" : "5G")/* : ""*/);
                            if (Objects.nonNull(guest5g) && Objects.nonNull(guest5g.get("ssid")))
                                networks.add(String.valueOf(guest5g.get("enabled")) + LOG_DASH_SEPARATOR + guest5g.get("ssid") + LOG_DASH_SEPARATOR + guest5g.get("password") + LOG_DASH_SEPARATOR + "guest" + LOG_DASH_SEPARATOR + (String.valueOf(guest24g.get("ssid")).equals(String.valueOf(guest5g.get("ssid"))) ? "2.4G + 5G" : "5G")/* : ""*/);
                            if (Objects.nonNull(video5g) && Objects.nonNull(video5g.get("ssid")) && Objects.nonNull(video24g)) {
                                networks.add(String.valueOf(video5g.get("enabled")) + LOG_DASH_SEPARATOR + video5g.get("ssid") + LOG_DASH_SEPARATOR + video5g.get("password") + LOG_DASH_SEPARATOR + "video" + LOG_DASH_SEPARATOR + (String.valueOf(video5g.get("ssid")).equals(String.valueOf(video24g.get("ssid"))) ? "2.4G + 5G" : "5G")/* : ""*/);
                            }
                            if (Objects.nonNull(video24g) && Objects.nonNull(video24g.get("ssid")) && Objects.nonNull(video5g)) {
                                networks.add(String.valueOf(video24g.get("enabled")) + LOG_DASH_SEPARATOR + video24g.get("ssid") + LOG_DASH_SEPARATOR + video24g.get("password") + LOG_DASH_SEPARATOR + "video" + LOG_DASH_SEPARATOR + (String.valueOf(video24g.get("ssid")).equals(String.valueOf(video5g.get("ssid"))) ? "2.4G + 5G" : "2.4G")/* : ""*/);
                            }
                        } catch (NullPointerException npe) {
                            LOG.error("Error get in wifi network list");
                        }
                    } else {
                        DBObject primary24g = ((List<DBObject>) userEquipment.get("bssid24G")).get(0);
                        DBObject guest24g = ((List<DBObject>) userEquipment.get("bssid24G")).get(1);
                        DBObject primary5g = ((List<DBObject>) userEquipment.get("bssid5G")).get(0);
                        DBObject guest5g = ((List<DBObject>) userEquipment.get("bssid5G")).get(1);
                        DBObject video5g = null;
                        DBObject video24g = null;
                        if (((List<DBObject>) userEquipment.get("bssid5G")).size() >= 2) {
                            video5g = ((List<DBObject>) userEquipment.get("bssid5G")).get(2);
                        }
                        if (((List<DBObject>) userEquipment.get("bssid24G")).size() >= 2) {
                            video24g = ((List<DBObject>) userEquipment.get("bssid24G")).get(2);
                        }

                        try {
                            if (Objects.nonNull(primary24g) && Objects.nonNull(primary24g.get("ssid")))
                                networks.add(String.valueOf(primary24g.get("enabled")) + LOG_DASH_SEPARATOR + primary24g.get("ssid") + LOG_DASH_SEPARATOR + primary24g.get("password") + LOG_DASH_SEPARATOR + "primary" + LOG_DASH_SEPARATOR + (String.valueOf(primary24g.get("ssid")).equals(String.valueOf(primary5g.get("ssid"))) ? "2.4G + 5G" : "2.4G")/* : ""*/);
                            if (Objects.nonNull(guest24g) && Objects.nonNull(guest24g.get("ssid")))
                                networks.add(String.valueOf(guest24g.get("enabled")) + LOG_DASH_SEPARATOR + guest24g.get("ssid") + LOG_DASH_SEPARATOR + guest24g.get("password") + LOG_DASH_SEPARATOR + "guest" + LOG_DASH_SEPARATOR + (String.valueOf(guest24g.get("ssid")).equals(String.valueOf(guest5g.get("ssid"))) ? "2.4G + 5G" : "2.4G")/* : ""*/);
                            if (Objects.nonNull(primary5g) && Objects.nonNull(primary5g.get("ssid")))
                                networks.add(String.valueOf(primary5g.get("enabled")) + LOG_DASH_SEPARATOR + primary5g.get("ssid") + LOG_DASH_SEPARATOR + primary5g.get("password") + LOG_DASH_SEPARATOR + "primary" + LOG_DASH_SEPARATOR + (String.valueOf(primary24g.get("ssid")).equals(String.valueOf(primary5g.get("ssid"))) ? "2.4G + 5G" : "5G")/* : ""*/);
                            if (Objects.nonNull(guest5g) && Objects.nonNull(guest5g.get("ssid")))
                                networks.add(String.valueOf(guest5g.get("enabled")) + LOG_DASH_SEPARATOR + guest5g.get("ssid") + LOG_DASH_SEPARATOR + guest5g.get("password") + LOG_DASH_SEPARATOR + "guest" + LOG_DASH_SEPARATOR + (String.valueOf(guest24g.get("ssid")).equals(String.valueOf(guest5g.get("ssid"))) ? "2.4G + 5G" : "5G")/* : ""*/);
                            if (Objects.nonNull(video5g) && Objects.nonNull(video5g.get("ssid")) && Objects.nonNull(video24g)) {
                                networks.add(String.valueOf(video5g.get("enabled")) + LOG_DASH_SEPARATOR + video5g.get("ssid") + LOG_DASH_SEPARATOR + video5g.get("password") + LOG_DASH_SEPARATOR + "video" + LOG_DASH_SEPARATOR + (String.valueOf(video5g.get("ssid")).equals(String.valueOf(video24g.get("ssid"))) ? "2.4G + 5G" : "5G")/* : ""*/);
                            }
                            if (Objects.nonNull(video24g) && Objects.nonNull(video24g.get("ssid")) && Objects.nonNull(video5g)) {
                                networks.add(String.valueOf(video24g.get("enabled")) + LOG_DASH_SEPARATOR + video24g.get("ssid") + LOG_DASH_SEPARATOR + video24g.get("password") + LOG_DASH_SEPARATOR + "video" + LOG_DASH_SEPARATOR + (String.valueOf(video24g.get("ssid")).equals(String.valueOf(video5g.get("ssid"))) ? "2.4G + 5G" : "2.4G")/* : ""*/);
                            }
                        } catch (NullPointerException npe) {
                            LOG.error("Error get in wifi network list", npe);
                        }
                    }

                    networks.forEach(i -> {
                        NetworkListDTO.NetworkListData networkListData = networkListDTO.new NetworkListData();
                        networkListData.setEnabled(i.split(LOG_DASH_SEPARATOR)[0].equals("1") ? true : false);
                        networkListData.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : i.split(LOG_DASH_SEPARATOR)[1]);
                        networkListData.setPassword(dataSecurityMapping.contains(DataSecurityType.ssid_Password.name()) ? manageCommonService.encrypt() : i.split(LOG_DASH_SEPARATOR)[2]);
                        networkListData.setType(i.split(LOG_DASH_SEPARATOR)[3]);
                        networkListData.setBand(i.split(LOG_DASH_SEPARATOR)[4]);
                        networkListData.setSerialNumber(String.valueOf(userEquipment.get("serialNumber")));
                        networkListData.setApType(String.valueOf(userEquipment.get("type")));
                        networkListDataList.add(networkListData);
                    });
                }
            });
        }

        if (networkListDataList.isEmpty()) {
            NetworkListDTO.NetworkListData networkListData = networkListDTO.new NetworkListData();
            networkListDataList.add(networkListData);
        }
        networkListDTO.setData(networkListDataList);

        return networkListDTO;
    }

    public TopologyDTO getTopology(String equipmentIdOrSerialOrSTN) throws Exception {
        TopologyDTO topologyDTO = new TopologyDTO();
        TopologyDTO.TopologyData topologyData = topologyDTO.new TopologyData();
        EquipmentDetail equipmentDetail = new EquipmentDetail();

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);

        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        params.put("userId", userEquipment.getRgwSerial());
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("moca", 0);
        List<BasicDBObject> equipmentList = mongoService.findList(params, ActiontecConstants.AP_DETAIL, mongoFieldOptions);
        equipmentList = manageCommonService.filterEquipmentsAndCheckForRgwAndExt(equipmentList);

        HashMap<String, String> aggregationParams = new HashMap<>();
        aggregationParams.put("outputParams", "devices,count");
        aggregationParams.put("label", "$serialNumber");
        aggregationParams.put("operation", "$push,$sum");
        aggregationParams.put("keyToAggregate", "$$ROOT,1");
        aggregationParams.put("operand", "$gte");
        mongoFieldOptions.clear();
        mongoFieldOptions.put("devices._id", 0);
        List<BasicDBObject> devicesList = new ArrayList();
        List<BasicDBObject> StationDevices = mongoService.aggregate(params, null, ActiontecConstants.STATION_DETAIL, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_3_DAYS), aggregationParams, TIMESTAMP, mongoFieldOptions);
        if (Objects.nonNull(StationDevices) && !StationDevices.isEmpty()) {
            for (int i = 0; i < StationDevices.size(); i++) {
                devicesList.addAll((List<BasicDBObject>) StationDevices.get(i).get("devices"));
            }
        }
        String isp = equipmentList.get(ZERO).getString("isp");
        String version = equipmentList.get(ZERO).getString("etlVersion");
        String actualIsp = isp.split(HYPHEN_STRING)[0];
        if (!VERIZON_ISP.equals(actualIsp) || (VERIZON_ISP.equals(actualIsp) && version.equalsIgnoreCase("v4")))
            devicesList.addAll(manageCommonService.getHostnameDetail(userEquipment.getRgwSerial()));
        HashMap<String, List<BasicDBObject>> allDevices = new HashMap();
        for (BasicDBObject device : devicesList) {
            if (!allDevices.containsKey(device.getString("macAddress"))) {
                allDevices.put(device.getString("macAddress"), new ArrayList());
            }
            allDevices.get(device.getString("macAddress")).add(device);
        }
        ArrayList<BasicDBObject> filteredDeviceList = new ArrayList<>();
        allDevices.values().forEach(deviceList -> {
            if (deviceList.size() == ONE) {
                filteredDeviceList.addAll(deviceList);
            } else {
                for (BasicDBObject d : deviceList) {
                    if (Objects.isNull(d.getString("phyType"))) {
                        filteredDeviceList.add(d);
                        break;
                    }
                }
            }
        });
        ArrayList<ExtenderDetail> extenderDetailList = new ArrayList<>();
        HashSet<String> connectivityStatus = new HashSet<>();
        HashSet<Integer> severity = new HashSet<>();
        for (BasicDBObject equipment : equipmentList) {
            addDevicesToEquipment(userEquipment, filteredDeviceList, equipmentDetail, extenderDetailList, connectivityStatus, severity, equipment);
        }

        if (Objects.isNull(equipmentDetail.getDevices())) {
            equipmentDetail.setDevices(new ArrayList<>());
        }
        if (Objects.isNull(equipmentDetail.getAlarms()))
            equipmentDetail.setAlarms(new ArrayList<>());
        equipmentDetail.setExtenders(extenderDetailList);
        topologyData.setSeverity(-1);
        if (!connectivityStatus.isEmpty() && connectivityStatus.size() == 1 && connectivityStatus.iterator().next().equals("RED")) {
            topologyData.setSeverity(2);
        } else if (!severity.isEmpty() && severity.size() == 1) {
            topologyData.setSeverity(severity.iterator().next());
        } else if (severity.size() > 1) {
            topologyData.setSeverity(Collections.max(severity));
        }
        topologyData.setEquipment(equipmentDetail);
        topologyDTO.setData(topologyData);
        return topologyDTO;
    }


    private void addDevicesToEquipment(Equipment userEquipment, List<BasicDBObject> devicesList, EquipmentDetail equipmentDetail, ArrayList<ExtenderDetail> extenderDetailList, HashSet<String> connectivityStatus, HashSet<Integer> severity, BasicDBObject equipment) throws Exception {
        String serialNumber = equipment.getString("serialNumber");
        List<BasicDBObject> filteredDeviceList = devicesList.stream().filter(d -> d.getString("serialNumber").equals(serialNumber)).collect(Collectors.toList());
        //ArrayList<DeviceDetail> deviceDetailList = new ArrayList<>();
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList = new ArrayList<>();
        if (!filteredDeviceList.isEmpty()) {
            List<BasicDBObject> devices = filteredDeviceList;
            HashSet<String> deviceMacList = devices.stream().filter((device) -> Objects.isNull(device.getString("phyType"))).map((device) -> device.getString("macAddress")).collect(Collectors.toCollection(HashSet::new));
            HashMap<String, String> stationFriendlyName = manageCommonService.getFriendlyNameForStation(deviceMacList, userEquipment);
            for (BasicDBObject device : devices) {
                if (Objects.nonNull(device)) {
                    DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
                    DeviceDetailDTO.DeviceDetailData deviceDetail = deviceDetailDTO.new DeviceDetailData();
                    if (Objects.isNull(device.getString("phyType"))) {
                        addStationDevices(userEquipment, equipment, serialNumber, deviceDetailList, stationFriendlyName, device, deviceDetail);
                    } else {
                        addMiscDevices(device, deviceDetailList, userEquipment);
                    }
                }

            }
        }

        if (equipment.getString("type").equals(GATEWAY)) {
            equipmentDetail.setDevices(deviceDetailList);
            equipmentDetail.setMacAddress(String.valueOf(equipment.get("macAddress")));
            equipmentDetail.setName((Objects.isNull(equipment.get("friendlyName")) || equipment.get("friendlyName").toString().trim().isEmpty()) ? (Objects.isNull(equipment.get("modelName")) || equipment.get("modelName").toString().trim().isEmpty()) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            equipmentDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
            equipmentDetail.setType(equipment.getString("type"));
            equipmentDetail.setSerialNumber(equipment.getString("serialNumber"));
            //// ALARMS FOR EQUIPMENT START
            List<HashMap<String, Object>> alarmListForEquipment = manageEquipmentService.getEquipmentAlarmList(equipment, userEquipment);
            equipmentDetail.setAlarms(alarmListForEquipment);
            equipmentDetail.setSeverity(calculateSeverityForEquipment(/*deviceDetailList,*/ alarmListForEquipment, equipmentDetail.getConnectivityStatus()));
            severity.add(equipmentDetail.getSeverity());
//                severity.add(calculateSeverityForDevice(deviceDetailList));
            connectivityStatus.add(equipmentDetail.getConnectivityStatus());
            //// ALARMS FOR EQUIPMENT END
            for (BasicDBObject wifiRadio : (List<BasicDBObject>) equipment.get("wifiRadios")) {
                if (wifiRadio.getString("band") != null) {
                    if ("5G".equals(wifiRadio.getString("band"))) {
                        equipmentDetail.set_5GChannelNumber(Integer.valueOf(String.valueOf(wifiRadio.get("channel"))));
                    } else {
                        equipmentDetail.set_24GChannelNumber(Integer.valueOf(String.valueOf(wifiRadio.get("channel"))));
                    }
                }
            }
        } else if ((equipment.getString("type").equals("EXTENDER"))) {
            ExtenderDetail extenderDetail = new ExtenderDetail();
            extenderDetail.setDevices(deviceDetailList);
            extenderDetail.setMacAddress(String.valueOf(equipment.get("macAddress")));
            extenderDetail.setName((Objects.isNull(equipment.get("friendlyName")) || equipment.get("friendlyName").toString().trim().isEmpty()) ? (Objects.isNull(equipment.get("modelName")) || equipment.get("modelName").toString().trim().isEmpty()) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
            extenderDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
            extenderDetail.setType(equipment.getString("type"));
            extenderDetail.setSerialNumber(equipment.getString("serialNumber"));

            //// ALARMS FOR EQUIPMENT START
            List<HashMap<String, Object>> alarmListForExtender = manageEquipmentService.getEquipmentAlarmList(equipment, userEquipment);

            extenderDetail.setAlarms(alarmListForExtender);
            extenderDetail.setSeverity(calculateSeverityForEquipment(/*deviceDetailList,*/ alarmListForExtender, extenderDetail.getConnectivityStatus()));
            severity.add(extenderDetail.getSeverity());
//                severity.add(calculateSeverityForDevice(deviceDetailList));
            connectivityStatus.add(extenderDetail.getConnectivityStatus());
            //// ALARMS FOR EQUIPMENT END
            for (BasicDBObject wifiRadio : (List<BasicDBObject>) equipment.get("wifiRadios")) {
                if (Objects.nonNull(wifiRadio.getString("band"))) {
                    if ("5G".equals(wifiRadio.getString("band"))) {
                        extenderDetail.set_5GChannelNumber(Integer.valueOf(wifiRadio.getString("channel")));
                    } else {
                        extenderDetail.set_24GChannelNumber(Integer.valueOf(wifiRadio.getString("channel")));
                    }
                }
            }
            extenderDetailList.add(extenderDetail);
        }
    }

    private void addStationDevices(Equipment userEquipment, BasicDBObject equipment, String serialNumber, ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList, HashMap<String, String> stationFriendlyName, BasicDBObject device, DeviceDetailDTO.DeviceDetailData deviceDetail) throws Exception {
        String band = Objects.nonNull(device.get("band")) ? device.get("band").toString() : null;
        String userId = Objects.nonNull(device.get("userId")) ? device.get("userId").toString() : null;
        String serialNo = Objects.isNull(device.get("serialNumber")) ? null : device.get("serialNumber").toString();
        String bssid = Objects.isNull(device.get("bssid")) ? null : device.get("bssid").toString();
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        if (band != null || userId != null || serialNo != null || bssid != null) {
            deviceDetail.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : manageCommonService.getSsidForDeviceDetail(userId, serialNo, bssid));
        } else {
            deviceDetail.setSsid("N/A");
        }
        deviceDetail.setFromDSHost(false);
        deviceDetail.setMac(String.valueOf(device.get("macAddress")));
        deviceDetail.setDeviceType(Objects.isNull(device.get("deviceType")) ? "Other" : device.get("deviceType").toString());
        deviceDetail.setConnectedTo(serialNumber);
        HashSet<String> serialNumbers = new HashSet<>();
        serialNumbers.add(deviceDetail.getConnectedTo());
        HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
        deviceDetail.setConnectedToName(equipmentFriendlyName.get(deviceDetail.getConnectedTo()));
        deviceDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(device)); //Changed from status to ConnectivityStatus to make sync with device detail api
        deviceDetail.setName(stationFriendlyName.get(device.get("macAddress").toString()));
        deviceDetail.setBand(Objects.isNull(device.get("band")) ? "N/A" : device.get("band").toString());
        deviceDetail.setRssi(Objects.isNull(device.get("rssi")) ? 0.0 : Double.valueOf(device.get("rssi").toString()));
        deviceDetail.setIp(String.valueOf(device.get("ip")));
        deviceDetail.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetail.getMac(), userEquipment.getRgwSerial()));
        deviceDetail.setWifiMode(Objects.isNull(device.get("wifiMode")) ? "N/A" : device.get("wifiMode").toString());
        deviceDetail.setVendor(manageCommonService.getVendorName(String.valueOf(device.get("macAddress"))));

        Set<String> macAddressSet = new HashSet<>();
        macAddressSet.add(String.valueOf(device.get("macAddress")));
        deviceDetail.setHostname(manageCommonService.getHostNameForStation(macAddressSet, userEquipment).get(device.get("macAddress").toString()));

        deviceDetail.setDownlinkPhyRate(Objects.isNull(device.get("uplinkPhyRate")) ? 0 : Double.valueOf(device.get("uplinkPhyRate").toString()));
        deviceDetail.setUplinkPhyRate(Objects.isNull(device.get("downlinkPhyRate")) ? 0 : Double.valueOf(device.get("downlinkPhyRate").toString()));
        deviceDetail.setDownlinkErrors(Objects.isNull(device.get("downlinkErrors")) ? 0 : Double.valueOf(device.get("downlinkErrors").toString()));
        deviceDetail.setDownlinkRetransmissions(Objects.isNull(device.get("downlinkRetransmissions")) ? 0 : Double.valueOf(String.valueOf(device.get("downlinkRetransmissions"))));

        DeviceUtilityULDL deviceUtilityULDL = new DeviceUtilityULDL(String.valueOf(device.get("macAddress")), userEquipment, mongoService, manageCommonService).invoke();
        deviceDetail.setUplinkBytes(deviceUtilityULDL.getUpLinkBytes());
        deviceDetail.setDownlinkBytes(deviceUtilityULDL.getDownLinkBytes());

        String capabilityFromStationDetail = manageCommonService.getCapabilityFromDB(device);
        String version = equipment.getString("etlVersion");
        if ("v3".equalsIgnoreCase(version)) {
            List<DeviceCapability> deviceCapability = null;
            HashMap<String, String> queryParams = new HashMap<>();
            queryParams.put("userId", userEquipment.getRgwSerial());
            queryParams.put("macAddress", device.getString("macAddress"));
            if (Objects.isNull(capabilityFromStationDetail) || capabilityFromStationDetail.isEmpty()) {
                HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
                if ((Objects.nonNull(commonProps.get(ABS_DATABASE)) && commonProps.get(ABS_DATABASE).equalsIgnoreCase("MONGO")) ? true : false) {
                    List<DBObject> deviceCapabilityMongo = mongoService.findList(DEVICE_CAPABILITY, queryParams, TIMESTAMP, DESC);
                    deviceCapability = manageCommonService.convertToDeviceCapability(deviceCapabilityMongo);
                } else {
                    deviceCapability = (List<DeviceCapability>) cassandraRepository.read(DeviceCapability.class, queryParams, null, null, new ArrayList<>(), 0);
                }
                deviceDetail.setCapability(Objects.nonNull(deviceCapability) && !deviceCapability.isEmpty() ? (Objects.isNull(deviceCapability.get(0).getCapability()) ? null : deviceCapability.get(0).getCapability()) : null);
            } else {
                deviceDetail.setCapability(capabilityFromStationDetail);
            }
        } else {
            deviceDetail.setCapability(capabilityFromStationDetail);
        }

        deviceDetail.setIsp(Objects.isNull(device.get("isp")) ? "N/A" : String.valueOf(device.get("isp")));

        StationSpeedTest stationSpeedTest = null;
        HashMap<String, Object> query = new HashMap<>();
        query.put("macAddr", String.valueOf(device.get("macAddress")));
        List<DBObject> stationSpeedTestList = mongoService.findList(STATION_SPEED_TEST_INFO, query, DATE, DESC);
        DeviceDetailDTO.DeviceDetailData.DeviceSpeedTest deviceSpeedTest = deviceDetail.new DeviceSpeedTest();
        if (Objects.nonNull(stationSpeedTestList) && !stationSpeedTestList.isEmpty()) {
            stationSpeedTest = new StationSpeedTest();
            stationSpeedTest.setDate(((Date) stationSpeedTestList.get(0).get("date")));
            stationSpeedTest.setDataRate(Objects.isNull(stationSpeedTestList.get(0).get("dataRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("dataRate"))));
            deviceSpeedTest.setDataRate(stationSpeedTest.getDataRate());
            deviceSpeedTest.setDate(Objects.isNull(stationSpeedTest.getDate()) ? 0 : stationSpeedTest.getDate());
        } else {
            deviceSpeedTest.setDataRate(0f);
            deviceSpeedTest.setDate(0l);
        }
        deviceDetail.setSpeedTestStats(deviceSpeedTest);
        // ALARMS for DEVICE START
        deviceDetail.setAlarms(manageDeviceService.getAlarmListForDevice(deviceDetail, userId, manageDeviceService.getRgDfsEnabled(equipment)));
        deviceDetail.setLastReportAt(Objects.nonNull(device.get("lastReportAt")) ? Long.valueOf(device.get("lastReportAt").toString()) : 0);
        HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
        int rssiRange = LOWER_LIMIT;
        try {
            rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
        } catch (Exception e) {

        }


        deviceDetail.setRssiRange(rssiRange);
        // ALARMS for DEVICE END
        deviceDetailList.add(deviceDetail);

    }

    /*private void filterDevices(TopologyDTO topologyDTO){
        List<DeviceDetailDTO.DeviceDetailData> equipmentDevices = topologyDTO.getData().getEquipment().getDevices();
        if(equipmentDevices.isEmpty())
            return;
        List<DeviceDetailDTO.DeviceDetailData> extenderDevices = topologyDTO.getData().getEquipment().getDevices();
        if(extenderDevices.isEmpty())
            return;
        for(ExtenderDetail extenderDetail : topologyDTO.getData().getEquipment().getExtenders()){
            Iterator<DeviceDetailDTO.DeviceDetailData> itr = extenderDetail.getDevices().iterator();
            while (itr.hasNext()){
                DeviceDetailDTO.DeviceDetailData device = itr.next();
                DeviceDetailDTO.DeviceDetailData matchedMAC = equipmentDevices.stream().filter(q -> q.getMac().equals(device.getMac())).findAny().orElse(null);
                if(Objects.nonNull(matchedMAC)){
                    if("2.4 GHz".equals(device.getBand()) || "5 GHz".equals(device.getBand()) || "WiFi".equals(device.getBand())){
                        equipmentDevices.remove(matchedMAC);
                    }
                    else {
                        itr.remove();
                    }
                }
            }
        }
    }*/

    public void addMiscDevices(BasicDBObject q, ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList, Equipment userEquipment) throws Exception {
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
        DeviceDetailDTO.DeviceDetailData deviceDetail = deviceDetailDTO.new DeviceDetailData();
        deviceDetail.setSsid("N/A");
        deviceDetail.setMac(String.valueOf(q.get("macAddress")));
        deviceDetail.setDeviceType(q.getBoolean("isExtender") ? "EXT" : (Objects.isNull(q.get("deviceType")) ? "Other" : q.getString("deviceType")));
        deviceDetail.setConnectedTo(q.getString("serialNumber"));
        HashSet<String> serialNumbers = new HashSet<>();
        serialNumbers.add(deviceDetail.getConnectedTo());
        HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
        deviceDetail.setConnectedToName(equipmentFriendlyName.get(deviceDetail.getConnectedTo()));
        deviceDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(q)); //Changed from status to ConnectivityStatus to make sync with device detail api
        deviceDetail.setName(manageCommonService.getDisplayNameByPriorityForDevice(q));
        deviceDetail.setBand(q.getString("phyType"));
        deviceDetail.setRssi(0.0);
        deviceDetail.setIp(String.valueOf(q.get("ip")));
        deviceDetail.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetail.getMac(), q.getString("userId")));
        deviceDetail.setWifiMode("N/A");
        // ALARMS for DEVICE START
        deviceDetail.setAlarms(new ArrayList<>());
        deviceDetail.setLastReportAt(q.getLong(TIMESTAMP));
        deviceDetail.setRssiRange(0);
        deviceDetail.setFromDSHost(true);
        deviceDetail.setVendor(manageCommonService.getVendorName(String.valueOf(q.get("macAddress"))));
        // ALARMS for DEVICE END
        deviceDetailList.add(deviceDetail);

    }

    private int calculateSeverityForEquipment(/*ArrayList<DeviceDetail> deviceDetailList,*/ List<HashMap<String, Object>> alarmListForEquipment, String connectivityStatus) {
        int alarmLevelSeverity = -1;
        /*for (DeviceDetail deviceDetail : deviceDetailList) {
            for (HashMap<String, Object> alarm : deviceDetail.getAlarms()) {
                if (Integer.parseInt(alarm.get("severity").toString()) > alarmLevelSeverity) {
                    alarmLevelSeverity = Integer.parseInt(alarm.get("severity").toString());
                }
            }
        }*/
        for (HashMap<String, Object> i : alarmListForEquipment) {
            if (Integer.parseInt(i.get("severity").toString()) > alarmLevelSeverity) {
                alarmLevelSeverity = Integer.parseInt(i.get("severity").toString());
            }
        }

        return (alarmLevelSeverity == 1 && "RED".equals(connectivityStatus)) ? 1 : alarmLevelSeverity;
    }

    /*private int calculateSeverityForDevice(ArrayList<DeviceDetail> deviceDetailList) {
        int alarmLevelSeverity = -1;
        for (DeviceDetail deviceDetail : deviceDetailList) {
            for (HashMap<String, Object> alarm : deviceDetail.getAlarms()) {
                if (Integer.parseInt(alarm.get("severity").toString()) > alarmLevelSeverity) {
                    alarmLevelSeverity = Integer.parseInt(alarm.get("severity").toString());
                }
            }
        }

        return alarmLevelSeverity;
    }*/
    //@Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SMART_STEERING_ENABLE_DISABLE)
    public void modifySmartSteering(String equipmentIdOrSerialOrSTN, boolean enable, HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        String rpcUri = RpcConstants.STEERING_URI;
        Map<String, Object> payloadMap = new HashMap<>();
        payloadMap.put("enable", enable);
        String payload = objectMapper.writeValueAsString(payloadMap);

        String tid = CommonUtils.generateUUID();
        String serial = manageCommonService.getControllerSerialByUserId(userEquipment.getRgwSerial()).orElseGet(()->userEquipment.getRgwSerial());

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
        cpeRpcService.sendRpcAndWaitResult(tid, userEquipment.getRgwSerial(), serial, rpcUri, "PUT", payload, max_Tries, THREAD_TO_SLEEP);
    }

    public boolean getStatusOfSmartSteering(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        return manageCommonService.isSmartSteeringEnabledForDeviceMac(userEquipment.getRgwSerial(), userEquipment.getRgwSerial());
    }
}
