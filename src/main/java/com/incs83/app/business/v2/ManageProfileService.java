//package com.incs83.app.business.v2;
//
//import com.incs83.app.common.v2.CreateUserProfileRequest;
//import com.incs83.app.common.v2.EditUserProfileRequest;
//import com.incs83.app.common.v2.ScheduleDetails;
//import com.incs83.app.entities.Compartment;
//import com.incs83.app.entities.Equipment;
//import com.incs83.app.responsedto.v2.Device.DeviceListDTO;
//import com.incs83.app.responsedto.v2.Profile.ProfileDevicesDTO;
//import com.incs83.app.responsedto.v2.Profile.ProfileListDTO;
//import com.incs83.app.responsedto.v2.Profile.SubscriberProfileDTO;
//import com.incs83.app.responsedto.v2.Profile.SubscriberProfileListDTO;
//import com.incs83.app.service.data.MongoServiceImpl;
//import com.incs83.app.utils.CalendarUtils;
//import com.incs83.app.utils.ValidationUtil;
//import com.incs83.exceptions.handler.ValidationException;
//import com.incs83.mt.DataAccessService;
//import com.incs83.util.CommonUtils;
//import com.mongodb.BasicDBObject;
//import com.mongodb.DBObject;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//import static com.incs83.app.constants.misc.ActiontecConstants.*;
//import static com.incs83.app.constants.misc.ApplicationConstants.*;
//import static com.incs83.util.CommonUtils.getCurrentTimeInMillis;
//
///**
// * Created by Jayant on 29/1/18.
// */
//@Service
//public class ManageProfileService {
//
//    @Autowired
//    private ManageCommonService manageCommonService;
//
//    @Autowired
//    private MongoServiceImpl mongoService;
//
//    @Autowired
//    private ManageDeviceService manageDeviceService;
//
//    @Autowired
//    private ManageProfileService manageProfileServiceV3;
//
//    @Autowired
//    private DataAccessService dataAccessService;
//
//    private static final Logger LOG = LogManager.getLogger("org");
//
//    public SubscriberProfileDTO createUserProfile(String serialNumberOrRGWMAC, CreateUserProfileRequest userProfileRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//        if (Objects.nonNull(userProfileRequest.getProfileName()))
//            isProfileNameAlreadyExistForUser(userProfileRequest.getProfileName(), userEquipment);
//        HashMap<String, Object> profileObj = new HashMap<>();
//        profileObj.put("id", CommonUtils.generateUUID());
//        profileObj.put("profileName", userProfileRequest.getProfileName());
//        profileObj.put("devices", new ArrayList<>());
//        profileObj.put("contentFilters", new ArrayList<Integer>());
//        profileObj.put("currentState", UNPAUSE);
//        profileObj.put("onDemandState", UNPAUSE);
//        profileObj.put("timedAccess", null);
//        profileObj.put("scheduleActivated", false);
//        profileObj.put("scheduleDetails", null);
//        profileObj.put("subscriberId", userEquipment.getRgwSerial());
//        profileObj.put("createdAt", getCurrentTimeInMillis());
//        profileObj.put("updatedAt", getCurrentTimeInMillis());
//        profileObj.put("createdBy", CommonUtils.getUserIdOfLoggedInUser());
//        mongoService.create(USER_PROFILE, profileObj);
//
//        SubscriberProfileDTO subscriberProfileDTO = new SubscriberProfileDTO();
//        SubscriberProfileDTO.SubscriberProfileData subscriberProfileData = subscriberProfileDTO.new SubscriberProfileData();
//        subscriberProfileData.setId(profileObj.get("id").toString());
//        subscriberProfileData.setProfileName(profileObj.get("profileName").toString());
//        subscriberProfileData.setDevices(new ArrayList<>());
//        subscriberProfileData.setCurrentState(true);
//        subscriberProfileData.setOnDemandState(true);
//        subscriberProfileData.setScheduleActivated(false);
//        subscriberProfileData.setSubscriberId(profileObj.get("subscriberId").toString());
//        subscriberProfileData.setScheduleDetails(new ArrayList<>());
//
//        subscriberProfileDTO.setData(subscriberProfileData);
//        subscriberProfileDTO.setCode(0);
//        subscriberProfileDTO.setMessage("Request Completed Successfully");
//
//        return subscriberProfileDTO;
//    }
//
//    public SubscriberProfileDTO fetchUserProfileById(String id) throws Exception {
//        BasicDBObject projection = new BasicDBObject();
//        projection.put("_class", 0);
//        projection.put("_id", 0);
//        HashMap<String, String> queryParams = new HashMap<>();
//        HashMap<String, String> appendableParams = new HashMap<>();
//        queryParams.put("id", id);
//        DBObject profileObj = mongoService.findOne(queryParams, appendableParams, USER_PROFILE, TIMESTAMP, DESC, projection);
//        if (Objects.isNull(profileObj)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Profile found with Id: " + id);
//        }
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(String.valueOf(profileObj.get("subscriberId")));
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//        Set<String> profileDevices = new HashSet<>((List<String>) profileObj.get("devices"));
//
//        Set<String> allDevices = getSubscriberAllDevices(String.valueOf(profileObj.get("subscriberId")));
//        Set<String> devicesAttachToAllProfile = getAllDevicesAttachToProfile(String.valueOf(profileObj.get("subscriberId")));
//
//        allDevices.removeAll(devicesAttachToAllProfile);
//
//        List<HashMap<String, Object>> devicesAttachToProfile = populateFriendlyNameDataForDeviceMAC(manageCommonService.getFriendlyNameForStation(profileDevices, userEquipment));
//
//        devicesAttachToProfile.forEach(device -> {
//            device.put("isAttachToProfile", true);
//        });
//
//        List<HashMap<String, Object>> devicesDetattachToProfile = populateFriendlyNameDataForDeviceMAC(manageCommonService.getFriendlyNameForStation(allDevices, userEquipment));
//        devicesDetattachToProfile.forEach(device -> {
//            device.put("isAttachToProfile", false);
//        });
//
//        devicesAttachToProfile.addAll(devicesDetattachToProfile);
//
//        SubscriberProfileDTO subscriberProfileDTO = new SubscriberProfileDTO();
//
//        SubscriberProfileDTO.SubscriberProfileData subscriberProfileData = subscriberProfileDTO.new SubscriberProfileData();
//        subscriberProfileData.setId(String.valueOf(profileObj.get("id")));
//        subscriberProfileData.setProfileName(String.valueOf(profileObj.get("profileName")));
//        subscriberProfileData.setSubscriberId(String.valueOf(profileObj.get("subscriberId")));
//        subscriberProfileData.setScheduleActivated(Objects.isNull(profileObj.get("scheduleActivated")) ? false : Boolean.valueOf(profileObj.get("scheduleActivated").toString()));
//        subscriberProfileData.setCurrentState(PAUSE.equalsIgnoreCase(String.valueOf(profileObj.get("currentState"))) ? false : true);
//        subscriberProfileData.setOnDemandState(PAUSE.equalsIgnoreCase(String.valueOf(profileObj.get("onDemandState"))) ? false : true);
//        subscriberProfileData.setNextActionDetails(getNextActionTimeRemaining(profileObj));
//        subscriberProfileData.setContentFilters(Objects.isNull(profileObj.get("contentFilters")) ? new ArrayList<>() : ((ArrayList<Integer>) profileObj.get("contentFilters")));
//
//        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, userEquipment.getGroupId());
//        String ispName = "N/A";
//        if (Objects.nonNull(compartment)) {
//            HashMap<String, Object> query = new HashMap<>();
//            query.put("id", compartment.getIspId());
//
//            BasicDBObject fieldsToRemove = new BasicDBObject();
//            fieldsToRemove.put("_id", ZERO);
//
//            DBObject dbObject = mongoService.findOne(query, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
//            if (Objects.nonNull(dbObject))
//                ispName = String.valueOf(dbObject.get("name"));
//        }
//
//        subscriberProfileData.setIspName(ispName);
//
//        ArrayList<ProfileDevicesDTO> deviceList = new ArrayList<>();
//        devicesAttachToProfile.forEach(device -> {
//            ProfileDevicesDTO profileDevicesDTO = new ProfileDevicesDTO();
//            profileDevicesDTO.setName(String.valueOf(device.get("name")));
//            profileDevicesDTO.setMac(String.valueOf(device.get("mac")));
//            profileDevicesDTO.setAttachToProfile(Objects.isNull(device.get("isAttachToProfile")) ? false : Boolean.valueOf(device.get("isAttachToProfile").toString()));
//            deviceList.add(profileDevicesDTO);
//        });
//        subscriberProfileData.setDevices(deviceList);
//        ArrayList<ScheduleDetails> scheduleDetailList = new ArrayList<>();
//        if (Objects.nonNull(profileObj.get("scheduleDetails"))) {
//            int i = 1;
//            for (HashMap<String, Object> schedule : (List<HashMap<String, Object>>) profileObj.get("scheduleDetails")) {
//                if (Objects.nonNull(schedule)) {
//                    ScheduleDetails scheduleDetails = new ScheduleDetails();
//                    scheduleDetails.setDay(Objects.isNull(schedule.get("dayOfTheWeek")) ? i : Integer.parseInt(String.valueOf(schedule.get("dayOfTheWeek"))));
//                    scheduleDetails.setInternetOffTime(Objects.isNull(schedule.get("internetOffTime")) ? 0 : Long.parseLong(String.valueOf(schedule.get("internetOffTime"))));
//                    scheduleDetails.setInternetOnTime(Objects.isNull(schedule.get("internetOnTime")) ? 0 : Long.parseLong(String.valueOf(schedule.get("internetOnTime"))));
//                    scheduleDetailList.add(scheduleDetails);
//                } else {
//                    ScheduleDetails scheduleDetails = new ScheduleDetails();
//                    scheduleDetails.setDay(i);
//                    scheduleDetails.setInternetOffTime(CalendarUtils.getCalendarInstanceForYesterday().getTimeInMillis());
//                    scheduleDetails.setInternetOnTime(CalendarUtils.getCalendarInstanceForYesterday().getTimeInMillis());
//                    scheduleDetailList.add(scheduleDetails);
//                }
//                i++;
//            }
//            subscriberProfileData.setScheduleDetails(scheduleDetailList);
//        } else {
//            subscriberProfileData.setScheduleDetails(new ArrayList<>());
//        }
//        subscriberProfileDTO.setData(subscriberProfileData);
//        return subscriberProfileDTO;
//    }
//
//    private List<HashMap<String, Object>> populateFriendlyNameDataForDeviceMAC(HashMap<String, String> friendlyNameMap) {
//        List<HashMap<String, Object>> deviceList = new ArrayList<>();
//        friendlyNameMap.forEach((k, v) -> {
//            HashMap<String, Object> devicesData = new HashMap<>();
//            devicesData.put("mac", k);
//            devicesData.put("name", v);
//            deviceList.add(devicesData);
//        });
//        return deviceList;
//    }
//
//    public SubscriberProfileListDTO fetchAllUserProfiles(String serialNumberOrRGWMAC) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        BasicDBObject projection = new BasicDBObject();
//        projection.put("_class", 0);
//        projection.put("_id", 0);
//        HashMap<String, String> queryParams = new HashMap<>();
//        queryParams.put("subscriberId", userEquipment.getRgwSerial());
//        List<DBObject> profileObjList = mongoService.findList(queryParams, USER_PROFILE, projection);
//        profileObjList.forEach(item -> {
//            Set<String> devices = new HashSet<>((List<String>) item.get("devices"));
//            item.put("devices", populateFriendlyNameDataForDeviceMAC(manageCommonService.getFriendlyNameForStation(devices, userEquipment)));
//        });
//
//
//        SubscriberProfileListDTO subscriberProfileListDTO = new SubscriberProfileListDTO();
//        ArrayList<SubscriberProfileDTO.SubscriberProfileData> subscriberProfileDataList = new ArrayList<>();
//        SubscriberProfileDTO subscriberProfileDTO = new SubscriberProfileDTO();
//        profileObjList.forEach(element -> {
//            SubscriberProfileDTO.SubscriberProfileData subscriberProfileData = subscriberProfileDTO.new SubscriberProfileData();
//            subscriberProfileData.setId(String.valueOf(element.get("id")));
//            subscriberProfileData.setProfileName(String.valueOf(element.get("profileName")));
//            subscriberProfileData.setSubscriberId(String.valueOf(element.get("subscriberId")));
//            subscriberProfileData.setScheduleActivated(Objects.isNull(element.get("scheduleActivated")) ? false : Boolean.valueOf(element.get("scheduleActivated").toString()));
//            subscriberProfileData.setCurrentState(PAUSE.equalsIgnoreCase(String.valueOf(element.get("currentState"))) ? false : true);
//            subscriberProfileData.setOnDemandState(PAUSE.equalsIgnoreCase(String.valueOf(element.get("currentState"))) ? false : true);
//            subscriberProfileData.setNextActionDetails(getNextActionTimeRemaining(element));
//            subscriberProfileData.setContentFilters(Objects.isNull(element.get("contentFilters")) ? new ArrayList<>() : ((ArrayList<Integer>) element.get("contentFilters")));
//
//            ArrayList<ProfileDevicesDTO> deviceList = new ArrayList<>();
//            if (Objects.nonNull(element.get("devices"))) {
//                ((List<HashMap<String, Object>>) element.get("devices")).forEach(device -> {
//                    ProfileDevicesDTO profileDevicesDTO = new ProfileDevicesDTO();
//                    profileDevicesDTO.setName(String.valueOf(device.get("name")));
//                    profileDevicesDTO.setMac(String.valueOf(device.get("mac")));
//                    deviceList.add(profileDevicesDTO);
//                });
//            }
//
//            subscriberProfileData.setDevices(deviceList);
//
//            ArrayList<ScheduleDetails> scheduleDetailList = new ArrayList<>();
//            if (Objects.nonNull(element.get("scheduleDetails"))) {
//                int i = 1;
//                for (HashMap<String, Object> schedule : (List<HashMap<String, Object>>) element.get("scheduleDetails")) {
//                    if (Objects.nonNull(schedule)) {
//                        ScheduleDetails scheduleDetails = new ScheduleDetails();
//                        scheduleDetails.setDay(Integer.parseInt(String.valueOf(schedule.get("dayOfTheWeek"))));
//                        scheduleDetails.setInternetOffTime(Long.parseLong(String.valueOf(schedule.get("internetOffTime"))));
//                        scheduleDetails.setInternetOnTime(Long.parseLong(String.valueOf(schedule.get("internetOnTime"))));
//                        scheduleDetailList.add(scheduleDetails);
//                    } else {
//                        ScheduleDetails scheduleDetails = new ScheduleDetails();
//                        scheduleDetails.setDay(i);
//                        scheduleDetails.setInternetOffTime(CalendarUtils.getCalendarInstanceForYesterday().getTimeInMillis());
//                        scheduleDetails.setInternetOnTime(CalendarUtils.getCalendarInstanceForYesterday().getTimeInMillis());
//                        scheduleDetailList.add(scheduleDetails);
//                    }
//                    i++;
//                }
//            }
//
//
//            subscriberProfileData.setScheduleDetails(scheduleDetailList);
//
//            subscriberProfileDataList.add(subscriberProfileData);
//        });
//
//        subscriberProfileListDTO.setData(subscriberProfileDataList);
//
//        return subscriberProfileListDTO;
//    }
//
//    private String getNextActionTimeRemaining(DBObject dbObject) {
//        String message = null;
//        if (dbObject.get("timedAccess") != null) {
//            String action = ((DBObject) dbObject.get("timedAccess")).get("nextAction").toString().equalsIgnoreCase(PAUSE) ? "Locking in " : "Unlocking in ";
//            long timeDiff = ((Long.parseLong(((DBObject) dbObject.get("timedAccess")).get("nextActionOn").toString()) - Calendar.getInstance().getTimeInMillis()) / 1000) / 60;
//            message = action + (timeDiff == 0 ? "less than a minute..." : (timeDiff == 1 ? " a minute... " : timeDiff + " minutes..."));
//        } else {
//            if (dbObject.get("currentState").toString().equals(PAUSE)) {
//                message = "Locked indefinitely/as per Schedule";
//            }
//        }
//        return message;
//    }
//
//    public void deleteProfileById(String id) throws Exception {
//        DBObject profile = new BasicDBObject();
//        SubscriberProfileDTO.SubscriberProfileData subscriberProfileData = fetchUserProfileById(id).getData();
//        List<String> devices = new ArrayList<>();
//        subscriberProfileData.getDevices().stream().filter(p -> (p.isAttachToProfile())).forEach(device -> {
//            devices.add(device.getMac());
//        });
//        profile.put("subscriberId", subscriberProfileData.getSubscriberId());
//        profile.put("devices", devices);
//        if (!subscriberProfileData.isCurrentState()) {
//            performProfilePauseUnPause(profile, UNPAUSE);
//        }
//        HashMap<String, String> queryParams = new HashMap<>();
//        queryParams.put("id", id);
//        mongoService.deleteOne(queryParams, USER_PROFILE);
//    }
//
//    public void pauseUnpauseForXMins(long duration, String action, String id, boolean isCallFromAssistantService) throws Exception {
//        if (duration < 0) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Duration cannot be Negative");
//        }
//
//        if (!action.equalsIgnoreCase(PAUSE) && !action.equalsIgnoreCase(UNPAUSE)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Action: Allowed values - pause/unpause");
//        }
//
//        BasicDBObject fieldsToRemove = new BasicDBObject();
//        fieldsToRemove.put("_id", 0);
//
//        HashMap<String, Object> queryParam = new HashMap<>();
//        queryParam.put("id", id);
//
//        DBObject profile = mongoService.findOne(queryParam, new HashMap<>(), USER_PROFILE, fieldsToRemove);
//        if (Objects.isNull(profile)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Profile Id does not exist :: " + id);
//        }
//        if (!isCallFromAssistantService) {
//            Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(profile.get("subscriberId").toString());
//            manageCommonService.subscriberDataExistInMongo(userEquipment);
//            manageCommonService.subscriberGroupMisMatch(userEquipment);
//        }
//
//        if (((List<String>) profile.get("devices")).isEmpty()) {
//            return;
//        }
//
//        BasicDBObject insertField = new BasicDBObject();
//        BasicDBObject timedAccess = new BasicDBObject();
//        timedAccess.put("type", action.equalsIgnoreCase(PAUSE) ? "deny" : "allow");
//        timedAccess.put("duration", duration);
//
//        if (profile.get("currentState").toString().equals(PAUSE) && action.equalsIgnoreCase(PAUSE) || profile.get("currentState").toString().equals(UNPAUSE) && action.equalsIgnoreCase(UNPAUSE)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Profile is already in " + action + " state :: " + id);
//        } else if (profile.get("currentState").toString().equals(PAUSE) && action.equalsIgnoreCase(UNPAUSE)) {
//            timedAccess.put("nextAction", PAUSE);
//            timedAccess.put("nextActionOn", System.currentTimeMillis() + duration * 60 * 1000);
//            insertField.put("currentState", action);
//        } else if (profile.get("currentState").toString().equals(UNPAUSE) && action.equalsIgnoreCase(PAUSE)) {
//            timedAccess.put("nextAction", UNPAUSE);
//            timedAccess.put("nextActionOn", System.currentTimeMillis() + duration * 60 * 1000);
//            insertField.put("currentState", action);
//        }
//
//        performProfilePauseUnPause(profile, action);
//
//        insertField.put("timedAccess", timedAccess);
//        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
//        basicDBObjectUpdate.put("$set", insertField);
//        HashMap<String, Object> params = new HashMap<>();
//        params.put("id", id);
//        mongoService.findAndModify(params, USER_PROFILE, TIMESTAMP, basicDBObjectUpdate);
//    }
//
//    public boolean pauseUnpauseNow(String action, String id) throws Exception {
//        if (!action.equalsIgnoreCase(PAUSE) && !action.equalsIgnoreCase(UNPAUSE)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Action: Allowed values - pause/unpause");
//        }
//        BasicDBObject fieldsToRemove = new BasicDBObject();
//        fieldsToRemove.put("_id", 0);
//
//        HashMap<String, Object> queryParam = new HashMap<>();
//        queryParam.put("id", id);
//        DBObject profile = mongoService.findOne(queryParam, new HashMap<>(), USER_PROFILE, fieldsToRemove);
//        if (Objects.isNull(profile)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Profile Id does not exist :: " + id);
//        }
//
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(profile.get("subscriberId").toString());
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        if (performProfilePauseUnPause(profile, action)) {
//            BasicDBObject insertField = new BasicDBObject();
//            insertField.put("currentState", action);
//            insertField.put("onDemandState", action);
//            insertField.put("timedAccess", null);
//            BasicDBObject basicDBObjectUpdate = new BasicDBObject();
//            basicDBObjectUpdate.put("$set", insertField);
//            HashMap<String, Object> params = new HashMap<>();
//            params.put("id", id);
//            mongoService.findAndModify(params, USER_PROFILE, TIMESTAMP, basicDBObjectUpdate);
//            return action.equalsIgnoreCase(PAUSE) ? false : true;
//        } else {
//            return action.equalsIgnoreCase(PAUSE) ? true : false;
//        }
//    }
//
//    public boolean performProfilePauseUnPause(DBObject profile, String action) throws Exception {
//        boolean playPausePerformed = false;
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(profile.get("subscriberId").toString());
//        BasicDBObject fieldsToRemove = new BasicDBObject();
//        fieldsToRemove.put("_id", 0);
//        List<String> deviceList = (List<String>) profile.get("devices");
//        if (Objects.nonNull(deviceList) && !deviceList.isEmpty()) {
//            deviceList.stream().forEach(deviceMac -> {
//                HashMap<String, Object> where = new HashMap<>();
//                where.put("macAddress", deviceMac);
//                where.put("userId", userEquipment.getRgwSerial());
//                DBObject station = mongoService.findOne(where, new HashMap<>(), STATION_DETAIL, fieldsToRemove);
//                if (Objects.nonNull(station)) {
//                    if (action.equalsIgnoreCase(PAUSE)) {
//                        try {
//                            if (checkDeviceInternetAccessStateAndThenPerform(deviceMac, action, userEquipment.getRgwSerial())) {
//                                manageDeviceService.invokeInternetRPCMethodsForDevice(profile.get("subscriberId").toString(), null, "off", deviceMac, null, Objects.isNull(profile.get("trafficType")) ? null : profile.get("trafficType").toString());
//                            }
//                        } catch (Exception e) {
//                            LOG.error("Error during invoke internet RPC methods for device", e);
//                        }
//                    } else if (action.equalsIgnoreCase(UNPAUSE)) {
//                        try {
//                            if (checkDeviceInternetAccessStateAndThenPerform(deviceMac, action, userEquipment.getRgwSerial())) {
//                                manageDeviceService.invokeInternetRPCMethodsForDevice(profile.get("subscriberId").toString(), null, "on", deviceMac, null, Objects.isNull(profile.get("trafficType")) ? null : profile.get("trafficType").toString());
//                            }
//                        } catch (Exception e) {
//                            LOG.error("Error during invoke internet RPC methods for device", e);
//                        }
//                    }
//                }
//            });
//            playPausePerformed = true;
//        }
//        return playPausePerformed;
//    }
//
//    private boolean checkDeviceInternetAccessStateAndThenPerform(String deviceMac, String action, String userId) {
//        boolean currentState = manageCommonService.isInternetEnabledForDeviceMac(deviceMac, userId);
//        if (currentState && action.equals(UNPAUSE)) {
//            return false;
//        } else if (!currentState && action.equals(PAUSE)) {
//            return false;
//        }
//        return true;
//    }
//
//    private void isProfileNameAlreadyExistForUser(String profileName, Equipment userEquipment) throws Exception {
//        BasicDBObject projection = new BasicDBObject();
//        projection.put("_class", 0);
//        projection.put("_id", 0);
//        HashMap<String, String> queryParams = new HashMap<>();
//        HashMap<String, String> appendableParams = new HashMap<>();
//
//        queryParams.put("profileName", profileName);
//        queryParams.put("subscriberId", userEquipment.getRgwSerial());
//        DBObject profileObj = mongoService.findOne(queryParams, appendableParams, USER_PROFILE, TIMESTAMP, DESC, projection);
//        if (Objects.nonNull(profileObj))
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Profile with this name " + profileName + " already exist");
//    }
//
//    public void editProfileForUser(String id, EditUserProfileRequest editUserProfileRequest) throws Exception {
//        BasicDBObject fieldsToRemove = new BasicDBObject();
//        fieldsToRemove.put("_id", 0);
//        HashMap<String, Object> queryParam = new HashMap<>();
//        queryParam.put("id", id);
//        DBObject profile = mongoService.findOne(queryParam, new HashMap<>(), USER_PROFILE, fieldsToRemove);
//        if (Objects.isNull(profile)) {
//            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Profile Id does not exist :: " + id);
//        }
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(profile.get("subscriberId").toString());
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        if (Objects.nonNull(editUserProfileRequest.getProfileName()) && !profile.get("profileName").equals(editUserProfileRequest.getProfileName()))
//            isProfileNameAlreadyExistForUser(editUserProfileRequest.getProfileName(), userEquipment);
//
//        DBObject query = new BasicDBObject().append("id", id);
//
//        if (Objects.nonNull(editUserProfileRequest.getContentFilters())) {
//            if (editUserProfileRequest.getContentFilters().isEmpty()) {
//                ArrayList<Integer> contentFiltersFromDB = (ArrayList<Integer>) profile.get("contentFilters");
//                if (!contentFiltersFromDB.isEmpty()) {
//                    DBObject update = new BasicDBObject().append("$unset", new BasicDBObject().append("trafficType", ""));
//                    update.put("$set", new BasicDBObject().append("contentFilters", new ArrayList<>()));
//                    mongoService.update(query, update, true, false, USER_PROFILE);
//                }
//            } else {
//                long total = 0;
//                for (int i = 0; i < editUserProfileRequest.getContentFilters().size(); i++) {
//                    if (!contentFilterList.contains(editUserProfileRequest.getContentFilters().get(i))) {
//                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Selected content filter is not found");
//                    }
//                    long result = (1L << editUserProfileRequest.getContentFilters().get(i));
//                    total += result;
//                }
//                DBObject update = new BasicDBObject().append("$set", new BasicDBObject().append("trafficType", total).append("contentFilters", editUserProfileRequest.getContentFilters()));
//                mongoService.update(query, update, true, false, USER_PROFILE);
//            }
//        }
//
//        if (Objects.nonNull(editUserProfileRequest.getDevices())) {
//            editUserProfileRequest.getDevices().forEach(device -> {
//                manageCommonService.checkMacAddressOfStation(device, userEquipment);
//                manageCommonService.checkDeviceMACBelongsToSubscriber(device, userEquipment);
//            });
//            BasicDBObject projection = new BasicDBObject();
//            projection.put("_class", 0);
//            projection.put("_id", 0);
//            HashMap<String, Object> queryParams = new HashMap<>();
//            HashMap<String, String> notInMap = new HashMap<>();
//            notInMap.put("$ne", id);
//            queryParams.put("subscriberId", userEquipment.getRgwSerial());
//            queryParams.put("id", notInMap);
//            List<DBObject> profilesForSubscriber = mongoService.findList(queryParams, USER_PROFILE, projection);
//            final List<String> devicesFromOtherProfiles = new ArrayList<>();
//            profilesForSubscriber.forEach(profileItem -> {
//                ((List<String>) profileItem.get("devices")).forEach(device -> {
//                    devicesFromOtherProfiles.add(device.toString());
//                });
//            });
//            editUserProfileRequest.getDevices().forEach(deviceToBeAdded -> {
//                if (devicesFromOtherProfiles.contains(deviceToBeAdded)) {
//                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device " + deviceToBeAdded + " is already mapped with some other Profile so cannot be added to this profile");
//                }
//            });
//            DBObject update = new BasicDBObject().append("$set", new BasicDBObject().append("devices", editUserProfileRequest.getDevices()));
//            // Check All the newly Added or delete devices and then take appropriate action to either pause or unpause devices.
//            List<String> preExistingList = ((List<String>) profile.get("devices"));
//            List<String> editedList = editUserProfileRequest.getDevices();
//
//            List<String> addedMacs = new ArrayList<>();
//            List<String> removedMacs = new ArrayList<>();
//
//            editedList.forEach(i -> {
//                if (!preExistingList.contains(i)) {
//                    addedMacs.add(i);
//                }
//            });
//
//            preExistingList.forEach(i -> {
//                if (!editedList.contains(i)) {
//                    removedMacs.add(i);
//                }
//            });
//            String currentStateOfProfile = profile.get("currentState").toString();
//            // The moment the Device is removed and if the State of the Profile is Paused then unpause the device
//            removedMacs.forEach(removed -> {
//                try {
//                    if (currentStateOfProfile.equals(PAUSE)) {
//                        if (!checkDeviceInternetAccessStateAndThenPerform(removed, PAUSE, userEquipment.getRgwSerial())) {
//                            manageDeviceService.invokeInternetRPCMethodsForDevice(profile.get("subscriberId").toString(), null, "on", removed, null, Objects.isNull(profile.get("trafficType")) ? null : profile.get("trafficType").toString());
//                        }
//                    }
//                } catch (Exception e) {
//                    LOG.error("Error during invoke internet RPC methods", e);
//                }
//            });
//            // The moment the Device is added and if the State of the Profile is Paused then pause the device
//            addedMacs.forEach(added -> {
//                try {
//                    if (currentStateOfProfile.equals(PAUSE)) {
//                        if (checkDeviceInternetAccessStateAndThenPerform(added, PAUSE, userEquipment.getRgwSerial())) {
//                            manageDeviceService.invokeInternetRPCMethodsForDevice(profile.get("subscriberId").toString(), null, "off", added, null, Objects.isNull(profile.get("trafficType")) ? null : profile.get("trafficType").toString());
//                        }
//                    }
//                } catch (Exception e) {
//                    LOG.error("Error during invoke internet RPC methods for device", e);
//                }
//            });
//            mongoService.update(query, update, true, false, USER_PROFILE);
//        }
//        if (Objects.nonNull(editUserProfileRequest.isAutoSchedule())) {
//            DBObject update = new BasicDBObject().append("$set", new BasicDBObject().append("scheduleActivated", editUserProfileRequest.isAutoSchedule()));
//            mongoService.update(query, update, true, false, USER_PROFILE);
//        }
//
//        if (Objects.nonNull(editUserProfileRequest.getScheduleDetails()) && !editUserProfileRequest.getScheduleDetails().isEmpty()) {
//            ArrayList<HashMap<String, Object>> scheduleList = new ArrayList<>();
//            if (editUserProfileRequest.getScheduleDetails().size() < 7) {
//                List<Integer> userDefinedWeekList = new ArrayList<>();
//                List<Integer> weekList = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
//                editUserProfileRequest.getScheduleDetails().forEach(scheduleDetails -> {
//                    userDefinedWeekList.add(scheduleDetails.getDay());
//                    HashMap<String, Object> scheduleData = new BasicDBObject();
//                    scheduleData.put("dayOfTheWeek", scheduleDetails.getDay());
//                    scheduleData.put("internetOnTime", CalendarUtils.getCalendarInstanceForTodayMidNight().getTimeInMillis());
//                    scheduleData.put("internetOffTime", CalendarUtils.getCalendarInstanceForYesterday().getTimeInMillis());
//
//                    scheduleList.add(scheduleData);
//                });
//
//                weekList = weekList.stream().filter(element -> !userDefinedWeekList.contains(element)).collect(Collectors.toList());
//                weekList.forEach(day -> {
//                    HashMap<String, Object> scheduleData = new BasicDBObject();
//                    scheduleData.put("dayOfTheWeek", day);
//                    scheduleData.put("internetOnTime", CalendarUtils.getCalendarInstanceForYesterday().getTimeInMillis());
//                    scheduleData.put("internetOffTime", CalendarUtils.getCalendarInstanceForYesterday().getTimeInMillis());
//                    scheduleList.add(scheduleData);
//                });
//
//                scheduleList.sort((q1, q2) -> Long.compare((Integer) q1.get("dayOfTheWeek"), (Integer) q2.get("dayOfTheWeek")));
//
//            } else {
//                editUserProfileRequest.getScheduleDetails().forEach(scheduleItem -> {
//                    if (Objects.nonNull(scheduleItem)) {
//                        if (scheduleItem.getDay() > 0 && scheduleItem.getDay() < 8) {
//                            HashMap<String, Object> scheduleData = new HashMap<>();
//                            int dayOfTheWeek = scheduleItem.getDay();
//                            scheduleData.put("dayOfTheWeek", dayOfTheWeek);
//                            scheduleData.put("internetOnTime", scheduleItem.getInternetOnTime());
//                            scheduleData.put("internetOffTime", scheduleItem.getInternetOnTime());
//                            scheduleList.add(scheduleData);
//                        } else {
//                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid day : " + scheduleItem.getDay() + " - Can only take values from 1-7 (1 being Sunday)");
//                        }
//                    }
//                });
//            }
//
//            DBObject update = new BasicDBObject().append("$set", new BasicDBObject().append("scheduleDetails", scheduleList).append("updatedAt", System.currentTimeMillis() - 24 * 60 * 60 * 1000));
//            mongoService.update(query, update, true, false, USER_PROFILE);
//        }
//        if (Objects.nonNull(editUserProfileRequest.getProfileName())) {
//            if (editUserProfileRequest.getProfileName().length() >= 1 && editUserProfileRequest.getProfileName().length() <= 255) {
//                DBObject update = new BasicDBObject().append("$set", new BasicDBObject().append("profileName", editUserProfileRequest.getProfileName()));
//                mongoService.update(query, update, true, false, USER_PROFILE);
//            } else {
//                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Profile name cannot be blank, must be between 1 and 255 characters");
//            }
//        }
//    }
//
//    private Set<String> getSubscriberAllDevices(String subscriberId) throws Exception {
//        Set<String> deviceList;
//        DeviceListDTO deviceListDTO = manageDeviceService.getDeviceList(subscriberId);
//        ArrayList<DeviceListDTO.DeviceDetailListData> allDeviceList = deviceListDTO.getData();
//        deviceList = allDeviceList.stream().map(device -> device.getMac()).collect(Collectors.toSet());
//
//        return deviceList;
//    }
//
//    private HashSet<String> getAllDevicesAttachToProfile(String subscriberId) throws Exception {
//        BasicDBObject projection = new BasicDBObject();
//        projection.put("_class", 0);
//        projection.put("_id", 0);
//        HashMap<String, String> queryParams = new HashMap<>();
//        queryParams.put("subscriberId", subscriberId);
//        List<DBObject> profilesForSubscriber = mongoService.findList(queryParams, USER_PROFILE, projection);
//        final HashSet<String> devicesFromOtherProfiles = new HashSet<>();
//        profilesForSubscriber.forEach(profileItem -> {
//            ((List<String>) profileItem.get("devices")).forEach(device -> {
//                devicesFromOtherProfiles.add(device.toString());
//            });
//        });
//
//        return devicesFromOtherProfiles;
//    }
//
//    public ProfileListDTO getTopFiveProfiles(String serialNumberOrRGWMAC, long duration) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(serialNumberOrRGWMAC);
//        manageCommonService.subscriberDataExistInMongo(userEquipment);
//        manageCommonService.subscriberGroupMisMatch(userEquipment);
//
//        ProfileListDTO profileListDTO = new ProfileListDTO();
//        ArrayList<ProfileListDTO.ProfileListData> profileListDataList = new ArrayList<>();
//        List<HashMap<String, Object>> top5Profiles;
//        Map<String, HashMap<String, Object>> allDevicesSumMap;
//        allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), duration, BANDWIDTH);
//        String platformUserId = userEquipment.getRgwSerial();
//        HashMap<String, Object> params = new HashMap<>();
//        params.clear();
//        params.put("userId", platformUserId);
//        SubscriberProfileListDTO userProfiles = manageProfileServiceV3.fetchAllUserProfiles(serialNumberOrRGWMAC);
//        List<HashMap<String, Object>> profileBandwidthList = new ArrayList<>();
//        for (SubscriberProfileDTO.SubscriberProfileData userProfile : userProfiles.getData()) {
//            params.clear();
//            params.put("profileId", userProfile.getId());
//            HashMap<String, Object> profileBandwidth = new HashMap<>();
//            Double totalBandwidthProfile = 0.0;
//            for (ProfileDevicesDTO station : userProfile.getDevices()) {
//                HashMap<String, Object> sumDataMap = allDevicesSumMap.get(station.getMac());
//                totalBandwidthProfile += Double.valueOf(Objects.isNull(sumDataMap) || Objects.isNull(sumDataMap.get("bandwidth")) ? "0.0" : String.valueOf(sumDataMap.get("bandwidth")));
//            }
//            profileBandwidth.put("profileName", userProfile.getProfileName());
//            profileBandwidth.put("profileId", userProfile.getId());
//            profileBandwidth.put("totalBandwidth", totalBandwidthProfile);
//            profileBandwidthList.add(profileBandwidth);
//        }
//        top5Profiles = profileBandwidthList.parallelStream().sorted(Comparator.comparing((Map<String, Object> s) -> Double.valueOf(s.get("totalBandwidth").toString())).reversed()).limit(5).collect(Collectors.toList());
//        for (Map<String, Object> item : top5Profiles) {
//            ProfileListDTO.ProfileListData profileListData = profileListDTO.new ProfileListData();
//            profileListData.setProfileId(String.valueOf(item.get("profileId")));
//            profileListData.setProfileName(String.valueOf(item.get("profileName")));
//            profileListData.setTotalBandwidth(Double.valueOf(FOUR_DECIMAL_PLACE.format(Double.valueOf(item.get("totalBandwidth").toString()) / (1000F * 1000F))));
//            profileListDataList.add(profileListData);
//        }
//
//        if (profileListDataList.isEmpty()) {
//            ProfileListDTO.ProfileListData profileListData = profileListDTO.new ProfileListData();
//            profileListDataList.add(profileListData);
//        }
//        profileListDTO.setData(profileListDataList);
//        return profileListDTO;
//    }
//
//    private Map<String, HashMap<String, Object>> processDevices(String userId, long duration, String field) {
//        HashMap<String, Object> params = new HashMap<>();
//        BasicDBObject mongoFieldOptions = new BasicDBObject();
//        HashMap<String, HashMap<String, Object>> allDeviceSum = new HashMap<>();
//        mongoFieldOptions.clear();
//        mongoFieldOptions.put("_id", 0);
//
//        TimeZone timeZone = TimeZone.getTimeZone("UTC");
//        Calendar now = Calendar.getInstance(timeZone);
//        now.set(Calendar.MINUTE, 0);
//        now.set(Calendar.SECOND, 0);
//        now.set(Calendar.MILLISECOND, 0);
//
//        long nowMillSec = now.getTimeInMillis();
//
//        long minutes = duration;
//
//        long dateHour = nowMillSec / 1000 - minutes * 60;
//
//        BasicDBObject dateCriteria = new BasicDBObject();
//        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));
//
//        params.put("userId", userId);
//        params.put("dateHour", dateCriteria);
//
//        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(params, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);
//
//        if (Objects.nonNull(deviceDownLinkUpLinkBytes) && !deviceDownLinkUpLinkBytes.isEmpty()) {
//            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
//                String today = String.valueOf(dbObject.get("date"));
//                HashMap<String, Object> devMap = (HashMap<String, Object>) dbObject.get("devMap");
//                if (Objects.nonNull(devMap) && !devMap.isEmpty()) {
//                    for (Map.Entry<String, Object> entry : devMap.entrySet()) {
//                        HashMap<String, Object> values = allDeviceSum.get(entry.getKey());
//                        if (Objects.nonNull(values)) {
//                            Double sum = Objects.nonNull(values.get(field)) ? Double.valueOf(values.get(field).toString()) : 0;
//                            List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
//                            if (Objects.nonNull(dataList)) {
//                                Calendar criteriaCalendar = Calendar.getInstance();
//                                criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
//                                long currTimeStamp = criteriaCalendar.getTimeInMillis();
//                                dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
//                                try {
//                                    sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0")).sum();
//                                } catch (Exception e) {
//                                }
//
//                                HashMap<String, Object> data = new HashMap<>();
//                                data.put("macAddress", entry.getKey());
//                                data.put(field, sum);
//
//                                allDeviceSum.put(entry.getKey(), data);
//                            }
//                        } else {
//                            Double sum = 0.0;
//                            List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
//                            if (Objects.nonNull(dataList)) {
//                                Calendar criteriaCalendar = Calendar.getInstance();
//                                criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
//                                long currTimeStamp = criteriaCalendar.getTimeInMillis();
//                                dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
//                                try {
//                                    sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0")).sum();
//                                } catch (Exception e) {
//                                }
//                                HashMap<String, Object> data = new HashMap<>();
//                                data.put("macAddress", entry.getKey());
//                                data.put(field, sum);
//
//                                allDeviceSum.put(entry.getKey(), data);
//                            }
//                        }
//                    }
//                }
//
//                if (Objects.nonNull(dbObject.get(today))) {
//                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
//                    for (String key : dataByDay.keySet()) {
//                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
//                        HashMap<String, Object> latestDevMap = (HashMap<String, Object>) userSlidingData.get("devMap");
//                        if (Objects.nonNull(latestDevMap) && !latestDevMap.isEmpty()) {
//                            for (Map.Entry<String, Object> entry : latestDevMap.entrySet()) {
//                                HashMap<String, Object> values = allDeviceSum.get(entry.getKey());
//                                if (Objects.nonNull(values)) {
//                                    Double sum = Objects.nonNull(values.get(field)) ? Double.valueOf(values.get(field).toString()) : 0;
//                                    List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
//                                    if (Objects.nonNull(dataList)) {
//                                        Calendar criteriaCalendar = Calendar.getInstance();
//                                        criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
//                                        long currTimeStamp = criteriaCalendar.getTimeInMillis();
//                                        dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
//                                        try {
//                                            sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0")).sum();
//                                        } catch (Exception e) {
//                                        }
//
//                                        HashMap<String, Object> data = new HashMap<>();
//                                        data.put("macAddress", entry.getKey());
//                                        data.put(field, sum);
//
//                                        allDeviceSum.put(entry.getKey(), data);
//                                    }
//                                } else {
//                                    Double sum = 0.0;
//                                    List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
//                                    if (Objects.nonNull(dataList)) {
//                                        Calendar criteriaCalendar = Calendar.getInstance();
//                                        criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
//                                        long currTimeStamp = criteriaCalendar.getTimeInMillis();
//                                        dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
//                                        try {
//                                            sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0")).sum();
//                                        } catch (Exception e) {
//                                        }
//                                        HashMap<String, Object> data = new HashMap<>();
//                                        data.put("macAddress", entry.getKey());
//                                        data.put(field, sum);
//
//                                        allDeviceSum.put(entry.getKey(), data);
//                                    }
//                                }
//                            }
//                        }
//
//
//                    }
//                }
//            }
//        }
//
//        return allDeviceSum;
//    }
//}
