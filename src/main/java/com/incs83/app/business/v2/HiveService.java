//package com.incs83.app.business.v2;
//
//import com.incs83.abstraction.ApiResponseCode;
//import com.incs83.app.config.HiveConfig;
//import com.incs83.app.enums.ApiResponseCodeImpl;
//import com.incs83.exceptions.ApiException;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.sql.Connection;
//import java.sql.ResultSet;
//import java.sql.Statement;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Objects;
//
//import static com.incs83.app.constants.misc.ActiontecConstants.INVALID_JSON_TABLE_HIVE;
//
//@Service
//public class HiveService {
//
//    private static final Logger LOG = LogManager.getLogger("org");
//
//    @Autowired
//    private HiveConfig hiveConfig;
//
//    public List<HashMap<String, Object>> readInvalidJson(String date) {
//        List<HashMap<String, Object>> results = new ArrayList();
//        Connection hiveConnection;
//        try {
//            String qry = "SELECT case when userid = '' then 'UNDEFINED' else userid end as userid, count(remark) , version FROM " + INVALID_JSON_TABLE_HIVE +
//                    " where hive_date = '" + date + "' GROUP BY userid, version";
//            hiveConnection = hiveConfig.getConnection();
//            if (Objects.isNull(hiveConnection)) {
//                LOG.error("Unable to establish connection with hive.");
//                throw new Exception();
//            }
//            Statement statement = hiveConnection.createStatement();
//            ResultSet res = statement.executeQuery(qry);
//            while (res.next()) {
//                HashMap<String, Object> data = new HashMap();
//                data.put("userId", res.getString(1));
//                data.put("count", res.getString(2));
//                data.put("version", res.getString(3));
//                results.add(data);
//            }
//        } catch (Exception e) {
//            LOG.error(e.getMessage());
//        }
//        return results;
//    }
//
//    public List<HashMap<String, Object>> readInvalidJsonById(String userId, Integer count, String date) {
//        List<HashMap<String, Object>> results = new ArrayList();
//        Connection hiveConnection;
//        try {
//            String qry = "SELECT * FROM " + INVALID_JSON_TABLE_HIVE +
//                    " where hive_date = '" + date +
//                    "' AND userid ='" + userId + "' sort by timestamp DESC LIMIT " + count;
//            hiveConnection = hiveConfig.getConnection();
//            if (Objects.isNull(hiveConnection)) {
//                LOG.error("Unable to establish connection with hive.");
//                throw new Exception();
//            }
//            Statement statement = hiveConnection.createStatement();
//            ResultSet res = statement.executeQuery(qry);
//            while (res.next()) {
//                HashMap<String, Object> data = new HashMap();
//                data.put("timestamp", res.getString(2));
//                data.put("userId", res.getString(1));
//                data.put("serialNo", res.getString(3));
//                data.put("remarks", res.getString(4));
//                data.put("version", res.getString(5));
//                data.put("value", res.getString(6));
//                results.add(data);
//            }
//        } catch (Exception e) {
//            LOG.error(e.getMessage());
//            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
//        }
//        return results;
//    }
//}
