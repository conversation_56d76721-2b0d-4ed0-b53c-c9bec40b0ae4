package com.incs83.app.business.v2;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.Transactional;
import com.incs83.app.common.v2.LoginRequest;
import com.incs83.app.common.v2.OauthProviderConfig;
import com.incs83.app.common.v2.OauthRoleMappingConfig;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.constants.queries.*;
import com.incs83.app.entities.*;
import com.incs83.app.responsedto.v2.Equipment.EditSubscriberRequest;
import com.incs83.app.responsedto.v2.oauth.OauthConfigResponse;
import com.incs83.app.service.components.HttpServiceImpl;
import com.incs83.app.utils.AuditUtils;
import com.incs83.app.utils.CacheUtils;
import com.incs83.business.ESService;
import com.incs83.config.JwtConfig;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.enums.Gender;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.*;
import com.incs83.mt.CurrentTenantIdentifier;
import com.incs83.mt.DataAccessService;
import com.incs83.queries.LoginDetailsSQL;
import com.incs83.security.tokenFactory.JwtToken;
import com.incs83.security.tokenFactory.JwtTokenFactory;
import com.incs83.security.tokenFactory.UserContext;
import com.incs83.service.CommonService;
import com.incs83.service.ESServiceImpl;
import com.incs83.service.S3Service;
import com.incs83.services.HazelcastService;
import com.incs83.util.ClientInfoUtils;
import com.incs83.util.CommonUtils;
import com.incs83.util.DateUtils;
import org.apache.commons.collections4.map.PassiveExpiringMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_BY_STN;
import static com.incs83.app.constants.queries.UserSQL.GET_USER_BY_EMAIL;
import static com.incs83.constants.ApplicationCommonConstants.EXTERNAL_USER_PASSWORD;
import static com.incs83.constants.ApplicationCommonConstants.SPACE;
import static com.incs83.constants.ApplicationCommonConstants.TOKEN;
import static com.incs83.constants.ApplicationCommonConstants.*;

/**
 * Created by Jayant on 15/2/19.
 */
@Service
@SuppressWarnings({"rawtypes", "unchecked"})
public class Oauth2Service {

    private static final Logger LOG = LogManager.getLogger(com.incs83.app.business.v2.Oauth2Service.class);
    @Value("${internal.secretKey}")
    private String internalSecretKey;
    @Autowired
    private BCryptPasswordEncoder encoder;
    @Autowired
    private HttpServiceImpl httpService;
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private ESService esService;
    @Autowired
    private NewManageSubscriberService manageSubscriberService;
    @Autowired
    private ESServiceImpl esServiceImpl;
    @Autowired
    private JwtTokenFactory tokenFactory;
    @Autowired
    private HazelcastService hazelcastService;
    @Autowired
    private S3Service s3Service;
    @Autowired
    private CommonService commonService;
    @Autowired
    private JwtConfig jwtConfig;
    @Autowired
    private AuditUtils auditUtils;

    @Value("${s3.bucket}")
    private String s3Bucket;
    @Value("${s3.domain}")
    private String s3Domain;
    @Autowired
    private ObjectMapper mapper;
    @Autowired
    private ClientInfoUtils clientInfoUtils;
    @Autowired
    private OauthConfigDao oauthConfigDao;

    @Value("${wellKnownConfig.cacheTtl:300000}")
    private long wellKnownConfigCacheTtl = 300000; // in millis

    private Map<String, String> wellKnownConfigCache;

    @PostConstruct
    private void init() {
        wellKnownConfigCache = Collections.synchronizedMap(new PassiveExpiringMap<>(wellKnownConfigCacheTtl));
    }

    private void checkApiKey(boolean isSecure, String apiKey) {
        if (!isSecure || !StringUtils.equals(internalSecretKey, apiKey)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Secret Key is invalid / Request not authenticated");
        }
    }

    private void checkClientIdentifier(String state) {
        if (!StringUtils.equals(CLIENT_IDENTIFIER_SECRET_KEY, state)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Secret Key is invalid / Request not authenticated");
        }
    }

    public Map getOptimTokenFromOauthCode(String apiKey, boolean isSecure, String code, String state, String redirect_uri, String oauthConfigName, HttpServletRequest request) throws Exception {
        checkApiKey(isSecure, apiKey);
        checkClientIdentifier(state);
        if (StringUtils.isEmpty(code)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Oauth Code is Blank / null / Stale");
        }

        OauthConfig oauthConfig = findOauthConfigByNameOrDefault(oauthConfigName);
        if (StringUtils.isNotEmpty(redirect_uri)) {
            oauthConfig.setRedirectUri(redirect_uri);
        }

        Role optimRole = null;
        Map token = null;

        HashMap<String, Object> accessTokenResponse = getAccessTokenResponseFromOAuthProvider(oauthConfig, code);
        String accessToken = accessTokenResponse.get(ACCESS_TOKEN) == null ? null : accessTokenResponse.get(ACCESS_TOKEN).toString();
        //String oauthRole = accessTokenResponse.get("role") == null ? null : accessTokenResponse.get("role").toString();
        if (null == accessToken) {
            throw new OAUTH2InvalidCodeException(HttpStatus.BAD_REQUEST.value(), "Oauth Code is Expired or invalid");
        }

        UserInfo userInfo = fetchUserInfoFromOauthServer(oauthConfig, accessToken);

        HashMap<String, String> query = new HashMap<>();
        query.put("email", userInfo.getEmail());
        List<User> users = (List<User>) dataAccessService.read(User.class, GET_USER_BY_EMAIL, query);
        if(users != null && !users.isEmpty()) {
            auditUtils.createLogAndPublish(request.getMethod(),
                    AuditorConstants.USER_LOGIN,
                    users.get(0).getId(),
                    auditUtils.getRequestIP(request),
                    request.getRequestURI(),
                    null,
                    null,
                    200,
                    null);
        }

        String s3imageUrl = null;
        if (userInfo.getPictureUrl() != null) {
            s3imageUrl = savePictureToS3(userInfo.getPictureUrl(), accessToken, userInfo.getSub());
        }

        checkUserLimit(userInfo.getEmail());

        String userStatus;
        if (CUSTOMER.equals(userInfo.getUserType())) {
            throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Unauthorized, End user Login not Allowed");
        } else if (EMPLOYEE.equals(userInfo.getUserType())) {
            LOG.info("\nEmployee Access Token :::::: " + accessTokenResponse + "\n");

            //XXX: replace by findOauthRoleMappingByOauthRole
            List<OauthRoleMapping> oauthRoleMappingList = (List<OauthRoleMapping>) dataAccessService.read(OauthRoleMapping.class);
            final String userType = userInfo.getUserType();
            OauthRoleMapping oauthRoleMapping = oauthRoleMappingList.stream().filter(ele -> userType.equals(ele.getOauthRole())).findAny().orElse(null);
            if (Objects.isNull(oauthRoleMapping)) {
                throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Oauth Mapping Config does not exist");
            }

            final List<HashMap<String, Object>> optimRoleList = mapper.readValue(oauthRoleMapping.getOptimRole(), List.class);
            if (Objects.isNull(optimRoleList) || optimRoleList.isEmpty()) {
                throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Oauth Mapping Config does not exist");
            }

            List<HashMap<String, Object>> memberOfRoleList = new ArrayList<>();
            userInfo.getMemberOf().forEach(element -> {
                HashMap<String, Object> memberOfRole = optimRoleList.stream().filter(q -> element.trim().equals(String.valueOf(q.get("oauthGroup")))).findAny().orElse(null);
                if (Objects.nonNull(memberOfRole))
                    memberOfRoleList.add(memberOfRole);
            });

            if (memberOfRoleList.isEmpty()) {
                //throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Unauthorized, Invalid memberOf Content in token");
                optimRole = getOptimRoleByOauthConfig(oauthConfig);
            } else {
                if (memberOfRoleList.size() > 1) {
                    memberOfRoleList.sort(Comparator.comparingInt(q -> (Integer) q.get("precedence")));
                }
                HashMap<String, Object> memberOfRole = memberOfRoleList.get(0);
                optimRole = (Role) dataAccessService.read(Role.class, memberOfRole.get("optimRoleId").toString());
            }
        } else {
            optimRole = getOptimRoleByOauthConfig(oauthConfig);
        }

        userStatus = isInternalUserCreatedWithSameEmail(userInfo.getEmail(), optimRole, oauthConfig, s3imageUrl);
        // Create External User If not Exists
        if (userStatus == null) {
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Some Error Occurred");
        }
        if (userStatus.equals("EXTERNAL")) {
            token = createTokenForUser(userInfo.getEmail(), false, EMPLOYEE, null);
        } else {
            token = createExternalUser(userInfo.getFirstName(), userInfo.getLastName(), s3imageUrl, userInfo.getEmail(), optimRole, oauthConfig, EMPLOYEE);
        }

        return token;
    }

    private boolean isRootUser(User user) {
        String userEmail = user.getEmail();
        return userEmail.equals(ROOT_ADMIN_EMAIL);
    }
    public Map getAccessTokenForLogin(LoginRequest request) throws Exception {

        String username = request.getUsername();
        String password = request.getPassword();

        HashMap<String, Object> params = new HashMap<>();
        params.put("email", username);

        Subscriber subscriber = null;
        LoginDetails loginDetails = null;
        Boolean isSubscriber = false;
        Boolean isExternalUser = false;

        List<User> users = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_EMAIL, params);
        if(users == null || users.isEmpty()) {
            throw new BadCredentialsException("Authentication Failed.");
        }
        User user = users.iterator().next();

        String errorMessage = "Authentication Failed.";
        Long attempts = Long.valueOf(user.getPasswordAttempt());
        Long attemptsLimit = Long.valueOf(commonService.read(COMMON_CONFIG).get(LOGIN_ATTEMPTS));
        if (!encoder.matches(password, user.getPassword())) {
            if (attempts < attemptsLimit && attempts >= 0 && !user.getEmail().equals(ROOT_ADMIN_EMAIL)) {
                user.setPasswordAttempt(++attempts + "");
                dataAccessService.update(User.class, user);
            }

            throw new BadCredentialsException(errorMessage);
        } else {
            if (!isRootUser(user)) {
                if (attempts.equals(attemptsLimit)) {
                    errorMessage = "Authentication Failed.";
                    throw new BadCredentialsException(errorMessage);
                }
                if(!user.isVerify()) {
                    user.setVerify(true);
                }
                if (attempts < attemptsLimit && attempts > 0) {
                    user.setPasswordAttempt(ZERO + "");
                    dataAccessService.update(User.class, user);
                }
            }
        }

        List<Subscriber> subscribers = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_BY_EMAIL, params);
        if(subscribers != null && !subscribers.isEmpty()) {
            subscriber = subscribers.iterator().next();
            isSubscriber = true;
        }

        List<LoginDetails> loginDetailsList = (List<LoginDetails>) dataAccessService.read(LoginDetails.class, LoginDetailsSQL.GET_USER_BY_EMAIL, params);
        if(loginDetailsList == null || loginDetailsList.isEmpty()) {
            loginDetails = new LoginDetails();
            loginDetails.setId(subscriber != null ? subscriber.getId() : user.getId());
            loginDetails.setEmail(user.getEmail());
            loginDetails.setSubscriber(isSubscriber);
            loginDetails.setActive(true);
            loginDetails.setToken(null);
            loginDetails.setTokenCreatedAt(new Date());
            loginDetails.setTokenUpdatedAt(new Date());
            loginDetails.setTokenType("INTERNAL");
            CommonUtils.setCreateEntityFields(loginDetails);

            try {
                dataAccessService.create(LoginDetails.class, loginDetails);
            } catch (Exception ex) {
                LOG.error("Create login record fail");
            }
        } else {
            loginDetails = loginDetailsList.iterator().next();
        }

        UserContext userContext;
        if(loginDetails.isSubscriber()) {
            if (Objects.isNull(subscriber.getSubscriberRole()) || Objects.isNull(subscriber.getSubscriberRole().iterator().next()))
                throw new InsufficientAuthenticationException("User has no roles assigned");
            userContext = prepareUserContext(isExternalUser, subscriber, EMPTY_STRING);
        } else {
            try {
                HttpServletRequest httpServletRequest = ((ServletRequestAttributes) (RequestContextHolder.getRequestAttributes())).getRequest();
                user.setIp(String.valueOf(clientInfoUtils.getClientIpAddr(httpServletRequest)));
                dataAccessService.update(User.class, user);
            } catch (Exception ex) {
                LOG.error("Error in fetching userIp ", ex);
            }

            if (Objects.isNull(user.getUserRole()) || Objects.isNull(user.getUserRole().iterator().next()))
                throw new InsufficientAuthenticationException("User has no roles assigned");
            userContext = prepareUserContext(isExternalUser, user, EMPTY_STRING);
        }

        Map token = generateTokenForExternalUser(userContext);
        token.remove("verify");

        return token;
    }

    public Map getAccessTokenForAppLogin(Map request) throws Exception {

//        checkApiKey(isSecure, apiKey);

        String state = String.valueOf(request.get("state"));
        String code = String.valueOf(request.get("code"));
        String configId = String.valueOf(request.get("configId"));

        checkClientIdentifier(state);
        if (StringUtils.isEmpty(code)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Oauth Code is Blank / null / Stale");
        }

        LOG.info("DEBUG:\n" + configId + "\n" + state + "\n" + code);
        OauthConfig oauthConfig = findOauthConfigByIdOrDefault(configId);

        HashMap<String, Object> accessTokenResponse = getAccessTokenResponseFromOAuthProvider(oauthConfig, code);
        String accessToken = accessTokenResponse.get(ACCESS_TOKEN) == null ? null : accessTokenResponse.get(ACCESS_TOKEN).toString();
        if (null == accessToken) {
            throw new OAUTH2InvalidCodeException(HttpStatus.BAD_REQUEST.value(), "Oauth Code is Expired or invalid");
        }

        UserInfo userInfo = fetchUserInfoFromOauthServer(oauthConfig, accessToken);

        String s3ImageUrl = null;
        if (userInfo.getPictureUrl() != null) {
            s3ImageUrl = savePictureToS3(userInfo.getPictureUrl(), accessToken, userInfo.getSub());
        }

        checkUserLimit(userInfo.getEmail());
        Role optimRole = getOptimRoleByOauthConfig(oauthConfig);

        Map token = createAppUser(userInfo, optimRole, oauthConfig, s3ImageUrl, EMPLOYEE);
        token.remove("verify");

        return token;
    }

    public Map getOptimTokenFromOauthAccessToken(String userToken, String oauthConfigName, HttpServletRequest request) throws Exception {
        if(StringUtils.isBlank(userToken)){
            throw new ValidationException(HttpStatus.UNAUTHORIZED.value(), "Unauthorized");
        }

        OauthConfig oauthConfig = findOauthConfigByNameOrDefault(oauthConfigName);

        Role optimRole;
        Map token;

        String accessToken = userToken;

        UserInfo userInfo = fetchUserInfoFromOauthServer(oauthConfig, accessToken);

        HashMap<String, String> query = new HashMap<>();
        query.put("email", userInfo.getEmail());
        List<User> users = (List<User>) dataAccessService.read(User.class, GET_USER_BY_EMAIL, query);
        if(users != null && !users.isEmpty()) {
            auditUtils.createLogAndPublish(request.getMethod(),
                    AuditorConstants.USER_LOGIN,
                    users.get(0).getId(),
                    auditUtils.getRequestIP(request),
                    request.getRequestURI(),
                    null,
                    null,
                    200,
                    null);
        }

        LOG.info("** OAUTH DEBUG LOGS ** USER TYPE received from provider : " + userInfo.getUserType());
        String s3imageUrl = null;
        if (Objects.nonNull(userInfo.getPictureUrl())) {
            s3imageUrl = savePictureToS3(userInfo.getPictureUrl(), accessToken, userInfo.getSub());
        }

        checkUserLimit(userInfo.getEmail());

        if (CUSTOMER.equals(userInfo.getUserType())) {
            String subscriberId = fetchSubscriberInfoByGlobalAccountId(userInfo.getGlobalAccountId());
            if (Objects.isNull(subscriberId)) {
                throw new ValidationException(HttpStatus.UNAUTHORIZED.value(), "Unauthorized, User does not exist.");
            }

            Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, subscriberId);
            if (Objects.isNull(subscriber)) {
                throw new ValidationException(HttpStatus.UNAUTHORIZED.value(), "Unauthorized, User does not exist.");
            }

            optimRole = getOptimRoleFromOauthConfig(userInfo.getUserType(), null);

            LOG.info("** OAUTH DEBUG LOGS ** optimRole : " + optimRole);
            if (Objects.isNull(optimRole))
                optimRole = getOptimRoleByOauthConfig(oauthConfig);

            SubscriberRole subscriberRole = subscriber.getSubscriberRole().iterator().next();
            Role role = subscriberRole.getRole();
            if (Objects.isNull(role) || !role.getId().equals(optimRole.getId())) {   //
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "User Role misconfigured ");
            }

            EditSubscriberRequest editSubscriber = new EditSubscriberRequest();
            if (!subscriber.getEmail().equals(userInfo.getEmail()) || !subscriber.getFirstName().equalsIgnoreCase(userInfo.getFirstName()) || !subscriber.getLastName().equalsIgnoreCase(userInfo.getLastName())) {
                checkAccountMismatch(userInfo.getEmail(), userInfo.getGlobalAccountId());
                editSubscriber.setEmail(userInfo.getEmail());
                editSubscriber.setLastName(userInfo.getLastName());
                editSubscriber.setFirstName(userInfo.getFirstName());
                manageSubscriberService.editSubscriber(userInfo.getGlobalAccountId(), editSubscriber);
            }

            //TODO add equipment ID on the token
            token = createTokenForUser(userInfo.getEmail(), true, CUSTOMER, userInfo.getGlobalAccountId());
            LOG.info("** OAUTH DEBUG LOGS ** token : " + token);
        } else if (EMPLOYEE.equals(userInfo.getUserType())) {
            if (userInfo.getMemberOf().isEmpty()) {
                throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Unauthorized, memberOf content does not exist");
            }

            optimRole = getOptimRoleFromOauthConfig(userInfo.getUserType(), userInfo.getMemberOf());
            LOG.info("** OAUTH DEBUG LOGS ** userInfoResponse : " + optimRole);
            if (Objects.isNull(optimRole))
                optimRole = getOptimRoleByOauthConfig(oauthConfig);
            String userStatus = isInternalUserCreatedWithSameEmail(userInfo.getEmail(), optimRole, oauthConfig, s3imageUrl);

            // Create External User If not Exists
            if (Objects.isNull(userStatus)) {
                throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
            }
            if (userStatus.equals("EXTERNAL")) {
                token = createTokenForUser(userInfo.getEmail(), false, EMPLOYEE, userInfo.getGlobalAccountId());
            } else if (userStatus.equals("NEW")) {
                token = createExternalUser(userInfo.getFirstName(), userInfo.getLastName(), s3imageUrl, userInfo.getEmail(), optimRole, oauthConfig, EMPLOYEE);
            } else {
                throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "This email is already registered as an Optim IAM user, so cannot use this method to Login");
            }
        } else {
            throw new ValidationException(HttpStatus.UNAUTHORIZED.value(), "Unauthorized");
        }

        return token;
    }

    private UserInfo fetchUserInfoFromOauthServer(OauthConfig oauthConfig, String accessToken) {
        UserInfo userInfo = null;
        if (introspectionEndpointExist(oauthConfig)) {
            HashMap<String, Object>userInfoResponse = getUserInfoUsingAccessTokenForIntrospectionEndpoint(oauthConfig, accessToken);
            userInfo = UserInfo.fromResponseOfIntrospectionEndPoint(userInfoResponse);
        } else {
            HashMap<String, Object>userInfoResponse = getUserInfoUsingAccessToken(oauthConfig, accessToken);
            userInfo = UserInfo.fromResponseOfUserinfoEndPoint(userInfoResponse);
        }

        if (userInfo.getFirstName() == null || userInfo.getLastName() == null || userInfo.getEmail() == null) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Missing Parameters in Oauth Response");
        }
        return userInfo;
    }

    private Role getOptimRoleByOauthConfig(OauthConfig oauthConfig) throws Exception {
        List<Role> roleList = new ArrayList<>();
        if (Objects.nonNull(hazelcastService.read(SYSTEM_ADMIN, ROLE))) {
            roleList = (List<Role>) hazelcastService.read(SYSTEM_ADMIN, ROLE);
        }

        Role optimRole;
        if (!roleList.isEmpty()) {
            optimRole = roleList.stream().filter(q -> q.getId().equals(oauthConfig.getRoleId())).findAny().orElse(null);
        } else {
            optimRole = (Role) dataAccessService.read(Role.class, oauthConfig.getRoleId());
        }

        return optimRole;
    }

    private void checkUserLimit(String email) throws Exception {
        Long userLimit = Long.valueOf(commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG).get(ACTIVE_USER_LIMIT));
        Long activeUsers = 0L;

        Integer tokenExpirationTime = 0;
        tokenExpirationTime = jwtConfig.getTokenExpirationTime();
        HashMap<String, Object> query = new HashMap<>();
        query.put("tokenExpiration", tokenExpirationTime);

        activeUsers = Long.valueOf(dataAccessService.readNative(TokenSQL.GET_ACTIVE_TOKEN_FOR_EXTERNAL_AND_MOBILE_USER, query).iterator().next().toString());
        LOG.info("active user :: " + activeUsers + "user limit :: " + userLimit);
        if (activeUsers >= userLimit)
            throw new UserLoginLimitException(HttpStatus.BAD_REQUEST.value(), email);
    }

    private Role getOptimRoleFromOauthConfig(String userType, Set<String> memberOf) throws Exception {
        List<OauthRoleMapping> oauthRoleMappingList = (List<OauthRoleMapping>) dataAccessService.read(OauthRoleMapping.class);

        OauthRoleMapping oauthRoleMapping = oauthRoleMappingList.stream().filter(ele -> userType.equals(ele.getOauthRole())).findAny().orElse(null);
        LOG.info("oauthRoleMapping : " + oauthRoleMapping);
        if (Objects.isNull(oauthRoleMapping))
            throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Oauth mapping config does not exist");

        HashMap<String, Object> memberOfRole = null;
        if (CUSTOMER.equals(userType)) {
            List<HashMap<String, Object>> optimRoleList = mapper.readValue(oauthRoleMapping.getOptimRole(), List.class);
            LOG.info("optimRoleList : " + optimRoleList);
            if (Objects.isNull(optimRoleList) || optimRoleList.isEmpty())
                throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Oauth mapping config does not exist");
            memberOfRole = optimRoleList.get(0);
        } else {
            final List<HashMap<String, Object>> optimRoleList = mapper.readValue(oauthRoleMapping.getOptimRole(), List.class);
            if (Objects.isNull(optimRoleList) || optimRoleList.isEmpty()) {
                throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Oauth mapping config does not exist");
            }

            List<HashMap<String, Object>> memberOfRoleList = new ArrayList<>();
            memberOf.forEach(element -> {
                HashMap<String, Object> memberRole = optimRoleList.stream().filter(q -> element.trim().equals(String.valueOf(q.get("oauthGroup")))).findAny().orElse(null);
                if (Objects.nonNull(memberRole))
                    memberOfRoleList.add(memberRole);
            });
            if (memberOfRoleList.isEmpty())
                throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Unauthorized, invalid memberOf content provided");

            if (memberOfRoleList.size() > 1) {
                try {
                    memberOfRoleList.sort((q1, q2) -> Integer.compare((Integer) q1.get("precedence"), (Integer) q2.get("precedence")));
                } catch (Exception e) {
                    LOG.error("Error in searching role : ", e);
                }
            }

            memberOfRole = memberOfRoleList.get(0);
        }

        Role role = (Role) dataAccessService.read(Role.class, memberOfRole.get("optimRoleId").toString());
        return role;
    }

    private Map createAppUser(UserInfo userInfo, Role optimRole, OauthConfig oauthConfig, String s3ImageUrl, String userType) throws Exception {

        Boolean isSubscriber = false;
        String tokenType = EXTERNAL_USER;
        String id = CommonUtils.generateUUID();

        HashMap<String, Object> params = new HashMap<>();
        params.clear();
        params.put("email", userInfo.getEmail());
        List<Subscriber> subscribers = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_BY_EMAIL, params);
        if (subscribers != null && !subscribers.isEmpty()) {
            isSubscriber = true;
            tokenType = SUBSCRIBER;
            id = subscribers.get(0).getId();
        }

        List<LoginDetails> loginDetailsList = (List<LoginDetails>) dataAccessService.read(LoginDetails.class, LoginDetailsSQL.GET_USER_BY_EMAIL, params);
        LoginDetails loginDetails = null;
        if(loginDetailsList != null && !loginDetailsList.isEmpty()) {
            loginDetails = loginDetailsList.iterator().next();
        }

        if(loginDetails == null) {
            loginDetails = new LoginDetails();
            loginDetails.setId(id);
            loginDetails.setEmail(userInfo.getEmail());
            loginDetails.setSubscriber(isSubscriber);
            loginDetails.setActive(true);
            loginDetails.setToken(null);
            loginDetails.setTokenCreatedAt(new Date());
            loginDetails.setTokenUpdatedAt(new Date());
            loginDetails.setTokenType(tokenType);
            CommonUtils.setCreateEntityFields(loginDetails);
            dataAccessService.create(LoginDetails.class, loginDetails);
        }

        UserContext userContext;
        if(loginDetails.isSubscriber()) {
            Subscriber subscriber = subscribers.get(0);
            if (subscriber.getSubscriberRole() == null)
                throw new InsufficientAuthenticationException("User has no roles assigned");
            subscriber.setFirstName(userInfo.getFirstName());
            subscriber.setLastName(userInfo.getLastName());
            userContext = prepareUserContext(false, subscriber, EMPTY_STRING);
        } else {

            params.clear();
            params.put("ispId", oauthConfig.getIsp());
            List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_COMPARTMENT_BY_ISPID, params);
            Set<Compartment> compartmentSet = null;
            if(compartmentList != null) {
                compartmentSet = new HashSet<>(compartmentList);
            }
            UserRole userRole = new UserRole();
            userRole.setId(CommonUtils.generateUUID());
            userRole.setRole(optimRole);
            CommonUtils.setCreateEntityFields(userRole);
            userRole.setGrantBy(UserRole.RoleType.DIRECT.name());

            Set<UserRole> roles = new HashSet<>();
            roles.add(userRole);

            User user = new User();
            user.setId(id);
            user.setUserRole(roles);
            user.setEmail(userInfo.getEmail());
            user.setVerify(true);
            user.setId(loginDetails.getId());
            user.setImageUrl(s3ImageUrl);
            user.setFirstName(userInfo.getFirstName());
            user.setLastName(userInfo.getLastName());
            user.setCompartment(compartmentSet);

            userContext = prepareUserContextV2(false, user, EMPTY_STRING, isSubscriber);
        }

        return generateTokenForExternalUser(userContext);
    }

    private Map createExternalUser(String firstName, String lastName, String s3imageUrl, String email, Role optimRole, OauthConfig oauthConfig, String userType) throws Exception {
        User u = new User();
        u.setId(CommonUtils.generateUUID());
        Set<UserRole> roles = new HashSet<>();
        UserRole userRole = new UserRole();
        userRole.setId(CommonUtils.generateUUID());
        userRole.setRole(optimRole);
        CommonUtils.setCreateEntityFields(userRole);
        userRole.setGrantBy(UserRole.RoleType.DIRECT.name());
        roles.add(userRole);
        u.setUserRole(roles);
        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", oauthConfig.getIsp());
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);

        if (compartmentList == null || compartmentList.isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group / ISP mapping missing");
        }

        Set<Compartment> compartment = new HashSet<>();
        compartment.add(compartmentList.get(0));
        u.setCompartment(compartment);
        u.setCompany(compartmentList.get(0).getName());
        u.setTitle("EXTERNAL");
        u.setFirstName(firstName);
        u.setLastName(lastName + " (" + oauthConfig.getOauthProvider() + ")");
        u.setGender(Gender.valueOf("MALE"));
        u.setImageUrl(s3imageUrl);
        u.setEmail(email);
        u.setVerify(true);
        u.setInternalUser(false);
        u.setPassword(encoder.encode(EXTERNAL_USER_PASSWORD));
        CommonUtils.setCreateEntityFields(u);

        try {
            persistUser(u);
            HashMap<String, Object> userMap = esServiceImpl.prepareDataForES(u, false);
            esService.insert(USER_INDEX, userMap, u.getId(), null);
        } catch (ValidationException vex) {
            throw vex;
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Failed to update User details!");
        }

        return createTokenForUser(email, false, userType, null);
    }

    @Transactional
    private void persistUser(User user) throws Exception {
        setCurrentIPToUser(user);
        dataAccessService.create(User.class, user);

        LoginDetails loginDetails = new LoginDetails();
        loginDetails.setId(user.getId());
        loginDetails.setEmail(user.getEmail());
        loginDetails.setSubscriber(false);
        loginDetails.setActive(true);
        loginDetails.setToken(null);
        loginDetails.setTokenCreatedAt(null);
        loginDetails.setTokenUpdatedAt(null);
        loginDetails.setTokenType(EXTERNAL_USER);
        CommonUtils.setCreateEntityFields(loginDetails);

        dataAccessService.create(LoginDetails.class, loginDetails);
    }

    private String fetchSubscriberInfoByGlobalAccountId(String globalAccountId) throws Exception {
        //TODO if there is multiple Subscriber for same globalAccountId the get first index data
        String subscriberId = null;
        HashMap<String, Object> param = new HashMap<>();
        param.put("globalAccountNo", globalAccountId);
        List<Subscriber> subscriberList = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_BY_GLOBAL_ACCOUNT_NUMBER, param);
        if (Objects.nonNull(subscriberList) && !subscriberList.isEmpty()) {
            subscriberId = subscriberList.get(0).getId();
        } else {
            //TODO fetch from windstream microservices
        }

        return subscriberId;
    }

    private String fetchEquipmentInfoByServiceTelephoneNumber(String serviceTelephoneNo) throws Exception {
        HashMap<String, Object> param = new HashMap<>();
        param.put("serviceTelephoneNo", serviceTelephoneNo);
        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_STN, param);
        if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty()) {
            return equipmentList.get(0).getId();
        } else {
            throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "User does not have any Equipments with STN :: " + serviceTelephoneNo);
        }
    }

    private String savePictureToS3(String pictureUrl, String accessToken, String sub) {
        String s3ImageURL = null;
        HashMap<String, String> headerParams = new HashMap<>();
        headerParams.put("Authorization", "Bearer " + accessToken);
        try {
            byte[] respBytes = httpService.doGetByte(pictureUrl, headerParams, new HashMap<>());
            LOG.info("** OAUTH DEBUG LOGS ** respBytes in saving picture : " + respBytes);
            if (Objects.nonNull(respBytes) && respBytes.length > 0) {
                s3ImageURL = s3Service.uploadImageByteArray(USER_IMAGE_PATH + sub, respBytes);
            }
        } catch (Exception e) {
            LOG.error("Error while saving image :: ", e);
        }

        return s3ImageURL;
    }

    private Map createTokenForUser(String userPrincipalName, boolean isInternal, String userType, String gan) throws Exception {
        HashMap<String, Object> params = new HashMap<>();

        LoginDetails loginDetails;
        try {
            params.clear();
            params.put("email", userPrincipalName);
            loginDetails = (LoginDetails) dataAccessService.read(LoginDetails.class, LoginDetailsSQL.GET_USER_BY_EMAIL, params).iterator().next();
            if (Objects.isNull(loginDetails)) {
                throw new UsernameNotFoundException("User not found: " + userPrincipalName);
            }
        } catch (Exception e) {
            loginDetails = new LoginDetails();
            Boolean isSubscriber = true;
            String tokenType = null;
            String id = null;
            List<User> users = null;
            List<Subscriber> subscribers = null;
            if (userType.equals(EMPLOYEE)) {
                params.clear();
                params.put("email", userPrincipalName);
                users = (List<User>) dataAccessService.read(User.class, UserSQL.GET_USER_BY_EMAIL, params);
                id = users.get(0).getId();
                isSubscriber = false;
                tokenType = EXTERNAL_USER;
            } else if (userType.equals(CUSTOMER)) {
                if ("".equals(gan)){
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Global Account No can not be empty");
                }
                params.clear();
                params.put("globalAccountNo", gan);
                subscribers = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_BY_GLOBAL_ACCOUNT_NUMBER, params);
                id = subscribers.get(0).getId();
                isSubscriber = true;
                tokenType = SUBSCRIBER;
            }

            loginDetails.setId(id);
            loginDetails.setEmail(userPrincipalName);
            loginDetails.setSubscriber(isSubscriber);
            loginDetails.setActive(true);
            loginDetails.setToken(null);
            loginDetails.setTokenCreatedAt(null);
            loginDetails.setTokenUpdatedAt(null);
            loginDetails.setTokenType(tokenType);
            CommonUtils.setCreateEntityFields(loginDetails);

            dataAccessService.create(LoginDetails.class, loginDetails);
//            throw new UsernameNotFoundException("Authentication Failed. Username is not valid.");
        }

        UserContext userContext;
        try {
            if (loginDetails.isSubscriber()) {
                params.clear();
                params.put("globalAccountNo", gan);
                List<Subscriber> subscribers = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_BY_GLOBAL_ACCOUNT_NUMBER, params);
                if (subscribers == null || subscribers.isEmpty()) {
                    params.clear();
                    params.put("email", userPrincipalName);
                    subscribers = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_BY_EMAIL, params);
                    if (subscribers == null || subscribers.isEmpty()) {
                        throw new UsernameNotFoundException("User not found: " + userPrincipalName);
                    }
                }
                Subscriber subscriber = subscribers.get(0);
                if (subscriber.getSubscriberRole() == null)
                    throw new InsufficientAuthenticationException("User has no roles assigned");
                userContext = prepareUserContext(isInternal, subscriber, EMPTY_STRING);
            } else {
                User user = (User) dataAccessService.read(User.class, loginDetails.getId());
                if (user == null) {
                    throw new UsernameNotFoundException("User not found: " + userPrincipalName);
                }

                if (user.getUserRole() == null)
                    throw new InsufficientAuthenticationException("User has no roles assigned");

                userContext = prepareUserContext(isInternal, user, EMPTY_STRING);
            }
        } catch (UsernameNotFoundException e) {
            throw e;
        } catch (InsufficientAuthenticationException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("", e);
            throw new UsernameNotFoundException("User not found: " + userPrincipalName);
        }

        return generateTokenForExternalUser(userContext);
    }

    private UserContext prepareUserContextV2(boolean isExternalUser, User user, String equipmentId, boolean isSubscriber) throws Exception {
        List<String> dataSecurityTypeList = new ArrayList<>();
        UserRole userRole = user.getUserRole().iterator().next();
        Role role = userRole.getRole();
        String dataSecurityTypes = role.getDataSecurityMapping();
        if (Objects.nonNull(dataSecurityTypes)) {
            String[] data = dataSecurityTypes.split(ApplicationCommonConstants.EMPTY_STRING);
            for (int i = 0; i < data.length; i++) {
                dataSecurityTypeList.add(data[i]);
            }
        }

        String groupId = "";
        String roleId = null;

        if(user.getCompartment() != null) {
            groupId = user.getCompartment().iterator().next().getId();
        }

        if(user.getUserRole() != null) {
            roleId = user.getUserRole().iterator().next().getRole().getId();
        }

        return UserContext.create(user.getId(), user.getUsername(), user.getFirstName() + SPACE + user.getLastName(),
                CurrentTenantIdentifier.DEFAULT_TENANT, CommonUtils.generateScopePayloadForToken(user), groupId, roleId, SPACE, user.isVerify(), isExternalUser ? false : true, dataSecurityTypeList, user.getImageUrl(), isSubscriber, equipmentId);
    }

    private UserContext prepareUserContext(boolean isExternalUser, User user, String equipmentId) throws Exception {
        List<String> dataSecurityTypeList = new ArrayList<>();
        UserRole userRole = user.getUserRole().iterator().next();
        Role role = userRole.getRole();
        String dataSecurityTypes = role.getDataSecurityMapping();
        if (Objects.nonNull(dataSecurityTypes)) {
            String[] data = dataSecurityTypes.split(ApplicationCommonConstants.EMPTY_STRING);
            for (int i = 0; i < data.length; i++) {
                dataSecurityTypeList.add(data[i]);
            }
        }

        String groupId = "";
        String roleId = null;

        if(user.getCompartment() != null) {
            groupId = user.getCompartment().iterator().next().getId();
        }

        if(user.getUserRole() != null) {
            roleId = user.getUserRole().iterator().next().getRole().getId();
        }

        return UserContext.create(user.getId(), user.getUsername(), user.getFirstName() + SPACE + user.getLastName(),
                CurrentTenantIdentifier.DEFAULT_TENANT, CommonUtils.generateScopePayloadForToken(user), groupId, roleId, SPACE, user.isVerify(), isExternalUser ? false : true, dataSecurityTypeList, user.getImageUrl(), false, equipmentId);
    }

    private UserContext prepareUserContext(boolean isExternalUser, Subscriber subscriber, String equipmentId) throws Exception {
        List<String> dataSecurityTypeList = new ArrayList<>();

        SubscriberRole subscriberRole = subscriber.getSubscriberRole().iterator().next();
        Role role = subscriberRole.getRole();
        String dataSecurityTypes = role.getDataSecurityMapping();
        if (Objects.nonNull(dataSecurityTypes)) {
            String[] data = dataSecurityTypes.split(ApplicationCommonConstants.EMPTY_STRING);
            for (int i = 0; i < data.length; i++) {
                dataSecurityTypeList.add(data[i]);
            }
        }

        LOG.info("isSubscriber active : " + subscriber.isActive());
        LOG.info("subscriber Role : " + subscriberRole.getRole());
        return UserContext.create(subscriber.getId(), subscriber.getEmail(), subscriber.getFirstName() + SPACE + subscriber.getLastName(),
                CurrentTenantIdentifier.DEFAULT_TENANT, subscriber.getAuthorities(), subscriber.getCompartment().iterator().next().getId(), subscriber.getSubscriberRole().iterator().next().getRole().getId(), SPACE, subscriber.isActive(), isExternalUser ? false : true, dataSecurityTypeList, subscriber.getImageUrl(), true, equipmentId);
    }

    private Map<String, Object> generateTokenForExternalUser(UserContext userContext) {
        String issuer;
        try {
            if (hazelcastService.isMapExist(TOKEN_ISSUERS) && Objects.nonNull(hazelcastService.read(ISSUER_TEXT, TOKEN_ISSUERS))) {
                TokenIssuer tokenIssuer = (TokenIssuer) hazelcastService.read(ISSUER_TEXT, TOKEN_ISSUERS);
                issuer = tokenIssuer.getIssuerText();
            } else {
                List<TokenIssuer> tokenIssuers = (List<TokenIssuer>) dataAccessService.read(TokenIssuer.class);
                issuer = tokenIssuers.get(0).getIssuerText();
            }
        } catch (Exception e) {
            LOG.error("Issuer id not found == " + e.getMessage());
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }
        JwtToken accessToken = tokenFactory.createAccessJwtToken(userContext, issuer);

        Map<String, Object> tokenMap = new HashMap<>();
        tokenMap.put("accessToken", accessToken.getToken());
        tokenMap.put("verify", userContext.getVerify());

        HashMap<String, String> params = new HashMap<>();
        params.put("user_id", userContext.getId());

        try {
            LoginDetails loginDetails = (LoginDetails) dataAccessService.read(LoginDetails.class, userContext.getId());

            if(loginDetails != null) {
                loginDetails.setToken(accessToken.getToken());
                loginDetails.setTokenCreatedAt(Objects.isNull(loginDetails.getTokenUpdatedAt()) ? DateUtils.getCurrentDate() : new Date(loginDetails.getTokenUpdatedAt().getTime()));
                loginDetails.setTokenUpdatedAt(DateUtils.getCurrentDate());
                loginDetails.setAgent(userContext.getTokenIssuer());
            }

            CommonUtils.setUpdateEntityFields(loginDetails);
            dataAccessService.update(LoginDetails.class, loginDetails);

            HashMap<String, Object> userTokenMap = new HashMap<>();
            userTokenMap.put("token", accessToken.getToken());
            userTokenMap.put("createdAt", loginDetails.getTokenCreatedAt().getTime());
            userTokenMap.put("updatedAt", loginDetails.getTokenUpdatedAt().getTime());

            hazelcastService.createForTokenTtl(userContext.getId(), userTokenMap, TOKEN);
        } catch (Exception e) {
            LOG.error("Error while Updating UserToken == " + e.getMessage());
        }

        return tokenMap;
    }

    private String isInternalUserCreatedWithSameEmail(String userPrincipalName, Role optimRole, OauthConfig oauthConfig, String s3imageUrl) throws Exception {
        String query = UserSQL.GET_USER_BY_EMAIL;
        String status;
        HashMap<String, Object> params = new HashMap<>();
        params.put("email", userPrincipalName);
        List<User> users = new ArrayList<>();
        try {
            users = (List<User>) dataAccessService.read(User.class, query, params);
        } catch (Exception e) {
        }
        if (users == null || users.isEmpty()) {
            status = "NEW";
        } else if (users.get(0).isInternalUser()) {
            //IF User already exist with same email as Internal User then Update user as External ODI-2705
            updateUserAsExternalUser(users.get(0), optimRole, oauthConfig, s3imageUrl);
            status = "EXTERNAL";
        } else {
            try {
                User user = users.get(0);
                Set<UserRole> userRoles = user.getUserRole();
                UserRole userRole = userRoles.iterator().next();
                userRole.setRole(optimRole);
                userRoles.add(userRole);
                user.setUserRole(userRoles);

                HashMap<String, String> param = new HashMap<>();
                param.put("ispId", oauthConfig.getIsp());
                List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, param);
                if (compartmentList == null || compartmentList.isEmpty()) {
                    throw new ValidationException(HttpStatus.FORBIDDEN.value(), "Group / ISP mapping missing");
                }
                Set<Compartment> compartment = new HashSet<>();
                compartment.add(compartmentList.get(0));
                user.setCompartment(compartment);
                user.setImageUrl(s3imageUrl);
                user.setCompany(compartmentList.get(0).getName());

                CommonUtils.setUpdateEntityFields(user);
                setCurrentIPToUser(user);
                dataAccessService.update(User.class, user);

                esService.updateById(USER_INDEX, esServiceImpl.prepareDataForES(user, false), user.getId(), null);
            } catch (Exception e) {
                LOG.error("Error while Updating external user role", e);
                throw e;
            }
            status = "EXTERNAL";
        }
        return status;
    }

    public void setCurrentIPToUser(User user) {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.getRequestAttributes())).getRequest();
            user.setIp(String.valueOf(clientInfoUtils.getClientIpAddr(request)));
        } catch (Throwable t) {
            LOG.error("Error in fetching userIp ", t);
        }
    }

    private void updateUserAsExternalUser(User user, Role optimRole, OauthConfig oauthConfig, String s3imageUrl) throws Exception {
        Set<UserRole> roles = user.getUserRole();

        UserRole userRole = roles.iterator().next();
        userRole.setRole(optimRole);
        CommonUtils.setUpdateEntityFields(userRole);
        userRole.setGrantBy(UserRole.RoleType.DIRECT.name());
        roles.add(userRole);

        user.setUserRole(roles);

        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", oauthConfig.getIsp());
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);

        if (compartmentList == null || compartmentList.isEmpty()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group / ISP mapping missing");
        }
        Set<Compartment> compartment = new HashSet<>();
        compartment.add(compartmentList.get(0));
        user.setCompartment(compartment);
        user.setCompany(compartmentList.get(0).getName());
        user.setTitle("EXTERNAL");
        user.setLastName(user.getLastName() + " (" + oauthConfig.getOauthProvider() + ")");
        user.setGender(Gender.valueOf("MALE"));
        user.setImageUrl(s3imageUrl);
        user.setVerify(true);
        user.setInternalUser(false);
        user.setPassword(encoder.encode(EXTERNAL_USER_PASSWORD));
        CommonUtils.setUpdateEntityFields(user);

        try {
            setCurrentIPToUser(user);
            dataAccessService.update(User.class, user);
            HashMap<String, Object> userMap = esServiceImpl.prepareDataForES(user, false);
            esService.insert(USER_INDEX, userMap, user.getId(), null);
        } catch (ValidationException vex) {
            throw vex;
        } catch (Exception e) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Failed to update User details!");
        }
    }

    public OauthConfigResponse getDefaultOauthConfigAsResponse(String apiKey, boolean isSecure) throws Exception {
        checkApiKey(isSecure, apiKey);
        return getDefaultOauthConfigAsResponse();
    }

    public OauthConfigResponse getDefaultOauthConfigAsResponse() throws Exception {
        OauthConfigResponse oauthConfigResponse = null;
        OauthConfig defaultConfig = oauthConfigDao.findDefault();
        if (defaultConfig != null) {
            oauthConfigResponse = OauthConfigResponse.fromOauthConfig(defaultConfig);
            fillAuthorizationUrl(oauthConfigResponse);
        }
        return oauthConfigResponse;
    }

    public OauthConfig getDefaultOauthConfig() throws Exception {
        return oauthConfigDao.findDefault();
    }

    private void fillAuthorizationUrl(OauthConfigResponse oauthConfigResponse) {
        String authUrl = getInfoURLFromWellKnownConfig(oauthConfigResponse.getWellKnownConfig(), AUTHORIZATION_INFO_URL);
        oauthConfigResponse.setAuthorizationURL(authUrl);
    }

    public List<OauthConfigResponse> getAllOauthConfigs(String apiKey, boolean isSecure) throws Exception {
        checkApiKey(isSecure, apiKey);
        List<OauthConfigResponse> result = getAllOauthConfigsResponse();
        return result;
    }

    public List<OauthConfigResponse> getAllOauthConfigsResponse() throws Exception {
        List<OauthConfigResponse> result = new ArrayList<>();
        for(OauthConfig oauthConfig : oauthConfigDao.listAll()) {
            OauthConfigResponse oauthConfigResponse = OauthConfigResponse.fromOauthConfig(oauthConfig);
            result.add(oauthConfigResponse);
        }
        return result;
    }

    public OauthConfigResponse getOauthConfigResponse(String id) throws Exception {
        OauthConfig oauthConfig = oauthConfigDao.find(id);
        OauthConfigResponse oauthConfigResponse = OauthConfigResponse.fromOauthConfig(oauthConfig);
        return oauthConfigResponse;
    }

    @Transactional
    public void createOauthConfig(OauthProviderConfig oauthProviderConfig) throws Exception {
        if (!validateWellKnownConfigURL(oauthProviderConfig.getWellKnownConfig())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Well Known Config URL provided.");
        }
        OauthConfig oauthConfig = oauthProviderConfig.toOauthConfig();
        CommonUtils.setCreateEntityFields(oauthConfig);

        oauthConfigDao.create(oauthConfig);
    }

    @Transactional
    public void updateOauthConfig(String id, OauthProviderConfig oauthProviderConfig) throws Exception {
        if (!validateWellKnownConfigURL(oauthProviderConfig.getWellKnownConfig())) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Well Known Config URL provided.");
        }
        OauthConfig oc;
        String oldIsp = null;
        String oldRoleId = null;
        OauthConfig config = (OauthConfig) dataAccessService.read(OauthConfig.class, id);
        if (null != config) {
            oc = config;
            oldRoleId = config.getRoleId();
            oldIsp = config.getIsp();
            CommonUtils.setUpdateEntityFields(oc);
            oc.setName(oauthProviderConfig.getName());
            oc.setClientId(oauthProviderConfig.getClientId());
            oc.setClientSecret(oauthProviderConfig.getClientSecret());
            oc.setOauthProvider(oauthProviderConfig.getOauthProvider());
            oc.setRedirectUri(oauthProviderConfig.getRedirectUri());
            oc.setIsp(oauthProviderConfig.getIsp());
            oc.setRoleId(oauthProviderConfig.getDefaultRoleId());
            oc.setWellKnownConfig(oauthProviderConfig.getWellKnownConfig());
            oc.setRespType(oauthProviderConfig.getRespType());
            oc.setScope(oauthProviderConfig.getScope());
            oc.setDefaultProvider(oauthProviderConfig.isDefaultProvider());
            if (oauthConfigDao.update(oc) == null) {
                throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Error While Saving the Oauth Config");
            }
        }

        if (Objects.nonNull(oldIsp) && Objects.nonNull(oldRoleId)) {
            if (!oldIsp.equals(oauthProviderConfig.getIsp()) || !oldRoleId.equals(oauthProviderConfig.getDefaultRoleId())) {
                HashMap<String, Object> params = new HashMap<>();
                List<String> users = (List<String>) dataAccessService.read(User.class, UserSQL.GET_ALL_EXTERNAL_USER, params);
                if (!users.isEmpty()) {
                    Runnable clearExternalUsersTokenFromCache = () -> {
                        try {
                            Iterator usersIterator = users.iterator();
                            while (usersIterator.hasNext())
                                manageCommonService.deleteUserTokenFromHazelcast((String) usersIterator.next());
                        } catch (Exception e) {
                            LOG.error("Error processing delete operation", e);
                        }
                    };
                    // XXX: DONT CREATE NEW THREAD
                    new Thread(clearExternalUsersTokenFromCache).start();
                }
            }
        }
    }

    @Transactional
    public void deleteOauthConfig(String id) throws Exception {
        oauthConfigDao.delete(id);
    }

    public List<OauthRoleMapping> createEditOauthRoleMapping(List<OauthRoleMappingConfig> oAuthRoleMappingConfigList) throws Exception {
        List<OauthRoleMapping> toBeEditedCreated = new ArrayList<>();
        oAuthRoleMappingConfigList.forEach(i -> {
            if (Objects.isNull(i.getOauthRole()) || i.getOauthRole().isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Oauth Role Name cannot be empty");

            if (Objects.isNull(i.getOptimRole()) || i.getOptimRole().isEmpty())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "IAM Role cannot be empty. Please assign any IAM Role for OAuth Role Name :: " + i.getOauthRole());

            OauthRoleMapping m = new OauthRoleMapping();
            m.setId(CommonUtils.generateUUID());
            m.setOauthRole(i.getOauthRole());
            List<HashMap<String, Object>> optimRoleList = i.getOptimRole();
            HashSet<Integer> setOfPrecedence = new HashSet<>();
            HashSet<String> setOfOptimRoleId = new HashSet<>();
            if (Objects.nonNull(optimRoleList) && !optimRoleList.isEmpty()) {
                List<HashMap<String, Object>> listToCreate = new ArrayList<>();
                optimRoleList.forEach(optimRole -> {
                    HashMap<String, Object> optimRoleMapping = new HashMap<>();
                    if (Objects.nonNull(optimRole.get("precedence"))) {
                        try {
                            Integer precedence = Integer.valueOf(optimRole.get("precedence").toString().trim());
                            if (setOfPrecedence.contains(precedence))
                                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Duplicate Precedence exist");
                            setOfPrecedence.add(precedence);
                            optimRoleMapping.put("precedence", precedence);
                        } catch (ValidationException e) {
                            throw e;
                        } catch (Exception e) {
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Value passed for Precedence");
                        }
                    }
                    if (Objects.isNull(optimRole.get("optimRoleId"))) {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Please Select Optim Role");
                    } else {
                        try {
                            Role role = (Role) dataAccessService.read(Role.class, optimRole.get("optimRoleId").toString());
                            if (Objects.isNull(role))
                                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Optim Role with id :: " + optimRole.get("optimRoleId"));
                            if (setOfOptimRoleId.contains(optimRole.get("optimRoleId").toString()))
                                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Optim Role already exist");
                            setOfOptimRoleId.add(optimRole.get("optimRoleId").toString());
                            optimRoleMapping.put("optimRoleId", optimRole.get("optimRoleId").toString().trim());
                        } catch (ValidationException e) {
                            throw e;
                        } catch (Exception e) {
                            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
                        }
                    }

                    if (Objects.nonNull(optimRole.get("oauthGroup"))) {
                        optimRoleMapping.put("oauthGroup", optimRole.get("oauthGroup").toString().trim());
                    }

                    listToCreate.add(optimRoleMapping);
                });
                if (listToCreate.size() > 1) {
                    if (!(listToCreate.size() == setOfPrecedence.size() && listToCreate.size() == setOfOptimRoleId.size()))
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Oauth Group/Precedence missing");
                }
                try {
                    m.setOptimRole(mapper.writeValueAsString(listToCreate));
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                    throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
                }
            }

            CommonUtils.setCreateEntityFields(m);
            toBeEditedCreated.add(m);
        });

        List<OauthRoleMapping> mappingList = (List<OauthRoleMapping>) dataAccessService.read(OauthRoleMapping.class);
        if (null != mappingList && !mappingList.isEmpty()) {
            mappingList.forEach(item -> {
                try {
                    dataAccessService.delete(OauthRoleMapping.class, item.getId());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        }
        dataAccessService.create(OauthRoleMapping.class, toBeEditedCreated);
        return toBeEditedCreated;
    }

    public List<HashMap<String, Object>> getOauthRoleMapping() throws Exception {
        List<OauthRoleMapping> mappingList = (List<OauthRoleMapping>) dataAccessService.read(OauthRoleMapping.class);
        List<HashMap<String, Object>> OauthRoleMappingList = new ArrayList<>();
        mappingList.forEach(element -> {
            HashMap<String, Object> OauthRole = new HashMap<>();
            OauthRole.put("id", element.getId());
            OauthRole.put("oauthRole", element.getOauthRole());
            try {
                OauthRole.put("optimRole", mapper.readValue(element.getOptimRole(), List.class));
            } catch (IOException e) {
                throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
            }

            OauthRoleMappingList.add(OauthRole);
        });
        return OauthRoleMappingList;
    }

    private String getInfoURLFromWellKnownConfig(String wellKnownConfigUrl, String infoType) {
        String info = null;
        try {
            String wellKnownConfigResp = wellKnownConfigCache.get(wellKnownConfigUrl);
            if (StringUtils.isEmpty(wellKnownConfigResp)) {
                wellKnownConfigResp = httpService.doGet(wellKnownConfigUrl, new HashMap<>(), new HashMap<>());
                wellKnownConfigCache.put(wellKnownConfigUrl, wellKnownConfigResp);
                LOG.debug("reload wellKnownConfigCache, Url:[{}]", wellKnownConfigUrl);
            }
            //String respString = httpService.doGet(wellKnownConfig, new HashMap<>(), new HashMap<>());
            HashMap responseMap = new ObjectMapper().readValue(wellKnownConfigResp, HashMap.class);
            info = (String) responseMap.get(infoType);
            LOG.debug("** OAUTH DEBUG LOGS ** Response from URL:[{}] response:[{}] ", wellKnownConfigUrl, wellKnownConfigResp);
        } catch (Exception e) {
            LOG.error("Some error occured in getInfoURLFromWellKnownConfig : ", e);
        }
        return info;
    }

    private boolean validateWellKnownConfigURL(String url) {
        return null != getInfoURLFromWellKnownConfig(url, AUTHORIZATION_INFO_URL);
    }

    private HashMap<String, Object> getAccessTokenResponseFromOAuthProvider(OauthConfig oauthConfig, String code) {
        HashMap<String, Object> responseMap = null;
        HashMap<String, String> headerParams = new HashMap<>();
        headerParams.put("Content-Type", "application/x-www-form-urlencoded");
        List<NameValuePair> form = new ArrayList<>();
        form.add(new BasicNameValuePair("client_id", oauthConfig.getClientId()));
        if(oauthConfig.getRedirectUri().contains("https")) {
            form.add(new BasicNameValuePair("client_secret", oauthConfig.getClientSecret()));
        }
        form.add(new BasicNameValuePair("redirect_uri", oauthConfig.getRedirectUri()));
        form.add(new BasicNameValuePair("code", code));
        form.add(new BasicNameValuePair("grant_type", GRANT_TYPE));
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(form, Consts.UTF_8);
        try {
            String respString = httpService.doPost(entity, getInfoURLFromWellKnownConfig(oauthConfig.getWellKnownConfig(), TOKEN_INFO_URL), headerParams);
            responseMap = new ObjectMapper().readValue(respString, HashMap.class);
        } catch (Exception e) {
            LOG.error("failed to getAccessTokenResponseFromOAuthProvider", e);
        }

        if (responseMap == null) {
            LOG.error("Access Token is NULL");
            throw new OAUTH2ConfigException(HttpStatus.BAD_REQUEST.value(), "Check Oauth Config / Tenant Mismatch");
        }
        return responseMap;
    }

    private boolean introspectionEndpointExist(OauthConfig oauthConfig) {
        return null != getInfoURLFromWellKnownConfig(oauthConfig.getWellKnownConfig(), INTROSPECTION_ENDPOINT);
    }

    private HashMap<String, Object> getUserInfoUsingAccessToken(OauthConfig oauthConfig, String accessToken) {
        HashMap<String, Object> responseMap = null;
        HashMap<String, String> headerParams = new HashMap<>();
        headerParams.put("Authorization", "Bearer " + accessToken);
        try {
            String respString = httpService.doGet(getInfoURLFromWellKnownConfig(oauthConfig.getWellKnownConfig(), USER_INFO_URL), headerParams, new HashMap<>());
            LOG.info("** OAUTH DEBUG LOGS ** userInfoResponse : " + respString);
            responseMap = new ObjectMapper().readValue(respString, HashMap.class);
        } catch (Exception e) {
            LOG.error("failed to getUserInfoUsingAccessToken", e);
            throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Check Oauth Config / Tenant Mismatch");
        }

        return responseMap;
    }

    private HashMap<String, Object> getUserInfoUsingAccessTokenForIntrospectionEndpoint(OauthConfig oauthConfig, String accessToken) {
        HashMap<String, Object> responseMap = null;
        HashMap<String, String> headerParams = new HashMap<>();
        headerParams.put("Content-Type", "application/x-www-form-urlencoded");
        String key = oauthConfig.getClientId() + COLON + oauthConfig.getClientSecret();
        headerParams.put("Authorization", "Basic " + Base64.getEncoder().encodeToString(key.getBytes()));

        LOG.info("** OAUTH DEBUG LOGS ** AccessToken  : " + accessToken);

        List<NameValuePair> form = new ArrayList<>();
        form.add(new BasicNameValuePair("token", accessToken));
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(form, Consts.UTF_8);

        try {
            String respString = httpService.doPost(entity, getInfoURLFromWellKnownConfig(oauthConfig.getWellKnownConfig(), INTROSPECTION_ENDPOINT), headerParams);
            LOG.info("** OAUTH DEBUG LOGS ** introspection : " + respString);
            responseMap = new ObjectMapper().readValue(respString, HashMap.class);
        } catch (Exception e) {
            LOG.error("Error in fetching data from wellknown config.", e);
            throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Check Oauth Config / Tenant Mismatch");
        }

        return responseMap;
    }

    public String getLogo(String type, String apiKey, boolean isSecure) {
        checkApiKey(isSecure, apiKey);

        if (!(type.equals(DASHBOARD_LOGO) || type.equals(SPLASH_LOGO)))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid logo type.");
        try {
            if (s3Service.getAllFiles(LOGO_INITIAL_PATH + type).isEmpty() || Objects.isNull(s3Service.getAllFiles(LOGO_INITIAL_PATH + type))) {
                return null;
            }
        } catch (Exception e) {
            LOG.error("Exception while getting logo :: ", e);
            return null;
        }
        if (CacheUtils.logoNearCache.containsKey(type) && Objects.nonNull(CacheUtils.logoNearCache.get(type)) && !(CacheUtils.logoNearCache.get(type).toString()).isEmpty()) {
            return CacheUtils.logoNearCache.get(type).toString();
        } else {
            String logoUrl = "https://" + s3Domain + "/" + s3Bucket + "/" + LOGO_INITIAL_PATH + type;
            // try {
            //     String response = httpService.doGet(ZONE_AVAILABILITY_URL, new HashMap<>(), new HashMap<>());
            //     String res = response.substring(0, response.length() - 1);
            //     if (!res.equals("us-east-1")) {
            //         logoUrl = "https://" + s3Bucket + ".s3." + res + ".amazonaws.com/" + LOGO_INITIAL_PATH + type;
            //     }
            // } catch (Exception e) {
            //     LOG.error("Error while fetching availability zone :: ", e);
            //     return null;
            // }
            CacheUtils.logoNearCache.put(type, logoUrl);
            return logoUrl;
        }
    }

    public HashMap<String, Object> getDeadlockedThreads() {
        try {
            Runtime runtime = Runtime.getRuntime();

            LOG.info("* GC STATS * Total mem : " + runtime.totalMemory() / 1000000.0 + " MB");
            LOG.info("* GC STATS * Free mem  : " + runtime.freeMemory() / 1000000.0 + " MB");
            runtime.gc();
            LOG.info("* GC STATS * Total mem(GC) : " + runtime.totalMemory() / 1000000.0 + " MB");
            LOG.info("* GC STATS * Free mem (GC) : " + runtime.freeMemory() / 1000000.0 + " MB");

        } catch (Exception e) {
            LOG.error("some error occured in calling GC.");
        }

        Long startTime = CommonUtils.getCurrentTimeInMillis();
        HashMap<String, Object> healthStatus = new HashMap<>();
        ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();

        long[] threadIds = threadMXBean.findDeadlockedThreads();

        if (threadIds == null || threadIds.length == 0) {
            LOG.info("No deadlocks found. No of threads :: " + threadMXBean.getThreadCount());
            healthStatus.put("status", "OK");
            healthStatus.put("lockedThread", 0);
        } else {
            LOG.info("Blocked Threads :: " + threadIds);
            healthStatus.put("status", "Not OK");
            healthStatus.put("lockedThread", threadIds.length);
        }

        Long endTime = CommonUtils.getCurrentTimeInMillis();
        LOG.info("time consumed :: " + Long.valueOf(endTime - startTime).toString());

        return healthStatus;

    }

    private void checkAccountMismatch(String email, String globalAccountId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("email", email);
        params.put("gan", globalAccountId);
        List<Subscriber> subscriberList = (List<Subscriber>) dataAccessService.read(Subscriber.class, SubscriberSQL.GET_SUBSCRIBER_BY_EMAIL_AND_GAN, params);
        if (Objects.nonNull(subscriberList) && !subscriberList.isEmpty()) {
            throw new ValidationException(HttpStatus.UNAUTHORIZED.value(), "Account Mismatch");
        }
    }

    @Transactional
    public void updateDefaultOauthConfig(OauthProviderConfig oauthProviderConfig) throws Exception {
        oauthProviderConfig.setDefaultProvider(true);
        OauthConfig defaultOauthConfig = oauthConfigDao.findDefault();
        if (defaultOauthConfig == null) {
            createOauthConfig(oauthProviderConfig);
        } else {
            updateOauthConfig(defaultOauthConfig.getId(), oauthProviderConfig);
        }
    }

    @Transactional
    public void deleteDefaultOauthConfig() throws Exception {
        OauthConfig defaultOauthConfig = oauthConfigDao.findDefault();
        if (defaultOauthConfig != null) {
            oauthConfigDao.delete(defaultOauthConfig.getId());
        }
    }

    private OauthConfig findOauthConfigByIdOrDefault(String configId) throws Exception {
        OauthConfig oauthConfig = null;
        if (StringUtils.isNotEmpty(configId)) {
            oauthConfig = oauthConfigDao.find(configId);
        } else {
            oauthConfig = getDefaultOauthConfig();
        }

        if (oauthConfig == null) {
            throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Oauth Config Invalid");
        }

        return oauthConfig;
    }

    private OauthConfig findOauthConfigByNameOrDefault(String oauthConfigName) throws Exception {
        OauthConfig oauthConfig = null;
        if (StringUtils.isNotEmpty(oauthConfigName)) {
            oauthConfig = oauthConfigDao.findByName(oauthConfigName);
        } else {
            oauthConfig = getDefaultOauthConfig();
        }

        if (oauthConfig == null) {
            throw new OAUTH2AuthenticationException(HttpStatus.BAD_REQUEST.value(), "Oauth Config Invalid");
        }

        return oauthConfig;
    }

    private static class UserInfo {
        private String sub;
        private String firstName;
        private String lastName;
        private String email;
        private String phoneNo;
        private String userType;
        private Set<String> memberOf;
        private String pictureUrl;
        private String globalAccountId;

        public static UserInfo fromResponseOfIntrospectionEndPoint(Map<String, Object> responseMap) {
            UserInfo userInfo = new UserInfo();
            userInfo.sub = responseMap.get(SUB) == null ? null : responseMap.get(SUB).toString();
            userInfo.firstName = responseMap.get(FIRST_NAME) == null ? null : responseMap.get(FIRST_NAME).toString();
            userInfo.lastName = responseMap.get(LAST_NAME) == null ? null : responseMap.get(LAST_NAME).toString();
            userInfo.email = responseMap.get(EMAIL_ADDRESS) == null ? null : responseMap.get(EMAIL_ADDRESS).toString();
            userInfo.phoneNo = responseMap.get(CUSTOMER_PHONE_NO) == null ? null : responseMap.get(CUSTOMER_PHONE_NO).toString();
            userInfo.userType = responseMap.get(USER_TYPE) == null ? null : responseMap.get(USER_TYPE).toString();
            userInfo.memberOf = parseMemberOf(responseMap.get(MEMBER_OF));
            userInfo.globalAccountId = responseMap.get(GLOBAL_ACCOUNT_ID) == null ? null : responseMap.get(GLOBAL_ACCOUNT_ID).toString();
            return userInfo;
        }

        public static UserInfo fromResponseOfUserinfoEndPoint(Map<String, Object> responseMap) {
            UserInfo userInfo = new UserInfo();
            userInfo.sub = responseMap.get(SUB) == null ? null : responseMap.get(SUB).toString();
            userInfo.firstName = responseMap.get(GIVEN_NAME) == null ? null : responseMap.get(GIVEN_NAME).toString();
            userInfo.lastName = responseMap.get(FAMILY_NAME) == null ? null : responseMap.get(FAMILY_NAME).toString();
            userInfo.email = responseMap.get(EMAIL) == null ? null : responseMap.get(EMAIL).toString();
            userInfo.phoneNo = responseMap.get(CUSTOMER_PHONE_NO) == null ? null : responseMap.get(CUSTOMER_PHONE_NO).toString();
            userInfo.userType = responseMap.get(USER_TYPE) == null ? null : responseMap.get(USER_TYPE).toString();
            userInfo.memberOf = parseMemberOf(responseMap.get(MEMBER_OF));
            userInfo.pictureUrl = responseMap.get(PICTURE) == null ? null : responseMap.get(PICTURE).toString();
            userInfo.globalAccountId = responseMap.get(GLOBAL_ACCOUNT_ID) == null ? null : responseMap.get(GLOBAL_ACCOUNT_ID).toString();
            return userInfo;
        }

        private static Set<String> parseMemberOf(Object input) {
            // Convert String memberOf to Array to match the contract ODI-2553, ODI-2731
            Set<String> result = new HashSet<>();
            if (input != null) {
                try {
                    List<String> memberOfAsList = (List<String>) input;

                    for (String line : memberOfAsList) {
                        String[] memberOfItems = line.split(",");
                        for (String memberOfItem : memberOfItems) {
                            result.add(memberOfItem.trim());
                        }
                    }
                } catch (Exception e) {
                    LOG.error("failed to parse memberOf. input=[{}]", input, e);
                    result = new HashSet<>();
                }
            }
            return result;
        }

        public String getSub() {
            return sub;
        }

        public String getFirstName() {
            return firstName;
        }

        public String getLastName() {
            return lastName;
        }

        public String getEmail() {
            return email;
        }

        public String getPhoneNo() {
            return phoneNo;
        }

        public String getUserType() {
            return userType;
        }

        public Set<String> getMemberOf() {
            return memberOf;
        }

        public String getPictureUrl() {
            return pictureUrl;
        }

        public String getGlobalAccountId() {
            return globalAccountId;
        }
    }
}
