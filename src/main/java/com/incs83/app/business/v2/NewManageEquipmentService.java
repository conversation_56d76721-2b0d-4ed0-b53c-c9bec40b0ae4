package com.incs83.app.business.v2;/* sakshi created on 4/11/19 inside the package - com.incs83.app.business.v2 */

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.Transactional;
import com.incs83.app.business.v3.FsecureAysnService;
import com.incs83.app.common.v3.DetachedEquipmentDTO;
import com.incs83.app.common.v3.TechnicianDashboardDTO;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.queries.EquipmentSQL;
import com.incs83.app.constants.queries.GroupISPSQL;
import com.incs83.app.entities.ClusterInfo;
import com.incs83.app.entities.Compartment;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.Subscriber;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.Equipment.AttachEquipmentRequest;
import com.incs83.app.responsedto.v2.Equipment.DetachedEquipmentListDTO;
import com.incs83.app.responsedto.v2.Equipment.EditEquipmentRequest;
import com.incs83.app.responsedto.v2.Equipment.EquipmentModel;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberListDTO;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ValidationUtil;
import com.incs83.business.ESService;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.context.ExecutionContext;
import com.incs83.dto.ESRequest;
import com.incs83.dto.ElasticSearchDTO;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.mt.MongoClientHolder;
import com.incs83.mt.MongoTenantTemplate;
import com.incs83.service.ESServiceImpl;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.BulkWriteOptions;
import com.mongodb.client.model.DeleteOneModel;
import com.mongodb.client.model.InsertOneModel;
import com.mongodb.client.model.UpdateManyModel;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.WriteModel;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.elasticsearch.search.sort.SortOrder;
import org.hibernate.exception.ConstraintViolationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.persistence.PersistenceException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;

import static com.incs83.Constants.ESConstants.EXT_MAC;
import static com.incs83.Constants.ESConstants.EXT_SERIAL;
import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.FSECURE_CACHE;
import static com.incs83.app.constants.misc.ActiontecConstants.SERVICE_DETAIL;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.queries.ClusterInfoSQL.DELETE_EQUIPMENT_FROM_CLUSTER_BY_EQUIPMENT_ID;
import static com.incs83.app.constants.queries.ClusterInfoSQL.MAPPING_OF_CLUSTER_AND_EQUIPMENT;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_DETACHED_EQUIPMENT_BY_CLUSTER;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_BY_CLUSTER;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_BY_MACADDRESS;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_BY_STN;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_COUNT_BY_CLUSTER;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_COUNT_SQL;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_FOR_SUBSCRIBER;
import static com.incs83.app.constants.queries.EquipmentSQL.GET_EQUIPMENT_LIST;
import static com.incs83.app.constants.queries.EquipmentSQL.REMOVE_SUBSCRIBER_ID_FROM_EQUIPMENT;
import static com.incs83.app.constants.queries.EquipmentSQL.UPDATE_SN_MAC_OF_EQUIPMENT;
import static com.incs83.app.constants.queries.EquipmentSQL.UPDATE_SUBSCRIBER_ID_FOR_EQUIPMENT;
import static com.incs83.app.constants.queries.SubscriberSQL.GET_EQUIPMENT_BY_SERIAL_NUMBER;
import static com.incs83.app.utils.ValidationUtil.isValidSnString;
import static com.incs83.constants.ApplicationCommonConstants.DEFAULT;
import static com.incs83.constants.ApplicationCommonConstants.EMPTY_STRING;
import static com.incs83.constants.ApplicationCommonConstants.EQUIPMENT_INDEX;
import static com.incs83.constants.ApplicationCommonConstants.INTERNET_SERVICE_PROVIDER;

@Service
public class NewManageEquipmentService {
    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    private DataAccessService dataAccessService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private MongoServiceImpl mongoServiceImpl;
    @Autowired
    private MongoTenantTemplate mongoTemplate;
    @Autowired
    private MongoClientHolder mongoClientHolder;
    @Autowired
    private ESService esService;
    @Autowired
    private ESServiceImpl esServiceImpl;
    @Autowired
    private ISPService ispService;
    @Autowired
    private FsecureAysnService fsecureAysnService;


    @Value("${elastic-search.enable}")
    private Boolean elasticSearchEnable;

    @Value("${rgwMacNotKey:false}")
    private Boolean rgwMacNotKey;

    private void doReplaceServiceSerial(String oldSerial, String newSerial) {
        LOG.info("oldSerial: " + oldSerial + " newSerial: " + newSerial);

        BasicDBObject query = new BasicDBObject();
        query.put("userId", oldSerial);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("_id", 0);

        DBObject serviceObj = mongoServiceImpl.findOne(query, new HashMap<>(), SERVICE_DETAIL, mongoFieldOptions);
        LOG.info(serviceObj);
        if(serviceObj != null) {
            BasicDBObject removeQuery = new BasicDBObject();
            removeQuery.put("userId", oldSerial);
            mongoServiceImpl.deleteOne(removeQuery, SERVICE_DETAIL);

            String stn = String.valueOf(serviceObj.get("serviceTelephoneNo"));
            LOG.info("STN: " + stn);

            if(newSerial != null) {
                serviceObj.put("userId", newSerial);
                serviceObj.put("serialNumber", newSerial);
            } else if(stn != null) {
                serviceObj.put("userId", stn);
            } else {
                return;
            }

            mongoTemplate.getCollection(SERVICE_DETAIL).insert(serviceObj);
        }
    }

    public long getEquipmentLastReportedAt(String apId) {
        long lastReportedAt = 0;
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", apId);
        queryParams.put("type", GATEWAY);

        DBObject aPDetails = mongoServiceImpl.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if (Objects.nonNull(aPDetails)) {
            lastReportedAt = Objects.isNull(aPDetails.get("timestamp")) ? 0 : Long.valueOf(aPDetails.get("timestamp").toString());
        }

        return lastReportedAt;
    }

    public Equipment createEquipment(EquipmentModel createEquipmentRequest) throws Exception {
        try {
            if (!Pattern.matches(LINK_RATE_PATTERN, String.valueOf(createEquipmentRequest.getDownLinkRate()))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "DL not valid");
            }

            if (!Pattern.matches(LINK_RATE_PATTERN, String.valueOf(createEquipmentRequest.getUpLinkRate()))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "UL not valid");
            }

            boolean createEquipmentInMongo = true;
            HashMap<String, String> params = new HashMap<>();

            if (Objects.nonNull(createEquipmentRequest.getRgwSerial()) && !createEquipmentRequest.getRgwSerial().isEmpty()) {
                if(!isValidSnString(createEquipmentRequest.getRgwSerial())) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial number not valid");
                }

                params.put("rgwSerial", String.valueOf(createEquipmentRequest.getRgwSerial()));
                List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_LIST_BY_RGW_SERIAL, params);

                if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty()) {
                    Equipment equipment = equipmentList.get(0);
                    if (Objects.nonNull(createEquipmentRequest.getServiceTelephoneNo()) && !createEquipmentRequest.getServiceTelephoneNo().equals(equipment.getServiceTelephoneNo())) {
                        if(!isValidSnString(createEquipmentRequest.getServiceTelephoneNo())) {
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Service telephone number not valid");
                        }
                        HashMap<String, Object> param = new HashMap<>();
                        param.put("serviceTelephoneNo", createEquipmentRequest.getServiceTelephoneNo());
                        List<Equipment> equipFromSTN = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_BY_STN, param);
                        if (Objects.nonNull(equipFromSTN) && !equipFromSTN.isEmpty())
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "STN already exists");
                    }

                    if(rgwMacNotKey == false) {
                        if (Objects.nonNull(createEquipmentRequest.getRgwMAC()) && !createEquipmentRequest.getRgwMAC().equals(equipment.getRgwMAC()) && !createEquipmentRequest.getRgwMAC().isEmpty()) {
                            HashMap<String, Object> param = new HashMap<>();
                            param.put("rgwMAC", createEquipmentRequest.getRgwMAC());
                            param.put("requestedRgwSerial", String.valueOf(createEquipmentRequest.getRgwSerial()));
                            List<Equipment> equipFromMAC = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_BY_MACADDRESS, param);
                            if (Objects.nonNull(equipFromMAC) && !equipFromMAC.isEmpty())
                                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "MAC address already exists");
                        }
                    }

                    HashMap<String, Object> param = new HashMap<>();
                    param.put("equipmentId", equipment.getId());

                    dataAccessService.deleteUpdateNative(DELETE_EQUIPMENT_FROM_CLUSTER_BY_EQUIPMENT_ID, param);

                    if (Objects.nonNull(equipmentList.get(0).getSubscriber()) && Objects.nonNull(equipmentList.get(0).getSubscriber().getId()) && !equipmentList.get(0).getSubscriber().getId().isEmpty()) {
                        dataAccessService.update(Equipment.class, UPDATE_SN_MAC_OF_EQUIPMENT, param);

                        equipment.setRgwSerial(null);
                        equipment.setRgwMAC(null);
                        esService.updateById(EQUIPMENT_INDEX, prepareDataForES2(equipment, equipment.getSubscriber()), equipment.getId(), null);

                        BasicDBObject mongoFieldOptions = new BasicDBObject();
                        mongoFieldOptions.put("_id", 0);
                        HashMap<String, String> queryParams = new HashMap<>();
                        queryParams.put("userId", createEquipmentRequest.getRgwSerial());
                        queryParams.put("serialNumber", createEquipmentRequest.getRgwSerial());
                        DBObject aPDetails = mongoServiceImpl.findOne(queryParams, new HashMap<>(), AP_DETAIL, "serialNumber", DESC, mongoFieldOptions);
                        if (Objects.nonNull(aPDetails.get("type")) && GATEWAY.equals(String.valueOf(aPDetails.get("type")))) {
                            queryParams.clear();
                            queryParams.put("userId", createEquipmentRequest.getRgwSerial());
                            String[] rgwCleanUp = ActiontecConstants.cleanupEquipment.split(",");
                            cleanUpData(queryParams, rgwCleanUp);
                        }

                        BasicDBObject query = new BasicDBObject();
                        query.put("userId", createEquipmentRequest.getRgwSerial());
                        query.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());

                        DBObject updateQuery = new BasicDBObject().append("$set", new BasicDBObject().append("serviceTelephoneNo", createEquipmentRequest.getServiceTelephoneNo()));

                        mongoServiceImpl.update(query, updateQuery, false, false, FSECURE_CACHE);
                        mongoServiceImpl.update(query, updateQuery, false, false, SERVICE_DETAIL);
                    } else {
                        dataAccessService.delete(Equipment.class, equipment.getId());
                        esService.delete(EQUIPMENT_INDEX, equipment.getId());
                    }
                    createEquipmentInMongo = false;

                    String rgwSerial = createEquipmentRequest.getRgwSerial();
                    BasicDBObject query = new BasicDBObject();
                    query.put("userId", rgwSerial);
                    query.put("serviceTelephoneNo", new BasicDBObject("$exists", true));
                    DBObject dbObject = mongoServiceImpl.findOne(SERVICE_DETAIL, query);
                    if (Objects.nonNull(dbObject)) {
                        if (Objects.nonNull(dbObject.get("enabled")) && (Boolean.valueOf(dbObject.get("enabled").toString()))) {
                            fsecureAysnService.deleteRegistrationKey(rgwSerial, ExecutionContext.get());
                            mongoServiceImpl.deleteOne(query, SERVICE_DETAIL);
                        }
                    }
                }
            }

            String ispId = getIspIdByName(createEquipmentRequest.getIsp());
            Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, createEquipmentRequest.getSubscriberId());
            if (Objects.isNull(subscriber)) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Subscriber doesn't exists");
            }

            if (!ispId.equals(subscriber.getCompartment().iterator().next().getIspId()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment and Subscriber isp id mismatch");


            String groupId = getGroupIdByIsp(createEquipmentRequest.getIsp());
            Equipment equipment = new Equipment();
            equipment.setId(CommonUtils.generateUUID());
            equipment.setRgwSerial(Objects.nonNull(createEquipmentRequest.getRgwSerial()) && !createEquipmentRequest.getRgwSerial().isEmpty() ? createEquipmentRequest.getRgwSerial() : null);
            equipment.setRgwMAC(Objects.nonNull(createEquipmentRequest.getRgwMAC()) && !createEquipmentRequest.getRgwMAC().isEmpty() ? createEquipmentRequest.getRgwMAC() : null);
            equipment.setFriendlyName(createEquipmentRequest.getFriendlyName());
            equipment.setServiceTelephoneNo(Objects.nonNull(createEquipmentRequest.getServiceTelephoneNo()) && !createEquipmentRequest.getServiceTelephoneNo().isEmpty() ? createEquipmentRequest.getServiceTelephoneNo() : createEquipmentRequest.getRgwSerial());
            equipment.setGroupId(groupId);
            equipment.setDownLinkRate(createEquipmentRequest.getDownLinkRate());
            equipment.setUpLinkRate(createEquipmentRequest.getUpLinkRate());
            equipment.setSubscriber(subscriber);
            CommonUtils.setCreateEntityFields(equipment);

            dataAccessService.create(Equipment.class, equipment);

            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, groupId);

            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster()).findAny().orElse(null);
            if (Objects.nonNull(clusterInfo)) {
                HashMap<String, Object> param = new HashMap<>();
                param.put("clusterId", clusterInfo.getId());
                param.put("equipmentId", equipment.getId());
                dataAccessService.deleteUpdateNative(MAPPING_OF_CLUSTER_AND_EQUIPMENT, param);
            }


            HashMap<String, Object> queryParam = new HashMap<>();
            queryParam.put("serialNumber", equipment.getRgwSerial());
            queryParam.put("userId", equipment.getRgwSerial());
            DBObject dbObject = mongoServiceImpl.findOne(AP_DETAIL, queryParam);

            if (createEquipmentInMongo && Objects.isNull(dbObject) && Objects.nonNull(createEquipmentRequest.getRgwSerial()) && !createEquipmentRequest.getRgwSerial().isEmpty()) {
                HashMap<String, Object> apDetail = new HashMap<>();
                apDetail.put("userId", equipment.getRgwSerial());
                apDetail.put("serialNumber", equipment.getRgwSerial());
                apDetail.put("macAddress", equipment.getRgwMAC());
                apDetail.put("type", GATEWAY);
                apDetail.put("isp", createEquipmentRequest.getIsp());
                apDetail.put("processed", true);
                apDetail.put("mysqlProcessed", true);
                mongoServiceImpl.create(AP_DETAIL, apDetail);
            }

            BasicDBObject serviceQuery = new BasicDBObject();
            serviceQuery.put("serviceTelephoneNo", createEquipmentRequest.getServiceTelephoneNo());
            DBObject serviceObj = mongoServiceImpl.findOne(SERVICE_DETAIL, serviceQuery);
            if(serviceObj != null) {
                String serial = String.valueOf(serviceObj.get("userId"));
                if(!serial.equals(createEquipmentRequest.getRgwSerial())) {
                    doReplaceServiceSerial(createEquipmentRequest.getRgwSerial(), null);
                    doReplaceServiceSerial(String.valueOf(serviceObj.get("userId")), createEquipmentRequest.getRgwSerial());
                }
            }

            BasicDBObject record;
            DBObject update;
            DBObject query;

            if (elasticSearchEnable) {
                try {
                    HashMap<String, Object> equipmentMap = prepareDataForES2(equipment, subscriber);
                    esService.insert(EQUIPMENT_INDEX, equipmentMap, String.valueOf(equipmentMap.get("id")), null);
                    record = new BasicDBObject();
                    record.put("esProcessed", true);
                    update = new BasicDBObject("$set", record);
                    query = new BasicDBObject("userId", equipment.getRgwSerial());
                    mongoServiceImpl.update(query, update, true, false, ApplicationCommonConstants.AP_DETAIL);
                } catch (Exception e) {
                    LOG.error("Error in updating esProcessed true. AP ID ::" + equipment.getRgwSerial());
                }
            }
            return equipment;
        } catch (Exception e) {
            if (e instanceof PersistenceException)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment already exist");

            throw e;
        }
    }

    public String getGroupIdByIsp(String isp) throws Exception {
        List<Compartment> compartmentList = null;
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.clear();
        queryParams.put("name", isp);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        DBObject dbObject = mongoServiceImpl.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
        if (Objects.nonNull(dbObject)) {
            HashMap<String, String> params = new HashMap<>();
            params.put("ispId", String.valueOf(dbObject.get("id")));
            compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);
            if (Objects.isNull(compartmentList) || compartmentList.isEmpty()) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Group doesn't exist with ISP");
            }

            return compartmentList.get(0).getId();
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP doesn't exist");
        }
    }


    public String getIspIdByName(String isp) throws Exception {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.clear();
        queryParams.put("name", isp);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        DBObject dbObject = mongoServiceImpl.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
        if (Objects.nonNull(dbObject)) {
            return String.valueOf(dbObject.get("id"));
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP doesn't exist");
        }
    }


    private HashMap<String, Object> prepareDataForES2(Equipment equipment, Subscriber subscriber) {
        List<String> clusters = new ArrayList<>();
        HashMap<String, Object> userAPMap = null;
        try {
            Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());
            ClusterInfo clusterInfo = compartment.getCluster().stream().filter(cluster -> cluster.isDefaultCluster() && cluster.getName().equals(DEFAULT + compartment.getName())).findAny().orElse(null);
            if (Objects.nonNull(clusterInfo)) {
                clusters.add(clusterInfo.getId());
            }

            HashMap<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", equipment.getRgwSerial());
            HashMap<String, Object> projection = new HashMap<>();
            projection.put("type", 1);
            projection.put("serialNumber", 1);
            projection.put("macAddress", 1);
            List<HashMap<String, String>> apDetails = mongoServiceImpl.findList(queryParams, new HashMap<>(), ApplicationCommonConstants.AP_DETAIL, projection);
            List<Map<String, Object>> extInfo = new ArrayList<>();
            apDetails.stream().filter(ap -> !GATEWAY.equals(ap.get("type"))).forEach(elem -> {
                Map<String, Object> extender = new HashMap<>();
                extender.put(EXT_MAC, elem.get("macAddress"));
                extender.put(EXT_SERIAL, elem.get("serialNumber"));
                extInfo.add(extender);
            });

            userAPMap = new HashMap<>();
            userAPMap.put("id", equipment.getId());
            userAPMap.put("name", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getFirstName()) ? subscriber.getFirstName() + " " + subscriber.getLastName() : equipment.getRgwSerial() + " " + equipment.getRgwMAC());
            userAPMap.put("createdAt", equipment.getCreatedAt());
            userAPMap.put("createdBy", equipment.getCreatedBy());
            userAPMap.put("email", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getEmail()) ? subscriber.getEmail() : equipment.getRgwSerial() + "@optim.com");
            userAPMap.put("subscriberId", equipment.getSubscriber().getId());
            userAPMap.put("mobileSubscribed", false);
            userAPMap.put("downLinkRate", equipment.getDownLinkRate());
            userAPMap.put("upLinkRate", equipment.getUpLinkRate());
            userAPMap.put("rgwMAC", equipment.getRgwMAC());
            userAPMap.put("apId", equipment.getRgwSerial());
            userAPMap.put("serialNumber", equipment.getRgwSerial());
            userAPMap.put("groupId", equipment.getGroupId());
            userAPMap.put("phoneNo", equipment.getServiceTelephoneNo());
            userAPMap.put("globalAccountNo", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getGlobalAccountNo()) ? subscriber.getGlobalAccountNo() : null);
            userAPMap.put("camsAccountNo", Objects.nonNull(subscriber) && Objects.nonNull(subscriber.getCamsAccountNo()) ? subscriber.getCamsAccountNo() : null);
            userAPMap.put("extInfo", extInfo);
            userAPMap.put("clusters", clusters);
            userAPMap.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
        } catch (Exception e) {
            LOG.error("ERROR in prepare data for ES.");
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }
        return userAPMap;
    }

    public void attachEquipment(AttachEquipmentRequest attachEquipmentRequest) throws Exception {
        try {
            for (String apId : attachEquipmentRequest.getApId()) {
                HashMap<String, Object> param = new HashMap<>();
                param.put("id", apId);
                Equipment equipment = (Equipment) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_SERIAL_NUMBER, param).iterator().next();
                if (Objects.isNull(equipment.getSubscriber())) {
                    param.clear();
                    param.put("subscriberId", attachEquipmentRequest.getSubscriberId());
                    param.put("equipmentId", equipment.getId());

                    dataAccessService.update(Equipment.class, UPDATE_SUBSCRIBER_ID_FOR_EQUIPMENT, param);

                    Subscriber subscriber = (Subscriber) dataAccessService.read(Subscriber.class, attachEquipmentRequest.getSubscriberId());

                    equipment.setSubscriber(subscriber);

                    esService.updateById(EQUIPMENT_INDEX, prepareDataForES2(equipment, null), equipment.getId(), null);
                }
            }
        } catch (Exception e) {
            if (e instanceof ConstraintViolationException) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error while fetching data for equipment");
            } else {
                LOG.error("attachEquipment failed, subscriberId:[{}], apId:[{}]", attachEquipmentRequest.getSubscriberId(), attachEquipmentRequest.getApId(), e);
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment is already attached to another subscriber.");
            }
        }
    }

    public void editEquipment(String equipmentIdOrSerialOrSTN, EditEquipmentRequest editEquipmentRequest) throws Exception {
        if (Objects.nonNull(equipmentIdOrSerialOrSTN) && !equipmentIdOrSerialOrSTN.isEmpty()) {
            if (editEquipmentRequest.getDownLinkRate() != 0.0 && !Pattern.matches(LINK_RATE_PATTERN, String.valueOf(editEquipmentRequest.getDownLinkRate()))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "DL not valid");
            }

            if (editEquipmentRequest.getUpLinkRate() != 0.0 && !Pattern.matches(LINK_RATE_PATTERN, String.valueOf(editEquipmentRequest.getUpLinkRate()))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "UL not valid");
            }

            if(editEquipmentRequest.getRgwSerial() != null) {
                if (!isValidSnString(editEquipmentRequest.getRgwSerial())) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Serial number not valid");
                }
            }

            if(editEquipmentRequest.getServiceTelephoneNo() != null) {
                if (!isValidSnString(editEquipmentRequest.getServiceTelephoneNo())) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Service telephone number not valid");
                }
            }

            boolean isFsecureProvsioningRecovered = false;
            Subscriber subscriber = null;
            Equipment equipment = null;
            Equipment existingEquipment = new Equipment();
            double upLinkRate = editEquipmentRequest.getUpLinkRate();
            double downLinkRate = editEquipmentRequest.getDownLinkRate();
            String friendlyName = editEquipmentRequest.getFriendlyName();
            BasicDBObject updateEquipment = new BasicDBObject();
//            String isp = editEquipmentRequest.getIsp();
            String serviceTelephoneNo = editEquipmentRequest.getServiceTelephoneNo();

            HashMap<String, Object> param = new HashMap<>();
            param.put("id", equipmentIdOrSerialOrSTN);
            try {
                equipment = (Equipment) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_SERIAL_NUMBER, param).iterator().next();
                existingEquipment.setRgwSerial(equipment.getRgwSerial());
                existingEquipment.setRgwMAC(equipment.getRgwMAC());
                existingEquipment.setSubscriber(equipment.getSubscriber());
                existingEquipment.setFriendlyName(equipment.getFriendlyName());
                existingEquipment.setUpLinkRate(equipment.getUpLinkRate());
                existingEquipment.setDownLinkRate(equipment.getDownLinkRate());
                existingEquipment.setServiceTelephoneNo(equipment.getServiceTelephoneNo());
                existingEquipment.setGroupId(equipment.getGroupId());
                existingEquipment.setId(equipment.getId());
            } catch (Exception e) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error while fetching data for equipment");
            }

            if (Objects.isNull(existingEquipment.getSubscriber()) || Objects.isNull(existingEquipment.getSubscriber().getId()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Orphan Equipment cannot be updated");

            if (Objects.nonNull(equipment)) {
                /*if (Objects.nonNull(editEquipmentRequest.getServiceTelephoneNo())) {
                    param.clear();
                    param.put("serviceTelephoneNo", editEquipmentRequest.getServiceTelephoneNo());
                    List<Equipment> oldEquipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_STN, param);
                    if (Objects.nonNull(oldEquipmentList) && !oldEquipmentList.isEmpty()) {
                        *//*Equipment oldEquipment = oldEquipmentList.get(0);
                 *//**//*equipment.setUpLinkRate(oldEquipment.getUpLinkRate());
                        equipment.setDownLinkRate(oldEquipment.getDownLinkRate());
                        equipment.setFriendlyName(oldEquipment.getFriendlyName());
                        equipment.setSubscriberId(oldEquipment.getSubscriberId());*//**//*

                        upLinkRate = oldEquipment.getUpLinkRate();
                        downLinkRate = oldEquipment.getDownLinkRate();
                        friendlyName = oldEquipment.getFriendlyName();
                        equipment.setSubscriberId(oldEquipment.getSubscriberId());

                        subscriber = (Subscriber) dataAccessService.read(Subscriber.class, oldEquipment.getSubscriberId());

                        removeOldEquipmentData(oldEquipment);*//*
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "STN already exists");
                    }
                    equipment.setServiceTelephoneNo(serviceTelephoneNo);
                }*/

                if (Objects.nonNull(editEquipmentRequest.getServiceTelephoneNo()) && !existingEquipment.getServiceTelephoneNo().equals(editEquipmentRequest.getServiceTelephoneNo())) {
                    LOG.info("equipment management, seviceTelephoneNumber changed. orig:[{}] new:[{}]",
                            existingEquipment.getServiceTelephoneNo(),
                            editEquipmentRequest.getServiceTelephoneNo());
                    param.clear();
                    param.put("serviceTelephoneNo", serviceTelephoneNo);
                    List<Equipment> oldEquipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_STN, param);
                    if (Objects.nonNull(oldEquipmentList) && !oldEquipmentList.isEmpty())
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "STN already exists");

                    equipment.setServiceTelephoneNo(serviceTelephoneNo);

                    BasicDBObject query = new BasicDBObject();
                    query.put("userId", equipment.getRgwSerial());
                    query.put("serviceTelephoneNo", existingEquipment.getServiceTelephoneNo());

                    DBObject updateQuery = new BasicDBObject().append("$set", new BasicDBObject().append("serviceTelephoneNo", serviceTelephoneNo));
                    mongoServiceImpl.update(query, updateQuery, false, false, FSECURE_CACHE);
                    mongoServiceImpl.update(query, updateQuery, false, false, SERVICE_DETAIL);
                }

                if (editEquipmentRequest.getDownLinkRate() != 0.0) {
                    if (existingEquipment.getDownLinkRate() != editEquipmentRequest.getDownLinkRate())
                        equipment.setDownLinkRate(downLinkRate);
                }

                if (editEquipmentRequest.getUpLinkRate() != 0.0) {
                    if (existingEquipment.getDownLinkRate() != editEquipmentRequest.getDownLinkRate())
                        equipment.setUpLinkRate(upLinkRate);
                }

                if (Objects.nonNull(editEquipmentRequest.getFriendlyName()) && !(Objects.isNull(existingEquipment.getFriendlyName()) ? "" : existingEquipment.getFriendlyName()).equals(editEquipmentRequest.getFriendlyName()))
                    equipment.setFriendlyName(friendlyName);

                if (Objects.nonNull(editEquipmentRequest.getRgwMAC()) && !(Objects.isNull(existingEquipment.getRgwMAC()) ? "" : existingEquipment.getRgwMAC()).equals(editEquipmentRequest.getRgwMAC())) {
                    String rgwMac = null;

                    if (!StringUtils.isBlank(editEquipmentRequest.getRgwMAC())) {
                        if(rgwMacNotKey == false) {
                            param.clear();
                            param.put("rgwMAC", editEquipmentRequest.getRgwMAC());
                            param.put("requestedRgwSerial", String.valueOf(editEquipmentRequest.getRgwSerial()));
                            List<Equipment> oldEquipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_MACADDRESS, param);
                            if (Objects.nonNull(oldEquipmentList) && !oldEquipmentList.isEmpty()) {
                                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "MAC Address already exists");
                            }
                        }
                        rgwMac = editEquipmentRequest.getRgwMAC();
                    }

                    updateEquipment.put("macAddress", rgwMac);
                    equipment.setRgwMAC(rgwMac);
                }

                if (Objects.nonNull(editEquipmentRequest.getRgwSerial()) && !((Objects.isNull(existingEquipment.getRgwSerial()) ? "" : existingEquipment.getRgwSerial())).equals(editEquipmentRequest.getRgwSerial())) {
                    LOG.info("equipment management, serialNumber changed. orig:[{}] new:[{}]",
                            existingEquipment.getRgwSerial(),
                            editEquipmentRequest.getRgwSerial());

                    String rgwSerial = null;
                    if (!StringUtils.isBlank(editEquipmentRequest.getRgwSerial())) {
                        rgwSerial = editEquipmentRequest.getRgwSerial();

                        doReplaceServiceSerial(editEquipmentRequest.getRgwSerial(), null);
                        doReplaceServiceSerial(existingEquipment.getRgwSerial(), editEquipmentRequest.getRgwSerial());

                        HashMap<String, String> params = new HashMap<>();
                        params.put("rgwSerial", String.valueOf(editEquipmentRequest.getRgwSerial()));
                        List<Equipment> equipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, EquipmentSQL.GET_EQUIPMENT_LIST_BY_RGW_SERIAL, params);

                        if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty()) {
                            Equipment existingEquip = equipmentList.get(0);
                            HashMap<String, Object> queryParam = new HashMap<>();
                            queryParam.put("equipmentId", existingEquip.getId());

                            dataAccessService.deleteUpdateNative(DELETE_EQUIPMENT_FROM_CLUSTER_BY_EQUIPMENT_ID, queryParam);

                            if (Objects.nonNull(equipmentList.get(0).getSubscriber()) && Objects.nonNull(equipmentList.get(0).getSubscriber().getId()) && !equipmentList.get(0).getSubscriber().getId().isEmpty()) {
                                dataAccessService.update(Equipment.class, UPDATE_SN_MAC_OF_EQUIPMENT, queryParam);

                                existingEquip.setRgwSerial(null);
                                existingEquip.setRgwMAC(null);
                                esService.updateById(EQUIPMENT_INDEX, prepareDataForES2(existingEquip, existingEquipment.getSubscriber()), existingEquip.getId(), null);

                                BasicDBObject mongoFieldOptions = new BasicDBObject();
                                mongoFieldOptions.put("_id", 0);
                                HashMap<String, String> queryParams = new HashMap<>();
                                queryParams.put("userId", editEquipmentRequest.getRgwSerial());
                                queryParams.put("serialNumber", editEquipmentRequest.getRgwSerial());
                                DBObject aPDetails = mongoServiceImpl.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
                                if (Objects.nonNull(aPDetails.get("type")) && GATEWAY.equals(String.valueOf(aPDetails.get("type")))) {
                                    queryParams.clear();
                                    queryParams.put("userId", editEquipmentRequest.getRgwSerial());
                                    String[] rgwCleanUp = ActiontecConstants.cleanupEquipment.split(",");
                                    cleanUpData(queryParams, rgwCleanUp);
                                }
                            } else {
                                dataAccessService.delete(Equipment.class, existingEquip.getId());
                                esService.delete(EQUIPMENT_INDEX, existingEquip.getId());
                            }
                        } else {
                            updateEquipment.put("serialNumber", rgwSerial);
                        }
                    }
                    equipment.setRgwSerial(rgwSerial);
                    isFsecureProvsioningRecovered = true;

                    mongoTemplate.getCollection(AP_DETAIL).remove(new BasicDBObject().append("userId", existingEquipment.getRgwSerial()));
                }
            }

            CommonUtils.setUpdateEntityFields(equipment);

            try {
                dataAccessService.update(Equipment.class, equipment);

                if (isFsecureProvsioningRecovered) {

                    BasicDBObject query = new BasicDBObject();
                    query.put("userId", editEquipmentRequest.getRgwSerial());
                    query.put("serviceTelephoneNo", new BasicDBObject("$exists", true));

                    LOG.info("Update userId: " + editEquipmentRequest.getRgwSerial());
                    BasicDBObject dataToUpdate = new BasicDBObject();
                    dataToUpdate.put("fsecureForce", true);
                    dataToUpdate.put("transitionId", "");
                    dataToUpdate.put("state", 0);
                    DBObject updateQuery = new BasicDBObject().append("$set", dataToUpdate);
                    mongoServiceImpl.update(query, updateQuery, false, false, SERVICE_DETAIL);
                }
            } catch (Exception e) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in updating equipment.");
            }

                /*if (Objects.nonNull(editEquipmentRequest.getIsp()) && !editEquipmentRequest.getIsp().isEmpty()) {
                    DBObject query = new BasicDBObject().append("rgwSerial", editEquipmentRequest.getRgwSerial());
                    BasicDBObject updateEquipment = new BasicDBObject();
                    updateEquipment.put("isp", isp);

                    DBObject update = new BasicDBObject().append("$set", updateEquipment);

                    mongoServiceImpl.update(query, update, false, false, AP_DETAIL);
                }*/

            if (!updateEquipment.isEmpty()) {
                if (updateEquipment.containsKey("serialNumber")) {
                    HashMap<String, Object> queryParam = new HashMap<>();
                    queryParam.put("serialNumber", equipment.getRgwSerial());
                    queryParam.put("userId", equipment.getRgwSerial());
                    DBObject obj = mongoServiceImpl.findOne(AP_DETAIL, queryParam);

                    if (Objects.isNull(obj)) {
                        String isp = manageCommonService.getIspByGroupId(existingEquipment.getGroupId());
                        HashMap<String, Object> apDetail = new HashMap<>();
                        apDetail.put("userId", equipment.getRgwSerial());
                        apDetail.put("serialNumber", equipment.getRgwSerial());
                        apDetail.put("macAddress", equipment.getRgwMAC());
                        apDetail.put("type", GATEWAY);
                        apDetail.put("isp", isp);
                        apDetail.put("processed", true);
                        apDetail.put("mysqlProcessed", true);
                        apDetail.put("esProcessed", true);
                        mongoServiceImpl.create(AP_DETAIL, apDetail);
                    }
                } else {
                    DBObject query = new BasicDBObject();
                    query.put("rgwSerial", editEquipmentRequest.getRgwSerial());
                    query.put("userId", editEquipmentRequest.getRgwSerial());

                    DBObject update = new BasicDBObject().append("$set", updateEquipment);

                    mongoServiceImpl.update(query, update, false, false, AP_DETAIL);
                }
            }

            // for smm app
            refreshSmmAppOwner(existingEquipment.getServiceTelephoneNo(), existingEquipment.getRgwSerial(), editEquipmentRequest.getServiceTelephoneNo(), editEquipmentRequest.getRgwSerial());


            if (Objects.nonNull(equipment.getSubscriber()) && Objects.nonNull(equipment.getSubscriber().getId()))
                subscriber = (Subscriber) dataAccessService.read(Subscriber.class, equipment.getSubscriber().getId());

            esService.updateById(EQUIPMENT_INDEX, prepareDataForES2(equipment, subscriber), equipment.getId(), null);
        } else
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment doesn't exists");
    }

    public void refreshSmmAppOwner(String origStn, String origSerial, String newStn, String newSerial) {
        LOG.info("refreshSmmAppOwner origStn:[{}] origSerial:[{}] newStn:[{}] newSerial:[{}]", origStn, origSerial, newStn, newSerial);
        if (StringUtils.isNotBlank(newStn) && !StringUtils.equals(origStn, newStn)) {
            List<WriteModel> writeModels = new ArrayList<>();
            writeModels.add(new UpdateManyModel(
                    new Document("serviceTelephoneNumber", origStn),
                    new Document("$set", new Document("serviceTelephoneNumber", newStn)),
                    new UpdateOptions().upsert(false)
            ));

            if (!writeModels.isEmpty()) {
                mongoClientHolder.getCollection("cpeSmmApp").bulkWrite(writeModels, new BulkWriteOptions().ordered(false));
            }
        }

        if (StringUtils.isNotBlank(newSerial) && !StringUtils.equals(origSerial, newSerial)) {
            String targetStn = newStn;
            if (StringUtils.isBlank(targetStn)) {
                targetStn = origStn;
            }

            List<WriteModel> writeModels = new ArrayList<>();

            Document smmQuery = new Document();
            smmQuery.put("userId", newSerial);
            smmQuery.put("serviceTelephoneNumber", new BasicDBObject().append("$exists", true));
            MongoCursor<Document> mongoCursor = mongoClientHolder.getCollection("cpeSmmApp").find(smmQuery).iterator();
            while (mongoCursor.hasNext()) {
                Document document = mongoCursor.next();
                if (!StringUtils.equals(document.getString("serviceTelephoneNumber"), targetStn)) {
                    Document filter = new Document("_id", document.getObjectId("_id"));
                    writeModels.add(new DeleteOneModel<>(filter));

                    document.remove("_id");
                    if (StringUtils.equals(document.getString("userId"), document.getString("serialNumber"))) {
                        document.put("serialNumber", "");
                    }
                    document.put("userId", "");
                    writeModels.add(new InsertOneModel(document));
                }
            }

            smmQuery.clear();
            smmQuery.put("serviceTelephoneNumber", targetStn);
            mongoCursor = mongoClientHolder.getCollection("cpeSmmApp").find(smmQuery).iterator();
            while(mongoCursor.hasNext()) {
                Document document = mongoCursor.next();
                Document filter = new Document("_id", document.getObjectId("_id"));
                writeModels.add(new DeleteOneModel<>(filter));

                document.remove("_id");
                if (StringUtils.equals(document.getString("userId"), document.getString("serialNumber"))) {
                    document.put("serialNumber", newSerial);
                }
                document.put("userId", newSerial);
                writeModels.add(new InsertOneModel(document));
            }

            // TBD: check the result of bulkWrite ?
            if (!writeModels.isEmpty()) {
                mongoClientHolder.getCollection("cpeSmmApp").bulkWrite(writeModels, new BulkWriteOptions().ordered(true));
            }
        }

        Document smmQuery = new Document();
        smmQuery.put("userId", newSerial);
        smmQuery.put("serviceTelephoneNumber", new BasicDBObject().append("$exists", false));
        mongoClientHolder.getCollection("cpeSmmApp").deleteMany(smmQuery);

        smmQuery.clear();
        smmQuery.put("userId", origSerial);
        smmQuery.put("serviceTelephoneNumber", new BasicDBObject().append("$exists", false));
        mongoClientHolder.getCollection("cpeSmmApp").deleteMany(smmQuery);
    }

    @Transactional
    private void removeOldEquipmentData(Equipment oldEquipment) throws Exception {
        try {
            HashMap<String, Object> param = new HashMap<>();
            param.put("equipmentId", oldEquipment.getId());
            dataAccessService.deleteUpdateNative(DELETE_EQUIPMENT_FROM_CLUSTER_BY_EQUIPMENT_ID, param);
            dataAccessService.delete(Equipment.class, oldEquipment.getId());

            if (Objects.nonNull(oldEquipment.getRgwSerial()) && !oldEquipment.getRgwSerial().isEmpty()) {
                String oldSerial = manageCommonService.getGatewaySerialByUserId(oldEquipment.getRgwSerial()).orElseGet(()->oldEquipment.getRgwSerial());

                BasicDBObject mongoFieldOptions = new BasicDBObject();
                mongoFieldOptions.clear();
                mongoFieldOptions.put("_id", 0);
                HashMap<String, String> queryParams = new HashMap<>();
                queryParams.put("userId", oldEquipment.getRgwSerial());
                queryParams.put("serialNumber", oldSerial);
                DBObject aPDetails = mongoServiceImpl.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
                if (Objects.nonNull(aPDetails) && Objects.nonNull(aPDetails.get("type")) && GATEWAY.equals(String.valueOf(aPDetails.get("type")))) {
                    queryParams.clear();
                    queryParams.put("userId", oldEquipment.getRgwSerial());
                    String[] rgwCleanUp = ActiontecConstants.cleanupRGW.split(",");
                    cleanUpData(queryParams, rgwCleanUp);
                }
            }

            // for smm app
            BasicDBObject smmQuery = new BasicDBObject();
            smmQuery.put("serviceTelephoneNumber", oldEquipment.getServiceTelephoneNo());
            mongoTemplate.getCollection("cpeSmmApp").remove(smmQuery);

            smmQuery.clear();
            smmQuery.put("userId", oldEquipment.getRgwSerial());
            mongoTemplate.getCollection("cpeSmmApp").remove(smmQuery);

            esService.delete(EQUIPMENT_INDEX, oldEquipment.getId());
        } catch (Exception e) {
            LOG.info("---- Exception Occur in deleting old equipment ----" + e);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Old equipment cannot be delete " + e);
        }
    }

    public void cleanUpData(HashMap<String, String> queryParams, String[] collectionList) {
        Runnable runnable = () -> {
            try {
                for (String collectionName : collectionList) {
                    mongoServiceImpl.deleteOne(queryParams, collectionName);
                }
            } catch (Exception e) {
                LOG.error("Error during perform delete operation" + e);
            }
        };
        new Thread(runnable).start();
    }

    public void detachEquipment(String equipmentIdOrSerialOrSTN) throws Exception {
        HashMap<String, Object> param = new HashMap<>();
        param.put("id", equipmentIdOrSerialOrSTN);
        List<Equipment> oldEquipmentList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_BY_SERIAL_NUMBER, param);
        if (Objects.nonNull(oldEquipmentList) && !oldEquipmentList.isEmpty()) {
            Equipment equipment = oldEquipmentList.get(0);

            param.clear();
            param.put("equipmentId", equipment.getId());
            param.put("serviceTelephoneNo", equipment.getRgwSerial());

            dataAccessService.update(Equipment.class, REMOVE_SUBSCRIBER_ID_FROM_EQUIPMENT, param);

            equipment.setSubscriber(null);

            esService.updateById(EQUIPMENT_INDEX, prepareDataForES2(equipment, null), equipment.getId(), null);

            //todo equipment data cleanup in case of offline

        } else
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Equipment doesn't exists");
    }

    public List<HashMap<String, Object>> getAllEquipments(String subscriberId) throws Exception {
        List<HashMap<String, Object>> listOfSubscriber = new ArrayList<>();
        List<Equipment> equipmentIdList = getAllEquipment(subscriberId);
        for (Equipment equipment : equipmentIdList) {
            HashMap<String, Object> subscriberModel = new HashMap<>();
            subscriberModel.put("serialNumber", equipment.getRgwSerial());
            subscriberModel.put("macAddress", equipment.getRgwMAC());
            subscriberModel.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
            subscriberModel.put("equipmentId", equipment.getId());

            listOfSubscriber.add(subscriberModel);
        }
        return listOfSubscriber;
    }

    public List<Equipment> getAllEquipment(String subscriberId) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("subscriberId", subscriberId);
        List<Equipment> equipmentIdList = (List<Equipment>) dataAccessService.read(Equipment.class, GET_EQUIPMENT_FOR_SUBSCRIBER, params);
        return equipmentIdList;
    }

    private boolean isIllegalCharacterExist(TechnicianDashboardDTO technicianDashboardDTO) {
        if (Objects.nonNull(technicianDashboardDTO.getName()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getName()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getRgwMAC()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getRgwMAC()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getSerialNumber()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getSerialNumber()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getGlobalAccountNo()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getCamsAccountNo()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getCamsAccountNo()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getEmail()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getEmail()))
            return true;
        else if (Objects.nonNull(technicianDashboardDTO.getPhoneNo()) && !ValidationUtil.isValidSearchingCharacter(technicianDashboardDTO.getPhoneNo()))
            return true;
        else
            return false;
    }

    private boolean isSearchEnabled(TechnicianDashboardDTO technicianDashboardDTO) {
        return Objects.nonNull(technicianDashboardDTO.getSerialNumber()) || Objects.nonNull(technicianDashboardDTO.getServiceTelephoneNo()) || Objects.nonNull(technicianDashboardDTO.getName()) || Objects.nonNull(technicianDashboardDTO.getEmail()) || Objects.nonNull(technicianDashboardDTO.getRgwMAC()) || Objects.nonNull(technicianDashboardDTO.getPhoneNo()) || Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo()) || Objects.nonNull(technicianDashboardDTO.getCamsAccountNo());
    }

    private StringBuffer getSearchQuery(TechnicianDashboardDTO technicianDashboardDTO) {
        ArrayList<String> queryParams = new ArrayList<>();

        StringBuffer sb = new StringBuffer(ApplicationConstants.EMPTY_STRING);

        if (Objects.nonNull(technicianDashboardDTO.getName())) {
            queryParams.add("CONCAT( subs.firstName, ' ', subs.lastName ) LIKE '%" + technicianDashboardDTO.getName() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getEmail())) {
            queryParams.add("subs.email LIKE '%" + technicianDashboardDTO.getEmail() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getSerialNumber())) {
            queryParams.add("eq.rgwSerial LIKE '%" + technicianDashboardDTO.getSerialNumber() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getPhoneNo())) {
            queryParams.add("eq.serviceTelephoneNo LIKE '%" + technicianDashboardDTO.getPhoneNo() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo())) {
            queryParams.add("subs.globalAccountNo LIKE '%" + technicianDashboardDTO.getGlobalAccountNo() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getCamsAccountNo())) {
            queryParams.add("subs.camsAccountNo LIKE '%" + technicianDashboardDTO.getCamsAccountNo() + "%'");
        }

        if (Objects.nonNull(technicianDashboardDTO.getRgwMAC())) {
            queryParams.add("eq.rgwMAC LIKE '%" + technicianDashboardDTO.getRgwMAC() + "%'");
        }

        if (!queryParams.isEmpty()) {
            sb.append("Where ");
            int i = 0;
            for (String condition : queryParams) {
                if (i > 0)
                    sb.append(" and ");
                sb.append(condition);
                i++;
            }
        }

        return sb;
    }

    private String isNullString(String data) {
        return data.equals("null") ? null : data;
    }

    public Object getEquipmentFromES(TechnicianDashboardDTO technicianDashboardDTO, List<String> dataSecurityMapping, ClusterInfo clusterInfo, Boolean exact) {
        HashMap<String, Object> response = new HashMap<>();
        List<HashMap<String, Object>> listOfSubscriber = new ArrayList<>();
        ElasticSearchDTO esResponse = null;
        if (isSearchEnabled(technicianDashboardDTO)) {
            ESRequest elasticSearchRequest = new ESRequest();
            elasticSearchRequest.setMax(technicianDashboardDTO.getMax().intValue()).setOffset(technicianDashboardDTO.getOffset().intValue()).setSortBy(technicianDashboardDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
            HashMap<String, Object> esQueryParams = new HashMap<>();
            esQueryParams.put("name", technicianDashboardDTO.getName());
            esQueryParams.put("email", technicianDashboardDTO.getEmail());
            esQueryParams.put("serviceTelephoneNo", Objects.nonNull(technicianDashboardDTO.getServiceTelephoneNo()) ? dataSecurityMapping.contains(DataSecurityType.phoneNumber.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getServiceTelephoneNo() : technicianDashboardDTO.getServiceTelephoneNo());
            esQueryParams.put("globalAccountNo", Objects.nonNull(technicianDashboardDTO.getGlobalAccountNo()) ? dataSecurityMapping.contains(DataSecurityType.globalAccount.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getGlobalAccountNo() : technicianDashboardDTO.getGlobalAccountNo());
            esQueryParams.put("camsAccountNo", Objects.nonNull(technicianDashboardDTO.getCamsAccountNo()) ? dataSecurityMapping.contains(DataSecurityType.camsAccount.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getCamsAccountNo() : technicianDashboardDTO.getCamsAccountNo());
            esQueryParams.put("rgwMAC", Objects.nonNull(technicianDashboardDTO.getRgwMAC()) ? dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : technicianDashboardDTO.getRgwMAC() : technicianDashboardDTO.getRgwMAC());
            esQueryParams.put("apId", technicianDashboardDTO.getSerialNumber());

            if (!CommonUtils.isSysAdmin()) {
                esQueryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
            }
            elasticSearchRequest.setQueryParams(esQueryParams);
            elasticSearchRequest.setShouldParams(null);
            elasticSearchRequest.setMustNotExistValue(null);
            if (Objects.nonNull(clusterInfo) && Objects.nonNull(technicianDashboardDTO.getClusterId())) {
                esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", (Objects.nonNull(technicianDashboardDTO.getClusterId()) ? technicianDashboardDTO.getClusterId() : clusterInfo.getId()), exact);
            } else {
                esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", null, exact);
            }

        } else {
            if (technicianDashboardDTO.getClusterId() != null) {
                ESRequest elasticSearchRequest = new ESRequest();
                elasticSearchRequest.setMax(technicianDashboardDTO.getMax()).setOffset(technicianDashboardDTO.getOffset()).setSortBy(technicianDashboardDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
                if (!technicianDashboardDTO.getClusterId().equals("0")) {
                    esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", technicianDashboardDTO.getClusterId(), exact);
                } else {
                    esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", null, exact);
                }
            } else {
                SortOrder sortOrder = technicianDashboardDTO.getOrder().name().equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
                ESRequest elasticSearchRequest = new ESRequest();
                elasticSearchRequest.setMax(technicianDashboardDTO.getMax()).setOffset(technicianDashboardDTO.getOffset()).setOrder(sortOrder).setSortBy(technicianDashboardDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
                if (!CommonUtils.isSysAdmin()) {
                    HashMap<String, Object> queryParams = new HashMap<>();
                    queryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
                    elasticSearchRequest.setQueryParams(queryParams);
                    elasticSearchRequest.setShouldParams(null);
                    elasticSearchRequest.setMustNotExistValue(null);
                }
                esResponse = esService.getPaginatedData(elasticSearchRequest);
            }
        }

        if (Objects.nonNull(esResponse) && esResponse.getObjects().size() > 0) {
            esResponse.getObjects().forEach(subscriber -> {
                HashMap<String, Object> subscriberModel = new HashMap<>();
                subscriberModel.put("serialNumber", subscriber.get("serialNumber"));
                subscriberModel.put("rgwMAC", subscriber.get("rgwMAC"));
                subscriberModel.put("name", subscriber.get("name"));
                subscriberModel.put("email", subscriber.get("email"));
                subscriberModel.put("globalAccountNo", subscriber.get("globalAccountNo"));
                subscriberModel.put("camsAccountNo", subscriber.get("camsAccountNo"));
                subscriberModel.put("downLinkRate", subscriber.get("downLinkRate"));
                subscriberModel.put("upLinkRate", subscriber.get("upLinkRate"));
                subscriberModel.put("serviceTelephoneNo", subscriber.get("serviceTelephoneNo"));
                subscriberModel.put("equipmentId", subscriber.get("id"));

                listOfSubscriber.add(subscriberModel);
            });
        }
        response.put("objects", listOfSubscriber);
        response.put("totalCount", Objects.isNull(esResponse) ? 0 : esResponse.getTotalCount());
        return response;
    }

    public Object getEquipmentFromMysql(TechnicianDashboardDTO technicianDashboardDTO, List<String> dataSecurityMapping, ClusterInfo clusterInfo) throws Exception {
        long count = 0;
        String countQuery;

        List<Object[]> equipmentList;

        String limitSql = " LIMIT " + (Objects.nonNull(technicianDashboardDTO.getOffset()) ? technicianDashboardDTO.getOffset() : 0) + COMMA + (Objects.nonNull(technicianDashboardDTO.getMax()) ? technicianDashboardDTO.getMax() : 10);
        if (isSearchEnabled(technicianDashboardDTO)) {
            StringBuffer sb = getSearchQuery(technicianDashboardDTO);

            if (Objects.nonNull(clusterInfo) || Objects.nonNull(technicianDashboardDTO.getClusterId())) {
                String equipmentByClusterSql = GET_EQUIPMENT_BY_CLUSTER + sb.toString() + " AND cluster.cluster_id='" + (Objects.nonNull(technicianDashboardDTO.getClusterId()) ? technicianDashboardDTO.getClusterId() : clusterInfo.getId()) + "' " + limitSql;
                countQuery = GET_EQUIPMENT_COUNT_BY_CLUSTER + sb.toString() + " AND cluster.cluster_id='" + (Objects.nonNull(technicianDashboardDTO.getClusterId()) ? technicianDashboardDTO.getClusterId() : clusterInfo.getId()) + "' ";

                equipmentList = dataAccessService.readNative(equipmentByClusterSql, new HashMap<>());
            } else {
                String equipmentSql;
                if (!CommonUtils.isSysAdmin()) {
                    equipmentSql = GET_EQUIPMENT_LIST + sb.toString() + " AND eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'" + limitSql;
                    countQuery = GET_EQUIPMENT_COUNT_SQL + sb.toString() + " AND eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'";
                } else {
                    equipmentSql = GET_EQUIPMENT_LIST + sb.toString() + limitSql;
                    countQuery = GET_EQUIPMENT_COUNT_SQL + sb.toString();
                }

                equipmentList = dataAccessService.readNative(equipmentSql, new HashMap<>());
            }
        } else {
            if (Objects.nonNull(clusterInfo) || Objects.nonNull(technicianDashboardDTO.getClusterId())) {
                String equipmentByClusterSql = GET_EQUIPMENT_BY_CLUSTER + " Where cluster.cluster_id='" + (Objects.nonNull(technicianDashboardDTO.getClusterId()) ? technicianDashboardDTO.getClusterId() : clusterInfo.getId()) + "' " + limitSql;
                countQuery = GET_EQUIPMENT_COUNT_BY_CLUSTER + " Where cluster.cluster_id='" + (Objects.nonNull(technicianDashboardDTO.getClusterId()) ? technicianDashboardDTO.getClusterId() : clusterInfo.getId()) + "' ";
                equipmentList = dataAccessService.readNative(equipmentByClusterSql, new HashMap<>());
            } else {
                String equipmentSql;
                if (!CommonUtils.isSysAdmin()) {
                    equipmentSql = GET_EQUIPMENT_LIST + " Where eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'" + limitSql;
                    countQuery = GET_EQUIPMENT_COUNT_SQL + " Where eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'";
                } else {
                    equipmentSql = GET_EQUIPMENT_LIST + limitSql;
                    countQuery = GET_EQUIPMENT_COUNT_SQL;
                }
                equipmentList = dataAccessService.readNative(equipmentSql, new HashMap<>());
            }
        }

        List<Object> totalEquipment = (List<Object>) dataAccessService.readNative(countQuery, new HashMap<>());
        count = Long.valueOf(Objects.nonNull(totalEquipment) ? totalEquipment.get(0).toString() : "0");

        SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
        SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();

        ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();

        if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty()) {
            for (Object subscriberDetail[] : equipmentList) {
                SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();

                subscriberData.setEquipmentId(isNullString(String.valueOf(subscriberDetail[0])));
                subscriberData.setSerialNumber(isNullString(String.valueOf(subscriberDetail[1])));
                subscriberData.setRgwMAC(isNullString(String.valueOf(subscriberDetail[2])));
                subscriberData.setPhoneNo(isNullString(String.valueOf(subscriberDetail[3])));
                subscriberData.setUpLinkRate(Double.valueOf(String.valueOf(subscriberDetail[4])));
                subscriberData.setDownLinkRate(Double.valueOf(String.valueOf(subscriberDetail[5])));
                if (Objects.isNull(isNullString(String.valueOf(subscriberDetail[6]))) && Objects.isNull(isNullString(String.valueOf(subscriberDetail[7])))) {
                    subscriberData.setName(null);
                } else if (Objects.nonNull(isNullString(String.valueOf(subscriberDetail[6]))) || Objects.isNull(isNullString(String.valueOf(subscriberDetail[7])))) {
                    subscriberData.setName(isNullString(String.valueOf(subscriberDetail[6])));
                } else if (Objects.nonNull(isNullString(String.valueOf(subscriberDetail[7]))) || Objects.isNull(isNullString(String.valueOf(subscriberDetail[6])))) {
                    subscriberData.setName(isNullString(String.valueOf(subscriberDetail[7])));
                } else {
                    subscriberData.setName(isNullString(String.valueOf(subscriberDetail[6])) + ApplicationConstants.SPACE + isNullString(String.valueOf(subscriberDetail[7])));
                }

                subscriberData.setCamsAccountNo(isNullString(String.valueOf(subscriberDetail[8])));
                subscriberData.setGlobalAccountNo(isNullString(String.valueOf(subscriberDetail[9])));
                subscriberData.setEmail(isNullString(String.valueOf(subscriberDetail[10])));

                subscriberDataList.add(subscriberData);
            }
        }

        data.setObjects(subscriberDataList);
        data.setTotalCount(count);
        subscriberListDTO.setData(data);

        return subscriberListDTO.getData();
    }

    public Object getAllEquipmentListByPagination(TechnicianDashboardDTO technicianDashboardDTO, Boolean exact) throws Exception {
        if (technicianDashboardDTO.getMax() > 500)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Max cannot be greater than 500");

        if (technicianDashboardDTO.getOrder().name().equals("ASC") || technicianDashboardDTO.getOrder().name().equals("DESC")) {
            if (technicianDashboardDTO.getOffset() < 0)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "offset cannot be negative");

            SubscriberListDTO subscriberListDTO = new SubscriberListDTO();
            SubscriberListDTO.SubscriberData data = subscriberListDTO.new SubscriberData();
            ArrayList<SubscriberListDTO.SubscriberData.SubscriberModel> subscriberDataList = new ArrayList<>();
            SubscriberListDTO.SubscriberData.SubscriberModel subscriberData = data.new SubscriberModel();

            if (isIllegalCharacterExist(technicianDashboardDTO)) {
                subscriberDataList.add(subscriberData);
                data.setObjects(subscriberDataList);
                data.setTotalCount(0L);
                subscriberListDTO.setData(data);

                return subscriberListDTO.getData();
            } else {
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
                ClusterInfo clusterInfo = null;
                if (Objects.nonNull(technicianDashboardDTO.getClusterId()) && !technicianDashboardDTO.getClusterId().equals("0")) {
                    clusterInfo = (ClusterInfo) dataAccessService.read(ClusterInfo.class, technicianDashboardDTO.getClusterId());
                } else if (!CommonUtils.isSysAdmin()) {
                    clusterInfo = compartment.getCluster().stream().filter(q -> q.isDefaultCluster()).findAny().orElse(null);
                }

                List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

                if (elasticSearchEnable) {
                    return getEquipmentFromES(technicianDashboardDTO, dataSecurityMapping, clusterInfo, exact);
                } else {
                    return getEquipmentFromMysql(technicianDashboardDTO, dataSecurityMapping, clusterInfo);
                }
            }
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Allowed values for order - ASC,DESC");
        }
    }

    public void deleteEquipmentById(String equipmentIdOrSerialOrSTN) throws Exception {
        deleteEquipment(equipmentIdOrSerialOrSTN);
    }

    @Transactional
    private void deleteEquipment(String equipmentId) throws Exception {
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentId);
        removeOldEquipmentData(equipment);
    }

    public Object getEquipmentById(String equipmentId) throws Exception {
        HashMap<String, Object> response = new HashMap<>();
        Equipment equipment = null;
        Subscriber subscriber = null;
        equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentId);
        if (Objects.nonNull(equipment.getSubscriber()) && Objects.nonNull(equipment.getSubscriber().getId()) && !equipment.getSubscriber().getId().isEmpty())
            subscriber = (Subscriber) dataAccessService.read(Subscriber.class, equipment.getSubscriber().getId());

        Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, equipment.getGroupId());

        response.put("rgwSerial", equipment.getRgwSerial());
        response.put("rgwMAC", equipment.getRgwMAC());
        response.put("isp", ispService.getISPById(compartment.getIspId()).getData().getName());
        response.put("friendlyName", equipment.getFriendlyName());
        response.put("serviceTelephoneNo", equipment.getServiceTelephoneNo());
        response.put("downLinkRate", equipment.getDownLinkRate());
        response.put("upLinkRate", equipment.getUpLinkRate());
        response.put("globalAccountNo", Objects.nonNull(subscriber)  &&  Objects.nonNull(subscriber.getGlobalAccountNo()) && !subscriber.getGlobalAccountNo().isEmpty() ? subscriber.getGlobalAccountNo() : EMPTY_STRING);
        response.put("camsAccountNo", Objects.nonNull(subscriber)  &&  Objects.nonNull(subscriber.getCamsAccountNo()) && !subscriber.getCamsAccountNo().isEmpty() ? subscriber.getCamsAccountNo() : EMPTY_STRING);
        response.put("phoneNo", Objects.nonNull(subscriber)  &&  Objects.nonNull(subscriber.getPhoneNo()) && !subscriber.getPhoneNo().isEmpty() ? subscriber.getPhoneNo() : EMPTY_STRING);
        response.put("subscriberId",Objects.nonNull(subscriber)  &&  Objects.nonNull(subscriber.getId()) && !subscriber.getId().isEmpty() ? subscriber.getId() : EMPTY_STRING);
        response.put("subscriberName",Objects.nonNull(subscriber)  &&  Objects.nonNull(subscriber.getFirstName()) && !subscriber.getFirstName().isEmpty() ? subscriber.getFirstName() + " " + subscriber.getLastName() : EMPTY_STRING);
        response.put("email",Objects.nonNull(subscriber)  &&  Objects.nonNull(subscriber.getEmail()) && !subscriber.getEmail().isEmpty() ? subscriber.getEmail() : EMPTY_STRING);
        response.put("name", equipment.getRgwSerial() + ":" + equipment.getRgwMAC());
        response.put("lastReportedAt", getEquipmentLastReportedAt(equipment.getRgwSerial()));

        return response;
    }

    public Object getAllDetachedEquipment(DetachedEquipmentDTO detachedEquipmentDTO, Boolean exact) throws Exception {
        Compartment compartment = null;
        ClusterInfo clusterInfo = null;
        if (Objects.nonNull(detachedEquipmentDTO.getGroupId()) && !detachedEquipmentDTO.getGroupId().isEmpty()) {
            compartment = (Compartment) dataAccessService.read(Compartment.class, detachedEquipmentDTO.getGroupId());
            if (Objects.isNull(compartment) || Objects.isNull(compartment.getCluster()))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Compartment doesn't exist with id :: " + detachedEquipmentDTO.getGroupId());

            clusterInfo = compartment.getCluster().stream().filter(q -> q.isDefaultCluster()).findAny().orElse(null);
        }

        if (elasticSearchEnable) {
            return getDetachedEquipmentES(detachedEquipmentDTO, clusterInfo, exact);
        } else {
            return getDetachedEquipmentFromMysql(detachedEquipmentDTO, clusterInfo);
        }
    }

    private Object getDetachedEquipmentFromMysql(DetachedEquipmentDTO detachedEquipmentDTO, ClusterInfo clusterInfo) throws Exception {
        long count = 0;
        String countQuery;

        List<Object[]> equipmentList;

        String limitSql = " LIMIT " + (Objects.nonNull(detachedEquipmentDTO.getOffset()) ? detachedEquipmentDTO.getOffset() : 0) + COMMA + (Objects.nonNull(detachedEquipmentDTO.getMax()) ? detachedEquipmentDTO.getMax() : 10);
        if (isDetachedEqSearchEnabled(detachedEquipmentDTO)) {
            StringBuffer sb = getDetachedEquipmentSearchQuery(detachedEquipmentDTO);

            if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                String equipmentByClusterSql = GET_DETACHED_EQUIPMENT_BY_CLUSTER + sb.toString() + " AND cluster.cluster_id='" + (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()) + "' " + limitSql;
                countQuery = GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER + sb.toString() + " AND cluster.cluster_id='" + (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()) + "' ";

                equipmentList = dataAccessService.readNative(equipmentByClusterSql, new HashMap<>());
            } else {
                String equipmentSql;
                if (!CommonUtils.isSysAdmin()) {
                    equipmentSql = GET_DETACHED_EQUIPMENT_BY_CLUSTER + sb.toString() + " AND eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'" + limitSql;
                    countQuery = GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER + sb.toString() + " AND eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'";
                } else {
                    equipmentSql = GET_DETACHED_EQUIPMENT_BY_CLUSTER + sb.toString() + limitSql;
                    countQuery = GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER + sb.toString();
                }

                equipmentList = dataAccessService.readNative(equipmentSql, new HashMap<>());
            }
        } else {
            if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                String equipmentByClusterSql = GET_DETACHED_EQUIPMENT_BY_CLUSTER + " Where cluster.cluster_id='" + (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()) + "' " + limitSql;
                countQuery = GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER + " Where cluster.cluster_id='" + (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()) + "' ";
                equipmentList = dataAccessService.readNative(equipmentByClusterSql, new HashMap<>());
            } else {
                String equipmentSql;
                if (!CommonUtils.isSysAdmin()) {
                    equipmentSql = GET_DETACHED_EQUIPMENT_BY_CLUSTER + " Where eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'" + limitSql;
                    countQuery = GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER + " Where eq.groupId='" + CommonUtils.getGroupIdOfLoggedInUser() + "'";
                } else {
                    equipmentSql = GET_DETACHED_EQUIPMENT_BY_CLUSTER + limitSql;
                    countQuery = GET_DETACHED_EQUIPMENT_COUNT_BY_CLUSTER;
                }
                equipmentList = dataAccessService.readNative(equipmentSql, new HashMap<>());
            }
        }

        List<Object> totalEquipment = (List<Object>) dataAccessService.readNative(countQuery, new HashMap<>());
        count = Long.valueOf(Objects.nonNull(totalEquipment) ? totalEquipment.get(0).toString() : "0");

        DetachedEquipmentListDTO subscriberListDTO = new DetachedEquipmentListDTO();
        DetachedEquipmentListDTO.DetachedEquipmentListData data = subscriberListDTO.new DetachedEquipmentListData();

        ArrayList<DetachedEquipmentListDTO.DetachedEquipmentListData.DetachedEquipmentModel> subscriberDataList = new ArrayList<>();

        if (Objects.nonNull(equipmentList) && !equipmentList.isEmpty()) {
            for (Object subscriberDetail[] : equipmentList) {
                DetachedEquipmentListDTO.DetachedEquipmentListData.DetachedEquipmentModel subscriberData = data.new DetachedEquipmentModel();

                subscriberData.setEquipmentId(isNullString(String.valueOf(subscriberDetail[0])));
                subscriberData.setSerialNumber(isNullString(String.valueOf(subscriberDetail[1])));
                subscriberData.setMacAddress(isNullString(String.valueOf(subscriberDetail[2])));
                subscriberData.setServiceTelephoneNo(isNullString(String.valueOf(subscriberDetail[3])));

                subscriberDataList.add(subscriberData);
            }
        }

        data.setObjects(subscriberDataList);
        data.setTotalCount(count);
        subscriberListDTO.setData(data);

        return subscriberListDTO.getData();
    }

    private Object getDetachedEquipmentES(DetachedEquipmentDTO detachedEquipmentDTO, ClusterInfo clusterInfo, Boolean exact) {
        HashMap<String, Object> response = new HashMap<>();
        List<HashMap<String, Object>> listOfSubscriber = new ArrayList<>();
        ElasticSearchDTO esResponse = null;
        if (isDetachedEqSearchEnabled(detachedEquipmentDTO)) {
            ESRequest elasticSearchRequest = new ESRequest();
            elasticSearchRequest.setMax(detachedEquipmentDTO.getMax()).setOffset(detachedEquipmentDTO.getOffset()).setSortBy(detachedEquipmentDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
            HashMap<String, Object> esQueryParams = new HashMap<>();
            esQueryParams.put("serviceTelephoneNo", detachedEquipmentDTO.getServiceTelephoneNo());
            esQueryParams.put("rgwMAC", detachedEquipmentDTO.getMacAddress());
            esQueryParams.put("apId", detachedEquipmentDTO.getSerialNumber());

            List<String> mustNotList = new ArrayList();
            mustNotList.add("subscriberId");

            if (!CommonUtils.isSysAdmin()) {
                esQueryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
            }
            elasticSearchRequest.setQueryParams(esQueryParams);
            elasticSearchRequest.setMustNotExistValue(mustNotList);
            elasticSearchRequest.setShouldParams(null);
            if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()), exact);
            } else {
                esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", null, exact);
            }

        } else {
            if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                ESRequest elasticSearchRequest = new ESRequest();
                HashMap<String, Object> esQueryParams = new HashMap<>();
                List<String> mustNotList = new ArrayList();
                mustNotList.add("subscriberId");

                elasticSearchRequest.setQueryParams(esQueryParams);
                elasticSearchRequest.setMustNotExistValue(mustNotList);
                elasticSearchRequest.setShouldParams(null);

                elasticSearchRequest.setMax(detachedEquipmentDTO.getMax()).setOffset(detachedEquipmentDTO.getOffset()).setSortBy(detachedEquipmentDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
                if (Objects.nonNull(clusterInfo) || Objects.nonNull(detachedEquipmentDTO.getClusterId())) {
                    esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", (Objects.nonNull(detachedEquipmentDTO.getClusterId()) ? detachedEquipmentDTO.getClusterId() : clusterInfo.getId()), exact);
                } else {
                    esResponse = esService.searchWithFilter(elasticSearchRequest, "clusters", null, exact);
                }
            } else {
                SortOrder sortOrder = detachedEquipmentDTO.getOrder().name().equals("DESC") ? SortOrder.DESC : SortOrder.ASC;
                ESRequest elasticSearchRequest = new ESRequest();
                HashMap<String, Object> esQueryParams = new HashMap<>();
                List<String> mustNotList = new ArrayList();
                mustNotList.add("subscriberId");
                elasticSearchRequest.setMustNotExistValue(mustNotList);
                elasticSearchRequest.setQueryParams(esQueryParams);
                elasticSearchRequest.setShouldParams(null);
                elasticSearchRequest.setMax(detachedEquipmentDTO.getMax()).setOffset(detachedEquipmentDTO.getOffset()).setOrder(sortOrder).setSortBy(detachedEquipmentDTO.getSortBy()).setIndex(EQUIPMENT_INDEX);
                if (!CommonUtils.isSysAdmin()) {
                    HashMap<String, Object> queryParams = new HashMap<>();
                    queryParams.put("groupId", CommonUtils.getGroupIdOfLoggedInUser());
                    elasticSearchRequest.setQueryParams(queryParams);
                }
                esResponse = esService.getPaginatedData(elasticSearchRequest);
            }
        }

        if (Objects.nonNull(esResponse) && esResponse.getObjects().size() > 0) {
            esResponse.getObjects().forEach(subscriber -> {
                HashMap<String, Object> subscriberModel = new HashMap<>();
                subscriberModel.put("serialNumber", subscriber.get("serialNumber"));
                subscriberModel.put("macAddress", subscriber.get("rgwMAC"));
                subscriberModel.put("serviceTelephoneNo", subscriber.get("serviceTelephoneNo"));
                subscriberModel.put("equipmentId", subscriber.get("id"));

                listOfSubscriber.add(subscriberModel);
            });
        }
        response.put("objects", listOfSubscriber);
        response.put("totalCount", Objects.isNull(esResponse) ? 0 : esResponse.getTotalCount());
        return response;

    }

    private boolean isDetachedEqSearchEnabled(DetachedEquipmentDTO detachedEquipmentDTO) {
        return Objects.nonNull(detachedEquipmentDTO.getSerialNumber()) || Objects.nonNull(detachedEquipmentDTO.getMacAddress()) || Objects.nonNull(detachedEquipmentDTO.getServiceTelephoneNo());
    }


    private StringBuffer getDetachedEquipmentSearchQuery(DetachedEquipmentDTO detachedEquipmentDTO) {
        ArrayList<String> queryParams = new ArrayList<>();

        StringBuffer sb = new StringBuffer(ApplicationConstants.EMPTY_STRING);

        if (Objects.nonNull(detachedEquipmentDTO.getSerialNumber())) {
            queryParams.add("eq.rgwSerial LIKE '%" + detachedEquipmentDTO.getSerialNumber() + "%'");
        }

        if (Objects.nonNull(detachedEquipmentDTO.getMacAddress())) {
            queryParams.add("eq.rgwMAC LIKE '%" + detachedEquipmentDTO.getMacAddress() + "%'");
        }

        if (Objects.nonNull(detachedEquipmentDTO.getServiceTelephoneNo())) {
            queryParams.add("eq.serviceTelephoneNo LIKE '%" + detachedEquipmentDTO.getServiceTelephoneNo() + "%'");
        }

        if (!queryParams.isEmpty()) {
//            sb.append("Where ");
            int i = 0;
            for (String condition : queryParams) {
                sb.append(" and ");
                sb.append(condition);
                i++;
            }
        }

        return sb;
    }


}
