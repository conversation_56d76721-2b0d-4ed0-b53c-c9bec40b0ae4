package com.incs83.app.business.v2;

import com.actiontec.optim.platform.constant.ApplicationConstants;
import com.incs83.app.common.v2.ISPRequest;
import com.incs83.app.constants.queries.CompartmentSQL;
import com.incs83.app.constants.queries.GroupISPSQL;
import com.incs83.app.entities.Compartment;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.isp.ISPDetail;
import com.incs83.app.responsedto.v2.isp.ISPModel;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.INTERNET_SERVICE_PROVIDER;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.AP_DETAIL;
import static com.incs83.constants.ApplicationCommonConstants.GATEWAY;

@Service
public class ISPService {


    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private HazelcastService cacheService;

    private void populateNetworkInfoInCache() throws Exception {
        HashMap<String, Object> data = new HashMap<>();
        HashMap<String, Object> query = new HashMap<>();
        ISPDTOList ispdtoList = getAllISPs();
        ArrayList<ISPDetail> ispDetailDataList = new ArrayList<>();
        if (Objects.nonNull(ispdtoList) && !ispdtoList.getData().isEmpty()) {
            ispdtoList.getData().forEach(isp -> {
                ISPDetail ispDetail = new ISPDetail();
                ispDetail.setIspName(isp.getName());

                query.clear();
                query.put("isp", isp.getName());
                query.put("type", GATEWAY);
                long totalAp = mongoService.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                query.clear();
                query.put("isp", isp.getName());
                query.put("type", GATEWAY);
                query.put("mysqlProcessed", new BasicDBObject("$exists", true));
                query.put("processed", new BasicDBObject("$exists", true));
                long totalSubscriber = mongoService.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                query.clear();
                query.put("isp", isp.getName());
                query.put("type", GATEWAY);
                query.put("processed", new BasicDBObject("$exists", true));
                long totalScannedRecord = mongoService.count(query, null, ApplicationCommonConstants.AP_DETAIL);

                ispDetail.setMapped(totalSubscriber);
                ispDetail.setUnMapped(totalAp - totalScannedRecord);
                ispDetail.setFailedRecord(totalScannedRecord - totalSubscriber);

                ispDetailDataList.add(ispDetail);
            });
        }
        data.put("ispDetails", ispDetailDataList);

        query.clear();
        query.put("type", GATEWAY);
        long rgwCount = mongoService.count(query, new HashMap<>(), AP_DETAIL);
        data.put("totalRGW", rgwCount);

        cacheService.create(NETWORK_INFO_KEY, data, NETWORK_INFO_MAP_NAME);
    }

    public void createISP(ISPRequest ispRequest) throws Exception {

        if (!CommonUtils.isSysAdmin()) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Not authorized to create ISP.");
        }
        if (Objects.nonNull(ispRequest.getName()) && ispRequest.getName().equals(EMPTY_STRING))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Name cannot be empty");

        if (Objects.nonNull(ispRequest.getDescription()) && ispRequest.getDescription().length() > 255)
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Description length must be between 1-255 characters");

        if (Objects.nonNull(ispRequest.getDisplayName()) && ispRequest.getDisplayName().equals(EMPTY_STRING))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Display Name cannot be empty");

        String isp = null;

        ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
        if (Objects.nonNull(ispdtoList)) {
            ISPModel ispModel = ispdtoList.getData().stream().filter(q -> q.getName().equals(ispRequest.getName())).findAny().orElse(null);
            isp = Objects.nonNull(ispModel) ? ispModel.getName() : null;
        } else {
            isp = manageCommonService.getIspByName(ispRequest.getName());
        }

        if (Objects.nonNull(isp))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP already exist with name : " + ispRequest.getName());
        HashMap<String, Object> ispDetails = new HashMap<>();
        ispDetails.put("id", CommonUtils.generateUUID());
        ispDetails.put("name", ispRequest.getName());
        ispDetails.put("displayName", ispRequest.getDisplayName());
        ispDetails.put("description", ispRequest.getDescription());
        ispDetails.put("default", false);
        ispDetails.put("dateCreated", new Date());

        boolean flag = mongoService.create(INTERNET_SERVICE_PROVIDER, ispDetails);
        if (!flag) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request failed.Unable to create ISP");
        }

        cacheService.delete(ISP_KEY, ISP);

        populateNetworkInfoInCache();
    }

    public void updateISP(ISPRequest ispRequest, String id) throws Exception {
        if (Objects.nonNull(id)) {

            if (Objects.nonNull(ispRequest.getName()) && ispRequest.getName().equals(EMPTY_STRING))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Name cannot be empty");

            if (Objects.nonNull(ispRequest.getDisplayName()) && ispRequest.getDisplayName().equals(EMPTY_STRING))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Display Name cannot be empty");

            if (Objects.nonNull(ispRequest.getDescription()) && ispRequest.getDescription().length() > 255)
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Description length must be between 1-255 characters");

            ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
            ISPModel ispModel = null;
            if (Objects.nonNull(ispdtoList)) {
                ispModel = ispdtoList.getData().stream().filter(q -> q.getId().equals(id)).findAny().orElse(null);
            }

            if (Objects.isNull(ispModel)) {
                ispModel = getISPById(id).getData();
            }

            if (!ispModel.getName().equalsIgnoreCase(ispRequest.getName())) {
                String isp = manageCommonService.getIspByName(ispRequest.getName());
                if (Objects.nonNull(isp))
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP already exist with name : " + ispRequest.getName());
            }

            if (ispModel.isDefaultType())
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Default ISP cannot be edited");

            DBObject query = new BasicDBObject().append("id", id);
            BasicDBObject updateISPMap = new BasicDBObject();
            if (Objects.nonNull(ispRequest.getName()) && !ispRequest.getName().isEmpty()) {
                if (ispRequest.getName().length() > 20)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Name length must be between 1-20 characters");
                else
                    updateISPMap.put("name", ispRequest.getName());
            }
            if (Objects.nonNull(ispRequest.getDisplayName()) && !ispRequest.getDisplayName().isEmpty()) {
                if (ispRequest.getDisplayName().length() > 20)
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Display Name length must be between 1-20 characters");
                else
                    updateISPMap.put("displayName", ispRequest.getDisplayName());
            }
            if (Objects.nonNull(ispRequest.getDescription())) {
                updateISPMap.put("description", ispRequest.getDescription());
            }

            if (!updateISPMap.isEmpty()) {
                DBObject update = new BasicDBObject().append("$set", updateISPMap);
                mongoService.update(query, update, true, false, INTERNET_SERVICE_PROVIDER);
                cacheService.delete(ISP_KEY, ISP);

                populateNetworkInfoInCache();
            }
        }
    }

    private String getISPNameById(String ispId) throws Exception {
        String ispName = null;
        HashMap<String, Object> queryParams = new HashMap<>();

        if (Objects.nonNull(ispId)) {
            ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
            if (Objects.nonNull(ispdtoList)) {
                ISPModel ispModel = ispdtoList.getData().stream().filter(q -> q.getId().equals(ispId)).findAny().orElse(null);
                ispName = Objects.nonNull(ispModel) ? ispModel.getName() : null;
            } else {
                queryParams.clear();
                queryParams.put("id", ispId);

                HashMap<String, Object> appendableParams = new HashMap<>();
                BasicDBObject fieldsToRemove = new BasicDBObject();
                fieldsToRemove.put("_id", ZERO);

                DBObject dbObject = mongoService.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
                if (Objects.nonNull(dbObject)) {
                    ispName = String.valueOf(dbObject.get("name"));
                }
            }
        }

        return ispName;
    }

    public ISPDTOList getAllISPs() {
        String isp = null;
        if (!CommonUtils.isSysAdmin()) {
            try {
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
                if (Objects.nonNull(compartment) && Objects.nonNull(compartment.getIspId())) {
                    isp = getISPNameById(compartment.getIspId());
                }
            } catch (Exception e) {
                LOG.error("Error while fetching Data from DB");
            }
        }
        ISPDTOList ispdtoList = (ISPDTOList) cacheService.read(ISP_KEY, ISP);
        String loggedInUserIsp = isp;
        if (Objects.nonNull(ispdtoList)) {
            if (Objects.nonNull(loggedInUserIsp)) {

                List<ISPModel> modelArrayList = ispdtoList.getData().stream()
                        .filter(q -> {
                            String ispName = q.getName();
                            return ApplicationConstants.ALL_ISP.equals(ispName) || ispName.equals(loggedInUserIsp);
                        })
                        .collect(Collectors.toList());

                ispdtoList.setData(new ArrayList<>(modelArrayList));
            }
            return ispdtoList;
        } else {
            ispdtoList = new ISPDTOList();
        }
        ArrayList<ISPModel> ispModels = new ArrayList<>();

        BasicDBObject fieldsToremove = new BasicDBObject();
        fieldsToremove.put("_id", 0);
        List<DBObject> dbObjectList = mongoService.findList(new HashMap<>(), INTERNET_SERVICE_PROVIDER, fieldsToremove);
        if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty()) {
            for (DBObject dbObject : dbObjectList) {
                ISPModel ispModel = new ISPModel();
                ispModel.setId(String.valueOf(dbObject.get("id")));
                ispModel.setName(String.valueOf(dbObject.get("name")));
                ispModel.setDisplayName(String.valueOf(dbObject.get("displayName")));
                ispModel.setDescription(String.valueOf(dbObject.get("description")));
                ispModel.setDefaultType(Boolean.valueOf(dbObject.get("default").toString()));
                ispModels.add(ispModel);
            }
        }
        ispdtoList.setData(ispModels);
        cacheService.create(ISP_KEY, ispdtoList, ISP);

        if (Objects.nonNull(loggedInUserIsp)) {
            ArrayList<ISPModel> modelArrayList = new ArrayList<>(ispdtoList.getData().stream().filter(q -> q.getName().equals(loggedInUserIsp)).collect(Collectors.toList()));
            ispdtoList.setData(modelArrayList);
        }

        return ispdtoList;
    }


    public ISPDTOList getAllMappedISPs() throws Exception {
        ISPDTOList ispdtoList = new ISPDTOList();
        ArrayList<ISPModel> ispModels = new ArrayList<>();

        BasicDBObject fieldsToremove = new BasicDBObject();
        fieldsToremove.put("_id", 0);
        List<DBObject> dbObjectList = mongoService.findList(new HashMap<>(), INTERNET_SERVICE_PROVIDER, fieldsToremove);
        if (Objects.nonNull(dbObjectList) && !dbObjectList.isEmpty()) {
            List<String> ispList = new ArrayList<>();
            if (!CommonUtils.isSysAdmin()) {
                Compartment compartment = (Compartment) dataAccessService.read(Compartment.class, CommonUtils.getGroupIdOfLoggedInUser());
                ispList.add(compartment.getIspId());
            } else {
                ispList = (List<String>) dataAccessService.read(Compartment.class, CompartmentSQL.GET_ISP_ID_FROM_COMPARTMENT, new HashMap());
            }

            for (DBObject dbObject : dbObjectList) {
                if (ispList.contains(String.valueOf(dbObject.get("id")))) {
                    ISPModel ispModel = new ISPModel();
                    ispModel.setId(String.valueOf(dbObject.get("id")));
                    ispModel.setName(String.valueOf(dbObject.get("name")));
                    ispModel.setDisplayName(String.valueOf(dbObject.get("displayName")));
                    ispModel.setDescription(String.valueOf(dbObject.get("description")));
                    ispModel.setDefaultType(Boolean.valueOf(dbObject.get("default").toString()));
                    ispModel.setGroupId(getCompartmentIdByIsp(String.valueOf(dbObject.get("id"))));
                    ispModels.add(ispModel);
                }
            }
        } else {
            ISPModel ispModel = new ISPModel();
            ispModels.add(ispModel);
        }

        ispdtoList.setData(ispModels);
        return ispdtoList;
    }


    public ISPDTO getISPById(String id) throws Exception {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        DBObject dbObject = mongoService.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
        if (Objects.isNull(dbObject))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Record not found for ISP ID : " + id);

        ISPModel ispModel = new ISPModel();
        ispModel.setId(String.valueOf(dbObject.get("id")));
        ispModel.setName(String.valueOf(dbObject.get("name")));
        ispModel.setDisplayName(String.valueOf(dbObject.get("displayName")));
        ispModel.setDescription(String.valueOf(dbObject.get("description")));
        ispModel.setDefaultType(Boolean.valueOf(dbObject.get("default").toString()));

        ISPDTO ispdto = new ISPDTO();
        ispdto.setData(ispModel);

        return ispdto;
    }

    public void deleteISPById(String id) throws Exception {
        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("id", id);

        HashMap<String, Object> appendableParams = new HashMap<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", ZERO);

        DBObject dbObject = mongoService.findOne(queryParams, appendableParams, INTERNET_SERVICE_PROVIDER, fieldsToRemove);
        if (Objects.isNull(dbObject))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Record not found for ID : " + id);

        if (Objects.nonNull(dbObject.get("default")) && Boolean.valueOf(dbObject.get("default").toString()))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Default ISP cannot be deleted");

        if (getCompartmentByIsp(id))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "ISP is associated to a group, hence cannot be deleted");

        mongoService.deleteOne(queryParams, INTERNET_SERVICE_PROVIDER);
        cacheService.delete(ISP_KEY, ISP);

        populateNetworkInfoInCache();
    }

    private boolean getCompartmentByIsp(String ispId) throws Exception {
        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", ispId);
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);

        return Objects.nonNull(compartmentList) && !compartmentList.isEmpty();
    }

    private String getCompartmentIdByIsp(String ispId) throws Exception {
        String groupId = null;
        HashMap<String, String> params = new HashMap<>();
        params.put("ispId", ispId);
        List<Compartment> compartmentList = (List<Compartment>) dataAccessService.read(Compartment.class, GroupISPSQL.GET_COMPARTMENT_BY_ISP_ID, params);
        if(Objects.nonNull(compartmentList) && !compartmentList.isEmpty()){
           groupId = compartmentList.get(0).getId();
        }

        return groupId;
    }
}
