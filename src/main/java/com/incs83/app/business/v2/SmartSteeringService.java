package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.entities.Equipment;
import com.incs83.app.utils.SimpleVersion;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.service.MongoService;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import com.mongodb.util.JSON;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.FSECURE_RESPONSE_MESSAGE;
import static com.incs83.app.constants.misc.ApplicationConstants.THREAD_TO_SLEEP;

@Service
public class SmartSteeringService {
    private static final String VER_SmartSteeringDiagnosticLoggingOptions = "3.6.3";

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private ManageNetworkService manageNetworkService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private MongoService mongoService;

    @Autowired
    private RPCUtilityService rpcUtilityService;

    private static final Logger logger = LogManager.getLogger("org");

    public Object putSteering(String equipmentIdOrSerialOrSTN, Map requestBody) throws Exception {
        logger.debug("putSteering equipmentId:[{}]", equipmentIdOrSerialOrSTN);
        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (equipment.getRgwSerial().isEmpty()) {
            logger.error("putSteering equipment.rgwSerial is empty, equipmentId:[{}]", equipmentIdOrSerialOrSTN);
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "internal error");
        }

        String serial = manageCommonService.getControllerSerialByUserId(equipment.getRgwSerial()).orElseGet(()->equipment.getRgwSerial());

        HashMap<String, String> queryParam = new HashMap<>();
        queryParam.put("userId", equipment.getRgwSerial());
        queryParam.put("serialNumber", serial);
        DBObject apDetail = mongoService.findOne(AP_DETAIL, queryParam);
        if (Objects.isNull(apDetail)) {
            logger.error("putSteering apDetail is not found, equipmentId:[{}] rgwSerial:[{}]",  serial, equipment.getRgwSerial());
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
        }

        // determine CPE version
        String buildVer = (String) apDetail.get("buildVersion");
        if (new SimpleVersion(buildVer).isOlderThan(VER_SmartSteeringDiagnosticLoggingOptions)) {
            if (requestBody.get("diagnosticLoggingOptions") != null) {
                throw new ValidationException(HttpStatus.NOT_IMPLEMENTED.value(), "not supported");
            }
            boolean steeringEnabled = false;
            if (requestBody.containsKey("enable") && (boolean) requestBody.get("enable")) {
                steeringEnabled = true;
            }
            manageNetworkService.modifySmartSteering(equipmentIdOrSerialOrSTN, steeringEnabled, null);
            return requestBody;
        }

        try {
            if (!requestBody.containsKey("enable")) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "bad request");
            }

            boolean steeringEnabled = (boolean) requestBody.get("enable");

            HashMap<String, Boolean> steeringOptionsMap = new HashMap<>();
            steeringOptionsMap.put("staticEnable", steeringEnabled);
            steeringOptionsMap.put("dynamicEnable", steeringEnabled);
            steeringOptionsMap.put("deviceSteerEnable", steeringEnabled);
            steeringOptionsMap.put("deviceSteerStaticEnable", steeringEnabled);
            steeringOptionsMap.put("deviceSteerDynamicEnable", steeringEnabled);
            requestBody.put("steeringOptions", steeringOptionsMap);
        } catch (Exception e) {
            logger.error("putSteering failed to handle steeringEnabled, equipmentId:[{}]", equipmentIdOrSerialOrSTN, e);
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "bad request");
        }

        String isp = (String) apDetail.get("isp");
        String tid = CommonUtils.generateUUID();

        HashMap<String, String> publishParam = new HashMap<>();
        publishParam.put("-TID-", tid);
        publishParam.put("-USER_ID-", equipment.getRgwSerial());
        publishParam.put("-S_ID-", serial);
        publishParam.put("-URI-", "/cpe-api/steering");
        publishParam.put("-METHOD-", "PUT");
        publishParam.put("-PAYLOAD-", objectMapper.writeValueAsString(requestBody));

        HashMap<String, Object> data = new HashMap<>();
        data.put("_id", tid);
        data.put("isTimeout", false);
        data.put("userId", equipment.getRgwSerial());
        data.put("dateCreated", new Date());
        data.put("uri", publishParam.get("-URI-"));
        data.put("method", publishParam.get("-METHOD-"));
        data.put("isp", isp);
        logger.debug("putSteering record rpc in mongo, equipmentId:[{}] tid:[{}]", equipmentIdOrSerialOrSTN, tid);
        mongoService.create(JSON_RPC_V3_INFO, data);

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));

        logger.debug("putSteering publish to kafka, equipmentId:[{}] tid:[{}]", equipmentIdOrSerialOrSTN, tid);
        rpcUtilityService.publishToTopic(publishParam, MqttTemplate.PROFILE, MqttTemplate.TOPIC_FOR_RPC_CALL, maxTries);

        return processSteeringRpcResult(tid, maxTries, THREAD_TO_SLEEP);
    }

    private Object processSteeringRpcResult(String transactionId, int maxTries, long threadToSleep) throws Exception {
        BasicDBObject query = new BasicDBObject();
        query.put("_id", transactionId);
        boolean resultReceived = false;
        DBObject rpcResult = null;
        int initialStart = 0;
        Boolean isTimeout = false;
        Object response = new Object();
        while (!resultReceived) {
            try {
                Thread.sleep(threadToSleep);
            } catch (InterruptedException e) {
                logger.error("processSteeringRpcResult is interrupted, tid:[{}]", transactionId, e);
            }
            initialStart++;
            if (initialStart == maxTries) {
                isTimeout = true;
                logger.error("processSteeringRpcResult timeout, tid:[{}], tries:[{}]", transactionId, initialStart);
                BasicDBObject dataToUpdate = new BasicDBObject();
                dataToUpdate.put("isTimeout", isTimeout);
                dataToUpdate.put("timestamp", CommonUtils.getCurrentTimeInMillis());

                BasicDBObject update = new BasicDBObject();
                update.put("$set", dataToUpdate);
                mongoService.update(query, update, false, false, JSON_RPC_V3_INFO);
                break;
            }
            rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
            if (Objects.isNull(rpcResult) || Objects.isNull(rpcResult.get(RPC_RESPONSE_KEY)) || !Boolean.valueOf("" + rpcResult.get(RPC_RESPONSE_KEY))) {
                continue;
            } else {
                resultReceived = true;
            }
        }
        rpcResult = mongoService.findOne(JSON_RPC_V3_INFO, query);
        if (Objects.nonNull(rpcResult)) {
            Integer code = Integer.valueOf(String.valueOf(Objects.isNull(rpcResult.get("code")) ? "0" : rpcResult.get("code")));
            if (code == 200) {
                HashMap<String, Object> rpcRawResponse = objectMapper.convertValue(JSON.parse(String.valueOf(rpcResult.get("rawResponse"))), HashMap.class);
                response = rpcRawResponse.get("payload");

                DBObject responseDbObj = (DBObject)((List) response).get(0);
                responseDbObj.removeField("steeringOptions");
            } else {
                if (isTimeout || code == 0) {
                    throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Request Timed Out, please try again after sometime.");
                } else {
                    throw new ValidationException(code, FSECURE_RESPONSE_MESSAGE.get(code));
                }
            }
        }
        return response;
    }

    public Object getSteering(String equipmentIdOrSerialOrSTN) throws Exception {
        logger.debug("getSteering equipmentId:[{}]", equipmentIdOrSerialOrSTN);

        Equipment equipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (equipment.getRgwSerial().isEmpty()) {
            logger.error("getSteering equipment.rgwSerial is empty, equipmentId:[{}]", equipmentIdOrSerialOrSTN);
            throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "internal error");
        }

        String serial = manageCommonService.getControllerSerialByUserId(equipment.getRgwSerial()).orElseGet(()->equipment.getRgwSerial());

        HashMap<String, String> queryParam = new HashMap<>();
        queryParam.put("userId", equipment.getRgwSerial());
        queryParam.put("serialNumber",  serial);
        DBObject apDetail = mongoService.findOne(AP_DETAIL, queryParam);
        if (apDetail == null) {
            logger.error("getSteering apDetail not found, equipmentId:[{}]", equipmentIdOrSerialOrSTN);
            throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
        }

        String buildVer = (String) apDetail.get("buildVersion");
        if (new SimpleVersion(buildVer).isOlderThan(VER_SmartSteeringDiagnosticLoggingOptions)) {
            DBObject smartSteering = new BasicDBObject();
            smartSteering.put("enable", manageNetworkService.getStatusOfSmartSteering(equipmentIdOrSerialOrSTN));
            return smartSteering;
        } else {
            DBObject wifiConf = (DBObject) apDetail.get("wifiConf");
            if (wifiConf == null) {
                logger.error("getSteering wifiConf not found, equipmentId:[{}]", equipmentIdOrSerialOrSTN);
                throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
            }

            DBObject smartSteering = (DBObject) wifiConf.get("SmartSteering");
            if (smartSteering == null) {
                logger.error("getSteering smartSteering not found, equipmentId:[{}]", equipmentIdOrSerialOrSTN);
                throw new ValidationException(HttpStatus.NOT_FOUND.value(), "The resource is not found");
            }

            smartSteering.removeField("steeringOptions");
            return smartSteering;
        }
    }
}
