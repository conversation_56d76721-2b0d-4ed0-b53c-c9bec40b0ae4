package com.incs83.app.business.v2;

import com.incs83.annotation.Transactional;
import com.incs83.app.entities.OauthConfig;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.HashMap;

@Service
public class OauthConfigDao {
    private Logger logger = LogManager.getLogger(this.getClass());

    private static final String HQL__FIND_BY_NAME = " from OauthConfig where name =:name ";
    private static final String HQL__FIND_DEFAULT = " from OauthConfig where defaultProvider =:defaultProvider ";
    private static final String COLUMN__NAME = "name";
    private static final String COLUMN__DEFAULT_PROVIDER = "defaultProvider";

    @Autowired
    private DataAccessService dataAccessService;

    @Transactional
    public OauthConfig find(String id) throws Exception {
        OauthConfig config = (OauthConfig) dataAccessService.read(OauthConfig.class, id);
        return config;
    }

    @Transactional
    public OauthConfig findByName(String name) throws Exception {
        OauthConfig result = null;
        HashMap<String, String> params = new HashMap<>();
        params.put(COLUMN__NAME, name);
        Iterable<OauthConfig> oauthConfigs = dataAccessService.read(OauthConfig.class, HQL__FIND_BY_NAME, params);
        for (OauthConfig oauthConfig : oauthConfigs) {
            result = oauthConfig;
            break;
        }
        return result;
    }

    public OauthConfig findDefault() throws Exception {
        OauthConfig result = null;
        HashMap<String, Object> params = new HashMap<>();
        params.put(COLUMN__DEFAULT_PROVIDER, true);
        Iterable<OauthConfig> oauthConfigs = dataAccessService.read(OauthConfig.class, HQL__FIND_DEFAULT, params);
        for (OauthConfig oauthConfig : oauthConfigs) {
            result = oauthConfig;
            break;
        }
        return result;
    }

    @Transactional
    public Iterable<OauthConfig> listAll() throws Exception {
        return dataAccessService.read(OauthConfig.class);
    }

    @Transactional
    public void create(OauthConfig oauthConfig) throws Exception {
        CommonUtils.setCreateEntityFields(oauthConfig);
        if (oauthConfig.getDefaultProvider() != null && !oauthConfig.getDefaultProvider().booleanValue()) {
            oauthConfig.setDefaultProvider(null);
        }
        dataAccessService.create(OauthConfig.class, oauthConfig);
    }

    @Transactional
    public OauthConfig update(OauthConfig oauthConfig) throws Exception {
        OauthConfig result = null;
        OauthConfig config = (OauthConfig) dataAccessService.read(OauthConfig.class, oauthConfig.getId());
        if (null != config) {
            if (oauthConfig.getDefaultProvider() != null && !oauthConfig.getDefaultProvider().booleanValue()) {
                oauthConfig.setDefaultProvider(null);
            }
            result = (OauthConfig) dataAccessService.update(OauthConfig.class, oauthConfig);
            if (result == null) {
                throw new ValidationException(HttpStatus.INTERNAL_SERVER_ERROR.value(), "Error While Saving the Oauth Config");
            }
        }
        return result;
    }

    @Transactional
    public void delete(String id) throws Exception {
        com.incs83.app.entities.OauthConfig config = (com.incs83.app.entities.OauthConfig) dataAccessService.read(com.incs83.app.entities.OauthConfig.class, id);
        if (null != config) {
            dataAccessService.delete(OauthConfig.class, id);
        } else {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Such Oauth Config Available");
        }
    }
}
