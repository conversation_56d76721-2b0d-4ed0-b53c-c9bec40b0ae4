package com.incs83.app.business.v2;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.common.v2.SDAConfigEnvironmentRequest;
import com.incs83.app.common.v2.SDAConfigRequest;
import com.incs83.app.service.components.HttpServiceImpl;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.utils.ServiceNameUtils;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.SDA_CONFIG;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class ManageSDAConfigService {

    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    private MongoServiceImpl mongoServiceImpl;

    @Autowired
    private HttpServiceImpl httpService;

    @Autowired
    private ManageApplicationStatsService manageApplicationStatsService;

    public List<HashMap<String, Object>> getSDAConfigPropsByServiceName(String service) throws Exception {
        List<HashMap<String, Object>> sdaConfigList = new ArrayList<>();
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);

        String[] serviceNameList = {"jobRpcServer", "jobStatsServer", "kafkaConnect", "platform", "vmq"};
        ArrayList<String> availableServices = new ArrayList<>();
        Collections.addAll(availableServices, serviceNameList);
        if (!availableServices.contains(service))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Service Name");

        List<HashMap<String, Object>> fileDetailList = new ArrayList<>();
        HashMap<String, Object> query = new HashMap<>();
        query.put("service", service);
        DBObject dbObject = mongoServiceImpl.findOne(query, new HashMap<>(), SDA_CONFIG, ORIGIN, DESC, fieldsToRemove);
        List<HashMap<String, Object>> envList = new ArrayList();
        if (Objects.nonNull(dbObject) && dbObject.get("files") instanceof List) {
            List<HashMap<String, Object>> files = (List<HashMap<String, Object>>) dbObject.get("files");
            envList = (List<HashMap<String, Object>>) dbObject.get("env");
            if (Objects.nonNull(files) && !files.isEmpty()) {
                for (HashMap<String, Object> file : files) {
                    HashMap<String, Object> fileDetail = new HashMap<>();
                    fileDetail.put("fileName", file.get("name"));

                    List<HashMap<String, Object>> fileProps = new ArrayList<>();

                    List<HashMap<String, Object>> dataList = (List<HashMap<String, Object>>) file.get("data");
                    if (Objects.nonNull(dataList) && !dataList.isEmpty()) {
                        dataList = dataList.stream().filter(q -> !"static".equals(String.valueOf(q.get("type")))).collect(Collectors.toList());
                        for (HashMap<String, Object> properties : dataList) {
                            if (Objects.nonNull(properties.get("key"))) {
                                HashMap<String, Object> props = new HashMap<>();
                                props.put("key", String.valueOf(properties.get("key")));
                                props.put("value", properties.get("value"));
                                props.put("type", String.valueOf(properties.get("type")));
                                List range = (List) properties.get("options");
                                if (Objects.isNull(range)) {
                                    props.put("options", new ArrayList<>());
                                } else {
                                    props.put("options", properties.get("options"));
                                }
                                fileProps.add(props);
                            }
                        }
                    }

                    fileDetail.put("data", fileProps);
                    fileDetailList.add(fileDetail);
                }
            }
        }

        if (!fileDetailList.isEmpty()) {
            HashMap<String, Object> services = new HashMap<>();
            services.put("service", service);
            services.put("files", fileDetailList);
            services.put("env", envList);

            sdaConfigList.add(services);
        }

        return sdaConfigList;
    }

    public List<HashMap<String, Object>> getAllSDAConfigProps(String serviceName) throws Exception {
        if (Objects.isNull(serviceName) || serviceName.isEmpty()) {
            List<HashMap<String, Object>> sdaConfigList = new ArrayList<>();
            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("service", 1);
            fieldsToRemove.put("origin", 1);
            fieldsToRemove.put("version", 1);

            String[] serviceNameList = {"jobRpcServer", "jobStatsServer", "kafkaConnect", "platform", "vmq"};
            for (String service : serviceNameList) {
                HashMap<String, Object> query = new HashMap<>();
                query.put("service", service);
                DBObject dbObject = mongoServiceImpl.findOne(query, new HashMap<>(), SDA_CONFIG, ORIGIN, DESC, fieldsToRemove);
                if (Objects.nonNull(dbObject)) {
                    HashMap<String, Object> services = new HashMap<>();
                    services.put("displayName", ServiceNameUtils.getServiceName(service));
                    services.put("lastUpdated", Objects.nonNull(dbObject.get("origin")) ? dbObject.get("origin") : 0);
                    services.put("version", Objects.nonNull(dbObject.get("version")) ? dbObject.get("version") : "v1");
                    services.put("totalVersions", mongoServiceImpl.count(query, new HashMap<>(), SDA_CONFIG));
                    services.put("service", service);

                    sdaConfigList.add(services);
                }
            }

            return sdaConfigList;
        } else {
            return getSDAConfigPropsByServiceName(serviceName);
        }
    }

    public void updateSDAConfig(SDAConfigRequest request) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("service", request.getService());
        BasicDBObject sort = new BasicDBObject();
        sort.put("origin", DESC);
        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", ZERO);

        DBObject config = mongoServiceImpl.findOne(params, SDACONFIG, sort, projection);
        if (Objects.isNull(config))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid service name " + request.getService());

        DBObject file = ((List<DBObject>) config.get("files")).stream().filter(elem -> elem.get("name").equals(request.getFileName())).findAny().orElse(null);
        if (Objects.isNull(file))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid file name " + request.getFileName());

        List<DBObject> oldFiles = ((List<DBObject>) config.get("files")).stream().filter(elem -> !elem.get("name").equals(request.getFileName())).collect(Collectors.toList());
        oldFiles.forEach(elem -> elem.put("update", "false"));
        List<DBObject> updateFileData = (List<DBObject>) file.get("data");
        List<String> fetchedDataKeys = updateFileData.stream().map(elem -> String.valueOf(elem.get("key"))).collect(Collectors.toList());
        DBObject matchedElem = null;
        boolean elementUpdated, fileUpdated = false;
        for (Document requestElem : request.getData()) {
            elementUpdated = false;
            if (!fetchedDataKeys.contains(requestElem.get("key"))) {
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Key " + requestElem.get("key"));
            } else {
                List<String> listOptions;
                List<Integer> options;
                Integer value;
                matchedElem = updateFileData.stream().filter(data -> data.get("key").equals(requestElem.get("key"))).collect(Collectors.toList()).get(0);
                if (requestElem.get("type").equals("int") && !requestElem.get("value").equals(matchedElem.get("value"))) {
                    try {
                        value = Integer.parseInt(requestElem.getString("value"));
                        options = (List<Integer>) ((List) matchedElem.get("options")).stream().map(elem -> Integer.valueOf(elem.toString())).collect(Collectors.toList());
                        if ((options.get(0) <= value && value <= options.get(1)) || (options.get(0) <= value && value <= options.get(1))) {
                            matchedElem.put("value", String.valueOf(value));
                            elementUpdated = true;
                            fileUpdated = true;
                        } else {
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Provided value '" + value + "' is out of range [" + options.get(0) + "-" + options.get(1) + "] for key '" + requestElem.get("key") + "'");
                        }
                    } catch (NumberFormatException e) {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Only numerical values allowed for key '" + requestElem.get("key") + "'");
                    }
                } else if (requestElem.get("type").equals("list") && !requestElem.get("value").equals(matchedElem.get("value"))) {
                    listOptions = ((List<String>) matchedElem.get("options"));
                    if (listOptions.contains(requestElem.get("value"))) {
                        matchedElem.put("value", requestElem.get("value"));
                        elementUpdated = true;
                        fileUpdated = true;
                    } else {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Only  '" + listOptions.get(0) + "' and '" + listOptions.get(1) + "' values are allowed for key '" + requestElem.get("key") + "'");
                    }
                } else if (requestElem.get("type").equals("env") && !requestElem.get("value").equals(matchedElem.get("value"))) {
                    matchedElem.put("value", requestElem.get("value"));
                    elementUpdated = true;
                    fileUpdated = true;
                }
            }
            if (elementUpdated) {
                updateFileData.remove(matchedElem);
                matchedElem.put("updatedAt", CommonUtils.getCurrentTimeInMillis());
                matchedElem.put("updatedBy", CommonUtils.getUserIdOfLoggedInUser());
                updateFileData.add(matchedElem);
            }
        }
        if (fileUpdated) {
            file.put("data", updateFileData);
            file.put("update", "true");
            oldFiles.add(file);
            String oldVersion = Objects.isNull(config.get("version")) ? "v1" : String.valueOf(config.get("version"));
            config.put("version", "v" + (Integer.parseInt(oldVersion.substring(1, oldVersion.length())) + 1));
            config.put("files", oldFiles);
            config.put("origin", CommonUtils.getCurrentTimeInMillis());
            if (mongoServiceImpl.create(SDACONFIG, (HashMap<String, Object>) config)) {
                invokeSDAAPI(request.getService());
            }
        }
    }

    public void updateSDAEnvironmentConfig(SDAConfigEnvironmentRequest request) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("service", request.getService());
        BasicDBObject sort = new BasicDBObject();
        sort.put("origin", DESC);
        BasicDBObject projection = new BasicDBObject();
        projection.put("_id", ZERO);

        DBObject config = mongoServiceImpl.findOne(params, SDACONFIG, sort, projection);
        if (Objects.isNull(config))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid service name " + request.getService());

        List<DBObject> oldFiles = ((List<DBObject>) config.get("files"));
        oldFiles.forEach(elem -> elem.put("update", "false"));

        List<DBObject> oldEnvProps = ((List<DBObject>) config.get("env"));
        boolean elementUpdated = false;
        for (Document requestElem : request.getData()) {
            DBObject oldEnv = oldEnvProps.stream().filter(elem -> String.valueOf(elem.get("key")).equals(String.valueOf(requestElem.get("key")))).findAny().orElse(null);
            if (Objects.nonNull(oldEnv) && !String.valueOf(oldEnv.get("value")).equals(String.valueOf(requestElem.get("value")))) {
                elementUpdated = true;
            }
        }
        if (elementUpdated) {
            String oldVersion = Objects.isNull(config.get("version")) ? "v1" : String.valueOf(config.get("version"));
            config.put("version", "v" + (Integer.parseInt(oldVersion.substring(1, oldVersion.length())) + 1));
            config.put("files", oldFiles);
            config.put("env", request.getData());
            config.put("origin", CommonUtils.getCurrentTimeInMillis());
            if (mongoServiceImpl.create(SDACONFIG, (HashMap<String, Object>) config)) {
                invokeSDAAPI(request.getService());
            }
        }
    }

    private void invokeSDAAPI(String invokeService) {
        ObjectMapper mapper = new ObjectMapper();
        String httpResponse;
        HashMap response;
        try {
            httpResponse = manageApplicationStatsService.getResponseForServiceHealthURL("checkstatus/" + invokeService);
            if (!httpResponse.isEmpty()) {
                response = mapper.readValue(httpResponse, HashMap.class);
                if (!(response.get("Health_Status") instanceof String)) {
                    List<HashMap<String, Object>> result = (List<HashMap<String, Object>>) response.get("Health_Status");
                    if (Objects.nonNull(result) && !result.isEmpty()) {
                        result = result.parallelStream().filter(q -> !"4545".equals(q.get("Port"))).collect(Collectors.toList());
                        if (!result.isEmpty()) {
                            ExecutorService executorService = Executors.newFixedThreadPool(result.size());
                            int i = 1;
                            for (HashMap<String, Object> service : result) {
                                i++;
                                Runnable invokeSDAUpdateAPI = () -> {
                                    HashMap<String, String> headerInfo = new HashMap<>();
                                    HashMap<String, Object> queryParam = new HashMap<>();
                                    if (Objects.nonNull(service.get("PrivateIP"))) {
                                        String url = "http://" + service.get("PrivateIP") + ":4545/updateconfig";
                                        try {
                                            httpService.doGet(url, headerInfo, queryParam);
                                        } catch (Exception e) {
                                            LOG.error("Error while Updation of Service config " + url);
                                        }
                                    }
                                };
                                executorService.submit(invokeSDAUpdateAPI);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Error while Fetching SDA detail -", e);
        }
    }

    public List<String> getVersions(String service) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("service", service);
        List<BasicDBObject> versions = mongoServiceImpl.findList(SDACONFIG, params, "version", DESC);
        return versions.stream().map(elem -> (Objects.isNull(elem.get("version")) ? "v1" : String.valueOf(elem.get("version")))).collect(Collectors.toList());
    }

    public DBObject getHistory(String service, String version) {
        HashMap<String, Object> query = new HashMap<>();
        query.put("service", service);

        BasicDBObject sort = new BasicDBObject();
        sort.put("origin", DESC);
        DBObject currentConfig = mongoServiceImpl.findOne(query, SDACONFIG, sort, new BasicDBObject("_id", ZERO));

        query.put("version", version);
        DBObject prevConfig = mongoServiceImpl.findOne(SDACONFIG, query);
        if (Objects.isNull(prevConfig))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid version or service");

        String prevVersion = version;
        String currentVersion = String.valueOf(currentConfig.get("version"));
        List<BasicDBObject> currentFiles = (List<BasicDBObject>) currentConfig.get("files");
        List<BasicDBObject> comparedFiles = new ArrayList<>();
        currentFiles.forEach(currentFile -> {
            currentFile.remove("updated");
            List<BasicDBObject> currentFileData = (List<BasicDBObject>) currentFile.get("data");
            List<DBObject> historyData = new ArrayList<>();
            List<String> updatedKeys = new ArrayList<>();
            DBObject prevFile = ((List<BasicDBObject>) prevConfig.get("files")).stream().filter(file -> file.get("name").equals(currentFile.get("name"))).findFirst().orElse(null);

            if (Objects.nonNull(prevFile)) {
                List<BasicDBObject> prevFileData = ((List<BasicDBObject>) prevFile.get("data")).stream().filter(q -> !"static".equals(String.valueOf(q.get("type")))).collect(Collectors.toList());
                currentFileData = currentFileData.stream().filter(q -> !"static".equals(String.valueOf(q.get("type")))).collect(Collectors.toList());

                currentFileData.forEach(elem -> {
                    DBObject prevData = prevFileData.stream().filter(data -> data.get("key").equals(elem.get("key"))).findFirst().orElse(null);
                    if (!currentVersion.equals(prevVersion) && !elem.get("value").equals((prevData).get("value"))) {
                        prevData.put("currentVersion", false);
                        elem.put("currentVersion", true);
                        currentFile.put("updated", true);
                        historyData.add(prevData);
                        updatedKeys.add(String.valueOf(elem.get("key")));
                    }
                    historyData.add(elem);
                });
            }
            currentFile.put("data", historyData);
            currentFile.put("updatedKeys", updatedKeys);
            comparedFiles.add(currentFile);
        });

        List<BasicDBObject> env = new ArrayList<>();
        List<String> envUpdateKeys = new ArrayList<>();
        List<BasicDBObject> currentEnvProps = (List<BasicDBObject>) currentConfig.get("env");
        currentEnvProps.forEach(currentEnv -> {
            BasicDBObject prevEnvProps = ((List<BasicDBObject>) prevConfig.get("env")).stream().filter(q -> String.valueOf(q.get("key")).equals(String.valueOf(currentEnv.get("key")))).findAny().orElse(null);
            if (Objects.nonNull(prevEnvProps) && !currentEnv.get("value").equals(prevEnvProps.get("value"))) {
                currentEnv.put("currentVersion", true);
                prevEnvProps.put("currentVersion", false);
                env.add(prevEnvProps);
                envUpdateKeys.add(String.valueOf(currentEnv.get("key")));
            }

            env.add(currentEnv);
        });

        currentConfig.put("files", comparedFiles);
        currentConfig.put("env", env);
        currentConfig.put("envUpdateKeys", envUpdateKeys);
        return currentConfig;
    }
}
