package com.incs83.app.business.v2;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.common.v2.DeviceFriendlyNameRequest;
import com.incs83.app.common.v2.UpdateDeviceTypeRequest;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.constants.misc.RpcConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.entities.StationSpeedTest;
import com.incs83.app.entities.cassandra.*;
import com.incs83.app.enums.APDetailLookType;
import com.incs83.app.enums.ApiResponseCodeImpl;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.Device.*;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.app.service.repository.CassandraRepository;
import com.incs83.app.utils.DeviceUtilityULDL;
import com.incs83.app.utils.WiFiUtils;
import com.incs83.context.ExecutionContext;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.server.ResponseStatusException;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;
import static com.incs83.app.constants.misc.ApplicationConstants.ZERO;
import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.*;
import static java.util.Comparator.comparing;

/**
 * Created by Jayant on 29/1/18.
 */
@Service("v2.ManageDeviceService")
public class ManageDeviceService {
    private final Logger LOG = LogManager.getLogger(this.getClass());
    @Autowired
    CassandraRepository cassandraRepository;
    @Autowired
    private RPCUtilityService rPCUtilityService;
    @Autowired
    private MongoServiceImpl mongoService;
    @Autowired
    private ManageCommonService manageCommonService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SimpleRpcService cpeRpcService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private At3Adapter at3Adapter;

    public DeviceListDTO getDeviceList(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        params.put("userId", userEquipment.getRgwSerial());
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("moca", 0);
        mongoFieldOptions.put("bssid24G", 0);
        mongoFieldOptions.put("bssid5G", 0);

        List<BasicDBObject> equipmentList = mongoService.findList(params, ActiontecConstants.AP_DETAIL, mongoFieldOptions);

        HashMap<String, String> aggregationParams = new HashMap<>();
        aggregationParams.put("outputParams", "devices,count");
        aggregationParams.put("label", "$serialNumber");
        aggregationParams.put("operation", "$push,$sum");
        aggregationParams.put("keyToAggregate", "$$ROOT,1");
        aggregationParams.put("operand", "$gte");


        mongoFieldOptions.clear();
        mongoFieldOptions.put("devices._id", 0);
        List<BasicDBObject> devicesList = new ArrayList();
        List<BasicDBObject> StationDevices = mongoService.aggregate(params, null, ActiontecConstants.STATION_DETAIL, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_3_DAYS), aggregationParams, TIMESTAMP, mongoFieldOptions);
        if (Objects.nonNull(StationDevices) && !StationDevices.isEmpty()) {
            for (int i = 0; i < StationDevices.size(); i++) {
                devicesList.addAll((List<BasicDBObject>) StationDevices.get(i).get("devices"));
            }
        }
        String isp = equipmentList.get(ZERO).getString("isp");
        String version = equipmentList.get(ZERO).getString("etlVersion");
        String actualIsp = isp.split(HYPHEN_STRING)[0];
        if (!VERIZON_ISP.equals(actualIsp) || (VERIZON_ISP.equals(actualIsp) && version.equalsIgnoreCase("v4"))) {
            devicesList.addAll(manageCommonService.getHostnameDetail(userEquipment.getRgwSerial()));
        }
        HashMap<String, List<BasicDBObject>> allDevices = new HashMap();
        for (BasicDBObject device : devicesList) {
            if (!allDevices.containsKey(device.getString("macAddress"))) {
                allDevices.put(device.getString("macAddress"), new ArrayList());
            }
            allDevices.get(device.getString("macAddress")).add(device);
        }
        ArrayList<BasicDBObject> filteredDeviceList = new ArrayList<>();
        allDevices.values().forEach(deviceList -> {
            if (deviceList.size() == ONE) {
                filteredDeviceList.addAll(deviceList);
            } else {
                for (BasicDBObject d : deviceList) {
                    if (Objects.isNull(d.getString("phyType"))) {
                        filteredDeviceList.add(d);
                        break;
                    }
                }
            }
        });
        DeviceListDTO deviceListDTO = new DeviceListDTO();
        ArrayList<DeviceListDTO.DeviceDetailListData> deviceDetailDataList = new ArrayList<>();
        for (BasicDBObject equipment : equipmentList) {
            String equipmentSerialNumber = equipment.getString("serialNumber");
            List<BasicDBObject> filterBySerialDeviceList = filteredDeviceList.parallelStream().filter(d ->
                    d.getString("serialNumber").equals(equipmentSerialNumber)).collect(Collectors.toList());
            if (!filterBySerialDeviceList.isEmpty()) {
//                List<BasicDBObject> devices = (List<BasicDBObject>) filterBySerialDeviceList.get(0).get("devices");
                HashSet<String> deviceMacList = filterBySerialDeviceList.stream().map((device) -> String.valueOf(device.get("macAddress"))).collect(Collectors.toCollection(HashSet::new));
                HashMap<String, String> stationFriendlyName = manageCommonService.getFriendlyNameForStation(deviceMacList, userEquipment);
                for (BasicDBObject device : filterBySerialDeviceList) {
                    if (Objects.nonNull(device)) {
                        DeviceListDTO.DeviceDetailListData deviceDetail = deviceListDTO.new DeviceDetailListData();
                        String band = device.getString("band");
                        String userId = device.getString("userId");
                        String serialNumber = device.getString("serialNumber");
                        String bssid = device.getString("bssid");
                        if (band != null || userId != null || serialNumber != null || bssid != null) {
                            deviceDetail.setSsid(manageCommonService.getSsidForDeviceDetail(userId, serialNumber, bssid));
                        } else {
                            deviceDetail.setSsid(null);
                        }
                        deviceDetail.setConnectedTo(device.getString("serialNumber"));
                        deviceDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(device));
                        deviceDetail.setIp(device.getString("ip"));
                        deviceDetail.setMac(device.getString("macAddress"));
                        deviceDetail.setRssi(Objects.isNull(device.get("rssi")) ? 0D : Long.valueOf(String.valueOf(device.get("rssi"))));
                        deviceDetail.setVendor(manageCommonService.getVendorName(device.getString("macAddress")));
                        deviceDetail.setName(Objects.isNull(device.get("phyType")) ? stationFriendlyName.get(device.getString("macAddress")) : manageCommonService.getDisplayNameByPriorityForDevice(device));
                        deviceDetail.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetail.getMac(), device.getString("userId")));
                        deviceDetail.setDeviceType(Objects.isNull(device.get("deviceType")) ? "Other" : device.getString("deviceType"));
                        deviceDetail.setBand(Objects.isNull(device.get("phyType")) ? device.getString("band") : device.getString("phyType"));
                        deviceDetail.setLastReportedAt(Objects.isNull(device.get("lastReportAt")) ? device.getLong("timestamp") : device.getLong("lastReportAt"));
                        deviceDetailDataList.add(deviceDetail);
                    }
                }
            }
//            manageCommonService.addMiscDevicesForEquipment(equipmentSerialNumber, dataFromHostnameDetail, deviceDetailDataList, userAP);
        }
        if (deviceDetailDataList.isEmpty()) {
            DeviceListDTO.DeviceDetailListData deviceDetail = deviceListDTO.new DeviceDetailListData();
            deviceDetailDataList.add(deviceDetail);
        }
        deviceListDTO.setData(deviceDetailDataList);
        return deviceListDTO;
    }

    public DeviceDetailDTO fetchDeviceDetaiForMocaEth(String equipmentIdOrSerialOrSTN, String macAddr) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
        DeviceDetailDTO.DeviceDetailData deviceDetailData = deviceDetailDTO.new DeviceDetailData();

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);

        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, HOSTNAME_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        int minutes = 60;
        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
        aggregationWhereClause.put("dateHour", dateCriteria);

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(aggregationWhereClause, null, DS_HOST_INSIGHTS, mongoFieldOptions);
        double downLinkBytes = 0.0;
        double upLinkBytes = 0.0;
        if (!deviceDownLinkUpLinkBytes.isEmpty()) {
            List<DBObject> actualDevMap = new ArrayList<>();
            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    List<DBObject> devMap = (List<DBObject>) ((DBObject) dbObject.get("devMap")).get(macAddr);
                    if (Objects.nonNull(devMap)) {
                        actualDevMap.addAll(devMap);
                    }
                }

                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<DBObject> latestDevMap = (List<DBObject>) userSlidingData.get("devMap").get(macAddr);
                        if (Objects.nonNull(latestDevMap)) {
                            actualDevMap.addAll(latestDevMap);
                        }
                    }
                }
            }

            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            actualDevMap = actualDevMap.stream().filter(element -> ((Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp && (Objects.nonNull(element.get(INTERNET_RX)) && Objects.nonNull(element.get(INTERNET_TX))))).collect(Collectors.toList());

            if (!actualDevMap.isEmpty()) {
                downLinkBytes = downLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get(INTERNET_RX)) ? Double.valueOf(element.get(INTERNET_RX).toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
                upLinkBytes = upLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get(INTERNET_TX)) ? Double.valueOf(element.get(INTERNET_TX).toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
            }
        }

        deviceDetailData.setSpeedTestStats(null);

        if (Objects.nonNull(deviceDetails)) {
            deviceDetailData.setSsid("N/A");
            deviceDetailData.setConnectedTo(Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString());
            if (Objects.nonNull(deviceDetailData.getConnectedTo())) {
                HashSet<String> serialNumbers = new HashSet<>();
                serialNumbers.add(deviceDetailData.getConnectedTo());
                HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
                deviceDetailData.setConnectedToName(equipmentFriendlyName.get(deviceDetailData.getConnectedTo()));
            }
            deviceDetailData.setName(manageCommonService.getDisplayNameForEthMoca(String.valueOf(deviceDetails.get("macAddress")), userEquipment));
            deviceDetailData.setDeviceType(Objects.nonNull(deviceDetails.get("isExtender")) ? "EXT" : (Objects.isNull(deviceDetails.get("deviceType")) ? "Other" : deviceDetails.get("deviceType").toString()));
            Set<String> macAddressSet = new HashSet<>();
            macAddressSet.add(String.valueOf(deviceDetails.get("macAddress")));
            deviceDetailData.setHostname(Objects.nonNull(deviceDetails.get("hostname")) ? deviceDetails.get("hostname").toString() : null);
            deviceDetailData.setIp(Objects.isNull(deviceDetails.get("ip")) ? null : deviceDetails.get("ip").toString());
            deviceDetailData.setMac(Objects.isNull(deviceDetails.get("macAddress")) ? null : deviceDetails.get("macAddress").toString());
            deviceDetailData.setVendor(manageCommonService.getVendorName(String.valueOf(deviceDetails.get("macAddress"))));
            deviceDetailData.setInternetAccessBlocked(Objects.isNull(deviceDetails.get("blocked")) ? false : (Boolean) deviceDetails.get("internetAccessBlocked"));
            deviceDetailData.setInternetAccessBlockStartTime(Objects.isNull(deviceDetails.get("blockStartTime")) ? 0 : Long.valueOf(deviceDetails.get("internetAccessBlockStartTime").toString()));
            deviceDetailData.setInternetAccessBlockDuration(Objects.isNull(deviceDetails.get("blockDuration")) ? 0 : Long.valueOf(deviceDetails.get("internetAccessBlockDuration").toString()));
            deviceDetailData.setWifiMode("N/A");
            deviceDetailData.setBand(Objects.nonNull(deviceDetails.get("phyType")) ? deviceDetails.get("phyType").toString() : null);
            deviceDetailData.setRssi(0);
            deviceDetailData.setDownlinkPhyRate(0);
            deviceDetailData.setUplinkPhyRate(0);
            deviceDetailData.setDownlinkErrors(0);
            deviceDetailData.setDownlinkRetransmissions(0);
            deviceDetailData.setLastReportAt(Long.valueOf((deviceDetails.get("timestamp").toString())));
            deviceDetailData.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(deviceDetails));
            deviceDetailData.setInternetOn(false);
            deviceDetailData.setDownlinkBytes(downLinkBytes != 0.0 ? downLinkBytes : 0);
            deviceDetailData.setUplinkBytes(upLinkBytes != 0.0 ? upLinkBytes : 0);
            deviceDetailData.setCapability("N/A");
            deviceDetailData.setIsp(Objects.isNull(deviceDetails.get("isp")) ? "N/A" : String.valueOf(deviceDetails.get("isp")));
            deviceDetailData.setAlarms(new ArrayList<>());
            deviceDetailData.setRssiRange(0);
        }
        deviceDetailDTO.setData(deviceDetailData);
        return deviceDetailDTO;

    }

    public DeviceDetailDTO fetchDeviceDetailsByMacAddr(String equipmentIdOrSerialOrSTN, String macAddr) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        if (isEthernetDevice(macAddr, userEquipment)) {
            DeviceDetailDTO deviceDetailDTO = fetchDeviceDetaiForMocaEth(equipmentIdOrSerialOrSTN, macAddr);
            return deviceDetailDTO;
        }

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);
        manageCommonService.checkMacAddressOfStation(macAddr, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddr, userEquipment);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);


        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        String capabilityFromStationDetail = manageCommonService.getCapabilityFromDB((BasicDBObject) deviceDetails);
        queryParams.clear();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);

        List<String> projection = new ArrayList<>();

        List<DeviceCapability> deviceCapability = null;
        if (Objects.isNull(capabilityFromStationDetail) || capabilityFromStationDetail.isEmpty()) {
            HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
            if ((Objects.nonNull(commonProps.get(ABS_DATABASE)) && commonProps.get(ABS_DATABASE).equalsIgnoreCase("MONGO")) ? true : false) {
                List<DBObject> deviceCapabilityMongo = mongoService.findList(DEVICE_CAPABILITY, queryParams, TIMESTAMP, DESC);
                deviceCapability = manageCommonService.convertToDeviceCapability(deviceCapabilityMongo);
            } else {
                deviceCapability = (List<DeviceCapability>) cassandraRepository.read(DeviceCapability.class, queryParams, null, null, projection, 0);
            }
        }

        DeviceUtilityULDL getULDLForDevice = new DeviceUtilityULDL(macAddr, userEquipment, mongoService, manageCommonService).invoke();
        double downLinkBytes = getULDLForDevice.getDownLinkBytes();
        double upLinkBytes = getULDLForDevice.getUpLinkBytes();

        HashMap<String, Object> query = new HashMap<>();
        query.put("macAddr", macAddr);

        List<DBObject> stationSpeedTestList = mongoService.findList(STATION_SPEED_TEST_INFO, query, DATE, DESC);

        StationSpeedTest stationSpeedTest = null;
        if (Objects.nonNull(stationSpeedTestList) && !stationSpeedTestList.isEmpty()) {
            stationSpeedTest = new StationSpeedTest();
            stationSpeedTest.setDate(((Date) stationSpeedTestList.get(0).get("date")));
            stationSpeedTest.setDataRate(Objects.isNull(stationSpeedTestList.get(0).get("dataRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("dataRate"))));
            stationSpeedTest.setRxRate(Objects.isNull(stationSpeedTestList.get(0).get("rxRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("rxRate"))));
            stationSpeedTest.setTxRate(Objects.isNull(stationSpeedTestList.get(0).get("txRate")) ? 0 : Float.valueOf(TWO_DECIMAL_PLACE.format(stationSpeedTestList.get(0).get("txRate"))));
            stationSpeedTest.setBand(Objects.isNull(stationSpeedTestList.get(0).get("band")) ? "N/A" : stationSpeedTestList.get(0).get("band").toString());
            stationSpeedTest.setIpAddr(Objects.isNull(stationSpeedTestList.get(0).get("ipAddr")) ? "N/A" : stationSpeedTestList.get(0).get("ipAddr").toString());
            stationSpeedTest.setMacAddr(Objects.isNull(stationSpeedTestList.get(0).get("macAddr")) ? "N/A" : stationSpeedTestList.get(0).get("macAddr").toString());
            stationSpeedTest.setUserId(Objects.isNull(stationSpeedTestList.get(0).get("userId")) ? "N/A" : stationSpeedTestList.get(0).get("userId").toString());
        }

        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
        DeviceDetailDTO.DeviceDetailData deviceDetailData = deviceDetailDTO.new DeviceDetailData();
        DeviceDetailDTO.DeviceDetailData.DeviceSpeedTest deviceSpeedTest = deviceDetailData.new DeviceSpeedTest();

        if (Objects.nonNull(stationSpeedTest)) {
            deviceSpeedTest.setDataRate(stationSpeedTest.getDataRate());
            deviceSpeedTest.setDate(Objects.isNull(stationSpeedTest.getDate()) ? 0 : stationSpeedTest.getDate());
        } else {
            deviceSpeedTest.setDataRate(0f);
            deviceSpeedTest.setDate(0l);
        }
        deviceDetailData.setSpeedTestStats(deviceSpeedTest);

        if (Objects.nonNull(deviceDetails)) {
            String band = Objects.nonNull(deviceDetails.get("band")) ? deviceDetails.get("band").toString() : null;
            String userId = Objects.nonNull(deviceDetails.get("userId")) ? deviceDetails.get("userId").toString() : null;
            String serialNumber = Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString();
            String bssid = Objects.isNull(deviceDetails.get("bssid")) ? null : deviceDetails.get("bssid").toString();

            if (band != null || userId != null || serialNumber != null || bssid != null) {
                deviceDetailData.setSsid(dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : manageCommonService.getSsidForDeviceDetail(userId, serialNumber, bssid));
            } else {
                deviceDetailData.setSsid(null);
            }

            deviceDetailData.setConnectedTo(Objects.isNull(deviceDetails.get("serialNumber")) ? null : deviceDetails.get("serialNumber").toString());
            if (Objects.nonNull(deviceDetailData.getConnectedTo())) {
                HashSet<String> serialNumbers = new HashSet<>();
                serialNumbers.add(deviceDetailData.getConnectedTo());
                HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbers, APDetailLookType.serialNumber, true, userEquipment);
                deviceDetailData.setConnectedToName(equipmentFriendlyName.get(deviceDetailData.getConnectedTo()));
            }
            deviceDetailData.setName(manageCommonService.getDisplayNameForStation(String.valueOf(deviceDetails.get("macAddress")), userEquipment));
            deviceDetailData.setDeviceType(Objects.isNull(deviceDetails.get("deviceType")) ? "Other" : deviceDetails.get("deviceType").toString());
            Set<String> macAddressSet = new HashSet<>();
            macAddressSet.add(String.valueOf(deviceDetails.get("macAddress")));
            deviceDetailData.setHostname(manageCommonService.getHostNameForStation(macAddressSet, userEquipment).get(deviceDetails.get("macAddress").toString()));
            deviceDetailData.setIp(Objects.isNull(deviceDetails.get("ip")) ? null : deviceDetails.get("ip").toString());
            deviceDetailData.setMac(Objects.isNull(deviceDetails.get("macAddress")) ? null : deviceDetails.get("macAddress").toString());
            deviceDetailData.setVendor(manageCommonService.getVendorName(String.valueOf(deviceDetails.get("macAddress"))));
            deviceDetailData.setWifiMode(Objects.isNull(deviceDetails.get("wifiMode")) ? null : deviceDetails.get("wifiMode").toString());
            deviceDetailData.setBand(Objects.isNull(deviceDetails.get("band")) ? null : deviceDetails.get("band").toString());
            deviceDetailData.setRssi(Objects.isNull(deviceDetails.get("rssi")) ? 0 : Double.valueOf(deviceDetails.get("rssi").toString()));
            deviceDetailData.setDownlinkPhyRate(Objects.isNull(deviceDetails.get("uplinkPhyRate")) ? 0 : Double.valueOf(deviceDetails.get("uplinkPhyRate").toString()));
            deviceDetailData.setUplinkPhyRate(Objects.isNull(deviceDetails.get("downlinkPhyRate")) ? 0 : Double.valueOf(deviceDetails.get("downlinkPhyRate").toString()));
            deviceDetailData.setDownlinkErrors(Objects.isNull(deviceDetails.get("downlinkErrors")) ? 0 : Double.valueOf(deviceDetails.get("downlinkErrors").toString()));
            deviceDetailData.setDownlinkRetransmissions(Objects.isNull(deviceDetails.get("downlinkRetransmissions")) ? 0 : Double.valueOf(String.valueOf(deviceDetails.get("downlinkRetransmissions"))));
            if (Objects.nonNull(deviceDetails.get("lastReportAt"))) {
                if (deviceDetails.get("lastReportAt") instanceof Date) {
                    deviceDetailData.setLastReportAt(((Date) deviceDetails.get("lastReportAt")).getTime());
                } else {
                    deviceDetailData.setLastReportAt(Long.valueOf(deviceDetails.get("lastReportAt").toString()));
                }
            } else {
                deviceDetailData.setLastReportAt(new Date().getTime());
            }

            deviceDetailData.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(deviceDetails));
            deviceDetailData.setInternetOn(manageCommonService.isInternetEnabledForDeviceMac(deviceDetailData.getMac(), userEquipment.getRgwSerial()));
            deviceDetailData.setDownlinkBytes(downLinkBytes != 0.0 ? downLinkBytes : 0);
            deviceDetailData.setUplinkBytes(upLinkBytes != 0.0 ? upLinkBytes : 0);
            if (Objects.isNull(capabilityFromStationDetail) || capabilityFromStationDetail.isEmpty())
                deviceDetailData.setCapability(Objects.nonNull(deviceCapability) && !deviceCapability.isEmpty() ? (Objects.isNull(deviceCapability.get(0).getCapability()) ? null : deviceCapability.get(0).getCapability()) : null);
            else
                deviceDetailData.setCapability(capabilityFromStationDetail);
            deviceDetailData.setIsp(Objects.isNull(deviceDetails.get("isp")) ? "N/A" : String.valueOf(deviceDetails.get("isp")));
            deviceDetailData.setAlarms(getAlarmListForDevice(deviceDetailData, userId, getRgDfsEnabled(userId)));
            HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
            int rssiRange = LOWER_LIMIT;
            try {
                rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
            } catch (Exception e) {

            }

            deviceDetailData.setRssiRange(rssiRange);

            // XXX: reduce mongodb query times
            DBObject hostDetail = mongoService.findOne(queryParams, appendableParams, HOSTNAME_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
            if (hostDetail != null ) {
                deviceDetailData.setInternetAccessBlocked(Objects.isNull(hostDetail.get("internetAccessBlocked")) ? false : (Boolean) hostDetail.get("internetAccessBlocked"));
                deviceDetailData.setInternetAccessBlockStartTime(Objects.isNull(hostDetail.get("internetAccessBlockStartTime")) ? 0 : Long.valueOf(hostDetail.get("internetAccessBlockStartTime").toString()));
                deviceDetailData.setInternetAccessBlockDuration(Objects.isNull(hostDetail.get("internetAccessBlockDuration")) ? 0 : Long.valueOf(hostDetail.get("internetAccessBlockDuration").toString()));
            } else {
                deviceDetailData.setInternetAccessBlocked(false);
                deviceDetailData.setInternetAccessBlockStartTime(0);
                deviceDetailData.setInternetAccessBlockDuration(0);
            }
        }
        deviceDetailDTO.setData(deviceDetailData);
        return deviceDetailDTO;
    }


    public List<HashMap<String, Object>> getAlarmListForDevice(DeviceDetailDTO.DeviceDetailData deviceDetailData, String userId, boolean dfsEnabled) throws Exception {
        HashMap<String, Object> rssiAlarm = new HashMap<>();
        HashMap<String, Object> wifiModeAlarm = new HashMap<>();
        HashMap<String, Object> wifiAuthAlarm = new HashMap<>();
        List<HashMap<String, Object>> alarmListForDevice = new ArrayList<>();

        HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
        Double rssiRange = -75.0;
        try {
            rssiRange = Double.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
        } catch (Exception e) {

        }

        if (deviceDetailData.getRssi() < rssiRange && !deviceDetailData.getConnectivityStatus().equals("GREY")) {
            rssiAlarm.put("alarmType", "deviceAlarm");
            rssiAlarm.put("mac", deviceDetailData.getMac());
            rssiAlarm.put("serial", null);
            rssiAlarm.put("equipmentType", null);
            rssiAlarm.put("severity", 0);
            rssiAlarm.put("name", deviceDetailData.getName());
            rssiAlarm.put("desc", "WiFi signal is low(RSSI-: " + deviceDetailData.getRssi() + " dBm). Please move the device closer to the WiFi Router, Gateway or Extender, or consider adding a WiFi Network Extender to improve the WiFi coverage.");

            alarmListForDevice.add(rssiAlarm);
        }
        String wifiMode = deviceDetailData.getWifiMode();
        if (("802.11 b").equals(wifiMode) || ("802.11 bg").equals(wifiMode) || ("802.11 a").equals(wifiMode) || ("802.11 g").equals(wifiMode)) {
            wifiModeAlarm.put("alarmType", "deviceAlarm");
            wifiModeAlarm.put("mac", deviceDetailData.getMac());
            wifiModeAlarm.put("serial", null);
            wifiModeAlarm.put("equipmentType", null);
            wifiModeAlarm.put("severity", 0);
            wifiModeAlarm.put("name", deviceDetailData.getName());
            wifiModeAlarm.put("desc", "is not using the latest WiFi standard, and may be slowing down your WiFi network.  Disconnect or upgrade this device to help improve your WiFi network performance.");

            alarmListForDevice.add(wifiModeAlarm);
        }

        if (dfsEnabled) {
            if (StringUtils.contains(deviceDetailData.getSupportedBands(), "5G") && (false == deviceDetailData.isDfsSupported())) {
                // refers to ETL implementation, "DB " -> dual band, "-" -> not support dfs
                HashMap<String, Object> alarm = new HashMap<>();
                alarm.put("alarmType", "deviceAlarm");
                alarm.put("mac", deviceDetailData.getMac());
                alarm.put("serial", null);
                alarm.put("equipmentType", null);
                alarm.put("severity", 0);
                alarm.put("name", deviceDetailData.getName());
                alarm.put("desc", "does not appear to support DFS channels. Disable DFS to ensure that this device always has the capability to connect using the 5GHz band.");

                alarmListForDevice.add(alarm);
            }
        }

        HashMap<String, Object> param = new HashMap<>();
        param.put("macAddress", deviceDetailData.getMac());
        param.put("userId", userId);

        HashMap<String, Object> rangeParam = new HashMap<>();
        rangeParam.put("key", TIMESTAMP);
        rangeParam.put("operand", "gt");

        HashMap<String, Object> wpaFailInParam = new HashMap<>();
        wpaFailInParam.put("assocType", ASSOC_TYPE_WPA_AUTH_FAILED_LIST);

        List<EvDisassociation> disAssociationEvents = (List<EvDisassociation>) cassandraRepository.read(EvDisassociation.class, param, rangeParam, wpaFailInParam, new ArrayList<>(), 3 * 60);
        if ((disAssociationEvents != null) && !disAssociationEvents.isEmpty()) {

            disAssociationEvents.sort(Comparator.comparing(s -> ((Long) s.getTimestamp()), Comparator.reverseOrder()));
            EvDisassociation evDisassociation = disAssociationEvents.get(0);

            EvAssociation evAssociation = null;
            List<EvAssociation> associationEvents = (List<EvAssociation>) cassandraRepository.read(EvAssociation.class, param, rangeParam, null, new ArrayList<>(), 3 * 60);

            if((associationEvents != null) && !associationEvents.isEmpty()) {
                associationEvents.sort(Comparator.comparing(s -> ((Long) s.getTimestamp()), Comparator.reverseOrder()));
                evAssociation = associationEvents.get(0);
            }

            wifiAuthAlarm.put("alarmType", "deviceAlarm");
            wifiAuthAlarm.put("mac", deviceDetailData.getMac());
            wifiAuthAlarm.put("serial", null);
            wifiAuthAlarm.put("equipmentType", null);
            wifiAuthAlarm.put("severity", 0);
            wifiAuthAlarm.put("name", deviceDetailData.getName());
            wifiAuthAlarm.put("desc", deviceDetailData.getName() + " failed to connect to " + deviceDetailData.getSsid() + " due to wrong password.");
            if(evAssociation == null) {
                LOG.debug("UserId:[{}] MAC:[{}], No associate event", userId, deviceDetailData.getMac());
                alarmListForDevice.add(wifiAuthAlarm);
            } else if (evDisassociation.getTimestamp() > evAssociation.getTimestamp()) {
                LOG.debug("UserId:[{}] MAC:[{}], WPA auth:[{}] > Associate:[{}]", userId, deviceDetailData.getMac(), evDisassociation.getTimestamp(), evAssociation.getTimestamp());
                alarmListForDevice.add(wifiAuthAlarm);
            } else {
                LOG.debug("UserId:[{}] MAC:[{}], WPA auth:[{}] < Associate:[{}]", userId, deviceDetailData.getMac(), evDisassociation.getTimestamp(), evAssociation.getTimestamp());
            }
        }

        return alarmListForDevice;
    }

    public DeviceRssiDetailDTO fetchRSSIData(String equipmentIdOrSerialOrSTN, String macAddress, Long duration) throws
            Exception {
        manageCommonService.isDurationValid(duration);
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddress, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddress, userEquipment);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        long minutes = duration < 60 ? 60 : duration;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
        aggregationWhereClause.put("dateHour", dateCriteria);

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
//        mongoFieldOptions.put("devMap." + macAddress, 1);

        List<DBObject> deviceDownlinkUplinkBytes = mongoService.findList(aggregationWhereClause, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);

        List<Map<String, Object>> devMapData;
        DeviceRssiDetailDTO deviceRssiDetailDTO = new DeviceRssiDetailDTO();
        ArrayList<DeviceRssiDetailDTO.DeviceRssiData> deviceRssiDataList = new ArrayList<>();
        if (!deviceDownlinkUplinkBytes.isEmpty()) {

            HashMap<String, String> deviceProps = commonService.read(DEVICE_CONFIG);
            int rssiRange = LOWER_LIMIT;
            try {
                rssiRange = Integer.valueOf(deviceProps.get(DEVICE_RSSI_LIMIT));
            } catch (Exception e) {

            }

            List<Map<String, Object>> actualDevMapData = new ArrayList<>();
            List<Map<String, Object>> combinedData = new ArrayList<>();
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();

            HashSet<String> serialNumbersList = new HashSet<>();
            for (DBObject dbObject : deviceDownlinkUplinkBytes) {
                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    devMapData = (List<Map<String, Object>>) ((DBObject) dbObject.get("devMap")).get(macAddress);
                    if (Objects.nonNull(devMapData) && !devMapData.isEmpty()) {
                        combinedData.addAll(devMapData);
                    }
                }

                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<Map<String, Object>> latestDevMap = (List<Map<String, Object>>) userSlidingData.get("devMap").get(macAddress);
                        if (Objects.nonNull(latestDevMap)) {
                            combinedData.addAll(latestDevMap);
                        }
                    }
                }
            }
            combinedData = combinedData.stream().filter(element -> Long.valueOf(Objects.nonNull(element.get("timestamp")) ? element.get("timestamp").toString() : "0") > currTimeStamp).collect(Collectors.toList());
            HashSet<String> serialNumbers = combinedData.stream().map((devMap) -> String.valueOf(devMap.get("serialNumber"))).collect(Collectors.toCollection(HashSet::new));
            if (!serialNumbers.isEmpty())
                serialNumbersList.addAll(serialNumbers);
            if (Objects.nonNull(combinedData) && !combinedData.isEmpty())
                actualDevMapData.addAll(combinedData);

            if (!actualDevMapData.isEmpty()) {
                HashMap<String, String> equipmentFriendlyName = manageCommonService.getDisplayNameForEquipment(serialNumbersList, APDetailLookType.serialNumber, true, userEquipment);
                if (equipmentFriendlyName.keySet().size() > 1) {
                    int i = 0;
                    HashSet<String> equipmentNames = new HashSet<>();
                    HashMap<String, String> equipmentUniqueFriendlyNames = new HashMap<>();
                    for (Map.Entry<String, String> map : equipmentFriendlyName.entrySet()) {
                        if (equipmentNames.contains(map.getValue())) {
                            i++;
                            equipmentUniqueFriendlyNames.put(map.getKey(), map.getValue() + UNDER_SCORE_STRING + i);
                        } else {
                            equipmentUniqueFriendlyNames.put(map.getKey(), map.getValue());
                            equipmentNames.add(map.getValue());
                        }
                    }
                    equipmentFriendlyName = equipmentUniqueFriendlyNames;
                }
                for (Map<String, Object> deviceGraph : actualDevMapData) {
                    if (Objects.nonNull(deviceGraph.get("rssi")) && Double.valueOf(deviceGraph.get("rssi").toString()) != 0) {
                        DeviceRssiDetailDTO.DeviceRssiData deviceRssiData = deviceRssiDetailDTO.new DeviceRssiData();
                        if (Objects.nonNull(deviceGraph.get("serialNumber"))) {
                            String band = null;
                            if(deviceGraph.get("radioKey") != null) {
                                band = deviceGraph.get("radioKey").toString();
                            } else {
                                band = Objects.isNull(deviceGraph.get("band")) ? "" : deviceGraph.get("band").toString().equals("1") ? "5G" : "2.4G";
                            }
                            String friendlyName = equipmentFriendlyName.get(deviceGraph.get("serialNumber")) + " (" + band + ")";
                            deviceRssiData.setEquipment(friendlyName);
                        } else {
                            deviceRssiData.setEquipment(EMPTY_STRING);
                        }
                        deviceRssiData.setRssiRange(rssiRange);
                        deviceRssiData.setRssi(Double.valueOf(deviceGraph.get("rssi").toString()));
                        deviceRssiData.setTimestamp(Objects.isNull(deviceGraph.get("timestamp")) ? 0 : Long.valueOf(deviceGraph.get("timestamp").toString()));
                        deviceRssiDataList.add(deviceRssiData);
                    }
                }
                Collections.sort(deviceRssiDataList);
                if (!deviceRssiDataList.isEmpty() && duration < 60) {
                    Collections.reverse(deviceRssiDataList);
                    deviceRssiDataList.addAll(deviceRssiDataList.subList(0, Integer.valueOf(duration.toString())));
                    Collections.reverse(deviceRssiDataList);
                }
            }
        }
        if (deviceRssiDataList.isEmpty()) {
            DeviceRssiDetailDTO.DeviceRssiData deviceRssiData = deviceRssiDetailDTO.new DeviceRssiData();
            deviceRssiData.setEquipment(null);
            deviceRssiDataList.add(deviceRssiData);
        }
        deviceRssiDetailDTO.setData(deviceRssiDataList);

        return deviceRssiDetailDTO;
    }


    public DeviceWifiPhyDTO fetchWifiPhyRateData(String equipmentIdOrSerialOrSTN, String macAddress, Long
            duration) throws Exception {
        manageCommonService.isDurationValid(duration);
//        if (ValidationUtil.validateMAC(equipmentIdOrSerialOrSTN)) {
//            equipmentIdOrSerialOrSTN = manageCommonService.getAPIDFromMAC(equipmentIdOrSerialOrSTN);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddress, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddress, userEquipment);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        long minutes = duration < 60 ? 60 : duration;

        long dateHour = nowMillSec / 1000 - (minutes * 60);

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
        aggregationWhereClause.put("dateHour", dateCriteria);

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
//        mongoFieldOptions.put("devMap." + macAddress, 1);

        List<DBObject> deviceDownlinkUplinkBytes = mongoService.findList(aggregationWhereClause, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);

        if (Objects.isNull(deviceDownlinkUplinkBytes)) {
            throw new ApiException(ApiResponseCodeImpl.MAC_ADDRESS_NOT_FOUND);
        }

        DeviceWifiPhyDTO deviceWifiPhyDTO = new DeviceWifiPhyDTO();
        ArrayList<DeviceWifiPhyDTO.DeviceWifiPhyData> deviceWifiPhyDataList = new ArrayList<>();
        List<Map<String, Object>> devMapData;
        List<Map<String, Object>> actualDevMapData = new ArrayList<>();
        List<Map<String, Object>> combinedData = new ArrayList<>();
        if (!deviceDownlinkUplinkBytes.isEmpty()) {
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            for (DBObject dbObject : deviceDownlinkUplinkBytes) {
                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    devMapData = (List<Map<String, Object>>) ((DBObject) dbObject.get("devMap")).get(macAddress);
                    if (Objects.nonNull(devMapData) && !devMapData.isEmpty()) {
                        combinedData.addAll(devMapData);
                    }
                }

                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<Map<String, Object>> latestDevMap = (List<Map<String, Object>>) userSlidingData.get("devMap").get(macAddress);
                        if (Objects.nonNull(latestDevMap) && !latestDevMap.isEmpty()) {
                            combinedData.addAll(latestDevMap);
                        }
                    }
                }
            }
            combinedData = combinedData.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
            if (!combinedData.isEmpty())
                actualDevMapData.addAll(combinedData);
            if (!actualDevMapData.isEmpty()) {
                for (Map<String, Object> deviceGraph : actualDevMapData) {
                    DeviceWifiPhyDTO.DeviceWifiPhyData deviceWifiPhyData = deviceWifiPhyDTO.new DeviceWifiPhyData();
                    deviceWifiPhyData.setDownlinkPhyRate(Objects.isNull(deviceGraph.get("lastDataUplinkRate")) ? 0.0 : Double.valueOf(deviceGraph.get("lastDataUplinkRate").toString()));
                    deviceWifiPhyData.setTimestamp(Objects.isNull(deviceGraph.get("timestamp")) ? 0 : Long.valueOf(deviceGraph.get("timestamp").toString()));
                    deviceWifiPhyData.setUplinkPhyRate(Objects.isNull(deviceGraph.get("lastDataDownlinkRate")) ? 0.0 : Double.valueOf(deviceGraph.get("lastDataDownlinkRate").toString()));
                    deviceWifiPhyDataList.add(deviceWifiPhyData);
                }
                Collections.sort(deviceWifiPhyDataList);
                if (!deviceWifiPhyDataList.isEmpty() && duration < 60) {
                    Collections.reverse(deviceWifiPhyDataList);
                    deviceWifiPhyDataList.addAll(deviceWifiPhyDataList.subList(0, Integer.valueOf(duration.toString())));
                    Collections.reverse(deviceWifiPhyDataList);
                }
            }
        }
        if (deviceWifiPhyDataList.isEmpty()) {
            DeviceWifiPhyDTO.DeviceWifiPhyData deviceWifiPhyData = deviceWifiPhyDTO.new DeviceWifiPhyData();
            deviceWifiPhyDataList.add(deviceWifiPhyData);
        }

        deviceWifiPhyDTO.setData(deviceWifiPhyDataList);

        return deviceWifiPhyDTO;
    }

    public DeviceBandWidthDTO getTopTenBandWidthConsumingDevices(String equipmentIdOrSerialOrSTN, long minutes, String type) throws
            Exception {
        manageCommonService.isDurationValid(minutes);
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        List<HashMap<String, Object>> topTenDevicesDataList;
        topTenDevicesDataList = getTopTenDevices(userEquipment, minutes, Objects.isNull(type) ? EMPTY_STRING : type);

        DeviceBandWidthDTO deviceBandWidthDTO = new DeviceBandWidthDTO();
        ArrayList<DeviceBandWidthDTO.DeviceBandWidthData> deviceBandWidthDataList = new ArrayList<>();
        for (HashMap<String, Object> device : topTenDevicesDataList) {
            DeviceBandWidthDTO.DeviceBandWidthData deviceBandWidthData = deviceBandWidthDTO.new DeviceBandWidthData();
            deviceBandWidthData.setRx(Objects.isNull(device.get("rx")) ? 0.0 : (Double.valueOf(device.get("rx").toString()) / (1000F * 1000F)));
            deviceBandWidthData.setTx(Objects.isNull(device.get("tx")) ? 0.0 : (Double.valueOf(device.get("tx").toString()) / (1000F * 1000F)));
            deviceBandWidthData.setBandwidth(Objects.isNull(device.get("totalTxRx")) ? 0.0 : (Double.valueOf(device.get("totalTxRx").toString()) / (1000F * 1000F)));
            deviceBandWidthData.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : Objects.isNull(device.get("macAddress")) ? null : device.get("macAddress").toString());
            deviceBandWidthData.setName(Objects.isNull(device.get("name")) ? null : device.get("name").toString());
            deviceBandWidthDataList.add(deviceBandWidthData);
        }
        if (deviceBandWidthDataList.isEmpty()) {
            DeviceBandWidthDTO.DeviceBandWidthData deviceBandWidthData = deviceBandWidthDTO.new DeviceBandWidthData();
            deviceBandWidthDataList.add(deviceBandWidthData);
        }
        deviceBandWidthDTO.setData(deviceBandWidthDataList);

        return deviceBandWidthDTO;
    }

    public DeviceBandWidthDTO getTopTenInternetBandWidthConsumingDevices(String equipmentIdOrSerialOrSTN,
                                                                         long minutes, String type) throws Exception {
        manageCommonService.isDurationValid(minutes);
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        List<HashMap<String, Object>> topTenDevicesDataList;

        topTenDevicesDataList = getTopTenDevicesInternet(userEquipment, minutes, TRAFFIC_DIRECTION.get(type));

        DeviceBandWidthDTO deviceBandWidthDTO = new DeviceBandWidthDTO();
        ArrayList<DeviceBandWidthDTO.DeviceBandWidthData> deviceBandWidthDataList = new ArrayList<>();
        for (HashMap<String, Object> device : topTenDevicesDataList) {
            DeviceBandWidthDTO.DeviceBandWidthData deviceBandWidthData = deviceBandWidthDTO.new DeviceBandWidthData();
            deviceBandWidthData.setRx(Objects.isNull(device.get(INTERNET_RX)) ? 0.0 : Double.valueOf(device.get(INTERNET_RX).toString()));
            deviceBandWidthData.setTx(Objects.isNull(device.get(INTERNET_TX)) ? 0.0 : Double.valueOf(device.get(INTERNET_TX).toString()));
            deviceBandWidthData.setBandwidth(Objects.isNull(device.get(INTERNET_TRAFFIC)) ? 0.0 : Double.valueOf(device.get(INTERNET_TRAFFIC).toString()));
            deviceBandWidthData.setMacAddress(Objects.isNull(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : device.get("macAddress")) ? null : device.get("macAddress").toString());


            HashMap<String, String> queryParams = new HashMap<>();
            HashMap<String, String> appendableParams = new HashMap<>();
            queryParams.put("userId", ((Equipment) userEquipment).getRgwSerial());
            queryParams.put("macAddress", Objects.isNull(device.get("macAddress")) ? null : device.get("macAddress").toString());

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

            deviceBandWidthData.setFromDSHost(!Objects.nonNull(deviceDetails));

            deviceBandWidthData.setName(manageCommonService.getDisplayNameByUserIdMacAddr(userEquipment.getRgwSerial(), String.valueOf(device.get("macAddress"))));
            if (StringUtils.isBlank(deviceBandWidthData.getName())) {
                if (deviceBandWidthData.isFromDSHost()) {
                    deviceBandWidthData.setName(manageCommonService.getDisplayNameByPriorityForEthernetMoCADevices(new BasicDBObject("macAddress", device.get("macAddress")).append("userId", userEquipment.getRgwSerial())));
                } else {
                    deviceBandWidthData.setName(Objects.isNull(device.get("name")) ? null : device.get("name").toString());
                }
            }

            deviceBandWidthDataList.add(deviceBandWidthData);

        }
        if (deviceBandWidthDataList.isEmpty()) {
            DeviceBandWidthDTO.DeviceBandWidthData deviceBandWidthData = deviceBandWidthDTO.new DeviceBandWidthData();
            deviceBandWidthDataList.add(deviceBandWidthData);
        }
        deviceBandWidthDTO.setData(deviceBandWidthDataList);

        return deviceBandWidthDTO;
    }

    public DeviceWifiTrafficDTO getWifiTrafficByDevice(String equipmentIdOrSerialOrSTN, long minutes, String type) throws
            Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<HashMap<String, Object>> top5DevicesDataList;
        top5DevicesDataList = getTopTenWiFiTraffic(userEquipment, minutes, type);

        DeviceWifiTrafficDTO deviceWifiTrafficDTO = new DeviceWifiTrafficDTO();
        ArrayList<DeviceWifiTrafficDTO.DeviceWifiTrafficData> deviceWifiTrafficDataList = new ArrayList<>();
        for (HashMap<String, Object> device : top5DevicesDataList) {
            DeviceWifiTrafficDTO.DeviceWifiTrafficData deviceWifiTrafficData = deviceWifiTrafficDTO.new DeviceWifiTrafficData();
            deviceWifiTrafficData.setMacAddress(Objects.isNull(device.get("macAddress")) ? null : device.get("macAddress").toString());
            deviceWifiTrafficData.setName(Objects.isNull(device.get("name")) ? null : device.get("name").toString());
            deviceWifiTrafficData.setWifiTrafficPercentage(Objects.isNull(device.get("wifiTrafficPercentage")) ? 0.0 : Double.valueOf(device.get("wifiTrafficPercentage").toString()));
            deviceWifiTrafficData.setTraffic(Objects.isNull(device.get("wifiTraffic")) ? 0.0 : Double.valueOf(device.get("wifiTraffic").toString()));
            deviceWifiTrafficDataList.add(deviceWifiTrafficData);
        }
        if (deviceWifiTrafficDataList.isEmpty()) {
            DeviceWifiTrafficDTO.DeviceWifiTrafficData deviceWifiTrafficData = deviceWifiTrafficDTO.new DeviceWifiTrafficData();
            deviceWifiTrafficDataList.add(deviceWifiTrafficData);
        }

        deviceWifiTrafficDTO.setData(deviceWifiTrafficDataList);
        return deviceWifiTrafficDTO;
    }

    private ArrayList<String> getExtenderWanMacAddress(String userId) {

        ArrayList<String> extenderMacList = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        params.put("userId", userId);
        params.put("type", "EXTENDER");

        List<DBObject> extenderList = mongoService.findList(params, null, AP_DETAIL, mongoFieldOptions);

        if(extenderList != null && !extenderList.isEmpty()) {
            for(DBObject extender : extenderList) {
                HashMap<String, Object> wanMap = (HashMap<String, Object>) extender.get("wan");
                String macAddress = String.valueOf(wanMap.get("macAddress")).toLowerCase();
                extenderMacList.add(macAddress);

                List<DBObject> ssids = (List<DBObject>) extender.get("ssids");
                for(DBObject ssid : ssids) {
                    String ssidKey = String.valueOf(ssid.get("SSIDKey"));
                    if (ssidKey.equals(SSID_BACKHAUL_AP)) {
                        String bssid = String.valueOf(ssid.get("BSSID")).toLowerCase();
                        if (!bssid.equals(INVALID_MAC_ADDRESS)) {
                            extenderMacList.add(bssid);
                        }
                    }

                }
            }
        }

        LOG.debug("userId: {}, result: {}", userId, extenderMacList);
        return extenderMacList;
    }

    private boolean isExtender(ArrayList<String> extenderWanMac, String staMac) {

        long macValue1 = Long.parseLong(staMac.substring(3).replaceAll(":", ""), 16);
        for(String mac : extenderWanMac) {
            if (mac == null || mac.indexOf(":") < 0)
                continue;
            long macValue2 = Long.parseLong(mac.substring(3).replaceAll(":", ""), 16);
            long macDelta = macValue1 - macValue2;
            if (macDelta <= 15 && macDelta >= -15) {
                return true;
            }
        }
        return false;
    }

//    public List<HashMap<String, Object>> getTopTenWiFiTraffic(Equipment userEquipment, long minutes, String field) throws Exception {
//        List<HashMap<String, Object>> topDevices = new ArrayList<>();
//        Map<String, HashMap<String, Object>> allDevicesSumMap = new HashMap<>();
//
//        ArrayList<String> extenderWanMac = getExtenderWanMacAddress(userEquipment.getRgwSerial());
//
//        Double totalTraffic;
//
//        switch (field) {
//
//            case DOWN_STREAM:
//                field = "rx";
//                allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, field);
//                totalTraffic = allDevicesSumMap.values().stream().mapToDouble(p -> Double.valueOf(p.get("rx").toString())).sum();
//                if (totalTraffic == 0)
//                    totalTraffic = 1.0;
//                if(extenderWanMac != null && !extenderWanMac.isEmpty()) {
//                    topDevices = allDevicesSumMap.values().stream().filter((HashMap<String, Object> m) -> !isExtender(extenderWanMac, String.valueOf(m.get("macAddress")))).sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("rx").toString()))).reversed()).limit(10).collect(Collectors.toList());
//                } else {
//                    topDevices = allDevicesSumMap.values().stream().sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("rx").toString()))).reversed()).limit(10).collect(Collectors.toList());
//                }
//                if (Objects.nonNull(topDevices)) {
//                    topDevices = populateDeviceNameArgMap(topDevices, userEquipment);
//                }
//                for (Map<String, Object> item : topDevices) {
//                    item.put("wifiTrafficPercentage", TWO_DECIMAL_PLACE.format((((((Double.valueOf(item.get("rx").toString()))) * 1.0)) / totalTraffic) * 100));
//                    item.put("wifiTraffic", TWO_DECIMAL_PLACE.format(Double.valueOf(item.get("rx").toString())));
//                }
//                break;
//
//            case UP_STREAM:
//                field = "tx";
//                allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, field);
//                totalTraffic = allDevicesSumMap.values().stream().mapToDouble(p -> Double.valueOf(p.get("tx").toString())).sum();
//                if (totalTraffic == 0)
//                    totalTraffic = 1.0;
//                if(extenderWanMac != null && !extenderWanMac.isEmpty()) {
//                    topDevices = allDevicesSumMap.values().stream().filter((HashMap<String, Object> m) -> !isExtender(extenderWanMac, String.valueOf(m.get("macAddress")))).sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("tx").toString()))).reversed()).limit(10).collect(Collectors.toList());
//                } else {
//                    topDevices = allDevicesSumMap.values().stream().sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("tx").toString()))).reversed()).limit(10).collect(Collectors.toList());
//                }
//                if (Objects.nonNull(topDevices)) {
//                    topDevices = populateDeviceNameArgMap(topDevices, userEquipment);
//                }
//                for (Map<String, Object> item : topDevices) {
//                    item.put("wifiTrafficPercentage", TWO_DECIMAL_PLACE.format((((((Double.valueOf(item.get("tx").toString()))) * 1.0)) / totalTraffic) * 100));
//                    item.put("wifiTraffic", TWO_DECIMAL_PLACE.format(Double.valueOf(item.get("tx").toString())));
//                }
//                break;
//
//            default:
//                field = "totalTxRx";
//                allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, null);
//                totalTraffic = allDevicesSumMap.values().stream().mapToDouble(p -> Double.valueOf(p.get("totalTxRx").toString())).sum();
//                if (totalTraffic == 0)
//                    totalTraffic = 1.0;
//                if(extenderWanMac != null && !extenderWanMac.isEmpty()) {
//                    topDevices = allDevicesSumMap.values().stream().filter((HashMap<String, Object> m) -> !isExtender(extenderWanMac, String.valueOf(m.get("macAddress")))).sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("totalTxRx").toString()))).reversed()).limit(10).collect(Collectors.toList());
//                } else {
//                    topDevices = allDevicesSumMap.values().stream().sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("totalTxRx").toString()))).reversed()).limit(10).collect(Collectors.toList());
//                }
//                if (Objects.nonNull(topDevices)) {
//                    topDevices = populateDeviceNameArgMap(topDevices, userEquipment);
//                }
//                for (Map<String, Object> item : topDevices) {
//                    item.put("wifiTrafficPercentage", TWO_DECIMAL_PLACE.format((((((Double.valueOf(item.get("totalTxRx").toString()))) * 1.0)) / totalTraffic) * 100));
//                    item.put("wifiTraffic", TWO_DECIMAL_PLACE.format(Double.valueOf(item.get("totalTxRx").toString())));
//                }
//                break;
//
//        }
//
//        return topDevices;
//    }

    public List<HashMap<String, Object>> getTopTenWiFiTraffic(Equipment userEquipment, long minutes, String field) throws Exception {
        List<HashMap<String, Object>> topDevices = new ArrayList<>();
        ArrayList<String> extenderWanMac = getExtenderWanMacAddress(userEquipment.getRgwSerial());

        String trafficField;
        switch (field) {
            case DOWN_STREAM:
                trafficField = "rx";
                break;
            case UP_STREAM:
                trafficField = "tx";
                break;
            default:
                trafficField = "totalTxRx";
                break;
        }

        Map<String, HashMap<String, Object>> allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, null);
        double totalTraffic = allDevicesSumMap.values().stream()
                .mapToDouble(p -> Double.parseDouble(p.get(trafficField).toString()))
                .sum();
        totalTraffic = totalTraffic == 0 ? 1.0 : totalTraffic;

        topDevices = filterAndSortDevices(allDevicesSumMap, extenderWanMac, trafficField);
        topDevices = populateDeviceNameArgMap(topDevices, userEquipment);

        final double finalTotalTraffic = totalTraffic;
        topDevices.forEach(item -> {
            double traffic = Double.parseDouble(item.get(trafficField).toString());
            item.put("wifiTrafficPercentage", TWO_DECIMAL_PLACE.format((traffic / finalTotalTraffic) * 100));
            item.put("wifiTraffic", TWO_DECIMAL_PLACE.format(traffic));
        });

        return topDevices;
    }

    private List<HashMap<String, Object>> filterAndSortDevices(
            Map<String, HashMap<String, Object>> allDevicesSumMap,
            ArrayList<String> extenderWanMac,
            String trafficField) {
        Stream<HashMap<String, Object>> deviceStream = allDevicesSumMap.values().stream();

        if (extenderWanMac != null && !extenderWanMac.isEmpty()) {
            deviceStream = deviceStream.filter(device -> !isExtender(extenderWanMac, String.valueOf(device.get("macAddress"))));
        }

        return deviceStream
                .sorted(comparing(device -> Double.parseDouble(device.get(trafficField).toString()), Comparator.reverseOrder()))
                .limit(10)
                .collect(Collectors.toList());
    }


    public List<HashMap<String, Object>> getTopTenDevices(Equipment userEquipment, long minutes, String field) throws Exception {
        List<HashMap<String, Object>> topDevices = new ArrayList<>();
        Map<String, HashMap<String, Object>> allDevicesSumMap = new HashMap<>();

        ArrayList<String> extenderWanMac = getExtenderWanMacAddress(userEquipment.getRgwSerial());

        switch (field) {
            case BANDWIDTH:
                allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, field);
                if (Objects.isNull(allDevicesSumMap) || allDevicesSumMap.isEmpty()) {
                    return topDevices;
                }

                for (Map<String, Object> item : topDevices) {
                    item.put("bandwidth", FOUR_DECIMAL_PLACE.format(Double.valueOf(item.get("bandwidth").toString()) / (1000F * 1000F)));
                }
                break;

            case WIFI_TRAFFIC:
                field = "rx";
                allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, field);
                Double totalTraffic = allDevicesSumMap.values().stream().mapToDouble(p -> Double.valueOf(p.get("rx").toString())).sum();
                if (totalTraffic == 0)
                    totalTraffic = 1.0;
                if(extenderWanMac != null && !extenderWanMac.isEmpty()) {
                    topDevices = allDevicesSumMap.values().stream().filter((HashMap<String, Object> m) -> !isExtender(extenderWanMac, String.valueOf(m.get("macAddress")))).sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("rx").toString()))).reversed()).limit(10).collect(Collectors.toList());
                } else {
                    topDevices = allDevicesSumMap.values().stream().sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get("rx").toString()))).reversed()).limit(10).collect(Collectors.toList());
                }
                if (Objects.nonNull(topDevices)) {
                    topDevices = populateDeviceNameArgMap(topDevices, userEquipment);
                }
                for (Map<String, Object> item : topDevices) {
                    item.put("wifiTrafficPercentage", TWO_DECIMAL_PLACE.format((((((Double.valueOf(item.get("rx").toString()))) * 1.0)) / totalTraffic) * 100));
                    item.put("wifiTraffic", TWO_DECIMAL_PLACE.format(Double.valueOf(item.get("rx").toString())));
                }
                return topDevices;

            case UP_STREAM:
                field = "tx";
                allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, null);
                if (Objects.isNull(allDevicesSumMap) || allDevicesSumMap.isEmpty()) {
                    return topDevices;
                }
                break;

            case DOWN_STREAM:
                field = "rx";
                allDevicesSumMap = processDevices(userEquipment.getRgwSerial(), minutes, null);
                if (Objects.isNull(allDevicesSumMap) || allDevicesSumMap.isEmpty()) {
                    return topDevices;
                }
                break;

            default:
                field = "totalTxRx";
                allDevicesSumMap = processDevicesV2(userEquipment.getRgwSerial(), minutes, null);
                if (Objects.isNull(allDevicesSumMap) || allDevicesSumMap.isEmpty()) {
                    return topDevices;
                }
                break;

        }

        final String key = field;
        if(extenderWanMac != null && !extenderWanMac.isEmpty()) {
            topDevices = allDevicesSumMap.values().stream().filter((HashMap<String, Object> m) -> !isExtender(extenderWanMac, String.valueOf(m.get("macAddress")))).sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get(key).toString()))).reversed()).limit(10).collect(Collectors.toList());
        } else {
            topDevices = allDevicesSumMap.values().stream().sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get(key).toString()))).reversed()).limit(10).collect(Collectors.toList());
        }
        if (Objects.nonNull(topDevices)) {
            topDevices = populateDeviceNameArgMap(topDevices, userEquipment);
        }


        return topDevices;
    }

    public List<HashMap<String, Object>> getTopTenDevicesInternet(Equipment userEquipment, long minutes, String field) throws
            Exception {
        List<HashMap<String, Object>> allDevices = new ArrayList<>();
        Map<String, HashMap<String, Object>> allDevicesSumMap;
        allDevicesSumMap = processDevicesInternet(userEquipment.getRgwSerial(), minutes);
        if (Objects.isNull(allDevicesSumMap) || allDevicesSumMap.isEmpty()) {
            return allDevices;
        }

        allDevices = allDevicesSumMap.values().stream().collect(Collectors.toList());

        if (field.equals(INTERNET_RX) || field.equals(INTERNET_TX) || field.equals(INTERNET_TRAFFIC)) {
            for (Map<String, Object> item : allDevices) {
                Double rx = Double.valueOf(FOUR_DECIMAL_PLACE.format(Double.valueOf(item.get(INTERNET_RX).toString()) / (1000F * 1000F)));
                Double tx = Double.valueOf(FOUR_DECIMAL_PLACE.format(Double.valueOf(item.get(INTERNET_TX).toString()) / (1000F * 1000F)));
                item.put(INTERNET_RX, rx);
                item.put(INTERNET_TX, tx);
                item.put(INTERNET_TRAFFIC, (tx + rx));
            }
        } else if (field.equals(INTERNET_TRAFFIC_PERCENT)) {
            //Discuss with john for sum of tx,rx or only rx
            Double totalTraffic = allDevices.stream().mapToDouble(p -> ((Objects.isNull(p.get(INTERNET_TX)) ? 0.0 : Double.valueOf(p.get(INTERNET_TX).toString())) + (Objects.isNull(p.get(INTERNET_RX)) ? 0.0 : Double.valueOf(p.get(INTERNET_RX).toString())))).sum();
            if (totalTraffic == 0)
                totalTraffic = 1.0;
            for (Map<String, Object> item : allDevices) {
                Double tx = Double.valueOf(String.valueOf(item.get(INTERNET_TX) == null ? 0.0 : item.get(INTERNET_TX)));
                Double rx = Double.valueOf(String.valueOf(item.get(INTERNET_RX) == null ? 0.0 : item.get(INTERNET_RX)));
                //Discuss with john for sum of tx,rx or only rx
                item.put("internetTrafficPercentage", TWO_DECIMAL_PLACE.format(((tx + rx) / totalTraffic) * 100));
                //Discuss with john for sum of tx,rx or only rx
                item.put(INTERNET_TRAFFIC_PERCENT, (tx + rx));
                item.remove(INTERNET_TX);
            }
        }

        String finalField = field;
        allDevices = allDevicesSumMap.values().stream().sorted(comparing((Map<String, Object> s) -> (Double.valueOf(s.get(finalField).toString()))).reversed()).limit(10).collect(Collectors.toList());
        if (Objects.nonNull(allDevices)) {
            allDevices = populateDeviceNameArgMap(allDevices, userEquipment);
        }

        return allDevices;
    }

    public DeviceWifiTrafficDTO getInternetTrafficByDevice(String equipmentIdOrSerialOrSTN, long minutes) throws
            Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        List<HashMap<String, Object>> topTenDevicesDataList;
        topTenDevicesDataList = getTopTenDevicesInternet(userEquipment, minutes, INTERNET_TRAFFIC_PERCENT);

        DeviceWifiTrafficDTO deviceWifiTrafficDTO = new DeviceWifiTrafficDTO();
        ArrayList<DeviceWifiTrafficDTO.DeviceWifiTrafficData> deviceWifiTrafficDataList = new ArrayList<>();
        for (HashMap<String, Object> device : topTenDevicesDataList) {
            DeviceWifiTrafficDTO.DeviceWifiTrafficData deviceWifiTrafficData = deviceWifiTrafficDTO.new DeviceWifiTrafficData();
            deviceWifiTrafficData.setMacAddress(Objects.isNull(device.get("macAddress")) ? null : device.get("macAddress").toString());
            deviceWifiTrafficData.setWifiTrafficPercentage(Objects.isNull(device.get("internetTrafficPercentage")) ? 0.0 : Double.valueOf(device.get("internetTrafficPercentage").toString()));
            deviceWifiTrafficData.setTraffic(Objects.isNull(device.get(INTERNET_TRAFFIC_PERCENT)) ? 0.0 : Double.valueOf(device.get(INTERNET_TRAFFIC_PERCENT).toString()));

            HashMap<String, String> queryParams = new HashMap<>();
            HashMap<String, String> appendableParams = new HashMap<>();
            queryParams.put("userId", userEquipment.getRgwSerial());
            queryParams.put("macAddress", Objects.isNull(device.get("macAddress")) ? null : device.get("macAddress").toString());

            BasicDBObject mongoFieldOptions = new BasicDBObject();
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);

            DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

            deviceWifiTrafficData.setFromDSHost(!Objects.nonNull(deviceDetails));
            deviceWifiTrafficData.setName(manageCommonService.getDisplayNameByUserIdMacAddr(userEquipment.getRgwSerial(), String.valueOf(device.get("macAddress"))));

            if (StringUtils.isBlank(deviceWifiTrafficData.getName())) {
                if (deviceWifiTrafficData.isFromDSHost()) {
                    deviceWifiTrafficData.setName(manageCommonService.getDisplayNameByPriorityForEthernetMoCADevices(new BasicDBObject("macAddress", device.get("macAddress")).append("userId", userEquipment.getRgwSerial())));
                } else {
                    deviceWifiTrafficData.setName(Objects.isNull(device.get("name")) ? null : device.get("name").toString());
                }
            }

            deviceWifiTrafficDataList.add(deviceWifiTrafficData);
        }
        if (deviceWifiTrafficDataList.isEmpty()) {
            DeviceWifiTrafficDTO.DeviceWifiTrafficData deviceWifiTrafficData = deviceWifiTrafficDTO.new DeviceWifiTrafficData();
            deviceWifiTrafficDataList.add(deviceWifiTrafficData);
        }

        deviceWifiTrafficDTO.setData(deviceWifiTrafficDataList);
        return deviceWifiTrafficDTO;
    }

    private Map<String, HashMap<String, Object>> processDevices(String userId, long duration, String field) {
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        HashMap<String, HashMap<String, Object>> allDeviceSum = new HashMap<>();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long minutes = duration;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        params.put("userId", userId);
        params.put("dateHour", dateCriteria);

        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(params, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);

        if (Objects.nonNull(deviceDownLinkUpLinkBytes) && !deviceDownLinkUpLinkBytes.isEmpty()) {
            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
                String today = String.valueOf(dbObject.get("date"));
                HashMap<String, Object> devMap = (HashMap<String, Object>) dbObject.get("devMap");
                if (Objects.nonNull(devMap)) {
                    for (Map.Entry<String, Object> entry : devMap.entrySet()) {
                        HashMap<String, Object> values = allDeviceSum.get(entry.getKey());
                        if (Objects.nonNull(values)) {
                            Double sum = 0.0;
                            Double totalTx = 0.0;
                            Double totalRx = 0.0;
                            if (Objects.isNull(field)) {
                                sum = Objects.nonNull(values.get("totalTxRx")) ? Double.valueOf(values.get("totalTxRx").toString()) : 0;
                                totalTx = Objects.nonNull(values.get("tx")) ? Double.valueOf(values.get("tx").toString()) : 0;
                                totalRx = Objects.nonNull(values.get("rx")) ? Double.valueOf(values.get("rx").toString()) : 0;
                            } else {
                                sum = Objects.nonNull(values.get(field)) ? Double.valueOf(values.get(field).toString()) : 0;
                            }

                            List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                            if (Objects.nonNull(dataList)) {
                                Calendar criteriaCalendar = Calendar.getInstance();
                                criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
                                long currTimeStamp = criteriaCalendar.getTimeInMillis();
                                dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());

                                if (Objects.isNull(field)) {
                                    HashMap<String, Object> data = new HashMap<>();
                                    data.put("macAddress", entry.getKey());

                                    totalTx = totalTx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("tx").toString())).sum();
                                    totalRx = totalRx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("rx").toString())).sum();
                                    sum = totalRx + totalTx;

                                    data.put("tx", totalTx);
                                    data.put("rx", totalRx);
                                    data.put("totalTxRx", sum);

                                    allDeviceSum.put(entry.getKey(), data);
                                } else {
                                    sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0.0")).sum();
                                    HashMap<String, Object> data = new HashMap<>();
                                    data.put("macAddress", entry.getKey());
                                    data.put(field, sum);

                                    allDeviceSum.put(entry.getKey(), data);
                                }
                            }
                        } else {
                            Double sum = 0.0;
                            Double totalTx = 0.0;
                            Double totalRx = 0.0;
                            List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                            if (Objects.nonNull(dataList)) {
                                Calendar criteriaCalendar = Calendar.getInstance();
                                criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
                                long currTimeStamp = criteriaCalendar.getTimeInMillis();
                                dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());

                                if (Objects.isNull(field)) {
                                    HashMap<String, Object> data = new HashMap<>();
                                    data.put("macAddress", entry.getKey());

                                    totalTx = totalTx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("tx").toString())).sum();
                                    totalRx = totalRx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("rx").toString())).sum();
                                    sum = totalRx + totalTx;

                                    data.put("tx", totalTx);
                                    data.put("rx", totalRx);
                                    data.put("totalTxRx", sum);

                                    allDeviceSum.put(entry.getKey(), data);
                                } else {
                                    sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0.0")).sum();

                                    HashMap<String, Object> data = new HashMap<>();
                                    data.put("macAddress", entry.getKey());
                                    data.put(field, sum);

                                    allDeviceSum.put(entry.getKey(), data);
                                }
                            }
                        }
                    }
                }


                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        HashMap<String, Object> latestDevMap = (HashMap<String, Object>) userSlidingData.get("devMap");
                        if (Objects.nonNull(latestDevMap)) {
                            for (Map.Entry<String, Object> entry : latestDevMap.entrySet()) {
                                HashMap<String, Object> values = allDeviceSum.get(entry.getKey());
                                if (Objects.nonNull(values)) {
                                    Double sum = 0.0;
                                    Double totalTx = 0.0;
                                    Double totalRx = 0.0;
                                    if (Objects.isNull(field)) {
                                        sum = Objects.nonNull(values.get("totalTxRx")) ? Double.valueOf(values.get("totalTxRx").toString()) : 0;
                                        totalTx = Objects.nonNull(values.get("tx")) ? Double.valueOf(values.get("tx").toString()) : 0;
                                        totalRx = Objects.nonNull(values.get("rx")) ? Double.valueOf(values.get("rx").toString()) : 0;
                                    } else {
                                        sum = Objects.nonNull(values.get(field)) ? Double.valueOf(values.get(field).toString()) : 0;
                                    }

                                    List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                                    if (Objects.nonNull(dataList)) {
                                        Calendar criteriaCalendar = Calendar.getInstance();
                                        criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
                                        long currTimeStamp = criteriaCalendar.getTimeInMillis();
                                        dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());

                                        if (Objects.isNull(field)) {
                                            HashMap<String, Object> data = new HashMap<>();
                                            data.put("macAddress", entry.getKey());

                                            totalTx = totalTx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("tx").toString())).sum();
                                            totalRx = totalRx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("rx").toString())).sum();
                                            sum = totalRx + totalTx;

                                            data.put("tx", totalTx);
                                            data.put("rx", totalRx);
                                            data.put("totalTxRx", sum);

                                            allDeviceSum.put(entry.getKey(), data);
                                        } else {
                                            sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0.0")).sum();
                                            HashMap<String, Object> data = new HashMap<>();
                                            data.put("macAddress", entry.getKey());
                                            data.put(field, sum);

                                            allDeviceSum.put(entry.getKey(), data);
                                        }
                                    }
                                } else {
                                    Double sum = 0.0;
                                    Double totalTx = 0.0;
                                    Double totalRx = 0.0;
                                    List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                                    if (Objects.nonNull(dataList)) {
                                        Calendar criteriaCalendar = Calendar.getInstance();
                                        criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
                                        long currTimeStamp = criteriaCalendar.getTimeInMillis();
                                        dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
                                        if (Objects.isNull(field)) {
                                            HashMap<String, Object> data = new HashMap<>();
                                            data.put("macAddress", entry.getKey());

                                            totalTx = totalTx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("tx").toString())).sum();
                                            totalRx = totalRx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(p.get("rx").toString())).sum();
                                            sum = totalRx + totalTx;

                                            data.put("tx", totalTx);
                                            data.put("rx", totalRx);
                                            data.put("totalTxRx", sum);

                                            allDeviceSum.put(entry.getKey(), data);
                                        } else {
                                            sum = sum + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(field)) ? p.get(field).toString() : "0.0")).sum();

                                            HashMap<String, Object> data = new HashMap<>();
                                            data.put("macAddress", entry.getKey());
                                            data.put(field, sum);

                                            allDeviceSum.put(entry.getKey(), data);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return allDeviceSum;
    }

    private Map<String, HashMap<String, Object>> processDevicesV2(String userId, long duration, String field) {
        Map<String, HashMap<String, Object>> allDeviceSum = new HashMap<>();

        long cutoffTime = System.currentTimeMillis() - (duration < 60 ? 60 * 60 * 1000L : duration * 60 * 1000L);

        BasicDBObject dateCriteria = new BasicDBObject("$gte", manageCommonService.convertMinutesToDateHour(duration));

        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("dateHour", dateCriteria);

        BasicDBObject mongoFieldOptions = new BasicDBObject("_id", 0);

        List<DBObject> records = mongoService.findList(params, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);
        if (records == null || records.isEmpty()) return allDeviceSum;

        for (DBObject dbObject : records) {
            processDevMap((Map<String, Object>) dbObject.get("devMap"), allDeviceSum, field, cutoffTime);

            String today = String.valueOf(dbObject.get("date"));
            if (dbObject.containsField(today)) {
                Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                for (Object userData : dataByDay.values()) {
                    Map<String, Object> nested = (Map<String, Object>) userData;
                    Map<String, Object> nestedDevMap = (Map<String, Object>) nested.get("devMap");
                    processDevMap(nestedDevMap, allDeviceSum, field, cutoffTime);
                }
            }
        }

        return allDeviceSum;
    }

    private void processDevMap(Map<String, Object> devMap, Map<String, HashMap<String, Object>> allDeviceSum,
                               String field, long cutoffTime) {
        if (devMap == null) return;

        for (Map.Entry<String, Object> entry : devMap.entrySet()) {
            String mac = entry.getKey();
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();

            if (dataList == null) continue;
            List<Map<String, Object>> filtered = dataList.stream()
                    .filter(e -> {
                        Object ts = e.get("timestamp");
                        return ts != null && Long.parseLong(ts.toString()) > cutoffTime;
                    }).collect(Collectors.toList());

            Map<String, Object> existing = allDeviceSum.get(mac);
            HashMap<String, Object> aggregated = aggregateData(mac, filtered, field, existing);
            if (aggregated != null) {
                allDeviceSum.put(mac, aggregated);
            }
        }
    }

    private HashMap<String, Object> aggregateData(String mac, List<Map<String, Object>> dataList,
                                                  String field, Map<String, Object> existing) {
        if (dataList.isEmpty()) return null;

        HashMap<String, Object> result = new HashMap<>();
        result.put("macAddress", mac);

        if (field == null) {
            double tx = existing != null && existing.get("tx") != null ? Double.parseDouble(existing.get("tx").toString()) : 0;
            double rx = existing != null && existing.get("rx") != null ? Double.parseDouble(existing.get("rx").toString()) : 0;

            tx += dataList.stream().mapToDouble(d -> Double.parseDouble(d.get("tx").toString())).sum();
            rx += dataList.stream().mapToDouble(d -> Double.parseDouble(d.get("rx").toString())).sum();

            result.put("tx", tx);
            result.put("rx", rx);
            result.put("totalTxRx", tx + rx);
        } else {
            double sum = existing != null && existing.get(field) != null ? Double.parseDouble(existing.get(field).toString()) : 0;
            sum += dataList.stream()
                    .mapToDouble(d -> Double.parseDouble(String.valueOf(d.getOrDefault(field, "0.0"))))
                    .sum();

            result.put(field, sum);
        }

        return result;
    }

    private Map<String, HashMap<String, Object>> processDevicesInternet(String userId, long duration) {
        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        HashMap<String, HashMap<String, Object>> allDeviceSum = new HashMap<>();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        long minutes = duration;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        params.put("userId", userId);
        params.put("dateHour", dateCriteria);

        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(params, null, DS_HOST_INSIGHTS, mongoFieldOptions);

        if (Objects.nonNull(deviceDownLinkUpLinkBytes) && !deviceDownLinkUpLinkBytes.isEmpty()) {
            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
                String today = String.valueOf(dbObject.get("date"));
                HashMap<String, Object> devMap = new HashMap<>();
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    devMap = (HashMap<String, Object>) dbObject.get("devMap");
                    getSumOfAllDevices(duration, allDeviceSum, devMap);
                } else {
                    if (Objects.nonNull(dbObject.get(today))) {
                        Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                        for (String key : dataByDay.keySet()) {
                            Map<String, Map<String, Object>> dsHostInsightData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                            HashMap<String, Object> latestDevMap = (HashMap<String, Object>) dsHostInsightData.get("devMap");
                            getSumOfAllDevices(duration, allDeviceSum, latestDevMap);
                        }
                    }
                }

            }
        }

        return allDeviceSum;
    }

    private void getSumOfAllDevices(long duration, HashMap<String, HashMap<String, Object>> allDeviceSum, HashMap<String, Object> devMap) {
        for (Map.Entry<String, Object> entry : devMap.entrySet()) {
            HashMap<String, Object> values = allDeviceSum.get(entry.getKey());
            if (Objects.nonNull(values)) {
                Double sumTx = Objects.nonNull(values.get(INTERNET_TX)) ? Double.valueOf(values.get(INTERNET_TX).toString()) : 0;
                Double sumRx = Objects.nonNull(values.get(INTERNET_RX)) ? Double.valueOf(values.get(INTERNET_RX).toString()) : 0;
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                if (Objects.nonNull(dataList)) {
                    Calendar criteriaCalendar = Calendar.getInstance();
                    criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
                    long currTimeStamp = criteriaCalendar.getTimeInMillis();
                    dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
                    try {
                        sumTx = sumTx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(INTERNET_TX)) ? p.get(INTERNET_TX).toString() : "0.0")).sum();
                        sumRx = sumRx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(INTERNET_RX)) ? p.get(INTERNET_RX).toString() : "0.0")).sum();
                    } catch (Exception e) {
                    }

                    HashMap<String, Object> data = new HashMap<>();
                    data.put("macAddress", entry.getKey());
                    data.put(INTERNET_TX, sumTx);
                    data.put(INTERNET_RX, sumRx);

                    allDeviceSum.put(entry.getKey(), data);
                }
            } else {
                Double sumTx = 0.0;
                Double sumRx = 0.0;
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                if (Objects.nonNull(dataList)) {
                    Calendar criteriaCalendar = Calendar.getInstance();
                    criteriaCalendar.setTime(new Date(new Date().getTime() - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
                    long currTimeStamp = criteriaCalendar.getTimeInMillis();
                    dataList = dataList.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
                    try {
                        sumTx = sumTx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(INTERNET_TX)) ? p.get(INTERNET_TX).toString() : "0.0")).sum();
                        sumRx = sumRx + dataList.parallelStream().mapToDouble(p -> Double.valueOf(Objects.nonNull(p.get(INTERNET_RX)) ? p.get(INTERNET_RX).toString() : "0.0")).sum();

                    } catch (Exception e) {
                    }
                    HashMap<String, Object> data = new HashMap<>();
                    data.put("macAddress", entry.getKey());
                    data.put(INTERNET_TX, sumTx);
                    data.put(INTERNET_RX, sumRx);

                    allDeviceSum.put(entry.getKey(), data);
                }
            }
        }
    }

    private List<HashMap<String, Object>> populateDeviceNameArgMap
            (List<HashMap<String, Object>> dataList, Equipment userEquipment) {
        Set<String> deviceMacAddr = dataList.stream().map(p -> String.valueOf(p.get("macAddress"))).collect(Collectors.toSet());

        HashMap<String, String> deviceFriendlyName = manageCommonService.getFriendlyNameByStation(deviceMacAddr, userEquipment);
        dataList.forEach(item -> {
            String friendlyName = deviceFriendlyName.get(String.valueOf(item.get("macAddress")));
            if(friendlyName == null || friendlyName.isEmpty()) {
                item.put("name", manageCommonService.getHostNameByGateway(String.valueOf(item.get("macAddress")), userEquipment));
                LOG.debug("Get hostname by gateway:[{}] macAddress:[{}] hostname:[{}]", userEquipment.getRgwSerial(), item.get("macAddress"), item.get("name"));
            } else {
                item.put("name", friendlyName);
            }
        });
        return dataList;
    }

    public DeviceWifiThroughputDTO fetchWifiThroughput(String equipmentIdOrSerialOrSTN, String macAddr, Long
            duration) throws Exception {
        manageCommonService.isDurationValid(duration);
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddr, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddr, userEquipment);
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        long minutes = duration < 60 ? 60 : duration;

        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
        aggregationWhereClause.put("dateHour", dateCriteria);

        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
//        mongoFieldOptions.put("devMap." + macAddr, 1);
        List<DBObject> deviceDownlinkUplinkBytes = mongoService.findList(aggregationWhereClause, null, USER_STATION_SLIDING_DATA, mongoFieldOptions);

        List<Map<String, Object>> devMapData;
        List<Map<String, Object>> actualDevMapData = new ArrayList<>();
        List<Map<String, Object>> combinedData = new ArrayList<>();
        DeviceWifiThroughputDTO deviceWifiThroughputDTO = new DeviceWifiThroughputDTO();
        ArrayList<DeviceWifiThroughputDTO.DeviceWifiThroughputData> deviceWifiThroughputDataList = new ArrayList<>();

        if (!deviceDownlinkUplinkBytes.isEmpty()) {
            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            for (DBObject dbObject : deviceDownlinkUplinkBytes) {
                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    devMapData = (List<Map<String, Object>>) ((DBObject) dbObject.get("devMap")).get(macAddr);
                    if (Objects.nonNull(devMapData) && !devMapData.isEmpty()) {
                        combinedData.addAll(devMapData);
                    }
                }

                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<Map<String, Object>> latestDevMap = (List<Map<String, Object>>) userSlidingData.get("devMap").get(macAddr);
                        if (Objects.nonNull(latestDevMap) && !latestDevMap.isEmpty()) {
                            combinedData.addAll(latestDevMap);
                        }
                    }
                }
            }
            combinedData = combinedData.stream().filter(element -> (Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp).collect(Collectors.toList());
            if (!combinedData.isEmpty())
                actualDevMapData.addAll(combinedData);
            if (!actualDevMapData.isEmpty()) {
                for (Map<String, Object> throughputItem : actualDevMapData) {
                    if ((Objects.nonNull(throughputItem.get("avgRx")) && Objects.nonNull(throughputItem.get("avgTx")))) {
                        DeviceWifiThroughputDTO.DeviceWifiThroughputData deviceWifiThroughputData = deviceWifiThroughputDTO.new DeviceWifiThroughputData();
                        deviceWifiThroughputData.setRx(Objects.isNull(throughputItem.get("avgRx")) ? 0.0 : Double.valueOf(TWO_DECIMAL_PLACE.format(Objects.isNull(throughputItem.get("avgRx")) ? 0.0 : Double.parseDouble(throughputItem.get("avgRx").toString()))));
                        deviceWifiThroughputData.setTx(Objects.isNull(throughputItem.get("avgTx")) ? 0.0 : Double.valueOf(TWO_DECIMAL_PLACE.format(Objects.isNull(throughputItem.get("avgTx")) ? 0.0 : Double.parseDouble(throughputItem.get("avgTx").toString()))));
                        deviceWifiThroughputData.setTimestamp(Objects.isNull(throughputItem.get("timestamp")) ? 0 : Long.valueOf(throughputItem.get("timestamp").toString()));
                        deviceWifiThroughputDataList.add(deviceWifiThroughputData);
                    }
                }

                Collections.sort(deviceWifiThroughputDataList);
                if (!deviceWifiThroughputDataList.isEmpty() && duration < 60) {
                    Collections.reverse(deviceWifiThroughputDataList);
                    deviceWifiThroughputDataList.addAll(deviceWifiThroughputDataList.subList(0, Integer.valueOf(duration.toString())));
                    Collections.sort(deviceWifiThroughputDataList);
                }
            }
        }

        if (deviceWifiThroughputDataList.isEmpty()) {
            DeviceWifiThroughputDTO.DeviceWifiThroughputData deviceWifiThroughputData = deviceWifiThroughputDTO.new DeviceWifiThroughputData();
            deviceWifiThroughputDataList.add(deviceWifiThroughputData);
        }
        deviceWifiThroughputDTO.setData(deviceWifiThroughputDataList);

        return deviceWifiThroughputDTO;
    }

    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.DEVICE_SPEED_TEST)
    public DeviceSpeedTestDTO performStationSpeedTestForUserDevice(String equipmentIdOrSerialOrSTN, String
            serialNo, String macAddr, String ipAddr, String band, HttpServletRequest httpServletRequest) throws Exception {

//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddr, userEquipment);
        manageCommonService.checkSerialNumOfEquipment(serialNo, userEquipment);

        manageCommonService.checkAPSerialNumBelongsToSubscriber(serialNo, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddr, userEquipment);

//          manageCommonService.isEquipmentOffline(userAP.getApId(), userAP);

        HashMap<String, Object> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);
        BasicDBObject fieldsToRemove = new BasicDBObject();
        fieldsToRemove.put("_id", 0);

        DBObject device = mongoService.findOne(queryParams, new HashMap<>(), STATION_DETAIL, fieldsToRemove);
        if (Objects.isNull(device)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device with macAddress :: " + macAddr + " does not exist.");
        }

        if (manageCommonService.getCurrentDevStatus(device))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Cannot perform operation, Device is offline.");

        String rpcUri = "/cpe-api/diag/" + macAddr + "/phyRate";
        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer maxTries = Integer.valueOf(equipmentProps.get(WAN_SPEED_TEST_RPC_POLL_COUNT));
        Date sendRpcTime = new Date();
        Map<String, Object> rpcResult = cpeRpcService.sendRpcAndWaitResult(null, userEquipment.getRgwSerial(), serialNo, rpcUri, "GET", "\"\"", maxTries, THREAD_TO_SLEEP);

        DeviceSpeedTestDTO deviceSpeedTestDTO = new DeviceSpeedTestDTO();
        DeviceSpeedTestDTO.DeviceSpeedTestData deviceSpeedTestData = deviceSpeedTestDTO.new DeviceSpeedTestData();

        BasicDBObject stationSpeedTest = new BasicDBObject();

        if (StringUtils.equals(String.valueOf(rpcResult.get("code")), "200")) {

            deviceSpeedTestData.setUserId(userEquipment.getRgwSerial());
            deviceSpeedTestData.setBand(band);
            deviceSpeedTestData.setIpAddr(ipAddr);
            deviceSpeedTestData.setMacAddr(macAddr);
            deviceSpeedTestData.setDate(sendRpcTime.getTime());

            stationSpeedTest.put("_id", CommonUtils.generateUUID());
            stationSpeedTest.put("userId", userEquipment.getRgwSerial());
            stationSpeedTest.put("band", band);
            stationSpeedTest.put("ipAddr", ipAddr);
            stationSpeedTest.put("macAddr", macAddr);
            stationSpeedTest.put("date", sendRpcTime);

            if (rpcResult.get("payload") != null) {
                List<Object> respPayloadList = (List) rpcResult.get("payload");
                Map<String, Object> respPayloadMap = (Map) respPayloadList.get(0);
                Float txRate = Float.valueOf(respPayloadMap.get("tx").toString()) / 1000;
                Float rxRate = Float.valueOf(respPayloadMap.get("rx").toString()) / 1000;
                Double dataRate = (txRate+rxRate) * 0.3 / 1000;

                deviceSpeedTestData.setRxRate(rxRate);
                deviceSpeedTestData.setTxRate(txRate);
                deviceSpeedTestData.setResult(EXECUTE_OK);
                deviceSpeedTestData.setDataRate(dataRate.floatValue());

                stationSpeedTest.put("rxRate", rxRate);
                stationSpeedTest.put("txRate", txRate);
                stationSpeedTest.put("dataRate", dataRate);
                stationSpeedTest.put("result", EXECUTE_OK);
            } else {
                deviceSpeedTestData.setRxRate(0.0f);
                deviceSpeedTestData.setTxRate(0.0f);
                deviceSpeedTestData.setResult(EXECUTE_FAIL);
                deviceSpeedTestData.setDataRate(0.0f);

                stationSpeedTest.put("rxRate", 0);
                stationSpeedTest.put("txRate", 0);
                stationSpeedTest.put("dataRate", 0);
                stationSpeedTest.put("result", EXECUTE_FAIL);
            }
        } else {
            LOG.error("response: {}", rpcResult.toString());
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Device Speed Test RPC Failed");
        }

        mongoService.create(STATION_SPEED_TEST_INFO, stationSpeedTest);
        deviceSpeedTestDTO.setData(deviceSpeedTestData);
        return deviceSpeedTestDTO;
    }

    public ApiResponseDTO getDeviceSteeringEventsByMACAddress(String equipmentIdOrSerialOrSTN, String macAddress,
                                                              long duration, String type) throws Exception {
        manageCommonService.isDurationValid(duration);
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddress, userEquipment);
        manageCommonService.checkDeviceMACBelongsToUser(macAddress, userEquipment);

        HashMap<String, String> commonProps = commonService.read(COMMON_CONFIG);
        boolean flag = (Objects.nonNull(commonProps.get(ABS_DATABASE)) && commonProps.get(ABS_DATABASE).equalsIgnoreCase("MONGO")) ? true : false;
        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        long currentTimestamp = Calendar.getInstance().getTimeInMillis();

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, Object> param = new HashMap<>();
        param.put("userId", userEquipment.getRgwSerial());
        param.put("macAddress", macAddress);

        HashMap<String, Object> rangeParam = new HashMap<>();
        rangeParam.put("key", TIMESTAMP);
        rangeParam.put("operand", "gt");

        HashMap<String, Object> inparams = new HashMap<>();

        List<String> projection = new ArrayList<>();
        if (type.equals(evAssociation)) {
            // Collect ALL BSSID's from all Events (Except Steering Logs since they do not have BSSID)
            List<EvAssociation> associationEvents;
            if (flag) {
                List<DBObject> associationEventsFromMongo = mongoService.findListByTimestamp(macAddress, MAC_ADDRESS, DEVICE_ASSOCIATION, TIMESTAMP, duration, DESC, mongoFieldOptions);
                associationEvents = manageCommonService.convertToAssocEvent(associationEventsFromMongo);
            } else {
                associationEvents = (List<EvAssociation>) cassandraRepository.read(EvAssociation.class, param, rangeParam, null, projection, duration);
            }
            associationEvents = associationEvents.stream().filter(associationEvent -> String.valueOf(associationEvent.getUserId()).equals(userEquipment.getRgwSerial())).collect(Collectors.toList());
            HashSet<String> bssidSets = (HashSet<String>) associationEvents.stream().map(event -> String.valueOf(event.getBssid())).collect(Collectors.toSet());
            HashMap<String, Object> bssidDetails = getBssidDetails(bssidSets, userEquipment);

            DeviceAssociationDTO deviceAssociationDTO = new DeviceAssociationDTO();
            HashSet<DeviceAssociationDTO.DeviceAssociationData> deviceAssociationDataList = new HashSet<>();
            associationEvents.forEach(associationEvent -> {
                HashMap<String, String> detailsMap = (HashMap<String, String>) bssidDetails.get(String.valueOf(associationEvent.getBssid()));
                if (Objects.nonNull(detailsMap) && Objects.nonNull(associationEvent.getTimestamp()) && Long.valueOf(String.valueOf(associationEvent.getTimestamp())) <= currentTimestamp) {
                    DeviceAssociationDTO.DeviceAssociationData deviceAssociationData = deviceAssociationDTO.new DeviceAssociationData();
                    deviceAssociationData.setAssociatedTo("Associated to " + detailsMap.get("friendlyName") + " / " + (dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : detailsMap.get("ssid") + " / " + detailsMap.get("band")));
                    deviceAssociationData.setMacAddress(Objects.isNull(associationEvent.getMacAddress()) ? null : associationEvent.getMacAddress());
                    deviceAssociationData.setVendor(manageCommonService.getVendorName(String.valueOf(associationEvent.getMacAddress())));
                    deviceAssociationData.setTimestamp(Objects.isNull(associationEvent.getTimestamp()) ? 0 : Long.valueOf(String.valueOf(associationEvent.getTimestamp())));
                    deviceAssociationDataList.add(deviceAssociationData);
                }
            });
            if (deviceAssociationDataList.isEmpty()) {
                DeviceAssociationDTO.DeviceAssociationData deviceAssociationData = deviceAssociationDTO.new DeviceAssociationData();
                deviceAssociationDataList.add(deviceAssociationData);
            }
            deviceAssociationDTO.setData(deviceAssociationDataList);
            return deviceAssociationDTO;
        }

        if (type.equals(evDisassociation)) {
            List<EvDisassociation> disAssociationEvents;
            if (flag) {
                List<DBObject> disAssociationEventsFromMongo = mongoService.findListByTimestamp(macAddress, MAC_ADDRESS, DEVICE_DISASSOCIATION, TIMESTAMP, duration, DESC, mongoFieldOptions);
                disAssociationEvents = manageCommonService.convertToDisAssocEvent(disAssociationEventsFromMongo);
            } else {
                HashMap<String, Object> inparamsEVD = new HashMap<>();
                inparamsEVD.put("assocType", ASSOC_TYPE_LIST);
                disAssociationEvents = (List<EvDisassociation>) cassandraRepository.read(EvDisassociation.class, param, rangeParam, inparamsEVD, projection, duration);
            }
            disAssociationEvents = disAssociationEvents.stream().filter(disAssociationEvent -> String.valueOf(disAssociationEvent.getUserId()).equals(userEquipment.getRgwSerial())).collect(Collectors.toList());
            HashSet<String> bssidSets = (HashSet<String>) disAssociationEvents.stream().map(event -> String.valueOf(event.getBssid())).collect(Collectors.toSet());
            HashMap<String, Object> bssidDetails = getBssidDetails(bssidSets, userEquipment);

            DeviceDisAssociationDTO deviceDisAssociationDTO = new DeviceDisAssociationDTO();
            HashSet<DeviceDisAssociationDTO.DeviceDisAssociationData> deviceDisAssociationDataList = new HashSet<>();
            // Prepare Final Data
            disAssociationEvents.forEach(disAssociationEvent -> {
                HashMap<String, Object> detailsMap = (HashMap<String, Object>) bssidDetails.get(String.valueOf(disAssociationEvent.getBssid()));
                if (Objects.nonNull(detailsMap) && Objects.nonNull(disAssociationEvent.getTimestamp()) && Long.valueOf(String.valueOf(disAssociationEvent.getTimestamp())) <= currentTimestamp) {
                    DeviceDisAssociationDTO.DeviceDisAssociationData deviceDisAssociationData = deviceDisAssociationDTO.new DeviceDisAssociationData();
                    deviceDisAssociationData.setDisassociatedFrom("Disassociated from " + detailsMap.get("friendlyName") + " / " + (dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : detailsMap.get("ssid") + " / " + detailsMap.get("band")));
                    deviceDisAssociationData.setMacAddress(Objects.isNull(disAssociationEvent.getMacAddress()) ? null : disAssociationEvent.getMacAddress());
                    deviceDisAssociationData.setTimestamp(Objects.isNull(String.valueOf(disAssociationEvent.getTimestamp())) ? 0 : Long.valueOf(String.valueOf(disAssociationEvent.getTimestamp())));
                    deviceDisAssociationData.setVendor(manageCommonService.getVendorName(String.valueOf(disAssociationEvent.getMacAddress())));
                    deviceDisAssociationDataList.add(deviceDisAssociationData);
                }
            });

            if (deviceDisAssociationDataList.isEmpty()) {
                DeviceDisAssociationDTO.DeviceDisAssociationData deviceDisAssociationData = deviceDisAssociationDTO.new DeviceDisAssociationData();
                deviceDisAssociationDataList.add(deviceDisAssociationData);
            }
            deviceDisAssociationDTO.setData(deviceDisAssociationDataList);
            return deviceDisAssociationDTO;
        }

        if (type.equals(steer)) {
            HashMap where = new HashMap();
//            where.put(STEERING_END_CODE, 1);
            List<EvSteeringLogs> steeringEvents;
            if (flag) {
                List<DBObject> steeringEventsFromMongo = mongoService.findListByTimestamp(where, macAddress, MAC_ADDRESS, DEVICE_STEERING, TIMESTAMP, duration, DESC, mongoFieldOptions);
                steeringEvents = manageCommonService.convertToEVSteeringLog(steeringEventsFromMongo);
            } else {
                steeringEvents = (List<EvSteeringLogs>) cassandraRepository.read(EvSteeringLogs.class, param, rangeParam, null, projection, duration);
            }
            steeringEvents = steeringEvents.stream().filter(p -> (!(Objects.nonNull(p.getSteeringend()) ? p.getSteeringend() : EMPTY_STRING).equals(EMPTY_STRING))).collect(Collectors.toList());

            HashSet<String> bssidSet = new HashSet<>();
            steeringEvents.forEach(steeringEvent -> {
                bssidSet.add(String.valueOf(steeringEvent.getIbssbssid()));
                bssidSet.add(String.valueOf(steeringEvent.getTbssbssid()));
                bssidSet.add(String.valueOf(steeringEvent.getObssbssid()));
            });

            HashMap<String, Object> bssidDetails = getBssidDetails(bssidSet, userEquipment);

            DeviceSteeringEventDTO deviceSteeringEventDTO = new DeviceSteeringEventDTO();
            HashSet<DeviceSteeringEventDTO.DeviceSteeringEventData> deviceSteeringEventDataList = new HashSet<>();
            steeringEvents.forEach(steeringEvent -> {
                HashMap<String, Object> detailsMapOrigin = (HashMap<String, Object>) bssidDetails.get(String.valueOf(steeringEvent.getObssbssid()));
                HashMap<String, Object> detailsMapIntended = (HashMap<String, Object>) bssidDetails.get(String.valueOf(steeringEvent.getIbssbssid()));
                HashMap<String, Object> detailsMapTarget = (HashMap<String, Object>) bssidDetails.get(String.valueOf(steeringEvent.getTbssbssid()));
                if (Objects.nonNull(detailsMapOrigin) && Objects.nonNull(detailsMapIntended) && Objects.nonNull(detailsMapTarget) && Objects.nonNull(steeringEvent.getTimestamp()) && Long.valueOf(String.valueOf(steeringEvent.getTimestamp())) <= currentTimestamp) {
                    DeviceSteeringEventDTO.DeviceSteeringEventData deviceSteeringEventData = deviceSteeringEventDTO.new DeviceSteeringEventData();
                    deviceSteeringEventData.setIntended("Intended / " + detailsMapIntended.get("friendlyName") + " / "
                            + (dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : detailsMapIntended.get("ssid") + " / "
                            + detailsMapIntended.get("band") + " / " + steeringEvent.getIbssrssi() + " dBm"
                            + (Objects.isNull(steeringEvent.getIchannel()) ? "" : ((steeringEvent.getIchannel() > 0) ? (" / Channel " + String.valueOf(steeringEvent.getIchannel())) : ""))));
                    deviceSteeringEventData.setType("Steering Type : " + steeringEvent.getSteeringtype());
                    deviceSteeringEventData.setEnd("Steering End : " + steeringEvent.getSteeringend());
                    deviceSteeringEventData.setOrigin("Original / " + detailsMapOrigin.get("friendlyName") + " / "
                            + (dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : detailsMapOrigin.get("ssid") + " / "
                            + detailsMapOrigin.get("band") + " / " + steeringEvent.getObssrssi() + " dBm"
                            + (Objects.isNull(steeringEvent.getOchannel()) ? "" : ((steeringEvent.getOchannel() > 0) ? (" / Channel " + String.valueOf(steeringEvent.getOchannel())) : ""))));
                    deviceSteeringEventData.setTarget("Actual / " + ((Objects.isNull(steeringEvent.getSteeringend()) ? "N/A" : (steeringEvent.getSteeringend().equals("N/A") ? "N/A" : detailsMapTarget.get("friendlyName"))) + " / "
                            + detailsMapTarget.get("ssid") + " / " + detailsMapTarget.get("band") + " / " + steeringEvent.getTbssrssi() + " dBm"
                            + (Objects.isNull(steeringEvent.getTchannel()) ? "" : ((steeringEvent.getTchannel() > 0) ? (" / Channel " + String.valueOf(steeringEvent.getTchannel())) : ""))));
                    deviceSteeringEventData.setMacAddress(Objects.isNull(steeringEvent.getMacaddress()) ? null : steeringEvent.getMacaddress());
                    deviceSteeringEventData.setVendor(manageCommonService.getVendorName(String.valueOf(steeringEvent.getMacaddress())));
                    deviceSteeringEventData.setTimestamp(Objects.isNull(steeringEvent.getTimestamp()) ? 0 : Long.valueOf(String.valueOf(steeringEvent.getTimestamp())));
                    deviceSteeringEventData.setSteeringTime(Objects.isNull(steeringEvent.getSteeringtime()) ? 0 : Long.valueOf(String.valueOf(steeringEvent.getSteeringtime())));
                    deviceSteeringEventDataList.add(deviceSteeringEventData);
                }
            });
            if (deviceSteeringEventDataList.isEmpty()) {
                DeviceSteeringEventDTO.DeviceSteeringEventData deviceSteeringEventData = deviceSteeringEventDTO.new DeviceSteeringEventData();
                deviceSteeringEventDataList.add(deviceSteeringEventData);
            }
            deviceSteeringEventDTO.setData(deviceSteeringEventDataList);

            return deviceSteeringEventDTO;
        }

        if (type.equals(eventLog)) {
            List<EvLogs> steeringLogEvents;
            if (flag) {
                List<DBObject> steeringLogEventsFromMongo = mongoService.findListByTimestamp(macAddress, MAC_ADDRESS, DEVICE_EVENT_LOG, TIMESTAMP, duration, DESC, mongoFieldOptions);
                steeringLogEvents = manageCommonService.convertToEvLogs(steeringLogEventsFromMongo);
            } else {
                steeringLogEvents = (List<EvLogs>) cassandraRepository.read(EvLogs.class, param, rangeParam, null, projection, duration);
            }
            DeviceSteeringLogDTO deviceSteeringLogDTO = new DeviceSteeringLogDTO();
            HashSet<DeviceSteeringLogDTO.DeviceSteeringLogData> deviceSteeringLogDataList = new HashSet<>();
            steeringLogEvents.forEach(steeringLogEvent -> {
                if (Objects.nonNull(steeringLogEvent.getTimestamp()) && Long.valueOf(String.valueOf(steeringLogEvent.getTimestamp())) <= currentTimestamp) {

                    DeviceSteeringLogDTO.DeviceSteeringLogData deviceSteeringLogData = deviceSteeringLogDTO.new DeviceSteeringLogData();
                    deviceSteeringLogData.setLog(Objects.isNull(steeringLogEvent.getLog()) ? null : steeringLogEvent.getLog());
                    deviceSteeringLogData.setMacAddress(Objects.isNull(steeringLogEvent.getMacaddress()) ? null : steeringLogEvent.getMacaddress());
                    deviceSteeringLogData.setVendor(manageCommonService.getVendorName(String.valueOf(steeringLogEvent.getMacaddress())));
                    deviceSteeringLogData.setTimestamp(Objects.isNull(steeringLogEvent.getTimestamp()) ? 0 : Long.valueOf(String.valueOf(steeringLogEvent.getTimestamp())));
                    deviceSteeringLogDataList.add(deviceSteeringLogData);
                }
            });
            if (deviceSteeringLogDataList.isEmpty()) {
                DeviceSteeringLogDTO.DeviceSteeringLogData deviceSteeringLogData = deviceSteeringLogDTO.new DeviceSteeringLogData();
                deviceSteeringLogDataList.add(deviceSteeringLogData);
            }
            deviceSteeringLogDTO.setData(deviceSteeringLogDataList);
            return deviceSteeringLogDTO;
        }

        if (type.equals(roam)) {
            List<EvRoaming> roamingEvents;
            if (flag) {
                List<DBObject> roamingEventsFromMongo = mongoService.findListByTimestamp(macAddress, MAC_ADDRESS, DEVICE_ROAMING, TIMESTAMP, duration, DESC, mongoFieldOptions);
                roamingEvents = manageCommonService.convertToRoamEvent(roamingEventsFromMongo);
            } else {
                roamingEvents = (List<EvRoaming>) cassandraRepository.read(EvRoaming.class, param, rangeParam, null, projection, duration);
            }
            HashSet<String> bssidSet = new HashSet<>();
            roamingEvents.forEach(roamingEvent -> {
                bssidSet.add(String.valueOf(roamingEvent.getObssid()));
                bssidSet.add(String.valueOf(roamingEvent.getNbssid()));
            });

            HashMap<String, Object> bssidDetails = getBssidDetails(bssidSet, userEquipment);

            DeviceRoamEventDTO deviceRoamEventDTO = new DeviceRoamEventDTO();
            HashSet<DeviceRoamEventDTO.DeviceRoamEventData> deviceRoamEventDataList = new HashSet<>();
            // Prepare Final Data
            roamingEvents.forEach(roamingEvent -> {
                HashMap<String, Object> detailsMapOrigin = (HashMap<String, Object>) bssidDetails.get(String.valueOf(roamingEvent.getObssid()));
                HashMap<String, Object> detailsMapTarget = (HashMap<String, Object>) bssidDetails.get(String.valueOf(roamingEvent.getNbssid()));
                if (Objects.nonNull(detailsMapOrigin) && Objects.nonNull(detailsMapTarget) && Objects.nonNull(roamingEvent.getTimestamp()) && Long.valueOf(String.valueOf(roamingEvent.getTimestamp())) <= currentTimestamp) {
                    DeviceRoamEventDTO.DeviceRoamEventData deviceRoamEventData = deviceRoamEventDTO.new DeviceRoamEventData();
                    deviceRoamEventData.setFrom("Original / " + detailsMapOrigin.get("friendlyName") + " / " + (dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : detailsMapOrigin.get("ssid") + " / " + detailsMapOrigin.get("band") + " / " + roamingEvent.getOrssi() + " dBm"));
                    deviceRoamEventData.setTo("Actual / " + detailsMapTarget.get("friendlyName") + " / " + (dataSecurityMapping.contains(DataSecurityType.ssid_Name.name()) ? manageCommonService.encrypt() : detailsMapTarget.get("ssid") + " / " + detailsMapTarget.get("band") + " / " + roamingEvent.getNrssi() + " dBm"));
                    deviceRoamEventData.setMacAddress(Objects.isNull(roamingEvent.getMacaddress()) ? null : roamingEvent.getMacaddress());
                    deviceRoamEventData.setTimestamp(Objects.isNull(roamingEvent.getTimestamp()) ? 0 : Long.valueOf(String.valueOf(roamingEvent.getTimestamp())));
                    deviceRoamEventDataList.add(deviceRoamEventData);
                }
            });

            if (deviceRoamEventDataList.isEmpty()) {
                DeviceRoamEventDTO.DeviceRoamEventData deviceRoamEventData = deviceRoamEventDTO.new DeviceRoamEventData();
                deviceRoamEventDataList.add(deviceRoamEventData);
            }
            deviceRoamEventDTO.setData(deviceRoamEventDataList);
            return deviceRoamEventDTO;
        }

        if (type.equals(diagnostic)) {
            List<EvTargetApVictim> targetApVictims;
            targetApVictims = (List<EvTargetApVictim>) cassandraRepository.read(EvTargetApVictim.class, param, rangeParam, null, projection, duration);

            HashSet<String> bssidSet = new HashSet<>();
            targetApVictims.forEach( event -> {
                bssidSet.add(String.valueOf(event.getBssid()));
            });

            HashMap<String, Object> bssidDetails = getBssidDetails(bssidSet, userEquipment);

            DeviceDiagnosticDTO deviceDiagnosticDTO = new DeviceDiagnosticDTO();
            HashSet<DeviceDiagnosticDTO.DeviceDiagnosticData> deviceDiagnosticDataList = new HashSet<>();

            targetApVictims.forEach( event -> {
                if (Objects.nonNull(event.getTimestamp()) && Long.valueOf(String.valueOf(event.getTimestamp())) <= currentTimestamp) {
                    DeviceDiagnosticDTO.DeviceDiagnosticData deviceDiagnosticData = deviceDiagnosticDTO.new DeviceDiagnosticData();

                    HashMap<String, String> details = (HashMap<String, String>) bssidDetails.get(String.valueOf(event.getBssid()));

                    deviceDiagnosticData.setTimestamp(Long.valueOf(Objects.isNull(event.getTimestamp()) ? "0" : String.valueOf(event.getTimestamp())));
                    deviceDiagnosticData.setEvent(String.valueOf(event.getEvent()));
                    deviceDiagnosticData.setMacAddress(String.valueOf(event.getMacaddress()));
                    deviceDiagnosticData.setDevice(details == null ? null : details.get("friendlyName"));
                    deviceDiagnosticData.setBand(details == null ? null : details.get("band"));
                    deviceDiagnosticData.setVendor(manageCommonService.getVendorName(String.valueOf(event.getMacaddress())));
                    deviceDiagnosticData.setTrigger(Long.valueOf(event.getTrigger()));
                    deviceDiagnosticData.setTriggerDescription(ABS_TRIGGER_STRING.get(Long.valueOf(event.getTrigger()).intValue()));
                    deviceDiagnosticData.setReasonCode(Long.valueOf(event.getReasoncode()));

                    if (deviceDiagnosticData.getEvent().equals(EV_VICTIM_SELECTION)) {
                        deviceDiagnosticData.setReasonDescription(ABS_VICTIM_REASON_STRING.get(Long.valueOf(event.getReasoncode()).intValue()));
                    } else {
                        deviceDiagnosticData.setReasonDescription(ABS_TARGET_REASON_STRING.get(Long.valueOf(event.getReasoncode()).intValue()));
                    }

                    deviceDiagnosticDataList.add(deviceDiagnosticData);
                }
            });

            deviceDiagnosticDTO.setData(deviceDiagnosticDataList);
            return deviceDiagnosticDTO;
        }

        return new ApiResponseDTO();
    }

    private HashMap<String, Object> getBssidDetails(HashSet<String> bssidSet, Equipment userEquipment) {
        HashMap<String, Object> bssidDetails = new HashMap<>();

        HashMap<String, Object> query = new HashMap<>();
        query.put("userId", userEquipment.getRgwSerial());

        BasicDBObject projection = new BasicDBObject();
        projection.put("macAddress", 1);
        projection.put("friendlyName", 1);
        projection.put("modelName", 1);
        projection.put("serialNumber", 1);
        projection.put("ssids", 1);

        List<BasicDBObject> apDetailList = mongoService.findList(query, null, AP_DETAIL, projection);

        bssidSet.forEach(bssid -> {
            // Handle for 00:00:00:00:00:00 BSSID
            if (bssid.trim().equals("00:00:00:00:00:00")) {
                HashMap<String, String> details = new HashMap<>();
                details.put("ssid", "N/A");
                details.put("band", "N/A");
                details.put("friendlyName", "N/A");
                bssidDetails.put(bssid, details);
            } else {
                for (BasicDBObject apDetail : apDetailList) {
                    HashMap<String, String> details = new HashMap<>();

                    List<HashMap<String, String>> ssids = Objects.nonNull(apDetail.get("ssids")) ? (List<HashMap<String, String>>) apDetail.get("ssids") : new ArrayList<>();
                    ssids = ssids.stream().filter((ssidsDetail) -> String.valueOf(ssidsDetail.get("BSSID")).equals(bssid)).collect(Collectors.toList());
                    if (!ssids.isEmpty()) {
                        details.put("ssid", String.valueOf(ssids.get(0).get("SSID")));
                        details.put("band", WiFiUtils.getBandName(String.valueOf(ssids.get(0).get("RadioKey"))));
                        details.put("friendlyName", manageCommonService.getDisplayNameByPriorityForEquipment(apDetail));

                        bssidDetails.put(bssid, details);
                        break;
                    }

                }
            }
        });

        return bssidDetails;
    }

    private DBObject getDeviceDetails(Equipment userEquipment, String macAddr) throws Exception {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddr);

        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        if (Objects.isNull(deviceDetails))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device not found with macAddress :: " + macAddr);

        return deviceDetails;
    }

    private void invokeInternetForceResetRPC(String userId, String ssidIndex, String macAddress, int maxTries) throws Exception {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        String rpcUri = String.format(RpcConstants.DEVICE_ACTION_URI, macAddress);
        Map<String, String> payloadMap = new HashMap<>();
        payloadMap.put("action", RpcConstants.ACTION_KICK_WIFI_STA);
        String payload = objectMapper.writeValueAsString(payloadMap);

        List<BasicDBObject> equipmentList = mongoService.findList(params, AP_DETAIL, mongoFieldOptions);
        List<String> tids = new ArrayList<>();
        for (BasicDBObject equipment : equipmentList) {
            String tid = UUID.randomUUID().toString();
            tids.add(tid);
            cpeRpcService.sendRpc(tid, userId, equipment.getString("serialNumber"), rpcUri, "POST", payload);
        }

        for (int i = 0; i < maxTries; i++) {
            try {
                Thread.sleep(THREAD_TO_SLEEP);
            } catch (InterruptedException e) {
                throw e;
            }

            int fail_count = 0;
            for(String tid : tids) {
                Map<String, Object> result = cpeRpcService.readRpcResult(tid);
                if (result != null) {
                    LOG.debug("tid:[{}] result:[{}]", tid, result);
                    if (StringUtils.equals("200", String.valueOf(result.get("code")))) {
                        return;
                    } else {
                        fail_count++;
                        if(tids.size() == fail_count) {
                            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RPC fail, tid:[ " + tid + " ]");
                        }
                    }
                }
            }
        }

        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RPC timeout, tids:[ " + tids.toString() + " ]");
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.DEVICE_PAUSE_UNPAUSE)
    public void invokeInternetRPCMethodsForDevice(String equipmentIdOrSerialOrSTN, String serialNo, String
            operation, String macAddr, String band, String trafficType, HttpServletRequest httpServletRequest) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(macAddr, userEquipment);
        manageCommonService.checkDeviceMACBelongsToSubscriber(macAddr, userEquipment);

        DBObject deviceDetailData = getDeviceDetails(userEquipment, macAddr);

        if (Objects.isNull(deviceDetailData.get("serialNumber")) || deviceDetailData.get("serialNumber").toString().trim().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device is not connected to any RG/EXT");

        if (Objects.isNull(deviceDetailData.get("band")) || deviceDetailData.get("band").toString().trim().equalsIgnoreCase(EMPTY_STRING)) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Band Found for MAC :: " + macAddr);
        }

        String connectivityStatus = manageCommonService.getConnectivityStatusForDevice(deviceDetailData);
        boolean isForgetDevice = Objects.isNull(deviceDetailData.get("isForgetDevice")) ? false : Boolean.valueOf(deviceDetailData.get("isForgetDevice").toString());
        if (isForgetDevice && connectivityStatus.equals("GREY")) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device is already Disconnected");
        }

        if (Objects.isNull(deviceDetailData.get("bssid")))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Device is not connected to any RG/EXT");

        HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
        Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
        switch (operation) {
            case INTERNET_FORCE_RESET:
                invokeInternetForceResetRPC(userEquipment.getRgwSerial(), null, macAddr, max_Tries);
                break;
            default:
                throw new ApiException(ApiResponseCode.BAD_REQUEST);
        }
    }

    public void updateFriendlyNameForEthMocaDevice(DeviceFriendlyNameRequest friendlyNameRequest, String
            equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        manageCommonService.checkMacAddressOfEthMoca(friendlyNameRequest.getMacAddress(), userEquipment);

        BasicDBObject insertField = new BasicDBObject();
        insertField.put("friendlyName", friendlyNameRequest.getFriendlyName());
        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        basicDBObjectUpdate.put("$set", insertField);
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("macAddress", friendlyNameRequest.getMacAddress());
        mongoService.update(query, basicDBObjectUpdate, true, true, HOSTNAME_DETAIL);

    }

    public void updateDeviceTypeForEthMocaDevice(UpdateDeviceTypeRequest updateDeviceTypeRequest, String
            equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        manageCommonService.checkMacAddressOfEthMoca(updateDeviceTypeRequest.getMacAddress(), userEquipment);

        BasicDBObject insertField = new BasicDBObject();
        insertField.put("deviceType", updateDeviceTypeRequest.getType());
        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        basicDBObjectUpdate.put("$set", insertField);
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("macAddress", updateDeviceTypeRequest.getMacAddress());
        mongoService.update(query, basicDBObjectUpdate, true, true, HOSTNAME_DETAIL);

    }

    private boolean isEthernetDevice(String macAddress, Equipment userEquipment) throws Exception {
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("macAddress", macAddress);
        DBObject deviceDetails = mongoService.findOne(queryParams, appendableParams, STATION_DETAIL, TIMESTAMP, DESC, new BasicDBObject("_id", 0));
        if (Objects.isNull(deviceDetails)) {
            DBObject ethernetDevice = mongoService.findOne(queryParams, appendableParams, HOSTNAME_DETAIL, TIMESTAMP, DESC, new BasicDBObject("_id", 0));
            if (Objects.isNull(ethernetDevice))
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "No Device found with MAC Address " + macAddress);

            return true;
        } else {
            return false;
        }
    }

    public void updateFriendlyNameForUserDevice(DeviceFriendlyNameRequest friendlyNameRequest, String
            equipmentIdOrSerialOrSTN) throws Exception {

//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");

        if (isEthernetDevice(friendlyNameRequest.getMacAddress(), userEquipment)) {
            updateFriendlyNameForEthMocaDevice(friendlyNameRequest, equipmentIdOrSerialOrSTN);
            return;
        }


        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(friendlyNameRequest.getMacAddress(), userEquipment);
        manageCommonService.checkDeviceMACBelongsToUser(friendlyNameRequest.getMacAddress(), userEquipment);
        BasicDBObject insertField = new BasicDBObject();
        insertField.put("friendlyName", friendlyNameRequest.getFriendlyName());
        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        basicDBObjectUpdate.put("$set", insertField);
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("macAddress", friendlyNameRequest.getMacAddress());
        mongoService.update(query, basicDBObjectUpdate, true, true, STATION_DETAIL);
    }

    public void updateDeviceType(UpdateDeviceTypeRequest updateDeviceTypeRequest, String
            equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        if (isEthernetDevice(updateDeviceTypeRequest.getMacAddress(), userEquipment)) {
            updateDeviceTypeForEthMocaDevice(updateDeviceTypeRequest, equipmentIdOrSerialOrSTN);
            return;
        }

        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        manageCommonService.checkMacAddressOfStation(updateDeviceTypeRequest.getMacAddress(), userEquipment);
        manageCommonService.checkDeviceMACBelongsToUser(updateDeviceTypeRequest.getMacAddress(), userEquipment);
        BasicDBObject insertField = new BasicDBObject();
        insertField.put("deviceType", updateDeviceTypeRequest.getType());
        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        basicDBObjectUpdate.put("$set", insertField);
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userEquipment.getRgwSerial());
        query.put("macAddress", updateDeviceTypeRequest.getMacAddress());
        mongoService.update(query, basicDBObjectUpdate, true, true, STATION_DETAIL);
    }

    public void forgetDevice(String macAddress, boolean status, String equipmentIdOrSerialOrSTN) throws
            Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC))
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);

        Equipment userAP = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userAP.getRgwSerial()) || userAP.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userAP);
        manageCommonService.subscriberGroupMisMatch(userAP);

        manageCommonService.checkMacAddressOfStation(macAddress, userAP);
        manageCommonService.checkDeviceMACBelongsToUser(macAddress, userAP);
        BasicDBObject insertField = new BasicDBObject();
        insertField.put("isForgetDevice", status);
        BasicDBObject basicDBObjectUpdate = new BasicDBObject();
        basicDBObjectUpdate.put("$set", insertField);
        BasicDBObject query = new BasicDBObject();
        query.put("userId", userAP.getRgwSerial());
        query.put("macAddress", macAddress);
        mongoService.update(query, basicDBObjectUpdate, true, true, STATION_DETAIL);
    }

    public DeviceUtilizationDTO fetchDeviceUtilizationByMacAddr(String equipmentIdOrSerialOrSTN, String macAddress, Long duration, Boolean fromDSHost) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrRGWMAC)) {
//            serialNumberOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrRGWMAC);
//        }

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if (Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);
        manageCommonService.subscriberGroupMisMatch(userEquipment);

        macAddress = manageCommonService.getMacAddressOfL2MacAddress(macAddress, userEquipment);

        Calendar criteriaCalendar = Calendar.getInstance();
        long realCurrentTime = new Date().getTime();
        LOG.debug("real current time: {}", realCurrentTime);
        criteriaCalendar.setTime(new Date(realCurrentTime - (duration < 60 ? (60 * 60L * 1000L) : duration * 60L * 1000L)));
        long currTimestamp = criteriaCalendar.getTimeInMillis();
        LOG.debug("modified current time: {}", currTimestamp);

        BasicDBObject matchQuery = new BasicDBObject();
        matchQuery.put("userId", userEquipment.getRgwSerial());
        matchQuery.put("dateHour", new BasicDBObject("$gte", manageCommonService.convertMinutesToDateHour(duration)));

        List<BasicDBObject> pipeline = new ArrayList<>();

        // Step 1: Match userId and dateHour
        pipeline.add(new BasicDBObject("$match", matchQuery));

        // Step 2: Project to convert devMap field to array
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("date", 1)
                .append("allDevMaps", new BasicDBObject("$objectToArray", "$$ROOT"))));

        // Step 3: Unwind to handle allDevMaps array
        pipeline.add(new BasicDBObject("$unwind", "$allDevMaps"));

        // Step 4: Match to filter only the date fields (regex match on the key)
        pipeline.add(new BasicDBObject("$match", new BasicDBObject("allDevMaps.k", new BasicDBObject("$regex", "^\\d{4}-\\d{2}-\\d{2}$"))));

        // Step 5: Convert the value (v) of `allDevMaps` into an array of objects
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("allDevMapData", new BasicDBObject("$objectToArray", "$allDevMaps.v"))));

        // Step 6: Unwind the newly created array to access each numbered key (0, 1, 2, ...)
        pipeline.add(new BasicDBObject("$unwind", "$allDevMapData"));

        // Step 7: Extract the devMap field from the number-keyed objects
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("devMap", "$allDevMapData.v.devMap")));

        // Step 8: Convert the devMap into an array of key-value pairs (MAC addresses)
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("devMapEntries", new BasicDBObject("$objectToArray", new BasicDBObject("$ifNull", Arrays.asList("$devMap", new BasicDBObject()))))));

        // Step 9: Unwind devMapEntries to process each MAC address individually
        pipeline.add(new BasicDBObject("$unwind", "$devMapEntries"));

        // Step 10: Match to filter by specific MAC address
        pipeline.add(new BasicDBObject("$match", new BasicDBObject("devMapEntries.k", macAddress)));

        // Step 11: Unwind the array of MAC address data (i.e., devMapEntries.v)
        pipeline.add(new BasicDBObject("$unwind", "$devMapEntries.v"));

        // Step 12: Filter timestamp
        pipeline.add(new BasicDBObject("$match",
                new BasicDBObject("devMapEntries.v.timestamp",
                        new BasicDBObject("$gte", currTimestamp)
                )
        ));

        // Step 13: Sort by timestamp (sort the unwound records)
        pipeline.add(new BasicDBObject("$sort", new BasicDBObject("devMapEntries.v.timestamp", 1)));

        // Step 14: Group to calculate total downlink and uplink, and collect sorted records
        pipeline.add(new BasicDBObject("$group", new BasicDBObject("_id", "$devMapEntries.k")
                .append("totalDownlink", new BasicDBObject("$sum", "$devMapEntries.v.internetRx"))
                .append("totalUplink", new BasicDBObject("$sum", "$devMapEntries.v.internetTx"))
                .append("records", new BasicDBObject("$push", new BasicDBObject("timestamp", "$devMapEntries.v.timestamp")
                        .append("internetRx", "$devMapEntries.v.internetRx")
                        .append("internetTx", "$devMapEntries.v.internetTx")))));

        // Step 15: Project the required fields (totalDownlink, totalUplink, and sorted records)
        pipeline.add(new BasicDBObject("$project", new BasicDBObject("_id", 0)
                .append("totalDownlink", 1)
                .append("totalUplink", 1)
                .append("records", 1)));

        AggregationOutput aggregationOutput = at3Adapter.getMongoDbCollection(DS_HOST_INSIGHTS).aggregate(pipeline);
        Iterable<DBObject> results = aggregationOutput.results();

        List<HashMap<String, Object>> dataPoints = new ArrayList<>();

        double downLinkRate = userEquipment.getDownLinkRate() > 10000 || userEquipment.getDownLinkRate() <= 0 ? 1000.0 : userEquipment.getDownLinkRate();
        double upLinkRate = userEquipment.getUpLinkRate() > 10000 || userEquipment.getUpLinkRate() <= 0 ? 1000.0 : userEquipment.getUpLinkRate();
        double reportInterval = getReportIntervalForUser(userEquipment);

        DeviceUtilizationDTO deviceUtilizationDTO = new DeviceUtilizationDTO();
        DeviceUtilizationDTO.DeviceUtilizationData deviceUtilizationData = deviceUtilizationDTO.new DeviceUtilizationData();

        if (results.iterator().hasNext()) {
            DBObject result = results.iterator().next();

            double totalDownLinkBytes = Objects.nonNull(result.get("totalDownlink")) ? Double.parseDouble(result.get("totalDownlink").toString()) : 0.0;
            double totalUpLinkBytes = Objects.nonNull(result.get("totalUplink")) ? Double.parseDouble(result.get("totalUplink").toString()) : 0.0;

            deviceUtilizationData.setTotalDownLinkBytes(totalDownLinkBytes);
            deviceUtilizationData.setTotalUpLinkBytes(totalUpLinkBytes);

            List<DBObject> records = (List<DBObject>) result.get("records");
            for (DBObject record : records) {
                long timestamp = Long.parseLong(record.get("timestamp").toString());

//                if (timestamp < currTimestamp) {
//                    LOG.debug("throughput record timestamp < current timestamp!, timestamp: {}, currentTimestamp: {}", timestamp, currTimestamp);
//                    LOG.debug("internetRx: {}", record.get("internetRx"));
//                    LOG.debug("internetTx: {}", record.get("internetTx"));
//                    continue;
//                }

                double rx = Objects.nonNull(record.get("internetRx")) ? Double.parseDouble(record.get("internetRx").toString()) : 0.0;
                double tx = Objects.nonNull(record.get("internetTx")) ? Double.parseDouble(record.get("internetTx").toString()) : 0.0;

                HashMap<String, Object> data = new HashMap<>();
                data.put("timestamp", timestamp);
                data.put("tx", (tx * 8) / 1000F / 1000F / reportInterval);
                data.put("rx", (rx * 8) / 1000F / 1000F / reportInterval);
                data.put("uploadPercentage", (tx * 8 / 1000F / 1000F / reportInterval) / upLinkRate * 100);
                data.put("downloadPercentage", (rx * 8 / 1000F / 1000F / reportInterval) / downLinkRate * 100);
                data.put("downLinkRate", downLinkRate);
                data.put("upLinkRate", upLinkRate);

                dataPoints.add(data);
            }
        }

        deviceUtilizationData.setDatapoints(dataPoints);
        deviceUtilizationDTO.setData(deviceUtilizationData);

        return deviceUtilizationDTO;
    }

    private long getReportIntervalForUser(Equipment userEquipment) throws Exception {
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        HashMap<String, String> queryParams = new HashMap<>();
        queryParams.put("userId", userEquipment.getRgwSerial());
        queryParams.put("serialNumber", manageCommonService.getGatewaySerialByUserId(userEquipment.getRgwSerial()).orElseGet(()->userEquipment.getRgwSerial()));
        DBObject aPDetails = mongoService.findOne(queryParams, new HashMap<>(), AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);

        return Objects.isNull(aPDetails.get("reportInterval")) ? 1L : Long.valueOf(aPDetails.get("reportInterval").toString());
    }

    public boolean getRgDfsEnabled(String userId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        params.put("serialNumber", manageCommonService.getControllerSerialByUserId(userId).orElseGet(()->userId));

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.put("wifiRadios", 1);
        BasicDBObject apDetailDbObj = (BasicDBObject) mongoService.findOne(params, new HashMap<>(), AP_DETAIL, mongoFieldOptions);
        return getRgDfsEnabled(apDetailDbObj);
    }

    public boolean getRgDfsEnabled(BasicDBObject apDetailDbObj) {
        boolean dfsEnabled = false;
        BasicDBList wifiRadioDbList = (BasicDBList) apDetailDbObj.get("wifiRadios");
        for (Object wifiRadioObj : wifiRadioDbList) {
            BasicDBObject wifiRadioDbObj = (BasicDBObject) wifiRadioObj;
            String radioKey = wifiRadioDbObj.getString("radioKey");
            if (StringUtils.contains(radioKey, "5G")) {
                dfsEnabled = wifiRadioDbObj.getBoolean("dfsEnable");
                break;
            }
        }
        return dfsEnabled;
    }
}
