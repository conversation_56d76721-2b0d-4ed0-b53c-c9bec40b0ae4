package com.incs83.app.business.v2;

import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.entities.LoginDetails;
import com.incs83.config.HazelcastClientConfig;
import com.incs83.config.JwtConfig;
import com.incs83.constants.ApplicationCommonConstants;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.mt.DataAccessService;
import com.incs83.security.tokenFactory.RawAccessJwtToken;
import com.incs83.services.HazelcastService;
import com.incs83.util.CommonUtils;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.incs83.app.constants.misc.ApplicationConstants.TOKEN;

/**
 * Created by hari on 29/1/18.
 */
@Service
public class ManageLoginService {
    private static Logger logger = LogManager.getLogger(ManageLoginService.class);

    @Autowired
    private HazelcastService hazelcastService;

    @Autowired
    private HazelcastClientConfig hazelcastClientConfig;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private JwtConfig jwtConfig;

    public void doLogout() {
        try {
            LoginDetails loginDetails = (LoginDetails) dataAccessService.read(LoginDetails.class, CommonUtils.getUserIdOfLoggedInUser());
            loginDetails.setToken(null);
            dataAccessService.update(LoginDetails.class, loginDetails);
            hazelcastService.update(loginDetails.getId(), new HashMap<>(), TOKEN);
        } catch (Exception e) {
            logger.error("Error while updating token", e);
        }
    }

    public boolean isAuthenticated(HashMap<String, Object> accessToken) {
        //boolean isAuthenticated = false;
        if (!accessToken.containsKey("token")) {
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Missing Request Payload key : 'token'");
        }

        List<String> tokenAsList = Arrays.asList(accessToken.get("token").toString().split(ApplicationConstants.SPACE));
        String token = null;
        if (tokenAsList.size() == 2 && tokenAsList.get(0).equalsIgnoreCase("Bearer")) {
            token = tokenAsList.get(1);
        } else {
            logger.debug("token format error. tokenInReq:[{}]", accessToken.get("token"));
            return false;
        }


        return checkAccessTokenValidity(token);

        // Jws<Claims> jwsClaims = null;
        // try {
        //     RawAccessJwtToken rawAccessJwtToken = new RawAccessJwtToken(token);
        //     jwsClaims = rawAccessJwtToken.parseClaims(this.jwtConfig.getTokenSigningKey());
        // } catch (Exception e) {
        //     logger.debug("parse token failed:[{}]", token);
        //     return false;
        // }

        // if (jwsClaims == null) {
        //     logger.debug("jwsClaims is null. token:[{}]", token);
        //     return false;
        // }

        // if (this.isTokenExpired(jwsClaims.getBody().getExpiration())) {
        //     logger.debug("token expired, token:[{}] expiration:[{}]", token, jwsClaims.getBody().getExpiration());
        //     return false;
        // }

        // String id = jwsClaims.getBody().getId();

        // if (checkFromHazelcast(token, id)) {
        //     return true;
        // } else {
        //     return checkFromDb(token, id);
        // }
        //return isAuthenticated;
    }

    public boolean checkAccessTokenValidity(String accessToken) {
        Jws<Claims> jwsClaims = null;
        try {
            RawAccessJwtToken rawAccessJwtToken = new RawAccessJwtToken(accessToken);
            jwsClaims = rawAccessJwtToken.parseClaims(this.jwtConfig.getTokenSigningKey());
        } catch (Exception e) {
            logger.debug("parse token failed:[{}]", accessToken);
            return false;
        }

        if (jwsClaims == null) {
            logger.debug("jwsClaims is null. token:[{}]", accessToken);
            return false;
        }

        if (this.isTokenExpired(jwsClaims.getBody().getExpiration())) {
            logger.debug("token expired, token:[{}] expiration:[{}]", accessToken, jwsClaims.getBody().getExpiration());
            return false;
        }

        String id = jwsClaims.getBody().getId();

        if (checkFromHazelcast(accessToken, id)) {
            return true;
        } else {
            return checkFromDb(accessToken, id);
        }
    }



    private boolean checkFromHazelcast(String token, String id) {
        try {
            if (this.hazelcastClientConfig.isConnected()) {
                String tokenInCache = null;
                Object objInCache = hazelcastService.read(id, ApplicationCommonConstants.TOKEN);
                if (objInCache == null) {
                    logger.debug("cache miss, token:[{}] id:[{}]", token, id);
                    return false;
                } else {
                    HashMap<String, Object> mapInCache = (HashMap<String, Object>) objInCache;
                    if (mapInCache.get("token") == null) {
                        logger.debug("no token in cache map, token:[{}] id:[{}]", token, id);
                        return false;
                    } else {
                        tokenInCache = String.valueOf(mapInCache.get("token"));
                    }
                }

                boolean isAuthenticated = StringUtils.equals(token, tokenInCache);
                logger.debug("check token in hazelcast id:[{}] input:[{}] tartget:[{}], result:[{}]", id, token, String.valueOf(((HashMap<String, Object>) hazelcastService.read(id, ApplicationCommonConstants.TOKEN)).get("token")), isAuthenticated);
                return isAuthenticated;
            } else {
                logger.debug("hazelcast is not connected.");
                return false;
            }
        } catch (Exception e) {
            logger.error("checkFromHazelcast failed. id:[{}] token:[{}]", id, token, e);
            return false;
        }
    }

    private boolean checkFromDb(String token, String id) {
        //TODO:  put back into hazelcast ?

        try {
            LoginDetails loginDetails = (LoginDetails) dataAccessService.read(LoginDetails.class, id);
            if (loginDetails == null) {
                logger.debug("no loginDetails in database. id:[{}]", id);
                return false;
            }
            boolean result = StringUtils.equals(token, loginDetails.getToken());
            logger.debug("check token in db, id:[{}] input:[{}] target:[{}] result:[{}]", id, token, loginDetails.getToken(), result);
            return result;
        } catch (Exception e) {
            logger.error("checkFromDb failed. id:[{}] token:[{}]", id, token, e);
            return false;
        }
    }

    private boolean isTokenExpired(Date tokenExpiryDate) {
        return !tokenExpiryDate.after(Calendar.getInstance().getTime());
    }
}
