package com.incs83.app.business.v2;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.entities.Equipment;
import com.incs83.app.enums.DataSecurityType;
import com.incs83.app.responsedto.v2.Device.DeviceDetailDTO;
import com.incs83.app.responsedto.v2.Network.EquipmentDetail;
import com.incs83.app.responsedto.v2.Network.ExtenderDetail;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.context.ExecutionContext;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBList;
import com.mongodb.BasicDBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ApplicationConstants.CALENDER_CRITERIA_LAST_3_DAYS;
import static com.incs83.app.constants.misc.ApplicationConstants.GATEWAY;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;

@Service
public class ManageAlarmService {
    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

    @Autowired
    private ManageDeviceService manageDeviceService;

    @Autowired
    private At3Adapter at3Adapter;

    public List<HashMap<String, Object>> getAllAlarmsForSubscriber(String equipmentIdOrSerialOrSTN) throws Exception {
//        if (ValidationUtil.validateMAC(serialNumberOrSubscriberIdOrRGWMAC))
//            serialNumberOrSubscriberIdOrRGWMAC = manageCommonService.getAPIDFromMAC(serialNumberOrSubscriberIdOrRGWMAC);

        Equipment userEquipment = manageCommonService.getUserAPFromSubscriberIdOrApId(equipmentIdOrSerialOrSTN);
        if(Objects.isNull(userEquipment.getRgwSerial()) || userEquipment.getRgwSerial().isEmpty())
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "RGW Serial cannot be empty");
        manageCommonService.subscriberDataExistInMongo(userEquipment);

        long currentMillis = System.currentTimeMillis();
        long onlineBaseMillis = currentMillis - at3Adapter.getEquipmentOfflineMins() * 60000L;

        List<String> dataSecurityMapping = ExecutionContext.get().getUsercontext().getDataSecurityTypeList();

        HashMap<String, Object> params = new HashMap<>();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        params.put("userId", userEquipment.getRgwSerial());
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);
        mongoFieldOptions.put("ethPorts", 0);
        mongoFieldOptions.put("wan", 0);
        mongoFieldOptions.put("moca", 0);

        List<BasicDBObject> equipmentList = mongoService.findList(params, ActiontecConstants.AP_DETAIL, mongoFieldOptions);
        equipmentList = manageCommonService.filterEquipmentsAndCheckForRgwAndExt(equipmentList);
        HashMap<String, String> aggregationParams = new HashMap<>();
        aggregationParams.put("outputParams", "devices,count");
        aggregationParams.put("label", "$serialNumber");
        aggregationParams.put("operation", "$push,$sum");
        aggregationParams.put("keyToAggregate", "$$ROOT,1");
        aggregationParams.put("operand", "$gte");

        mongoFieldOptions.clear();
        mongoFieldOptions.put("devices._id", 0);
        List<BasicDBObject> devicesList = mongoService.aggregate(params, null, ActiontecConstants.STATION_DETAIL, CommonUtils.getCalendarInstanceForCriteria(CALENDER_CRITERIA_LAST_3_DAYS), aggregationParams, TIMESTAMP, mongoFieldOptions);

        EquipmentDetail equipmentDetail = new EquipmentDetail();
        List<HashMap<String, Object>> alarmsListForSubscriber = new ArrayList<>();
        for (BasicDBObject equipment : equipmentList) {
            String serialNumber = equipment.getString("serialNumber");
            List<BasicDBObject> filteredDeviceList = devicesList.parallelStream().filter(d -> d.getString("_id").equals(serialNumber)).collect(Collectors.toList());
            ArrayList<DeviceDetailDTO.DeviceDetailData> deviceDetailList = new ArrayList<>();
            if (!filteredDeviceList.isEmpty()) {
                List<BasicDBObject> devices = (List<BasicDBObject>) filteredDeviceList.get(0).get("devices");
                HashSet<String> deviceMacList = devices.stream().map((device) -> String.valueOf(device.get("macAddress"))).collect(Collectors.toCollection(HashSet::new));
                HashMap<String, String> stationFriendlyName = manageCommonService.getFriendlyNameByStation(deviceMacList, userEquipment);
                for (BasicDBObject device : devices) {
                    if (Objects.nonNull(device)) {

                        long lastReportAt = device.getLong("lastReportAt");
                        if (lastReportAt < onlineBaseMillis) {
                            continue;
                        }

                        DeviceDetailDTO deviceDetailDTO = new DeviceDetailDTO();
                        DeviceDetailDTO.DeviceDetailData deviceDetail = deviceDetailDTO.new DeviceDetailData();

                        if (Objects.nonNull(device.get("band")) || Objects.nonNull(device.get("userId")) || Objects.nonNull(device.get("serialNumber")) || Objects.nonNull(device.get("bssid"))) {
                            deviceDetail.setSsid(manageCommonService.getSsidForDeviceDetail(String.valueOf(device.get("userId")), serialNumber, String.valueOf(device.get("bssid"))));
                        } else {
                            deviceDetail.setSsid("N/A");
                        }

                        String friendlyName = stationFriendlyName.get(String.valueOf(device.get("macAddress")));
                        if(friendlyName == null || friendlyName.isEmpty()) {
                            deviceDetail.setName(manageCommonService.getHostNameByGateway(String.valueOf(device.get("macAddress")), userEquipment));
                        } else {
                            deviceDetail.setName(friendlyName);
                        }

                        deviceDetail.setWifiMode(Objects.isNull(device.get("wifiMode")) ? "N/A" : device.get("wifiMode").toString());
                        deviceDetail.setRssi(Objects.isNull(device.get("rssi")) ? 0 : Double.valueOf(device.get("rssi").toString()));
                        deviceDetail.setMac(String.valueOf(device.get("macAddress")));
                        deviceDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForDevice(device));
                        deviceDetail.setCapability(manageCommonService.getCapabilityFromDB(device));
                        deviceDetail.setDfsSupported(device.getBoolean("dfsSupported", false));
                        Object bandsObj = device.get("supportedBands");
                        StringBuilder bandsBuilder = new StringBuilder();
                        if (Objects.nonNull(bandsObj)) {
                            if (bandsObj instanceof BasicDBList) {
                                boolean first = true;
                                for(Object band:(BasicDBList)bandsObj) {
                                    if (first) {
                                       first = false;
                                    } else {
                                        bandsBuilder.append(",");
                                    }
                                    bandsBuilder.append(band.toString().toUpperCase(Locale.ROOT));
                                }
                            }
                        }
                        deviceDetail.setSupportedBands(bandsBuilder.toString());

                        alarmsListForSubscriber.addAll(manageDeviceService.getAlarmListForDevice(deviceDetail,String.valueOf(device.get("userId")), manageDeviceService.getRgDfsEnabled(equipment)));
                    }
                }
            }

            if (equipment.getString("type").equals(GATEWAY)) {
                equipmentDetail.setDevices(deviceDetailList);
                equipmentDetail.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : String.valueOf(equipment.get("macAddress")));
                equipmentDetail.setName(Objects.isNull(equipment.get("friendlyName")) ? Objects.isNull(equipment.get("modelName")) ? equipment.get("macAddress").toString() : equipment.get("modelName").toString() : equipment.get("friendlyName").toString());
                equipmentDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
                equipmentDetail.setType(equipment.getString("type"));
                equipmentDetail.setSerialNumber(equipment.getString("serialNumber"));

                alarmsListForSubscriber.addAll(manageEquipmentService.getEquipmentAlarmList(equipment, userEquipment));

                //// ALARMS FOR EQUIPMENT END
            } else if ((equipment.getString("type").equals("EXTENDER"))) {
                ExtenderDetail extenderDetail = new ExtenderDetail();
                extenderDetail.setDevices(deviceDetailList);
                extenderDetail.setMacAddress(dataSecurityMapping.contains(DataSecurityType.macAddress.name()) ? manageCommonService.encrypt() : String.valueOf(equipment.get("macAddress")));
                extenderDetail.setName(manageCommonService.getDisplayNameForEquipment(equipment));
                extenderDetail.setConnectivityStatus(manageCommonService.getConnectivityStatusForEquipment(equipment));
                extenderDetail.setType(equipment.getString("type"));
                extenderDetail.setSerialNumber(equipment.getString("serialNumber"));

                alarmsListForSubscriber.addAll(manageEquipmentService.getEquipmentAlarmList(equipment, userEquipment));
            }
        }
        return alarmsListForSubscriber;
    }
}
