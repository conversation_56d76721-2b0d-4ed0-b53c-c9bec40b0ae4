/**
 * <AUTHOR>
 * @created 10-Apr-2017
 */
package com.incs83.app.main;

import com.google.common.base.Predicates;
import com.incs83.app.service.components.S3ServiceImpl;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Collections;

import static com.incs83.app.constants.misc.ApplicationConstants.BUILD_VERSION_INFO;

@SpringBootApplication(scanBasePackages = { "com.incs83", "com.actiontec" })
//@EnableSwagger2
@EnableScheduling
@EnableAsync
public class Launcher {
    @Autowired
    private S3ServiceImpl s3Service;

    private static final Logger LOG = LogManager.getLogger("org");

    public static void main(String[] args) {
        SpringApplication.run(Launcher.class, args);

    }

    public class SwaggerConfig {
        @Bean
        public Docket parentalControlsAPIs() {
            return new Docket(DocumentationType.SWAGGER_2)
                    .groupName("Optim")
                    .apiInfo(apiInfo())
                    .protocols(Collections.singleton("https"))
                    .select()
                    .paths(PathSelectors.ant("/*/api/**"))
                    .paths(Predicates.not(PathSelectors.ant("/*/api/v3/security/**")))
                    .build();
        }
    }

    @Bean
    public Docket securityApis() {
        return new Docket(DocumentationType.SWAGGER_2)
                .groupName("Security")
                .apiInfo(securityApiInfo())
                .select()
                .paths(PathSelectors.ant("/*/api/v3/security/**"))
                .build();
    }

    private ApiInfo apiInfo() {
        return new ApiInfo(
                "Optim API",
                "These are API's for Optim",
                s3Service.getBuildVersionInfo(BUILD_VERSION_INFO),
                "Terms of service",
                "Actiontec",
                "License of API", "https://www.actiontec.com/contact/");
    }

    private ApiInfo securityApiInfo() {
        return new ApiInfo(
                "Security API",
                "These are API's for RGW Security Operations",
                s3Service.getBuildVersionInfo(BUILD_VERSION_INFO),
                "Terms of service",
                "Actiontec",
                "License of API", "https://www.actiontec.com/contact/");
    }


}
