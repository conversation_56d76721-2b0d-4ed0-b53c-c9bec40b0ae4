package com.incs83.app.annotation;

import com.incs83.app.constants.misc.AuditorConstants;
import org.springframework.web.bind.annotation.RequestMethod;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface Auditable {
    RequestMethod method();
    String operation();
    boolean persist() default true;
}

