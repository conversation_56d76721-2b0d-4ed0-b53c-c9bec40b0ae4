package com.incs83.app.utils;

import com.actiontec.optim.util.CustomStringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import java.lang.reflect.Field;

public class SqlStatementUtils {
    public static final String COLUMN_PLACEHOLDER = "%s";
    public static final String VALUE_PLACEHOLDER = ":%s";
    public static final String SORT_ASC = "ASC";
    public static final String SORT_DESC = "DESC";

    public static class Condition {
        public static final String WHERE = " WHERE ";
        public static final String AND = " AND ";
        public static final String OR = " OR ";
        public static final String LIKE = " LIKE ";
        public static final String LIMIT = " LIMIT ";
        public static final String EQUAL =  " = ";
        public static final String ORDERBY = " ORDER BY ";
    }

    public static class Alias {
        public static final String Compartment = "c.";
        public static final String NCSEquipment = "ne.";
        public static final String Subscriber = "s.";
        public static final String SubscriberCompartment = "sc.";
        public static final String Network = "n.";
        public static final String SubscriberNetwork = "sn.";
        public static final String BatchEquipmentUploadLog = "beul.";
        public static final String BrokerInfo = "bi.";
        public static final String RttySession = "rs.";
        public static final String User = "u.";
    }

    public static String getNativeColumnName(Field field) {
        // get native sql column name
        String columnName = field.getName();
        Column columnAnnotation = field.getAnnotation(Column.class);
        if (!ObjectUtils.isEmpty(columnAnnotation)) {
            columnName = columnAnnotation.name();
        }

        return columnName;
    }

    public static String buildLikeStatement(String tableAlias, String columnNativeName, String fieldValueName) {
        String likeStatement = tableAlias + COLUMN_PLACEHOLDER + Condition.LIKE + VALUE_PLACEHOLDER;
        return String.format(likeStatement, columnNativeName, fieldValueName);
    }

    public static String buildEqualStatement(String tableAlias, String columnNativeName, String fieldValueName) {
        String equalStatement = tableAlias + COLUMN_PLACEHOLDER + Condition.EQUAL + VALUE_PLACEHOLDER;
        return String.format(equalStatement, columnNativeName, fieldValueName);
    }

    public static String buildPageStatement(Integer offset, Integer limit) {
        return Condition.LIMIT + offset + ", " + limit;
    }

    public static String buildOrderStatement(String sort, String... orderColumns) {
        if (StringUtils.isEmpty(orderColumns)) {
            return CustomStringUtils.EMPTY;
        }

        return Condition.ORDERBY + String.join(", ", orderColumns) + CustomStringUtils.SPACE + sort;
    }
}
