package com.incs83.app.utils;

import java.util.HashMap;

public class ServiceNameUtils {
    public static String getServiceName(String serviceName) {
        HashMap<String, String> serviceNameList = new HashMap<>();
        serviceNameList.put("cds", "CDS");
        serviceNameList.put("externalRoutingTier", "EXTERNAL_ROUTING_TIER");
        serviceNameList.put("internalRoutingTier", "INTERNAL_ROUTING_TIER");
        serviceNameList.put("kafkaConnect", "KAFKA_CONNECT");
        serviceNameList.put("platform", "PLATFORM");
        serviceNameList.put("vmq", "VERNE_MQ");
        serviceNameList.put("ui", "UI");
        serviceNameList.put("mysql", "MYSQL");
        serviceNameList.put("mongo", "MONGO");
        serviceNameList.put("template", "TEMPLATE");
        serviceNameList.put("nagios", "NAGIOS");
        serviceNameList.put("manager", "MANAGER");
        serviceNameList.put("master", "MASTER");
        serviceNameList.put("gateway", "GATEWAY");
        serviceNameList.put("zkafka", "ZOOKEEPER_KAFKA");
//        serviceNameList.put("datanode", "DATA_NODE");
        serviceNameList.put("cache", "CACHE");
        serviceNameList.put("cassandra", "CASSANDRA");
        serviceNameList.put("jobStatsServer", "JOB_STATS_SERVER");
        serviceNameList.put("jobRpcServer", "JOB_RPC_SERVER");
        serviceNameList.put("network-elb", "NETWORK ELB");
        serviceNameList.put("classic-elb", "CLASSIC ELB");
        serviceNameList.put("elastic-search", "ELASTIC SEARCH");
//        serviceNameList.put("worker","WORKER");
        serviceNameList.put("worker_datanode","DATA_NODE_WORKER");
        serviceNameList.put("mongo-shard","MONGO_SHARD");
        serviceNameList.put("vmqMetrics","VMQ_METRICS");

        return serviceNameList.get(serviceName);
    }
}
