package com.incs83.app.utils;

import java.util.HashMap;

public class WiFiUtils {
    private static HashMap<String, String> BAND_NAME_MAP;
    static {
        BAND_NAME_MAP = new HashMap<>();
        BAND_NAME_MAP.put("2.4G", "2.4GHz");
        BAND_NAME_MAP.put("5G", "5GHz");
        BAND_NAME_MAP.put("5GHi", "5GHz High");
        BAND_NAME_MAP.put("5GLo", "5GHz Low");
        BAND_NAME_MAP.put("6G", "6GHz");
    }
    public static String getBandName(String radioKey) {
        return BAND_NAME_MAP.get(radioKey);
    }
}
