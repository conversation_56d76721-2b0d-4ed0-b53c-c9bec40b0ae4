package com.incs83.app.utils;

import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by hari on 5/2/18.
 */
public class ValidationUtil {
    private static Pattern pattern;
    private static Matcher matcher;

    private static final String IPADDRESS_PATTERN =
            "^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])$";

    private static final String EMAIL_PATTERN =
            "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
                    + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";
    private static final String MAC_PATTERN = "^([0-9a-fA-F][0-9a-fA-F]:){5}([0-9a-fA-F][0-9a-fA-F])$";

    private static final String URL_PATTERN = "^(http|https)://.*$";

    private static final String MQTT_URL_PATTERN = "^(tcp|ssl)://.*$";

    private static final String VALID_TEXT_PATTERN = "^[a-zA-Z\\\\d\\\\-_\\\\s]+$";

    private static final String VALID_HOUR_RANGE = "^(2[0-3][h]|1[0-9][h]|[1-9][h])$";

    private static final String VALID_DAY_RANGE = "^(1[0-8][d]|[1-9][d])$";

    private static final String VALID_SEARCHING_CHARACTER = "[a-zA-Z0-9-._ @:]*";

    private static final String VALID_PHONE_NUMBER_PATTERN = "^(\\d{10}|\\d{12})$";

    private static final String VALID_INTEGER_VALUE =".*[^0-9].*";

    private static final String VALID_SN_PATTERN = "[0-9a-zA-z-.()@]*";

    public static boolean isNullOrEmpty(String str) {
        return Objects.isNull(str) || str.isEmpty();
    }

    public static boolean nonNullOrEmpty(String str) {
        return !isNullOrEmpty(str);
    }

    public static boolean isNullOrEmpty(List list) {
        return Objects.isNull(list) || list.isEmpty();
    }

    public static boolean nonNullOrEmpty(List list) {
        return !isNullOrEmpty(list);
    }


    public static boolean isNotValidIntegerInput(final String input) {
        pattern = Pattern.compile(VALID_INTEGER_VALUE);
        matcher = pattern.matcher(input);
        return (matcher.matches());
    }

    public static boolean validateIpAddress(final String ip) {
        pattern = Pattern.compile(IPADDRESS_PATTERN);
        matcher = pattern.matcher(ip);
        return matcher.matches();
    }

    public static boolean validateMAC(final String mac) {
        pattern = Pattern.compile(MAC_PATTERN);
        matcher = pattern.matcher(mac);
        return matcher.matches();
    }

    public static boolean isContainSpecialCharacter(final String charSequenece) {
        pattern = Pattern.compile("[a-zA-Z0-9-._ ]*");
        matcher = pattern.matcher(charSequenece);

        return matcher.matches();
    }

    public static boolean isValidPassword(final String charSequenece) {
        pattern = Pattern.compile("[^\\s]+");
        matcher = pattern.matcher(charSequenece);

        return matcher.matches();
    }

    public static boolean isValidURL(String url) {
        pattern = Pattern.compile(URL_PATTERN);
        matcher = pattern.matcher(url);
        return matcher.matches();
    }

    public static boolean isValidMqttURL(String url) {
        pattern = Pattern.compile(MQTT_URL_PATTERN);
        matcher = pattern.matcher(url);
        return matcher.matches();
    }

    public static boolean isValidEmail(String email) {
        pattern = Pattern.compile(EMAIL_PATTERN);
        matcher = pattern.matcher(email);
        return matcher.matches();
    }

    public static boolean isValidText(String data) {
        pattern = Pattern.compile(VALID_TEXT_PATTERN);
        matcher = pattern.matcher(data);
        return matcher.matches();
    }

    public static boolean isHourValid(String data) {
        pattern = Pattern.compile(VALID_HOUR_RANGE, Pattern.CASE_INSENSITIVE);
        matcher = pattern.matcher(data);
        return matcher.matches();
    }

    public static boolean isDayValid(String data) {
        pattern = Pattern.compile(VALID_DAY_RANGE, Pattern.CASE_INSENSITIVE);
        matcher = pattern.matcher(data);
        return matcher.matches();
    }

    public static String isNullString(String data) {
        return data.toLowerCase().equals("null") ? null : data;
    }

    public static boolean isValidSearchingCharacter(final String charSequenece) {
        pattern = Pattern.compile(VALID_SEARCHING_CHARACTER);
        matcher = pattern.matcher(charSequenece);

        return matcher.matches();
    }

    public static boolean isValidPhoneNumber(String phoneNo) {
        pattern = Pattern.compile(VALID_PHONE_NUMBER_PATTERN);
        matcher = pattern.matcher(phoneNo);
        return matcher.matches();
    }

    public static boolean isValidSnString(final String charSequenece) {
        return Pattern.matches(VALID_SN_PATTERN, charSequenece);
    }
}
