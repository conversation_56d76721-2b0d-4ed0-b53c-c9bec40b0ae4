package com.incs83.app.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.entities.Role;
import com.incs83.app.entities.Subscriber;
import com.incs83.security.tokenFactory.Authority;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ApplicationCommonUtils {
    public static List<Authority> generateScopePayloadForToken(Subscriber subscriber) {
        List<Authority> authorities = new ArrayList<>();
        subscriber.getSubscriberRole().forEach(userRole -> {
            Role role = userRole.getRole();
            Authority authority = new Authority();
            authority.setName(role.getName());
            authority.setService("Optim");
            authority.setGrantBy(userRole.getGrantBy());
            authority.setSource(userRole.getSource());
            try {
                List<com.incs83.security.tokenFactory.Entitlement> list = (List<com.incs83.security.tokenFactory.Entitlement>) new ObjectMapper().readValue(role.getMapping(), List.class);
                List<com.incs83.security.tokenFactory.Entitlement> entitlements = new ArrayList<>();
                for (int i = 0; i < list.size(); i++) {
                    com.incs83.security.tokenFactory.Entitlement entitlement = new ObjectMapper().convertValue(list.get(i), com.incs83.security.tokenFactory.Entitlement.class);
                    entitlements.add(entitlement);
                }
                authority.setEntitlements(entitlements);
            } catch (IOException e) {
                e.printStackTrace();
            }
            authorities.add(authority);
        });
        return authorities;
    }
}
