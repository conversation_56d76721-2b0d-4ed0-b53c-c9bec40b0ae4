package com.incs83.app.utils;/* sakshi created on 11/12/19 inside the package - com.incs83.app.utils */

import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.util.CommonUtils;
import com.mongodb.BasicDBObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.incs83.app.constants.misc.ApplicationConstants.RPC_STATS;

@Service
@EnableAsync
public class AsyncUtils {
    @Autowired
    private MongoServiceImpl mongoService;

    @Async("threadPoolTaskExecutor")
    public void createRPCStats(String operation, String tid, Long requestTimestamp, Long responseTimestamp, Boolean success) {
            BasicDBObject basicDBObject = new BasicDBObject();
            basicDBObject.put("_id", CommonUtils.generateUUID());
            basicDBObject.put("operation", operation);
            basicDBObject.put("tid", tid);
            basicDBObject.put("success", success);
            basicDBObject.put("requestTimestamp", requestTimestamp);
            basicDBObject.put("responseTimestamp", responseTimestamp);
            basicDBObject.put("timestamp", CommonUtils.getCurrentTimeInMillis());
            basicDBObject.put("dateCreated", new Date());

            mongoService.create(RPC_STATS, basicDBObject);
    }

}
