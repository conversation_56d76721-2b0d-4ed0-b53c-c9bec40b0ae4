package com.incs83.app.utils;

import com.incs83.app.common.v2.AgentVersion;
import com.incs83.app.service.data.MongoServiceImpl;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import static com.incs83.app.constants.misc.ActiontecConstants.AP_DETAIL;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;

@Service
public class EquipmentUtils {
    private final Logger logger = LogManager.getLogger(this.getClass());

    @Autowired
    private MongoServiceImpl mongoService;

    public AgentVersion getAgentVersion(String userId, String serial) {
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("userId", userId);
        queryParams.put("serialNumber", serial);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        String buildVersion = String.valueOf(aPDetails.get("buildVersion"));
        AgentVersion agentVersion = new AgentVersion(buildVersion);

        logger.debug("Agent Version: userId[{}] serial[{}] buildVersion[{}] Major[{}] Minor[{}] Patch[{}]", userId, serial, buildVersion, agentVersion.getMajorVersion(), agentVersion.getMinorVersion(), agentVersion.getPatchVersion());
        return agentVersion;
    }

    public String getEquipmentUserId(String equipmentSerialId) {
        String userId = "";
        HashMap<String, String> queryParams = new HashMap<>();
        HashMap<String, String> appendableParams = new HashMap<>();
        queryParams.put("serialNumber", equipmentSerialId);

        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoFieldOptions.clear();
        mongoFieldOptions.put("_id", 0);

        DBObject aPDetails = mongoService.findOne(queryParams, appendableParams, AP_DETAIL, TIMESTAMP, DESC, mongoFieldOptions);
        if(aPDetails != null)
            userId = String.valueOf(aPDetails.get("userId"));

        logger.info("equipment serialId: {}, userId: {}", equipmentSerialId, userId);
        return userId;
    }
}
