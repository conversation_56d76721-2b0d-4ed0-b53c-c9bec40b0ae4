package com.incs83.app.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.business.v2.RPCUtilityService;
import com.incs83.app.business.v3.ManageFSecureService;
import com.incs83.app.constants.templates.MqttTemplate;
import com.incs83.app.service.data.MongoServiceImpl;
import com.incs83.service.CommonService;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TIMESTAMP;
import static com.incs83.app.constants.misc.FsecureConstants.*;
import static com.incs83.app.constants.misc.FsecureConstants.KEY_REGISTRATION;

@Service
@EnableAsync
public class FsecureCacheHelper {
    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private MongoServiceImpl mongoService;

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private RPCUtilityService rpcUtilityService;
@Autowired
    private ManageFSecureService manageFSecureService;
    @Autowired
    private CommonService commonService;


    private static final Logger LOG = LogManager.getLogger("org");

    @Async
    public void processPutRegistrationAndInitiateCacheSync(BasicDBObject dataToUpdate, BasicDBObject update, String rgwSerial, BasicDBObject query, Boolean registrationValid, HashMap<String, Object> registrationObj) throws Exception {

        HashMap<String, Object> registration = null;
        try {

            LOG.info("Asyn method is called and before try");
            registration = (HashMap<String, Object>) registrationObj.get(KEY_REGISTRATION);
            if (Objects.isNull(registration)) {
                registration = new HashMap<String, Object>();
            }

            dataToUpdate.put(KEY_IS_CACHE_SYNC, true);
            update.put("$set", dataToUpdate);
            mongoService.update(query, update, false, false, FSECURE_CACHE);
            LOG.info("cache sync is updated to true");

            HashMap<String, Object> appendableParams = new HashMap<>();
            BasicDBObject fieldsToRemove = new BasicDBObject();
            fieldsToRemove.put("profiles.id", 0);
            DBObject dbObject = mongoService.findOne(query, appendableParams, FSECURE_CACHE, TIMESTAMP, DESC, fieldsToRemove);

            if (Objects.nonNull(dbObject)) {

                List<BasicDBObject> profiles = (List<BasicDBObject>) dbObject.get(KEY_PROFILES);
                List<BasicDBObject> devices = (List<BasicDBObject>) dbObject.get(KEY_DEVICES);
                List<BasicDBObject> websiteExceptions = (List<BasicDBObject>) dbObject.get(KEY_WEBSITE_EXCEPTIONS);
                BasicDBObject protection = (BasicDBObject) dbObject.get(KEY_PROTECTION);


                LOG.info(" Start of regular expression  ");
                List<Object> fsceureRegistrationRequest = new ArrayList<>();
                for (BasicDBObject profile : profiles) {
                    HashMap<String, Object> putProfile = new HashMap<>();
                    putProfile.put("-URI-", "/cpe-security/profiles");
                    putProfile.put("-PROFILE-", mapper.writeValueAsString(profile));
                    HashMap<String, Object> prof = rpcUtilityService.registrationPutRPC(putProfile, MqttTemplate.fRPC_PROFILE);
                    if (Objects.nonNull(prof))
                        fsceureRegistrationRequest.add(prof);
                }

                for (BasicDBObject device : devices) {
                    HashMap<String, Object> putDevice = new HashMap<>();
                    String deviceId = (String) device.get("macAddress");
                    putDevice.put("-URI-", "/cpe-security/devices/" + deviceId);
                    putDevice.put("-DEVICE-", mapper.writeValueAsString(device));
                    HashMap<String, Object> dev = rpcUtilityService.registrationPutRPC(putDevice, MqttTemplate.fRPC_DEVICE);
                    if (Objects.nonNull(dev))
                        fsceureRegistrationRequest.add(dev);
                }

                HashMap<String, Object> pubWebExp = new HashMap<>();
                pubWebExp.put("-WEBSITE_EXCEPTION-", mapper.writeValueAsString(websiteExceptions));
                HashMap<String, Object> webExp = rpcUtilityService.registrationPutRPC(pubWebExp, MqttTemplate.fRPC_WEBSITE_EXCEPTION);
                if (Objects.nonNull(webExp))
                    fsceureRegistrationRequest.add(webExp);

                HashMap<String, Object> putProtection = new HashMap<>();
                putProtection.put("-PROTECTION-", mapper.writeValueAsString(protection));
                HashMap<String, Object> protect = rpcUtilityService.registrationPutRPC(putProtection, MqttTemplate.fRPC_PROTECTION);
                if (Objects.nonNull(protect))
                    fsceureRegistrationRequest.add(protect);

                HashMap<String, String> queryParam = new HashMap<>();
                queryParam.put("userId", rgwSerial);
                queryParam.put("serialNumber", rgwSerial);
                DBObject apDetail = mongoService.findOne(AP_DETAIL, queryParam);
                String isp = "";
                if (Objects.nonNull(apDetail)) {
                    isp = (String) apDetail.get("isp");
                }
                String tid = CommonUtils.generateUUID();
                HashMap<String, String> publishParam = new HashMap<>();
                publishParam.put("-TID-", tid);
                publishParam.put("-USER_ID-", rgwSerial);
                publishParam.put("-S_ID-", rgwSerial);
                publishParam.put("-ISP-", isp);
                publishParam.put("-REQUEST-", mapper.writeValueAsString(fsceureRegistrationRequest));

                HashMap<String, Object> data = new HashMap<>();
                data.put("_id", tid);
                data.put("isTimeout", false);
                data.put("userId", rgwSerial);
                data.put("dateCreated", new Date());
                data.put("isp", isp);
                mongoService.create(JSON_RPC_V3_INFO, data);

                HashMap<String, String> equipmentProps = commonService.read(EQUIPMENT_DIAGNOSTIC_CONFIG);
                Integer max_Tries = Integer.valueOf(equipmentProps.get(RPC_POLL_COUNT));
                rpcUtilityService.publishToTopic(publishParam, MqttTemplate.fRPC_Registration, MqttTemplate.TOPIC_FOR_RPC_CALL, max_Tries);

                LOG.info("final registrationPutRPC2 " + fsceureRegistrationRequest.toString());
                registration.put("valid", true);
            }
            else{
                LOG.info( " Service Telephone Number not found in fsecureCache " ) ;
            }

        } catch (Exception e) {
            registration.put("valid", false);
            LOG.info(" Error in processing async class . Exception is - " + e.toString());

        } finally {
            LOG.info("Asyn method  outer  finally is called ");
            dataToUpdate.put(KEY_IS_CACHE_SYNC, false);
            dataToUpdate.put(KEY_REGISTRATION, registration);
            update.put("$set", dataToUpdate);
            mongoService.update(query, update, false, false, FSECURE_CACHE);
            LOG.info("Asyn method  outer finally is called and updated the registration");

        }
        LOG.info("Async executed successfully without any exception ");
    }

}
