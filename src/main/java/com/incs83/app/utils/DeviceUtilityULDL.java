package com.incs83.app.utils;

import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.Equipment;
import com.incs83.app.service.data.MongoServiceImpl;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;

import java.util.*;
import java.util.stream.Collectors;

import static com.incs83.app.constants.misc.ActiontecConstants.USER_STATION_SLIDING_DATA;

public class DeviceUtilityULDL {
    private MongoServiceImpl mongoService;
    private ManageCommonService manageCommonService;
    private String macAddr;
    private Equipment userEquipment;
    private double downLinkBytes;
    private double upLinkBytes;

    public DeviceUtilityULDL(String macAddr, Equipment userEquipment, MongoServiceImpl mongoService, ManageCommonService manageCommonService) {
        this.macAddr = macAddr;
        this.userEquipment = userEquipment;
        this.mongoService = mongoService;
        this.manageCommonService = manageCommonService;
    }

    public double getDownLinkBytes() {
        return downLinkBytes;
    }

    public double getUpLinkBytes() {
        return upLinkBytes;
    }

    public DeviceUtilityULDL invoke() {
        BasicDBObject fieldOptions = new BasicDBObject();
        TimeZone timeZone = TimeZone.getTimeZone("UTC");
        Calendar now = Calendar.getInstance(timeZone);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);

        long nowMillSec = now.getTimeInMillis();

        int minutes = 60;

        long dateHour = nowMillSec / 1000 - minutes * 60;

        BasicDBObject dateCriteria = new BasicDBObject();
        dateCriteria.put("$gte", manageCommonService.convertMinutesToDateHour(minutes));

        HashMap<String, Object> aggregationWhereClause = new HashMap<>();
        aggregationWhereClause.put("userId", userEquipment.getRgwSerial());
        aggregationWhereClause.put("dateHour", dateCriteria);

        fieldOptions.put("_id", 0);
//        mongoFieldOptions.put("devMap." + macAddr, 1);

        List<DBObject> deviceDownLinkUpLinkBytes = mongoService.findList(aggregationWhereClause, null, USER_STATION_SLIDING_DATA, fieldOptions);

        downLinkBytes = 0.0;
        upLinkBytes = 0.0;
        if (!deviceDownLinkUpLinkBytes.isEmpty()) {
            List<DBObject> actualDevMap = new ArrayList<>();
            for (DBObject dbObject : deviceDownLinkUpLinkBytes) {
                String today = String.valueOf(dbObject.get("date"));
                if (Objects.nonNull(dbObject.get("devMap"))) {
                    List<DBObject> devMap = (List<DBObject>) ((DBObject) dbObject.get("devMap")).get(macAddr);
                    if (Objects.nonNull(devMap)) {
                        actualDevMap.addAll(devMap);
                    }
                }

                if (Objects.nonNull(dbObject.get(today))) {
                    Map<String, Object> dataByDay = (Map<String, Object>) dbObject.get(today);
                    for (String key : dataByDay.keySet()) {
                        Map<String, Map<String, Object>> userSlidingData = (Map<String, Map<String, Object>>) dataByDay.get(key);
                        List<DBObject> latestDevMap = (List<DBObject>) userSlidingData.get("devMap").get(macAddr);
                        if (Objects.nonNull(latestDevMap)) {
                            actualDevMap.addAll(latestDevMap);
                        }
                    }
                }
            }

            Calendar criteriaCalendar = Calendar.getInstance();
            criteriaCalendar.setTime(new Date(new Date().getTime() - (minutes < 60 ? (60 * 60L * 1000L) : minutes * 60L * 1000L)));
            long currTimeStamp = criteriaCalendar.getTimeInMillis();
            actualDevMap = actualDevMap.stream().filter(element -> ((Objects.nonNull(element.get("timestamp")) ? Long.valueOf(element.get("timestamp").toString()) : 0) > currTimeStamp && (Objects.nonNull(element.get("rx")) && Objects.nonNull(element.get("tx"))))).collect(Collectors.toList());

            if (!actualDevMap.isEmpty()) {
                downLinkBytes = downLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("rx")) ? Double.valueOf(element.get("rx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
                upLinkBytes = upLinkBytes + actualDevMap.stream().map(element -> Objects.nonNull(element.get("tx")) ? Double.valueOf(element.get("tx").toString()) : 0.0).collect(Collectors.summingDouble(p -> p));
            }
        }
        return this;
    }
}
