package com.incs83.app.utils;

/**
 * created By  KOUSHINDRA on Thu, 9/8/18
 */
@Deprecated
public class RequestUtils {
    /*public static UserAP createUserAP(CreateSubscriberRequest createSubscriberRequest, UserRequest userRequest, UserDTO userDTO) {
        UserAP userAP = new UserAP()
                .setId(CommonUtils.generateUUID())
                .setEmail(userDTO.getId())
                .setGroupId(userRequest.getCompartments().get(0))
                .setApId(createSubscriberRequest.getSerialNumber())
                .setPhoneNo(createSubscriberRequest.getPhoneNo())
                .setAccountNo(createSubscriberRequest.getAccountNo())
                .setSubscriberBandwidth(createSubscriberRequest.getSubscriberBandwidth())
                .setMobileSubscribed(createSubscriberRequest.isMobileSubscribed())
                .setAlexaVoiceSubscribed(createSubscriberRequest.isAlexaVoiceSubscribed())
                .setGoogleHomeVoiceSubscribed(createSubscriberRequest.isGoogleHomeVoiceSubscribed())
                .setStaticRGW(createSubscriberRequest.isStaticRGW());
        CommonUtils.setCreateEntityFields(userAP);
        CommonUtils.setUpdateEntityFields(userAP);
        return userAP;
    }*/

    /*public static UserApDTO mapToUserApDTO(UserAP userAP) {
        return new UserApDTO()
                .setSubscriberId(userAP.getUserId())
                .setSerialNumber(userAP.getApId().startsWith("NA-") ? "NA" : userAP.getApId());
    }*/

}
