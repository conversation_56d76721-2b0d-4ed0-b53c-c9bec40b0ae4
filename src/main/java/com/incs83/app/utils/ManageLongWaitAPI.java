package com.incs83.app.utils;

import com.incs83.context.ExecutionContext;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

import static java.util.Collections.emptyList;
import static java.util.concurrent.CompletableFuture.completedFuture;

@Service
public class ManageLongWaitAPI {

    private static final Logger LOG = LogManager.getLogger("org");
    private ExecutorService executor = Executors.newFixedThreadPool(5);

    public CompletableFuture<ResponseDTO<?>> call(FutureCall futureCall) {
//        LOG.info("Container Thread Name : " + Thread.currentThread().getName());
        CompletableFuture<ResponseDTO<?>> responseDTOCompletableFuture = new CompletableFuture<>();
        ExecutionContext executionContext = ExecutionContext.get();
        CompletableFuture.supplyAsync(() -> {
            ResponseDTO<?> responseDTO = new ApiResponseDTO<>();
            ExecutionContext.CONTEXT.set(executionContext);
            try {
//                LOG.info("Worker Thread Name : " + Thread.currentThread().getName());
                responseDTO = futureCall.execute();
                responseDTOCompletableFuture.complete(responseDTO);
            } catch (Exception e) {
                responseDTOCompletableFuture.completeExceptionally(e);
            }
            return responseDTO;
        });
//        LOG.info("Total number of threads in MACHINE  : " + ManagementFactory.getThreadMXBean().getThreadCount());
        return responseDTOCompletableFuture;
    }



    public <T> T tap(T obj, Consumer<T> c) {
        c.accept(obj);
        return obj;
    }

    public <T> CompletableFuture<List<T>> awaitAll(Collection<? extends CompletionStage<T>> stages) {
        return stages.stream().map(st -> st.thenApply(Collections::singletonList)).reduce(completedFuture(emptyList()), (f1, f2) -> f1.thenCombine(f2, (a, b) -> tap(new ArrayList<>(a), l -> l.addAll(b)))).toCompletableFuture();
    }

    public <R> CompletableFuture<R> toFuture(FutureCallCommon futureCall) throws Exception {
        CompletableFuture<R> future = CompletableFuture.supplyAsync(() -> {
            R r = null;
            try {
                r = (R) futureCall.execute();
            } catch (Exception e) {
                e.printStackTrace();
            }
            return r;
        }, executor);

        return future;
    }


    public static HashMap getForkPoolStats() {
        HashMap<String, Long> futureCallStats = new HashMap<>();
        futureCallStats.put("totalThreads", Long.valueOf(ManagementFactory.getThreadMXBean().getThreadCount()));
        futureCallStats.put("timestamp", CommonUtils.getCurrentTimeInMillis());
        return futureCallStats;
    }
}
