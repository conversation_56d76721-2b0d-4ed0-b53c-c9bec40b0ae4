package com.incs83.app.utils;

import com.incs83.app.constants.misc.ApplicationConstants;
import org.apache.tools.ant.taskdefs.Local;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.incs83.app.constants.misc.ApplicationConstants.CALENDER_CRITERIA_YESTERDAY;
import static com.incs83.app.constants.misc.ApplicationConstants.DATE;

public class CalendarUtils {
    public static Calendar getCalendarInstanceForToday() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.DATE, ApplicationConstants.CALENDER_CRITERIA_TODAY);
        return calendar;
    }

    public static Calendar getCalendarInstanceForYesterday() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.DATE, CALENDER_CRITERIA_YESTERDAY);
        return calendar;
    }

    public static Calendar getCalendarInstanceForTodayMidNight() {
        Calendar todayCalendar = Calendar.getInstance();
        todayCalendar.set(Calendar.HOUR_OF_DAY, 0);
        todayCalendar.set(Calendar.MINUTE, 0);
        todayCalendar.set(Calendar.SECOND, 0);
        todayCalendar.set(Calendar.MILLISECOND, 0);

        return todayCalendar;
    }

    public static long getMidNightTimeStamp(int day) {
        Calendar date = Calendar.getInstance();
        date.set(Calendar.DAY_OF_MONTH, date.get(Calendar.DAY_OF_MONTH) - (day - 1));
        date.set(Calendar.HOUR_OF_DAY, 0);
        date.set(Calendar.MINUTE, 0);
        date.set(Calendar.SECOND, 0);
        date.set(Calendar.MILLISECOND, 0);
        return date.getTimeInMillis();
    }

    public static String getCurrentDateInYDM(Long timestamp) {
        LocalDateTime ldt;
        if (timestamp == null || timestamp.equals(0L)) {
            ldt = LocalDateTime.now();
        } else {
            ldt = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.of("UTC"));
        }
        return DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.ENGLISH).format(ldt);
    }

    public static HashMap<String, Object> getDaysForCriteria(int days, String criteria) throws ParseException {

        HashMap<String, Object> result = new HashMap<>();
        List<Date> dateList = new ArrayList<>();

        ZonedDateTime ldtNow = ZonedDateTime.now();

        if(criteria.equals(DATE)) {
            ZonedDateTime truncatedDateTime = ldtNow.minusDays(days).truncatedTo(ChronoUnit.DAYS);

            while ( truncatedDateTime.isBefore(ldtNow) ) {
                dateList.add(Date.from(truncatedDateTime.toInstant()));
                truncatedDateTime = truncatedDateTime.plusDays(1);
            }
        } else {
            ZonedDateTime truncatedDateTime = ldtNow.minusDays(days).truncatedTo(ChronoUnit.DAYS).withDayOfMonth(1);

            while ( truncatedDateTime.isBefore(ldtNow) ) {
                dateList.add(Date.from(truncatedDateTime.toInstant()));
                truncatedDateTime = truncatedDateTime.plusMonths(1);
            }
        }

        result.put("ldtTime", ldtNow.minusDays(days));
        result.put("dates", dateList);

        return result;

    }
}
