package com.incs83.app.utils;

import com.datastax.driver.core.utils.UUIDs;
import com.incs83.config.KafkaConfig;
import com.incs83.pubsub.KafkaPublisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Calendar;
import java.util.HashMap;

import static com.incs83.app.constants.misc.ActiontecConstants.PLATFORM_LOGS;
import static com.incs83.app.constants.misc.ActiontecConstants.SUG_ACT_TYPE;

@Service
public class AuditUtils {

    @Autowired
    private KafkaConfig kafkaConfig;

    @Autowired
    private KafkaPublisher kafkaPublisher;

    private static final String[] IP_HEADERS = {
            "X-Forwarded-For",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_X_FORWARDED_FOR",
            "HTTP_X_FORWARDED",
            "HTTP_X_CLUSTER_CLIENT_IP",
            "HTTP_CLIENT_IP",
            "HTTP_FORWARDED_FOR",
            "HTTP_FORWARDED",
            "HTTP_VIA",
            "REMOTE_ADDR"
    };

    public String getRequestIP(HttpServletRequest request) {
        for (String header: IP_HEADERS) {
            String value = request.getHeader(header);
            if (value == null || value.isEmpty()) {
                continue;
            }
            String[] parts = value.split("\\s*,\\s*");
            return parts[0];
        }
        return request.getRemoteAddr();
    }

    public void createLogAndPublish(String method, String operation, String userId, String ip, String url, String apId, HashMap<String, Object> payload, int responseCode, String responseMessage) throws Exception {
        HashMap<String, Object> auditLogs = new HashMap();
        auditLogs.put(SUG_ACT_TYPE, PLATFORM_LOGS);
        auditLogs.put("id", UUIDs.timeBased().toString().replaceAll("-", ""));
        auditLogs.put("userId", userId);
        auditLogs.put("ip", ip);
        auditLogs.put("url", url);
        auditLogs.put("apId", apId);
        auditLogs.put("time", Calendar.getInstance().getTimeInMillis());
        auditLogs.put("method", method);
        auditLogs.put("operation", operation);
        auditLogs.put("payload", payload);
        auditLogs.put("responseCode", responseCode);
        auditLogs.put("responseMessage", responseMessage);
        kafkaPublisher.publishAuditLogsToKafka(auditLogs, kafkaConfig.getTopic());

    }
}
