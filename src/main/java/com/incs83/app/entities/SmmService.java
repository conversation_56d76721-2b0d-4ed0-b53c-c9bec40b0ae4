package com.incs83.app.entities;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Set;

@Entity
@Table(name = "service")
public class SmmService implements Serializable {

    @Id
    private String id;

    @Column(name = "name")
    private String name;

    @Column(name = "productionVersion")
    private String productionVersion;

    @Column(name = "deletable")
    private Boolean deletable;

    @OneToMany(mappedBy = "smmService", cascade = CascadeType.ALL)
    private Set<SmmApp> smmApps;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductionVersion() {
        return productionVersion;
    }

    public void setProductionVersion(String productionVersion) {
        this.productionVersion = productionVersion;
    }

    public Boolean getDeletable() {
        return deletable;
    }

    public void setDeletable(Boolean deletable) {
        this.deletable = deletable;
    }

    public Set<SmmApp> getSmmApps() {
        return smmApps;
    }

    public void setSmmApps(Set<SmmApp> smmApps) {
        this.smmApps = smmApps;
    }
}
