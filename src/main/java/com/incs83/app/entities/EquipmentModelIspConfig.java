package com.incs83.app.entities;

import com.incs83.app.entities.id.EquipmentModelIspConfigId;

import javax.persistence.*;

@Entity
@IdClass(EquipmentModelIspConfigId.class)
@Table(name = "equipment_model_isp_config", schema = "actiontec")
public class EquipmentModelIspConfig {

    @Id
    @Column(name = "equipment_model_id", length = 255, nullable = false)
    private String equipmentModelId;

    @Id
    @Column(name = "isp_id", length = 255, nullable = false)
    private String ispId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "equipment_model_id", insertable = false, updatable = false)
    private EquipmentModel equipmentModel;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "min_version_firmware_id")
    private Firmware minVersionFirmware;

    // getters and setters

    public String getEquipmentModelId() {
        return equipmentModelId;
    }

    public void setEquipmentModelId(String equipmentModelId) {
        this.equipmentModelId = equipmentModelId;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public EquipmentModel getEquipmentModel() {
        return equipmentModel;
    }

    public void setEquipmentModel(EquipmentModel equipmentModel) {
        this.equipmentModel = equipmentModel;
    }

    public Firmware getMinVersionFirmware() {
        return minVersionFirmware;
    }

    public void setMinVersionFirmware(Firmware minVersionFirmware) {
        this.minVersionFirmware = minVersionFirmware;
    }
}
