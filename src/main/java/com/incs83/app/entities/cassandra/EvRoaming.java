package com.incs83.app.entities.cassandra;

import com.datastax.driver.mapping.annotations.ClusteringColumn;
import com.datastax.driver.mapping.annotations.PartitionKey;
import com.datastax.driver.mapping.annotations.Table;

import java.util.Date;

@Table(keyspace = "actiontec", name = "evroaming")
public class EvRoaming {
    @PartitionKey(0)
    private String userid;
    @ClusteringColumn(1)
    private long timestamp;
    private Date datecreated;
    private int elapsed;
    private int ftroam;
    private String isp;
    @ClusteringColumn
    private String macaddress;
    private int nband;
    private String nbssid;
    private double nrssi;
    private double nrxphyrate;
    private double ntxphyrate;
    private int oband;
    private String obssid;
    private double orssi;
    private double orxphyrate;
    private double otxphyrate;
    private String serialnumber;


    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDatecreated() {
        return datecreated;
    }

    public void setDatecreated(Date datecreated) {
        this.datecreated = datecreated;
    }

    public int getElapsed() {
        return elapsed;
    }

    public void setElapsed(int elapsed) {
        this.elapsed = elapsed;
    }

    public int getFtroam() {
        return ftroam;
    }

    public void setFtroam(int ftroam) {
        this.ftroam = ftroam;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getMacaddress() {
        return macaddress;
    }

    public void setMacaddress(String macaddress) {
        this.macaddress = macaddress;
    }

    public int getNband() {
        return nband;
    }

    public void setNband(int nband) {
        this.nband = nband;
    }

    public String getNbssid() {
        return nbssid;
    }

    public void setNbssid(String nbssid) {
        this.nbssid = nbssid;
    }

    public double getNrssi() {
        return nrssi;
    }

    public void setNrssi(double nrssi) {
        this.nrssi = nrssi;
    }

    public double getNrxphyrate() {
        return nrxphyrate;
    }

    public void setNrxphyrate(double nrxphyrate) {
        this.nrxphyrate = nrxphyrate;
    }

    public double getNtxphyrate() {
        return ntxphyrate;
    }

    public void setNtxphyrate(double ntxphyrate) {
        this.ntxphyrate = ntxphyrate;
    }

    public int getOband() {
        return oband;
    }

    public void setOband(int oband) {
        this.oband = oband;
    }

    public String getObssid() {
        return obssid;
    }

    public void setObssid(String obssid) {
        this.obssid = obssid;
    }

    public double getOrssi() {
        return orssi;
    }

    public void setOrssi(double orssi) {
        this.orssi = orssi;
    }

    public double getOrxphyrate() {
        return orxphyrate;
    }

    public void setOrxphyrate(double orxphyrate) {
        this.orxphyrate = orxphyrate;
    }

    public double getOtxphyrate() {
        return otxphyrate;
    }

    public void setOtxphyrate(double otxphyrate) {
        this.otxphyrate = otxphyrate;
    }

    public String getSerialnumber() {
        return serialnumber;
    }

    public void setSerialnumber(String serialnumber) {
        this.serialnumber = serialnumber;
    }
}
