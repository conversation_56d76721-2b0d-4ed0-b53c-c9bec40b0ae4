package com.incs83.app.entities.cassandra;

import com.datastax.driver.mapping.annotations.ClusteringColumn;
import com.datastax.driver.mapping.annotations.PartitionKey;
import com.datastax.driver.mapping.annotations.Table;

import java.util.Date;

@Table(keyspace = "actiontec", name = "evsteeringlogs")
public class EvSteeringLogs {
    @PartitionKey(0)
    private String userId;
    @ClusteringColumn(1)
    private long timestamp;
    private Date datecreated;
    private String ibssbssid;
    private long ibssrcpi;
    private String ibssrole;
    private long ibssrssi;
    private long ifat;
    private String isp;
    private String log;
    @ClusteringColumn
    private String macaddress;
    private long oairusage;
    private String obssbssid;
    private long obssrcpi;
    private String obssrole;
    private long obssrssi;
    private long ofat;
    private double ophyrate;
    private String serialnumber;
    private String steeringend;
    private long steeringtime;
    private String steeringtype;
    private long tairusage;
    private String tbssbssid;
    private long tbssrcpi;
    private String tbssrole;
    private long tbssrssi;
    private long tfat;
    private double tphyrate;
    private long ichannel;
    private long ochannel;
    private long tchannel;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public Date getDatecreated() {
        return datecreated;
    }

    public void setDatecreated(Date datecreated) {
        this.datecreated = datecreated;
    }

    public String getIbssbssid() {
        return ibssbssid;
    }

    public void setIbssbssid(String ibssbssid) {
        this.ibssbssid = ibssbssid;
    }

    public long getIbssrcpi() {
        return ibssrcpi;
    }

    public void setIbssrcpi(long ibssrcpi) {
        this.ibssrcpi = ibssrcpi;
    }

    public String getIbssrole() {
        return ibssrole;
    }

    public void setIbssrole(String ibssrole) {
        this.ibssrole = ibssrole;
    }

    public long getIbssrssi() {
        return ibssrssi;
    }

    public void setIbssrssi(long ibssrssi) {
        this.ibssrssi = ibssrssi;
    }

    public long getIfat() {
        return ifat;
    }

    public void setIfat(long ifat) {
        this.ifat = ifat;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getLog() {
        return log;
    }

    public void setLog(String log) {
        this.log = log;
    }

    public String getMacaddress() {
        return macaddress;
    }

    public void setMacaddress(String macaddress) {
        this.macaddress = macaddress;
    }

    public long getOairusage() {
        return oairusage;
    }

    public void setOairusage(long oairusage) {
        this.oairusage = oairusage;
    }

    public String getObssbssid() {
        return obssbssid;
    }

    public void setObssbssid(String obssbssid) {
        this.obssbssid = obssbssid;
    }

    public long getObssrcpi() {
        return obssrcpi;
    }

    public void setObssrcpi(long obssrcpi) {
        this.obssrcpi = obssrcpi;
    }

    public String getObssrole() {
        return obssrole;
    }

    public void setObssrole(String obssrole) {
        this.obssrole = obssrole;
    }

    public long getObssrssi() {
        return obssrssi;
    }

    public void setObssrssi(long obssrssi) {
        this.obssrssi = obssrssi;
    }

    public long getOfat() {
        return ofat;
    }

    public void setOfat(long ofat) {
        this.ofat = ofat;
    }

    public double getOphyrate() {
        return ophyrate;
    }

    public void setOphyrate(double ophyrate) {
        this.ophyrate = ophyrate;
    }

    public String getSerialnumber() {
        return serialnumber;
    }

    public void setSerialnumber(String serialnumber) {
        this.serialnumber = serialnumber;
    }

    public String getSteeringend() {
        return steeringend;
    }

    public void setSteeringend(String steeringend) {
        this.steeringend = steeringend;
    }

    public long getSteeringtime() {
        return steeringtime;
    }

    public void setSteeringtime(long steeringtime) {
        this.steeringtime = steeringtime;
    }

    public String getSteeringtype() {
        return steeringtype;
    }

    public void setSteeringtype(String steeringtype) {
        this.steeringtype = steeringtype;
    }

    public long getTairusage() {
        return tairusage;
    }

    public void setTairusage(long tairusage) {
        this.tairusage = tairusage;
    }

    public String getTbssbssid() {
        return tbssbssid;
    }

    public void setTbssbssid(String tbssbssid) {
        this.tbssbssid = tbssbssid;
    }

    public long getTbssrcpi() {
        return tbssrcpi;
    }

    public void setTbssrcpi(long tbssrcpi) {
        this.tbssrcpi = tbssrcpi;
    }

    public String getTbssrole() {
        return tbssrole;
    }

    public void setTbssrole(String tbssrole) {
        this.tbssrole = tbssrole;
    }

    public long getTbssrssi() {
        return tbssrssi;
    }

    public void setTbssrssi(long tbssrssi) {
        this.tbssrssi = tbssrssi;
    }

    public long getTfat() {
        return tfat;
    }

    public void setTfat(long tfat) {
        this.tfat = tfat;
    }

    public double getTphyrate() {
        return tphyrate;
    }

    public void setTphyrate(double tphyrate) {
        this.tphyrate = tphyrate;
    }

    public long getIchannel() {
        return ichannel;
    }

    public void setIchannel(long ichannel) {
        this.ichannel = ichannel;
    }

    public long getOchannel() {
        return ochannel;
    }

    public void setOchannel(long ochannel) {
        this.ochannel = ochannel;
    }

    public long getTchannel() {
        return tchannel;
    }

    public void setTchannel(long tchannel) {
        this.tchannel = tchannel;
    }
}
