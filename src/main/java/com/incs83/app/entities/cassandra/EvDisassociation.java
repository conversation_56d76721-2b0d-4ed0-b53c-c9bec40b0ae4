package com.incs83.app.entities.cassandra;

import com.datastax.driver.mapping.annotations.ClusteringColumn;
import com.datastax.driver.mapping.annotations.PartitionKey;
import com.datastax.driver.mapping.annotations.Table;

import java.util.Date;

@Table(keyspace = "actiontec", name = "evdisassociation")
public class EvDisassociation {
    @PartitionKey(0)
    private String userId;
    @ClusteringColumn(0)
    private String macAddress;
    @ClusteringColumn(1)
    private String assocType;
    @ClusteringColumn(2)
    private long timestamp;
    private long assoc;
    private int band;
    private String bssid;
    private Date dateCreated;
    private String event;
    private String isp;
    private String serialNumber;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getBand() {
        return band;
    }

    public void setBand(int band) {
        this.band = band;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public long getAssoc() {
        return assoc;
    }

    public void setAssoc(long assoc) {
        this.assoc = assoc;
    }

    public String getAssocType() {
        return assocType;
    }

    public void setAssocType(String assocType) {
        this.assocType = assocType;
    }

    public Date getDateCreated() {
        return dateCreated;
    }

    public void setDateCreated(Date dateCreated) {
        this.dateCreated = dateCreated;
    }


    @Override
    public String toString() {
        return "EvDisassociation{" +
                "userId='" + userId + '\'' +
                ", macAddress='" + macAddress + '\'' +
                ", assocType='" + assocType + '\'' +
                ", timestamp=" + timestamp +
                ", assoc=" + assoc +
                ", band=" + band +
                ", bssid='" + bssid + '\'' +
                ", dateCreated=" + dateCreated +
                ", event='" + event + '\'' +
                ", isp='" + isp + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                '}';
    }
}
