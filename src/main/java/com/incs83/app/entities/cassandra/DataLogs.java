package com.incs83.app.entities.cassandra;

import com.datastax.driver.mapping.annotations.ClusteringColumn;
import com.datastax.driver.mapping.annotations.PartitionKey;
import com.datastax.driver.mapping.annotations.Table;

@Table(keyspace = "actiontec", name = "datalogs")
public class DataLogs {
    @PartitionKey
    private String userId;
    @ClusteringColumn
    private long timestamp;
    private String data;
    private String sugacttype;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSugacttype() {
        return sugacttype;
    }

    public void setSugacttype(String sugacttype) {
        this.sugacttype = sugacttype;
    }
}
