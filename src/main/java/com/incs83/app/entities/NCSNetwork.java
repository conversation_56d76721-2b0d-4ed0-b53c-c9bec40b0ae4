package com.incs83.app.entities;

import com.actiontec.optim.platform.api.v6.enums.NetworkClaimStatus;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "network")
public class NCSNetwork {

    @Id
    @NotNull
    @Column(name = "id")
    private String id;

    @Column(name = "name")
    private String name;

    @NotNull
    @Column(name = "gen_type")
    private String genType;

    @Column(name = "auto_network_id")
    private String autoNetworkId;

    @NotNull
    @Column(name = "createdAt")
    private LocalDateTime createdAt;

    @NotNull
    @Column(name = "createdBy")
    private String createdBy;

    @Column(name = "updatedAt")
    private LocalDateTime updatedAt;

    @Column(name = "updatedBy")
    private String updatedBy;

    @NotNull
    @Column(name = "isp_id")
    private String ispId;

    @Column(name = "claim_status")
    private NetworkClaimStatus claimStatus;

    @Column(name = "network_token_id")
    private String networkTokenId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public @NotNull String getGenType() { return genType; }

    public void setGenType(@NotNull String genType) { this.genType = genType; }

    public String getAutoNetworkId() { return autoNetworkId; }

    public void setAutoNetworkId(String autoNetworkId) { this.autoNetworkId = autoNetworkId; }

    public @NotNull LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(@NotNull LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public @NotNull String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(@NotNull String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public @NotNull String getIspId() {
        return ispId;
    }

    public void setIspId(@NotNull String ispId) {
        this.ispId = ispId;
    }

    public NetworkClaimStatus getClaimStatus() {
        return claimStatus;
    }

    public void setClaimStatus(NetworkClaimStatus claimStatus) {
        this.claimStatus = claimStatus;
    }

    public String getNetworkTokenId() {
        return networkTokenId;
    }

    public void setNetworkTokenId(String networkTokenId) {
        this.networkTokenId = networkTokenId;
    }
}
