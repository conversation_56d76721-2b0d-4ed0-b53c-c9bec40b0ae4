package com.incs83.app.entities;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "app")
public class SmmApp implements Serializable {

    @Id
    private String id;

    @Column(name = "version")
    private String version;

    @Column(name = "description")
    private String description;

    @Column(name = "dependencies")
    private String dependencies;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name="fileId")
    private OptimFile optimFile;

    @ManyToOne
    @JoinColumn(name="serviceId")
    private SmmService smmService;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDependencies() {
        return dependencies;
    }

    public void setDependencies(String dependencies) {
        this.dependencies = dependencies;
    }

    public OptimFile getOptimFile() {
        return optimFile;
    }

    public void setOptimFile(OptimFile optimFile) {
        this.optimFile = optimFile;
    }

    public SmmService getSmmService() {
        return smmService;
    }

    public void setSmmService(SmmService smmService) {
        this.smmService = smmService;
    }
}
