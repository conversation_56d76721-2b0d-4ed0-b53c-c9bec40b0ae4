package com.incs83.app.entities;

import com.incs83.app.entities.id.ClusterNetworkId;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Entity
@Table(name = "cluster_network")
@IdClass(ClusterNetworkId.class)
public class ClusterNetwork {

    @Id
    @Column(name = "cluster_id", length = 255)
    @NotNull
    private String clusterId;

    @Id
    @Column(name = "network_id", length = 255)
    @NotNull
    private String networkId;

    public @NotNull String getClusterId() {
        return clusterId;
    }

    public void setClusterId(@NotNull String clusterId) {
        this.clusterId = clusterId;
    }

    public @NotNull String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(@NotNull String networkId) {
        this.networkId = networkId;
    }
}
