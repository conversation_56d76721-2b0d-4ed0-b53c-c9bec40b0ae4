package com.incs83.app.entities;

import com.incs83.app.entities.id.SubscriberNetworkId;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

@Entity
@Table(name = "subscriber_network")
@IdClass(SubscriberNetworkId.class)
public class SubscriberNetwork {

    @Id
    @Column(name = "subscriber_id", length = 255)
    @NotNull
    private String subscriberId;

    @Id
    @Column(name = "network_id", length = 255)
    @NotNull
    private String networkId;

    public @NotNull String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(@NotNull String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public @NotNull String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(@NotNull String networkId) {
        this.networkId = networkId;
    }
}
