package com.incs83.app.entities;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Entity
@Table(name = "rtty_log")
public class RttyLog {

    @Id
    @NotNull
    @Column(name = "id")
    private String id;

    @Column(name = "device_id", length = 255)
    @NotNull
    private String deviceId;

    @Column(name = "session_id", length = 255)
    @NotNull
    private String sessionId;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name="fileId")
    private OptimFile optimFile;

    @Column(name = "created_at")
    @NotNull
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public OptimFile getOptimFile() {
        return optimFile;
    }

    public void setOptimFile(OptimFile optimFile) {
        this.optimFile = optimFile;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
