package com.incs83.app.entities.id;

import java.io.Serializable;
import java.util.Objects;

/**
 * Composite primary key class for EquipmentModelFirmware entity
 */
public class EquipmentModelFirmwareId implements Serializable {

    private String equipmentModelId;
    private String firmwareId;

    public EquipmentModelFirmwareId() {
    }

    public EquipmentModelFirmwareId(String equipmentModelId, String firmwareId) {
        this.equipmentModelId = equipmentModelId;
        this.firmwareId = firmwareId;
    }

    public String getEquipmentModelId() {
        return equipmentModelId;
    }

    public void setEquipmentModelId(String equipmentModelId) {
        this.equipmentModelId = equipmentModelId;
    }

    public String getFirmwareId() {
        return firmwareId;
    }

    public void setFirmwareId(String firmwareId) {
        this.firmwareId = firmwareId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EquipmentModelFirmwareId that = (EquipmentModelFirmwareId) o;
        return Objects.equals(equipmentModelId, that.equipmentModelId) &&
                Objects.equals(firmwareId, that.firmwareId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(equipmentModelId, firmwareId);
    }
}