package com.incs83.app.entities.id;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Objects;

public class EquipmentModelIspConfigId implements Serializable {

    @Column(name = "equipment_model_id", length = 255)
    private String equipmentModelId;

    @Column(name = "isp_id", length = 255)
    private String ispId;

    public String getEquipmentModelId() {
        return equipmentModelId;
    }

    public void setEquipmentModelId(String equipmentModelId) {
        this.equipmentModelId = equipmentModelId;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    // equals() 和 hashCode() 要正確覆寫
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof EquipmentModelIspConfigId)) return false;
        EquipmentModelIspConfigId that = (EquipmentModelIspConfigId) o;
        return Objects.equals(equipmentModelId, that.equipmentModelId)
                && Objects.equals(ispId, that.ispId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(equipmentModelId, ispId);
    }
}

