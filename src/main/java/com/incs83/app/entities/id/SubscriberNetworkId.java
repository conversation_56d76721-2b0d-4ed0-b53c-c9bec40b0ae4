package com.incs83.app.entities.id;

import java.io.Serializable;
import java.util.Objects;

public class SubscriberNetworkId implements Serializable {
    private String subscriberId;
    private String networkId;

    // Default constructor
    public SubscriberNetworkId() {}

    // Parameterized constructor
    public SubscriberNetworkId(String subscriberId, String networkId) {
        this.subscriberId = subscriberId;
        this.networkId = networkId;
    }

    // Getters and Setters
    public String getSubscriberId() {
        return subscriberId;
    }

    public void setSubscriberId(String subscriberId) {
        this.subscriberId = subscriberId;
    }

    public String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(String networkId) {
        this.networkId = networkId;
    }

    // equals and hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SubscriberNetworkId target = (SubscriberNetworkId) o;
        return Objects.equals(subscriberId, target.subscriberId) &&
                Objects.equals(networkId, target.networkId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(subscriberId, networkId);
    }
}
