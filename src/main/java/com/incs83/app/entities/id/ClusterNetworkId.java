package com.incs83.app.entities.id;

import java.io.Serializable;
import java.util.Objects;

public class ClusterNetworkId implements Serializable {
    private String clusterId;
    private String networkId;

    // Default constructor
    public ClusterNetworkId() {}

    // Parameterized constructor
    public ClusterNetworkId(String clusterId, String networkId) {
        this.clusterId = clusterId;
        this.networkId = networkId;
    }

    // Getters and Setters
    public String getClusterId() {
        return clusterId;
    }

    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    public String getNetworkId() {
        return networkId;
    }

    public void setNetworkId(String networkId) {
        this.networkId = networkId;
    }

    // equals and hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClusterNetworkId target = (ClusterNetworkId) o;
        return Objects.equals(clusterId, target.clusterId) &&
                Objects.equals(networkId, target.networkId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clusterId, networkId);
    }
}
