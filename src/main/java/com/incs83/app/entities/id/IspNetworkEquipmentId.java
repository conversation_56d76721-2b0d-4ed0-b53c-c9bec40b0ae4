package com.incs83.app.entities.id;

import java.io.Serializable;
import java.util.Objects;

public class IspNetworkEquipmentId implements Serializable {
    private String ispId;
    private String equipmentId;

    // Default constructor
    public IspNetworkEquipmentId() {}

    // Parameterized constructor
    public IspNetworkEquipmentId(String ispId, String equipmentId) {
        this.ispId = ispId;
        this.equipmentId = equipmentId;
    }

    // Getters and Setters
    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getEquipmentId() {
        return equipmentId;
    }

    public void setEquipmentId(String equipmentId) {
        this.equipmentId = equipmentId;
    }

    // equals and hashCode
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IspNetworkEquipmentId that = (IspNetworkEquipmentId) o;
        return Objects.equals(ispId, that.ispId) &&
                Objects.equals(equipmentId, that.equipmentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(ispId, equipmentId);
    }
}
