package com.incs83.app.entities;

import com.actiontec.optim.platform.api.v6.enums.BatchEquipmentAction;
import com.actiontec.optim.platform.api.v6.enums.BatchEquipmentStatus;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Entity
@Table(name = "batch_equipment_upload_log")
public class BatchEquipmentUploadLog {
    @Id
    @NotNull
    @Column(name = "id")
    private String id;

    @Column(name = "isp_id", length = 255)
    @NotNull
    private String ispId;

    @Column(name = "file_id", length = 255)
    @NotNull
    private String fileId;

    @Column(name = "is_processed")
    @NotNull
    private boolean isProcessed;

    @Enumerated(EnumType.STRING)
    @NotNull
    @Column(name = "action", length = 20)
    private BatchEquipmentAction action;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", length = 20)
    private BatchEquipmentStatus status;

    @NotNull
    @Column(name = "createdAt")
    private LocalDateTime createdAt;

    @NotNull
    @Column(name = "createdBy")
    private String createdBy;

    @Column(name = "updatedAt")
    private LocalDateTime updatedAt;

    @Column(name = "report_file_id")
    private String reportFileId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public @NotNull String getFileId() {
        return fileId;
    }

    public void setFileId(@NotNull String fileId) {
        this.fileId = fileId;
    }

    public @NotNull boolean isProcessed() {
        return isProcessed;
    }

    public void setProcessed(@NotNull boolean processed) {
        isProcessed = processed;
    }

    public @NotNull BatchEquipmentAction getAction() {
        return action;
    }

    public void setAction(@NotNull BatchEquipmentAction action) {
        this.action = action;
    }

    public BatchEquipmentStatus getStatus() {
        return status;
    }

    public void setStatus(BatchEquipmentStatus status) {
        this.status = status;
    }

    public @NotNull LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(@NotNull LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public @NotNull String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(@NotNull String createdBy) {
        this.createdBy = createdBy;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getReportFileId() {
        return reportFileId;
    }

    public void setReportFileId(String reportFileId) {
        this.reportFileId = reportFileId;
    }
}