package com.incs83.app.entities;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tracking_equipment")
public class TrackingEquipment implements Serializable {
    @Id
    @GeneratedValue(generator="idGenerator")
    @GenericGenerator(name="idGenerator", strategy = "native")
    private String id;

    @Column(name = "ispId")
    private String ispId;

    @Column(name = "serial")
    private String serial;

    @Column(name = "uId")
    private String uId;

    @Column(name = "createdBy")
    private String createdBy;

    @Column(name = "onboardBy")
    private String onboardBy;

    @Column(name = "createdAt")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "onboardAt")
    @Temporal(TemporalType.TIMESTAMP)
    private Date onboardAt;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getSerial() {
        return serial;
    }

    public void setSerial(String serial) {
        this.serial = serial;
    }

    public String getuId() {
        return uId;
    }

    public void setuId(String uId) {
        this.uId = uId;
    }


    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getOnboardBy() {
        return onboardBy;
    }

    public void setOnboardBy(String onboardBy) {
        this.onboardBy = onboardBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getOnboardAt() {
        return onboardAt;
    }

    public void setOnboardAt(Date onboardAt) {
        this.onboardAt = onboardAt;
    }
}
