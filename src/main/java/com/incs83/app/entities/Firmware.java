package com.incs83.app.entities;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "firmware")
public class Firmware implements Serializable {

    @Id
    private String id;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "version")
    private String version;

    @Column(name = "locationType")
    private String locationType;

    @Column(name = "location")
    private String location;

    @Column(name = "username")
    private String username;

    @Column(name = "password")
    private String password;

    @Column(name = "groupId")
    private String groupId;

    @Column(name = "seqVersion")
    private String seqVersion;

    @Column(name = "production")
    private Boolean production;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name="fileId")
    private OptimFile optimFile;

    @OneToMany(mappedBy = "firmware", cascade = CascadeType.ALL, fetch = FetchType.LAZY, orphanRemoval = true)
    private Set<EquipmentModelFirmware> equipmentModelFirmwares;

    @OneToMany(mappedBy = "minVersionFirmware", fetch = FetchType.LAZY)
    private List<EquipmentModelIspConfig> ispConfigs = new ArrayList<>();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getLocationType() {
        return locationType;
    }

    public void setLocationType(String locationType) {
        this.locationType = locationType;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getSeqVersion() {
        return seqVersion;
    }

    public void setSeqVersion(String seqVersion) {
        this.seqVersion = seqVersion;
    }

    public Boolean getProduction() {
        return production;
    }

    public void setProduction(Boolean production) {
        this.production = production;
    }

    public OptimFile getOptimFile() {
        return optimFile;
    }

    public void setOptimFile(OptimFile optimFile) {
        this.optimFile = optimFile;
    }

    public Set<EquipmentModelFirmware> getEquipmentModelFirmwares() {
        return equipmentModelFirmwares;
    }

    public void setEquipmentModelFirmwares(Set<EquipmentModelFirmware> equipmentModelFirmwares) {
        this.equipmentModelFirmwares = equipmentModelFirmwares;
    }

    public List<EquipmentModelIspConfig> ispConfigs() {
        return ispConfigs;
    }

    public void setIspConfigs(List<EquipmentModelIspConfig> ispConfigs) {
        this.ispConfigs = ispConfigs;
    }
}
