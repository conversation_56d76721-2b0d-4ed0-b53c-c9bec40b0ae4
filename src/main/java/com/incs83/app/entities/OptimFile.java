package com.incs83.app.entities;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "file")
public class OptimFile {

    @Id
    private String id;

    @Column(name = "type")
    private String type;

    @Column(name = "fileName")
    private String fileName;

    @Column(name = "fileSize")
    private int fileSize;

    @Column(name = "fileStatus")
    private String fileStatus;

    @Column(name = "s3Bucket")
    private String s3Bucket;

    @Column(name = "filePath")
    private String filePath;

    @Column(name = "secureUrl")
    private String secureUrl;

    @Column(name = "md5")
    private String md5;

    @Column(name = "fileUploadedTime")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fileUploadedTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getFileSize() {
        return fileSize;
    }

    public void setFileSize(int fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileStatus() {
        return fileStatus;
    }

    public void setFileStatus(String fileStatus) {
        this.fileStatus = fileStatus;
    }

    public String getS3Bucket() {
        return s3Bucket;
    }

    public void setS3Bucket(String s3Bucket) {
        this.s3Bucket = s3Bucket;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getSecureUrl() {
        return secureUrl;
    }

    public void setSecureUrl(String secureUrl) {
        this.secureUrl = secureUrl;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public Date getFileUploadedTime() {
        return fileUploadedTime;
    }

    public void setFileUploadedTime(Date fileUploadedTime) {
        this.fileUploadedTime = fileUploadedTime;
    }
}
