package com.incs83.app.entities;

import javax.persistence.*;
import java.util.List;
import java.util.Set;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "equipmentModel")
public class EquipmentModel {

    @Id
    private String id;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name="pictureFileId")
    private OptimFile optimFile;

    @Column(name = "name")
    private String name;

    @Column(name = "type")
    private String type;

    @Column(name = "ispIds")
    private String ispIds;

    @Column(name = "memorySize")
    private long memorySize;

    @Column(name = "flashSize")
    private long flashSize;

    @Column(name = "cpuSpeed")
    private int cpuSpeed;

    @Column(name = "cpuCores")
    private int cpuCores;

    @Column(name = "smmMemorySize")
    private long smmMemorySize;

    @Column(name = "smmFlashSize")
    private long smmFlashSize;

    @Column(name = "smmCpuMhz")
    private int smmCpuMhz;

    @Column(name = "radioKeys")
    private String radioKeys;

    @Column(name = "wanTypes")
    private String wanTypes;

    @Column(name = "lanTypes")
    private String lanTypes;

    @Column(name = "features")
    private String features;

    @OneToMany(mappedBy = "equipmentModel", fetch = FetchType.LAZY)
    private Set<EquipmentModelFirmware> equipmentModelFirmwares;

    @OneToMany(mappedBy = "equipmentModel", fetch = FetchType.LAZY)
    private List<EquipmentModelIspConfig> ispConfigs = new ArrayList<>();


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public OptimFile getOptimFile() {
        return optimFile;
    }

    public void setOptimFile(OptimFile optimFile) {
        this.optimFile = optimFile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getIspIds() {
        return ispIds;
    }

    public void setIspIds(String ispIds) {
        this.ispIds = ispIds;
    }

    public long getMemorySize() {
        return memorySize;
    }

    public void setMemorySize(long memorySize) {
        this.memorySize = memorySize;
    }

    public long getFlashSize() {
        return flashSize;
    }

    public void setFlashSize(long flashSize) {
        this.flashSize = flashSize;
    }

    public int getCpuSpeed() {
        return cpuSpeed;
    }

    public void setCpuSpeed(int cpuSpeed) {
        this.cpuSpeed = cpuSpeed;
    }

    public int getCpuCores() {
        return cpuCores;
    }

    public void setCpuCores(int cpuCores) {
        this.cpuCores = cpuCores;
    }

    public long getSmmMemorySize() {
        return smmMemorySize;
    }

    public void setSmmMemorySize(long smmMemorySize) {
        this.smmMemorySize = smmMemorySize;
    }

    public long getSmmFlashSize() {
        return smmFlashSize;
    }

    public void setSmmFlashSize(long smmFlashSize) {
        this.smmFlashSize = smmFlashSize;
    }

    public int getSmmCpuMhz() {
        return smmCpuMhz;
    }

    public void setSmmCpuMhz(int smmCpuMhz) {
        this.smmCpuMhz = smmCpuMhz;
    }

    public String getRadioKeys() {
        return radioKeys;
    }

    public void setRadioKeys(String radioKeys) {
        this.radioKeys = radioKeys;
    }

    public String getWanTypes() {
        return wanTypes;
    }

    public void setWanTypes(String wanTypes) {
        this.wanTypes = wanTypes;
    }

    public String getLanTypes() {
        return lanTypes;
    }

    public void setLanTypes(String lanTypes) {
        this.lanTypes = lanTypes;
    }

    public String getFeatures() {
        return features;
    }

    public void setFeatures(String features) {
        this.features = features;
    }

    public Set<EquipmentModelFirmware> equipmentModelFirmwares() {
        return equipmentModelFirmwares;
    }

    public void setEquipmentModelFirmwares(Set<EquipmentModelFirmware> equipmentModelFirmwares) {
        this.equipmentModelFirmwares = equipmentModelFirmwares;
    }

    public List<EquipmentModelIspConfig> getIspConfigs() {
        return ispConfigs;
    }

    public void setIspConfigs(List<EquipmentModelIspConfig> ispConfigs) {
        this.ispConfigs = ispConfigs;
    }
}
