package com.incs83.app.entities;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "containerJob")
public class SmmJob implements Serializable {
    @Id
    private String id;

    @Column(name = "ispId")
    private String ispId;

    @Column(name = "groupId")
    private String groupId;

    @Column(name = "action")
    private String action;

    @Column(name = "sourceVersion")
    private String sourceVersion;

    @Column(name = "targetVersion")
    private String targetVersion;

    @Column(name = "count")
    private int count = 0;

    @Column(name = "rate")
    private int rate = 0;

    @Column(name = "state")
    private String state;

    @Column(name = "resourceCheck")
    private boolean resourceCheck = true;

    @ManyToOne
    @JoinColumn(name = "serviceId")
    private SmmService smmService;

    @Column(name = "createdAt")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @Column(name = "updatedAt")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getSourceVersion() {
        return sourceVersion;
    }

    public void setSourceVersion(String sourceVersion) {
        this.sourceVersion = sourceVersion;
    }

    public String getTargetVersion() {
        return targetVersion;
    }

    public void setTargetVersion(String targetVersion) {
        this.targetVersion = targetVersion;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public boolean getResourceCheck() {
        return resourceCheck;
    }

    public void setResourceCheck(boolean resourceCheck) {
        this.resourceCheck = resourceCheck;
    }

    public SmmService getSmmService() {
        return smmService;
    }

    public void setSmmService(SmmService smmService) {
        this.smmService = smmService;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public int getRate() {
        return rate;
    }

    public void setRate(int rate) {
        this.rate = rate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
