package com.incs83.app.entities;

import com.incs83.app.entities.id.EquipmentModelFirmwareId;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "equipment_model_firmware")
@IdClass(EquipmentModelFirmwareId.class)
public class EquipmentModelFirmware implements Serializable {

    @Id
    @Column(name = "equipment_model_id", nullable = false)
    private String equipmentModelId;

    @Id
    @Column(name = "firmware_id", nullable = false)
    private String firmwareId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "firmware_id", referencedColumnName = "id", insertable = false, updatable = false)
    private Firmware firmware;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "equipment_model_id", referencedColumnName = "id", insertable = false, updatable = false)
    private EquipmentModel equipmentModel;

    public String getFirmwareId() {
        return firmwareId;
    }

    public void setFirmwareId(String firmwareId) {
        this.firmwareId = firmwareId;
    }

    public String getEquipmentModelId() {
        return equipmentModelId;
    }

    public void setEquipmentModelId(String equipmentModelId) {
        this.equipmentModelId = equipmentModelId;
    }

    public Firmware getFirmware() {
        return firmware;
    }

    public void setFirmware(Firmware firmware) {
        this.firmware = firmware;
    }

    public EquipmentModel getEquipmentModel() {
        return equipmentModel;
    }

    public void setEquipmentModel(EquipmentModel equipmentModel) {
        this.equipmentModel = equipmentModel;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EquipmentModelFirmware target = (EquipmentModelFirmware) o;
        return Objects.equals(firmwareId, target.firmwareId) &&
                Objects.equals(equipmentModelId, target.equipmentModelId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(firmwareId, equipmentModelId);
    }
}
