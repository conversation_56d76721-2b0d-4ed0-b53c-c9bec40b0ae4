package com.incs83.app.entities;

import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "tracking_transaction")
public class TrackingTransaction implements Serializable {

    @Id
    private String id;

    @Column(name = "action")
    private String action;

    @Column(name = "ispId")
    private String ispId;

    @Column(name = "description")
    private String description;

    @Column(name = "type")
    private String type;

    @Column(name = "status")
    private String status;

    @Column(name = "totalRecord")
    private long totalRecord;

    @Column(name = "noOfRecordUpdated")
    private long noOfRecordUpdated;

    @Column(name = "noOfRecordFailed")
    private long noOfRecordFailed;

    @Column(name = "noOfRecordInvalid")
    private long noOfRecordInvalid;

    @Column(name = "urlForInvalidRecord")
    private String urlForInvalidRecord;

    @Column(name = "createdBy")
    private String createdBy;

    @Column(name = "createdAt")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createdAt;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name="fileId")
    private OptimFile optimFile;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getIspId() {
        return ispId;
    }

    public void setIspId(String ispId) {
        this.ispId = ispId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public long getTotalRecord() {
        return totalRecord;
    }

    public void setTotalRecord(long totalRecord) {
        this.totalRecord = totalRecord;
    }

    public long getNoOfRecordUpdated() {
        return noOfRecordUpdated;
    }

    public void setNoOfRecordUpdated(long noOfRecordUpdated) {
        this.noOfRecordUpdated = noOfRecordUpdated;
    }

    public long getNoOfRecordFailed() {
        return noOfRecordFailed;
    }

    public void setNoOfRecordFailed(long noOfRecordFailed) {
        this.noOfRecordFailed = noOfRecordFailed;
    }

    public long getNoOfRecordInvalid() {
        return noOfRecordInvalid;
    }

    public void setNoOfRecordInvalid(long noOfRecordInvalid) {
        this.noOfRecordInvalid = noOfRecordInvalid;
    }

    public String getUrlForInvalidRecord() {
        return urlForInvalidRecord;
    }

    public void setUrlForInvalidRecord(String urlForInvalidRecord) {
        this.urlForInvalidRecord = urlForInvalidRecord;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public OptimFile getOptimFile() {
        return optimFile;
    }

    public void setOptimFile(OptimFile optimFile) {
        this.optimFile = optimFile;
    }
}
