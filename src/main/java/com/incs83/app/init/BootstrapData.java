package com.incs83.app.init;

import com.incs83.Constants.ESConstants;
import com.incs83.app.business.v2.ManageCommonService;
import com.incs83.app.entities.LoginDetails;
import com.incs83.app.entities.Role;
import com.incs83.app.entities.TokenIssuer;
import com.incs83.app.entities.User;
import com.incs83.business.ESService;
import com.incs83.config.HazelcastClientConfig;
import com.incs83.dao.Page;
import com.incs83.enums.sql.Order;
import com.incs83.mt.DataAccessService;
import com.incs83.pubsub.MQTTService;
import com.incs83.request.PaginatedRequest;
import com.incs83.service.ESServiceImpl;
import com.incs83.services.HazelcastService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.incs83.app.constants.misc.ApplicationConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.ADMIN_USER_ID;
import static com.incs83.app.constants.queries.LoginDetailsSQLImpl.GET_LOGIN_COUNT;
import static com.incs83.app.constants.queries.LoginDetailsSQLImpl.GET_LOGIN_DETAILS;
import static com.incs83.constants.ApplicationCommonConstants.*;
import static com.incs83.constants.ApplicationCommonConstants.TOKEN;

@Component
public class BootstrapData implements InitializingBean {

    @Autowired
    private HazelcastClientConfig hazelcastClientConfig;

    @Autowired
    private ManageCommonService manageCommonService;

    @Autowired
    private Environment environment;

    @Autowired
    private ESService esService;

    @Autowired
    private ESServiceImpl esServiceImpl;

    @Autowired
    private DataAccessService dataAccessService;

    @Autowired
    private MQTTService mqttService;

    @Autowired
    private HazelcastService cacheService;

    ExecutorService executorService;

    private static final Logger LOG = LogManager.getLogger("org");

    @Override
    public void afterPropertiesSet() throws Exception {
        addDaysOfTheWeek();
        hazelcastClientConfig.getHazelcastInstance();
        createElasticSearchIndexes();
        executorService = Executors.newFixedThreadPool(2);
        if (!cacheService.isMapExist(TOKEN)) {
            Runnable userToken = () -> {
                populateUserTokenInCache();
            };
            executorService.submit(userToken);
        }

        if (!cacheService.isMapExist(ROLE)) {
            Runnable role = () -> {
                populateRole();
            };
            executorService.submit(role);
        }

        populateTokenIssuer();
        populateMqttClientConnection();
    }

    private void createElasticSearchIndexes() throws Exception {
        final String accountAdminId = ADMIN_USER_ID;
        esService.createIndex(USER_INDEX, ESConstants.USER_INDEX_SOURCE);
        esService.createIndex(EQUIPMENT_INDEX, ESConstants.EQUIPMENT_INDEX_SOURCE);
        esService.createIndex(SUBSCRIBER_INDEX, ESConstants.SUBSCRIBER_INDEX_SOURCE);
        Map<String, Object> userData = esService.getById(USER_INDEX, accountAdminId, null);
        if (Objects.isNull(userData)) {
            User user = (User) dataAccessService.read(User.class, accountAdminId);
            userData = esServiceImpl.prepareDataForES(user, false);
            esService.insert(USER_INDEX, userData, String.valueOf(userData.get("id")), null);

        }
    }

    private void addDaysOfTheWeek() {
        ALLOWED_DAYS_OF_WEEK.add("SUN");
        ALLOWED_DAYS_OF_WEEK.add("MON");
        ALLOWED_DAYS_OF_WEEK.add("TUE");
        ALLOWED_DAYS_OF_WEEK.add("WED");
        ALLOWED_DAYS_OF_WEEK.add("THU");
        ALLOWED_DAYS_OF_WEEK.add("FRI");
        ALLOWED_DAYS_OF_WEEK.add("SAT");
    }

    public void populateMqttClientConnection() {
        List<String> vmqUrlList = mqttService.getVmqUrls();
        if (!vmqUrlList.isEmpty()) {
            ExecutorService executorService = Executors.newFixedThreadPool(vmqUrlList.size());
            String mqttPort = VMQ_PORT;
            for (String url : vmqUrlList) {
                Runnable mqttConnectionAsyncCall = () -> {
                    String mqttUrl = "tcp://" + url;
                    try {
                        MqttClient mqttClient = manageCommonService.getMqttClientConnection(mqttUrl, mqttPort);
                        if (Objects.nonNull(mqttClient)) {
                            MqttConnectOptions connectOptions = new MqttConnectOptions();
                            connectOptions.setMaxInflight(1000);
                            connectOptions.setKeepAliveInterval(1000);
                            connectOptions.setAutomaticReconnect(true);
                            if (Boolean.valueOf(environment.getProperty(VMQ_AUTH_ENABLED))) {
                                connectOptions.setUserName(environment.getProperty(VMQ_USER));
                                connectOptions.setPassword(environment.getProperty(VMQ_PASSWORD).toCharArray());
                            }
                            mqttClient.connect(connectOptions);
                            mqttConnectionList.add(mqttClient);
                            LOG.info("Client is Connected, putting in an in Cache :: " + url + "=> " + mqttUrl);
                        }
                    } catch (Exception e) {
                        LOG.error("Exception  :: MqttClient connection cannot be Obtain for url :: " + mqttUrl + " & port :: " + mqttPort + " exception message===>" + e.getMessage());
                    }
                };
                executorService.submit(mqttConnectionAsyncCall);
            }
        }
    }

    private void populateTokenIssuer() {
        try {
            List<TokenIssuer> tokenIssuers = (List<TokenIssuer>) dataAccessService.read(TokenIssuer.class);
            cacheService.create(ISSUER_TEXT, tokenIssuers.get(0), TOKEN_ISSUERS);
        } catch (Exception e) {
            LOG.error("Exception :: While fetching Token Issuer");
        }
    }

    private void populateUserTokenInCache() {
        try {
            int offset = 0;
            int max = 5000;
            Long totalCount = Long.valueOf(dataAccessService.read(LoginDetails.class, GET_LOGIN_COUNT, new HashMap<>()).iterator().next().toString());
            long pages = totalCount / max;
            pages = ((totalCount % max) != 0) ? pages + 1 : pages;

            PaginatedRequest paginatedRequest = new PaginatedRequest();

            for (int i = 0; i < pages; i++) {
                paginatedRequest.setOffset(offset);
                paginatedRequest.setMax(max);
                paginatedRequest.setSortBy("createdAt");
                paginatedRequest.setOrder(Order.ASC);
                Page page = dataAccessService.read(LoginDetails.class, new HashMap<>(), paginatedRequest, GET_LOGIN_DETAILS, GET_LOGIN_COUNT);
                List<LoginDetails> loginDetailsList = page.getObjects();
                if (Objects.nonNull(loginDetailsList) && !loginDetailsList.isEmpty()) {
                    for (LoginDetails loginDetails : loginDetailsList) {
                        HashMap<String, Object> userTokenMap = new HashMap<>();
                        userTokenMap.put("token", loginDetails.getToken());
                        userTokenMap.put("createdAt", loginDetails.getTokenCreatedAt());
                        userTokenMap.put("updatedAt", loginDetails.getTokenUpdatedAt());
                        cacheService.create(loginDetails.getId(), userTokenMap, TOKEN);
                    }
                }

                offset += max;
            }
        } catch (Exception e) {
            LOG.error("Unable to populate LoggedIn User Token on cache");
        }
    }

    private void populateRole() {
        try {
            List<Role> roleList = (List<Role>) dataAccessService.read(Role.class);
            if (!roleList.isEmpty())
                cacheService.create(SYSTEM_ADMIN, roleList, ROLE);
        } catch (Exception e) {
            LOG.error("Error While fetching Role from DB");
        }
    }
}
