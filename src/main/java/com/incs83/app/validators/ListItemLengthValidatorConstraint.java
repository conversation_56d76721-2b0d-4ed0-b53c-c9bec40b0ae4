package com.incs83.app.validators;


import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = ListItemLengthValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ListItemLengthValidatorConstraint {

    String message() default "Length must be from 1 to 23 and must not contain & * = | ; '' < >";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
