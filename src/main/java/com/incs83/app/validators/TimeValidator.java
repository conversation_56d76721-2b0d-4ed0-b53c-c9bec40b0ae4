package com.incs83.app.validators;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Calendar;

public class TimeValidator implements ConstraintValidator<TimeValidatorConstraint, Long> {
    @Override
    public void initialize(TimeValidatorConstraint timeValidator) {
    }

    @Override
    public boolean isValid(Long time, ConstraintValidatorContext context) {
        if (Calendar.getInstance().getTimeInMillis() > time) {
            return false;
        }
        return true;
    }
}
