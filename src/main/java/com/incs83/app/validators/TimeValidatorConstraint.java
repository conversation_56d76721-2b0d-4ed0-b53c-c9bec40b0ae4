package com.incs83.app.validators;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = TimeValidator.class)
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface TimeValidatorConstraint {
    String message() default "Invalid time, cannot be less than Current Time";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
