package com.incs83.app.validators;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

public class ListItemLengthValidator implements ConstraintValidator<ListItemLengthValidatorConstraint, List<String>> {

    @Override
    public void initialize(ListItemLengthValidatorConstraint listItemLengthValidatorConstraint) {

    }

    @Override
    public boolean isValid(List<String> list, ConstraintValidatorContext context) {
        for (int i = 0; i < list.size(); i++) {
            if (list.get(i).length() < 1 || list.get(i).length() > 23) {
                return false;
            } else if (list.get(i).contains("&") || list.get(i).contains("*") || list.get(i).contains("=") || list.get(i).contains("|") || list.get(i).contains(";") || list.get(i).contains("<") || list.get(i).contains(">")) {
                return false;
            }
        }
        return true;
    }
}
