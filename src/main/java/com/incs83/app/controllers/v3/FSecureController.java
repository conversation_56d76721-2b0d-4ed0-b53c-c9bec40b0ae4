package com.incs83.app.controllers.v3;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.business.v3.ManageFSecureService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.fSecure.*;
import com.incs83.app.responsedto.v3.*;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RestController("v3.SecurityController")
@Api(value = "(V3) Security", description = "API's for security operations", tags = {"Optim - (V3) Security"})
@RequestMapping(value = "/actiontec/api/v3/security/equipments")
public class FSecureController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageFSecureService manageFSecureService;

    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;

    @ApiOperation(value = "Get network protection policies", response = ProtectionResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ProtectionResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/protection", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_PROTECTION_POLICIES, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.PROTECTION)
//    @Timed(value =  AuditorConstants.GET_PROTECTION_POLICIES )
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> getProtectionSettings(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getCPESecurityProtection(equipmentIdOrSerialOrSTN, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

    }

    @ApiOperation(value = "Edit network protection policies", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/protection", method = RequestMethod.PUT)
//    @Timed(value =  AuditorConstants.PROTECTION_UPDATE )
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
//    @Auditable(operation = AuditorConstants.PROTECTION_UPDATE, method = RequestMethod.PUT)
//    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.PROTECTION)
    public CompletableFuture<ResponseDTO<?>> updateProtectionSettings(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "The payload of protection rules", required = true) @RequestBody ProtectionRequest protectionRequest,
            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageFSecureService.updateCPESecurityProtection(equipmentIdOrSerialOrSTN, protectionRequest,httpServletRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.updateCPESecurityProtection(equipmentIdOrSerialOrSTN, protectionRequest, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

    }

    @ApiOperation(value = "Get the data of website exception(s)", response = WebsiteExceptionResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = WebsiteExceptionResponse.class, message = "Request Completed Successfully")
    })

    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/websiteException", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_WEBSITE_EXCEPTIONS, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.WEBSITE_EXCEPTION)
//    @Timed(value = AuditorConstants.GET_WEBSITE_EXCEPTIONS)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> getWebsiteException(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageFSecureService.getCPESecurityWebSiteException(equipmentIdOrSerialOrSTN,httpServletRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getCPESecurityWebSiteException(equipmentIdOrSerialOrSTN, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

    }

    @ApiOperation(value = "Edit website exception rule", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/websiteException", method = RequestMethod.PUT)
//    @Timed(value = AuditorConstants.WEBSITE_EXCEPTION_UPDATE)
//    @Auditable(operation = AuditorConstants.WEBSITE_EXCEPTION_UPDATE, method = RequestMethod.PUT)
//    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.WEBSITE_EXCEPTION)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> updateWebsiteException(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "The payload of website exception rules", required = true) @RequestBody List<WebsiteExceptionRequest> websiteExceptionRequest,
            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.updateCPESecurityWebSiteException(equipmentIdOrSerialOrSTN, websiteExceptionRequest,httpServletRequest);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));


        return manageLongWaitAPI.call(
                () -> {
                    manageFSecureService.updateCPESecurityWebSiteException(equipmentIdOrSerialOrSTN, websiteExceptionRequest, httpServletRequest);
                    return responseUtil.ok(ApiResponseCode.SUCCESS,
                            CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
                }
        );
    }

    @ApiOperation(value = "Delete website exception rule", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/websiteException", method = RequestMethod.DELETE)
//    @Auditable(operation = AuditorConstants.WEBSITE_EXCEPTION_DELETE, method = RequestMethod.DELETE)
//    @Cachable(method = RequestMethod.DELETE, operation = FsecureConstants.WEBSITE_EXCEPTION)
//    @Timed(value = AuditorConstants.WEBSITE_EXCEPTION_DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> deleteWebsiteException(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.deleteCPESecurityWebSiteException(equipmentIdOrSerialOrSTN);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(
                () -> {
                    manageFSecureService.deleteCPESecurityWebSiteException(equipmentIdOrSerialOrSTN, httpServletRequest);
                    return responseUtil.ok(ApiResponseCode.SUCCESS,
                            CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
                }
        );
    }

    @ApiOperation(value = "Get All device(s)", response = DeviceResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = DeviceResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_ALL_DEVICE, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.DEVICES)
//    @Timed(value = AuditorConstants.GET_ALL_DEVICE )
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> getAllDeviceData(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageFSecureService.getCPESecurityDevices(equipmentIdOrSerialOrSTN, null), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getAllCPESecurityDevices(equipmentIdOrSerialOrSTN, null, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
    }


    @ApiOperation(value = "Get the data of device(s)", response = DeviceResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = DeviceResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{deviceId}", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_DEVICE_BY_ID, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.DEVICE_BY_ID)
//    @Timed(value = AuditorConstants.GET_DEVICE_BY_ID )
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> getDeviceData(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Device ID", required = true) @PathVariable(name = "deviceId") String deviceId,
            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageFSecureService.getCPESecurityDevices(equipmentIdOrSerialOrSTN, deviceId,httpServletRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getCPESecurityDeviceById(equipmentIdOrSerialOrSTN, deviceId, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

    }


    @ApiOperation(value = "Edit device data", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{deviceId}", method = RequestMethod.PUT)
//    @Auditable(operation = AuditorConstants.DEVICE_UPDATE, method = RequestMethod.PUT)
//    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.DEVICE_BY_ID)
//    @Timed(value = AuditorConstants.DEVICE_UPDATE )
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> updateWebsiteException(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Device ID", required = true) @PathVariable(name = "deviceId") String deviceId,
            @ApiParam(value = "The payload of Device data", required = true) @RequestBody DeviceDataRequest deviceDataRequest,
            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.updateCPESecurityDevices(equipmentIdOrSerialOrSTN, deviceId, deviceDataRequest);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(
                () -> {
                    manageFSecureService.updateCPESecurityDevices(equipmentIdOrSerialOrSTN, deviceId, deviceDataRequest, httpServletRequest);
                    return responseUtil.ok(ApiResponseCode.SUCCESS,
                            CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
                }
        );
    }


    @ApiOperation(value = "Trigger an action to this device\n", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{deviceId}/action", method = RequestMethod.POST)
//    @Timed(value = AuditorConstants.DEVICE_ACTION_CREATE)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> triggerActionForDevice(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Device ID", required = true) @PathVariable(name = "deviceId") String deviceId,
            @RequestBody DeviceActionRequest actionRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.triggerActionForDevice(equipmentIdOrSerialOrSTN, deviceId, actionRequest, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
    }

    @ApiOperation(value = "Get the data of profile(s)", response = ProfileResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ProfileResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/profiles/{profileId}", method = RequestMethod.GET)
//    @Timed(value = AuditorConstants.GET_PROFILE_BY_ID)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
//    @Auditable(operation = AuditorConstants.GET_PROFILE_BY_ID, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.PROFILE_BY_ID)
    public CompletableFuture<ResponseDTO<?>> getProfileRule(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Profile Id" , required = true) @PathVariable(name = "profileId") String profileId,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getFSecureProfileByID(equipmentIdOrSerialOrSTN, profileId, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

//              return responseUtil.ok(manageFSecureService.getFSecureProfileByName(equipmentIdOrSerialOrSTN, profileId), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get the data of profile(s)", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/profiles/categories", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getAllProfileCategories(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageFSecureService.getAllProfileCategories(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Edit profile rule", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/profiles/{profileId}", method = RequestMethod.PUT)
//    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.PROFILE_BY_ID)
//    @Auditable(operation = AuditorConstants.PROFILE_UPDATE, method = RequestMethod.PUT)
//    @Timed(value = AuditorConstants.PROFILE_UPDATE)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> updateProfileRule(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Profile Id", required = true) @PathVariable(name = "profileId") String profileId,
            @ApiParam(value = "The payload of profile rules", required = true) @RequestBody ProfileRuleRequest profileRuleRequest,
            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.updateFSecureProfile(equipmentIdOrSerialOrSTN, profileId, profileRuleRequest);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(
                () -> {
                    manageFSecureService.updateFSecureProfile(equipmentIdOrSerialOrSTN, profileId, profileRuleRequest, httpServletRequest);
                    return responseUtil.ok(ApiResponseCode.SUCCESS,
                            CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
                }
        );


    }

    @ApiOperation(value = "Delete a profile rule")
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/profiles/{profileId}", method = RequestMethod.DELETE)
//    @Auditable(operation = AuditorConstants.PROFILE_DELETE, method = RequestMethod.DELETE)
//    @Cachable(method = RequestMethod.DELETE, operation = FsecureConstants.PROFILE_BY_ID)
//    @Timed(value = AuditorConstants.PROFILE_DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> deleteProfileRule(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Profile Id", required = true) @PathVariable(name = "profileId") String profileId,
            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.deleteFSecureProfile(equipmentIdOrSerialOrSTN, profileId);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(
                () -> {
                    manageFSecureService.deleteFSecureProfile(equipmentIdOrSerialOrSTN, profileId, httpServletRequest);
                    return responseUtil.ok(ApiResponseCode.SUCCESS,
                            CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
                }
        );

    }

    @ApiOperation(value = "Add a new profile", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/profiles", method = RequestMethod.POST)
//    @Auditable(operation = AuditorConstants.PROFILE_CREATE, method = RequestMethod.POST)
//    @Cachable(method = RequestMethod.POST, operation = FsecureConstants.PROFILES)
//    @Timed(value = AuditorConstants.PROFILE_CREATE)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> createProfile(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "The payload of profile rules", required = true) @RequestBody ProfileRuleRequest profileRuleRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(
                () -> {
                    manageFSecureService.createFSecureProfile(equipmentIdOrSerialOrSTN, profileRuleRequest, httpServletRequest);
                    return responseUtil.ok(ApiResponseCode.SUCCESS,
                            CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
                }
        );
    }

    @ApiOperation(value = "Set registration key", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/registration", method = RequestMethod.PUT)
//    @Auditable(operation = AuditorConstants.REGISTRATION_UPDATE, method = RequestMethod.PUT)
//    @Cachable(method = RequestMethod.PUT, operation = FsecureConstants.REGISTRATION)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
//    @Timed(value = AuditorConstants.PROFILE_ACTION_CREATE )
    public CompletableFuture<ResponseDTO<?>> setRegistrationKey(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "The payload of parental control settings", required = true) @RequestBody @Valid RegistrationRequest registrationRequest,
            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.setRegistrationKey(equipmentIdOrSerialOrSTN, registrationRequest);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(
                () -> {
                    manageFSecureService.setRegistrationKey(equipmentIdOrSerialOrSTN, registrationRequest, httpServletRequest);
                    return responseUtil.ok(ApiResponseCode.SUCCESS,
                            CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
                }
        );


    }

    @ApiOperation(value = "Trigger an action to this profile", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/profiles/{profileId}/action", method = RequestMethod.POST)
//    @Auditable(operation = AuditorConstants.PROFILE_ACTION_CREATE, method = RequestMethod.POST)
//    @Timed(value = AuditorConstants.PROFILE_ACTION_CREATE )
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> triggerActionForProfile(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "The payload of profile rules", required = true) @RequestBody ProfileActionRequest actionRequest,
            @ApiParam(value = "Profile Id", required = true) @PathVariable(name = "profileId") String profileId,
            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.triggerActionForProfile(equipmentIdOrSerialOrSTN, profileId, actionRequest,httpServletRequest);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));


        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.triggerActionForProfile(equipmentIdOrSerialOrSTN, profileId, actionRequest, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));


    }

    @ApiOperation(value = "Get All profile List", response = ProfileResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ProfileResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/profiles", method = RequestMethod.GET)
//    @Timed(value = AuditorConstants.GET_ALL_PROFILES )
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
//    @Auditable(operation = AuditorConstants.GET_ALL_PROFILES, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.PROFILES)
    public CompletableFuture<ResponseDTO<?>> getAllProfilesForSubscriber(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getAllFSecureProfile(equipmentIdOrSerialOrSTN, null, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

    }
//
//    @ApiOperation(value = "Set registration key", response = ApiResponseDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 401, message = "You are Not Authenticated"),
//            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//            @ApiResponse(code = 405, message = "Method Not Allowed"),
//            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
//            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
//            @ApiResponse(code = 501, message = "Service is Not Implemented"),
//            @ApiResponse(code = 503, message = "Service is Unavailable"),
//            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
//    })
//    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/registration", method = RequestMethod.PUT)
//    @Auditable(operation = AuditorConstants.REGISTRATION_UPDATE,method = RequestMethod.PUT)
////    @Timed(value = AuditorConstants.REGISTRATION_UPDATE)
//    @Cachable(method = RequestMethod.PUT , operation = FsecureConstants.REGISTRATION)
//    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
//    public ResponseDTO<?> setRegistrationKey(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
//            @ApiParam(value = "The payload of parental control settings", required = true) @RequestBody @Valid RegistrationRequest registrationRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        manageFSecureService.setRegistrationKey(equipmentIdOrSerialOrSTN, registrationRequest);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

    @ApiOperation(value = "Get registration key", response = RegistrationResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = RegistrationResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/registration", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_REGISTRATION_KEY, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.REGISTRATION)
//    @Timed(value = AuditorConstants.GET_REGISTRATION_KEY)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> getRegistrationKey(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageFSecureService.getRegistrationKey(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getRegistrationKey(equipmentIdOrSerialOrSTN, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
    }

    @ApiOperation(value = "Deactivate this feature", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/registration", method = RequestMethod.DELETE)
//    @Auditable(operation = AuditorConstants.REGISTRATION_DELETE, method = RequestMethod.DELETE)
//    @Timed(value = AuditorConstants.REGISTRATION_DELETE)
    @Auditable(operation = AuditorConstants.REGISTRATION_DELETE, method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> deleteRegistrationKey(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {

//        return manageLongWaitAPI.call(() -> {
//            manageFSecureService.deleteRegistrationKey(equipmentIdOrSerialOrSTN);
//            return responseUtil.ok(ApiResponseCode.SUCCESS,
//                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//        }

        return manageLongWaitAPI.call(() -> {
            manageFSecureService.deleteRegistrationKey(equipmentIdOrSerialOrSTN);
            return responseUtil.ok(ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });


    }

    @ApiOperation(value = "Get event log(s)", response = EventLogResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = EventLogResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/events", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_ALL_EVENTS, method = RequestMethod.GET, persist = false)
//    @Cachable(method = RequestMethod.GET, operation = FsecureConstants.EVENTS)
//    @Timed(value = AuditorConstants.GET_ALL_EVENTS)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> getEventLogs(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @RequestParam(name = "limit", required = false) String limit,
            @RequestParam(name = "after", required = false) String after,
            HttpServletRequest httpServletRequest) throws Exception {

        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getCPESecurityEvents(equipmentIdOrSerialOrSTN, limit, after, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

//        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getRegistrationKey(equipmentIdOrSerialOrSTN, httpServletRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

    }

    @ApiOperation(value = "Delete event log(s)")
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/events", method = RequestMethod.DELETE)
//    @Timed(value = AuditorConstants.EVENTS_DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Common.class)
//    @Auditable(operation = AuditorConstants.EVENTS_DELETE, method = RequestMethod.DELETE)
//    @Cachable(method = RequestMethod.DELETE, operation = FsecureConstants.EVENTS)
    public CompletableFuture<ResponseDTO<?>> deleteEventId(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> {
            manageFSecureService.deleteCPESecurityEvents(equipmentIdOrSerialOrSTN, httpServletRequest);
            return responseUtil.ok(ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });
    }

    @ApiOperation(value = "Get event counters by categories", response = EventCategoryResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = EventCategoryResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/events/categoryCounters", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_EVENTS_COUNTERS, method = RequestMethod.GET, persist = false)
//    @Timed(value = AuditorConstants.GET_EVENTS_COUNTERS)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public CompletableFuture<ResponseDTO<?>> getEventCategoryCounters(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageFSecureService.getCPESecurityCategoryCounters(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

        return manageLongWaitAPI.call(() -> responseUtil.ok(manageFSecureService.getCPESecurityCategoryCounters(equipmentIdOrSerialOrSTN, httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

    }

    //feature is not required on .11-GA
    /*@ApiOperation(value = "Edit the data of a radio", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{serialNumberOrSubscriberIdOrRGWMAC}/radios", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
    public ResponseDTO<?> editRadioSettings(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "serialNumberOrSubscriberIdOrRGWMAC") String subscriberId,
            @ApiParam(value = "The payload of timezone settings", required = true) @RequestBody @Valid RadiosSettingRequest radiosSettingRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        manageFSecureService.modifyRadioSettings(subscriberId, radiosSettingRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get the data of all or one radio", response = EventLogResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = EventLogResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{serialNumberOrSubscriberIdOrRGWMAC}/radios", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getWifiResources(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "serialNumberOrSubscriberIdOrRGWMAC") String subscriberId,
            @ApiParam(value = "Unique radio ID. One of the \"2.4G\", \"5G\", \"5GLo\", \"5GHi\".")
            @RequestParam(required = false, name = "radioId") String radioId,
            @ApiParam(value = "A list of props, to filter the props you need. e.g. /radio/2.4G?filter=id,status,powerSave")
            @RequestParam(required = false, name = "filter") String filter,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageFSecureService.getWifiResources(subscriberId, radioId, filter), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/
}
