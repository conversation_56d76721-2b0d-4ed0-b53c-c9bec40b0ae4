package com.incs83.app.controllers.v3;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.business.v3.ManageFSecureProvisioning;
import com.incs83.app.common.v3.FsecureProvisionPaginatedRequestDTO;
import com.incs83.app.common.v3.FsecureProvisionRequestDTO;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v3.FsecureProvisioning.FsecureProvisionResponsePaginatedDTO;
import com.incs83.app.responsedto.v3.FsecureProvisioning.FsecureStatusResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController("v3.FsecureProvisioningController")
@Api(value = "(v3) Fsecure Provisioning", description = "API's for Fsecure Provisioning Operation", tags = {"Optim - (V3) Fsecure Provisioning "})
@RequestMapping(value = "/actiontec/api/v3/security/provisioning")
public class FsecureProvisioningController {

    @Autowired
    private ManageFSecureProvisioning manageFSecureProvisioning;

    @Autowired
    private ResponseUtil responseUtil;

    @ApiOperation(value = "Get Fsecure Provisioning Status", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = FsecureStatusResponseDTO.class, message = "Request Completed Successfully")
    })
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
//    @Auditable(method =  RequestMethod.GET , operation = AuditorConstants.GET_FSECURE_STATUS)
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}", method = RequestMethod.GET)
    public ResponseDTO<?> getFsecureStatus(
                                @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable String equipmentIdOrSerialOrSTN,
                                @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
                                           HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageFSecureProvisioning.getFsecureStatus(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Delete Fsecure Provisioning Status", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Common.class)
    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SET_FSECURE_KEY)
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}", method = RequestMethod.DELETE)
    public ResponseDTO<?> removeFsecure(
                                        @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)@PathVariable String equipmentIdOrSerialOrSTN,
                                        @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
                                        HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageFSecureProvisioning.removeFsecure(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }

    @ApiOperation(value = "Set Fsecure Provisioning Key", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SET_FSECURE_KEY)
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}", method = RequestMethod.POST)
    public ResponseDTO<?> setFsecureKey(
                                        @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)@PathVariable String equipmentIdOrSerialOrSTN,
                                        @ApiParam(value = "The payload of Fsecure Provisioning", required = true)@RequestBody FsecureProvisionRequestDTO fsecureProvisionRequestDTO,
                                        @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
                                        HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageFSecureProvisioning.setFsecureKey(equipmentIdOrSerialOrSTN, fsecureProvisionRequestDTO), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }


    @ApiOperation(value = "Get Fsecure Paginated provisioning DTOS", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = FsecureProvisionResponsePaginatedDTO.class, message = "Request Completed Successfully")
    })
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    @RequestMapping(value = "/paginated", method = RequestMethod.POST)
    public ResponseDTO<?> getFsecurePaginatedProvisions(
                                                        @ApiParam(value = "The payload of Fsecure Provisioning", required = true)@RequestBody FsecureProvisionPaginatedRequestDTO fsecureProvisionPaginatedRequestDTO,
                                                        @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
                                                        HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageFSecureProvisioning.getFsecurePaginatedProvisions(fsecureProvisionPaginatedRequestDTO), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }


}
