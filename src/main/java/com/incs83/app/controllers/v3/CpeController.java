package com.incs83.app.controllers.v3;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.business.v3.AutoWifiConfigRestoreSettingService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.responsedto.v3.cpe.CpeProvisioning;
import com.incs83.app.responsedto.v3.cpe.CpeProvisioningResp;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController("v3.CpeController")
@RequestMapping(value = "/actiontec/api/v3/cpe")
@Api(value = "(V3) CPE", description = "API's for CPE Operations",tags = {"Optim - (V3) CPE"})
public class CpeController {
    private static final String FILED__configRestore = "configRestore";
    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private AutoWifiConfigRestoreSettingService autoWifiConfigRestoreSettingService;

    @RequestMapping(value = "/provisioning/{equipmentIdOrSerialOrSTN}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    @ApiOperation(value = "Get CPE provisioning config", response = CpeProvisioningResp.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    public ResponseDTO<?> getProvisioning(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)
            @PathVariable(name = "equipmentIdOrSerialOrSTN")
                    String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, Object> resultMap = convertToHttpResp(autoWifiConfigRestoreSettingService.findByVagueId(equipmentIdOrSerialOrSTN));
        return responseUtil.ok(resultMap,
                ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/provisioning/{equipmentIdOrSerialOrSTN}", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = Common.class)
    @ApiOperation(value = "Edit CPE provisioning config", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.AUTO_WIFI_CONFIG_RESTORE_SETTINGS)
    public ResponseDTO<?> patchProvisioning(
            @PathVariable(name = "equipmentIdOrSerialOrSTN")
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)
                    String equipmentIdOrSerialOrSTN,
            @RequestBody
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)
                    CpeProvisioning cpeProvisioning,
            @RequestHeader(name = "X-Authorization")
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                    String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        Map<String, Object> inputMap = cpeProvisioning.toMap();
        autoWifiConfigRestoreSettingService.updateByVagueId(equipmentIdOrSerialOrSTN, inputMap);
        return responseUtil.ok(ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    private Map<String, Object> convertToHttpResp(Map<String, Object> map) {
        HashMap<String, Object> resultMap = new HashMap<>();
        if(map.containsKey(AutoWifiConfigRestoreSettingService.FIELD__setting_autoConfigRestoreEnabled)){
            resultMap.put(FILED__configRestore, map.get(AutoWifiConfigRestoreSettingService.FIELD__setting_autoConfigRestoreEnabled));
        } else {
            resultMap.put(FILED__configRestore, false);
        }
        return resultMap;
    }

    private Map<String, Object> convertFromHttpReq(Map<String, Object> map) {
        HashMap<String, Object> resultMap = new HashMap<>();
        if(map.containsKey(FILED__configRestore)){
            resultMap.put(AutoWifiConfigRestoreSettingService.FIELD__setting_autoConfigRestoreEnabled, map.get(FILED__configRestore));
        } else {
            resultMap.put(AutoWifiConfigRestoreSettingService.FIELD__setting_autoConfigRestoreEnabled, true);
        }
        return resultMap;
    }
}
