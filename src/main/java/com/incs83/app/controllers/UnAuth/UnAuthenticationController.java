package com.incs83.app.controllers.UnAuth;

import com.actiontec.optim.platform.adapter.At3Adapter;
import com.actiontec.optim.platform.api.v5.mapper.OauthConfigResponseMapper;
import com.actiontec.optim.platform.api.v5.model.OauthConfigIspDetail;
import com.actiontec.optim.platform.api.v5.model.OauthConfigRoleDetail;
import com.actiontec.optim.platform.api.v5.model.OauthResponse;
import com.actiontec.optim.platform.model.OauthConfig;
import com.actiontec.optim.platform.service.OauthConfigService;
import com.actiontec.optim.platform.service.OauthService;
import com.actiontec.optim.platform.exception.UnAuthException;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.business.v2.IAMServices;
import com.incs83.app.business.v2.ManageLoginService;
import com.incs83.app.business.v2.Oauth2Service;
import com.incs83.app.business.v4.ManageElasticLoadService;
import com.incs83.app.common.v2.LoginRequest;
import com.incs83.app.common.v2.PasswordChangeRequest;
import com.incs83.app.common.v2.ResetPasswordRequest;
import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.LoginResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import com.incs83.util.ValidationUtils;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.WebAsyncTask;
import org.springframework.core.env.Environment;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.regex.Pattern;

@RestController
@Api(value = "(V2) Mobile Authentication", description = "API's for Mobile Authentication", tags = {"Optim - (V2) Mobile Authentication"})
@RequestMapping(value = "/actiontec/api/unauth/platform")
public class UnAuthenticationController {
    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private IAMServices IAMServices;

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ValidationUtils validationUtils;

    @Autowired
    private Environment environment;

    @Autowired
    private ManageLoginService manageLoginService;

    @Autowired
    private Oauth2Service oauth2Service;

    @Autowired
    private OauthService oauthService;

    @Autowired
    private ManageElasticLoadService mangeElasticLoadService;

    @Autowired
    private OauthConfigService oauthConfigService;

    @Autowired
    private OauthConfigResponseMapper oauthConfigResponseMapper;

    @Autowired
    private At3Adapter at3Adapter;

    @Value("${useNewOauthConfig:true}")
    private Boolean useNewOauthConfig;

//    @Timed( value = "api resetPassword requests" )
    @ApiIgnore
    @RequestMapping(value = "/users/credentials/reset", method = RequestMethod.POST)
    public ResponseDTO<?> changePassword(
            HttpServletRequest httpServletRequest,
            @RequestBody PasswordChangeRequest passwordChangeRequest)
            throws Exception {
//        validationUtils.hasErrors(errors, httpServletRequest);

        if(!Pattern.matches("((?=.*\\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%?=*&]).{10,64})",String.valueOf(passwordChangeRequest.getNewPassword())))
            throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "The password must be between 10 and 64 characters in length, and it must include at least one uppercase letter, one lowercase letter, one number, and one special character.");

        if (passwordChangeRequest.getNewPassword().length() < 10 || passwordChangeRequest.getNewPassword().length() > 64)
            throw new ValidationException(org.springframework.http.HttpStatus.BAD_REQUEST.value(), "The password length must be between 10 and 64 characters");

        IAMServices.resetPassword(passwordChangeRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


//    @Timed( value = "api.forgetPassword.requests" )
    @ApiIgnore
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.RESET_PASSWORD)
    @RequestMapping(value = "/users/resetPassword", method = RequestMethod.POST)
    public ResponseDTO<?> sendEmailToResetPassword(
            HttpServletRequest httpServletRequest,
            @Valid @RequestBody ResetPasswordRequest resetPasswordRequest)
            throws Exception {
        IAMServices.sendEmailToResetPassword(resetPasswordRequest, httpServletRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @RequestMapping(value = "/verify/token", method = RequestMethod.GET)
    public ResponseDTO<?> isTokenValid(
            HttpServletRequest httpServletRequest,
            @RequestParam String id, @RequestParam String token)
            throws Exception {
        IAMServices.isTokenValid(id, token);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    /*@ApiIgnore
    @RequestMapping(value = "/voice/assistant", method = RequestMethod.POST)
    public HashMap processVoiceAssistantCommand(
            @RequestParam String type,
            @RequestBody HashMap requestParams,
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        HashMap resp = assistantService.processAssistantVoiceCommand(type, requestParams, accessToken);
        return resp;
    }*/


    @ApiIgnore
    @RequestMapping(value = "/checkTokenValidity", method = RequestMethod.POST, produces = "application/json")
    public ResponseDTO<?> isAuthenticated(@RequestBody HashMap<String, Object> accessTokenMap, HttpServletRequest httpServletRequest) {
        return responseUtil.ok(manageLoginService.isAuthenticated(accessTokenMap), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @RequestMapping(value = "/checkCookieTokenValidity", method = RequestMethod.GET, produces = "application/json")
    public ResponseDTO<?> checkCookieTokenValidity( HttpServletRequest httpServletRequest) {
        String cookieValue =  "";
        String cookieName = environment.getProperty(ApplicationConstants.ENV_NAME) + "-globalAccessToken";
        Cookie[] cookies = httpServletRequest.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(cookieName)) {
                    cookieValue = cookie.getValue();
                    
                }
            }
        }

        if (cookieValue.equals("")) {
            LOG.error("token", cookieValue);
            throw new UnAuthException();
        }

        boolean checkResult = manageLoginService.checkAccessTokenValidity(cookieValue);
        if (checkResult) {
            return responseUtil.ok(checkResult, ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        } else {
            LOG.error("token", cookieValue);
            throw new UnAuthException();
        }
    }


    @ApiIgnore
    @RequestMapping(value = "/loadUser", method = RequestMethod.GET, produces = "application/json")
    public ResponseDTO<?> loadDataToES(
            @RequestHeader(value = "apiKey") String encoding,
            @RequestHeader(value = "isSecure") boolean isSecure,
            HttpServletRequest httpServletRequest) throws Exception {

        mangeElasticLoadService.loadUserDataToES(encoding, isSecure);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @RequestMapping(value = "/loadSubscriber", method = RequestMethod.GET, produces = "application/json")
    public ResponseDTO<?> loadSubscribersDataToES(
            @RequestHeader(value = "apiKey") String encoding,
            @RequestHeader(value = "isSecure") boolean isSecure,
            HttpServletRequest httpServletRequest) throws Exception {
        mangeElasticLoadService.loadSubscriberDataToES(encoding, isSecure);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @RequestMapping(value = "/loadEquipment", method = RequestMethod.GET, produces = "application/json")
    public ResponseDTO<?> loadEquipmentsDataToES(
            @RequestHeader(value = "apiKey") String encoding,
            @RequestHeader(value = "isSecure") boolean isSecure,
            HttpServletRequest httpServletRequest) throws Exception {
        mangeElasticLoadService.loadEquipmentDataToES(encoding, isSecure);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


//    @Timed(value = AuditorConstants.USER_LOGIN)
    @ApiIgnore
    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.USER_LOGIN)
    @RequestMapping(value = "/token", method = RequestMethod.GET, produces = "application/json")
    public WebAsyncTask<ResponseDTO<?>> getOptimTokenFromCode(
            @RequestHeader(value = "apiKey") String apiKey,
            @RequestHeader(value = "isSecure") boolean isSecure,
            @RequestParam(name = "code") String code,
            @RequestParam(name = "state", required = false) String state,
            @RequestParam(name = "redirect_uri", required = false) String redirect_uri,
            @RequestParam(name = "oauthConfigName", required = false) String oauthConfigName,
            HttpServletRequest httpServletRequest) throws Exception {

        WebAsyncTask<ResponseDTO<?>> asyncTask;
        if(useNewOauthConfig) {
            LOG.info("Use new OAuth config");
            asyncTask = new WebAsyncTask<>(ApplicationConstants.OAUTH2_TOKEN_TIMEOUT, "longTaskExecutor", (Callable<ResponseDTO<?>>) () -> responseUtil.ok(oauthService.getOptimTokenFromOauthCode(apiKey, isSecure, code, state, redirect_uri, oauthConfigName, httpServletRequest), ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
        } else {
            asyncTask = new WebAsyncTask<>(ApplicationConstants.OAUTH2_TOKEN_TIMEOUT, "longTaskExecutor", (Callable<ResponseDTO<?>>) () -> responseUtil.ok(oauth2Service.getOptimTokenFromOauthCode(apiKey, isSecure, code, state, redirect_uri, oauthConfigName, httpServletRequest), ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
        }

        asyncTask.onTimeout(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));
        asyncTask.onError(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));

        return asyncTask;
    }

//    @Timed(value = AuditorConstants.USER_LOGIN)
    @ApiOperation(value = "Mobile User Login", response = LoginResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
    })
    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.USER_LOGIN)
    @RequestMapping(value = "/v2/token", method = RequestMethod.GET, produces = "application/json")
    public WebAsyncTask<ResponseDTO<?>> getOptimTokenFromOauthAccessToken(
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestParam(name = "oauthConfigName", required = false) String oauthConfigName,
            HttpServletRequest httpServletRequest) throws Exception {

        WebAsyncTask<ResponseDTO<?>> asyncTask;
        if(useNewOauthConfig) {
            LOG.info("Use new OAuth config");
            asyncTask = new WebAsyncTask<>(ApplicationConstants.OAUTH2_TOKEN_TIMEOUT, "longTaskExecutor", (Callable<ResponseDTO<?>>) () -> responseUtil.ok(oauthService.getOptimTokenFromOauthAccessToken(accessToken, oauthConfigName, httpServletRequest), ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
        } else {
            asyncTask = new WebAsyncTask<>(ApplicationConstants.OAUTH2_TOKEN_TIMEOUT, "longTaskExecutor", (Callable<ResponseDTO<?>>) () -> responseUtil.ok(oauth2Service.getOptimTokenFromOauthAccessToken(accessToken, oauthConfigName, httpServletRequest), ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
        }

        asyncTask.onTimeout(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));
        asyncTask.onError(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));

        return asyncTask;
    }

    @ApiIgnore
    @RequestMapping(value = "/oauth2/config", method = RequestMethod.GET, produces = "application/json")
    public ResponseDTO<?> getOauthConfig(
            @RequestHeader(value = "apiKey") String apiKey,
            @RequestHeader(value = "isSecure") boolean isSecure,
            HttpServletRequest httpServletRequest) throws Exception {
        try {
            return responseUtil.ok(oauth2Service.getDefaultOauthConfigAsResponse(apiKey, isSecure), ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        } catch (Exception e) {
            LOG.error("failed in oauth2 config", e);
            throw e;
        }
    }

    @ApiIgnore
    @RequestMapping(value = "/oauth2/configs", method = RequestMethod.GET, produces = "application/json")
    public ResponseDTO<?> getOauthConfigs(
            @RequestHeader(value = "apiKey") String apiKey,
            @RequestHeader(value = "isSecure") boolean isSecure,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(oauth2Service.getAllOauthConfigs(apiKey, isSecure), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @ApiOperation(value = "Get Optim Logo")
    @RequestMapping(value = "/logo/{type}", method = RequestMethod.GET)
    public ResponseDTO<?> getLogo(@PathVariable(name = "type") String type,
                                  @RequestHeader(value = "apiKey") String apiKey,
                                  @RequestHeader(value = "isSecure") boolean isSecure) throws Exception {
        return responseUtil.ok(oauth2Service.getLogo(type, apiKey, isSecure), ApiResponseCode.SUCCESS);
    }

    @ApiIgnore
    @ApiOperation(value = "Get Health of Platform")
    @RequestMapping(value = "/health", method = RequestMethod.GET)
    public ResponseDTO<?> getDeadlockedThreads() throws Exception {
        return responseUtil.ok(oauth2Service.getDeadlockedThreads(), ApiResponseCode.SUCCESS);
    }

    @ApiIgnore
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.USER_LOGIN)
    @RequestMapping(value = "/v5/oauth/login", method = RequestMethod.POST, produces = "application/json")
    public WebAsyncTask<ResponseDTO<?>> getAppTokenFromCode(
            @ApiParam(value = "Actions Properties", required = true) @RequestBody Map request,
            HttpServletRequest httpServletRequest) throws Exception {

        WebAsyncTask<ResponseDTO<?>> asyncTask = new WebAsyncTask<>(ApplicationConstants.OAUTH2_TOKEN_TIMEOUT, "longTaskExecutor", (Callable<ResponseDTO<?>>) () -> responseUtil.ok(oauthService.getAccessTokenFromOauth(request), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

        asyncTask.onTimeout(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));
        asyncTask.onError(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));

        return asyncTask;
    }

    @ApiIgnore
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.USER_LOGIN)
    @RequestMapping(value = "/v5/login", method = RequestMethod.POST, produces = "application/json")
    public WebAsyncTask<ResponseDTO<?>> userLogin(

            @ApiParam(value = "Actions Properties", required = true) @RequestBody @Valid LoginRequest loginRequest,
            HttpServletRequest httpServletRequest) throws Exception {

        WebAsyncTask<ResponseDTO<?>> asyncTask = new WebAsyncTask<>(ApplicationConstants.OAUTH2_TOKEN_TIMEOUT, "longTaskExecutor", (Callable<ResponseDTO<?>>) () -> responseUtil.ok(oauth2Service.getAccessTokenForLogin(loginRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));

        asyncTask.onTimeout(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));
        asyncTask.onError(() -> responseUtil.exception(ApiResponseCode.BAD_REQUEST));

        return asyncTask;
    }

    @ApiIgnore
    @RequestMapping(value = "/v5/oauth/configurations", method = RequestMethod.GET, produces = "application/json")
    public List<OauthResponse> getOauthConfigs(
            @RequestParam(name = "role", defaultValue = "", required = false) String role,
            HttpServletRequest httpServletRequest) throws Exception {

        List<String> roles = new ArrayList<String>(Arrays.asList(role.split(",")));
        List<OauthResponse> oauthResponseList = new ArrayList<>();

        List<OauthConfig> oauthConfigList = oauthConfigService.getOauthConfigs();

        for(OauthConfig oauthConfig : oauthConfigList) {
            OauthResponse oauthResponse = oauthConfigResponseMapper.toOauthResponse(oauthConfig);

            ArrayList<OauthConfigIspDetail> oauthConfigIspDetails = new ArrayList<>();
            oauthResponse.setIsps(oauthConfigIspDetails);

            OauthConfigRoleDetail oauthConfigRoleDetail = new OauthConfigRoleDetail();
            oauthResponse.setRole(oauthConfigRoleDetail);
            
            oauthResponseList.add(oauthResponse);

            // ArrayList<OauthConfigIspDetail> oauthConfigIspDetails = new ArrayList<>();
            // for(int i=0; i<oauthConfig.getIspIds().size(); i++) {
            //     String ispId = oauthConfig.getIspIds().get(i);
            //     OauthConfigIspDetail oauthConfigIspDetail = new OauthConfigIspDetail();
            //     oauthConfigIspDetail.setId(ispId);
            //     oauthConfigIspDetail.setName(at3Adapter.getIspNameById(ispId));
            //     if(oauthConfig.getDefaultIspId().equals(ispId)) {
            //         oauthConfigIspDetail.setDefaultIsp(true);
            //     } else {
            //         oauthConfigIspDetail.setDefaultIsp(false);
            //     }
            //     oauthConfigIspDetails.add(oauthConfigIspDetail);
            // }
            // oauthResponse.setIsps(oauthConfigIspDetails);

            // if(role.equals(ApplicationConstants.EMPTY_STRING)) {
            //     OauthConfigRoleDetail oauthConfigRoleDetail = new OauthConfigRoleDetail();
            //     oauthConfigRoleDetail.setId(oauthConfig.getRoleId());
            //     oauthConfigRoleDetail.setName(at3Adapter.getRoleNameById(oauthConfig.getRoleId()));
            //     oauthResponse.setRole(oauthConfigRoleDetail);
            //     oauthResponseList.add(oauthResponse);
            // } else {
            //     String roleName = at3Adapter.getRoleNameById(oauthConfig.getRoleId());
            //     if(roles.contains(roleName)) {
            //         OauthConfigRoleDetail oauthConfigRoleDetail = new OauthConfigRoleDetail();
            //         oauthConfigRoleDetail.setId(oauthConfig.getRoleId());
            //         oauthConfigRoleDetail.setName(roleName);
            //         oauthResponse.setRole(oauthConfigRoleDetail);
            //         oauthResponseList.add(oauthResponse);
            //     }
            // }
        }

        return oauthResponseList;
    }

    @ApiIgnore
    @RequestMapping(value = "/v5/oauth/configurations/{configId}", method = RequestMethod.GET, produces = "application/json")
    public List<OauthResponse> getOauthConfig(
            @ApiParam(value = "configId", required = true) @PathVariable(name = "configId") String configId,
            HttpServletRequest httpServletRequest) throws Exception {

        List<OauthResponse> oauthResponseList = new ArrayList<>();
        OauthConfig oauthConfig = oauthConfigService.getOauthConfigById(configId);
        OauthResponse oauthResponse = oauthConfigResponseMapper.toOauthResponse(oauthConfig);

        ArrayList<OauthConfigIspDetail> oauthConfigIspDetails = new ArrayList<>();
        for(int i=0; i<oauthConfig.getIspIds().size(); i++) {
            String ispId = oauthConfig.getIspIds().get(i);
            OauthConfigIspDetail oauthConfigIspDetail = new OauthConfigIspDetail();
            oauthConfigIspDetail.setId(ispId);
            oauthConfigIspDetail.setName(at3Adapter.getIspNameById(ispId));
            if(oauthConfig.getDefaultIspId().equals(ispId)) {
                oauthConfigIspDetail.setDefaultIsp(true);
            } else {
                oauthConfigIspDetail.setDefaultIsp(false);
            }
            oauthConfigIspDetails.add(oauthConfigIspDetail);
        }
        oauthResponse.setIsps(oauthConfigIspDetails);

        OauthConfigRoleDetail oauthConfigRoleDetail = new OauthConfigRoleDetail();
        oauthConfigRoleDetail.setId(oauthConfig.getRoleId());
        oauthConfigRoleDetail.setName(at3Adapter.getRoleNameById(oauthConfig.getRoleId()));
        oauthResponse.setRole(oauthConfigRoleDetail);

        oauthResponseList.add(oauthResponse);
        return oauthResponseList;
    }
}

