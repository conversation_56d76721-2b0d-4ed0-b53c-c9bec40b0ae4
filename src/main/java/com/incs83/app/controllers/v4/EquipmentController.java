package com.incs83.app.controllers.v4;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v4.ManageEquipmentService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.enums.ApiResponseCodeImpl;
import com.incs83.app.responsedto.v2.fSecure.TimeZoneResponse;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.responsedto.v3.TimeZoneRequest;
import com.incs83.app.responsedto.v4.EquipmentDetailsDTO;
import com.incs83.app.responsedto.v4.EquipmentHistoricalDetailDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * Created by Jayant Puri on 07-07-2017.
 */

@RestController("v4.EquipmentController")
@Api(value = "(V4) Equipment", description = "API's for Equipment Operations",tags = {"Optim - (V4) Equipment"})
@RequestMapping(value = "/actiontec/api/v4/equipments")
@SuppressWarnings("rawtypes")
public class EquipmentController {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_DETAILS_READ)
    @ApiOperation(value = "Get Equipment Details for an Equipment by Serial Number", response = EquipmentDetailsDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(required = true, value = "Attribute for which the details are requested.\n\nAllowed Attributes are case-sensitive and comma seprated \n\n[\n\nethernet,\n\nMoCA,\n\nMoCALAN,\n\nsystemInfo,\n\nwan,\n\n_24g,\n\n_5g\n\n]" +
                    "\n\nExample for ethernet and MoCA info\n\n" +
                    "attr = ethernet,MoCA")
            @RequestParam(name = "attr") String attr,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, attr), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_HISTORICAL_DATA)
    @ApiOperation(value = "Get Equipment Historical Data", response = EquipmentHistoricalDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/{serialNumber}/historical", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentGraph(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest,
            @ApiParam(required = true, value = "DurationFrom(UTC Timestamp in milliseconds) should be less than current timestamp and durationTo \n\n Example - durationFrom=1542690499123")
            @RequestParam(name = "durationFrom") Long durationFrom,
            @ApiParam(required = true, value = "DurationTo(UTC Timestamp in milliseconds) should be less than or equals to current timestamp and more than durationFrom \n\n Example - durationTo=1542863380000")
            @RequestParam(name = "durationTo") Long durationTo,
            @ApiParam(required = true, value = "Attribute for which the details are requested.\n\nAllowed Attributes are case-sensitive and comma seprated \n\n[\n\n_24gBusy,\n\n_24gBusyByDevice,\n\n_5gBusy,\n\n_5gBusyByDevice\n\n]" +
                    "\n\nExample for _24gBusy and _5gBusy info \n\n" +
                    "attr = _24gBusy,_5gBusy")
            @RequestParam(name = "attr") String attr) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentGraph(equipmentIdOrSerialOrSTN, serialNumber, attr, durationFrom, durationTo), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Edit the data of time settings", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/time", method = RequestMethod.PUT)
    @Auditable(operation = AuditorConstants.TIMEZONE_UPDATE,method = RequestMethod.PUT)
//    @Timed(value = AuditorConstants.TIMEZONE_UPDATE)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
    public ResponseDTO<?> editTimeZoneSettings(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "The payload of timezone settings", required = true) @RequestBody @Valid TimeZoneRequest timeZoneRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        manageEquipmentService.setTimeZoneForRGW(equipmentIdOrSerialOrSTN, timeZoneRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get the data of time settings", response = TimeZoneResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = TimeZoneResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/time", method = RequestMethod.GET)
    @Auditable(operation = AuditorConstants.GET_TIMEZONE_DATA,method = RequestMethod.GET,persist = false)
//    @Timed(value =  AuditorConstants.GET_TIMEZONE_DATA)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getTimeZoneSettings(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "SubscriberId or SerialNumber or RGW MAC", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getTimeZoneForRGW(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


}
