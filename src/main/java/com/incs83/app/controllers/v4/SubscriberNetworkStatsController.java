package com.incs83.app.controllers.v4;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v4.ManageSubscriberNetworkStatsService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.enums.ApiResponseCodeImpl;
import com.incs83.app.responsedto.v2.Network.TopologyDTO;
import com.incs83.app.responsedto.v2.Subscriber.InternetUtilizationDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.responsedto.v4.SubscriberNetworkStatsDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController("v4.SubscriberNetworkStatsController")
@Api(value = "(V4) Subscriber Home Network Stats", description = "API's for Subscriber Home Network Stats",tags = {"Optim - (V4) Subscriber Home Network Stats"})
@RequestMapping(value = "/actiontec/api/v4/equipments")
public class SubscriberNetworkStatsController {
    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageSubscriberNetworkStatsService manageSubscriberNetworkStatsService;

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORK_STATS)
    @ApiOperation(value = "Get Subscriber Network Stats Details", response = SubscriberNetworkStatsDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getSubscriberNetworkStats(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(required = true, value = "Attribute for which the details are requested.\n" +
                    "\n" +
                    "Allowed Attributes are case-sensitive and comma seprated \n\n[\n\navgUplinkRate,\n\navgDownlinkRate,\n\nhealthScore,\n\ninternet\n\n]" +
                    "\n\nExample for healthScore and internet\n\n" +
                    "attr = healthScore,internet")
            @RequestParam(name = "attr") String attr,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getSubscriberNetworkStats(equipmentIdOrSerialOrSTN, attr), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.INTERNET_UTILIZATION_LINE_CHART)
    @ApiOperation(value = "Get Networks Internet Utilization Graph Data for Subscribers", response = InternetUtilizationDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/historical", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchSubscriberNetworkInternetUtilizationGraphData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest,
            @ApiParam(required = true, value = "DurationFrom(UTC Timestamp in milliseconds) should be less than current timestamp and durationTo \n\n Example - durationFrom=1542690499123")
            @RequestParam(name = "durationFrom") Long durationFrom,
            @ApiParam(required = true, value = "DurationTo(UTC Timestamp in milliseconds) should be less than or equals to current timestamp and more than durationFrom  \n\n Example - durationTo=1542863380000")
            @RequestParam(name = "durationTo") Long durationTo,
            @ApiParam(required = true, value = "Attribute For which the details are requested.\n\nAllowed Attributes is case-sensitive \n\n[internetUtilization]\n\n" +
                    "\n\nExample \n\nattr = internetUtilization")
            @RequestParam(name = "attr") String attr) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getInternetUtilizationLineChartData(equipmentIdOrSerialOrSTN, attr, durationFrom, durationTo).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.TOPOLOGY_FOR_SUBSCRIBER)
    @ApiOperation(value = "Get Home Network Topology for a Subscriber", response = TopologyDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/topology", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchAPDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getTopology(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
