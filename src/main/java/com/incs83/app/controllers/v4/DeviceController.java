package com.incs83.app.controllers.v4;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v4.ManageDeviceService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.enums.ApiResponseCodeImpl;
import com.incs83.app.responsedto.v2.Device.DeviceDetailDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.responsedto.v4.DeviceHistoricalDetailDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * Created by Jayant Puri on 07-07-2017.
 */

@RestController("v4.DeviceController")
@Api(value = "(V4) Devices", description = "API's for Device Operations",tags = {"Optim - (V4) Devices"})
@RequestMapping(value = "/actiontec/api/v4/equipments")
@SuppressWarnings("rawtypes")
public class DeviceController {

    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageDeviceService manageDeviceService;

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.STATION_DETAILS)
    @ApiOperation(value = "Get station details for a station MAC", response = DeviceDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/detail", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.fetchDeviceDetailsByMacAddr(equipmentIdOrSerialOrSTN, macAddress).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.STATION_DETAILS_HISTORICAL)
    @ApiOperation(value = "Get station details historical data", response = DeviceHistoricalDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/historical", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceInfoByMac(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest,
            @ApiParam(required = true, value = "DurationFrom(UTC Timestamp in milliseconds) should be less than current timestamp and durationTo \n\n Example - durationFrom=1542690499123")
            @RequestParam(name = "durationFrom") Long durationFrom,
            @ApiParam(required = true, value = "DurationTo(UTC Timestamp in milliseconds) should be less than or equals to current timestamp and more than durationFrom \n\n Example - durationTo=1542863380000")
            @RequestParam(name = "durationTo") Long durationTo,
            @ApiParam(required = true, value = "Attribute for which the details are requested.\n" +
                    "\n" +
                    "Allowed Attributes are case-sensitive and comma seprated \n\n[\n\nassocEvents,\n\ndisAssocEvents,\n\nroamEvents,\n\nsteeringEvents,\n\nsteeringLogs,\n\nwifiThroughput,\n\nrssi,\n\nwifiPhy\n\n]" +
                    "\n\nExample for rssi and wifiPhy\n\n" +
                    "attr = rssi,wifiPhy")
            @RequestParam(name = "attr") String attr) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceSteeringEventsByMACAddress(equipmentIdOrSerialOrSTN, macAddress, durationFrom, durationTo, attr), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}

