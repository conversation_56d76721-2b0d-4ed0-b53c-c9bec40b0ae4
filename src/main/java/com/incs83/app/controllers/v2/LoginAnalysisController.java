package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.LoginAnalysis;
import com.incs83.app.authResources.User;
import com.incs83.app.business.v2.ManageLoginAnalysisService;
import com.incs83.app.common.v3.LoginUserInfoRequestDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@Api(value = "(V2) Login Analysis", description = "API's for Login Analysis", tags = {"Optim - (V2) Login Analysis"})
@RequestMapping(value = "/actiontec/api/v2")
public class LoginAnalysisController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageLoginAnalysisService manageLoginAnalysisService;


    @ApiOperation(value = "Get Login Analysis", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })

    @RequestMapping(value = "/loginAnalysis", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = LoginAnalysis.class)
    public ResponseDTO<?> getLoginAnalysis(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageLoginAnalysisService.getLoginAnalysis(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }


    @ApiOperation(value = "Get Login User Info", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/loginUserInfo", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = LoginAnalysis.class)
    public ResponseDTO<?> getLoginUserInfo(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "The payload of  rules", required = true) @RequestBody LoginUserInfoRequestDTO loginUserInfoRequestDTO,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageLoginAnalysisService.getLoginUserInfo(loginUserInfoRequestDTO), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }


}
