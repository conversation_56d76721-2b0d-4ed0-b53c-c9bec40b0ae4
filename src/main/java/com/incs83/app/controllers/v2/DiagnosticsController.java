package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.ETLDiagnostics;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v2.DiagnosticsService;
import com.incs83.app.common.v2.EtlResetRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.lang.management.ManagementFactory;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * Created By AMAN on Monday, 24-December-2018
 */
@RestController
@Api(value = "(V2) Diagnostics", description = "API for CPE Diagnostics", tags = {"Optim - (V2) Diagnostics"})
@RequestMapping(value = "/actiontec/api/v2/diagnostics")
public class DiagnosticsController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private DiagnosticsService diagnosticsService;

    private static final Logger LOG = LogManager.getLogger("org");

//    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.RGW_MONITOR_MODE)
//    @Timed(value = AuditorConstants.RGW_MONITOR_MODE )
    @ApiOperation(value = "Equipment diagnostic Mode", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}", method = RequestMethod.PATCH)
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public  ResponseDTO<?> channelReset(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Monitor Mode")
            @RequestParam(name = "diagnosticsEnabled") Boolean diagnosticsEnabled,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
                return responseUtil.ok(diagnosticsService.enableDisableDiagnos(equipmentIdOrSerialOrSTN, diagnosticsEnabled,httpServletRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.ETL_RESTART)
//    @Timed(value = AuditorConstants.ETL_RESTART )
    @ApiOperation(value = "ETL diagnostic reset", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/etl", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = ETLDiagnostics.class)
    public ResponseDTO<?> etlReset(
            @ApiParam(value = "ETL properties", required = true) @RequestBody EtlResetRequest etlPropertiesRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,

            HttpServletRequest httpServletRequest) throws Exception {
        diagnosticsService.etlReset(etlPropertiesRequest, httpServletRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ETL_PROPERTIES_READ)
    @RequestMapping(value = "/etl/{etlName}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = ETLDiagnostics.class)
    public ResponseDTO<?> etlProperties(
            @ApiParam(value = "ETL name", required = true) @PathVariable(name = "etlName") String etlName,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,

            HttpServletRequest httpServletRequest) throws Exception {

        return responseUtil.ok(diagnosticsService.getETLProperties(etlName), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

}
