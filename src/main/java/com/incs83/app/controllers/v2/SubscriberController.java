/*
package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.*;
import com.incs83.app.business.v2.ManageSubscriberService;
import com.incs83.app.common.v2.CreateSubscriberRequest;
import com.incs83.app.common.v2.EditEquipmentRequest;
import com.incs83.app.common.v2.RGWPaginationRequest;
import com.incs83.app.common.v3.TechnicianDashboardDTO;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.Equipment.RGWDisconnectedDTO;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberDetailDTO;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberList;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberListDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;

@RestController
@Api(value = "(V2) Subscribers", description = "API's for Subscriber Operations", tags = {"Optim - (V2) Subscribers"})
@RequestMapping(value = "/actiontec/api/v2/subscribers")
@SuppressWarnings("rawtypes")
public class SubscriberController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageSubscriberService manageSubscriberService;

    @ApiIgnore
    @Deprecated
    @ApiOperation(value = "Get all Subscribers list", response = SubscriberListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Subscribers.class)
    public ResponseDTO<?> getAllSubscribers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Max no of record.Numeric Value Allowed.") @RequestParam(name = "max", required = false, defaultValue = "10") Integer max,
            @ApiParam(value = "Offset, Numeric Value Allowed.") @RequestParam(name = "offset", required = false, defaultValue = "0") Integer offset,
            @ApiParam(value = "Order of Record. Allowed values - ASC,DESC") @RequestParam(name = "order", required = false, defaultValue = "DESC") String order,
            @ApiParam(value = "SortBy Record") @RequestParam(name = "sortBy", required = false, defaultValue = "createdAt") String sortBy,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.getAllSubscribers(max, offset, order, sortBy, false), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.SUBSCRIBER_SELECT)
    @ApiOperation(value = "Find subscriber Details by Id", response = SubscriberDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    //path variable name(serialNumberOrSubscriberIdOrRGWMAC) changed for audit logs
    public ResponseDTO<?> getSubscriberById(@ApiParam(value = "Subscriber Id", required = true) @PathVariable(name = "id") String serialNumberOrSubscriberIdOrRGWMAC,
                                            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                            @RequestHeader(name = "X-Authorization") String accessToken,
                                            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.findSubscriberByIdOrSerial(serialNumberOrSubscriberIdOrRGWMAC), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_BY_RGW_SNO)
    @ApiOperation(value = "Find subscriber Details by RGW Serial Number", response = SubscriberDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/serial/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Subscribers.class)
    public ResponseDTO<?> getSubscriberBySerialNumber(@ApiParam(value = "Serial Number", required = true) @PathVariable(name = "serialNumber") String serialNumber,
                                                      @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                      @RequestHeader(name = "X-Authorization") String accessToken,
                                                      HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.findSubscriberByIdOrSerial(serialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_BY_SERIAL_NO_LIST)
    @ApiOperation(value = "Find subscriber Details by Serial Number List", response = SubscriberList.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/serials", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Subscribers.class)
    public ResponseDTO<?> getSubscriberBySerialNumberList(
            @ApiParam(value = "Serial Number CSV String (Comma Separated Serial Number)", required = true)
            @RequestParam(name = "serialNumberCsv") String serialNumberCsv,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.findSubscriberByIdOrSerial(Arrays.asList(serialNumberCsv.split(COMMA)), false), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Create a Subscriber", response = ApiResponseDTO.class)
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SUBSCRIBER_CREATE)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Subscribers.class)
    @ResponseStatus(HttpStatus.CREATED)
    public void createSubscriber(@RequestBody @Valid CreateSubscriberRequest createSubscriberRequest,
                                 @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                 @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        manageSubscriberService.createSubscriber(createSubscriberRequest);
    }

@ApiOperation(value = "Create Subscribers (Bulk Operation)", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/bulk", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Subscribers.class)
    public ResponseDTO<?> createSubscriberBulk(@RequestBody MultipartFile file,
                                               @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                               @RequestHeader(name = "X-Authorization") String accessToken,
                                               HttpServletRequest httpServletRequest) throws Exception {
        manageSubscriberService.createSubscriberBulk(file);
        return responseUtil.ok(ApiResponseCodeImpl.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }



    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SUBSCRIBER_DELETE)
    @ApiOperation(value = "Delete a Subscriber by Id")
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Subscribers.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteSubscriber(
            @ApiParam(value = "Id of the Subscriber to be deleted", required = true)
            @PathVariable(name = "id") String subscriberId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        manageSubscriberService.deleteSubscriber(subscriberId);
    }

    @ApiIgnore
    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SUBSCRIBER_BULK_DELETE)
    @ApiOperation(value = "Delete Subscribers in bulk list")
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/bulk", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Subscribers.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteSubscriberBulk(
            @ApiParam(value = "Ids of the Subscriber to be deleted", required = true)
            @RequestBody List<String> subscriberIds,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        manageSubscriberService.deleteSubscriber(subscriberIds);
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SUBSCRIBER_UPDATE)
    @ApiOperation(value = "Update the Subscriber Details by Subscriber Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = Subscribers.class)
    public ResponseDTO<?> updateSubscriber(
            @ApiParam(value = "Id of the Subscriber to be Updated", required = true)
            @PathVariable(name = "id") String subscriberId,
            @RequestBody @Valid EditEquipmentRequest editSubscriberRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        manageSubscriberService.updateSubscriber(subscriberId, editSubscriberRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.SUBSCRIBERS_FILE_UPLOAD)
    @ApiOperation(value = "Update Bulk Subscriber Details", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/bulk", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = SubscriberBulkUpdation.class)
    public ResponseDTO<?> updateBulkSubscriberDetails(
            @RequestParam(name = "file") MultipartFile fileDetails,
            @RequestParam(name = "isp") String ispId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        manageSubscriberService.subscriberBulkFileUpload(fileDetails, ispId);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiIgnore
    @RequestMapping(value = "/mqtt/status", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = MQTTConnectStatus.class)
    public ResponseDTO<?> getSubscriberStatus(
            @RequestParam("rgwSerialNumber") String rgwSerialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return responseUtil.ok(manageSubscriberService.getSubscriberStatus(rgwSerialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.POST,operation = AuditorConstants.RGW_DISCONNECTED_LIST)
    @ApiOperation(value = "Get Disconnected RGW List", response = RGWDisconnectedDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/disconnected/rgw", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = DisconnectedRGW.class)
    public ResponseDTO<?> getDisconnectedRGWList(
            @RequestBody RGWPaginationRequest rgwPaginationRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.getRGWDisconnectedList(rgwPaginationRequest).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.POST,operation = AuditorConstants.SUBSCRIBER_LIST)
    @ApiOperation(value = "Get all Subscribers list", response = SubscriberListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/paginated", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public ResponseDTO<?> getAllSubscribers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody TechnicianDashboardDTO technicianDashboardDTO,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.getAllSubscriberList(technicianDashboardDTO), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

}
*/
