package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.*;
import com.incs83.app.business.v2.ManageApplicationStatsService;
import com.incs83.app.common.v2.*;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.enums.auditor.AuditorEnum;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.responsedto.v2.serviceStats.AppStatsResourceSpecification;
import com.incs83.app.responsedto.v2.serviceStats.HistoricalResourceSpecifications;
import com.incs83.app.responsedto.v2.serviceStats.ResourceSpecificationInfo;
import com.incs83.app.responsedto.v2.serviceStats.ServiceNotification.ETLFileConfigRequest;
import com.incs83.app.responsedto.v2.serviceStats.ServiceNotification.ServiceNotificationRequest;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import com.mongodb.DBObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ApiIgnore
@RestController
@Api(value = "Application Stats", description = "REST API for Application Stats")
@RequestMapping(value = "/actiontec/api/v2/app/stats")
public class ApplicationStatsController {
    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageApplicationStatsService manageApplicationStatsService;

    @RequestMapping(value = "/logIn/users", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentHealth.class)
    public ResponseDTO<?> getAllLoggedinUsers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getAllLoggedInUsers(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/subscribers/onboarding", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentHealth.class)
    public ResponseDTO<?> getStatsForOnBoardingOfSubscriber(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getStatsForOnBoardingOfSubscriber(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/rgw", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentHealth.class)
    public ResponseDTO<?> getRgwStats(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @ApiParam(value = "Duration, Default value is 60") @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "ISP, Default value is Global") @RequestParam(required = false, name = "isp") String isp) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getRgwStats(duration, isp), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/device", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentHealth.class)
    public ResponseDTO<?> getDeviceStats(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @ApiParam(value = "Duration, Default value is 60") @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "ISP, Default value is Global") @RequestParam(required = false, name = "isp") String isp) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getDeviceStats(duration, isp), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @RequestMapping(value = "/devices/connectivityStatus", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentHealth.class)
    public ResponseDTO<?> getDeviceConnectivityStatus(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "ISP, Default value is Global") @RequestParam(required = false, name = "isp") String isp,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getDeviceConnectivityStatusUsingCache(isp), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/topics", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getAllKafkaTopics(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getAllKafkaTopics(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/bytesReceived/health", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getKafkaBytesReceivedTimeSeriesData(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Kafka Topic")
            @RequestParam(name = "kafkaTopic") String kafkaTopic,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getBytesReceivedTimeSeriesData(duration, kafkaTopic), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/byteFetched/health", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getKafkaBytesFetchedTimeSeriesData(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Kafka Topic")
            @RequestParam(name = "kafkaTopic") String kafkaTopic,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getKafkaBytesFetchedTimeSeriesData(duration, kafkaTopic), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/messagesReceived/health", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getKafkaMessagesReceivedTimeSeriesData(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Kafka Topic")
            @RequestParam(name = "kafkaTopic") String kafkaTopic,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getKafkaMessagesReceivedTimeSeriesData(duration, kafkaTopic), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/messagesReceived/brokers", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getKafkaMessagesReceivedAcrossService(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getKafkaMessagesReceivedAcrossService(duration), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/brokers/partitions", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getTotalPartitionsAcrossKafkaBrokers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getTotalPartitionsAcrossKafkaBrokers(duration), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/brokers/offline/partitions", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getOfflinePartitionsAcrossKafkaBrokers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getOfflinePartitionsAcrossKafkaBrokers(duration), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/brokers/replicated/partitions", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getUnderReplicatedPartitionsAcrossKafkaBrokers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getUnderReplicatedPartitionsAcrossKafkaBrokers(duration), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/kafka/brokers/leader/replicas", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = KafkaDiagnostics.class)
    public ResponseDTO<?> getLeaderReplicasAcrossKafkaBrokers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getLeaderReplicasAcrossKafkaBrokers(duration), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/onBoarding", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = OnBoardingStats.class)
    public ResponseDTO<?> getApplicationStatsForOnBoardingInfo(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Max no of record.Numeric Value Allowed.") @RequestParam(name = "max", required = false, defaultValue = "10") Integer max,
            @ApiParam(value = "Offset, Numeric Value Allowed.") @RequestParam(name = "offset", required = false, defaultValue = "0") Integer offset,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getOnBoardingStats(offset, max), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/executors/{type}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SparkDiagnostics.class)
    public ResponseDTO<?> getSparkExecuters(
            @PathVariable String type,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getSparkExecutors(type), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/historical/rgw/reports", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentReport.class)
    public ResponseDTO<?> getRgwStatsPerDayReport(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @ApiParam(value = "Duration, Default value is 129600") @RequestParam(required = false, name = "duration", defaultValue = "129600") Long duration,
            @ApiParam(value = "ISP of required data") @RequestParam(name = "isp") String isp) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getRgwStatsPerDayReport(duration, isp), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/services", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> getApplicationServiceStats(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getApplicationServiceStats(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/loadBalancer", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> getLoadBalancerStats(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getLoadBalancerInfo(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @RequestMapping(value = "/cloudWatch/cpuUtilization", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForCPUUtilization(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudTimeSeriesRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForCPUUtilization(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/networkPacketsIn", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkPacketsIn(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudTimeSeriesRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkPacketsIn(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/networkPacketsOut", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkPacketsOut(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudTimeSeriesRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkPacketsOut(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/activeFlowCount", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBActiveFlowCount(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBActiveFlowCount(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/activeFlowCountTls", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBActiveFlowCountTLS(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBActiveFlowCountTLS(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/consumedLcus", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBConsumedLCUs(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBConsumedLCUs(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/newFlowCount", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBNewFlowCount(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBNewFlowCount(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/newFlowCountTls", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBNewFlowCountTLS(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBNewFlowCountTLS(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/tcpClientResetCount", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBTCPClientResetCount(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBTCPClientResetCount(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/tcpELBResetCount", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBTCPELBResetCount(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBTCPELBResetCount(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/network/tcpTargetResetCount", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForNetworkELBTCPTargetResetCount(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForNetworkELBTCPTargetResetCount(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/classic/latency", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForClassicLatency(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForClassicLatency(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/classic/requestCount", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForClassicRequestCount(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForClassicRequestCount(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/classic/elbError/500", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForClassicElbError500(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForClassicElbError500(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/classic/error/400", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForClassicBackendError400(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForClassicBackendError400(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/elb/classic/error/500", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForClassicBackendError500(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody CloudMetricsRequest cloudMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForClassicBackendError500(cloudMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/volume/readOps", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForVolumeReadOps(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody VolumeMetricsRequest volumeMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForVolumeReadOps(volumeMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/volume/writeOps", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForVolumeWriteOps(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody VolumeMetricsRequest volumeMetricsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForVolumeWriteOps(volumeMetricsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

//    @RequestMapping(value = "/cloudWatch/volume/statistics", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
//    public ResponseDTO<?> getCloudWatchMetricsForVolumeStats(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestBody VolumeStatisticsRequest volumeStatisticsRequest,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getCloudWatchMetricsForVolumeStats(volumeStatisticsRequest), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

    @RequestMapping(value = "/resource/specification", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getAppStatsResourceSpecifications(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody AppStatsResourceSpecification appStatsResourceSpecification,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getAppStatsResourceSpecification(appStatsResourceSpecification), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/historical/rps", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> getNginxStatsForRPS(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody HistoricalResourceSpecifications historicalResourceSpecifications,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getHistoricalStatsForRPS(historicalResourceSpecifications), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/historical/cps", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> getNginxStatsForCPS(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody HistoricalResourceSpecifications historicalResourceSpecifications,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getHistoricalStatsForCPS(historicalResourceSpecifications), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/historical/rpc", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> getNginxStatsForRPC(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody HistoricalResourceSpecifications historicalResourceSpecifications,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getHistoricalStatsForRPC(historicalResourceSpecifications), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/resource/specification/diskInfo", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getDiskInfoResourceSpecifications(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody ResourceSpecificationInfo resourceSpecificationInfo,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getDiskInfoResourceSpecifications(resourceSpecificationInfo), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/resource/specification/historical/diskInfo", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getHistoricalDiskInfoResourceSpecifications(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody HistoricalResourceSpecifications historicalResourceSpecifications,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getHistoricalDataForDiskInfo(historicalResourceSpecifications), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/resource/specification/memoryInfo", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getMemoryInfoResourceSpecifications(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody ResourceSpecificationInfo resourceSpecificationInfo,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getMemoryInfoResourceSpecifications(resourceSpecificationInfo), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/resource/specification/historical/memoryInfo", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getHistoricalMemoryInfoResourceSpecifications(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody HistoricalResourceSpecifications historicalResourceSpecifications,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getHistoricalDataForMemoryInfo(historicalResourceSpecifications), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/resource/specification/tcpInfo", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getTcpInfoResourceSpecifications(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody ResourceSpecificationInfo resourceSpecificationInfo,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getTcpInfoResourceSpecifications(resourceSpecificationInfo), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/resource/specification/historical/tcpInfo", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getHistoricalTcpInfoResourceSpecifications(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody HistoricalResourceSpecifications historicalResourceSpecifications,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getHistoricalDataForTcpInfo(historicalResourceSpecifications), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.NOTIFICATION_UPDATE)
////    @Timed(value = operations)
    @RequestMapping(value = "/config/notification", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Notification.class)
    public ResponseDTO<?> updateServiceNotification(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody @Valid List<ServiceNotificationRequest> serviceNotificationList,
            HttpServletRequest httpServletRequest) throws Exception {
        manageApplicationStatsService.updateServiceNotification(serviceNotificationList);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @RequestMapping(value = "/config/notification", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Notification.class)
    public ResponseDTO<?> getServiceNotification(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getServiceNotification(), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/config/etlUpload/{type}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Notification.class)
    public ResponseDTO<?> getETLFileUploadConfig(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "type") String type,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getETLFileUploadConfig(type), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.ETL_UPLOAD_CONFIG)
//    @Timed(value = AuditorConstants.ETL_UPLOAD_CONFIG)
    @RequestMapping(value = "/config/etlUpload", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Notification.class)
    public ResponseDTO<?> updateETLFileConfig(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody @Valid List<ETLFileConfigRequest> etlFileConfigRequestList,
            HttpServletRequest httpServletRequest) throws Exception {
        manageApplicationStatsService.updateETLFileConfig(etlFileConfigRequestList);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.APPLICATION_NOTIFICATION_UPDATE)
//    @Timed(value = AuditorConstants.APPLICATION_NOTIFICATION_UPDATE)
    @RequestMapping(value = "/notification", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = ApplicationNotification.class)
    @ResponseStatus(HttpStatus.CREATED)
    public void createNotification(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @ApiParam(value = "Notification content") @RequestBody HashMap<String, Object> content) throws Exception {
        manageApplicationStatsService.createNotification(content);
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.APPLICATION_NOTIFICATION_UPDATE)
//    @Timed(value = AuditorConstants.APPLICATION_NOTIFICATION_UPDATE)
    @RequestMapping(value = "/notification/{id}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = ApplicationNotification.class)
    public ResponseDTO<?> updateNotification(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @ApiParam(value = "Notification enable/disable") @RequestParam(name = "active") Boolean isActive,
            @ApiParam(value = "Notification ID") @PathVariable(name = "id") String notificationId) throws Exception {
        manageApplicationStatsService.updateNotification(notificationId, isActive);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @RequestMapping(value = "/paginated/notification", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = ApplicationNotification.class)
    public ResponseDTO<?> getNotificationList(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @RequestBody NotificationRequest notificationRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getNotificationsList(notificationRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiIgnore
    @RequestMapping(value = "/notification/mqttInfo", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Notification.class)
    public ResponseDTO<?> getNotificationMqttInfo(HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.getNotificationMqttInfo(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SDA_ACTIONS)
//    @Timed(value = AuditorConstants.SDA_ACTIONS )
    @RequestMapping(value = "/service/action", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = SystemHealth.class)
    public ResponseDTO<?> performSDAOperation(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @RequestBody SDAActions sdaActions) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.performSdaAction(sdaActions), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Get Rpc Stats", response = DBObject.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/rpcStats", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> getRpcStats(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @ApiParam(value = "Operation value must not be empty") @RequestParam(name = "operation") String operation,
            @ApiParam(value = "Duration, Default value is 60") @RequestParam(name = "duration", defaultValue = "60") Long duration) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.performRpcStats(operation, duration), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Get Total Rpc Stats Counts", response = DBObject.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/rpcStatsCount", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> getRpcStatsCount(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @ApiParam(value = "Duration, Default value is 60") @RequestParam(name = "duration", defaultValue = "60") Long duration) throws Exception {
        return responseUtil.ok(manageApplicationStatsService.performRpcStats(duration), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get All RPC's List", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = AuditEvent.class)
    @RequestMapping(value = "/rpcNames", method = RequestMethod.GET)
    public ResponseDTO<?> getAuditOperationNames(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                 @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(Stream.of(AuditorEnum.values()).map(AuditorEnum::getValue).filter(q -> q.contains("RPC")).sorted().collect(Collectors.toList()));
    }


    @RequestMapping(value = "/totalUsers/info", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SystemHealth.class)
    public ResponseDTO<?> totalUsersInfo(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration, Default value is 60") @RequestParam(name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {

        return responseUtil.ok(manageApplicationStatsService.totalUsersInfo(duration), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


//    @RequestMapping(value = "/etlVersionsCount", method = RequestMethod.GET, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
//    public ResponseDTO<?> getEtlVersionCounts(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageApplicationStatsService.getETLVersionCounts(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }

}
