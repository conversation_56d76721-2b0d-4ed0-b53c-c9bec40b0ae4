package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.ISP;
import com.incs83.app.business.v2.ISPService;
import com.incs83.app.common.v2.ISPRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.enums.ApiResponseCodeImpl;
import com.incs83.app.responsedto.v2.isp.ISPDTO;
import com.incs83.app.responsedto.v2.isp.ISPDTOList;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@Api(value = "(V2) Internet Service Provider", description = "REST API's for Managing ISP",tags = {"Optim - (V2) Internet Service Provider"})
@RequestMapping(value = "/actiontec/api/v2/isp")
public class ISPController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ISPService ispService;

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.ISP_CREATE)
//    @Timed(value =  AuditorConstants.ISP_CREATE)
    @ApiOperation(value = "Create an ISP", response = ISPDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = ISP.class)
    @ResponseStatus(HttpStatus.CREATED)
    public void createISP(@RequestBody @Valid ISPRequest ispRequest,
                          @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                          @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        ispService.createISP(ispRequest);
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.ISP_UPDATE)
//    @Timed(value = AuditorConstants.ISP_UPDATE)
    @ApiOperation(value = "Update ISP by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = ISP.class)
    public ResponseDTO<?> updateISP(@ApiParam(value = "ISP Id", required = true) @PathVariable(name = "id") String ispId,
                                    @RequestBody @Valid ISPRequest payload,
                                    @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        ispService.updateISP(payload, ispId);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @ApiIgnore
    @ApiOperation(value = "Get All Mapped ISPs with group list", response = ISPDTOList.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET, value = "/mapped")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public Object getMappedISPs(HttpServletRequest httpServletRequest, @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(ispService.getAllMappedISPs().getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.ISP_READ)
    @ApiOperation(value = "Get All ISPs list", response = ISPDTOList.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public Object getISPs(HttpServletRequest httpServletRequest, @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(ispService.getAllISPs().getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.ISP_READ_BY_ID)
    @ApiOperation(value = "Get ISP by Id", response = ISPDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = ISP.class)
    public Object getISPById(HttpServletRequest httpServletRequest, @ApiParam(value = "Ticket Id", required = true) @PathVariable(name = "id") String id,
                             @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                             @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(ispService.getISPById(id).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.ISP_DELETE)
//    @Timed(value =  AuditorConstants.ISP_DELETE)
    @ApiOperation(value = "Delete ISP by Id", response = ISPDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = ISP.class)
    public void deleteISPById(@ApiParam(value = "Ticket Id", required = true) @PathVariable(name = "id") String ispId,
                              @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                              @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        ispService.deleteISPById(ispId);
    }
}
