package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.SubscriberBulkUpdation;
import com.incs83.app.business.v2.ManageSubscriberInfoService;
import com.incs83.app.common.v2.FileRequest;
import com.incs83.app.common.v2.SubscriberInfoRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.UsersDTO.SubscriberListDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;

@ApiIgnore
@RestController
@Api(value = "Subscribers Demographic Information", description = "REST API for Subscribers Demographic Information")
@RequestMapping(value = "/actiontec/api/v2/file")
public class SubscriberInfoController {
    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageSubscriberInfoService manageSubscriberInfoService;

//    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.UPLOADED_FILES_READ)
    @ApiOperation(value = "Get all Uploaded File list", response = SubscriberListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SubscriberBulkUpdation.class)
    public ResponseDTO<?> getAllSubscriberDetails(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberInfoService.getFileUploadDetails(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.PROCESS_SUBSCRIBERS_BULK_UPDATE)
//    @Timed(value = AuditorConstants.PROCESS_SUBSCRIBERS_BULK_UPDATE)
    @RequestMapping(method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = SubscriberBulkUpdation.class)
    public ResponseDTO<?> processCSV(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                     @RequestHeader(name = "X-Authorization") String accessToken,
                                     @RequestBody FileRequest fileRequest,
                                     HttpServletRequest httpServletRequest) throws Exception {
        manageSubscriberInfoService.processBulkSubscriberDetails(fileRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SUBSCRIBERS_FILE_DELETE)
//    @Timed(value = AuditorConstants.SUBSCRIBERS_FILE_DELETE)
    @RequestMapping(method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = SubscriberBulkUpdation.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public ResponseDTO<?> deleteFileFromS3Bucket(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
    @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestParam("fileId") String fileId,
            HttpServletRequest httpServletRequest) throws Exception {
        manageSubscriberInfoService.deleteFileFromS3Bucket(fileId);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


//    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ALL_FAILED_RECORDS_FOR_FILE)
    @RequestMapping(value = "/failed/records", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SubscriberBulkUpdation.class)
    public ResponseDTO<?> getAllFailedRecordsForFile(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
    @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestParam("fileId") String fileId,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberInfoService.getAllFailedRecordsForFile(fileId), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SUBSCRIBER_FAILED_RECORDS)
//    @Timed(value = AuditorConstants.SUBSCRIBER_FAILED_RECORDS)
    @RequestMapping(value = "/failed/records", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SubscriberBulkUpdation.class)
    public ResponseDTO<?> getAllFailedRecordsForFile(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
    @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody SubscriberInfoRequest subscriberInfoRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberInfoService.getSubscriberFailedRecords(subscriberInfoRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
