package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.aop.Auditor;
import com.incs83.app.authResources.Cluster;
import com.incs83.app.authResources.Common;
import com.incs83.app.business.v2.ManageClusterServices;
import com.incs83.app.common.v2.ClusterRequest;
import com.incs83.app.common.v2.EditCluster;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.cluster.ClusterDTO;
import com.incs83.app.responsedto.v2.clusters.ClusterSearch;
import com.incs83.app.responsedto.v2.clusters.ClustersDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@Api(value = "(V2) Cluster", description = "API's for Cluster Operations", tags = "Optim - (V2) Cluster")
@RequestMapping(value = "/actiontec/api/v2/clusters")
@EnableAspectJAutoProxy
@SuppressWarnings("rawtypes")
public class ClusterController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageClusterServices manageClusterServices;

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.CLUSTER_READ)
    @ApiOperation(value = "Get all Clusters", response = ClusterDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getAllClusters(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageClusterServices.getClusterInfoListForNetworkDashBoard(false).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @ApiOperation(value = "Get Only Default Clusters", response = ClusterDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
////    @Timed("tech_dashboard")
    @RequestMapping(value = "/default", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getAllDefaultClusters(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageClusterServices.getClusterInfoListForNetworkDashBoard(true).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @ApiOperation(value = "Get all Clusters", response = ClustersDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/all", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Cluster.class)
    public ResponseDTO<?> getAllClustersForUI(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageClusterServices.getClusterInfoList().getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    /*@ApiIgnore
    @ApiOperation(value = "Get Cluster by Id", response = ClustersDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Cluster.class)
    public ResponseDTO<?> getClustersById(
            @ApiParam(value = "Id of a cluster to be edited", required = true) @PathVariable(name = "id") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Max no of record.Numeric Value Allowed.") @RequestParam(name = "max", required = false, defaultValue = "10") Integer max,
            @ApiParam(value = "Offset, Numeric Value Allowed.") @RequestParam(name = "offset", required = false, defaultValue = "0") Integer offset,
            @ApiParam(value = "Display Only Selected Subscribers") @RequestParam(name = "selected", required = false) boolean selected,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageClusterServices.getSelectedSubscriberForCluster(id, max, offset, selected).getData(), ApiResponseCodeImpl.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    @ApiIgnore
    @ApiOperation(value = "Get Subscriber by Cluster Id", response = ClustersDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Cluster.class)
    public ResponseDTO<?> searchSubscriberByClustersId(
            @ApiParam(value = "Id of a cluster to be edited", required = true) @PathVariable(name = "id") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody ClusterSearch clusterSearch,
            @RequestParam(required = false, defaultValue = "false") Boolean exact,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageClusterServices.getEquipmentByCluster(id, clusterSearch,exact).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.CLUSTER_CREATE)
//    @Timed(value = AuditorConstants.CLUSTER_CREATE )
    @ApiOperation(value = "Create Clusters", response = ClusterDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Cluster.class)
    public void createCluster(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody @Valid ClusterRequest clusterRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        manageClusterServices.createCluster(clusterRequest);
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.CLUSTER_UPDATE)
//    @Timed(value = AuditorConstants.CLUSTER_UPDATE )
    @ApiOperation(value = "Edit cluster by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Cluster.class)
    public ResponseDTO<?> editCluster(
            @ApiParam(value = "Id of a cluster to be edited", required = true) @PathVariable(name = "id") String clusterId,
            @RequestBody EditCluster editCluster,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        manageClusterServices.updateClusterInfoForESMysql(clusterId, editCluster);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.CLUSTER_DELETE)
//    @Timed(value = AuditorConstants.CLUSTER_DELETE )
    @ApiOperation(value = "Delete a cluster by Id")
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Cluster.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteCluster(
            @ApiParam(value = "Id of a cluster to be deleted", required = true) @PathVariable(name = "id") String clusterId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        manageClusterServices.deleteClusterInfo(clusterId, false);
    }
}
