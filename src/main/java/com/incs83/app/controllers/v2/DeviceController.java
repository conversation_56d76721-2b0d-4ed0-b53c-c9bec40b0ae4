package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v2.DeviceInternetAccessService;
import com.incs83.app.business.v2.ManageDeviceService;
import com.incs83.app.common.v2.DeviceFriendlyNameRequest;
import com.incs83.app.common.v2.UpdateDeviceTypeRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.Device.*;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.incs83.app.constants.misc.ActiontecConstants.*;

/**
 * Created by Jayant Puri on 07-07-2017.
 */

@RestController
@Api(value = "(V2) Devices", description = "API'S for Device Operations", tags = {"Optim - (V2) Devices"})
@RequestMapping(value = "/actiontec/api/v2/equipments")
@SuppressWarnings("rawtypes")
public class DeviceController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageDeviceService manageDeviceService;

    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;

    @Autowired
    private DeviceInternetAccessService deviceInternetAccessService;

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_READ_FOR_SUBSCRIBER )
    @ApiOperation(value = "Get all stations for a Subscriber", response = DeviceListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDevices(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceList(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_DETAILS)
    @ApiOperation(value = "Get station details for a station MAC", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/detail", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.fetchDeviceDetailsByMacAddr(equipmentIdOrSerialOrSTN, macAddress).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_UTILIZATION_BY_MAC)
    @ApiOperation(value = "Get station utilization for a station MAC", response = DeviceUtilizationDTO.class)
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/internetUtilization", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceUtilization(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @RequestParam(required = false, name = "fromDSHost") Boolean fromDSHost,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.fetchDeviceUtilizationByMacAddr(equipmentIdOrSerialOrSTN, macAddress, duration, fromDSHost).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.DEVICE_DETAIL)
//    @Timed(value =  AuditorConstants.DEVICE_DETAIL )
    @ApiOperation(value = "Get RSSI Data over time for station MAC", response = DeviceRssiDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/rssi", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getRSSIData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,

            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.fetchRSSIData(equipmentIdOrSerialOrSTN, macAddress, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_WIFI_PHY_RATES_DATA)
    @ApiOperation(value = "Get Wifi Phy Rates Data over time for a station MAC", response = DeviceWifiPhyDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/wifiPhy", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getWifiPhyData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.fetchWifiPhyRateData(equipmentIdOrSerialOrSTN, macAddress, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //Telus Drop2 API
//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.TOP_5_BANDWIDTH_CONSUMING_DEVICES)
    @ApiOperation(value = "Get Top 10 Stations by consumed bandwidth over time for a Subscriber", response = DeviceBandWidthDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/bandwidth", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getTopTenBanwidthConsumingDevices(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes") @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Type of details requested [upstream, downstream].Default is upstream+downstream") @RequestParam(required = false, name = "type") String type,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getTopTenBandWidthConsumingDevices(equipmentIdOrSerialOrSTN, duration, type).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.TOP_10_INTERNET_BANDWIDTH_CONSUMING_DEVICES)
    @ApiOperation(value = "Get Top 10 Stations by consumed internet bandwidth over time for a Subscriber", response = DeviceBandWidthDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/internetBandwidth", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getTopTenInternetBanwidthConsumingDevices(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes") @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Type of details requested [upstream, downstream].Default is upstream+downstream") @RequestParam(required = false, name = "type") String type,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getTopTenInternetBandWidthConsumingDevices(equipmentIdOrSerialOrSTN, duration, type).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.TOP_5_INTERNET_TRAFFIC_CONSUMING_DEVICES)
    @ApiOperation(value = "Get Top 10 Consuming Internet Traffic By station for a Subscriber", response = DeviceWifiTrafficDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/internetTraffic", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getTopTenInternetTrafficConsumingDevices(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 15 minutes") @RequestParam(required = false, name = "duration", defaultValue = "15") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getInternetTrafficByDevice(equipmentIdOrSerialOrSTN, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.TOP_10_WIFI_TRAFFIC_CONSUMING_DEVICES)
    @ApiOperation(value = "Get Top 10 Consuming Wifi Traffic By station for a Subscriber", response = DeviceWifiTrafficDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/wifiTraffic", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getTopTenWifiTrafficConsumingDevices(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 15 minutes") @RequestParam(required = false, name = "duration", defaultValue = "15") Long duration,
            @ApiParam(value = "Type of details requested [upstream, downstream].Default is upstream+downstream") @RequestParam(required = false, name = "type", defaultValue = "") String type,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getWifiTrafficByDevice(equipmentIdOrSerialOrSTN, duration, type).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_ASSOCIATION_EVENTS)
    @ApiOperation(value = "Get Device Association Events over time for a station MAC", response = DeviceAssociationDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/assocEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceAssociationData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceSteeringEventsByMACAddress(equipmentIdOrSerialOrSTN, macAddress, duration, evAssociation).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_DISASSOCIATION_EVENTS)
    @ApiOperation(value = "Get Device Disassociation Events over time for a station MAC", response = DeviceDisAssociationDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/disAssocEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceDisAssociationData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceSteeringEventsByMACAddress(equipmentIdOrSerialOrSTN, macAddress, duration, evDisassociation).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_STEERING_EVENTS)
    @ApiOperation(value = "Get Device Steering Events over time for a station MAC", response = DeviceSteeringEventDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/steeringEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceSteeringData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceSteeringEventsByMACAddress(equipmentIdOrSerialOrSTN, macAddress, duration, steer).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get Device Diagnostic Events over time for a station MAC", response = DeviceSteeringEventDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/diagnosticLogs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceDiagnosticData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceSteeringEventsByMACAddress(equipmentIdOrSerialOrSTN, macAddress, duration, diagnostic).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_ROAM_EVENTS)
    @ApiOperation(value = "Get Device Roam Events over time for a station MAC", response = DeviceRoamEventDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/roamEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceRoamingData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceSteeringEventsByMACAddress(equipmentIdOrSerialOrSTN, macAddress, duration, roam).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_STEERING_LOGS)
    @ApiOperation(value = "Get Device Steering Logs over time for a station MAC", response = DeviceSteeringLogDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/steeringLogs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceEventLogData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.getDeviceSteeringEventsByMACAddress(equipmentIdOrSerialOrSTN, macAddress, duration, eventLog).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DEVICE_WIFI_THROUGHPUT_DATA)
    @ApiOperation(value = "Get Wifi Throughput Data over time for a station MAC", response = DeviceWifiThroughputDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/wifiThroughput", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getWifiThroughPutData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.fetchWifiThroughput(equipmentIdOrSerialOrSTN, macAddress, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.DEVICE_SPEED_TEST)
//    @Timed(value = AuditorConstants.DEVICE_SPEED_TEST )
    @ApiOperation(value = "Perform SpeedTest for Station", response = DeviceSpeedTestDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/speedTest", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> performStationSpeedTestForUserDevice(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device whose details are requested", required = true)
            @PathVariable(name = "macAddress") String macAddress,
            @ApiParam(value = "Serial Number of the Equipment to which device connected.", required = true) @RequestParam(name = "serialNo") @NotBlank String serialNo,
            @ApiParam(value = "IP Address of the Device", defaultValue = "") @RequestParam(name = "ipAddr") String ipAddress,
            @ApiParam(value = "Band of the Equipment to which the Station connected( 2G and 5G Band Allowed)", defaultValue = "") @RequestParam(name = "band") @NotBlank String band,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageDeviceService.performStationSpeedTestForUserDevice(equipmentIdOrSerialOrSTN, serialNo, macAddress, ipAddress, band, httpServletRequest).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.DEVICE_FRIENDLY_NAME)
//    @Timed(value = AuditorConstants.DEVICE_FRIENDLY_NAME )
    @ApiOperation(value = "Update Friendly name for Device", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/friendlyName", method = RequestMethod.PATCH)
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> updateDeviceFriendlyName(@RequestBody @Valid DeviceFriendlyNameRequest friendlyNameRequest,
                                                   @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
                                                   @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                   @RequestHeader(name = "X-Authorization") String accessToken,
                                                   HttpServletRequest httpServletRequest) throws Exception {
        manageDeviceService.updateFriendlyNameForUserDevice(friendlyNameRequest, equipmentIdOrSerialOrSTN);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.DEVICE_RESET_INTERNET)
//    @Timed(value = AuditorConstants.DEVICE_RESET_INTERNET)
    @ApiOperation(value = "Reset Internet for Device", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/reset", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public CompletableFuture<ResponseDTO<?>> disconnectInternet(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device", required = true) @PathVariable String macAddress,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> {
            manageDeviceService.invokeInternetRPCMethodsForDevice(equipmentIdOrSerialOrSTN, null, INTERNET_FORCE_RESET, macAddress, null, null, httpServletRequest);
            return responseUtil.ok(ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.DEVICE_UPDATE_TYPE_FOR_MAC)
//    @Timed(value = AuditorConstants.DEVICE_UPDATE_TYPE_FOR_MAC )
    @ApiOperation(value = "Update Device Type for a MAC", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/type", method = RequestMethod.PATCH)
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> updateDeviceType(@RequestBody @Valid UpdateDeviceTypeRequest updateDeviceTypeRequest,
                                           @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                           @RequestHeader(name = "X-Authorization") String accessToken,
                                           @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
                                           HttpServletRequest httpServletRequest) throws Exception {
        manageDeviceService.updateDeviceType(updateDeviceTypeRequest, equipmentIdOrSerialOrSTN);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.DEVICE_FORGET)
//    @Timed(value = AuditorConstants.DEVICE_FORGET )
    @ApiIgnore
    @ApiOperation(value = "Forget Device for a MAC", response = DevicePauseStateDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/forget", method = RequestMethod.PATCH)
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> forgetDevice(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device", required = true) @PathVariable String macAddress,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        manageDeviceService.forgetDevice(macAddress, true, equipmentIdOrSerialOrSTN);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.DEVICE_INTERNET_ACCESS)
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/internetAccess/action", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> postDeviceInternetAccess (
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)
            @PathVariable(name = "equipmentIdOrSerialOrSTN")
                    String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device", required = true)
            @PathVariable
                    String macAddress,
            @ApiParam(value = "Update Device Internet Access", required = true)
            @RequestBody
                    Map<String, Object> requestBodyMap,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
                    HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(
                deviceInternetAccessService.updateInternetAccess(equipmentIdOrSerialOrSTN, macAddress, requestBodyMap),
                ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/devices/{macAddress}/internetAccess", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDeviceInternetAccess(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true)
            @PathVariable(name = "equipmentIdOrSerialOrSTN")
                    String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "MAC Address of the device", required = true)
            @PathVariable
                    String macAddress,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization")
                    String accessToken,
                    HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(
                deviceInternetAccessService.readInternetAccess(equipmentIdOrSerialOrSTN, macAddress),
                ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
