package com.incs83.app.controllers.v2;/* sakshi created on 4/11/19 inside the package - com.incs83.app.controllers.v2 */

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.*;
import com.incs83.app.business.v2.NewManageSubscriberService;
import com.incs83.app.common.v2.RGWPaginationRequest;
import com.incs83.app.common.v2.SubscriberSearchDTO;
import com.incs83.app.common.v3.DetachedEquipmentDTO;
import com.incs83.app.common.v3.TechnicianDashboardDTO;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.Equipment.EditSubscriberRequest;
import com.incs83.app.responsedto.v2.Equipment.RGWDisconnectedDTO;
import com.incs83.app.responsedto.v2.Subscriber.CreateSubscriberRequestDTO;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberListDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@Api(value = "(V2) Subscriber Controller", description = "API's for Subscribers", tags = {"Optim - (V2) Subscribers"})
@RequestMapping(value = "/actiontec/api/v2/subscribers")
public class NewSubscriberController {
    @Autowired
    NewManageSubscriberService manageSubscriberService;
    @Autowired
    ResponseUtil responseUtil;

    @ApiOperation(value = "Create a Subscriber", response = ApiResponseDTO.class)
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SUBSCRIBER_CREATE)
//    @Timed(value = AuditorConstants.SUBSCRIBER_CREATE)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Subscribers.class)
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseDTO<?> createSubscriber(@RequestBody @Valid CreateSubscriberRequestDTO createSubscriberRequest,
                                 @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                 @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(manageSubscriberService.createSubscriber(createSubscriberRequest), ApiResponseCode.SUCCESS);
    }

    @ApiOperation(value = "Get all Subscribers", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/paginated", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Subscribers.class)
    public ResponseDTO<?> getAllEquipment(@RequestBody SubscriberSearchDTO subscriberSearchDTO,
                                          @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                          @RequestHeader(name = "X-Authorization") String accessToken,
                                          @RequestParam(required = false, defaultValue = "false") Boolean exact) throws Exception {
        return responseUtil.ok(manageSubscriberService.getAllSubscribers(subscriberSearchDTO,exact), ApiResponseCode.SUCCESS);
    }

    @ApiOperation(value = "Edit Subscriber", response = ApiResponseDTO.class)
    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SUBSCRIBER_UPDATE)
//    @Timed(value = AuditorConstants.SUBSCRIBER_UPDATE)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{subscriberIdOrGAN}", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = Subscribers.class)
    public ResponseDTO<?> editSubscriber(@RequestBody @Valid EditSubscriberRequest editSubscriberRequest,
                                         @PathVariable(name = "subscriberIdOrGAN") String subscriberIdOrGAN,
                                         @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                         @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        manageSubscriberService.editSubscriber(subscriberIdOrGAN, editSubscriberRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    /*@ApiOperation(value = "Get Subscriber By Id", response = SubscriberListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{subscriberId}", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public ResponseDTO<?> getEquipmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "subscriberId") String subscriberId,
            @RequestBody DetachedEquipmentDTO technicianDashboardDTO,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.getSubscriberById(technicianDashboardDTO,subscriberId), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    @ApiOperation(value = "Get Subscriber By Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{subscriberIdOrGAN}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getEquipmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "subscriberIdOrGAN") String subscriberIdOrGAN,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.getSubscriberById(subscriberIdOrGAN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Delete Subscriber by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{subscriberIdOrGAN}", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Subscribers.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SUBSCRIBER_DELETE)
//    @Timed(value = AuditorConstants.SUBSCRIBER_DELETE)
    public void deleteEquipmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Delete Subscriber by Id", required = true)
            @PathVariable(name = "subscriberIdOrGAN") String subscriberIdOrGAN) throws Exception {
        manageSubscriberService.deleteSubscriberById(subscriberIdOrGAN);
    }

    @ApiIgnore
    @RequestMapping(value = "/mqtt/status", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = MQTTConnectStatus.class)
    public ResponseDTO<?> getSubscriberStatus(
            @RequestParam(value = "rgwSerialNumber", required = true) String rgwSerialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return responseUtil.ok(manageSubscriberService.getSubscriberStatus(rgwSerialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.POST,operation = AuditorConstants.RGW_DISCONNECTED_LIST)
    @ApiOperation(value = "Get Disconnected RGW List", response = RGWDisconnectedDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/disconnected/rgw", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = DisconnectedRGW.class)
    public ResponseDTO<?> getDisconnectedRGWList(
            @RequestBody RGWPaginationRequest rgwPaginationRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberService.getRGWDisconnectedList(rgwPaginationRequest).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.SUBSCRIBERS_FILE_UPLOAD)
//    @Timed(value = AuditorConstants.SUBSCRIBERS_FILE_UPLOAD)
    @ApiOperation(value = "Update Bulk Subscriber Details", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/bulk", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = SubscriberBulkUpdation.class)
    public ResponseDTO<?> updateBulkSubscriberDetails(
            @RequestParam(name = "file") MultipartFile fileDetails,
            @RequestParam(name = "isp") String ispId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        manageSubscriberService.subscriberBulkFileUpload(fileDetails, ispId);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

}
