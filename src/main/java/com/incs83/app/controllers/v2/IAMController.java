package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.*;
import com.incs83.app.business.v2.IAMServices;
import com.incs83.app.common.v2.ChangePasswordRequest;
import com.incs83.app.common.v2.CreateUserRequestDTO;
import com.incs83.app.common.v2.UserRequestDTO;
import com.incs83.app.common.v3.UserCompositeSearchDTO;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.UsersDTO.UserListDTO;
import com.incs83.app.responsedto.v2.cds.DataSecurityRoleRequest;
import com.incs83.app.responsedto.v2.cds.RoleRequest;
import com.incs83.app.responsedto.v2.cds.UserDetailDTO;
import com.incs83.app.responsedto.v2.compartment.CompartmentDetailDTO;
import com.incs83.app.responsedto.v2.compartment.CompartmentModelDTO;
import com.incs83.app.responsedto.v2.compartment.GroupDTO;
import com.incs83.app.responsedto.v2.dataObscurity.DataObscurityDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.role.RoleListDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.request.CompartmentRequest;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@RestController
@Api(value = "(V2) Identity Access Management", description = "API's for Identity Access Management", tags = {"Optim - (V2) Identity Access Management"})
@RequestMapping(value = "/actiontec/api/v2")
public class IAMController {

    @Autowired
    private IAMServices iamServices;

    @Autowired
    private ResponseUtil responseUtil;

    private static final Logger LOG = LogManager.getLogger("org");

    //    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.USERS_LIST)
    @ApiOperation(value = "Get Users", response = UserListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/users/paginated", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = User.class)
    public ResponseDTO<?> getUserList(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody @Valid UserCompositeSearchDTO userCompositeSearchDTO,
            @RequestParam(required = false, defaultValue = "false") Boolean exact,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(iamServices.getUsersList(userCompositeSearchDTO, exact), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Create Users", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.USER_CREATE)
//    @Timed(value = AuditorConstants.USER_CREATE)
    @RequestMapping(value = "/users", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = User.class)
    @ResponseStatus(HttpStatus.CREATED)
    public void createUser(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest,
            @RequestBody @Valid CreateUserRequestDTO userRequest) throws Exception {
        iamServices.createUser(userRequest);
    }

    @ApiIgnore
    @RequestMapping(value = "/users/stats/count", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = User.class)
    public ResponseDTO<?> getUserCountDetails(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(iamServices.getUsersCountStats(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Delete User by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/users/{id}", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = User.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.USER_DELETE)
//    @Timed(value = AuditorConstants.USER_DELETE)
    public void deleteUserById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Delete User by Id", required = true)
            @PathVariable(name = "id") String userId) throws Exception {
        iamServices.deleteUserById(userId);
    }

    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.USER_BY_ID)
    @ApiOperation(value = "Fetch User by Id", response = UserDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/users/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = User.class)
    public ResponseDTO<?> fetchUserById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest,
            @ApiParam(value = "Get User by Id", required = true)
            @PathVariable(name = "id") String id) throws Exception {
        return responseUtil.ok(iamServices.getUserById(id).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Update User by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/users/{id}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = User.class)
    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.USER_UPDATE)
//    @Timed(value = AuditorConstants.USER_UPDATE)
    public ResponseDTO<?> updateUserById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "User id to be Updated", required = true)
            @PathVariable(name = "id") String userId,
            HttpServletRequest httpServletRequest,
            @RequestBody @Valid UserRequestDTO userRequest) throws Exception {
        iamServices.updateUserById(userId, userRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.GROUP_CREATE)
//    @Timed(value = AuditorConstants.GROUP_CREATE)
    @ApiOperation(value = "Create Compartment", response = CompartmentModelDTO.class)
    @RequestMapping(value = "/compartment", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Compartment.class)
    @ResponseStatus(HttpStatus.CREATED)
    public void createCompartment(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody @Valid CompartmentRequest compartment) throws Exception {
        iamServices.createCompartment(compartment);
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.GROUP_UPDATE)
//    @Timed(value = AuditorConstants.GROUP_UPDATE)
    @RequestMapping(value = "/compartment/{id}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Compartment.class)
    public ResponseDTO<?> updateCompartmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Compartment id to be Updated", required = true)
            @PathVariable(name = "id") String groupId,
            @RequestBody @Valid CompartmentRequest compartment) throws Exception {
        iamServices.updateCompartmentById(groupId, compartment);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.GROUPS_READ)
    @ApiOperation(value = "Get All Compartments", response = GroupDTO.class)
    @RequestMapping(value = "/compartment", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> fetchAllCompartment(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return iamServices.getAllCompartment();
    }

    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.GROUP_DETAILS_BY_ID)
    @ApiOperation(value = "Get Detail Compartment Details by Id", response = CompartmentDetailDTO.class)
    @RequestMapping(value = "/compartment/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Compartment.class)
    public ResponseDTO<?> fetchCompartmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Compartment id to be fetch for Info", required = true)
            @PathVariable(name = "id") String id) throws Exception {
        return responseUtil.ok(iamServices.getCompartmentById(id), ApiResponseCode.SUCCESS);
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.GROUP_DELETE)
//    @Timed(value = AuditorConstants.GROUP_DELETE)
    @ApiOperation(value = "Delete Compartment Details by Id")
    @RequestMapping(value = "/compartment/{id}", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Compartment.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public void deleteCompartmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Compartment id to be deleted", required = true)
            @PathVariable(name = "id") String groupId) throws Exception {
        iamServices.deleteCompartmentById(groupId);
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.ROLE_CREATE)
//    @Timed(value = AuditorConstants.ROLE_CREATE)
    @ApiOperation(value = "Create Role", response = ApiResponseDTO.class)
    @RequestMapping(value = "/role", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Role.class)
    @ResponseStatus(HttpStatus.CREATED)
    public void createRole(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody @Valid RoleRequest roleRequest) throws Exception {
        iamServices.createRole(roleRequest);
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.ROLE_UPDATE)
//    @Timed(value = AuditorConstants.ROLE_UPDATE)
    @ApiOperation(value = "Update Role Detail by Id", response = ApiResponseDTO.class)
    @RequestMapping(value = "/role/{id}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Role.class)
    public ResponseDTO<?> updateRoleById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "RoleRequest id to be fetch for Info", required = true)
            @PathVariable(name = "id") String roleId,
            @RequestBody @Valid RoleRequest roleRequest) throws Exception {
        iamServices.updateRoleById(roleId, roleRequest, false);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.UPDATE_DATA_OBSCURITY)
//    @Timed(value = AuditorConstants.UPDATE_DATA_OBSCURITY)
    @RequestMapping(value = "/dataObscurity/{id}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Role.class)
    public ResponseDTO<?> updateDataSecurityRoleById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "RoleRequest id to be fetch for Info", required = true)
            @PathVariable(name = "id") String dataObscurityId,
            @RequestBody DataSecurityRoleRequest roleRequest) throws Exception {
        iamServices.updateDataSecurityForRole(dataObscurityId, roleRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ROLE_READ)
    @ApiOperation(value = "Get All Roles", response = RoleListDTO.class)
    @RequestMapping(value = "/role", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> fetchAllRole(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(iamServices.getAllRole(), ApiResponseCode.SUCCESS);
    }

    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ROLE_BY_ID)
    @ApiOperation(value = "Get Role Details by Id", response = RoleListDTO.class)
    @RequestMapping(value = "/role/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Role.class)
    public ResponseDTO<?> fetchRoleById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "RoleRequest id to be fetch for Info", required = true)
            @PathVariable(name = "id") String id) throws Exception {
        return responseUtil.ok(iamServices.getRoleById(id), ApiResponseCode.SUCCESS);
    }

    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.DATA_SECURITY_MAPPING_FOR_ALL_ROLES)
    @ApiOperation(value = "Get Data Obscurity", response = DataObscurityDTO.class)
    @RequestMapping(value = "/dataObscurity", method = RequestMethod.GET)
    public ResponseDTO<?> fetchDataSecurityRoleById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(iamServices.getDataSecurityMappingForAllRoles(), ApiResponseCode.SUCCESS);
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.ROLE_DELETE)
//    @Timed(value = AuditorConstants.ROLE_DELETE)
    @ApiOperation(value = "Delete Role by Id")
    @RequestMapping(value = "/role/{id}", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Role.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public ResponseDTO<?> deleteRoleById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "RoleRequest id to be deleted", required = true)
            @PathVariable(name = "id") String roleId) throws Exception {
        iamServices.deleteRoleById(roleId);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    //    @Auditable(method = RequestMethod.DELETE)
    @ApiIgnore
    @RequestMapping(value = "/role/entities/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Role.class)
    public ResponseDTO<?> getAllEntityForRole(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "RoleRequest id to be deleted", required = true)
            @PathVariable(name = "id") String id) throws Exception {
        return responseUtil.ok(iamServices.getAllEntityForRole(id), ApiResponseCode.SUCCESS);
    }

    @ApiIgnore
    @RequestMapping(value = "/permission", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Role.class)
    public ResponseDTO<?> getPermissions(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(iamServices.getPermissions(), ApiResponseCode.SUCCESS);
    }

    @ApiIgnore
    @RequestMapping(value = "/entitlement/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Role.class)
    public ResponseDTO<?> getEntitlement(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "id") String id) throws Exception {
        return responseUtil.ok(iamServices.getEntitlement(id), ApiResponseCode.SUCCESS);
    }



    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.CHANGE_USER_PASSWORD)
//    @Timed(value = AuditorConstants.CHANGE_USER_PASSWORD)
    @ApiIgnore
    @RequestMapping(value = "/users/changePassword", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public ResponseDTO<?> changeCredentials(
            HttpServletRequest httpServletRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody ChangePasswordRequest changePasswordRequest)
            throws Exception {
        iamServices.changePassword(changePasswordRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.UPLOAD_LOGO)
//    @Timed(value = AuditorConstants.UPLOAD_LOGO )
    @ApiOperation(value = "Upload Logo")
    @RequestMapping(value = "/logo/{type}", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Logo.class)
    public ResponseDTO<?> uploadLogo(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "type") String type,
            @RequestParam(name = "logo") MultipartFile logo) throws Exception {
        return responseUtil.ok(iamServices.uploadLogo(logo, type), ApiResponseCode.SUCCESS);
    }

}
