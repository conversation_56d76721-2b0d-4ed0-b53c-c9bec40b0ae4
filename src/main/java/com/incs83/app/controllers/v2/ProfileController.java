//package com.incs83.app.controllers.v2;
//
//import com.incs83.abstraction.ApiResponseCode;
//import com.incs83.annotation.PreHandle;
//import com.incs83.app.annotation.Auditable;
//import com.incs83.app.authResources.TechnicianDashboard;
//import com.incs83.app.business.v2.ManageProfileService;
//import com.incs83.app.common.v2.CreateUserProfileRequest;
//import com.incs83.app.common.v2.EditUserProfileRequest;
//import com.incs83.app.constants.misc.AuditorConstants;
//import com.incs83.app.responsedto.v2.Profile.ProfileListDTO;
//import com.incs83.app.responsedto.v2.Profile.SubscriberProfileDTO;
//import com.incs83.app.responsedto.v2.Profile.SubscriberProfileListDTO;
//import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
//import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
//import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
//import com.incs83.dto.ApiResponseDTO;
//import com.incs83.dto.ResponseDTO;
//import com.incs83.util.CommonUtils;
//import com.incs83.util.ResponseUtil;
//import io.swagger.annotations.*;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.validation.Valid;
//
///**
// * Created by Jayant Puri on 07-07-2017.
// */
//
//@RestController("v3.ProfileController")
//@Api(value = "(V2) Parental and Profile Controls", description = "API's for Parental & Profile Controls",tags = {"Optim - (V2) Parental and Profile Controls"})
//@RequestMapping(value = "/actiontec/api/v2/subscribers")
//@SuppressWarnings("rawtypes")
//public class ProfileController {
//
//    @Autowired
//    private ManageProfileService manageProfileService;
//
//    @Autowired
//    private ResponseUtil responseUtil;
//
//    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SUBSCRIBER_PROFILE_CREATE)
//    @ApiOperation(value = "Create a Profile for a subscriber", response = SubscriberProfileDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/{serialNumberOrRGWMAC}/profiles", method = RequestMethod.POST, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
//    @ResponseStatus(HttpStatus.CREATED)
//    public ResponseDTO<?> createProfile(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @RequestBody @Valid CreateUserProfileRequest profileRequest,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageProfileService.createUserProfile(serialNumberOrRGWMAC, profileRequest).getData());
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_PROFILES_READ)
//    @ApiOperation(value = "Fetch All Profiles for a subscriber", response = SubscriberProfileListDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/{serialNumberOrRGWMAC}/profiles", method = RequestMethod.GET, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getProfiles(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageProfileService.fetchAllUserProfiles(serialNumberOrRGWMAC).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.PROFILE_DETAILS_BY_ID)
//    @ApiOperation(value = "Get Profile Details by Id", response = SubscriberProfileDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/profiles/{id}", method = RequestMethod.GET, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getProfileDetails(
//            @ApiParam(value = "Id of a profile whose details need to be fetched", required = true) @PathVariable(name = "id") String id,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageProfileService.fetchUserProfileById(id).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
//    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SUBSCRIBER_PROFILE_DELETE)
//    @ApiOperation(value = "Delete Profile by Id")
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/profiles/{id}", method = RequestMethod.DELETE)
//    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = TechnicianDashboard.class)
//    @ResponseStatus(value = HttpStatus.NO_CONTENT)
//    public void deleteProfile(
//            @ApiParam(value = "Id of a profile to be deleted", required = true) @PathVariable(name = "id") String profileId,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
//        manageProfileService.deleteProfileById(profileId);
//    }
//
//    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SUBSCRIBER_PROFILE_PAUSE_UNPAUSE_FOR_DURATION)
//    @ApiOperation(value = "Pause/Un-pause Profile Fox X minutes By Id", response = ApiResponseDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/profiles/timed/{id}", method = RequestMethod.PATCH, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> pauseForXMinsByProfileId(
//            @ApiParam(value = "Id of a profile to be Paused", required = true) @PathVariable(name = "id") String profileId,
//            @ApiParam(value = "Duration (in minutes) for pause/unpause", required = true) @RequestParam(name = "duration") long duration,
//            @ApiParam(value = "Action - pause or unpause", required = true) @RequestParam(name = "action") String action,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        manageProfileService.pauseUnpauseForXMins(duration, action, profileId, false);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
//
//    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SUBSCRIBER_PROFILE_PAUSE_UNPAUSE)
//    @ApiOperation(value = "Pause/Un-pause Profile NOW By Id", response = ApiResponseDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/profiles/now/{id}", method = RequestMethod.PATCH, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> pauseNowByProfileId(
//            @ApiParam(value = "Id of a profile to be Paused", required = true) @PathVariable(name = "id") String profileId,
//            @ApiParam(value = "Action - pause or unpause", required = true) @RequestParam(name = "action") String action,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageProfileService.pauseUnpauseNow(action, profileId), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
//    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.SUBSCRIBER_PROFILE_UPDATE)
//    @ApiOperation(value = "Edit Profile (Name, Devices, Scheduling Activated, Scheduling Details)", response = ApiResponseDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/profiles/{id}", method = RequestMethod.PUT, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> editProfile(
//            @ApiParam(value = "Id of a profile to be edited", required = true) @PathVariable(name = "id") String profileId, @Valid @RequestBody EditUserProfileRequest editUserProfileRequest,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        manageProfileService.editProfileForUser(profileId, editUserProfileRequest);
//        return responseUtil.ok(ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_TOP_5_PROFILES_IN_ONE_HOUR)
//    @ApiOperation(value = "Get Top Five Profiles over duration of 1 hour on Home Network for a Subscriber", response = ProfileListDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/{serialNumberOrRGWMAC}/topProfiles", method = RequestMethod.GET, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> fetchProfiles(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes") @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageProfileService.getTopFiveProfiles(serialNumberOrRGWMAC, duration).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
//}
