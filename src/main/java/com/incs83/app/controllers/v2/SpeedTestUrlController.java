package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.SpeedTestUrl;
import com.incs83.app.business.v2.ManageSpeedTestUrlServices;
import com.incs83.app.common.v2.WanSpeedTestRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import com.incs83.util.ValidationUtils;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@ApiIgnore
@RestController
@Api(value = "(V2) Speed Test URL", description = "API's for Speed Test URL")
@RequestMapping(value = "/actiontec/api/v2/speedTest")
@SuppressWarnings("rawtypes")
public class SpeedTestUrlController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ValidationUtils validationUtils;

    @Autowired
    private ManageSpeedTestUrlServices manageSpeedTestUrlServices;

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SPEED_TEST_URL_CREATE)
//    @Timed(value = AuditorConstants.SPEED_TEST_URL_CREATE)
    @ApiOperation(value = "Create Speed Test URL", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SpeedTestUrl.class)
    public ResponseDTO<?> createWanSpeedTest(
            @RequestBody @Valid WanSpeedTestRequest wanSpeedTestRequest, HttpServletRequest httpServletRequest) {
        return responseUtil.ok(manageSpeedTestUrlServices.createWanSpeedTest(wanSpeedTestRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.SPEED_TEST_URL_UPDATE)
//    @Timed(value = AuditorConstants.SPEED_TEST_URL_UPDATE)
    @ApiOperation(value = "Edit Speed Test URL by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = SpeedTestUrl.class)
    public ResponseDTO<?> editWanSpeedTest(
            @ApiParam(value = "Speed Test URL Id", required = true) @PathVariable(name = "id") String speedTestURLId,
            @RequestBody @Valid WanSpeedTestRequest wanSpeedTestRequest, HttpServletRequest httpServletRequest) {
        manageSpeedTestUrlServices.editWanSpeedTest(wanSpeedTestRequest, speedTestURLId);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SPEED_TEST_URL_READ)
    @ApiOperation(value = "Get All Speed Test URL's", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SpeedTestUrl.class)
    public ResponseDTO<?> getAllWanSpeedTest(HttpServletRequest httpServletRequest) {
        return responseUtil.ok(manageSpeedTestUrlServices.getWanSpeedTest(null), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SPEED_TEST_URL_BY_ID)
    @ApiOperation(value = "Get Speed Test URL by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SpeedTestUrl.class)
    public ResponseDTO<?> getWanSpeedTest(
            @ApiParam(value = "SpeedTest URL Id", required = true) @PathVariable(name = "id") String id, HttpServletRequest httpServletRequest) {
        return responseUtil.ok(manageSpeedTestUrlServices.getWanSpeedTest(id), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SPEED_TEST_URL_DELETE)
//    @Timed(value = AuditorConstants.SPEED_TEST_URL_DELETE)
    @ApiOperation(value = "Delete Speed Test URL by id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = SpeedTestUrl.class)
    public ResponseDTO<?> deleteWanSpeedTest(
            @ApiParam(value = "Speed Test URL Id", required = true) @PathVariable(name = "id") String speedTestURLId) throws Exception {
        manageSpeedTestUrlServices.deleteWanSpeedTest(speedTestURLId);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }
}
