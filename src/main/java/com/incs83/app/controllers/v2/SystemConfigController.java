package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.Configuration;
import com.incs83.app.business.v2.ConfigPropertyService;
import com.incs83.app.business.v2.SystemConfigService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.System.ConfigPropertyDTO;
import com.incs83.app.responsedto.v2.System.SystemConfigDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.request.ConfigPropertyRequest;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

@ApiIgnore
@RestController
@RequestMapping(value = "/actiontec/api/v2/configurations")
public class SystemConfigController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private ConfigPropertyService configPropertyService;

    /*@Auditable(method = RequestMethod.POST,operation = AuditorConstants.SYSTEM_CONFIG_CREATE)
    @ApiOperation(value = "Create a System config", response = SystemConfigDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @SuppressWarnings("unchecked")
    @RequestMapping(method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = System.class)
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseDTO<?> createConfig(@RequestBody @Valid SystemConfigRequest systemConfigRequest,
                                       @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                       @RequestHeader(name = "X-Authorization") String accessToken,
                                       HttpServletRequest httpServletRequest) throws Exception{
        return responseUtil.ok(systemConfigService.createSystemConfig(systemConfigRequest), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    /*@ApiOperation(value = "Get single System config", response = SystemConfigDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getConfigById(@ApiParam(value = "System configuration id", required = true)
                                        @PathVariable(name = "id") String id,
                                        @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                        @RequestHeader(name = "X-Authorization") String accessToken,
                                        HttpServletRequest httpServletRequest) throws Exception{
        return responseUtil.ok(systemConfigService.getSystemConfigById(id), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SYSTEM_CONFIG_READ)
    @ApiOperation(value = "Get all System configuration", response = SystemConfigDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @SuppressWarnings("unchecked")
    @RequestMapping(method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Configuration.class)
    public ResponseDTO<?> getAllConfig(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                       @RequestHeader(name = "X-Authorization") String accessToken,
                                       HttpServletRequest httpServletRequest) throws Exception {
        List<SystemConfigDTO> systemConfigDTO = systemConfigService.getAllSystemConfig();

        return responseUtil.ok(systemConfigDTO, ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    /*@Auditable(method = RequestMethod.PUT,operation = AuditorConstants.SYSTEM_CONFIG_UPDATE)
    @ApiOperation(value = "Update System config", response = SystemConfigDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = System.class)
    public ResponseDTO<?> updateConfig(@ApiParam(value = "System configuration id", required = true)
                                       @PathVariable(name = "id") String id,
                                       @RequestBody @Valid SystemConfigRequest systemConfigRequest,
                                       @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                       @RequestHeader(name = "X-Authorization") String accessToken,
                                       HttpServletRequest httpServletRequest) throws Exception{
        return responseUtil.ok(systemConfigService.updateSystemConfig(systemConfigRequest, id), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    /*@ApiOperation(value = "Get single config property", response = ConfigPropertyDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/properties/{configPropertyId}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getConfigPropertyById(@ApiParam(value = "Configuration property id", required = true)
                                                @PathVariable(name = "configPropertyId") String configPropertyId,
                                                @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                @RequestHeader(name = "X-Authorization") String accessToken,
                                                HttpServletRequest httpServletRequest) throws Exception{
        return responseUtil.ok(configPropertyService.getConfigPropertyById(configPropertyId), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    /*@ApiOperation(value = "Get all config properties", response = ConfigPropertyDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/{id}/properties", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getAllConfigProperties(@PathVariable(name = "id", required = true) String id,
                                                 @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                 @RequestHeader(name = "X-Authorization") String accessToken,
                                                 HttpServletRequest httpServletRequest) throws Exception{
        return responseUtil.ok(configPropertyService.getAllConfigProperty(), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.CONFIG_PROPERTY_UPDATE)
//    @Timed(value = AuditorConstants.CONFIG_PROPERTY_UPDATE)
    @ApiOperation(value = "Update config property", response = ConfigPropertyDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @SuppressWarnings("unchecked")
    @RequestMapping(value = "/properties/{configPropertyId}", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Configuration.class)
    public ResponseDTO<?> updateConfigProperty(@ApiParam(value = "Configuration property id", required = true)
                                               @PathVariable(name = "configPropertyId") String configPropertyId,
                                               @RequestBody @Valid ConfigPropertyRequest configPropertyRequest,
                                               @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                               @RequestHeader(name = "X-Authorization") String accessToken,
                                               HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(configPropertyService.updateConfigProperty(configPropertyRequest, configPropertyId), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
