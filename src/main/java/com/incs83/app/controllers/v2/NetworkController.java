package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v2.ManageNetworkService;
import com.incs83.app.common.v2.ChangeNetworkCredsRequest;
import com.incs83.app.common.v2.ChangeNetworkState;
import com.incs83.app.responsedto.v2.Network.NetworkListDTO;
import com.incs83.app.responsedto.v2.Network.TopologyDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.concurrent.CompletableFuture;

/**
 * Created by Jayant Puri on 07-07-2017.
 */

@RestController
@Api(value = "(V2) Home Network", description = "API's for Home Network Operations", tags = {"Optim - (V2) Home Network"})
@RequestMapping(value = "/actiontec/api/v2/equipments")
@SuppressWarnings("rawtypes")
public class NetworkController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageNetworkService manageNetworkService;

    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;

//    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.NETWORK_CHANGE_UPDATE)
    @ApiOperation(value = "Change Network(SSID) State (Enable/Disable - Guest/Primary (2.4G/5G/Both))", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/networks/state/change", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public CompletableFuture<ResponseDTO<?>> changeNetworkState(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @Valid @RequestBody ChangeNetworkState changeNetworkState,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> {
            manageNetworkService.changeNetworkState(changeNetworkState, equipmentIdOrSerialOrSTN, httpServletRequest);
            return responseUtil.ok(ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.NETWORK_SSID_LIST_OF_SUBSCRIBER)
    @ApiOperation(value = "Get networks(SSID) list for a Subscriber", response = NetworkListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/networks", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchNetworksList(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkService.getWifiNetworksList(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.NETWORK_CREDS_UPDATE)
//    @Timed(value =  AuditorConstants.NETWORK_CREDS_UPDATE)
    @ApiOperation(value = "Modify Network(SSID) Creds for Guest/Primary (2.4G/5G/Both)", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 503, message = "Feature is not supported by extender"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/networks/creds/change", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public CompletableFuture<ResponseDTO<?>> modifyNetworkCreds(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @RequestBody @Valid ChangeNetworkCredsRequest changeNetworkCredsRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> {
            manageNetworkService.modifyWifi(changeNetworkCredsRequest, equipmentIdOrSerialOrSTN, httpServletRequest);
            return responseUtil.ok(ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.NETWORK_TOPOLOGY_OF_SUBSCRIBER)
    @ApiOperation(value = "Get Home Network Topology for a Subscriber", response = TopologyDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/topology", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchAPDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkService.getTopology(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

}
