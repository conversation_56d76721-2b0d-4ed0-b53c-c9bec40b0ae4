package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.business.v2.ManageLoginService;
import com.incs83.app.common.v2.LoginRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.LoginResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import springfox.documentation.annotations.ApiIgnore;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * Created by hari on 1/2/18.
 */
@RestController
@Api(value = "(V2) Authentication", description = "API's for Authentication",tags = {"Optim - (V2) Authentication"})
@RequestMapping(value = "/actiontec/api/v2")
public class LoginController {
    private static final Logger LOG = LogManager.getLogger("org");
    @Autowired
    private ManageLoginService manageLoginService;

    @Autowired
    private ResponseUtil responseUtil;

    @ApiOperation(value = "Auth Login", response = LoginResponseDTO.class, httpMethod = "POST")
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
    })

    @RequestMapping(value = "/login", method = RequestMethod.POST, produces = "application/json")
    @Auditable(operation = AuditorConstants.USER_LOGIN, method = RequestMethod.POST)
//    @Timed(value =  AuditorConstants.USER_LOGIN)
            public ResponseDTO<?> authLogin(@RequestBody @Valid LoginRequest loginRequest, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


//     @ApiOperation(value = "Auth Logout", response = ApiResponseDTO.class, httpMethod = "DELETE")
//     @ApiResponses(value = {
//             @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//             @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//             @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//     })
    @ApiIgnore
    @RequestMapping(value = "/logout", method = RequestMethod.DELETE, produces = "application/json")
    @Auditable(operation = AuditorConstants.USER_LOGOUT, method = RequestMethod.POST)
//    @Timed(value = AuditorConstants.USER_LOGOUT )
            public ResponseDTO<?> authLogout(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                     @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        manageLoginService.doLogout();
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
