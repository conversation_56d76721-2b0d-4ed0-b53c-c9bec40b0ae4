//package com.incs83.app.controllers.v2;
//
//import com.incs83.abstraction.ApiResponseCode;
//import com.incs83.annotation.PreHandle;
//import com.incs83.app.annotation.Auditable;
//import com.incs83.app.authResources.TechnicianDashboard;
//import com.incs83.app.business.v2.ManageUserNetworkStatsServices;
//import com.incs83.app.constants.misc.AuditorConstants;
//import com.incs83.app.enums.ApiResponseCodeImpl;
//import com.incs83.app.responsedto.v2.NetworkStats.*;
//import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
//import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
//import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
//import com.incs83.dto.ResponseDTO;
//import com.incs83.util.CommonUtils;
//import com.incs83.util.ResponseUtil;
//import io.swagger.annotations.*;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//import springfox.documentation.annotations.ApiIgnore;
//
//import javax.servlet.http.HttpServletRequest;
//
//import static com.incs83.app.constants.misc.ActiontecConstants.*;
//
//@RestController
//@ApiIgnore
//@Api(value = "User Network Stats ", description = "REST API for User Network Stats")
//@RequestMapping(value = "/actiontec/api/v2/subscribers/{serialNumberOrSubscriberIdOrRGWMAC}/network/stats")
//public class UserNetWorkStatsController {
//    //TODO Mobile app suport
//
//    @Autowired
//    private ResponseUtil responseUtil;
//
//    @Autowired
//    private ManageUserNetworkStatsServices manageUserNetworkStatsServices;
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_UPLOAD_SPEED_DISTRIBUTION_HISTORICAL)
//    @ApiOperation(value = "Get wifi UploadSpeedDist historical Data of Network Dashboard for Global or cluster Network", response = CommonDistDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/upSpeedDist", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getUploadSpeedDistribution(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageUserNetworkStatsServices.getUploadSpeedDist(serialNumberOrRGWMAC).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_DOWNLOAD_SPEED_DISTRIBUTION_HISTORICAL)
//    @ApiOperation(value = "Get wifi DownloadSpeedDist historical Data of Network Dashboard for Global or cluster Network", response = CommonDistDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/dnSpeedDist", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getdownloadSpeedDistribution(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageUserNetworkStatsServices.getDownloadSpeedDist(serialNumberOrRGWMAC).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_HEALTH_DISTRIBUTION_HISTORICAL)
//    @ApiOperation(value = "Get wifiHealthDist historical Data of Network Dashboard for Global or cluster Network", response = HealthDistDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/healthDist", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getHealthDistribution(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageUserNetworkStatsServices.getHealthDist(serialNumberOrRGWMAC).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.AIRTIME_UTILIZATION_DISTRIBUTION_HISTORICAL)
//    @ApiOperation(value = "Get AirTimeUtlizationDistribution historical Data of Network Dashboard for Global or cluster Network", response = AirTimeUtlzDist.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/airTimeUtzDist", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getAirTimeUtlizationDistribution(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageUserNetworkStatsServices.getAirTimeUtilizationDist(serialNumberOrRGWMAC).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SIGNAL_STRENGTH_DISTRIBUTION_HISTORICAL)
//    @ApiOperation(value = "Get SignalStrengthDistribution historical Data of Network Dashboard for Global or cluster Network", response = SignalStrengthDistDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/signalStrengthDist", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getSignalStrengthDistribution(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(manageUserNetworkStatsServices.getSignalStrengthDist(serialNumberOrRGWMAC).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_HEALTH_HISTORICAL)
//    @ApiOperation(value = "Get wifiHealth historical Data of Network Dashboard for Global or cluster Network", response = HealthDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/wifiHealth", method = RequestMethod.GET, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> fetchNetworkWifiHealthGraphDataForGlobalOrCluster(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(((HealthDTO) manageUserNetworkStatsServices.getClumnChartData(serialNumberOrRGWMAC, wifiHealth)).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_DOWNLOAD_SPEED_HISTORICAL)
//    @ApiOperation(value = "Get wifi DownloadSpeed historical Data of Network Dashboard for Global or cluster Network", response = DlSpeedDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/wifiDlSpeed", method = RequestMethod.GET, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> fetchNetworkWifiDlSpeedGraphForGlobalOrCluster(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(((DlSpeedDTO) manageUserNetworkStatsServices.getClumnChartData(serialNumberOrRGWMAC, wifiDlSpeed)).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_UPLOAD_SPEED_HISTORICAL)
//    @ApiOperation(value = "Get wifi UploadSpeed historical Data of Network Dashboard for Global or cluster Network", response = UlSpeedDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/wifiUlSpeed", method = RequestMethod.GET, produces = "application/json")
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> fetchNetworkWifiUlSpeedGraphForGlobalOrCluster(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(((UlSpeedDTO) manageUserNetworkStatsServices.getClumnChartData(serialNumberOrRGWMAC, wifiUlSpeed)).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
////    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.AIRTIME_UTILIZATION_HISTORICAL)
//    @ApiOperation(value = "Get AirTimeUtlization historical Data of Network Dashboard for Global or cluster Network", response = AirTimeUtlzDTO.class)
//    @ApiResponses(value = {
//            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
//            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
//            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
//            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
//    })
//    @RequestMapping(value = "/historical/airTimeUtz", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    public ResponseDTO<?> getAirTimeUtlizationGraphData(
//            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "serialNumberOrRGWMAC") String serialNumberOrRGWMAC,
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(((AirTimeUtlzDTO) manageUserNetworkStatsServices.getClumnChartData(serialNumberOrRGWMAC, wifiAirTimeUtlz)).getData(), ApiResponseCode.SUCCESS,
//                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
//    }
//
//}
