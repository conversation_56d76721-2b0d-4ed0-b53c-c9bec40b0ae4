package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v2.ManageSubscriberNetworkStatsService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.Subscriber.*;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.CompletableFuture;

@RestController
@Api(value = "(V2) Subscriber Home Network Stats", description = "API's for Subscriber Home Network Stats",tags = {"Optim - (V2) Subscriber Home Network Stats"})
@RequestMapping(value = "/actiontec/api/v2/equipments")
public class SubscriberNetworkStatsController {
    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageSubscriberNetworkStatsService manageSubscriberNetworkStatsService;

    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORKS_WIFI_HEALTH_SCORE)
    @ApiOperation(value = "Get Networks Wifi Health Score for Subscribers", response = WifiMeterDataDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/wifiHealthScore", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiHealthScore(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getWifiHealthScore(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORKS_WIFI_DOWNLOAD_SPEED)
    @ApiOperation(value = "Get Networks Wifi Download Speed for Subscribers", response = WifiMeterDataDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/avgDownlinkRate", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiDownloadSpeed(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getAvgWifiDownlinkRate(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORKS_WIFI_UPLOAD_SPEED)
    @ApiOperation(value = "Get Networks WifiUpload Speed for Subscribers", response = WifiMeterDataDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/avgUplinkRate", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiUploadSpeed(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getSubscriberAvgUplinkRate(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORKS_INTERNET_DETAILS)
    @ApiOperation(value = "Get Networks Internet Details for Subscribers", response = InternetDetailDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/internet", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchNetworkInternetDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getInternetDetails(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORKS_INTERNET_UTILIZATION_GRAPH)
    @ApiOperation(value = "Get Networks Internet Utilization Graph Data for Subscribers", response = InternetUtilizationDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/internetUtilization", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchNetworkInternetUtilizationGraphData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration, Default value is 60") @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getInternetUtilizationLineChartData(equipmentIdOrSerialOrSTN, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Get Speed Test of Network for Subscribers", response = SpeedTestHistoryDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/speedTest", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
//    @Auditable(operation = AuditorConstants.EQUIPMENT_SPEED_TEST, method = RequestMethod.GET)
//    @Timed(value = AuditorConstants.EQUIPMENT_SPEED_TEST)
    public CompletableFuture<ResponseDTO<?>> performSpeedTestForUser(
//            public ResponseDTO<?> performSpeedTestForUser(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "performSpeedTest, If true, will perform a WAN Speed Test") @RequestParam(required = false) boolean performSpeedTest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> responseUtil.ok(manageSubscriberNetworkStatsService.performSpeedTestForUser(equipmentIdOrSerialOrSTN, performSpeedTest,httpServletRequest).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest)));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORK_WIFI_HEALTH)
    @ApiOperation(value = "Get Wifi Health of Network for Subscribers", response = WifiHealthHistoryDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/wifiHealthHistory", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchWifiHealth(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getWifiHealth(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SUBSCRIBER_NETWORK_WIFI_HEALTH_PER_MINUTE)
    @ApiOperation(value = "Get Wifi Health Per Minute Graph Data of Network for Subscribers", response = WifiHealthPerMinuteDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/stats/wifiHealthPerMinute", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> fetchWifiHealthPerMinute(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSubscriberNetworkStatsService.getWifiHealthPerMinute(equipmentIdOrSerialOrSTN).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
