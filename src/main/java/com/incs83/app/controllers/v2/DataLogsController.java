package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.MQTTPackets;
import com.incs83.app.business.v2.ManageDataLogsService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.clusters.ClustersDTO;
import com.incs83.app.responsedto.v2.dataLogs.DataLogDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController("v2.DataLogsController")
@Api(value = "(V2) Data Logs Controller", description = "API's for Data Logs Operations", tags = "Optim - (V2) Data Logs Controller")
@RequestMapping(value = "/actiontec/api/v2/dataLogs")
public class DataLogsController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageDataLogsService dataLogsService;

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.DATA_LOGS)

    @ApiOperation(value = "Get all Data Logs for a serial number", response = DataLogDTO.class)
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = MQTTPackets.class)
    public ResponseDTO<?> getDataLogs(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(required = true, value = "RGW SerialNumber")
            @RequestParam(name = "rgwSerialNumber") String rgwSerialNumber,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(dataLogsService.getDataLogs(rgwSerialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
