package com.incs83.app.controllers.v2;

import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.AuditEvent;
import com.incs83.app.business.v2.AuditLogService;
import com.incs83.app.common.v3.AuditLogRequest;
import com.incs83.app.enums.auditor.AuditorEnum;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.lang.reflect.Proxy;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ApiIgnore
@RestController
@Api(value = "AuditLog", description = "Api For Audit Logs")
@RequestMapping(value = "/actiontec/api/v2/auditLogs")
public class AuditLogController {

    @Autowired
    private AuditLogService auditLogService;

    @Autowired
    private ResponseUtil responseUtil;


    @ApiOperation(value = "Get Audit Logs", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = AuditEvent.class)
    @RequestMapping(method = RequestMethod.POST)
    public ResponseDTO<?> getAuditLogs(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                       @RequestHeader(name = "X-Authorization") String accessToken, @RequestBody AuditLogRequest auditLogRequest) throws Exception {
        return responseUtil.ok(auditLogService.getAuditLogs(auditLogRequest));
    }




    @ApiOperation(value = "Get Audit Operations", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = AuditEvent.class)
    @RequestMapping(value = "/operation", method = RequestMethod.GET)
    public ResponseDTO<?> getAuditOperationNames(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                 @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(Stream.of(AuditorEnum.values()).map(AuditorEnum::getValue).filter(q -> !q.contains("Get")).sorted().collect(Collectors.toList()));
    }

    @ApiOperation(value = "Export All Audit Logs", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = AuditEvent.class)
    @RequestMapping(value = "/exportAll", method = RequestMethod.GET)
    public ResponseDTO<?> exportAllAuditLogs(@ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                             @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(auditLogService.exportAllAuditLogs());
    }

}




