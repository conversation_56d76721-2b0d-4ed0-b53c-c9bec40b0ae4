package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Schedule;
import com.incs83.app.business.v2.ManageSpeedTestScheduleServices;
import com.incs83.app.common.v2.ScheduleRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import com.incs83.util.ValidationUtils;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@ApiIgnore
@RestController
@Api(value = "(V2) SpeedTest Schedule", description = "API's for SpeedTest Schedule")
@RequestMapping(value = "/actiontec/api/v2/speedTest/schedules")
@SuppressWarnings("rawtypes")
public class SpeedTestScheduleController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ValidationUtils validationUtils;

    @Autowired
    private ManageSpeedTestScheduleServices manageSpeedTestScheduleServices;

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SCHEDULE_CREATE)
//    @Timed(value = AuditorConstants.SCHEDULE_CREATE)
    @ApiOperation(value = "Create Speed Test Schedule", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @ResponseStatus(HttpStatus.CREATED)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Schedule.class)
    public ResponseDTO<?> createWanSpeedTest(
            @RequestBody @Valid ScheduleRequest scheduleRequest, HttpServletRequest httpServletRequest) throws Exception {
        //LOG.info("Request to create an AP WanSpeedTest Schedule...");
        return responseUtil.ok(manageSpeedTestScheduleServices.createSchedule(scheduleRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.SCHEDULE_UPDATE)
//    @Timed(value = AuditorConstants.SCHEDULE_UPDATE)
    @ApiOperation(value = "Edit Speed Test Schedule", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Schedule.class)
    public ResponseDTO<?> editWanSpeedTest(
            @ApiParam(value = "Edit Speed Test Schedule by Id", required = true) @PathVariable(name = "id") String scheduleId,
            @RequestBody @Valid ScheduleRequest scheduleRequest, HttpServletRequest httpServletRequest) throws Exception {
        manageSpeedTestScheduleServices.editSchedule(scheduleRequest, scheduleId);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SCHEDULES_READ)
    @ApiOperation(value = "Get all Speed Test Schedules", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Schedule.class)
    public ResponseDTO<?> getAllWanSpeedTest(HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSpeedTestScheduleServices.getSchedules(null), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SCHEDULE_BY_ID)
    @ApiOperation(value = "Get Speed Test Schedule by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Schedule.class)
    public ResponseDTO<?> getWanSpeedTest(
            @ApiParam(value = "Fetch SpeedTest URL over id", required = true) @PathVariable(name = "id") String id, HttpServletRequest httpServletRequest) throws Exception {
        //LOG.info("Request to Fetch WanSpeedTest by Id or All...");
        return responseUtil.ok(manageSpeedTestScheduleServices.getSchedules(id), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.SCHEDULE_DELETE)
//    @Timed(value = AuditorConstants.SCHEDULE_DELETE)
    @ApiOperation(value = "Delete Speed Test Schedule by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Schedule.class)
    public void deleteWanSpeedTest(
            @ApiParam(value = "Delete Speed Test Schedule by Id", required = true) @PathVariable(name = "id") String scheduleId, HttpServletRequest httpServletRequest) throws Exception {
        manageSpeedTestScheduleServices.deleteSchedule(scheduleId);
    }
}
