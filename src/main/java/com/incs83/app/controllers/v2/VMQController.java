package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.SystemHealth;
import com.incs83.app.business.v2.ManageVMQMetricsService;
import com.incs83.app.common.v2.VMQPaginationRequest;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberListDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;

@RestController
@Api(value = "(V2) VMQ Metrics", description = "REST API for VMQ Metrics", tags = {"Optim - (V2) VMQ Metrics"} )
@RequestMapping(value = "/actiontec/api/v2/vmq/metrics/paginated")
public class VMQController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageVMQMetricsService manageVMQMetricsService;

    @ApiOperation(value = "Get all VMQ Metrics", response = SubscriberListDTO.class)
    @ApiIgnore
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = SystemHealth.class)
    public ResponseDTO<?> getAllMetrics(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest,
            @RequestBody VMQPaginationRequest vmqPaginationRequest) throws Exception {

        return responseUtil.ok(manageVMQMetricsService.getAllMetrics(vmqPaginationRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }
}
