package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.business.v2.Oauth2Service;
import com.incs83.app.common.v2.OauthProviderConfig;
import com.incs83.app.common.v2.OauthRoleMappingConfig;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.entities.OauthConfig;
import com.incs83.app.responsedto.v2.oauth.OauthConfigResponse;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping(value = "/actiontec/api/v2/oauth2")
@ApiIgnore
public class Oauth2Controller {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private Oauth2Service oauth2Service;

    @RequestMapping(value = "/configs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = OauthConfig.class)
    public ResponseDTO<?> getOauthConfig(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(oauth2Service.getAllOauthConfigsResponse(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/configs/{id}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = OauthConfig.class)
    public ResponseDTO<?> getOauthConfig(
            @Valid @PathVariable(name = "id") String oauthConfigId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        ArrayList<OauthConfigResponse> resultList = new ArrayList<>();
        resultList.add(oauth2Service.getOauthConfigResponse(oauthConfigId));
        return responseUtil.ok(resultList, ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.OAUTH_CREATE)
//    @Timed(value = AuditorConstants.OAUTH_CREATE)
    @RequestMapping(value = "/configs", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = OauthConfig.class)
    public ResponseDTO<?> createOauthConfig(
            @Valid @RequestBody OauthProviderConfig oauthProviderConfig,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        oauth2Service.createOauthConfig(oauthProviderConfig);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.OAUTH_UPDATE)
//    @Timed(value = AuditorConstants.OAUTH_UPDATE)
    @RequestMapping(value = "/configs/{id}", method = RequestMethod.PUT, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = OauthConfig.class)
    public ResponseDTO<?> updateOauthConfig(
            @Valid @PathVariable(name = "id") String oauthConfigId,
            @Valid @RequestBody OauthProviderConfig oauthProviderConfig,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        oauth2Service.updateOauthConfig(oauthConfigId, oauthProviderConfig);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.OAUTH_DELETE)
//    @Timed(value =  AuditorConstants.OAUTH_DELETE)
    @RequestMapping(value = "/configs/{id}", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = OauthConfig.class)
    public ResponseDTO<?> deleteOauthConfig(
            @Valid @PathVariable(name = "id") String OauthConfigId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        oauth2Service.deleteOauthConfig(OauthConfigId);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.OAUTH_MAPPING_CREATE)
//    @Timed(value = AuditorConstants.OAUTH_MAPPING_CREATE)
    @RequestMapping(value = "/mapping", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = OauthConfig.class)
    public ResponseDTO<?> createOrEditOauthRoleMapping(
            @RequestBody List<OauthRoleMappingConfig> oAuthRoleMappingConfigList,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(oauth2Service.createEditOauthRoleMapping(oAuthRoleMappingConfigList), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @RequestMapping(value = "/mapping", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = OauthConfig.class)
    public ResponseDTO<?> getOauthRoleMapping(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(oauth2Service.getOauthRoleMapping(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Deprecated
    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.OAUTH_DELETE)
    @RequestMapping(value = "/config", method = RequestMethod.DELETE, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = OauthConfig.class)
    public ResponseDTO<?> deleteDefaultOauthConfig(
            @Valid @PathVariable(name = "id") String OauthConfigId,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        oauth2Service.deleteDefaultOauthConfig();
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Deprecated
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.OAUTH_CREATE)
    @RequestMapping(value = "/config", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = OauthConfig.class)
    public ResponseDTO<?> updateDefaultOauthConfig(
            @Valid @RequestBody OauthProviderConfig oauthProviderConfig,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        oauth2Service.updateDefaultOauthConfig(oauthProviderConfig);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}