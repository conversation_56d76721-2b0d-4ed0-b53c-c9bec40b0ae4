package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.Common;
import com.incs83.app.authResources.EquipmentLookup;
import com.incs83.app.authResources.Subscribers;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v2.ManageEquipmentService;
import com.incs83.app.business.v2.ManageNetworkService;
import com.incs83.app.business.v2.NewManageEquipmentService;
import com.incs83.app.business.v2.SmartSteeringService;
import com.incs83.app.common.v2.EquipmentFriendlyNameRequest;
import com.incs83.app.common.v2.RadioRequestDTO;
import com.incs83.app.common.v3.DetachedEquipmentDTO;
import com.incs83.app.common.v3.NeighborScanRequestDTO;
import com.incs83.app.common.v3.TechnicianDashboardDTO;
import com.incs83.app.constants.misc.ActiontecConstants;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.Equipment.AttachEquipmentRequest;
import com.incs83.app.responsedto.v2.Equipment.EditEquipmentRequest;
import com.incs83.app.responsedto.v2.Equipment.EquipmentBusyByDeviceDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentChannelChangeEventDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentChannelSelectionDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentCommonDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentDetailsDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentEthernetDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentListDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentMoCADTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentMoCALanDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentModel;
import com.incs83.app.responsedto.v2.Equipment.EquipmentNeighborScanDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentSteeringDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentWANDTO;
import com.incs83.app.responsedto.v2.Equipment.EquipmentWirelessDTO;
import com.incs83.app.responsedto.v2.Subscriber.SubscriberListDTO;
import com.incs83.app.responsedto.v2.fSecure.TimeZoneResponse;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.app.utils.ManageLongWaitAPI;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.incs83.app.constants.misc.ActiontecConstants.*;

/**
 * Created by Jayant Puri on 07-07-2017.
 */

@RestController("v2.EquipmentController")
@Api(value = "(V2) Equipment", description = "API's for Equipment Operations", tags = {"Optim - (V2) Equipment"})
@RequestMapping(value = "/actiontec/api/v2/equipments")
@SuppressWarnings("rawtypes")
public class EquipmentController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageEquipmentService manageEquipmentService;

    @Autowired
    private NewManageEquipmentService newManageEquipmentService;

    @Autowired
    private SmartSteeringService smartSteeringService;

    @Autowired
    private ManageLongWaitAPI manageLongWaitAPI;

    @Autowired
    private ManageNetworkService manageNetworkService;

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENTS_FOR_SUBSCRIBER)
    @ApiOperation(value = "Get All Equipments for a subscriber", response = EquipmentListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getAllEquipments(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getAllEquipmentsForSubscriber(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_DETAILS_BY_SNO)
    @ApiOperation(value = "Get Equipment Details for an Equipment by Serial Number", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, all).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_SYSTEM_DETAILS)
    @ApiOperation(value = "Get Equipment System Details by Serial Number", response = EquipmentDetailsDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/systemInfo/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentSystemDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, system).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_WAN_DETAILS)
    @ApiOperation(value = "Get Equipment WAN Details by Serial Number", response = EquipmentWANDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/wan/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentWanDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, wan).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_MOCA_DETAILS)
    @ApiOperation(value = "Get Equipment MoCA Details by Serial Number", response = EquipmentMoCADTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/MoCA/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentMocaDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, moca).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_MOCA_LAN_INFO)
    @ApiOperation(value = "Get Equipment MoCA LAN Info Details for Subscriber by Serial Number", response = EquipmentMoCALanDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/MoCALAN/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentMocaInfoDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, mocaInfo).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_ETHERNET_DETAILS)
    @ApiOperation(value = "Get Equipment Ethernet Details for Subscriber by Serial Number", response = EquipmentEthernetDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/ethernet/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentethernetDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, ethernet).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_ASSOCIATION_EVENTS)
    @ApiOperation(value = "Get Equipment Association Events over time by Equipment MAC", response = EquipmentSteeringDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/assocEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentAssociationData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getSteeringDetailsForSubscriber(equipmentIdOrSerialOrSTN, duration, evAssociation).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_DISASSOCIATION_EVENTS)
    @ApiOperation(value = "Get Equipment Disassociation Events over time by Equipment MAC", response = EquipmentSteeringDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/disassocEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentDisAssociationData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getSteeringDetailsForSubscriber(equipmentIdOrSerialOrSTN, duration, evDisassociation).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_STEERING_EVENTS)
    @ApiOperation(value = "Get Equipment Steering Events over time by Equipment MAC", response = EquipmentSteeringDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/steeringEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentSteeringData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getSteeringDetailsForSubscriber(equipmentIdOrSerialOrSTN, duration, steer).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get Equipment Diagnostic Events over time by Equipment MAC", response = EquipmentSteeringDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/diagnosticLogs", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentDiagnosticData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getSteeringDetailsForSubscriber(equipmentIdOrSerialOrSTN, duration, diagnostic).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_ROAM_EVENTS)
    @ApiOperation(value = "Get Equipment Roam Events over time by Equipment MAC", response = EquipmentSteeringDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/roamEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentRoamingData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getSteeringDetailsForSubscriber(equipmentIdOrSerialOrSTN, duration, roam).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_STEERING_LOGS)
    @ApiOperation(value = "Get Equipment Steering Logs over time by Equipment MAC", response = EquipmentSteeringDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/steeringLog", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentEventLogData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getSteeringDetailsForSubscriber(equipmentIdOrSerialOrSTN, duration, eventLog).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_CONNECT_STATE_LOGS)
    @ApiOperation(value = "Get Equipment Station Connect State Logs over time by Equipment MAC", response = EquipmentSteeringDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/staConnectState", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getStationConnectStateData(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getSteeringDetailsForSubscriber(equipmentIdOrSerialOrSTN, duration, staConnectState).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_2DOT4G_WIRELESS_DETAILS)
    @ApiOperation(value = "Get 2.4G Wireless Details of Equipment by Serial Number", response = EquipmentWirelessDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/24g/wireless/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentWireless24gDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, wireLess24g).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.EQUIPMENT_DETAILS)
//    @Timed(value = AuditorConstants.EQUIPMENT_DETAILS)
    @ApiOperation(value = "Get 2.4G Busy Details of Equipment by Serial Number", response = EquipmentCommonDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/24g/busy/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentBusy24gDetails(
            @ApiParam(value = "Serial Number or  RGW MAC", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getAPDetailsGraph(equipmentIdOrSerialOrSTN, serialNumber, busy24g, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_2DOT4G_BUSY)
    @ApiOperation(value = "Get 2.4G Busy By Device Details of Equipment by Serial Number", response = EquipmentBusyByDeviceDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/24g/busyByDevice/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentBusy24gByDeviceDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getAPDetailsGraphByDevice(equipmentIdOrSerialOrSTN, serialNumber, busy24gByDevice, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_5G_WIRELESS_DETAILS)
    @ApiOperation(value = "Get 5G Wireless Details of Equipment by Serial Number", response = EquipmentWirelessDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/5g/wireless/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentWireless5gDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentDetails(equipmentIdOrSerialOrSTN, serialNumber, wireLess5g).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_5G_BUSY_DETAILS)
    @ApiOperation(value = "Get 5G Busy Details of Equipment by Serial Number", response = EquipmentCommonDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/5g/busy/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentBusy5gDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getAPDetailsGraph(equipmentIdOrSerialOrSTN, serialNumber, busy5g, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENT_5G_BUSY)
    @ApiOperation(value = "Get 5G Busy By Device Details of Equipment by Serial Number", response = EquipmentBusyByDeviceDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/network/equipments/5g/busyByDevice/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentBusy5gByDeviceDetails(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getAPDetailsGraphByDevice(equipmentIdOrSerialOrSTN, serialNumber, busy5gByDevice, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.EQUIPMENT_UPDATE_FRIENDLY_NAME)
//    @Timed(value = AuditorConstants.EQUIPMENT_UPDATE_FRIENDLY_NAME)
    @ApiOperation(value = "Update Friendly name for Equipment", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/friendlyName", method = RequestMethod.PATCH)
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> updateEquipmentFriendlyName(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @RequestBody @Valid EquipmentFriendlyNameRequest friendlyNameRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        manageEquipmentService.updateFriendlyNameForEquipment(equipmentIdOrSerialOrSTN, friendlyNameRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //        @Auditable(method = RequestMethod.PATCH,operation = AuditorConstants.EQUIPMENT_REBOOT)
//    @Timed(value = AuditorConstants.EQUIPMENT_REBOOT)
    @ApiOperation(value = "Reboot Equipment", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/{serialNumber}/reboot", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> disconnectInternet(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        manageEquipmentService.invokeInternetRPCMethodsForEquipment(equipmentIdOrSerialOrSTN, serialNumber, AP_REBOOT, null, null, httpServletRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.EQUIPMENT_FORGET)
//    @Timed(value = AuditorConstants.EQUIPMENT_FORGET)
    @ApiOperation(value = "Forget Equipment", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/{serialNumber}/forget", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = TechnicianDashboard.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    public ResponseDTO<?> forgetDevice(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        manageEquipmentService.forgetEquipment(equipmentIdOrSerialOrSTN, serialNumber);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.PATCH,operation = AuditorConstants.EQUIPMENT_CHANNEL_OPTIMIZATION)
//    @Timed(value = AuditorConstants.EQUIPMENT_CHANNEL_OPTIMIZATION)
    @ApiOperation(value = "Equipment Channel Optimization", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/{serialNumber}/channelRescan", method = RequestMethod.PATCH)
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public CompletableFuture<ResponseDTO<?>> channelRescan(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Equipment Band")
            @RequestParam(required = true, name = "band") String band,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> {
            manageEquipmentService.processChannelRescanOrWIFIResetOptimization(equipmentIdOrSerialOrSTN, serialNumber, band, ActiontecConstants.OBJECT_TYPE_DOACS, httpServletRequest);
            return responseUtil.ok(ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });
    }

    //    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.EQUIPMENT_RESET_WIFI)
//    @Timed(value = AuditorConstants.EQUIPMENT_RESET_WIFI)
    @ApiOperation(value = "Equipment WIFI Reset", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/{serialNumber}/wifiReset", method = RequestMethod.PATCH)
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public CompletableFuture<ResponseDTO<?>> channelReset(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Equipment Band")
            @RequestParam(required = true, name = "band") String band,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> {
            manageEquipmentService.processChannelRescanOrWIFIResetOptimization(equipmentIdOrSerialOrSTN, serialNumber, band, ActiontecConstants.OBJECT_TYPE_RESET_WIFI, httpServletRequest);
            return responseUtil.ok(ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.EQUIPMENTS_LOOKUP)
    @ApiOperation(value = "Equipments Look Up", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/suggest", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentLookup.class)
    public ResponseDTO<?> suggestEquipment(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @RequestParam(name = "key") String key,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getEquipmentsSuggestion(key), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Create an Equipment", response = ApiResponseDTO.class)
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.EQUIPMENT_CREATE)
//    @Timed(value = AuditorConstants.EQUIPMENT_CREATE)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Subscribers.class)
    @ResponseStatus(HttpStatus.CREATED)
    public ResponseDTO<?> createSubscriber(@RequestBody @Valid EquipmentModel createEquipmentRequest,
                                           @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                           @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(newManageEquipmentService.createEquipment(createEquipmentRequest), ApiResponseCode.SUCCESS);
    }

    @ApiIgnore
    @ApiOperation(value = "Attach equipment to subscriber", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/attach", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Subscribers.class)
    public ResponseDTO<?> attachEquipment(@RequestBody @Valid AttachEquipmentRequest attachEquipmentRequest,
                                          @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                          @RequestHeader(name = "X-Authorization") String accessToken,
                                          HttpServletRequest httpServletRequest) throws Exception {
        newManageEquipmentService.attachEquipment(attachEquipmentRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @ApiOperation(value = "Edit Equipment", response = ApiResponseDTO.class)
    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.EQUIPMENT_UPDATE)
//    @Timed(value = AuditorConstants.EQUIPMENT_UPDATE)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = Subscribers.class)
    public ResponseDTO<?> editEquipment(@RequestBody @Valid EditEquipmentRequest editEquipmentRequest,
                                        @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
                                        @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                        @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        newManageEquipmentService.editEquipment(equipmentIdOrSerialOrSTN, editEquipmentRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @ApiIgnore
    @ApiOperation(value = "Detach equipment from a subscriber", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/detach/{equipmentIdOrSerialOrSTN}", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Subscribers.class)
    public ResponseDTO<?> detachEquipment(@PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
                                          @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                          @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        newManageEquipmentService.detachEquipment(equipmentIdOrSerialOrSTN);
        return responseUtil.ok(ApiResponseCode.SUCCESS);
    }

    @ApiOperation(value = "Get all attached equipments for a subscriber", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/attach/{subscriberId}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Subscribers.class)
    public ResponseDTO<?> getAllEquipment(@PathVariable(name = "subscriberId") String subscriberId,
                                          @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                          @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(newManageEquipmentService.getAllEquipments(subscriberId), ApiResponseCode.SUCCESS);
    }

    @Auditable(method = RequestMethod.POST,operation = AuditorConstants.EQUIPMENT_LIST)
    @ApiOperation(value = "Get all Equipments list", response = SubscriberListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/paginated", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public ResponseDTO<?> getAllSubscribers(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody TechnicianDashboardDTO technicianDashboardDTO,
            @RequestParam(required = false, defaultValue = "false") Boolean exact,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(newManageEquipmentService.getAllEquipmentListByPagination(technicianDashboardDTO, exact), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get Equipment By Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}", method = RequestMethod.GET, produces = "application/json")
    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.EQUIPMENT_SELECT)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Subscribers.class)
    public ResponseDTO<?> getEquipmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(newManageEquipmentService.getEquipmentById(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Delete Equipment by Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = Common.class)
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.EQUIPMENT_DELETE)
//    @Timed(value = AuditorConstants.EQUIPMENT_DELETE)
    public void deleteEquipmentById(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Delete Equipment by   Id", required = true)
            @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN) throws Exception {
        newManageEquipmentService.deleteEquipmentById(equipmentIdOrSerialOrSTN);
    }

    @ApiIgnore
    @ApiOperation(value = "Get Unattached equipment list", response = SubscriberListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/detached/paginated", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Common.class)
    public ResponseDTO<?> getAllDetachedEquipment(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody DetachedEquipmentDTO detachedEquipmentDTO,
            @RequestParam(required = false, defaultValue = "false") Boolean exact,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(newManageEquipmentService.getAllDetachedEquipment(detachedEquipmentDTO, exact), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get Neighbor Scan Details of Equipment", response = EquipmentNeighborScanDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{serialNumberOrSubscriberIdOrRGWMAC}/network/equipments/neighborScans", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentNeighborScan(
            @ApiParam(value = "Serial Number or Subscriber Id or RGW MAC", required = true) @PathVariable(name = "serialNumberOrSubscriberIdOrRGWMAC") String serialNumberOrSubscriberIdOrRGWMAC,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getNeighborScan(serialNumberOrSubscriberIdOrRGWMAC, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Trigger Neighbor Scan of Equipment", response = EquipmentNeighborScanDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{serialNumberOrSubscriberIdOrRGWMAC}/network/equipments/neighborScans/action", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> performEquipmentNeighborScan(
            @ApiParam(value = "Serial Number or Subscriber Id or RGW MAC", required = true) @PathVariable(name = "serialNumberOrSubscriberIdOrRGWMAC") String serialNumberOrSubscriberIdOrRGWMAC,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody NeighborScanRequestDTO neighborScanRequestDTO,
            HttpServletRequest httpServletRequest) throws Exception {
        manageEquipmentService.performNeighborScan(neighborScanRequestDTO, serialNumberOrSubscriberIdOrRGWMAC);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
    @ApiOperation(value = "Get radio channel selection", response = EquipmentChannelSelectionDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{serialNumberOrSubscriberIdOrRGWMAC}/network/equipments/{band}/channelSelection/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentChannelSelection(
            @ApiParam(value = "Serial Number or Subscriber Id or RGW MAC", required = true) @PathVariable(name = "serialNumberOrSubscriberIdOrRGWMAC") String serialNumberOrSubscriberIdOrRGWMAC,
            @ApiParam(value = "Which band you select: available values : 24g, 5g", required = true) @PathVariable(name = "band") String band,
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getChannelSelection(serialNumberOrSubscriberIdOrRGWMAC, band, serialNumber, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get radio channel selection", response = EquipmentChannelChangeEventDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{serialNumberOrSubscriberIdOrRGWMAC}/network/equipments/channelChangeEvents", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentChannelChangeEvent(
            @ApiParam(value = "Serial Number or Subscriber Id or RGW MAC", required = true) @PathVariable(name = "serialNumberOrSubscriberIdOrRGWMAC") String serialNumberOrSubscriberIdOrRGWMAC,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getChannelChangeEvent(serialNumberOrSubscriberIdOrRGWMAC, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get last channel interference", response = EquipmentChannelChangeEventDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{serialNumberOrSubscriberIdOrRGWMAC}/network/equipments/channelInterference", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getEquipmentChannelInterference(
            @ApiParam(value = "Serial Number or Subscriber Id or RGW MAC", required = true) @PathVariable(name = "serialNumberOrSubscriberIdOrRGWMAC") String serialNumberOrSubscriberIdOrRGWMAC,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Duration for which the details are requested. Default is last 60 minutes")
            @RequestParam(required = false, name = "duration", defaultValue = "60") Long duration,
            @RequestParam(required = false, name = "band", defaultValue = "both") String band,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getChannelInterference(serialNumberOrSubscriberIdOrRGWMAC, band, duration).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get Data Element", response = EquipmentListDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/dataElement", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getDataElement(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Serial Number or Subscriber Id or RGW MAC")
            @RequestParam(required = true, name = "serialNumber") String serialNumber,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getDataElement(serialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get the data of All or one Radio", response = TimeZoneResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = TimeZoneResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/radios/{radioId}", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_TIMEZONE_DATA, method = RequestMethod.GET, persist = false)
////    @Timed(value = AuditorConstants.GET_TIMEZONE_DATA)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getDataOfRadios(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "SubscriberId or SerialNumber or RGW MAC", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Unique Radio Id One of the  \"2.4G\",\"5G\",\"5GLo\",\"5GHi\"   ", required = true) @PathVariable(name = "radioId") String radioId,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getDataOfRadios(equipmentIdOrSerialOrSTN, radioId), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Put the data of  Radio", response = TimeZoneResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = TimeZoneResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/radios/{radioId}", method = RequestMethod.PUT)
//    @Auditable(operation = AuditorConstants.GET_TIMEZONE_DATA, method = RequestMethod.PUT, persist = false)
////    @Timed(value = AuditorConstants.GET_TIMEZONE_DATA)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = Common.class)
    public ResponseDTO<?> putDataOfRadios(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "SubscriberId or SerialNumber or RGW MAC", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Unique Radio Id One of the  \"2.4G\",\"5G\",\"5GLo\",\"5GHi\"  ", required = true) @PathVariable(name = "radioId") String radioId,
            @RequestBody RadioRequestDTO radioRequestDTO,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.putDataOfRadios(equipmentIdOrSerialOrSTN, radioId, radioRequestDTO), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Get ApDetail Of 5G Radio Band", response = TimeZoneResponse.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = TimeZoneResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/equipments/radios/5G/dfs", method = RequestMethod.GET)
//    @Auditable(operation = AuditorConstants.GET_TIMEZONE_DATA, method = RequestMethod.PUT, persist = false)
////    @Timed(value = AuditorConstants.GET_TIMEZONE_DATA)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getApDetailOfRadio5GBand(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "SubscriberId or SerialNumber or RGW MAC", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageEquipmentService.getApDetailOfRadio5GBandDFS(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SMART_STEERING_ENABLE_DISABLE)
//    @Timed(value = AuditorConstants.SMART_STEERING_ENABLE_DISABLE)
    @ApiOperation(value = "Enable/Disable Smart Steering for Subscriber", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/smartSteering", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = TechnicianDashboard.class)
    public CompletableFuture<ResponseDTO<?>> enableDisableSmartSteering(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Enable/Disable", required = true) @RequestParam(name = "enable") boolean enable,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return manageLongWaitAPI.call(() -> {
            manageNetworkService.modifySmartSteering(equipmentIdOrSerialOrSTN, enable, httpServletRequest);
            return responseUtil.ok(ApiResponseCode.SUCCESS,
                    CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
        });
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SMART_STEERING_STATUS_OF_SUBSCRIBER)
    @ApiOperation(value = "Get Status of Smart Steering for Subscriber", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/smartSteering/status", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getStatusOfSmartSteering(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkService.getStatusOfSmartSteering(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.SMART_STEERING_CONFIG)
    @ApiOperation(value = "Config Smart Steering for Subscriber\n", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = ApiResponseDTO.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/smartSteering/config", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = Common.class)
    public ResponseDTO<?> patchSmartSteering(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Change SmartSteering", required = true) @RequestBody Map requestBody,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(smartSteeringService.putSteering(equipmentIdOrSerialOrSTN, requestBody), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get Status of Smart Steering for Subscriber", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
            @ApiResponse(code = 405, message = "Method Not Allowed"),
            @ApiResponse(code = 400, message = "Request not Completed, Bad Request"),
            @ApiResponse(code = 500, message = "Sorry, something went wrong. We're working on it and get it fixed as soon as we can"),
            @ApiResponse(code = 501, message = "Service is Not Implemented"),
            @ApiResponse(code = 503, message = "Service is Unavailable"),
            @ApiResponse(code = 200, response = TimeZoneResponse.class, message = "Request Completed Successfully")
    })
    @RequestMapping(value = "/{equipmentIdOrSerialOrSTN}/smartSteering/config", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getSmartSteeringStatus(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @ApiParam(value = "Serial Number or RGW MAC", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(smartSteeringService.getSteering(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
