package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.TechnicianDashboard;
import com.incs83.app.business.v2.ManageAlarmService;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.alarms.AlarmsDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@RestController
@Api(value = "(V2) Equipment and Device Alarms", description = "API's for various Equipment and Device level alarms",tags = { "Optim - (V2) Equipment and Device Alarms" })
@RequestMapping(value = "/actiontec/api/v2/equipments/{equipmentIdOrSerialOrSTN}")
@SuppressWarnings("rawtypes")
public class AlarmController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageAlarmService manageAlarmService;

    @ApiOperation(value = "Get all Alarms For a Subscriber", response = AlarmsDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/alarms", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = TechnicianDashboard.class)
    public ResponseDTO<?> getAllAlarmsForSubscriber(
            @ApiParam(value = "EquipmentId or Serial Number or STN", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageAlarmService.getAllAlarmsForSubscriber(equipmentIdOrSerialOrSTN), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
