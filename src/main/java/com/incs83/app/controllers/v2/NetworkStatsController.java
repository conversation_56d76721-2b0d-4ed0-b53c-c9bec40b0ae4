package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.authResources.NetworkDashboard;
import com.incs83.app.business.v2.ManageNetworkStatsService;
import com.incs83.app.responsedto.v2.NetworkStats.*;
import com.incs83.app.responsedto.v2.Subscriber.WifiMeterDataDTO;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import static com.incs83.app.constants.misc.ActiontecConstants.*;
import static com.incs83.app.constants.misc.ApplicationConstants.*;

@RestController
@Api(value = "(V2) Cluster Network Stats", description = "API's for Cluster Network Stats", tags = {"Optim - (V2) Cluster Network Stats"})
@RequestMapping(value = "/actiontec/api/v2/network/stats")
public class NetworkStatsController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageNetworkStatsService manageNetworkStatsService;

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_HEALTH_CURRENT_DATA)
    @ApiOperation(value = "Get wifiHealth current Data of Network Dashboard for Global or cluster Network", response = WifiMeterDataDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/current/wifiHealth", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiHealthForGlobalOrCluster(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getMeterData(id, wifiHealth).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_DOWNLOAD_SPEED_CURRENT_DATA)
    @ApiOperation(value = "Get wifi DownloadSpeed current Data of Network Dashboard for Global or cluster Network", response = WifiMeterDataDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/current/avgDownlinkRate", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiDlSpeedForGlobalOrCluster(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getMeterData(id, wifiDlSpeed).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_UPLOAD_SPEED_CURRENT_DATA)
    @ApiOperation(value = "Get wifi UploadSpeed current Data of Network Dashboard for Global or cluster Network", response = WifiMeterDataDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/current/avgUplinkRate", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiUlSpeedForGlobalOrCluster(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getMeterData(id, wifiUlSpeed).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_UPLOAD_SPEED_DISTRIBUTION)
    @ApiOperation(value = "Get wifi UploadSpeedDist historical Data of Network Dashboard for Global or cluster Network", response = CommonDistDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/upSpeedDist", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getUploadSpeedDistribution(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getUploadSpeedDist(id).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_DOWNLOAD_SPEED_DISTRIBUTION)
    @ApiOperation(value = "Get wifi DownloadSpeedDist historical Data of Network Dashboard for Global or cluster Network", response = CommonDistDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/dnSpeedDist", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getdownloadSpeedDistribution(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getDownloadSpeedDist(id).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_HEALTH_DISTRIBUTION)
    @ApiOperation(value = "Get wifiHealthDist historical Data of Network Dashboard for Global or cluster Network", response = HealthDistDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/healthDist", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getHealthDistribution(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getHealthDist(id).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    //    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.AIRTIME_UTILIZATION_DISTRIBUTION_5G)
    @ApiOperation(value = "Get 6G AirTimeUtlizationDistribution historical Data of Network Dashboard for Global or cluster Network", response = AirTimeUtlzDist.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/6g/airTimeUtzDist", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getAirTimeUtlizationDistributionFor6g(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getAirTimeUtilizationDist(id, BAND_6G).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.AIRTIME_UTILIZATION_DISTRIBUTION_5G)
    @ApiOperation(value = "Get 5G AirTimeUtlizationDistribution historical Data of Network Dashboard for Global or cluster Network", response = AirTimeUtlzDist.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/5g/airTimeUtzDist", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getAirTimeUtlizationDistributionFor5g(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getAirTimeUtilizationDist(id, BAND_5G).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.AIRTIME_UTILIZATION_DISTRIBUTION_2DOT4G)
    @ApiOperation(value = "Get 2.4G AirTimeUtlizationDistribution historical Data of Network Dashboard for Global or cluster Network", response = AirTimeUtlzDist.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/24g/airTimeUtzDist", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getAirTimeUtlizationDistributionFor24g(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getAirTimeUtilizationDist(id, BAND_24G).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.SIGNAL_STRENGTH_DISTRIBUTION)
    @ApiOperation(value = "Get SignalStrengthDistribution historical Data of Network Dashboard for Global or cluster Network", response = SignalStrengthDistDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/signalStrengthDist", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getSignalStrengthDistribution(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageNetworkStatsService.getSignalStrengthDist(id).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_HEALTH_HISTORICAL_DATA)
    @ApiOperation(value = "Get wifiHealth historical Data of Network Dashboard for Global or cluster Network", response = HealthDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/wifiHealth", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiHealthGraphDataForGlobalOrCluster(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(((HealthDTO) manageNetworkStatsService.getClumnChartData(id, wifiHealth)).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_DOWNLOAD_SPEED_HISTORICAL_DATA)
    @ApiOperation(value = "Get wifi DownloadSpeed historical Data of Network Dashboard for Global or cluster Network", response = DlSpeedDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/wifiDlSpeed", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiDlSpeedGraphForGlobalOrCluster(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(((DlSpeedDTO) manageNetworkStatsService.getClumnChartData(id, wifiDlSpeed)).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.WIFI_UPLOAD_SPEED_HISTORICAL_DATA)
    @ApiOperation(value = "Get wifi UploadSpeed historical Data of Network Dashboard for Global or cluster Network", response = UlSpeedDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/wifiUlSpeed", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> fetchNetworkWifiUlSpeedGraphForGlobalOrCluster(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(((UlSpeedDTO) manageNetworkStatsService.getClumnChartData(id, wifiUlSpeed)).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

//    @Auditable(method = RequestMethod.GET,operation = AuditorConstants.AIRTIME_UTILIZATION_HISTORICAL_DATA)
    @ApiOperation(value = "Get AirTimeUtilization historical Data of Network Dashboard for Global or cluster Network", response = AirTimeUtlzDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/airTimeUtz", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getAirTimeUtlizationGraphData(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(((AirTimeUtlzDTO) manageNetworkStatsService.getClumnChartData(id, wifiAirTimeUtlz)).getData(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get WiFi Channel usage historical Data of Network Dashboard for Global or cluster Network", response = ChannelDistDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/historical/channelSelection", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = NetworkDashboard.class)
    public ResponseDTO<?> getChannelSelectionGraphData(
            @ApiParam(value = "Global Id or cluster Id") @RequestParam(required = false, defaultValue = "0") String id,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok((manageNetworkStatsService.getChannelSelectionDistV2(id)), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
