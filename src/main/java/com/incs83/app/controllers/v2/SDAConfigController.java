package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.SDAConfig;
import com.incs83.app.business.v2.ManageSDAConfigService;
import com.incs83.app.common.v2.SDAConfigEnvironmentRequest;
import com.incs83.app.common.v2.SDAConfigRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.app.responsedto.v2.misc.AccessDeniedResponseDTO;
import com.incs83.app.responsedto.v2.misc.BadRequestResponseDTO;
import com.incs83.app.responsedto.v2.misc.NotAuthenticatedResponseDTO;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

@ApiIgnore
@RestController
@Api(value = "(V2) SDA Config", description = "API's for SDA")
@RequestMapping(value = "/actiontec/api/v2/sda/config")
public class SDAConfigController {
    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private ManageSDAConfigService manageSDAConfigService;

    @ApiOperation(value = "Get SDA Config", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SDAConfig.class)
    public ResponseDTO<?> getSDAConfigPropsByServiceName(HttpServletRequest httpServletRequest,
                                                         @ApiParam(value = "Service Name") @RequestParam(name = "serviceName", required = false) String serviceName,
                                                         @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                         @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {

        return responseUtil.ok(manageSDAConfigService.getAllSDAConfigProps(serviceName), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.UPDATE_SERVICE_CONFIG)
//    @Timed(value =  AuditorConstants.UPDATE_SERVICE_CONFIG)
    @ApiOperation(value = "Update SDA Config", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = SDAConfig.class)
    public ResponseDTO<?> updateSDAConfig(@Valid @RequestBody SDAConfigRequest request, HttpServletRequest httpServletRequest) throws Exception {
        manageSDAConfigService.updateSDAConfig(request);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @Auditable(method = RequestMethod.PATCH, operation = AuditorConstants.UPDATE_SERVICE_CONFIG_ENV)
//    @Timed(value = AuditorConstants.UPDATE_SERVICE_CONFIG_ENV)
    @ApiOperation(value = "Update SDA Config", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/envVars", method = RequestMethod.PATCH, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.PATCH, resourceType = SDAConfig.class)
    public ResponseDTO<?> updateSDAConfig(@Valid @RequestBody SDAConfigEnvironmentRequest request, HttpServletRequest httpServletRequest) throws Exception {
        manageSDAConfigService.updateSDAEnvironmentConfig(request);
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Get all version for a service", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/versions/service/{service}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SDAConfig.class)
    public ResponseDTO<?> getVersions(@PathVariable String service, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSDAConfigService.getVersions(service), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }


    @ApiOperation(value = "Get history for a service", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 400, message = "Bad Request", response = BadRequestResponseDTO.class),
            @ApiResponse(code = 401, message = "You are Not Authenticated", response = NotAuthenticatedResponseDTO.class),
            @ApiResponse(code = 403, message = "Not Authorized on this resource", response = AccessDeniedResponseDTO.class),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/service/{service}/version/{version}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = SDAConfig.class)
    public ResponseDTO<?> getHistory(@PathVariable String version, @PathVariable String service, HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(manageSDAConfigService.getHistory(service, version), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
