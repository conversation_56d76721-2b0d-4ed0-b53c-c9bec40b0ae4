package com.incs83.app.controllers.v2;

import com.incs83.abstraction.ApiResponseCode;
import com.incs83.annotation.PreHandle;
import com.incs83.app.annotation.Auditable;
import com.incs83.app.authResources.*;
import com.incs83.app.business.v2.UtilityServices;
import com.incs83.app.common.v2.ElasticSearchRequest;
import com.incs83.app.common.v2.IndexTTLRequest;
import com.incs83.app.constants.misc.AuditorConstants;
import com.incs83.dto.ApiResponseDTO;
import com.incs83.dto.ResponseDTO;
import com.incs83.util.CommonUtils;
import com.incs83.util.ResponseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

//import com.incs83.app.business.v2.HiveService;

@Api(value = "(V2) Dashboard Version", description = "API'S for Dashboard version", tags = {"Optim - (V2) Dashboard Version"})
@RestController
@RequestMapping(value = "/actiontec/api/v2/platform")
@SuppressWarnings("rawtypes")
public class UtilityController {

    @Autowired
    private ResponseUtil responseUtil;

    @Autowired
    private UtilityServices utilityServices;

//    @Autowired
//    private HiveService hiveService;


    @ApiIgnore
    @ApiOperation(value = "Get Network Wifi Details", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/devices/connection/status", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentLookup.class)
    public ResponseDTO<?> getCountOfDeviceOnlineStatus(
            @RequestParam(name = "clusterId", defaultValue = "0") String clusterId,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.countOfDeviceOnlineStatus(clusterId), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.SUBSCRIBER_DETAILS)
    @ApiOperation(value = "Get Subscriber Id", response = ApiResponseDTO.class)
    @ApiResponses(value = {
            @ApiResponse(code = 401, message = "You are Not Authenticated"),
            @ApiResponse(code = 403, message = "Accessing the resource you were trying to reach is forbidden"),
            @ApiResponse(code = 404, message = "The resource you were trying to reach is not found"),
    })
    @RequestMapping(value = "/subscribers", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getSubscriberDetail(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.getSubscriberDetails(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.RGW_FRIENDLY_NAME)
//    @Timed(value = AuditorConstants.RGW_FRIENDLY_NAME)
    @RequestMapping(value = "/upload/friendlyNames", method = RequestMethod.POST)
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = Subscribers.class)
    public ResponseDTO<?> uploadFriendlyNames(
            @RequestParam String type,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true) @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody MultipartFile fileDetails,
            HttpServletRequest httpServletRequest) throws Exception {
        utilityServices.upFriendlyNamesBulk(fileDetails, type);
        return responseUtil.ok(null, ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    /*@ApiIgnore
    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.EQUIPMENT_DETAILS)
    @RequestMapping(value = "/network/equipments/systemInfo/{serialNumber}", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = EquipmentLookup.class)
    @Deprecated
    public ResponseDTO<?> getEquipmentSystemDetails(
            @ApiParam(value = "Serial Number of Equipment", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.getEquipmentDetails(serialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    @ApiIgnore
    @Auditable(method = RequestMethod.POST, operation = AuditorConstants.SEARCH_EQUIPMENT_DETAILS)
//    @Timed(value =  AuditorConstants.SEARCH_EQUIPMENT_DETAILS)
    @RequestMapping(value = "/equipments/systemInfo", method = RequestMethod.POST, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.POST, resourceType = EquipmentLookup.class)
    public ResponseDTO<?> searchEquipmentSystemDetails(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            @RequestBody ElasticSearchRequest elasticSearchRequest,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.searchEquipmentDetails(elasticSearchRequest), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @RequestMapping(value = "/networkInfo", method = RequestMethod.GET, produces = "application/json")
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getNetworkInfoForCluster(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.getNetworkInfoForCluster(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    /*@ApiIgnore
    @RequestMapping(value = "/subscribers/serial/{serialNumber}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getSubscribersDetailsBySerialNumber(
            @ApiParam(value = "Serial Number", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            HttpServletRequest httpServletRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(utilityServices.getSubscriberDetailsBySerialNumber(serialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }*/

    @ApiIgnore
    @RequestMapping(value = "/equipments/{equipmentIdOrSerialOrSTN}/serial/{serialNumber}", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getEquipmentInfoBySerial(
            @ApiParam(value = "Subscriber Id", required = true) @PathVariable(name = "equipmentIdOrSerialOrSTN") String equipmentIdOrSerialOrSTN,
            @ApiParam(value = "Serial Number", required = true) @PathVariable(name = "serialNumber") String serialNumber,
            HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.getEquipmentInfoBySerial(equipmentIdOrSerialOrSTN,serialNumber), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @RequestMapping(value = "/voice/subscribers", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getAllViceSubscribersDetails(
            HttpServletRequest httpServletRequest,
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(utilityServices.getAllVoceSubscriber(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.DEVICE_VENDOR_DETAIL_UPDATE)
//    @Timed(value = AuditorConstants.DEVICE_VENDOR_DETAIL_UPDATE)
    @RequestMapping(value = "/vendor/detail", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = MACVendors.class)
    public ResponseDTO<?> updateVendorNameForStation(HttpServletRequest httpServletRequest,
                                                     @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                                     @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {

        utilityServices.updateVendorDetailsForStation();
        return responseUtil.ok(ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.VENDOR_COUNT)
    @RequestMapping(value = "/vendor/count", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = MACVendors.class)
    public ResponseDTO<?> getVendorCount(HttpServletRequest httpServletRequest,
                                         @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                         @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {

        return responseUtil.ok(utilityServices.getVendorCount(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ALL_COLLECTIONS)
    @RequestMapping(value = "/collection/indexes", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = DataExpiry.class)
    public ResponseDTO<?> getCollectionWithIndexes(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return responseUtil.ok(utilityServices.getAllCollections());
    }

    @ApiIgnore
    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ALL_COLLECTIONS_WITH_DESCRIPTION)
    @RequestMapping(value = "/collection", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = DataExpiry.class)
    public ResponseDTO<?> getCollectionWithDescription(
            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
            @RequestHeader(name = "X-Authorization") String accessToken,
            HttpServletRequest httpServletRequest) throws Exception {

        return responseUtil.ok(utilityServices.getAllCollectionsWithDesc());
    }

//    @ApiIgnore
//    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.INVALID_JSON)
//    @RequestMapping(value = "/lookup", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = MQTTDiagnostics.class)
//    public ResponseDTO<?> getInvalidJSON(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @RequestParam(name = "date") String date,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(hiveService.readInvalidJson(date));
//    }
//
//    @ApiIgnore
//    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.INVALID_JSON_BY_USERID)
//    @RequestMapping(value = "/lookup/{userId}", method = RequestMethod.GET)
//    @PreHandle(requestMethod = RequestMethod.GET, resourceType = MQTTDiagnostics.class)
//    public ResponseDTO<?> getInvalidJSONByUserId(
//            @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
//            @RequestHeader(name = "X-Authorization") String accessToken,
//            @PathVariable(name = "userId") String userId,
//            @RequestParam(name = "count") Integer count,
//            @RequestParam(name = "date") String date,
//            HttpServletRequest httpServletRequest) throws Exception {
//        return responseUtil.ok(hiveService.readInvalidJsonById(userId, count, date));
//    }

    @ApiIgnore
    @Auditable(method = RequestMethod.PUT, operation = AuditorConstants.INDEX_TTL_UPDATE)
//    @Timed(value = AuditorConstants.INDEX_TTL_UPDATE)
    @RequestMapping(value = "/index/ttl", method = RequestMethod.PUT)
    @PreHandle(requestMethod = RequestMethod.PUT, resourceType = DataExpiry.class)
    public ResponseDTO<?> updateTtlForIndex(@RequestBody @Valid List<IndexTTLRequest> indexTTLRequest, HttpServletRequest httpServletRequest) throws Exception {
        utilityServices.updateTTLForIndex(indexTTLRequest);
        return responseUtil.ok(ApiResponseCode.SUCCESS, CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiIgnore
    @Auditable(method = RequestMethod.DELETE, operation = AuditorConstants.INDEX_TTL_DELETE)
//    @Timed(value = AuditorConstants.INDEX_TTL_DELETE)
    @RequestMapping(value = "/index/ttl", method = RequestMethod.DELETE)
    @PreHandle(requestMethod = RequestMethod.DELETE, resourceType = DataExpiry.class)
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public void deleteTtlForIndex(IndexTTLRequest indexTTLRequest, HttpServletRequest httpServletRequest) throws Exception {
        utilityServices.removeTTlForIndex(indexTTLRequest);
    }

    @ApiIgnore
    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ETL_QUERY_PROGRESS)
    @RequestMapping(value = "/etlQueryProgress", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = ETLDiagnostics.class)
    public ResponseDTO<?> getETLQueryProgress(HttpServletRequest httpServletRequest,
                                              @RequestParam(required = false, name = "name") String name,
                                              @RequestParam(required = false, name = "type") String type) throws Exception {
        return responseUtil.ok(utilityServices.getETLQueryProgress(name, type), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }

    @ApiIgnore
    @RequestMapping(value = "/etlNames", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = ETLDiagnostics.class)
    public ResponseDTO<?> getETLNames(HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.getETLNames(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }

    @ApiIgnore
    @RequestMapping(value = "/etl/progress", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = ETLDiagnostics.class)
    public ResponseDTO<?> getAllSlowETL(HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.getSlowPerformingETLs(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }

    @ApiIgnore
    //    @Auditable(method = RequestMethod.GET, operation = AuditorConstants.ETL_PROPERTIES_CSV)
    @RequestMapping(value = "/etlProperties", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = ETLDiagnostics.class)
    public ResponseDTO<?> getEtlPropertiesCSV(HttpServletRequest httpServletRequest) throws Exception {
        return responseUtil.ok(utilityServices.getEtlPropertiesCSV(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));

    }

    @ApiIgnore
    @RequestMapping(value = "/mqttInfo", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getEncryptedPassword(HttpServletRequest httpServletRequest, @RequestParam(name = "type") String type) throws Exception {
        return responseUtil.ok(utilityServices.getEncryptedPassword(type), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }

    @ApiOperation(value = "Get Dashboard version", response = ApiResponseDTO.class)
    @RequestMapping(value = "/buildVersion", method = RequestMethod.GET)
    @PreHandle(requestMethod = RequestMethod.GET, resourceType = Common.class)
    public ResponseDTO<?> getBuildVersion(HttpServletRequest httpServletRequest,
                                          @ApiParam(value = "Bearer Access Token required for Authentication", required = true)
                                          @RequestHeader(name = "X-Authorization") String accessToken) throws Exception {
        return responseUtil.ok(utilityServices.getBuildVersionMap(), ApiResponseCode.SUCCESS,
                CommonUtils.getLocaleStringFromClientHTTPRequest(httpServletRequest));
    }
}
