package com.incs83.app.service.components;

import com.incs83.app.constants.misc.ApplicationConstants;
import com.incs83.app.constants.templates.MailTemplate;
import com.incs83.app.service.components.ses.EmailService;
import org.apache.logging.log4j.LogManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Objects;

import static com.incs83.app.constants.misc.ApplicationConstants.EMPTY_STRING;

/**
 * The type Mail service.
 */
@Service
public class MailService {

    private static final org.apache.logging.log4j.Logger LOG = LogManager.getLogger("org");

    @Autowired
    private Environment environment;

    @Autowired
    private EmailService emailService;


    /*public void sendPasswordChangedNotificationEmail(String email, HashMap<String, String> valuesMap) {
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.RESET_PASSWORD_MAIL.replace("-USER-", valuesMap.get("username")).replace("-LINK-", valuesMap.get("url"));
            emailService.sendSimpleMessage(email, "nVent - Reset Password Notification", text);
        }
    }*/

    @Async
    public void sendPasswordChangedNotificationEmail(String email, HashMap<String, String> valuesMap) {
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.PASSWORD_CHANGE_NOTIFICATION_MAIL.replace("-USER-", valuesMap.get("username")).replace("-LINK-", (Objects.isNull(valuesMap.get("LINK")) || EMPTY_STRING.equals(valuesMap.get("LINK").trim())) ? EMPTY_STRING : valuesMap.get("LINK"));
            emailService.sendSimpleMessage(email, "Password Changed Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    @Async
    public void sendPasswordChangedNotificationEmailByAdmin(String email, HashMap<String, String> valuesMap) {
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.PASSWORD_CHANGE_NOTIFICATION_MAIL_FROM_ADMIN.replace("-USER-", valuesMap.get("username"));
            emailService.sendSimpleMessage(email, "Password Changed Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    @Async
    public void sendPasswordResetLinkEmail(String email, HashMap<String, String> valuesMap) {
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.RESET_PASSWORD_MAIL.replace("-USER-", valuesMap.get("username")).replace("-LINK-", valuesMap.get("url"));
            emailService.sendSimpleMessage(email, "Password Reset", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    public void sendAutoGeneratedPassword(String email, HashMap<String, String> valuesMap) {
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.AUTO_GENERATED_PASSWORD.replace("-USER-", valuesMap.get("USER")).replace("-LINK-", (Objects.isNull(valuesMap.get("LINK")) || EMPTY_STRING.equals(valuesMap.get("LINK").trim())) ? "portal" : valuesMap.get("LINK")).
                    replace("-PASSWORD-", valuesMap.get("PASSWORD"));
            emailService.sendSimpleMessage(email, "Optim- User Registration", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    @Async
    public void sendAlexaSubscriptionEmail(String email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.ALEXA_VOICE_SUBSCRIPTION_MAIL.replace("-USER-", valuesMap.get("userName"));
            emailService.sendSimpleMessage(email, "Alexa Subscription Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    @Async
    public void sendAlexaUnSubscriptionEmail(String email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.ALEXA_VOICE_UN_SUBSCRIPTION_MAIL.replace("-USER-", valuesMap.get("userName"));
            emailService.sendSimpleMessage(email, "Alexa Unsubscription Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    @Async
    public void sendGoogleHomeSubscriptionEmail(String email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.GOOGLE_VOICE_SUBSCRIPTION_MAIL.replace("-USER-", valuesMap.get("userName"));
            emailService.sendSimpleMessage(email, "Google Home Subscription Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    @Async
    public void sendGoogleHomeUnSubscriptionEmail(String email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.GOOGLE_VOICE_UN_SUBSCRIPTION_MAIL.replace("-USER-", valuesMap.get("userName"));
            emailService.sendSimpleMessage(email, "Google Home Unsubscription Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    public void sendReportGeneratedEmail(String[] email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.REPORT_GENERATED.replace("-LINK-", valuesMap.get("link"));
            emailService.sendSimpleMessage(email, "Report Generated...", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    public void sendAutonomousSelfHealEmail(String email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.AUTONOMOUS_SELF_HEAL.replace("-USER-", valuesMap.get("username"));
            emailService.sendSimpleMessage(email, "Autonomous Self Heal", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    public void sendReplyEmailToUser(String email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.CONTACT_US_REPLY.replace("-USER-", valuesMap.get("username"));
            emailService.sendSimpleMessage(email, "Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    public void sendEmailToTeam(String email, HashMap<String, String> valuesMap) {
        //TODO Design template
        if (Boolean.valueOf(environment.getProperty(ApplicationConstants.APP_MAIL_CONFIGURED))) {
            String text = MailTemplate.CONTACT_US.replace("-CONTENT-", valuesMap.get("content")).replace("-NAME-", valuesMap.get("name")).replace("-PHONENO-", valuesMap.get("phone")).replace("-EMAIL-", valuesMap.get("email"));
            emailService.sendSimpleMessage(email, "Contact Us Notification", text, environment.getProperty(ApplicationConstants.MAIL_FROM));
        }
    }

    /*public void sendEmailWithParams(SendMailRequest sendMailRequest) throws Exception {
        String[] to = sendMailRequest.getTo().split(ApplicationConstants.COMMA);
        LOG.info("Triggering Mail to :: " + to);
        emailService.sendSimpleMessage(to, "Report Generated", MailTemplate.GENERATE_REPORT.replace("-LINK-", sendMailRequest.getParams().get("link")));
    }*/
}
