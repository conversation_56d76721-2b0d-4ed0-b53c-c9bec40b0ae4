package com.incs83.app.service.components.ses;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @Created 23/08/18
 */
@Component
public class EmailServiceImpl implements EmailService {

    @Autowired
    public JavaMailSender emailSender;

    @Override
    public void sendSimpleMessage(String to, String subject, String text, String from) {
        emailSender.send(constructMessage(to,null,null,subject,text,from));
    }

    @Override
    public void sendSimpleMessage(String[] to, String subject, String text, String from) {
        String []cc = {};
        String []bcc = {};
        emailSender.send(constructMessage(to,cc,bcc,subject,text,from));
    }

    @Override
    public void sendSimpleMessage(String to, String cc, String bcc, String subject, String text, String from) {
        emailSender.send(constructMessage(to,cc,bcc,subject,text,from));
    }

    @Override
    public void sendSimpleMessage(String[] to, String[] cc, String[] bcc, String subject, String text, String from) {
        emailSender.send(constructMessage(to,cc,bcc,subject,text,from));
    }

    private SimpleMailMessage constructMessage(String[] to, String[] cc, String[] bcc, String subject, String text, String from) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);
        if (cc.length > 0)
            message.setCc(cc);
        if (bcc.length > 0)
            message.setBcc(bcc);
        message.setFrom(from);
        message.setSubject(subject);
        message.setText(text);

        return message;
    }

    private SimpleMailMessage constructMessage(String to, String cc, String bcc, String subject, String text, String from) {
        SimpleMailMessage message = new SimpleMailMessage();
        message.setTo(to);
        if (Objects.nonNull(cc))
            message.setCc(cc);
        if (Objects.nonNull(bcc))
            message.setBcc(bcc);
        message.setFrom(from);
        message.setSubject(subject);
        message.setText(text);

        return message;
    }

}
