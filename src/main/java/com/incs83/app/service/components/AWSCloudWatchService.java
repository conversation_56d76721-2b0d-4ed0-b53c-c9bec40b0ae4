package com.incs83.app.service.components;

import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.services.cloudwatch.AmazonCloudWatch;
import com.amazonaws.services.cloudwatch.AmazonCloudWatchClientBuilder;
import com.amazonaws.services.cloudwatch.model.AmazonCloudWatchException;
import com.amazonaws.services.cloudwatch.model.Dimension;
import com.amazonaws.services.cloudwatch.model.GetMetricStatisticsRequest;
import com.amazonaws.services.cloudwatch.model.GetMetricStatisticsResult;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;

import static com.incs83.app.constants.misc.ApplicationConstants.*;

//@Service
public class AWSCloudWatchService {

    private static final Logger LOG = LogManager.getLogger("org");

    @PostConstruct
    public AmazonCloudWatch getCloudWatchClient() {
        AmazonCloudWatch amazonCloudWatch = null;
        try {
            amazonCloudWatch = AmazonCloudWatchClientBuilder.standard()
                    .withCredentials(new InstanceProfileCredentialsProvider(false))
                    .build();
        } catch (Exception e) {
            LOG.error("Unable to establish connection with AWS CloudWatch.");
        }

        return amazonCloudWatch;
    }

    public GetMetricStatisticsResult getCloudWatchMetricsForEC2(String instanceId, String metricName, long minutes, Integer period, String statistics) throws Exception {
        GetMetricStatisticsResult ec2Stats;
        long offsetInMilliseconds = 1000 * 60 * minutes;
        GetMetricStatisticsRequest request = new GetMetricStatisticsRequest()
                .withStartTime(new Date(new Date().getTime() - offsetInMilliseconds))
                .withNamespace(AWS_NAME_SPACE_EC2)
                .withPeriod(60 * period) //period in minutes
                .withDimensions(new Dimension().withName("InstanceId").withValue(instanceId))
                .withMetricName(metricName)
                .withStatistics(statistics)
                .withEndTime(new Date());
        try {
            if (Objects.nonNull(getCloudWatchClient())) {
                ec2Stats = getCloudWatchClient().getMetricStatistics(request);
                return ec2Stats;
            } else {
                ec2Stats = new GetMetricStatisticsResult();
                ec2Stats.setDatapoints(new ArrayList<>());

                return ec2Stats;
            }

        } catch (AmazonCloudWatchException e) {
            ec2Stats = new GetMetricStatisticsResult();
            ec2Stats.setDatapoints(new ArrayList<>());

            return ec2Stats;
        }
    }

    public GetMetricStatisticsResult getCloudWatchMetricsForNetworkELB(String loadBalancer, String metricName, long minutes, Integer period, String statistics) throws Exception {
        GetMetricStatisticsResult networkELBStats;
        long offsetInMilliseconds = 1000 * 60 * minutes;
        GetMetricStatisticsRequest request = new GetMetricStatisticsRequest()
                .withStartTime(new Date(new Date().getTime() - offsetInMilliseconds))
                .withNamespace(AWS_NAME_SPACE_NETWORK_ELB)
                .withPeriod(60 * period) //period in minutes
                .withDimensions(new Dimension().withName("LoadBalancer").withValue(loadBalancer))
                .withMetricName(metricName)
                .withStatistics(statistics)
                .withEndTime(new Date());
        try {
            if (Objects.nonNull(getCloudWatchClient())) {
                networkELBStats = getCloudWatchClient().getMetricStatistics(request);
                return networkELBStats;
            } else {
                networkELBStats = new GetMetricStatisticsResult();
                networkELBStats.setDatapoints(new ArrayList<>());

                return networkELBStats;
            }
        } catch (AmazonCloudWatchException e) {
            networkELBStats = new GetMetricStatisticsResult();
            networkELBStats.setDatapoints(new ArrayList<>());

            return networkELBStats;
        }
    }

    public GetMetricStatisticsResult getCloudWatchMetricsForClassicELB(String loadBalancer, String metricName, long minutes, Integer period, String statistics) throws Exception {
        GetMetricStatisticsResult classicELBStats;
        long offsetInMilliseconds = 1000 * 60 * minutes;
        GetMetricStatisticsRequest request = new GetMetricStatisticsRequest()
                .withStartTime(new Date(new Date().getTime() - offsetInMilliseconds))
                .withNamespace(AWS_NAME_SPACE_CLASSIC_ELB)
                .withPeriod(60 * period) //period in minutes
                .withDimensions(new Dimension().withName("LoadBalancerName").withValue(loadBalancer))
                .withMetricName(metricName)
                .withStatistics(statistics)
                .withEndTime(new Date());
        try {
            if (Objects.nonNull(getCloudWatchClient())) {
                classicELBStats = getCloudWatchClient().getMetricStatistics(request);
                return classicELBStats;
            } else {
                classicELBStats = new GetMetricStatisticsResult();
                classicELBStats.setDatapoints(new ArrayList<>());

                return classicELBStats;
            }
        } catch (AmazonCloudWatchException e) {
            classicELBStats = new GetMetricStatisticsResult();
            classicELBStats.setDatapoints(new ArrayList<>());

            return classicELBStats;
        }
    }

    public GetMetricStatisticsResult getCloudWatchMetricsForVolume(String volumeId, String metricName, long minutes, Integer period, String statistics) throws Exception {
        GetMetricStatisticsResult volumeStats;
        long offsetInMilliseconds = 1000 * 60 * minutes;
        GetMetricStatisticsRequest request = new GetMetricStatisticsRequest()
                .withStartTime(new Date(new Date().getTime() - offsetInMilliseconds))
                .withNamespace(AWS_NAME_SPACE_EBS_VOLUME)
                .withPeriod(60 * period) //period in minutes
                .withDimensions(new Dimension().withName("VolumeId").withValue(volumeId))
                .withMetricName(metricName)
                .withStatistics(statistics)
                .withEndTime(new Date());
        try {
            if (Objects.nonNull(getCloudWatchClient())) {
                volumeStats = getCloudWatchClient().getMetricStatistics(request);
                return volumeStats;
            } else {
                volumeStats = new GetMetricStatisticsResult();
                volumeStats.setDatapoints(new ArrayList<>());

                return volumeStats;
            }
        } catch (AmazonCloudWatchException e) {
            volumeStats = new GetMetricStatisticsResult();
            volumeStats.setDatapoints(new ArrayList<>());

            return volumeStats;
        }
    }
}