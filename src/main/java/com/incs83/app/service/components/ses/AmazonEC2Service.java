package com.incs83.app.service.components.ses;

import com.amazonaws.auth.InstanceProfileCredentialsProvider;
import com.amazonaws.services.ec2.AmazonEC2;
import com.amazonaws.services.ec2.AmazonEC2ClientBuilder;
import com.amazonaws.services.ec2.model.DescribeVolumesRequest;
import com.amazonaws.services.ec2.model.DescribeVolumesResult;
import com.amazonaws.services.ec2.model.Filter;
import com.amazonaws.services.ec2.model.Volume;
import com.incs83.exceptions.handler.ValidationException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

//@Service
public class AmazonEC2Service {
    private static final Logger LOG = LogManager.getLogger("org");

    @PostConstruct
    public AmazonEC2 getEC2Client() {
        AmazonEC2 amazonEC2 = null;
        try {
            amazonEC2 = AmazonEC2ClientBuilder.standard()
                    .withCredentials(new InstanceProfileCredentialsProvider(false))
                    .build();
        } catch (Exception e) {
            LOG.error("Unable to establish connection with Amazon EC2.");
        }

        return amazonEC2;
    }

    public List<Volume> getStatsForInstanceId(String instanceId) {
        AmazonEC2 client = getEC2Client();
        if (Objects.isNull(client))
            throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Error in Establishing connection with AWS EC2.");

        DescribeVolumesRequest request = new DescribeVolumesRequest().withFilters(
                new Filter().withName("attachment.instance-id").withValues(instanceId));
        DescribeVolumesResult response = client.describeVolumes(request);
        return response.getVolumes();
    }
}

