package com.incs83.app.service.components;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.exceptions.ApiException;
import com.incs83.exceptions.handler.ValidationException;
import com.incs83.service.HttpService;
import com.mongodb.util.JSON;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Objects;

import static com.incs83.app.constants.misc.ApplicationConstants.*;

@Service
public class HttpServiceImpl extends HttpService {
    private static final Logger LOG = LogManager.getLogger("org");

    public String doPut(StringEntity entity, String url, HashMap<String, String> headerInfo, HashMap<String, Object> queryParams) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String resp;
        try {
            StringBuffer params = new StringBuffer("");
            if (Objects.nonNull(queryParams) && !queryParams.isEmpty()) {
                queryParams.forEach((k, v) -> {
                    params.append(k).append("=").append(v).append("&");
                });
            }

            if (!params.toString().equals("")) {
                String queryParamsFinal = params.toString().substring(0, params.toString().length() - 1).toString();
                url = url + "?" + queryParamsFinal;
            }

            HttpPut httpPut = new HttpPut(url);
            headerInfo.forEach(httpPut::addHeader);
            httpPut.setEntity(entity);
            HttpResponse httpResponse = httpClient.execute(httpPut);
            resp = EntityUtils.toString(httpResponse.getEntity());
            if (httpResponse.getStatusLine().getStatusCode() != 200) {
                throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
            }

        } catch (Exception var15) {
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        } finally {
            try {
                httpClient.close();
            } catch (IOException var14) {
            }

        }

        return resp;
    }

    public String doPost(StringEntity entity, String url, HashMap<String, String> headerInfo, HashMap<String, Object> queryParams) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String resp;
        try {
            StringBuffer params = new StringBuffer("");
            if (Objects.nonNull(queryParams) && !queryParams.isEmpty()) {
                queryParams.forEach((k, v) -> {
                    params.append(k).append("=").append(v).append("&");
                });
            }

            if (!params.toString().equals("")) {
                String queryParamsFinal = params.toString().substring(0, params.toString().length() - 1).toString();
                url = url + "?" + queryParamsFinal;
            }

            HttpPost httpPost = new HttpPost(url);
            headerInfo.forEach(httpPost::addHeader);
            httpPost.setEntity(entity);
            HttpResponse httpResponse = httpClient.execute(httpPost);
            resp = EntityUtils.toString(httpResponse.getEntity());
            if (httpResponse.getStatusLine().getStatusCode() != 200) {
                throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
            }

        } catch (Exception var15) {
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        } finally {
            try {
                httpClient.close();
            } catch (IOException var14) {
            }

        }

        return resp;
    }

    public String doPost(UrlEncodedFormEntity entity, String url, HashMap<String, String> headerInfo) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String resp;
        try {
            HttpPost httpPost = new HttpPost(url);
            headerInfo.forEach(httpPost::addHeader);
            httpPost.setEntity(entity);
            HttpResponse httpResponse = httpClient.execute(httpPost);
            resp = EntityUtils.toString(httpResponse.getEntity());

            if (httpResponse.getStatusLine().getStatusCode() != 200) {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                String error = DEFAULT_ERROR;

                if (resp.contains(OAUTH_ERROR_KEY)) {
                    try {
                        HashMap<String, Object> errorHashMap = objectMapper.convertValue(JSON.parse(resp), HashMap.class);
                        if (Objects.nonNull(errorHashMap)) {
                            error = (String) (errorHashMap.get(OAUTH_ERROR_KEY));
                            error = error.substring(error.indexOf(COLON) + ONE, error.indexOf(DOT));
                        }
                    } catch (Exception e) {
                        throw new ValidationException(HttpStatus.BAD_REQUEST.value(), DEFAULT_ERROR);
                    }
                }
                throw new ValidationException(HttpStatus.BAD_REQUEST.value(), "Invalid Oauth config :: " + error.trim());
            }
        } finally {
            try {
                httpClient.close();
            } catch (IOException var14) {
            }
        }
        return resp;
    }

    public String doDelete(HashMap<String, Object> queryParams, String url, HashMap<String, String> headerInfo) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String resp;
        try {
            StringBuffer params = new StringBuffer("");
            if (Objects.nonNull(queryParams) && !queryParams.isEmpty()) {
                queryParams.forEach((k, v) -> {
                    params.append(k).append("=").append(v).append("&");
                });
            }

            if (!params.toString().equals("")) {
                String queryParamsFinal = params.toString().substring(0, params.toString().length() - 1).toString();
                url = url + "?" + queryParamsFinal;
            }

            HttpDelete httpDelete = new HttpDelete(url);
            headerInfo.forEach(httpDelete::addHeader);
            HttpResponse httpResponse = httpClient.execute(httpDelete);
            resp = EntityUtils.toString(httpResponse.getEntity());
            if (httpResponse.getStatusLine().getStatusCode() != 200) {
                throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
            }

        } catch (Exception ex) {
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        } finally {
            try {
                httpClient.close();
            } catch (IOException ioEx) {
            }

        }

        return resp;
    }
}
