package com.incs83.app.service.components;

import com.amazonaws.services.s3.AmazonS3;
import com.incs83.config.S3Config;
import com.incs83.service.S3Service;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Objects;

import static com.incs83.app.constants.misc.ApplicationConstants.HYPHEN_STRING;

@Service
public class S3ServiceImpl extends S3Service {
    private static final Logger LOG = LogManager.getLogger("org");

    @Autowired
    S3Config s3Config;

    public String getBuildVersionInfo(String pathAndName) {
        String line = HYPHEN_STRING;
        try {
            String bucket = this.s3Config.getBucket();
            if (Objects.nonNull(bucket) && !bucket.isEmpty()) {
                AmazonS3 s3Client = this.getS3Client();
                if (Objects.nonNull(s3Client)) {
                    if (s3Client.doesBucketExistV2(bucket)) {
                        BufferedReader reader = new BufferedReader(new InputStreamReader(s3Client.getObject(bucket, pathAndName).getObjectContent()));
                        while ((line = reader.readLine()) != null) {
                            line = line.trim();
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Error while reading content from s3 bucket.", e);
        }

        return line;
    }
}
