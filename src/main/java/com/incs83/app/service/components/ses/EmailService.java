package com.incs83.app.service.components.ses;

/**
 * <AUTHOR>
 * @Created 23/08/18
 */
public interface EmailService {
    void sendSimpleMessage(String to, String subject, String text, String from);

    void sendSimpleMessage(String[] to, String subject, String text, String from);

    void sendSimpleMessage(String to, String cc, String bcc, String subject, String text, String from);

    void sendSimpleMessage(String[] to, String[] cc, String[] bcc, String subject, String text, String from);
}
