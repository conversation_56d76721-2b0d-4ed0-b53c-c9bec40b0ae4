package com.incs83.app.service.repository;

import com.datastax.driver.core.Cluster;
import com.datastax.driver.core.ColumnMetadata;
import com.datastax.driver.core.ResultSet;
import com.datastax.driver.core.Session;
import com.datastax.driver.core.querybuilder.Clause;
import com.datastax.driver.core.querybuilder.QueryBuilder;
import com.datastax.driver.core.querybuilder.Select;
import com.datastax.driver.mapping.Mapper;
import com.datastax.driver.mapping.MappingManager;
import com.datastax.driver.mapping.Result;
import com.incs83.abstraction.ApiResponseCode;
import com.incs83.app.config.CassandraConfig;
import com.incs83.exceptions.ApiException;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;

@Repository
public class CassandraRepository<T> {
    private static final Logger LOG = LogManager.getLogger(CassandraRepository.class.getName());

    @Autowired
    private CassandraConfig cassandraConfig;

    private Cluster cluster;

    private Session session;

    private MappingManager mappingManager;

    @PostConstruct
    public void init() {
        if (cassandraConfig.isCassandraConnect()) {
            Cluster.Builder builder = Cluster.builder().addContactPoints(cassandraConfig.getServer().split(COMMA)).withPort(cassandraConfig.getPort());
            if (cassandraConfig.isAuthEnabled()) {
                builder.withCredentials(cassandraConfig.getUsername(), cassandraConfig.getPassword());
            }
            cluster = builder.build();
            if (Objects.nonNull(cluster)) {
                session = cluster.connect(cassandraConfig.getDatabase());
                mappingManager = new MappingManager(session);
            }
        }
    }

    public void create(Class type, T resource) {
        Mapper<T> greetingMapper = mappingManager.mapper(type);
        try {
            greetingMapper.save(resource);
        } catch (Exception e) {
            throw new ApiException(ApiResponseCode.ERROR_PROCESSING_REQUEST);
        }
    }


    public List read(Class type, HashMap<String, Object> param, HashMap<String, Object> rangeParam, HashMap<String, List> inparam, List<String> projection, long minutes) {
        Mapper mapper = mappingManager.mapper(type);

        long t1 = Calendar.getInstance().getTimeInMillis();
        long timestamp = t1 - minutes * 60L * 1000L;

        try {
            Select selectQuery = QueryBuilder.select(projection.isEmpty() ? mapper.getTableMetadata().getColumns().stream().map(ColumnMetadata::getName).toArray() : projection.toArray()).from(mapper.getTableMetadata().getKeyspace().getName(), mapper.getTableMetadata().getName());
            if (Objects.nonNull(param)) {
                for (Map.Entry<String, Object> entry : param.entrySet()) {
                    Clause clause = QueryBuilder.eq(entry.getKey(), entry.getValue());
                    selectQuery.where().and(clause);
                }
            }
            if (Objects.nonNull(inparam) && !inparam.isEmpty()) {
                for (Map.Entry<String, List> entry : inparam.entrySet()) {
                    Clause clause = QueryBuilder.in(entry.getKey(), entry.getValue().toArray());
                    selectQuery.where().and(clause);
                }
            }
            if (Objects.nonNull(rangeParam) && !rangeParam.isEmpty()) {
                if (rangeParam.get("operand").equals("gt")) {
                    Clause clause = QueryBuilder.gte(rangeParam.get("key").toString(), timestamp);
                    selectQuery.where().and(clause);
                }
            }

//            LOG.info("################################# select query : "+selectQuery);
            ResultSet results = session.execute(selectQuery);
            Result result = mapper.map(results);
            return result.all();

        } catch (Exception e) {
            LOG.error("Error while fetching data ", e);
            ArrayList arrayList = new ArrayList();
            return arrayList;
        }

    }
}
