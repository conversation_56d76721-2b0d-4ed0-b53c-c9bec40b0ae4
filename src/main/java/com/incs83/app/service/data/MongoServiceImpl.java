package com.incs83.app.service.data;

import com.incs83.app.business.v2.UtilityServices;
import com.incs83.mt.MongoTenantTemplate;
import com.incs83.service.MongoService;
import com.mongodb.AggregationOptions;
import com.mongodb.AggregationOutput;
import com.mongodb.BasicDBObject;
import com.mongodb.CommandResult;
import com.mongodb.Cursor;
import com.mongodb.DBCursor;
import com.mongodb.DBObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;
import static com.incs83.app.constants.misc.ApplicationConstants.DESC;
import static com.incs83.app.constants.misc.ApplicationConstants.TWO_DECIMAL_PLACE;

/**
 * Created by rosrivas on 6/23/17.
 */
@Service
@SuppressWarnings("unchecked")
public class MongoServiceImpl<T> extends MongoService<T> {
    private static final Logger LOG = LogManager.getLogger("mongo");
    @Autowired
    MongoTenantTemplate mongoTemplate;
    @Autowired
    UtilityServices utilityServices;

    public HashMap<String, Set<String>> listCollections() {
        HashMap<String, Set<String>> collectionData = new HashMap<>();
        Set<String> mongoCollections = mongoTemplate.getDb().getCollectionNames();
        BasicDBObject mongoFieldOptions = new BasicDBObject();
        mongoCollections.forEach(collection -> {
            mongoFieldOptions.clear();
            mongoFieldOptions.put("_id", 0);
            mongoFieldOptions.put("dateCreated", 0);
            mongoFieldOptions.put("id", 0);
            mongoFieldOptions.put("timestamp", 0);
            mongoFieldOptions.put("createdAt", 0);
            mongoFieldOptions.put("createdBy", 0);
            mongoFieldOptions.put("_class", 0);
            mongoFieldOptions.put("hour", 0);
            DBObject fields = mongoTemplate.getCollection(collection).findOne(new BasicDBObject(), mongoFieldOptions);
            if (Objects.nonNull(fields)) {
                String userId = (String) fields.get("userId");
                if (Objects.nonNull(userId)) {
                    collectionData.put(collection, fields.keySet());
                }
            }
        });
        return collectionData;
    }


    public Set<String> getAllCollectionList() throws Exception {
        Set<String> mongoCollections = mongoTemplate.getDb().getCollectionNames();
        return mongoCollections;
    }


    public List<LinkedHashMap> getIndexesForCollection(String collection) {
        List<LinkedHashMap> listOfIndexes = new ArrayList<>();
        List<DBObject> indexList = mongoTemplate.getCollection(collection).getIndexInfo();
        for (DBObject index : indexList) {
            if (Objects.nonNull(index.get("key"))) {
                LinkedHashMap ttlKey = (LinkedHashMap) index.get("key");
                Set<Entry<String, Object>> map = ttlKey.entrySet();
                Map.Entry<Integer, Integer> entry = (Entry) map.iterator().next();
                ttlKey.put("name", entry.getKey());
                if (Objects.nonNull(ttlKey.keySet()) && ttlKey.keySet().iterator().hasNext() && Objects.nonNull(index.get("expireAfterSeconds"))) {
                    ttlKey.put("expireAfterDays", TimeUnit.SECONDS.toDays(Double.valueOf(Objects.isNull(index.get("expireAfterSeconds")) ? "0" : index.get("expireAfterSeconds").toString()).longValue()));
                    listOfIndexes.add(ttlKey);
                }
            }
        }
        return listOfIndexes;
    }

    public Optional<LinkedHashMap> getTtlIndexFromKey(String collection, String key) {
        List<LinkedHashMap> listOfIndexes = getIndexesForCollection(collection);
        Optional<LinkedHashMap> op = listOfIndexes.stream().filter(index->StringUtils.equals(String.valueOf(index.get("name")), key) && Objects.nonNull(index.get("expireAfterDays"))).findFirst();
        return op;
    }

    public void updateTTLIndex(String collection, String indexField, int order, long ttl) {
        DBObject key = new BasicDBObject();
        key.put(indexField, order);
        mongoTemplate.getDb().getCollection(collection).dropIndex(key);
        DBObject options = new BasicDBObject();
        options.put("expireAfterSeconds", ttl);
        mongoTemplate.getDb().getCollection(collection).createIndex(key, options);
    }

    public DBObject findCollectionIndex(String collection) {
        return mongoTemplate.getCollection(collection).findOne();
    }

    public void insertDataInCollection(String collection, DBObject object) {
        mongoTemplate.getCollection(collection).insert(object);
    }

    public void updateTTLForIndex(String collection, String indexField, long ttl) {

        BasicDBObject command = new BasicDBObject();
        command.put("collMod", collection);

        BasicDBObject keyPattern = new BasicDBObject();
        keyPattern.put(indexField, -1);

        BasicDBObject index = new BasicDBObject();
        index.put("keyPattern", keyPattern);
        index.put("expireAfterSeconds", ttl);

        command.put("index", index);

        CommandResult commandResult = mongoTemplate.getDb().command(command);
    }


    public void removeTTLForIndex(String collection, String indexField) throws Exception {
        DBObject key = new BasicDBObject();
        key.put(indexField, -1);
        mongoTemplate.getDb().getCollection(collection).dropIndex(key);

        BasicDBObject index = new BasicDBObject();
        index.put(indexField, -1);
        mongoTemplate.getDb().getCollection(collection).createIndex(index);
    }

    public boolean findCollection(String collection) {
        return mongoTemplate.collectionExists(collection);
    }

    public DBObject aggregateDataForAllOrResourceId(HashMap<String, Object> aggParams, HashMap<String, Object> inParams, String collection, Calendar dateCriteria, HashMap<String, String> params, String matchCiteria) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject matchCriteria = new BasicDBObject();
        BasicDBObject groupCriteria = new BasicDBObject();
        if (Objects.nonNull(aggParams)) {
            aggParams.forEach((k, v) -> {
                matchCriteria.put(k, new BasicDBObject("$eq", v));
            });
        }

        if (Objects.nonNull(inParams) && inParams.get("in") != null) {
            matchCriteria.put(String.valueOf(((HashMap) inParams.get("in")).get("resourceName")), new BasicDBObject("$in", ((HashMap) inParams.get("in")).get("resource")));
        }

        if (Objects.nonNull(matchCiteria) && matchCiteria.equals("timestamp")) {
            matchCriteria.put(matchCiteria, new BasicDBObject((String) params.get("operand"), dateCriteria.getTimeInMillis()));
        } else if (Objects.nonNull(matchCiteria) && matchCiteria.equals("date")) {
            matchCriteria.put(matchCiteria, new BasicDBObject((String) params.get("operand"), new Date(dateCriteria.getTimeInMillis())));
        } else if (Objects.nonNull(matchCiteria) && matchCiteria.equals("dateCreated")) {
            matchCriteria.put(matchCiteria, new BasicDBObject((String) params.get("operand"), dateCriteria.getTime()));
        }
        groupCriteria.put("_id", params.get("label"));

        for (int i = 0; i < ((String) params.get("outputParams")).split(",").length; ++i) {
            groupCriteria.put(((String) params.get("outputParams")).split(",")[i], new BasicDBObject((String) params.get("operation"), ((String) params.get("keyToAggregate")).split(",")[i]));
        }

        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(Arrays.asList(new BasicDBObject("$match", matchCriteria), new BasicDBObject("$group", groupCriteria)));
        DBObject resultObject = null;

        for (Iterator resultItr = aggregationResult.results().iterator(); resultItr.hasNext(); resultObject = (DBObject) resultItr.next()) {
            ;
        }

        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: \t" + collection + "\t" + aggParams + "------" + inParams + "------" + params + "----" + matchCiteria + "-----Time Taken ::: " + (t2 - t1));
        }

        return resultObject;
    }

    public DBObject aggregateDataForAllOrResourceIdV2(HashMap<String, Object> aggParams, HashMap<String, Object> inParams, String collection, Calendar dateCriteria, HashMap<String, String> params, String matchCiteria) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject matchCriteria = new BasicDBObject();
        BasicDBObject groupCriteria = new BasicDBObject();
        if (Objects.nonNull(aggParams)) {
            aggParams.forEach((k, v) -> {
                matchCriteria.put(k, new BasicDBObject("$eq", v));
            });
        }

        if (Objects.nonNull(inParams) && inParams.get("in") != null) {
            matchCriteria.put(String.valueOf(((HashMap) inParams.get("in")).get("resourceName")), new BasicDBObject("$in", ((HashMap) inParams.get("in")).get("resource")));
        }

        if (Objects.nonNull(matchCiteria) && matchCiteria.equals("timestamp")) {
            matchCriteria.put(matchCiteria, new BasicDBObject((String) params.get("operand"), dateCriteria.getTime()));
        } else if (Objects.nonNull(matchCiteria) && matchCiteria.equals("date")) {
            matchCriteria.put(matchCiteria, new BasicDBObject((String) params.get("operand"), new Date(dateCriteria.getTimeInMillis())));
        } else if (matchCiteria.equals("dateCreated")) {
            matchCriteria.put(matchCiteria, new BasicDBObject((String) params.get("operand"), dateCriteria.getTime()));
        }

        groupCriteria.put("_id", params.get("label"));

        for (int i = 0; i < ((String) params.get("outputParams")).split(",").length; ++i) {
            groupCriteria.put(((String) params.get("outputParams")).split(",")[i], new BasicDBObject((String) params.get("operation"), ((String) params.get("keyToAggregate")).split(",")[i]));
        }

        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(Arrays.asList(new BasicDBObject("$match", matchCriteria), new BasicDBObject("$group", groupCriteria)));
        DBObject resultObject = null;

        for (Iterator resultItr = aggregationResult.results().iterator(); resultItr.hasNext(); resultObject = (DBObject) resultItr.next()) {
            ;
        }

        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: \t" + collection + "\t" + aggParams + "------" + inParams + "------" + params + "----" + matchCiteria + "-----Time Taken ::: " + (t2 - t1));
        }

        return resultObject;
    }

    public DBObject aggregateDataForAllOrResourceIdV3(HashMap<String, Object> aggParams, HashMap<String, Object> inParams, HashMap<String, String> params, String collection, HashMap<String, Object> timeParams) {

        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject matchCriteria = new BasicDBObject();
        BasicDBObject projectCriteria = new BasicDBObject();
        BasicDBObject timeMatchCriteria = new BasicDBObject();
        BasicDBObject groupCriteria = new BasicDBObject();

        ZonedDateTime sTime = (ZonedDateTime) timeParams.get("ldtTime");

        try {
            matchCriteria.put("statsDateTime", new BasicDBObject("$in", timeParams.get("dates")));
        } catch (Exception ex) {
            LOG.error(ex);
        }

        if (Objects.nonNull(aggParams)) {
            aggParams.forEach((k, v) -> {
                matchCriteria.put(k, new BasicDBObject("$eq", v));
            });
        }

        if (Objects.nonNull(inParams) && inParams.get("in") != null) {
            matchCriteria.put(String.valueOf(((HashMap) inParams.get("in")).get("resourceName")), new BasicDBObject("$in", ((HashMap) inParams.get("in")).get("resource")));
        }

        projectCriteria.put("statsData", new BasicDBObject("$objectToArray", "$statsData"));
        timeMatchCriteria.put("statsData.v.recordDateTime", new BasicDBObject("$gte", Date.from(sTime.toInstant())));
        groupCriteria.put("_id", params.get("label"));

        for (int i = 0; i < ((String) params.get("outputParams")).split(",").length; ++i) {
            groupCriteria.put(((String) params.get("outputParams")).split(",")[i], new BasicDBObject((String) params.get("operation"), ((String) params.get("keyToAggregate")).split(",")[i]));
        }

        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(Arrays.asList(
                new BasicDBObject("$match", matchCriteria),
                new BasicDBObject("$project", projectCriteria),
                new BasicDBObject("$unwind", "$statsData"),
                new BasicDBObject("$match",timeMatchCriteria),
                new BasicDBObject("$group", groupCriteria)));

        DBObject resultObject = null;
        for (Iterator resultItr = aggregationResult.results().iterator(); resultItr.hasNext(); resultObject = (DBObject) resultItr.next()) {
            ;
        }

        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: {} --- {} --- {} --- {} --- Time Taken ::: {}", collection, aggParams, inParams, params, (t2 - t1));
        }

        return resultObject;
    }

    public DBObject getLastStatsOneRecord(HashMap<String, Object> queryParams, String collection) {

        BasicDBObject matchCriteria = new BasicDBObject();
        BasicDBObject projectCriteria = new BasicDBObject();

        BasicDBObject sortDate = new BasicDBObject();
        BasicDBObject sortHour = new BasicDBObject();

        if (Objects.nonNull(queryParams)) {
            queryParams.forEach((k, v) -> {
                matchCriteria.put(k, new BasicDBObject("$eq", v));
            });
        }

        projectCriteria.put("statsDateTime", 1);
        projectCriteria.put("isp", 1);
        projectCriteria.put("statsData", new BasicDBObject("$objectToArray", "$statsData"));

        sortDate.put("statsDateTime", DESC);
        sortHour.put("statsData.k", DESC);

        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(Arrays.asList(
                new BasicDBObject("$match", matchCriteria),
                new BasicDBObject("$sort", sortDate),
                new BasicDBObject("$limit", 1),
                new BasicDBObject("$project", projectCriteria),
                new BasicDBObject("$unwind", "$statsData"),
                new BasicDBObject("$sort", sortHour),
                new BasicDBObject("$limit", 1)));

        DBObject resultObject = null;
        for (Iterator resultItr = aggregationResult.results().iterator(); resultItr.hasNext(); resultObject = (DBObject) resultItr.next()) {
            ;
        }

        return resultObject;
    }

    public DBObject aggregateDataForLastData(HashMap<String, Object> aggParams, HashMap<String, Object> inParams, HashMap<String, String> params, String collection) {
        BasicDBObject projectCriteria = new BasicDBObject();
        BasicDBObject matchCriteria = new BasicDBObject();
        BasicDBObject matchHour = new BasicDBObject();
        BasicDBObject groupCriteria = new BasicDBObject();

        projectCriteria.put("date", 1);
        projectCriteria.put("isp", 1);
        projectCriteria.put("statsData", new BasicDBObject("$objectToArray", "$statsData"));

        if (Objects.nonNull(aggParams)) {
            matchCriteria.put("statsDateTime", new BasicDBObject("$in", aggParams.get("statsDateTime")));
            matchHour.put("statsData.k", new BasicDBObject("$eq", aggParams.get("hour")));
        }

        if (Objects.nonNull(inParams) && inParams.get("in") != null) {
            matchCriteria.put(String.valueOf(((HashMap) inParams.get("in")).get("resourceName")), new BasicDBObject("$in", ((HashMap) inParams.get("in")).get("resource")));
        }

        groupCriteria.put("_id", params.get("label"));
        for (int i = 0; i < ((String) params.get("outputParams")).split(",").length; ++i) {
            groupCriteria.put(((String) params.get("outputParams")).split(",")[i], new BasicDBObject((String) params.get("operation"), ((String) params.get("keyToAggregate")).split(",")[i]));
        }

        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(Arrays.asList(
                new BasicDBObject("$match", matchCriteria),
                new BasicDBObject("$project", projectCriteria),
                new BasicDBObject("$unwind", "$statsData"),
                new BasicDBObject("$match", matchHour),
                new BasicDBObject("$group", groupCriteria)));

        DBObject resultObject = null;
        for (Iterator resultItr = aggregationResult.results().iterator(); resultItr.hasNext(); resultObject = (DBObject) resultItr.next()) {
            ;
        }

        return resultObject;
    }

    public List<HashMap<String, Object>> generateHistoricalGraphDataFromDataForColumnChart(String keyToRead, Double dataForToday, Double dataForLast7Days, Double dataForLast30Days, Double dataForLast90Days) {
        List<HashMap<String, Object>> graphDataHistorical = new ArrayList();
        HashMap<String, Object> dataMapForToday = new HashMap();
        HashMap<String, Object> dataMapForLast7Days = new HashMap();
        HashMap<String, Object> dataMapForLast30Days = new HashMap();
        HashMap<String, Object> dataMapForLast90Days = new HashMap();
        dataMapForToday.put("Day", "Today");

        int i;
        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForToday.put(keyToRead.split(",")[i], dataForToday == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForToday)));
        }

        dataMapForLast7Days.put("Day", "Last 7 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast7Days.put(keyToRead.split(",")[i], dataForLast7Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForLast7Days)));
        }

        dataMapForLast30Days.put("Day", "Last 30 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast30Days.put(keyToRead.split(",")[i], dataForLast30Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForLast30Days)));
        }

        dataMapForLast90Days.put("Day", "Last 90 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast90Days.put(keyToRead.split(",")[i], dataForLast90Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForLast90Days)));
        }

        graphDataHistorical.add(dataMapForToday);
        graphDataHistorical.add(dataMapForLast7Days);
        graphDataHistorical.add(dataMapForLast30Days);
        graphDataHistorical.add(dataMapForLast90Days);
        return graphDataHistorical;
    }

    public List<BasicDBObject> aggregate(HashMap<String, Object> aggParams, HashMap<String, Object> inParams, String collection, Calendar dateCriteria, HashMap<String, String> params, String matchCiteria, HashMap<String, Object> projection) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject matchCriteria = new BasicDBObject();
        BasicDBObject projectionCriteria = new BasicDBObject();
        BasicDBObject groupCriteria = new BasicDBObject();

        if (Objects.nonNull(aggParams)) {
            aggParams.forEach((k, v) -> {
                matchCriteria.put(k, new BasicDBObject("$eq", v));
            });
        }
        if (Objects.nonNull(projection)) {
            projectionCriteria.putAll(projection);
        }

        if (Objects.nonNull(inParams) && inParams.get("in") != null) {
            matchCriteria.put(String.valueOf(((HashMap) inParams.get("in")).get("resourceName")), new BasicDBObject("$in", ((HashMap) inParams.get("in")).get("resource")));
        }

        if (Objects.nonNull(matchCiteria)) {
            if (matchCiteria.equals("timestamp")) {
                matchCriteria.put(matchCiteria, new BasicDBObject(params.get("operand"), dateCriteria.getTimeInMillis()));
            } else if (matchCiteria.equals("date")) {
                matchCriteria.put(matchCiteria, new BasicDBObject(params.get("operand"), new Date(dateCriteria.getTimeInMillis())));
            } else if (matchCiteria.equals("dateCreated")) {
                matchCriteria.put(matchCiteria, new BasicDBObject(params.get("operand"), dateCriteria.getTime()));
            }
        }

        groupCriteria.put("_id", params.get("label"));

        for (int i = 0; i < (params.get("outputParams")).split(COMMA).length; ++i) {
            groupCriteria.put((params.get("outputParams")).split(",")[i], new BasicDBObject((params.get("operation")).split(COMMA)[i], getKeyToAggregate((params.get("keyToAggregate")).split(COMMA)[i])));
        }

        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(Arrays.asList(new BasicDBObject("$match", matchCriteria), new BasicDBObject("$group", groupCriteria), new BasicDBObject("$project", projectionCriteria)));

        List<BasicDBObject> devicesList = new ArrayList<>();

        for (Iterator resultItr = aggregationResult.results().iterator(); resultItr.hasNext(); devicesList.add((BasicDBObject) resultItr.next()))
            ;
        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: \t" + collection + "\t" + aggParams + "------" + inParams + "------" + params + "----" + matchCiteria + "-----Time Taken ::: " + (t2 - t1));
        }

        return devicesList;
    }

    private Object getKeyToAggregate(String key) {
        if (key.equals("1")) {
            return 1;
        }
        return key;
    }

    public long count(HashMap<String, Object> params, HashMap<String, Object> inParams, String collection) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject query = new BasicDBObject();
        params.forEach(query::put);

        if (Objects.nonNull(inParams) && inParams.get("in") != null) {
            query.put(String.valueOf(((HashMap) inParams.get("in")).get("resourceName")), new BasicDBObject("$in", ((HashMap) inParams.get("in")).get("resource")));
        }

        long count = this.mongoTemplate.getCollection(collection).count(query);
        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: \t" + collection + "\t" + params + "\t" + inParams + "---------Time Taken ::: " + (t2 - t1));
        }

        return count;
    }

    public long distinctCount(HashMap<String, Object> params, HashMap<String, Object> inParams, String collection, String distinctField) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject query = new BasicDBObject();

        if (Objects.nonNull(params)) {
            params.forEach(query::put);
        }

        if (Objects.nonNull(inParams) && inParams.get("in") != null) {
            query.put(String.valueOf(((HashMap) inParams.get("in")).get("resourceName")), new BasicDBObject("$in", ((HashMap) inParams.get("in")).get("resource")));
        }

        long count = this.mongoTemplate.getCollection(collection).distinct(distinctField, query).size();
        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: \t" + collection + "\t" + params + "---------Time Taken ::: " + (t2 - t1));
        }

        return count;
    }


    public DBObject findOne(HashMap<String, Object> queryParams, HashMap<String, Object> appendableParams, String collection, BasicDBObject fieldsToRemove) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject query = new BasicDBObject();
        queryParams.forEach(query::put);
        appendableParams.forEach(query::append);
        DBCursor cursor = this.mongoTemplate.getCollection(collection).find(query, fieldsToRemove);
        if (cursor.hasNext()) {
            DBObject result = cursor.next();
            long t2 = Calendar.getInstance().getTimeInMillis();
            if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
                LOG.info("Collection Name :: \t" + collection + "\t" + queryParams + "-------Time Taken ::: " + (t2 - t1));
            }

            return result;
        } else {
            return null;
        }
    }

//    public void insertAll(List<BasicDBObject> recordList, String collection) {
//        this.mongoTemplate.getCollection(collection).insert(recordList);
//    }

    public void findAndModify(HashMap<String, Object> params, String collection, String sortBy, BasicDBObject basicDBObject) {
        BasicDBObject query = new BasicDBObject();
        BasicDBObject sort = new BasicDBObject();
        params.forEach(query::put);
        if (Objects.nonNull(sortBy))
            sort.put(sortBy, Integer.valueOf(-1));
        this.mongoTemplate.getCollection(collection).findAndModify(query, (DBObject) null, sort, false, basicDBObject, true, false);
    }

    public List<DBObject> findListByTimestamp(HashMap<String, Object> queryParams, String collection, String sortBy, long minutes, int sortType, BasicDBObject fieldsToRemove) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject query = new BasicDBObject();
        queryParams.forEach(query::put);
        BasicDBObject sort = new BasicDBObject();


        if (sortBy.equalsIgnoreCase("timestamp")) {
            query.put("timestamp", new BasicDBObject("$gt", t1 - minutes * 60L * 1000L));
        } else if (sortBy.equalsIgnoreCase("dateCreated")) {
            query.put("dateCreated", new BasicDBObject("$gt", new Date(t1 - minutes * 60L * 1000L)));
        }


        sort.put(sortBy, sortType);
        DBCursor cursor = this.mongoTemplate.getCollection(collection).find(query, fieldsToRemove).sort(sort);
        ArrayList result = new ArrayList();

        while (cursor.hasNext()) {
            result.add(cursor.next());
        }

        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: \t" + collection + "\t" + queryParams + "----" + sortBy + "-----Time Taken ::: " + (t2 - t1));
        }

        return result;
    }

    public List<HashMap<String, Object>> generateHistoricalGraphDataFromDataV2(String keyToRead, DBObject dataForToday, DBObject dataForLast7Days, DBObject dataForLast30Days, DBObject dataForLast90Days) {
        List<HashMap<String, Object>> graphDataHistorical = new ArrayList();
        HashMap<String, Object> dataMapForToday = new HashMap();
        HashMap<String, Object> dataMapForLast7Days = new HashMap();
        HashMap<String, Object> dataMapForLast30Days = new HashMap();
        HashMap<String, Object> dataMapForLast90Days = new HashMap();
        dataMapForToday.put("Day", "Last 24 Hours");

        int i;
        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForToday.put(keyToRead.split(",")[i], dataForToday == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForToday.get(keyToRead.split(",")[i]) != null && !dataForToday.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForToday.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast7Days.put("Day", "Last 7 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast7Days.put(keyToRead.split(",")[i], dataForLast7Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast7Days.get(keyToRead.split(",")[i]) != null && !dataForLast7Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast7Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast30Days.put("Day", "Last 30 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast30Days.put(keyToRead.split(",")[i], dataForLast30Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast30Days.get(keyToRead.split(",")[i]) != null && !dataForLast30Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast30Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        dataMapForLast90Days.put("Day", "Last 90 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast90Days.put(keyToRead.split(",")[i], dataForLast90Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(Double.parseDouble(dataForLast90Days.get(keyToRead.split(",")[i]) != null && !dataForLast90Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast90Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)))));
        }

        graphDataHistorical.add(dataMapForToday);
        graphDataHistorical.add(dataMapForLast7Days);
        graphDataHistorical.add(dataMapForLast30Days);
        graphDataHistorical.add(dataMapForLast90Days);
        return graphDataHistorical;
    }

    public List<HashMap<String, Object>> generateHistoricalGraphDataFromDataV3(String keyToRead, String equipmentType, DBObject dataForToday, DBObject dataForLast7Days, DBObject dataForLast30Days, DBObject dataForLast90Days) {
        List<HashMap<String, Object>> graphDataHistorical = new ArrayList();
        HashMap<String, Object> dataMapForToday = new HashMap();
        HashMap<String, Object> dataMapForLast7Days = new HashMap();
        HashMap<String, Object> dataMapForLast30Days = new HashMap();
        HashMap<String, Object> dataMapForLast90Days = new HashMap();
        dataMapForToday.put("Day", "Last 24 Hours");
        dataMapForToday.put("EquipmentType", equipmentType);

        int i;
        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForToday.put(keyToRead.split(",")[i], dataForToday == null ? 0 : Long.parseLong(dataForToday.get(keyToRead.split(",")[i]) != null && !dataForToday.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForToday.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)));
        }

        dataMapForLast7Days.put("Day", "Last 7 days");
        dataMapForLast7Days.put("EquipmentType", equipmentType);

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast7Days.put(keyToRead.split(",")[i], dataForLast7Days == null ? 0 : Long.parseLong(dataForLast7Days.get(keyToRead.split(",")[i]) != null && !dataForLast7Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast7Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)));
        }

        dataMapForLast30Days.put("Day", "Last 30 days");
        dataMapForLast30Days.put("EquipmentType", equipmentType);

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast30Days.put(keyToRead.split(",")[i], dataForLast30Days == null ? 0 : Long.parseLong(dataForLast30Days.get(keyToRead.split(",")[i]) != null && !dataForLast30Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast30Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)));
        }

        dataMapForLast90Days.put("Day", "Last 90 days");
        dataMapForLast90Days.put("EquipmentType", equipmentType);

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast90Days.put(keyToRead.split(",")[i], dataForLast90Days == null ? 0 : Long.parseLong(dataForLast90Days.get(keyToRead.split(",")[i]) != null && !dataForLast90Days.get(keyToRead.split(",")[i]).toString().equals("NaN") ? dataForLast90Days.get(keyToRead.split(",")[i]).toString() : String.valueOf(0)));
        }

        graphDataHistorical.add(dataMapForToday);
        graphDataHistorical.add(dataMapForLast7Days);
        graphDataHistorical.add(dataMapForLast30Days);
        graphDataHistorical.add(dataMapForLast90Days);
        return graphDataHistorical;
    }

    public List<HashMap<String, Object>> generateHistoricalGraphDataFromDataForColumnChartV2(String keyToRead, Double dataForToday, Double dataForLast7Days, Double dataForLast30Days, Double dataForLast90Days) {
        List<HashMap<String, Object>> graphDataHistorical = new ArrayList();
        HashMap<String, Object> dataMapForToday = new HashMap();
        HashMap<String, Object> dataMapForLast7Days = new HashMap();
        HashMap<String, Object> dataMapForLast30Days = new HashMap();
        HashMap<String, Object> dataMapForLast90Days = new HashMap();
        dataMapForToday.put("Day", "Last 24 Hours");

        int i;
        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForToday.put(keyToRead.split(",")[i], dataForToday == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForToday)));
        }

        dataMapForLast7Days.put("Day", "Last 7 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast7Days.put(keyToRead.split(",")[i], dataForLast7Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForLast7Days)));
        }

        dataMapForLast30Days.put("Day", "Last 30 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast30Days.put(keyToRead.split(",")[i], dataForLast30Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForLast30Days)));
        }

        dataMapForLast90Days.put("Day", "Last 90 days");

        for (i = 0; i < keyToRead.split(",").length; ++i) {
            dataMapForLast90Days.put(keyToRead.split(",")[i], dataForLast90Days == null ? 0.0D : Double.parseDouble(TWO_DECIMAL_PLACE.format(dataForLast90Days)));
        }

        graphDataHistorical.add(dataMapForToday);
        graphDataHistorical.add(dataMapForLast7Days);
        graphDataHistorical.add(dataMapForLast30Days);
        graphDataHistorical.add(dataMapForLast90Days);
        return graphDataHistorical;
    }

    public DBObject findOne(HashMap<String, Object> queryParams, String collection, BasicDBObject sort, BasicDBObject fieldsToRemove) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        BasicDBObject query = new BasicDBObject();
        queryParams.forEach(query::put);
        DBCursor cursor = this.mongoTemplate.getCollection(collection).find(query, fieldsToRemove).sort(sort);
        if (cursor.hasNext()) {
            DBObject result = cursor.next();
            long t2 = Calendar.getInstance().getTimeInMillis();
            if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
                LOG.info("Collection Name :: \t" + collection + "\t" + queryParams + "----" + sort + "-----Time Taken ::: " + (t2 - t1));
            }

            return result;
        } else {
            return null;
        }
    }

    public HashMap<String, String> mongoHealthCheckup() throws Exception {
        CommandResult cr = mongoTemplate.getDb().command(new BasicDBObject().append("serverStatus", 1).append("repl", 0).append("metrics", 0).append("locks", 0));
        HashMap<String, String> result = new HashMap<>();
        result.put("version", cr.getString("version"));
        result.put("uptime", cr.getString("uptime"));
        return result;
    }

    public List<DBObject> findList(String collection, HashMap<String, Object> params, String sortBy, int sortType) {
        BasicDBObject query = new BasicDBObject();
        BasicDBObject sort = new BasicDBObject();
        sort.put(sortBy, sortType);
        params.forEach(query::put);
        DBCursor cursor = this.mongoTemplate.getCollection(collection).find(query).sort(sort);
        ArrayList result = new ArrayList();

        while (cursor.hasNext()) {
            result.add(cursor.next());
        }

        return result;
    }

    public List<DBObject> findList(String collection, HashMap<String, Object> params, String sortBy, int sortType, BasicDBObject projection) {
        BasicDBObject query = new BasicDBObject();
        BasicDBObject sort = new BasicDBObject();
        sort.put(sortBy, sortType);
        params.forEach(query::put);
        DBCursor cursor = this.mongoTemplate.getCollection(collection).find(query, projection).sort(sort);
        ArrayList result = new ArrayList();

        while (cursor.hasNext()) {
            result.add(cursor.next());
        }

        return result;
    }

    public List<DBObject> aggregationQueryForPagination(HashMap<String, Object> param, String collection, BasicDBObject projection, String sortBy, int sortType, int max, int offset) {
        List<BasicDBObject> pipeline = new ArrayList();

        BasicDBObject match = new BasicDBObject();
        match.put("$match", param);
        pipeline.add(match);

        BasicDBObject sort = new BasicDBObject();
        sort.put("$sort", new BasicDBObject(sortBy, sortType));
        pipeline.add(sort);

        BasicDBObject project = new BasicDBObject();
        project.put("$project", projection);
        pipeline.add(project);

        BasicDBObject skip = new BasicDBObject();
        skip.put("$skip", offset);
        pipeline.add(skip);

        BasicDBObject limit = new BasicDBObject();
        limit.put("$limit", max);
        pipeline.add(limit);


        Cursor cursor = this.mongoTemplate.getCollection(collection).aggregate(pipeline, AggregationOptions.builder().allowDiskUse(true).build());
        ArrayList resultObjects = new ArrayList();

        while (cursor.hasNext()) {
            resultObjects.add(cursor.next());
        }

        return resultObjects;
    }

    public BasicDBObject aggregateDataForRPCStats(HashMap<String, Object> aggParams, String collection) {
        long t1 = Calendar.getInstance().getTimeInMillis();
        String operation = null;
        BasicDBObject matchCriteriaAndOperation = makeMatchStage(aggParams);
        operation = (String) matchCriteriaAndOperation.get("operation");

        BasicDBObject matchCriteria = (BasicDBObject) matchCriteriaAndOperation.get("matchCriteria");
        BasicDBObject project1Criteria = makeProject1Stage(operation);
        BasicDBObject groupCriteria = makeGroupStage(operation);
        BasicDBObject project2Criteria = makeProject2Stage(operation);
        return processAggregationResult(matchCriteria, project1Criteria, groupCriteria, project2Criteria, collection, t1);
    }

    private BasicDBObject makeMatchStage(HashMap<String, Object> aggParams) {
        BasicDBObject matchCriteria = new BasicDBObject();
        String operation = null;
        if (Objects.nonNull(aggParams)) {
            if (Objects.nonNull(aggParams.get("operation"))) {
                operation = (String) aggParams.get("operation");
                matchCriteria.put("operation", (String) aggParams.get("operation"));
            }
            matchCriteria.put("timestamp", new BasicDBObject("$gte", (Long) aggParams.get("fromTime")).append("$lte", (Long) aggParams.get("toTime")));
        }
        BasicDBObject matchCriteriaAndOperation = new BasicDBObject();
        matchCriteriaAndOperation.put("matchCriteria", matchCriteria);
        matchCriteriaAndOperation.put("operation", operation);
        return matchCriteriaAndOperation;
    }

    private BasicDBObject makeProject1Stage(String operation) {
        BasicDBObject project = new BasicDBObject();
        if (Objects.nonNull(operation))
            project.put("operation", 1);

        ArrayList<Object> successCond = new ArrayList<>();
        successCond.add("$success");
        successCond.add(true);
        project.put("success", new BasicDBObject("$cond", new BasicDBObject("if", new BasicDBObject("$eq", successCond)).append("then", 1).append("else", 0)));
        ArrayList<Object> failureCond = new ArrayList<>();
        failureCond.add("$success");
        failureCond.add(false);
        project.put("failure", new BasicDBObject("$cond", new BasicDBObject("if", new BasicDBObject("$eq", failureCond)).append("then", 1).append("else", 0)));
        ArrayList<String> subtractList = new ArrayList<>();
        subtractList.add("$responseTimestamp");
        subtractList.add("$requestTimestamp");
        project.put("processingTime", new BasicDBObject("$sum", new BasicDBObject("$subtract", subtractList)));
        return project;
    }

    private BasicDBObject makeGroupStage(String operation) {
        BasicDBObject groupCriteria = new BasicDBObject();
        if (Objects.nonNull(operation))
            groupCriteria.put("_id", "$operation");
        else
            groupCriteria.put("_id", "$timestamp");
        groupCriteria.put("totalCount", new BasicDBObject("$sum", 1));
        groupCriteria.put("success", new BasicDBObject("$sum", "$success"));
        groupCriteria.put("failure", new BasicDBObject("$sum", "$failure"));
        groupCriteria.put("avgProcessingTime", new BasicDBObject("$avg", "$processingTime"));
        return groupCriteria;
    }

    private BasicDBObject makeProject2Stage(String operation) {
        BasicDBObject project = new BasicDBObject();
        project.put("_id", 0);
        if (Objects.nonNull(operation))
            project.put("operation", "$_id");
        project.put("totalCount", "$totalCount");
        project.put("success", "$success");
        project.put("failure", "$failure");

        ArrayList<String> successDivideList = new ArrayList<>();
        successDivideList.add("$success");
        successDivideList.add("$totalCount");
        BasicDBObject divideObj = new BasicDBObject();
        divideObj.put("$divide", successDivideList);
        ArrayList<Object> succesMultiplyList = new ArrayList<>();
        succesMultiplyList.add(divideObj);
        succesMultiplyList.add(100);
        project.put("successRate", new BasicDBObject("$multiply", succesMultiplyList));

        ArrayList<String> failureDivideList = new ArrayList<>();
        failureDivideList.add("$failure");
        failureDivideList.add("$totalCount");
        divideObj = new BasicDBObject();
        divideObj.put("$divide", failureDivideList);
        ArrayList<Object> failureMultiplyList = new ArrayList<>();
        failureMultiplyList.add(divideObj);
        failureMultiplyList.add(100);
        project.put("failureRate", new BasicDBObject("$multiply", failureMultiplyList));

        project.put("avgProcessingTime", "$avgProcessingTime");
        return project;
    }


    private BasicDBObject processAggregationResult(BasicDBObject matchCriteria, BasicDBObject project1Criteria, BasicDBObject groupCriteria, BasicDBObject project2Criteria, String collection, Long t1) {
        AggregationOutput aggregationResult = this.mongoTemplate.getCollection(collection).aggregate(Arrays.asList(new BasicDBObject("$match", matchCriteria), new BasicDBObject("$project", project1Criteria), new BasicDBObject("$group", groupCriteria), new BasicDBObject("$project", project2Criteria)));
        BasicDBObject resultObject = null;
        for (Iterator resultItr = aggregationResult.results().iterator(); resultItr.hasNext(); resultObject = (BasicDBObject) resultItr.next()) {
            ;
        }
        long t2 = Calendar.getInstance().getTimeInMillis();
        if (t2 - t1 > utilityServices.getQueryMaxTimeMongo()) {
            LOG.info("Collection Name :: \t" + collection + "\t takes time  is greater than " + utilityServices.getQueryMaxTimeMongo());
        }
        return resultObject;
    }

}

