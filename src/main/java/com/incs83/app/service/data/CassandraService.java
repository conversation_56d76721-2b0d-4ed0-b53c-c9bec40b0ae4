package com.incs83.app.service.data;

import com.datastax.driver.core.Cluster;
import com.datastax.driver.core.Session;
import com.incs83.app.config.CassandraConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

import static com.incs83.app.constants.misc.ApplicationConstants.COMMA;

@Service
public final class CassandraService {
    @Autowired
    private CassandraConfig cassandraConfig;

    private Session session = null;
    private Cluster cluster = null;

    //@Bean
    public Session getCassandraSession() {
        if (cassandraConfig.isCassandraConnect()) {
            Cluster.Builder builder = Cluster.builder().addContactPoints(cassandraConfig.getServer().split(COMMA)).withPort(cassandraConfig.getPort());
            if (cassandraConfig.isAuthEnabled()) {
                builder.withCredentials(cassandraConfig.getUsername(), cassandraConfig.getPassword());
            }
            cluster = builder.build();
            if (Objects.nonNull(cluster))
                session = cluster.connect(cassandraConfig.getDatabase());
            return session;
        } else return session;
    }

    public static void closeCassandraSession(Session session) {
        if (Objects.nonNull(session)) {
            session.close();
        }
    }
}
