package com.incs83.app.service.data;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

/**
 * Created by jayant on 07/09/16.
 */
@Service
public class CacheServiceImpl<T> extends com.incs83.cache.CacheService {


    @Override
    public Object read(Object o, String s) {
        return null;
    }

    @Override
    public Object read(Object o) {
        return null;
    }

    @Override
    public boolean create(Object o, Object t1) {
        return false;
    }

    @Override
    public void delete(Object o, String s) {

    }

    @Override
    public void delete(Object o) {

    }

    @Override
    public boolean update(Object o, Object t1) {
        return false;
    }

    @Override
    public void merge(Class aClass, Object o) {

    }

    @Override
    public List<?> readNative(String s, HashMap hashMap) {
        return null;
    }

    @Override
    public void delete(String s, HashMap hashMap) {

    }

    @Override
    public boolean update(Object o, String s, HashMap hashMap) {
        return false;
    }

    @Override
    public void deleteNative(String s, HashMap hashMap) {

    }

    @Override
    public Object read(Object o, String s, HashMap hashMap) {
        return null;
    }
}
