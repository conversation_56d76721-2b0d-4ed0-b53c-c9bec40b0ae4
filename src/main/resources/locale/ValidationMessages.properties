##############################################
###############Http Request Validations##########
##############################################
NotNull.FormTemplateRequest.formJson=formJson cannot be null
NotNull.FormTemplateRequest.formName=formName cannot be null
NotNull.flowRequest.flowJson=flowJson cannot be null
NotNull.flowRequest.flowName=flowName cannot be null
######## Schedule Validations For Telus ################
NotNull.scheduleName=Schedule must have a Name
NotBlank.scheduleName=Schedule name can not be blank
NotNull.startTime=Schedule must have a Start Time (timestamp in milliseconds)
NotNull.endTime=Schedule must have an End Time (timestamp in milliseconds)
NotNull.profileName=Profile Name cannot be null or empty
validValues.status=status can have only two values ON or OFF. Default is OFF
validValues.NetworkType=Type can be any one of the combinations of 1,2,5,6 or individual 1,2,5 & 6. They represent the 2.4G & 5G Guest and Primary Networks 
validValues.ReportType="Report Type can only take EXCEL and PDF as values"
NotBlank.email=Email cannot be blank
NotBlank.serialNumber=Serial Number cannot be blank
NotBlank.networkType=Network type can not be blank
NotBlank.ScheduleName=Schedule Name can not be blank
NotNull.ScheduleName=Schedule must have a Name
NotBlank.name=Name can not be blank
NotBlank.urls=URl can not be blank
validValues.DeviceType= Device Type can be Device,Phone,Tablet,Computer,Camera,TV,Printer,Other
Range.tlsPort=Port Number must be from 0 to 65535
Range.tcpPort=Port Number must be from 0 to 65535
Length.userName=UserName Length must be from 1 to 23 and must not contain & * = | ; " < > symbols.
Length.password=Password Length must be from 1 to 23 and must not contain & * = | ; " < > symbols.
NotBlank.taskType=Tasktype cannot be empty.
NotBlank.cluster=Cluster cannot be empty.
NotBlank.channelName=Channel Name cannot be empty.
CompartmentRequest.NotBlank.mqttConnectionId=Mqtt Channel cannot be empty.
CompartmentRequest.NotBlank.serviceProvider=ISP cannot be empty.

####### System Configuration Message #######
NotBlank.SysConfigTitle = Title can not be blank
NotBlank.Property = Property can not be blank
NotBlank.PropertyValue = Value can not be blank

UserRequest.NotBlank.firstName=First Name cannot be empty
UserRequest.Min.firstName=First Name length must be between 1 and 255 character

UserRequest.NotBlank.lastName=Last Name cannot be empty
UserRequest.Min.lastName=Last Name length must be between 1 and 255 character

UserRequest.Invalid.email=Invalid Email Address

UserRequest.NotBlank.company=Company Name cannot be empty


