CREATE TABLE broker_info (
  id VARCHAR(255) PRIMARY KEY,
  env_name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  broker_url VARCHAR(255) NOT NULL,
  port INT(5) NOT NULL,
  username VA<PERSON>HA<PERSON>(255),
  password VARCHAR(255),
  is_default BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by <PERSON><PERSON><PERSON><PERSON>(255),
  UNIQUE KEY uk_env_name (env_name)
);

CREATE TABLE equipment_redirect (
  id VARCHAR(255) PRIMARY KEY,
  equipment_id VARCHAR(255) NOT NULL UNIQUE,
  serial VARCHAR(255) NOT NULL UNIQUE,
  broker_info_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  updated_by <PERSON><PERSON><PERSON><PERSON>(255),
  INDEX idx_equipment_redirect_equipment_id (equipment_id),
  INDEX idx_equipment_redirect_serial (serial),
  CONSTRAINT fk_broker_info_id FOREIGN KEY (broker_info_id) REFERENCES broker_info(id)
);