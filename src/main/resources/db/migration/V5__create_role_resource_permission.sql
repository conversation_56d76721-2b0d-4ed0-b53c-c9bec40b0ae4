-- Insert roles
INSERT INTO `actiontec`.`role` (`id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`, `description`, `mapping`, `dataSecurityMapping`, `name`, `type`) 
VALUES 
(
    'b22b4832f4b446539f7b26708a6dead8', 
    NOW(), 
    'SYSTEM', 
    NOW(), 
    'SYSTEM', 
    'Actiontec Engineer', 
    '[{"resource":"Acs","permissions":["ALL"]},{"resource":"Common","permissions":["ALL"]},{"resource":"Configuration","permissions":["ALL"]},{"resource":"DisconnectedRGW","permissions":["ALL"]},{"resource":"EquipmentLookup","permissions":["ALL"]},{"resource":"Equipments","permissions":["ALL"]},{"resource":"NetworkDashboard","permissions":["ALL"]},{"resource":"Schedule","permissions":["ALL"]},{"resource":"Smm","permissions":["READ","CREATE","UPDATE","DELETE"]},{"resource":"SpeedTestUrl","permissions":["ALL"]},{"resource":"Subscribers","permissions":["ALL"]},{"resource":"TechnicianDashBoard","permissions":["ALL"]},{"resource":"User","permissions":["ALL"]}]', 
    NULL, 
    'ACTIONTEC_ENGINEER', 
    'ENTERPRISE_ROLE'
),
(
    'b9d27e0888234aaa9cc45d1f92c64eb4', 
    NOW(), 
    'SYSTEM', 
    NOW(), 
    'SYSTEM', 
    'ISP Engineer', 
    '[{"resource":"Acs","permissions":["ALL"]},{"resource":"Common","permissions":["ALL"]},{"resource":"Configuration","permissions":["ALL"]},{"resource":"DisconnectedRGW","permissions":["ALL"]},{"resource":"EquipmentLookup","permissions":["ALL"]},{"resource":"Equipments","permissions":["ALL"]},{"resource":"NetworkDashboard","permissions":["ALL"]},{"resource":"Schedule","permissions":["ALL"]},{"resource":"Smm","permissions":["READ","CREATE","UPDATE","DELETE"]},{"resource":"SpeedTestUrl","permissions":["ALL"]},{"resource":"Subscribers","permissions":["ALL"]},{"resource":"TechnicianDashBoard","permissions":["ALL"]},{"resource":"User","permissions":["ALL"]}]', 
    NULL, 
    'ISP_ENGINEER', 
    'ENTERPRISE_ROLE'
);

-- Insert role entities for ACTIONTEC_ENGINEER
INSERT INTO `actiontec`.`role_entity` (`Role_id`, `entity`) 
VALUES 
('b22b4832f4b446539f7b26708a6dead8', 'Acs'),
('b22b4832f4b446539f7b26708a6dead8', 'Common'),
('b22b4832f4b446539f7b26708a6dead8', 'Configuration'),
('b22b4832f4b446539f7b26708a6dead8', 'DisconnectedRGW'),
('b22b4832f4b446539f7b26708a6dead8', 'EquipmentLookup'),
('b22b4832f4b446539f7b26708a6dead8', 'Equipments'),
('b22b4832f4b446539f7b26708a6dead8', 'NetworkDashboard'),
('b22b4832f4b446539f7b26708a6dead8', 'Schedule'),
('b22b4832f4b446539f7b26708a6dead8', 'Smm'),
('b22b4832f4b446539f7b26708a6dead8', 'SpeedTestUrl'),
('b22b4832f4b446539f7b26708a6dead8', 'Subscribers'),
('b22b4832f4b446539f7b26708a6dead8', 'TechnicianDashBoard'),
('b22b4832f4b446539f7b26708a6dead8', 'User');

-- Insert role entities for ISP_ENGINEER
INSERT INTO `actiontec`.`role_entity` (`Role_id`, `entity`) 
VALUES 
('b9d27e0888234aaa9cc45d1f92c64eb4', 'Acs'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'Common'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'Configuration'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'DisconnectedRGW'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'EquipmentLookup'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'Equipments'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'NetworkDashboard'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'Schedule'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'Smm'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'SpeedTestUrl'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'Subscribers'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'TechnicianDashBoard'),
('b9d27e0888234aaa9cc45d1f92c64eb4', 'User');


CREATE TABLE resource (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    createdAt datetime NOT NULL,
    createdBy varchar(255) NOT NULL,
    updatedAt datetime DEFAULT NULL,
    updatedBy varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE permission_type (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    createdAt datetime NOT NULL,
    createdBy varchar(255) NOT NULL,
    updatedAt datetime DEFAULT NULL,
    updatedBy varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE role_resource_permission (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_id VARCHAR(255) NOT NULL,
    resource_id INT NOT NULL,
    permission_type_id INT NOT NULL,
    createdAt datetime NOT NULL,
    createdBy varchar(255) NOT NULL,
    updatedAt datetime DEFAULT NULL,
    updatedBy varchar(255) DEFAULT NULL,
    FOREIGN KEY (role_id) REFERENCES role(id),
    FOREIGN KEY (resource_id) REFERENCES resource(id),
    FOREIGN KEY (permission_type_id) REFERENCES permission_type(id),
    UNIQUE KEY (role_id, resource_id, permission_type_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO `actiontec`.`resource` (`name`, `description`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
VALUES
('DEBUG_SESSION', NULL, NOW(), 'SYSTEM', NULL, NULL),
('DEBUG_CONNECTION', NULL, NOW(), 'SYSTEM', NULL, NULL);

INSERT INTO `actiontec`.`permission_type` (`name`, `description`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
VALUES
('CREATE', NULL, NOW(), 'SYSTEM', NULL, NULL),
('DELETE', NULL, NOW(), 'SYSTEM', NULL, NULL),
('EXTEND', NULL, NOW(), 'SYSTEM', NULL, NULL),
('HTTP', NULL, NOW(), 'SYSTEM', NULL, NULL),
('SSH', NULL, NOW(), 'SYSTEM', NULL, NULL);

INSERT INTO `actiontec`.`role_resource_permission` (`role_id`, `resource_id`, `permission_type_id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
SELECT
  r.id,
  res.id,
  pt.id,
  NOW(),
  'SYSTEM',
  NULL,
  NULL
FROM
  `actiontec`.`role` r,
  `actiontec`.`resource` res,
  `actiontec`.`permission_type` pt
WHERE
  r.name = 'SYSTEM_ADMIN'
  AND res.name = 'DEBUG_SESSION'
  AND pt.name IN ('CREATE', 'DELETE', 'EXTEND');

INSERT INTO `actiontec`.`role_resource_permission` (`role_id`, `resource_id`, `permission_type_id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
SELECT
  r.id,
  res.id,
  pt.id,
  NOW(),
  'SYSTEM',
  NULL,
  NULL
FROM
  `actiontec`.`role` r,
  `actiontec`.`resource` res,
  `actiontec`.`permission_type` pt
WHERE
  r.name = 'SYSTEM_ADMIN'
  AND res.name = 'DEBUG_CONNECTION'
  AND pt.name IN ('SSH', 'HTTP');


INSERT INTO `actiontec`.`role_resource_permission` (`role_id`, `resource_id`, `permission_type_id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
SELECT
  r.id,
  res.id,
  pt.id,
  NOW(),
  'SYSTEM',
  NULL,
  NULL
FROM
  `actiontec`.`role` r,
  `actiontec`.`resource` res,
  `actiontec`.`permission_type` pt
WHERE
  r.name = 'GROUP_ADMIN'
  AND res.name = 'DEBUG_SESSION'
  AND pt.name IN ('CREATE', 'DELETE', 'EXTEND');

INSERT INTO `actiontec`.`role_resource_permission` (`role_id`, `resource_id`, `permission_type_id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
SELECT
  r.id,
  res.id,
  pt.id,
  NOW(),
  'SYSTEM',
  NULL,
  NULL
FROM
  `actiontec`.`role` r,
  `actiontec`.`resource` res,
  `actiontec`.`permission_type` pt
WHERE
  r.name = 'GROUP_ADMIN'
  AND res.name = 'DEBUG_CONNECTION'
  AND pt.name IN ('SSH', 'HTTP');

INSERT INTO `actiontec`.`role_resource_permission` (`role_id`, `resource_id`, `permission_type_id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
SELECT
  r.id,
  res.id,
  pt.id,
  NOW(),
  'SYSTEM',
  NULL,
  NULL
FROM
  `actiontec`.`role` r,
  `actiontec`.`resource` res,
  `actiontec`.`permission_type` pt
WHERE
  r.name = 'OPERATOR'
  AND res.name = 'DEBUG_SESSION'
  AND pt.name IN ('CREATE', 'DELETE', 'EXTEND');

INSERT INTO `actiontec`.`role_resource_permission` (`role_id`, `resource_id`, `permission_type_id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
SELECT
  r.id,
  res.id,
  pt.id,
  NOW(),
  'SYSTEM',
  NULL,
  NULL
FROM
  `actiontec`.`role` r,
  `actiontec`.`resource` res,
  `actiontec`.`permission_type` pt
WHERE
  r.name = 'ISP_ENGINEER'
  AND (
    (res.name = 'DEBUG_SESSION' AND pt.name = 'DELETE') OR
    (res.name = 'DEBUG_CONNECTION' AND pt.name = 'HTTP')
  );

INSERT INTO `actiontec`.`role_resource_permission` (`role_id`, `resource_id`, `permission_type_id`, `createdAt`, `createdBy`, `updatedAt`, `updatedBy`)
SELECT
  r.id,
  res.id,
  pt.id,
  NOW(),
  'SYSTEM',
  NULL,
  NULL
FROM
  `actiontec`.`role` r,
  `actiontec`.`resource` res,
  `actiontec`.`permission_type` pt
WHERE
  r.name = 'ACTIONTEC_ENGINEER'
  AND (
    (res.name = 'DEBUG_SESSION' AND pt.name = 'DELETE') OR
    (res.name = 'DEBUG_CONNECTION' AND pt.name IN ('SSH', 'HTTP'))
  );

-- Note: rtty_server_config needs to be initialized, but settings vary by environment. Please contact DevOps team for proper configuration.
