-- Create rtty_session table
CREATE TABLE `rtty_session` (
  `id` VARCHAR(255) NOT NULL,                     -- Session UUID
  `equipment_id` VARCHAR(255) NOT NULL UNIQUE,    -- Equipment ID, must be unique
  `token` VARCHAR(255) NOT NULL,                  -- Session token (UUID)
  `ssh_url` VARCHAR(255) NOT NULL,                -- SSH URL
  `http_url` VARCHAR(255) NOT NULL,               -- WebGUI URL
  `status` VARCHAR(64) NULL,                  -- Session status (active, expired)
  `rtty_server_id` VARCHAR(255) NOT NULL,         -- RTTY server ID
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Creation time
  `created_by` VARCHAR(255) NOT NULL,             -- C<PERSON> (ISP user)
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Last update time
  `updated_by` VARCHAR(255),                     -- Last updater
  `expired_at` TIMESTAMP NULL,                        -- Expiration time
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Create rtty_server_config table
CREATE TABLE `rtty_server_config` (
  `id` VARCHAR(255) NOT NULL,        -- UUID
  `name` VARCHAR(255) NOT NULL,      -- Server name
  `host` VARCHAR(255) NOT NULL,      -- Host name or IP
  `port` INT NOT NULL,              -- Port
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Creation time
  `created_by` VARCHAR(255) NOT NULL, -- Creator
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- Last update time
  `updated_by` VARCHAR(255),       -- Last updatee
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
