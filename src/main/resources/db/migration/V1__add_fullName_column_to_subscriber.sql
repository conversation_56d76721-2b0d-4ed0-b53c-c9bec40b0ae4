ALTER TABLE subscriber
ADD COLUMN fullName VARCHAR(255),
ADD FULLTEXT INDEX idx_fullName (fullName);

UPDATE subscriber
SET fullName = CONCAT(firstName, ' ', lastName);

DELIMITER //

CREATE TRIGGER trg_update_fullName_on_insert
BEFORE INSERT ON actiontec.subscriber
FOR EACH ROW
BEGIN
    SET NEW.fullName = CONCAT(NEW.firstName, ' ', NEW.lastName);
END;

//

CREATE TRIGGER trg_update_fullName_on_update
BEFORE UPDATE ON actiontec.subscriber
FOR EACH ROW
BEGIN
    SET NEW.fullName = CONCAT(NEW.firstName, ' ', NEW.lastName);
END;

//

DELIMITER ;

