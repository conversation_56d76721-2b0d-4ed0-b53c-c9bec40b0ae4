ALTER TABLE user
ADD COLUMN fullName VARCHAR(255),
ADD FULLTEXT INDEX idx_fullName (fullName);

UPDATE user
SET fullName = CONCAT(firstName, ' ', lastName);

DELIMITER //

CREATE TRIGGER trg_update_fullName_on_insert_for_user
BEFORE INSERT ON actiontec.user
FOR EACH ROW
BEGIN
    SET NEW.fullName = CONCAT(NEW.firstName, ' ', NEW.lastName);
END;

//

CREATE TRIGGER trg_update_fullName_on_update_for_user
BEFORE UPDATE ON actiontec.user
FOR EACH ROW
BEGIN
    SET NEW.fullName = CONCAT(NEW.firstName, ' ', NEW.lastName);
END;

//

DELIMITER ;

