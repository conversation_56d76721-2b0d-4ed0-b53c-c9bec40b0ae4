#server.port=9092
server.port=8888
spring.servlet.multipart.maxFileSize=100MB
spring.servlet.multipart.maxRequestSize=100MB
mysql.username=optim
mysql.password=optim123!
mysql.driverClassName=com.mysql.jdbc.Driver
mysql.hibernateDialect=org.hibernate.dialect.MySQL5InnoDBDialect
mysql.hibernateShowSql=false
mysql.hibernateFormatSql=true
mysql.hibernateCreateMode=update
mysql.connectionPoolClass=org.hibernate.c3p0.internal.C3P0ConnectionProvider
mysql.poolMaxConnections=100
mysql.poolMinConnections=10
mysql.poolTimeout=5000
mysql.poolMaxStatements=50
mysql.acquireIncrement=5
mysql.idleTestPeriod=300
mysql.generateMLTables=false
mysql.allowMerge=allow
mysql.jdbcBatchSize=50
mongodb.hostname=**************
mongodb.username=optim
mailFrom=<EMAIL>
mongodb.password=optim123
mongodb.port=27017
mongodb.database=actiontec
mongodb.authEnabled=true
spring.kafka.bootstrap-servers=localhost:32092
kafka.topic=AUDIT_EVENTS
kafka.rpcTopic=CTRL_MQTT
kafka.numOfThreads=1
kafka.pollIntervalInMilliSeconds=10000
hazelcast.hostname=localhost:32071
mail.host=email-smtp.us-east-1.amazonaws.com
mail.port=587
mail.username=AKIAYF6CDDKISF776YHI
mail.password=BL73a8ueTxZUkYME33CUL4ksQMAPcxgD3C06N2bNH9CQ
app.mail.configured=true
cache.available=true
jwt.tokenSigningKey=xm8EV6Hy5RMFK4EEACIDAwQus
cmPort=8765
mqttAuthtoken=secret
vmqUsername=optim
vmqPassword=optim123!
vmqAuthEnabled=true
s3.bucket=optim5-object-store
aws.s3.region=us-east-2
loggerEnabled=false
auditLogsEnabled=true
cloudera.host=**************
cloudera.port=7180
cloudera.username=admin
cloudera.password=AeIOptIm@1b3
hive.url=********************************
hive.username=hdfs
hive.password=hdfs
hive.driverClassName=org.apache.hive.jdbc.HiveDriver
hive.database=actiontec
hive.auth=true
spark.host=**************
spark.port=8088
elastic-search.hostname=**************
elastic-search.port=9200
elastic-search.enable=true
internal.secretKey=bTgzLWVsYXN0aWNzZWFyY2gtc2VjcmV0a2V5
cassandra.server=**************
cassandra.auth.enabled=true
cassandra.port=9042
cassandra.database.name=actiontec
cassandra.database.username=optim
cassandra.database.password=optim123
cassandra.connect.enable=true
internal.service.stats.dns=http://localhost:4545/
vmqDebugUsername=optim-debug
vmqDebugPassword=X9eb77XVB63KEXkF
vmqNotificationUsername=optim-notification
fSecureCacheEnabled=true
vmqNotificationPassword=Uk7VrmN4p57XNSYs
spring.mvc.async.request-timeout=-1
management.endpoint.metrics.enabled=true
management.endpoints.web.exposure.include=*
management.endpoint.prometheus.enabled=true
management.endpoints.web.base-path=/
management.security.enabled=false
management.endpoints.web.path-mapping.prometheus=metrics
management.endpoints.web.path-mapping.metrics=defaultMetrics
asyncThreadPoolCount=200
jwt.tokenExpirationTime=480
spring.servlet.multipart.maxFileSize=100MB
spring.servlet.multipart.maxRequestSize=100MB
aws.s3.region=us-east-2
uCentralEnabled=true
rgwMacNotKey=false
rtty.server=aeidev.optimdemo.com
ouiUrl=https://gitlab.com/wireshark/wireshark/-/raw/master/manuf
rtty.port=5912
longWaitTask.maxPoolSize=100
useNewOauthConfig=true
mesh.enable=true
vmqApiKey=D4XsTMzd5L93yEdu7S3KWdJnCEkykP
appleAuthKeyPath=/home/<USER>/setup/config/AppleLogin_AuthKey.p8
mysql.url=********************************************************************************************************************************