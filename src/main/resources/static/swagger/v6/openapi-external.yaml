openapi: 3.1.0
info:
  title: NCS Optim API
  version: 6.5.1
  description: The API for NCS Optim
servers:
  - url: /
security:
  - xAuthBearer: []
tags:
  - name: Network
  - name: IAM
paths:
  /actiontec/api/v6/networks:
    get:
      tags:
        - Network
      operationId: searchNetworks
      parameters:
        - name: keyword
          in: query
          required: true
          description: 'The keyword is used for searching networks. It can be a partial match from the following fields: Subscriber Name or Serial Number.'
          schema:
            type: string
        - name: offset
          in: query
          description: Pagination offset.
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: Number of results per page.
          required: false
          schema:
            type: integer
            default: 50
      responses:
        '200':
          description: Successful response with log data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        name:
                          type: string
                        subscriber:
                          type: object
                          properties:
                            id:
                              type: string
                              format: uuid
                              readOnly: true
                            firstName:
                              type: string
                              readOnly: true
                            lastName:
                              type: string
                              readOnly: true
                        equipment:
                          type: array
                          items:
                            type: string
                          description: Serial numbers of the equipment belong to the network
                      required:
                        - id
                        - name
                        - equipment
  /actiontec/api/v6/networks/{networkId}/topology/history:
    get:
      description: |
        Fetches data from the history about CPI.
      operationId: getTopologyHistory
      tags:
        - Network
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: startTime
          in: query
          description: Start of the time frame in Unix timestamp format (e.g., `1745232001000`). Default value is the current time minus 1 day.
          schema:
            type: integer
        - name: endTime
          in: query
          description: End of the time frame in Unix timestamp format (e.g., `1745232001000`), should be within 7 days of `startTime`. Default value is the current time.
          schema:
            type: integer
        - name: deviceId
          in: query
          description: Filter topology snapshots within a time range include the specified deviceId (the 'id' key in the response), it's not required
          schema:
            type: string
      responses:
        '200':
          description: Successful response with network topology data.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    devices:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: string
                          hostname:
                            type: string
                          macAddress:
                            type: string
                          status:
                            type: string
                            enum:
                              - Up
                              - Down
                          type:
                            type: string
                            enum:
                              - Device
                              - Router
                              - Extender
                            description: The currently used 'Device' include printer, computer, garage door, sprinkler, smart speaker, thermostat, game console, streaming player, phone, tablet, IoT, NAS, camera, robot vacuum, smart hub, smart lock, car, router extender, security, monitoring, TV
                          upstreamId:
                            type: string
                          lastChangeTime:
                            type: integer
                          ipAddresses:
                            type: array
                            items:
                              type: object
                              properties:
                                version:
                                  type: integer
                                  enum:
                                    - 4
                                    - 6
                                address:
                                  type: string
                                type:
                                  type: string
                                  enum:
                                    - GUA
                                    - LLA
                                    - ULA
                                lifetimeState:
                                  type: string
                                  enum:
                                    - Valid
                                    - Unknown
                                    - Infinite
                                lifetime:
                                  type: integer
                          connections:
                            type: array
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                  enum:
                                    - Ethernet
                                    - Wireless
                                uptime:
                                  type: integer
                                wireless:
                                  type: object
                                  properties:
                                    ssid:
                                      type: string
                                    radio:
                                      type: string
                                      enum:
                                        - 2.4G
                                        - 5G
                                        - 6G
                                    rssi:
                                      type: integer
                                    uplinkPhyRate:
                                      type: integer
                                    downlinkPhyRate:
                                      type: integer
                                ethernet:
                                  type: object
                                  properties:
                                    port:
                                      type: integer
                                    bitRate:
                                      type: integer
                          capabilities:
                            type: array
                            readOnly: true
                            description: Omit this object if the type is not Router or Extender
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                  readOnly: true
                                  enum:
                                    - Wireless
                                    - Ethernet
                                radio:
                                  type: string
                                  readOnly: true
                                  description: Omit this field if the type is not Wireless
                                  enum:
                                    - 2.4G
                                    - 5G
                                    - 6G
                    radios:
                      type: array
                      items:
                        type: object
                        properties:
                          enabled:
                            type: boolean
                          band:
                            type: string
                            enum:
                              - 2.4G
                              - 5G
                              - 6G
                    timeOfSnapshot:
                      type: integer
        default:
          description: Unexpected error.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/networks/action:
    post:
      summary: Claim or disclaim a network
      tags:
        - Network
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/ClaimNetworkRequest'
                - $ref: '#/components/schemas/DisclaimNetworkRequest'
            examples:
              claim:
                value:
                  action: claim
                  serialNumber: string
              disclaim:
                value:
                  action: disclaim
                  networkId: string
      responses:
        '200':
          description: Claim or disclaim action succeeded
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/NetworkClaimResponse'
                  - $ref: '#/components/schemas/NetworkDisclaimResponse'
        '403':
          description: Network already claimed
        '404':
          description: Serial number not found
        '409':
          description: Corresponding network not found
        '503':
          description: CPE not responding
  /actiontec/api/v6/networks/{networkId}:
    get:
      parameters:
        - description: Target network ID
          in: path
          name: networkId
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Network'
          description: Network details
        '404':
          description: Network not found
      security:
        - bearerAuth: []
      summary: Get network details
      tags:
        - Network
    patch:
      parameters:
        - description: Target network ID
          in: path
          name: networkId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RenameNetworkRequest'
        required: true
      responses:
        '200':
          description: Network renamed
        '400':
          description: Invalid name
        '404':
          description: Network not found
      security:
        - bearerAuth: []
      summary: Rename network
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/devices:
    get:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/Device'
                type: array
          description: List of devices and equipments
      security:
        - bearerAuth: []
      summary: Get all devices in the network
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/devices/{deviceId}:
    get:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: deviceId
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device'
          description: Device details
        '404':
          description: Device not found
      security:
        - bearerAuth: []
      summary: Get a single device's detail
      tags:
        - Network
    patch:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: deviceId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceUpdateRequest'
        required: true
      responses:
        '200':
          description: Device updated
        '400':
          description: Invalid request
        '404':
          description: Device not found
      security:
        - bearerAuth: []
      summary: Update device name, type or verified flag
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/devices/{deviceId}/action:
    post:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: deviceId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceActionRequest'
        required: true
      responses:
        '200':
          description: Action accepted
        '400':
          description: Invalid action
        '404':
          description: Device not found
        '503':
          description: Device unavailable
      security:
        - bearerAuth: []
      summary: Perform action on a device (e.g., disconnect)
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/equipment:
    post:
      summary: Add extender to a network (pairing)
      tags:
        - Network
      security:
        - bearerAuth: []
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PairExtenderRequest'
      responses:
        '202':
          description: Pairing initiated
        '403':
          description: Forbidden
        '404':
          description: Equipment not found or DPP key missing
        '409':
          description: Equipment already belongs to another network or this network
        '503':
          description: Gateway unavailable
  /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}:
    get:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: equipmentId
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentDetail'
          description: Equipment details
        '404':
          description: Equipment not found
      security:
        - bearerAuth: []
      summary: Get equipment details
      tags:
        - Network
    patch:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: equipmentId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EquipmentUpdateRequest'
        required: true
      responses:
        '200':
          description: Name updated
        '400':
          description: Invalid request
        '404':
          description: Equipment not found
      security:
        - bearerAuth: []
      summary: Update equipment friendly name
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}/action:
    post:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: equipmentId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EquipmentActionRequest'
        required: true
      responses:
        '200':
          description: Action accepted
        '400':
          description: Invalid action
        '404':
          description: Equipment not found
        '503':
          description: Equipment unavailable
      security:
        - bearerAuth: []
      summary: Perform action on equipment (e.g., reboot)
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/equipment/{equipmentId}/system:
    patch:
      parameters:
        - description: Target network ID
          in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: equipmentId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetPasswordRequest'
        required: true
      responses:
        '200':
          description: Password updated
        '400':
          description: Invalid password
        '404':
          description: Not found
      security:
        - bearerAuth: []
      summary: Update equipment login password
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/internet-connection:
    get:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                properties:
                  status:
                    enum:
                      - Up
                      - Down
                    type: string
                required:
                  - status
                type: object
          description: Internet connection status
        '404':
          description: Network not found
        '503':
          description: Service temporarily unavailable
      security:
        - bearerAuth: []
      summary: Get internet connection status
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/speed-test:
    post:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SpeedTestStartRequest'
        required: false
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpeedTestStartResponse'
          description: Speed test initiated
        '400':
          description: Invalid request
        '404':
          description: Network or equipment not found
        '503':
          description: Equipment not ready
      security:
        - bearerAuth: []
      summary: Start a speed test
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/speed-test_records:
    get:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SpeedTestResult'
                type: array
          description: List of speed test records
        '404':
          description: Network not found
      security:
        - bearerAuth: []
      summary: Get list of speed test records (last 7 days)
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/speed-test_records/{recordId}:
    get:
      parameters:
        - in: path
          name: networkId
          required: true
          schema:
            type: string
        - in: path
          name: recordId
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpeedTestResult'
          description: Speed test record
        '404':
          description: Record not found
      security:
        - bearerAuth: []
      summary: Get speed test result by record ID
      tags:
        - Network
  /actiontec/api/v6/networks/{networkId}/ssid-settings:
    get:
      summary: Get SSID settings
      tags:
        - Network
      security:
        - bearerAuth: []
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: SSID Settings
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SsidSettingItem'
        '404':
          description: Network not found
    patch:
      summary: Update SSID settings
      tags:
        - Network
      security:
        - bearerAuth: []
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/SsidSettingItem'
      responses:
        '200':
          description: Updated
        '400':
          description: Invalid request
        '404':
          description: Network not found
  /actiontec/api/v6/networks/{networkId}/port-forwarding_rules:
    get:
      summary: List port forwarding rules
      tags:
        - Network
      security:
        - bearerAuth: []
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Rules list
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PortForwardRule'
    post:
      summary: Create port forwarding rule
      tags:
        - Network
      security:
        - bearerAuth: []
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PortForwardRuleInput'
      responses:
        '201':
          description: Created
        '400':
          description: Invalid
        '409':
          description: Conflict
  /actiontec/api/v6/networks/{networkId}/port-forwarding_rules/{ruleId}:
    get:
      summary: Get rule
      tags:
        - Network
      security:
        - bearerAuth: []
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
        - name: ruleId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Detail
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PortForwardRule'
        '404':
          description: Not found
    patch:
      summary: Update rule
      tags:
        - Network
      security:
        - bearerAuth: []
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
        - name: ruleId
          in: path
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PortForwardRuleInput'
      responses:
        '200':
          description: Updated
        '400':
          description: Invalid
        '404':
          description: Not found
        '409':
          description: Conflict
  /actiontec/api/v6/iam/subscribers:
    get:
      tags:
        - IAM
      description: Get all subscribers
      operationId: getAllSubscribers
      parameters:
        - in: query
          name: offset
          description: Number of records to skip
          schema:
            type: integer
            format: int32
            default: 0
        - in: query
          name: limit
          description: Max number of records to return
          schema:
            type: integer
            format: int32
            default: 50
        - in: query
          name: ispId
          description: Specified ISP ID
          schema:
            type: string
            format: uuid
        - in: query
          name: firstName
          description: Filter of subscriber first name, support fuzzy search
          schema:
            type: string
        - in: query
          name: lastName
          description: Filter of subscriber last name, support fuzzy search
          schema:
            type: string
      responses:
        '200':
          description: A portion of subscriber data
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        firstName:
                          type: string
                        lastName:
                          type: string
                        ispId:
                          type: string
                          format: uuid
                        userId:
                          type: string
                          format: uuid
                      required:
                        - id
                        - ispId
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/subscribers/{subscriberId}:
    get:
      tags:
        - IAM
      description: Get a subscriber
      operationId: getSubscriber
      parameters:
        - name: subscriberId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Subscriber data
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                    readOnly: true
                  firstName:
                    type: string
                  lastName:
                    type: string
                  ispId:
                    type: string
                    format: uuid
                  userId:
                    type: string
                    format: uuid
                required:
                  - id
                  - ispId
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/networks:
    get:
      tags:
        - IAM
      description: Get all networks
      operationId: getAllNetworks
      parameters:
        - in: query
          name: offset
          description: Number of records to skip
          schema:
            type: integer
            format: int32
            default: 0
        - in: query
          name: limit
          description: Max number of records to return
          schema:
            type: integer
            format: int32
            default: 50
        - in: query
          name: ispId
          description: Specified ISP ID
          schema:
            type: string
            format: uuid
        - in: query
          name: subscriberId
          description: Specified subscriber ID
          schema:
            type: string
            format: uuid
        - in: query
          name: name
          description: Filter of network name, support fuzzy search
          schema:
            type: string
        - in: query
          name: isAuto
          description: Filter of network type
          schema:
            type: boolean
      responses:
        '200':
          description: A portion of network data
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        isAuto:
                          type: boolean
                          readOnly: true
                        autoNetworkId:
                          type: string
                          readOnly: true
                        name:
                          type: string
                        subscriberId:
                          type: string
                          format: uuid
                        ispId:
                          type: string
                          format: uuid
                        lastChangedTime:
                          type: integer
                          readOnly: true
                      required:
                        - id
                        - isAuto
                        - subscriberId
                        - lastChangedTime
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/networks/{networkId}:
    get:
      tags:
        - IAM
      description: Get a network
      operationId: getNetwork
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Network data
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                        readOnly: true
                      isAuto:
                        type: boolean
                        readOnly: true
                      autoNetworkId:
                        type: string
                        readOnly: true
                      name:
                        type: string
                      subscriberId:
                        type: string
                        format: uuid
                      ispId:
                        type: string
                        format: uuid
                      lastChangedTime:
                        type: integer
                        readOnly: true
                    required:
                      - id
                      - isAuto
                      - subscriberId
                      - lastChangedTime
                  - type: object
                    properties:
                      equipment:
                        type: array
                        items:
                          type: string
                        description: Serial numbers of the equipment belong to the network
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/equipment:
    get:
      tags:
        - IAM
      description: Get all equipment
      operationId: getAllEquipment
      parameters:
        - in: query
          name: offset
          description: Number of records to skip
          schema:
            type: integer
            format: int32
            default: 0
        - in: query
          name: limit
          description: Max number of records to return
          schema:
            type: integer
            format: int32
            default: 50
        - in: query
          name: ispId
          description: Specified ISP ID
          schema:
            type: string
            format: uuid
        - in: query
          name: networkId
          description: Specified network ID
          schema:
            type: string
            format: uuid
        - in: query
          name: brokerId
          description: Specified broker ID
          schema:
            type: string
            format: uuid
        - in: query
          name: serialNumber
          description: Filter of equipment serial number, support fuzzy search
          schema:
            type: string
        - in: query
          name: modelName
          description: Filter of equipment model name, support fuzzy search
          schema:
            type: string
        - in: query
          name: inUse
          description: Filter of equipment inUse state
          schema:
            type: boolean
      responses:
        '200':
          description: A portion of equipment data
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        serialNumber:
                          type: string
                          readOnly: true
                        macAddress:
                          type: string
                          readOnly: true
                        modelName:
                          type: string
                          readOnly: true
                        productId:
                          type: string
                          readOnly: true
                        ispId:
                          type: string
                          format: uuid
                          readOnly: true
                        ispName:
                          type: string
                          readOnly: true
                        inUse:
                          type: boolean
                          readOnly: true
                        networkId:
                          type: string
                          format: uuid
                          readOnly: true
                        brokerId:
                          type: string
                          format: uuid
                          readOnly: true
                        lastChangedTime:
                          type: integer
                          readOnly: true
                      required:
                        - id
                        - serialNumber
                        - macAddress
                        - modelName
                        - productId
                        - ispId
                        - ispName
                        - inUse
                        - networkId
                        - brokerId
                        - lastChangedTime
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
components:
  securitySchemes:
    xAuthBearer:
      type: apiKey
      in: header
      name: X-Authorization
      description: |
        ## Example
        ```
        X-Authorization: Bearer <token>
        ```
  schemas:
    ClaimNetworkRequest:
      title: Claim Network Request Schema
      type: object
      required:
        - action
        - serialNumber
      properties:
        action:
          type: string
          enum:
            - claim
        serialNumber:
          type: string
    DisclaimNetworkRequest:
      title: Disclaim Network Request Schema
      type: object
      required:
        - action
        - networkId
      properties:
        action:
          type: string
          enum:
            - disclaim
        networkId:
          type: string
    NetworkClaimResponse:
      type: object
      required:
        - isConfigured
        - networkId
        - networkToken
      properties:
        networkId:
          type: string
        isConfigured:
          type: boolean
        networkToken:
          type: string
          description: Network token used for App Cloud access.
        isp:
          type: object
          properties:
            logo:
              type: string
              description: ISP logo URL
            message:
              type: string
              description: ISP message
    NetworkDisclaimResponse:
      type: object
      properties:
        message:
          type: string
    Equipment:
      properties:
        id:
          type: string
        serialNumber:
          type: string
      type: object
    Network:
      properties:
        id:
          type: string
        name:
          type: string
        equipment:
          items:
            $ref: '#/components/schemas/Equipment'
          type: array
        isp:
          properties:
            id:
              type: string
            name:
              type: string
          type: object
      type: object
    RenameNetworkRequest:
      properties:
        name:
          type: string
      type: object
    Device:
      properties:
        id:
          type: string
        firstSeenTime:
          type: integer
        hostname:
          type: string
        lastChangeTime:
          type: integer
        macAddress:
          type: string
        name:
          type: string
        status:
          enum:
            - Up
            - Down
          type: string
        type:
          type: string
          enum:
            - Router
            - Extender
            - Device
        upstreamId:
          type: string
        verified:
          type: boolean
        capabilities:
          items:
            properties:
              radio:
                enum:
                  - 2.4G
                  - 5G
                  - 6G
                type: string
              type:
                type: string
            type: object
          type: array
        connections:
          items:
            properties:
              ethernet:
                properties:
                  bitRate:
                    type: integer
                  port:
                    type: integer
                type: object
              type:
                type: string
              uptime:
                type: integer
              wireless:
                properties:
                  downlinkPhyRate:
                    type: integer
                  radio:
                    enum:
                      - 2.4G
                      - 5G
                      - 6G
                    type: string
                  rssi:
                    type: integer
                  ssid:
                    type: string
                  uplinkPhyRate:
                    type: integer
                type: object
            type: object
          type: array
        ipAddresses:
          items:
            properties:
              address:
                type: string
              lifetime:
                type: integer
              lifetimeState:
                enum:
                  - Valid
                  - Infinite
                  - Unknown
                type: string
              type:
                enum:
                  - GUA
                  - ULA
                  - LLA
                type: string
              version:
                type: integer
            required:
              - version
              - address
              - type
              - lifetimeState
              - lifetime
            type: object
          type: array
      required:
        - id
        - type
        - status
      type: object
    DeviceUpdateRequest:
      properties:
        name:
          type: string
        type:
          type: string
        verified:
          type: boolean
      type: object
    DeviceActionRequest:
      type: object
      required:
        - action
      properties:
        action:
          type: string
          enum:
            - disconnect
    PairExtenderRequest:
      type: object
      required:
        - serialNumber
      properties:
        serialNumber:
          type: string
    EquipmentDetail:
      properties:
        id:
          type: string
        firmwareVersion:
          type: string
        macAddress:
          type: string
        model:
          type: string
        name:
          type: string
        serialNumber:
          type: string
        status:
          enum:
            - Up
            - Down
          type: string
        type:
          type: string
      required:
        - id
        - type
        - serialNumber
        - macAddress
        - status
      type: object
    EquipmentUpdateRequest:
      properties:
        name:
          type: string
      required:
        - name
      type: object
    EquipmentActionRequest:
      type: object
      required:
        - action
      properties:
        action:
          type: string
          enum:
            - reboot
    SetPasswordRequest:
      properties:
        password:
          type: string
        timezone:
          description: Timezone in IANA format (e.g., Asia/Taipei)
          type: string
      type: object
    SpeedTestStartRequest:
      properties:
        equipmentId:
          type: string
      type: object
    SpeedTestStartResponse:
      properties:
        id:
          type: string
        status:
          enum:
            - Pending
          type: string
      required:
        - id
        - status
      type: object
    SpeedTestResult:
      properties:
        id:
          type: string
        downloadRate:
          type: integer
        equipmentId:
          type: string
        latency:
          type: integer
        status:
          enum:
            - Pending
            - Complete
            - Failed
          type: string
        time:
          type: integer
        uploadRate:
          type: integer
        url:
          type: string
      required:
        - id
        - status
        - uploadRate
        - downloadRate
        - latency
        - time
        - equipmentId
      type: object
    SSID:
      properties:
        id:
          type: string
        enabled:
          type: boolean
        name:
          type: string
        passphrase:
          type: string
        radio:
          enum:
            - 2.4G
            - 5G
            - 6G
          type: string
        securityMode:
          type: string
        status:
          enum:
            - Up
            - Down
          type: string
        type:
          enum:
            - Primary
            - Guest
            - SmartHome
          type: string
        securityModesSupported:
          items:
            type: string
          type: array
      required:
        - status
        - enabled
        - passphrase
        - id
        - name
        - securityMode
        - type
        - securityModesSupported
        - radio
      type: object
    SsidSettingItem:
      type: object
      required:
        - id
        - type
        - separatedSsid
        - ssids
      properties:
        id:
          type: string
        type:
          type: string
          enum:
            - Primary
            - Guest
            - SmartHome
            - Backhaul
        separatedSsid:
          type: boolean
        ssids:
          type: array
          items:
            $ref: '#/components/schemas/SSID'
    PortForwardRule:
      type: object
      required:
        - id
        - enabled
        - name
        - protocol
        - external
        - destination
      properties:
        id:
          type: string
        enabled:
          type: boolean
        name:
          type: string
        protocol:
          type: string
          enum:
            - TCP
            - UDP
            - TCP+UDP
        external:
          type: object
          required:
            - portRange
          properties:
            portRange:
              type: string
        destination:
          type: object
          required:
            - ipAddress
            - portRange
          properties:
            ipAddress:
              type: string
              format: ipv4
            portRange:
              type: string
    PortForwardRuleInput:
      type: object
      required:
        - enabled
        - name
        - protocol
        - external
        - destination
      properties:
        enabled:
          type: boolean
        name:
          type: string
        protocol:
          type: string
          enum:
            - TCP
            - UDP
            - TCP+UDP
        external:
          type: object
          required:
            - portRange
          properties:
            portRange:
              type: string
        destination:
          type: object
          required:
            - ipAddress
            - portRange
          properties:
            ipAddress:
              type: string
              format: ipv4
            portRange:
              type: string
