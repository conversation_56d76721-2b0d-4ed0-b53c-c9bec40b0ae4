openapi: 3.1.0
info:
  title: NCS Optim API
  version: 6.5.0
  description: The API for NCS Optim
servers:
  - url: /
security:
  - xAuthBearer: []
tags:
  - name: Network
  - name: IAM
paths:
  /actiontec/api/v6/networks:
    get:
      tags:
        - Network
      operationId: searchNetworks
      parameters:
        - name: keyword
          in: query
          required: true
          description: 'The keyword is used for searching networks. It can be a partial match from the following fields: Subscriber Name or Serial Number.'
          schema:
            type: string
        - name: offset
          in: query
          description: Pagination offset.
          required: false
          schema:
            type: integer
            default: 0
        - name: limit
          in: query
          description: Number of results per page.
          required: false
          schema:
            type: integer
            default: 50
      responses:
        '200':
          description: Successful response with log data.
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        name:
                          type: string
                        subscriber:
                          type: object
                          properties:
                            id:
                              type: string
                              format: uuid
                              readOnly: true
                            firstName:
                              type: string
                              readOnly: true
                            lastName:
                              type: string
                              readOnly: true
                        equipment:
                          type: array
                          items:
                            type: string
                          description: Serial numbers of the equipment belong to the network
                      required:
                        - id
                        - name
                        - equipment
  /actiontec/api/v6/networks/{networkId}/topology/history:
    get:
      description: |
        Fetches data from the history about CPI.
      operationId: getTopologyHistory
      tags:
        - Network
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: startTime
          in: query
          description: Start of the time frame in Unix timestamp format (e.g., `1745232001000`). Default value is the current time minus 1 day.
          schema:
            type: integer
        - name: endTime
          in: query
          description: End of the time frame in Unix timestamp format (e.g., `1745232001000`), should be within 7 days of `startTime`. Default value is the current time.
          schema:
            type: integer
        - name: deviceId
          in: query
          description: Filter topology snapshots within a time range include the specified deviceId (the 'id' key in the response), it's not required
          schema:
            type: string
      responses:
        '200':
          description: Successful response with network topology data.
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    devices:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: string
                          hostname:
                            type: string
                          macAddress:
                            type: string
                          status:
                            type: string
                            enum:
                              - Up
                              - Down
                          type:
                            type: string
                            enum:
                              - Device
                              - Router
                              - Extender
                            description: The currently used 'Device' include printer, computer, garage door, sprinkler, smart speaker, thermostat, game console, streaming player, phone, tablet, IoT, NAS, camera, robot vacuum, smart hub, smart lock, car, router extender, security, monitoring, TV
                          upstreamId:
                            type: string
                          lastChangeTime:
                            type: integer
                          ipAddresses:
                            type: array
                            items:
                              type: object
                              properties:
                                version:
                                  type: integer
                                  enum:
                                    - 4
                                    - 6
                                address:
                                  type: string
                                type:
                                  type: string
                                  enum:
                                    - GUA
                                    - LLA
                                    - ULA
                                lifetimeState:
                                  type: string
                                  enum:
                                    - Valid
                                    - Unknown
                                    - Infinite
                                lifetime:
                                  type: integer
                          connections:
                            type: array
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                  enum:
                                    - Ethernet
                                    - Wireless
                                uptime:
                                  type: integer
                                wireless:
                                  type: object
                                  properties:
                                    ssid:
                                      type: string
                                    radio:
                                      type: string
                                      enum:
                                        - 2.4G
                                        - 5G
                                        - 6G
                                    rssi:
                                      type: integer
                                    uplinkPhyRate:
                                      type: integer
                                    downlinkPhyRate:
                                      type: integer
                                ethernet:
                                  type: object
                                  properties:
                                    port:
                                      type: integer
                                    bitRate:
                                      type: integer
                          capabilities:
                            type: array
                            readOnly: true
                            description: Omit this object if the type is not Router or Extender
                            items:
                              type: object
                              properties:
                                type:
                                  type: string
                                  readOnly: true
                                  enum:
                                    - Wireless
                                    - Ethernet
                                radio:
                                  type: string
                                  readOnly: true
                                  description: Omit this field if the type is not Wireless
                                  enum:
                                    - 2.4G
                                    - 5G
                                    - 6G
                    radios:
                      type: array
                      items:
                        type: object
                        properties:
                          enabled:
                            type: boolean
                          band:
                            type: string
                            enum:
                              - 2.4G
                              - 5G
                              - 6G
                    timeOfSnapshot:
                      type: integer
        default:
          description: Unexpected error.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/subscribers:
    get:
      tags:
        - IAM
      description: Get all subscribers
      operationId: getAllSubscribers
      parameters:
        - in: query
          name: offset
          description: Number of records to skip
          schema:
            type: integer
            format: int32
            default: 0
        - in: query
          name: limit
          description: Max number of records to return
          schema:
            type: integer
            format: int32
            default: 50
        - in: query
          name: ispId
          description: Specified ISP ID
          schema:
            type: string
            format: uuid
        - in: query
          name: firstName
          description: Filter of subscriber first name, support fuzzy search
          schema:
            type: string
        - in: query
          name: lastName
          description: Filter of subscriber last name, support fuzzy search
          schema:
            type: string
      responses:
        '200':
          description: A portion of subscriber data
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        firstName:
                          type: string
                        lastName:
                          type: string
                        ispId:
                          type: string
                          format: uuid
                        userId:
                          type: string
                          format: uuid
                      required:
                        - id
                        - ispId
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/subscribers/{subscriberId}:
    get:
      tags:
        - IAM
      description: Get a subscriber
      operationId: getSubscriber
      parameters:
        - name: subscriberId
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Subscriber data
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                    readOnly: true
                  firstName:
                    type: string
                  lastName:
                    type: string
                  ispId:
                    type: string
                    format: uuid
                  userId:
                    type: string
                    format: uuid
                required:
                  - id
                  - ispId
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/networks:
    get:
      tags:
        - IAM
      description: Get all networks
      operationId: getAllNetworks
      parameters:
        - in: query
          name: offset
          description: Number of records to skip
          schema:
            type: integer
            format: int32
            default: 0
        - in: query
          name: limit
          description: Max number of records to return
          schema:
            type: integer
            format: int32
            default: 50
        - in: query
          name: ispId
          description: Specified ISP ID
          schema:
            type: string
            format: uuid
        - in: query
          name: subscriberId
          description: Specified subscriber ID
          schema:
            type: string
            format: uuid
        - in: query
          name: name
          description: Filter of network name, support fuzzy search
          schema:
            type: string
        - in: query
          name: isAuto
          description: Filter of network type
          schema:
            type: boolean
      responses:
        '200':
          description: A portion of network data
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        isAuto:
                          type: boolean
                          readOnly: true
                        autoNetworkId:
                          type: string
                          readOnly: true
                        name:
                          type: string
                        subscriberId:
                          type: string
                          format: uuid
                        ispId:
                          type: string
                          format: uuid
                        lastChangedTime:
                          type: integer
                          readOnly: true
                      required:
                        - id
                        - isAuto
                        - subscriberId
                        - lastChangedTime
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/networks/{networkId}:
    get:
      tags:
        - IAM
      description: Get a network
      operationId: getNetwork
      parameters:
        - name: networkId
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Network data
          content:
            application/json:
              schema:
                allOf:
                  - type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                        readOnly: true
                      isAuto:
                        type: boolean
                        readOnly: true
                      autoNetworkId:
                        type: string
                        readOnly: true
                      name:
                        type: string
                      subscriberId:
                        type: string
                        format: uuid
                      ispId:
                        type: string
                        format: uuid
                      lastChangedTime:
                        type: integer
                        readOnly: true
                    required:
                      - id
                      - isAuto
                      - subscriberId
                      - lastChangedTime
                  - type: object
                    properties:
                      equipment:
                        type: array
                        items:
                          type: string
                        description: Serial numbers of the equipment belong to the network
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
  /actiontec/api/v6/iam/equipment:
    get:
      tags:
        - IAM
      description: Get all equipment
      operationId: getAllEquipment
      parameters:
        - in: query
          name: offset
          description: Number of records to skip
          schema:
            type: integer
            format: int32
            default: 0
        - in: query
          name: limit
          description: Max number of records to return
          schema:
            type: integer
            format: int32
            default: 50
        - in: query
          name: ispId
          description: Specified ISP ID
          schema:
            type: string
            format: uuid
        - in: query
          name: networkId
          description: Specified network ID
          schema:
            type: string
            format: uuid
        - in: query
          name: brokerId
          description: Specified broker ID
          schema:
            type: string
            format: uuid
        - in: query
          name: serialNumber
          description: Filter of equipment serial number, support fuzzy search
          schema:
            type: string
        - in: query
          name: modelName
          description: Filter of equipment model name, support fuzzy search
          schema:
            type: string
        - in: query
          name: inUse
          description: Filter of equipment inUse state
          schema:
            type: boolean
      responses:
        '200':
          description: A portion of equipment data
          content:
            application/json:
              schema:
                type: object
                properties:
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      return:
                        type: integer
                      offset:
                        type: integer
                      limit:
                        type: integer
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: string
                          format: uuid
                          readOnly: true
                        serialNumber:
                          type: string
                          readOnly: true
                        macAddress:
                          type: string
                          readOnly: true
                        modelName:
                          type: string
                          readOnly: true
                        productId:
                          type: string
                          readOnly: true
                        ispId:
                          type: string
                          format: uuid
                          readOnly: true
                        ispName:
                          type: string
                          readOnly: true
                        inUse:
                          type: boolean
                          readOnly: true
                        networkId:
                          type: string
                          format: uuid
                          readOnly: true
                        brokerId:
                          type: string
                          format: uuid
                          readOnly: true
                        lastChangedTime:
                          type: integer
                          readOnly: true
                      required:
                        - id
                        - serialNumber
                        - macAddress
                        - modelName
                        - productId
                        - ispId
                        - ispName
                        - inUse
                        - networkId
                        - brokerId
                        - lastChangedTime
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    readOnly: true
components:
  securitySchemes:
    xAuthBearer:
      type: apiKey
      in: header
      name: X-Authorization
      description: |
        ## Example
        ```
        X-Authorization: Bearer <token>
        ```
