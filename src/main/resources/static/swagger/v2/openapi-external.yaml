openapi: 3.1.0
info:
  description: These are API's for Optim
  version: v2019.10-37.5-LA-OC-2.14GA
  title: Optim API
servers:
  - url: /
tags:
  - name: Authentication
    description: API's for Authentication
  - name: Devices
    description: API'S for Device Operations
  - name: Equipment
    description: API's for Equipment Operations
  - name: Equipment and Device Alarms
    description: API's for various Equipment and Device level alarms
  - name: Home Network
    description: API's for Home Network Operations
  - name: Subscriber Home Network Stats
    description: API's for Subscriber Home Network Stats
paths:
  /actiontec/api/v2/equipments/{networkId}:
    get:
      operationId: getEquipmentByIdUsingGET
      summary: Get Equipment By Id
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: NetworkId
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriberListDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
    patch:
      operationId: editEquipmentUsingPATCH
      summary: Edit Equipment
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: NetworkId
          required: true
          schema:
            type: string
      requestBody:
        description: editEquipmentRequest
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EditEquipmentRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/alarms:
    get:
      operationId: getAllAlarmsForSubscriberUsingGET
      summary: Get all Alarms For a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AlarmsDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment and Device Alarms
  /actiontec/api/v2/equipments/{networkId}/devices:
    get:
      operationId: getDevicesUsingGET
      summary: Get all stations for a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceListDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/bandwidth:
    get:
      operationId: getTopTenBanwidthConsumingDevicesUsingGET
      summary: Get Top 10 Stations by consumed bandwidth over time for a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: type
          in: query
          description: Type of details requested [upstream, downstream].Default is upstream+downstream
          required: false
          schema:
            type: string
          allowEmptyValue: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceBandWidthDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/friendlyName:
    patch:
      operationId: updateDeviceFriendlyNameUsingPATCH
      summary: Update Friendly name for Device
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
      requestBody:
        description: friendlyNameRequest
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceFriendlyNameRequest'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/internetBandwidth:
    get:
      operationId: getTopTenInternetBanwidthConsumingDevicesUsingGET
      summary: Get Top 10 Stations by consumed internet bandwidth over time for a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: type
          in: query
          description: Type of details requested [upstream, downstream].Default is upstream+downstream
          required: false
          schema:
            type: string
          allowEmptyValue: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceBandWidthDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/internetTraffic:
    get:
      operationId: getTopTenInternetTrafficConsumingDevicesUsingGET
      summary: Get Top 10 Consuming Internet Traffic By station for a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 15 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 15
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceWifiTrafficDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/type:
    patch:
      operationId: updateDeviceTypeUsingPATCH
      summary: Update Device Type for a MAC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
      requestBody:
        description: updateDeviceTypeRequest
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDeviceTypeRequest'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/wifiTraffic:
    get:
      operationId: getTopTenWifiTrafficConsumingDevicesUsingGET
      summary: Get Top 10 Consuming Wifi Traffic By station for a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 15 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 15
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceWifiTrafficDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/detail:
    get:
      operationId: getDeviceDetailsUsingGET
      summary: Get station details for a station MAC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/internetUtilization:
    get:
      operationId: getDeviceUtilizationUsingGET
      summary: Get station utilization for a station MAC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: duration
          required: false
          schema:
            type: integer
            format: int64
            default: 60
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: fromDSHost
          in: query
          description: fromDSHost
          required: false
          schema:
            type: boolean
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceUtilizationDTO'
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not Found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/reset:
    patch:
      operationId: disconnectInternetUsingPATCH
      summary: Reset Internet for Device
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: macAddress
          in: path
          description: MAC Address of the device
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/rssi:
    get:
      operationId: getRSSIDataUsingGET
      summary: Get RSSI Data over time for station MAC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceRssiDetailDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/speedTest:
    get:
      operationId: performStationSpeedTestForUserDeviceUsingGET
      summary: Perform SpeedTest for Station
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: band
          in: query
          description: Band of the Equipment to which the Station connected( 2G and 5G Band Allowed)
          required: true
          schema:
            type: string
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: ipAddr
          in: query
          description: IP Address of the Device
          required: true
          schema:
            type: string
          allowEmptyValue: false
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
        - name: serialNo
          in: query
          description: Serial Number of the Equipment to which device connected.
          required: true
          schema:
            type: string
          allowEmptyValue: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceSpeedTestDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/wifiPhy:
    get:
      operationId: getWifiPhyDataUsingGET
      summary: Get Wifi Phy Rates Data over time for a station MAC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceWifiPhyDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/wifiThroughput:
    get:
      operationId: getWifiThroughPutDataUsingGET
      summary: Get Wifi Throughput Data over time for a station MAC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceWifiThroughputDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/internetAccess:
    get:
      operationId: getDeviceInternetAccessStatusbyRpc
      summary: Get Internet Access status for a device by using RPC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceInternetAccessResponseDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/devices/{macAddress}/internetAccess/action:
    post:
      operationId: postDeviceInternetAccessStatebyRpc
      summary: Edit Internet Access state to a device by using RPC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: SerialNumber or RGW MAC
          required: true
          schema:
            type: string
        - name: macAddress
          in: path
          description: MAC Address of the device whose details are requested
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceInternetAccessPayload'
      responses:
        '200':
          description: OK
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Devices
  /actiontec/api/v2/equipments/{networkId}/equipments/friendlyName:
    patch:
      operationId: updateEquipmentFriendlyNameUsingPATCH
      summary: Update Friendly name for Equipment
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      requestBody:
        description: friendlyNameRequest
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EquipmentFriendlyNameRequest'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/equipments/{serialNumber}/forget:
    delete:
      operationId: forgetDeviceUsingDELETE
      summary: Forget Equipment
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No Content
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '400':
          description: Bad Request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/equipments/{serialNumber}/reboot:
    patch:
      operationId: disconnectInternetUsingPATCH_1
      summary: Reboot Equipment
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '204':
          description: No Content
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments:
    get:
      operationId: getAllEquipmentsUsingGET
      summary: Get All Equipments for a subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentListDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/24g/busy/{serialNumber}:
    get:
      operationId: getEquipmentBusy24gDetailsUsingGET
      summary: Get 2.4G Busy Details of Equipment by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: Serial Number or  RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentCommonDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/24g/busyByDevice/{serialNumber}:
    get:
      operationId: getEquipmentBusy24gByDeviceDetailsUsingGET
      summary: Get 2.4G Busy By Device Details of Equipment by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentBusyByDeviceDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/24g/wireless/{serialNumber}:
    get:
      operationId: getEquipmentWireless24gDetailsUsingGET
      summary: Get 2.4G Wireless Details of Equipment by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentWirelessDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/5g/busy/{serialNumber}:
    get:
      operationId: getEquipmentBusy5gDetailsUsingGET
      summary: Get 5G Busy Details of Equipment by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentCommonDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/5g/busyByDevice/{serialNumber}:
    get:
      operationId: getEquipmentBusy5gByDeviceDetailsUsingGET
      summary: Get 5G Busy By Device Details of Equipment by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentBusyByDeviceDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/5g/wireless/{serialNumber}:
    get:
      operationId: getEquipmentWireless5gDetailsUsingGET
      summary: Get 5G Wireless Details of Equipment by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentWirelessDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/ethernet/{serialNumber}:
    get:
      operationId: getEquipmentethernetDetailsUsingGET
      summary: Get Equipment Ethernet Details for Subscriber by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentEthernetDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/diagnosticLogs:
    get:
      operationId: getEquipmentDiagnisticLogDataUsingGET
      summary: Get Equipment Diagnostic Logs over time by Equipment MAC
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration for which the details are requested. Default is last 60 minutes
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentDiagnosticLogDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/systemInfo/{serialNumber}:
    get:
      operationId: getEquipmentSystemDetailsUsingGET
      summary: Get Equipment System Details by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentSystemInfoResponseDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/wan/{serialNumber}:
    get:
      operationId: getEquipmentWanDetailsUsingGET
      summary: Get Equipment WAN Details by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EquipmentWANDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/equipments/{serialNumber}:
    get:
      operationId: getEquipmentDetailsUsingGET
      summary: Get Equipment Details for an Equipment by Serial Number
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: serialNumber
          in: path
          description: Serial Number of Equipment
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/equipments/{networkId}/network/stats/avgDownlinkRate:
    get:
      operationId: fetchNetworkWifiDownloadSpeedUsingGET
      summary: Get Networks Wifi Download Speed for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WifiMeterDataDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/stats/avgUplinkRate:
    get:
      operationId: fetchNetworkWifiUploadSpeedUsingGET
      summary: Get Networks WifiUpload Speed for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WifiMeterDataDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/stats/internet:
    get:
      operationId: fetchNetworkInternetDetailsUsingGET
      summary: Get Networks Internet Details for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternetDetailDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/stats/internetUtilization:
    get:
      operationId: fetchNetworkInternetUtilizationGraphDataUsingGET
      summary: Get Networks Internet Utilization Graph Data for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Duration, Default value is 60
          required: false
          schema:
            type: integer
            format: int64
            default: 60
          allowEmptyValue: false
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InternetUtilizationDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/stats/speedTest:
    get:
      operationId: performSpeedTestForUserUsingGET
      summary: Get Speed Test of Network for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
        - name: performSpeedTest
          in: query
          description: performSpeedTest, If true, will perform a WAN Speed Test
          required: false
          schema:
            type: boolean
          allowEmptyValue: false
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpeedTestHistoryDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/stats/wifiHealthHistory:
    get:
      operationId: fetchWifiHealthUsingGET
      summary: Get Wifi Health of Network for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WifiHealthHistoryDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/stats/wifiHealthPerMinute:
    get:
      operationId: fetchWifiHealthPerMinuteUsingGET
      summary: Get Wifi Health Per Minute Graph Data of Network for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WifiHealthPerMinuteDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/stats/wifiHealthScore:
    get:
      operationId: fetchNetworkWifiHealthScoreUsingGET
      summary: Get Networks Wifi Health Score for Subscribers
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WifiMeterDataDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Subscriber Home Network Stats
  /actiontec/api/v2/equipments/{networkId}/network/topology:
    get:
      operationId: fetchAPDetailsUsingGET
      summary: Get Home Network Topology for a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopologyDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Home Network
  /actiontec/api/v2/equipments/{networkId}/networks:
    get:
      operationId: fetchNetworksListUsingGET
      summary: Get networks(SSID) list for a Subscriber
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: networkId
          in: path
          description: Serial Number or RGW MAC
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NetworkListDTO'
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Home Network
  /actiontec/api/v2/equipments/{id}:
    delete:
      operationId: deleteEquipmentByIdUsingDELETE
      summary: Delete Equipment by Id
      parameters:
        - name: X-Authorization
          in: header
          description: Bearer Access Token required for Authentication
          required: true
          schema:
            type: string
        - name: id
          in: path
          description: Delete Equipment by Id
          required: true
          schema:
            type: string
      responses:
        '204':
          description: No Content
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ApiResponseDTO'
        '400':
          description: Bad Request
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
        '403':
          description: Accessing the resource you were trying to reach is forbidden
        '404':
          description: The resource you were trying to reach is not found
      deprecated: false
      tags:
        - Equipment
  /actiontec/api/v2/login:
    post:
      operationId: authLoginUsingPOST
      summary: Auth Login
      requestBody:
        description: loginRequest
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponseDTO'
        '201':
          description: Created
        '400':
          description: Bad Request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BadRequestResponseDTO'
        '401':
          description: You are Not Authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotAuthenticatedResponseDTO'
        '403':
          description: Not Authorized on this resource
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessDeniedResponseDTO'
        '404':
          description: Not Found
      deprecated: false
      tags:
        - Authentication
components:
  schemas:
    SubscriberModel:
      type: object
      properties:
        camsAccountNo:
          description: camsAccountNo
          type: string
        downLinkRate:
          description: Allocated Bandwidth to Subscriber
          type: number
          format: double
        email:
          description: email
          type: string
        equipmentId:
          description: subscriberId
          type: string
        globalAccountNo:
          description: globalAccountNo
          type: string
        name:
          description: name
          type: string
        phoneNo:
          description: phoneNo
          type: string
        rgwMAC:
          description: RGW MAC
          type: string
        serialNumber:
          description: serialNumber
          type: string
        upLinkRate:
          description: Allocated Bandwidth to Subscriber
          type: number
          format: double
      required:
        - camsAccountNo
        - email
        - equipmentId
        - globalAccountNo
        - name
        - phoneNo
        - rgwMAC
        - serialNumber
      title: SubscriberModel
    SubscriberData:
      type: object
      properties:
        objects:
          type: array
          items:
            $ref: '#/components/schemas/SubscriberModel'
        totalCount:
          type: integer
          format: int64
      title: SubscriberData
    SubscriberListDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/SubscriberData'
        message:
          type: string
      required:
        - data
      title: SubscriberListDTO
    BadRequestResponseDTO:
      type: object
      properties:
        code:
          description: Response Code
          type: integer
          format: int32
        data:
          description: Response Data
          type: object
          properties: {}
        message:
          description: Response Message
          type: string
      required:
        - code
        - data
        - message
      title: BadRequestResponseDTO
    NotAuthenticatedResponseDTO:
      type: object
      properties:
        code:
          description: Response Code
          type: integer
          format: int32
        data:
          description: Response Data
          type: object
          properties: {}
        message:
          description: Response Message
          type: string
      required:
        - code
        - data
        - message
      title: NotAuthenticatedResponseDTO
    AccessDeniedResponseDTO:
      type: object
      properties:
        code:
          description: Response Code
          type: integer
          format: int32
        data:
          description: Response Data
          type: object
          properties: {}
        message:
          description: Response Message
          type: string
      required:
        - code
        - data
        - message
      title: AccessDeniedResponseDTO
    EditEquipmentRequest:
      type: object
      properties:
        downLinkRate:
          type: number
          format: double
        friendlyName:
          type: string
        rgwMac:
          type: string
        rgwSerial:
          type: string
        upLinkRate:
          type: number
          format: double
      title: EditEquipmentRequest
    ApiResponseDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          type: object
          properties: {}
        message:
          type: string
      title: ApiResponseDTO
    AlarmData:
      type: object
      properties:
        alarmType:
          description: Alarm Type (Device/Equipment)
          type: string
        desc:
          description: Alarm Desc
          type: string
        equipmentType:
          description: Type of the equipment GATEWAY/EXTENDER
          type: string
        mac:
          description: MAC Address of Station, in case for alarm is for Station
          type: string
        name:
          description: Name of equipment/Device
          type: string
        serial:
          description: Serial Number of Station, in case for alarm is for Equipment
          type: string
        severity:
          description: Severity Level (0 is lowest)
          type: integer
          format: int32
      title: AlarmData
    AlarmsDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/AlarmData'
        message:
          type: string
      required:
        - data
      title: AlarmsDTO
    DeviceDetailListData:
      type: object
      properties:
        band:
          description: Device band
          type: string
        connectedTo:
          description: Device connected To
          type: string
        connectivityStatus:
          description: Device connectivity status
          type: string
        deviceType:
          description: Device Type
          type: string
        internetOn:
          description: Device status ON/OFF
          type: boolean
        ip:
          description: Device ip address
          type: string
        lastReportedAt:
          description: Device last reported at
          type: integer
          format: int64
        mac:
          description: Device MAC
          type: string
        name:
          description: Device name
          type: string
        rssi:
          description: Device rssi
          type: number
          format: double
        ssid:
          description: Device ssid
          type: string
        vendor:
          description: Device vendor name
          type: string
      required:
        - band
        - connectedTo
        - connectivityStatus
        - deviceType
        - internetOn
        - ip
        - lastReportedAt
        - mac
        - name
        - rssi
        - ssid
        - vendor
      title: DeviceDetailListData
    DeviceListDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/DeviceDetailListData'
        message:
          type: string
      required:
        - data
      title: DeviceListDTO
    DeviceBandWidthData:
      type: object
      properties:
        bandwidth:
          description: Bandwidth consumed
          type: number
          format: double
        fromDSHost:
          description: fromDSHost
          type: boolean
        macAddress:
          description: MAC address
          type: string
        name:
          description: Device name
          type: string
        rx:
          description: Downstream
          type: number
          format: double
        tx:
          description: Upstream
          type: number
          format: double
      required:
        - bandwidth
        - macAddress
        - name
        - rx
        - tx
      title: DeviceBandWidthData
    DeviceBandWidthDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/DeviceBandWidthData'
        message:
          type: string
      required:
        - data
      title: DeviceBandWidthDTO
    DeviceFriendlyNameRequest:
      type: object
      properties:
        friendlyName:
          description: Device Friendly Name
          type: string
        macAddress:
          description: Device MAC Address
          type: string
      required:
        - friendlyName
        - macAddress
      title: DeviceFriendlyNameRequest
    DeviceWifiTrafficData:
      type: object
      properties:
        fromDSHost:
          description: fromDSHost
          type: boolean
        macAddress:
          description: MAC address
          type: string
        name:
          description: Device name
          type: string
        traffic:
          description: traffic
          type: number
          format: double
        wifiTrafficPercentage:
          description: wifiTrafficPercentage
          type: number
          format: double
      required:
        - macAddress
        - name
        - traffic
        - wifiTrafficPercentage
      title: DeviceWifiTrafficData
    DeviceWifiTrafficDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/DeviceWifiTrafficData'
        message:
          type: string
      required:
        - data
      title: DeviceWifiTrafficDTO
    UpdateDeviceTypeRequest:
      type: object
      properties:
        macAddress:
          description: MAC Address for the Device
          type: string
        type:
          description: Device Type
          type: string
      required:
        - macAddress
        - type
      title: UpdateDeviceTypeRequest
    HashMap_string_object_:
      type: object
      additionalProperties:
        type: object
        properties: {}
      title: HashMap«string,object»
    DeviceUtilizationData:
      type: object
      properties:
        datapoints:
          type: array
          items:
            $ref: '#/components/schemas/HashMap_string_object_'
          examples:
            - - timestamp: '0'
                tx: '0'
                rx: '0'
                uploadPercentage: '0'
                downloadPercentage: '0'
                downLinkRate: '0'
                upLinkRate: '0'
        totalDownLinkBytes:
          description: total downlink bytes
          type: number
          format: double
        totalUpLinkBytes:
          description: total uplink bytes
          type: number
          format: double
      required:
        - totalDownLinkBytes
        - totalUpLinkBytes
      title: DeviceUtilizationData
    DeviceUtilizationDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/DeviceUtilizationData'
        message:
          type: string
      title: DeviceUtilizationDTO
    DeviceRssiData:
      type: object
      properties:
        equipment:
          description: Device MAC
          type: string
        rssi:
          description: RSSI
          type: number
          format: double
        rssiRange:
          description: RSSI Lower Limit value
          type: integer
          format: int32
        timestamp:
          description: Timestamp
          type: integer
          format: int64
      required:
        - equipment
        - rssi
        - timestamp
      title: DeviceRssiData
    DeviceRssiDetailDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/DeviceRssiData'
        message:
          type: string
      required:
        - data
      title: DeviceRssiDetailDTO
    DeviceSpeedTestData:
      type: object
      properties:
        band:
          description: band
          type: string
        dataRate:
          description: dataRate
          type: number
          format: float
        date:
          description: date
          type: integer
          format: int64
        ipAddr:
          description: ip Address
          type: string
        macAddr:
          description: mac Address
          type: string
        result:
          description: RPC Result
          type: string
        rxRate:
          description: rxRate
          type: number
          format: float
        txRate:
          description: txRate
          type: number
          format: float
        userId:
          description: userId
          type: string
      required:
        - band
        - dataRate
        - date
        - ipAddr
        - macAddr
        - result
        - rxRate
        - txRate
        - userId
      title: DeviceSpeedTestData
    DeviceSpeedTestDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/DeviceSpeedTestData'
        message:
          type: string
      required:
        - data
      title: DeviceSpeedTestDTO
    DeviceWifiPhyData:
      type: object
      properties:
        downlinkPhyRate:
          description: downlinkPhyRate
          type: number
          format: double
        timestamp:
          description: timestamp
          type: integer
          format: int64
        uplinkPhyRate:
          description: uplinkPhyRate
          type: number
          format: double
      required:
        - downlinkPhyRate
        - timestamp
        - uplinkPhyRate
      title: DeviceWifiPhyData
    DeviceWifiPhyDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/DeviceWifiPhyData'
        message:
          type: string
      required:
        - data
      title: DeviceWifiPhyDTO
    DeviceWifiThroughputData:
      type: object
      properties:
        rx:
          description: rx
          type: number
          format: double
        timestamp:
          description: timestamp
          type: integer
          format: int64
        tx:
          description: tx
          type: number
          format: double
      required:
        - rx
        - timestamp
        - tx
      title: DeviceWifiThroughputData
    DeviceWifiThroughputDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/DeviceWifiThroughputData'
        message:
          type: string
      required:
        - data
      title: DeviceWifiThroughputDTO
    DeviceInternetAccessResponse:
      type: object
      properties:
        blocked:
          type: boolean
        blockStartTime:
          description: Block start time in unix ts
          type: number
        blockDuration:
          description: Block duration in second
          type: number
      required:
        - blockDuration
        - blockStartTime
      title: DeviceInternetAccessResponse
    DeviceInternetAccessResponseDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/DeviceInternetAccessResponse'
        message:
          type: string
      required:
        - data
      title: DeviceInternetAccessResponseDTO
    DeviceInternetAccessPayload:
      type: object
      properties:
        action:
          type: string
          enum:
            - ALLOW
            - BLOCK
        blockStartTime:
          description: Block start time in unix ts
          type: number
        blockDuration:
          description: Block duration in second
          type: number
      required:
        - action
      title: DeviceInternetAccessPayload
    EquipmentFriendlyNameRequest:
      type: object
      properties:
        friendlyName:
          description: Equipment Friendly Name
          type: string
        serialNumber:
          description: Equipment Serial Number
          type: string
      required:
        - friendlyName
        - serialNumber
      title: EquipmentFriendlyNameRequest
    EquipmentListData:
      type: object
      properties:
        buildVersion:
          description: Build version
          type: string
        connectivityStatus:
          description: connectivityStatus
          type: string
        diagnosticsEnabled:
          description: Diagnostics Enabled
          type: boolean
        etlVersion:
          description: Diagnostics Enabled
          type: string
        isp:
          description: Isp
          type: string
        macAddress:
          description: Mac Address
          type: string
        name:
          description: name
          type: string
        serialNumber:
          description: serialNumber
          type: string
        severity:
          description: Severity Level (0 is lowest)
          type: integer
          format: int32
        type:
          description: type
          type: string
      required:
        - buildVersion
        - connectivityStatus
        - macAddress
        - name
        - serialNumber
        - type
      title: EquipmentListData
    EquipmentListDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/EquipmentListData'
        message:
          type: string
      required:
        - data
      title: EquipmentListDTO
    EquipmentBusyData:
      type: object
      properties:
        timestamp:
          description: timestamp
          type: integer
          format: int64
        value:
          description: value
          type: number
          format: double
      required:
        - timestamp
        - value
      title: EquipmentBusyData
    EquipmentCommonDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/EquipmentBusyData'
        message:
          type: string
      required:
        - data
      title: EquipmentCommonDTO
    BusyByDevice:
      type: object
      properties:
        airTimePercentage:
          description: airTimePercentage
          type: number
          format: double
        macAddress:
          description: macAddress
          type: string
        name:
          description: name
          type: string
        radio:
          description: radio
          type: string
        timestamp:
          description: timestamp
          type: integer
          format: int64
      required:
        - airTimePercentage
        - macAddress
        - name
        - radio
        - timestamp
      title: BusyByDevice
    EquipmentBusyByDeviceData:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BusyByDevice'
        name:
          type: string
      title: EquipmentBusyByDeviceData
    EquipmentBusyByDeviceDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/EquipmentBusyByDeviceData'
        message:
          type: string
      required:
        - data
      title: EquipmentBusyByDeviceDTO
    EquipmentWirelessData:
      type: object
      properties:
        band:
          description: band
          type: string
        channel:
          description: channel
          type: integer
          format: int64
        channelWidth:
          description: channelWidth
          type: integer
          format: int64
        deviceAssociated:
          description: deviceAssociated
          type: integer
          format: int64
        enable:
          description: enable
          type: integer
          format: int64
        mode:
          description: mode
          type: string
        operatingStandards:
          description: operatingStandards
          type: string
        rxBytes:
          description: rxBytes
          type: number
          format: double
        txBytes:
          description: txBytes
          type: number
          format: double
        isBackhaul:
          type: boolean
      required:
        - band
        - channel
        - channelWidth
        - deviceAssociated
        - enable
        - isBackhaul
        - mode
        - operatingStandards
        - rxBytes
        - txBytes
      title: EquipmentWirelessData
    EquipmentWirelessDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/EquipmentWirelessData'
        message:
          type: string
      required:
        - data
      title: EquipmentWirelessDTO
    EquipmentEthernetData:
      type: object
      properties:
        lastChange:
          description: lastChange
          type: integer
          format: int64
        port:
          description: port
          type: integer
          format: int64
        rxBytes:
          description: rxBytes
          type: number
          format: double
        status:
          description: status
          type: string
        txBytes:
          description: txBytes
          type: number
          format: double
        maxBitRate:
          type: integer
        currentBitRate:
          type: integer
      required:
        - currentBitRate
        - lastChange
        - maxBitRate
        - port
        - rxBytes
        - status
        - txBytes
      title: EquipmentEthernetData
    EquipmentEthernetDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/EquipmentEthernetData'
        message:
          type: string
      required:
        - data
      title: EquipmentEthernetDTO
    EquipmentDiagnosticLogGetResponse:
      type: object
      properties:
        time:
          type: integer
        event:
          type: string
        macAddress:
          type: string
        hostname:
          type: string
        friendlyName:
          type: string
        device:
          type: string
        band:
          type: string
        vendor:
          type: string
        trigger:
          type: integer
        triggerDescription:
          type: string
        reasonCode:
          type: integer
        reasonDescription:
          type: string
    EquipmentDiagnosticLogDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/EquipmentDiagnosticLogGetResponse'
        message:
          type: string
      required:
        - data
      title: EquipmentSteeringDTO
    EquipmentSystemInfoResponse:
      type: object
      properties:
        alarms:
          type: array
          items:
            type: object
            properties: {}
        buildVersion:
          type: string
        connectivityStatus:
          type: string
        cpuUsage:
          type: number
        equipmentType:
          type: string
        dbInfoTimestamp:
          type: number
        dbReportTimestamp:
          type: number
        friendlyName:
          type: string
        fwVersion:
          type: string
        isp:
          type: string
        lastReboot:
          type: number
        lastReported:
          type: number
        localTimestamp:
          type: number
        macAddress:
          type: string
        memoryUsage:
          type: number
        modelName:
          type: string
        ntInfoTimestamp:
          type: number
        ntReportTimestamp:
          type: number
        serialNumber:
          type: string
        severity:
          type: number
        smartSteering:
          type: boolean
        uptime:
          type: string
        health:
          type: object
          properties: {}
    EquipmentSystemInfoResponseDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/EquipmentSystemInfoResponse'
        message:
          type: string
      title: EquipmentSystemInfoResponseDTO
    EquipmentWANData:
      type: object
      properties:
        devIpAddress:
          description: devIpAddress
          type: string
        dhcp:
          description: dhcp
          type: string
        dns1:
          description: dns1
          type: string
        dns2:
          description: dns2
          type: string
        gateway:
          description: gateway
          type: string
        leaseTimeRemaining:
          description: leaseTimeRemaining
          type: string
        leaseTimeRemainingInSeconds:
          type: number
        rxBytes:
          description: rxBytes
          type: number
          format: double
        status:
          description: status
          type: string
        txBytes:
          description: txBytes
          type: number
          format: double
        phyType:
          type: string
          enum:
            - Ethernet
            - PON
            - DSL
            - MoCA
            - WiFi
        phyTransmitRate:
          type: number
        phyReceiveRate:
          type: number
        macAddress:
          description: MAC Address of the phyical interface
          type: string
        lastChange:
          description: Unix timestamp
          type: integer
        uptime:
          description: Uptime in seconds
          type: integer
      required:
        - devIpAddress
        - dhcp
        - dns1
        - dns2
        - gateway
        - lastChange
        - leaseTimeRemaining
        - leaseTimeRemainingInSeconds
        - macAddress
        - phyReceiveRate
        - phyTransmitRate
        - phyType
        - rxBytes
        - status
        - txBytes
        - uptime
      title: EquipmentWANData
    EquipmentWANDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/EquipmentWANData'
        message:
          type: string
      required:
        - data
      title: EquipmentWANDTO
    WifiMeterDataDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: number
          format: double
        message:
          type: string
      required:
        - data
      title: WifiMeterDataDTO
    SpeedStats:
      type: object
      properties:
        date:
          description: date
          type: integer
          format: int64
        downloadSpeed:
          description: downloadSpeed
          type: number
          format: float
        latency:
          description: latency
          type: integer
          format: int64
        result:
          description: RPC Result
          type: string
        uploadSpeed:
          description: uploadSpeed
          type: number
          format: float
        url:
          description: Speed Test URL
          type: string
      required:
        - date
        - downloadSpeed
        - latency
        - result
        - uploadSpeed
        - url
      title: SpeedStats
    InternetDetailData:
      type: object
      properties:
        downLinkRate:
          description: DownLink Rate
          type: number
          format: double
        internetUtilization:
          description: Internet Utilization
          type: number
          format: double
        speedStats:
          $ref: '#/components/schemas/SpeedStats'
        subscriberRate:
          description: Subscriber Rate
          type: number
          format: double
        upLinkRate:
          description: UpLink Rate
          type: number
          format: double
      required:
        - downLinkRate
        - internetUtilization
        - speedStats
        - subscriberRate
        - upLinkRate
      title: InternetDetailData
    InternetDetailDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/InternetDetailData'
        message:
          type: string
      required:
        - data
      title: InternetDetailDTO
    InternetUtilizationData:
      type: object
      properties:
        downLinkRate:
          description: Allocated Bandwidth to Subscriber
          type: number
          format: double
        downloadPercentage:
          description: Download rate percentage
          type: number
          format: double
        downloadSpeed:
          description: downloadSpeed
          type: number
          format: double
        timestamp:
          description: timestamp
          type: integer
          format: int64
        upLinkRate:
          description: Allocated Bandwidth to Subscriber
          type: number
          format: double
        uploadPercentage:
          description: Upload rate percentage
          type: number
          format: double
        uploadSpeed:
          description: uploadSpeed
          type: number
          format: double
      required:
        - downloadSpeed
        - timestamp
        - uploadSpeed
      title: InternetUtilizationData
    InternetUtilizationDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/InternetUtilizationData'
        message:
          type: string
      required:
        - data
      title: InternetUtilizationDTO
    SpeedTestHistoryDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/SpeedStats'
        message:
          type: string
      required:
        - data
      title: SpeedTestHistoryDTO
    WifiHealthHistoryData:
      type: object
      properties:
        avgWifiHealth:
          description: avgWifiHealth
          type: number
          format: double
        day:
          type: string
      required:
        - avgWifiHealth
      title: WifiHealthHistoryData
    WifiHealthHistoryDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/WifiHealthHistoryData'
        message:
          type: string
      required:
        - data
      title: WifiHealthHistoryDTO
    WifiHealthPerMinuteData:
      type: object
      properties:
        timestamp:
          description: timestamp
          type: integer
          format: int64
        value:
          description: value
          type: number
          format: double
      required:
        - timestamp
        - value
      title: WifiHealthPerMinuteData
    WifiHealthPerMinuteDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/WifiHealthPerMinuteData'
        message:
          type: string
      required:
        - data
      title: WifiHealthPerMinuteDTO
    DeviceSpeedTest:
      type: object
      properties:
        dataRate:
          description: DataRate
          type: number
          format: float
        date:
          description: Date
          type: integer
          format: int64
      required:
        - dataRate
        - date
      title: DeviceSpeedTest
    DeviceDetailData:
      type: object
      properties:
        alarms:
          description: Alarms for this device
          type: array
          items:
            $ref: '#/components/schemas/HashMap_string_object_'
        band:
          description: Device band
          type: string
        capability:
          description: Device capability
          type: string
        connectedTo:
          description: RGW of Equipment, this device is connected to
          type: string
        connectedToName:
          description: Friendly Name of Equipment, this device is connected to
          type: string
        connectivityStatus:
          description: Device Connectivity Status
          type: string
        deviceType:
          description: Device Type
          type: string
        downlinkBytes:
          description: Device downlinkBytes
          type: number
          format: double
        downlinkErrors:
          description: Device down link error
          type: number
          format: double
        downlinkPhyRate:
          description: Device downlinkPhyRate
          type: number
          format: double
        downlinkRetransmissions:
          description: Device downlinkRetransmissions
          type: number
          format: double
        fromDSHost:
          type: boolean
        hostname:
          description: Device host name
          type: string
        internetOn:
          description: Device status ON/OFF
          type: boolean
        ip:
          description: Device ip address
          type: string
        isp:
          description: ISP
          type: string
        lastReportAt:
          description: Device last Report time
          type: integer
          format: int64
        mac:
          description: Device MAC
          type: string
        name:
          description: Device name
          type: string
        rssi:
          description: Device rssi
          type: number
          format: double
        rssiRange:
          description: RSSI Lower Limit value
          type: integer
          format: int32
        speedTestStats:
          $ref: '#/components/schemas/DeviceSpeedTest'
        ssid:
          description: Device ssid
          type: string
        uplinkBytes:
          description: Device uplinkBytes
          type: number
          format: double
        uplinkPhyRate:
          description: Device uplinkPhyRate
          type: number
          format: double
        vendor:
          description: Device vendor name
          type: string
        wifiMode:
          description: Device wifi mode
          type: string
      required:
        - alarms
        - band
        - capability
        - connectedTo
        - connectedToName
        - connectivityStatus
        - deviceType
        - downlinkBytes
        - downlinkErrors
        - downlinkPhyRate
        - downlinkRetransmissions
        - hostname
        - internetOn
        - ip
        - isp
        - lastReportAt
        - mac
        - name
        - rssi
        - speedTestStats
        - ssid
        - uplinkBytes
        - uplinkPhyRate
        - vendor
        - wifiMode
      title: DeviceDetailData
    ExtenderDetail:
      type: object
      properties:
        _24GChannelNumber:
          description: _24GChannelNumber
          type: integer
          format: int32
        _5GChannelNumber:
          description: 5GChannelNumber
          type: integer
          format: int32
        alarms:
          description: Alarms for this Extender
          type: array
          items:
            $ref: '#/components/schemas/HashMap_string_object_'
        connectivityStatus:
          description: connectivityStatus
          type: string
        devices:
          description: devices
          type: array
          items:
            $ref: '#/components/schemas/DeviceDetailData'
        macAddress:
          description: macAddress
          type: string
        name:
          description: name
          type: string
        serialNumber:
          description: serialNumber
          type: string
        severity:
          description: Severity Status for the Equipment
          type: integer
          format: int32
        type:
          description: type
          type: string
      required:
        - _24GChannelNumber
        - _5GChannelNumber
        - alarms
        - connectivityStatus
        - devices
        - macAddress
        - name
        - serialNumber
        - severity
        - type
      title: ExtenderDetail
    EquipmentDetail:
      type: object
      properties:
        _24GChannelNumber:
          description: _24GChannelNumber
          type: integer
          format: int32
        _5GChannelNumber:
          description: 5GChannelNumber
          type: integer
          format: int32
        alarms:
          description: Alarms for this Equipment
          type: array
          items:
            $ref: '#/components/schemas/HashMap_string_object_'
        connectivityStatus:
          description: connectivityStatus
          type: string
        devices:
          description: devices
          type: array
          items:
            $ref: '#/components/schemas/DeviceDetailData'
        extenders:
          description: extenders
          type: array
          items:
            $ref: '#/components/schemas/ExtenderDetail'
        macAddress:
          description: macAddress
          type: string
        name:
          description: name
          type: string
        serialNumber:
          description: serialNumber
          type: string
        severity:
          description: Severity Status for the Equipment
          type: integer
          format: int32
        type:
          description: type
          type: string
      required:
        - _24GChannelNumber
        - _5GChannelNumber
        - alarms
        - connectivityStatus
        - devices
        - extenders
        - macAddress
        - name
        - serialNumber
        - severity
        - type
      title: EquipmentDetail
    TopologyData:
      type: object
      properties:
        equipment:
          $ref: '#/components/schemas/EquipmentDetail'
        severity:
          type: integer
          format: int32
      required:
        - equipment
      title: TopologyData
    TopologyDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/TopologyData'
        message:
          type: string
      required:
        - data
      title: TopologyDTO
    NetworkListData:
      type: object
      properties:
        apType:
          description: AP type
          type: string
        band:
          description: band
          type: string
        enabled:
          description: enabled
          type: boolean
        password:
          description: password
          type: string
        serialNumber:
          description: AP serialNumber
          type: string
        ssid:
          description: ssid
          type: string
        type:
          description: type
          type: string
      required:
        - apType
        - band
        - enabled
        - password
        - serialNumber
        - ssid
        - type
      title: NetworkListData
    NetworkListDTO:
      type: object
      properties:
        code:
          type: integer
          format: int32
        data:
          description: Response Data
          type: array
          items:
            $ref: '#/components/schemas/NetworkListData'
        message:
          type: string
      required:
        - data
      title: NetworkListDTO
    LoginRequest:
      type: object
      properties:
        password:
          description: Password for the authentication
          type: string
        username:
          description: Username for login
          type: string
      required:
        - password
        - username
      title: LoginRequest
    LoginDataDTO:
      type: object
      properties:
        accessToken:
          description: Access Token for the Login request
          type: string
        verify:
          description: Status of user email verification
          type: boolean
      required:
        - accessToken
        - verify
      title: LoginDataDTO
    LoginResponseDTO:
      type: object
      properties:
        code:
          description: Response Code
          type: integer
          format: int32
        data:
          $ref: '#/components/schemas/LoginDataDTO'
        message:
          description: Response Message
          type: string
      required:
        - code
        - data
        - message
      title: LoginResponseDTO
