openapi: 3.0.0
info:
  title: Optim API v5
  version: 5.21.2
servers:
  - url: /
security:
  - xAuthBearer: []
tags:
  - name: Auth
    description: Authentication
  - name: Network
    description: Whole network info
  - name: Equipment
    description: Equipment resource
  - name: Device
    description: Device resource
  - name: Network Connection
    description: Network connection resource
  - name: WiFi
    description: WiFi resource
  - name: Firmware
    description: FW resource
paths:
  /actiontec/api/unauth/platform/v5/login:
    post:
      tags:
        - Auth
      summary: Login with crendntials
      operationId: loginWithCrendntials
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/loginPostData'
        required: true
      responses:
        '200':
          description: Login successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/authResponseData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/me:
    get:
      tags:
        - Auth
      summary: Get profile of the current user
      operationId: getUserProfile
      responses:
        '200':
          description: Get user profile successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userProfileData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    put:
      tags:
        - Auth
      summary: Set profile of the current user
      operationId: setUserProfile
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/userProfileData'
        required: true
      responses:
        '200':
          description: Set user profile successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network:
    get:
      tags:
        - Network
      summary: Get user's network contracts
      operationId: getAllNetworkData
      parameters:
        - name: subscriberId
          in: query
          description: Unique key to identify subscriber, this parameter only works when user's role is not USER
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Get network data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/networkData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    post:
      tags:
        - Network
      summary: Create a network contracts
      description: USER role can only post name
      operationId: postNetworkData
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/networkPostData'
        required: true
      responses:
        '201':
          description: Create network successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/generalPostResponseData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}:
    get:
      tags:
        - Network
      summary: Get user's network
      operationId: getNetworkData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get network data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/networkData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    put:
      tags:
        - Network
      summary: Edit a network contracts
      description: USER role can only edit name
      operationId: setNetworkData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/networkData'
        required: true
      responses:
        '201':
          description: Create network successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    delete:
      tags:
        - Network
      summary: Delete a network contracts
      description: USER role can not delete network created by ISP
      operationId: deleteNetworkData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Delete network successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment:
    get:
      tags:
        - Equipment
      summary: Get all equipment data
      operationId: getAllEquipmentData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get equipment data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/equipmentData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    post:
      tags:
        - Equipment
      summary: Add a new equipment to a network
      operationId: postEquipmentData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/equipmentPostData'
        required: true
      responses:
        '201':
          description: Add equipment to network successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/generalPostResponseData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}:
    get:
      tags:
        - Equipment
      summary: Get equipment data
      operationId: getEquipmentData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get equipment data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/equipmentData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    put:
      tags:
        - Equipment
      summary: Set equipment data
      operationId: setEquipmentData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/equipmentPutData'
        required: true
      responses:
        '200':
          description: Set equipment data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    delete:
      tags:
        - Equipment
      summary: Delete an equipment from network
      operationId: deleteEquipmentData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Delete equipment data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/actions:
    post:
      tags:
        - Equipment
      summary: Post an equipment action
      operationId: postEquipmentAction
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/equipmentPostActionData'
        required: true
      responses:
        '200':
          description: Post equipment action successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/series:
    get:
      tags:
        - Equipment
      summary: Get equipment time series data
      operationId: getEquipmentSeriesData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Time duration in seconds
          required: false
          schema:
            type: integer
            default: 3600
      responses:
        '200':
          description: Get time series equipment data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/equipmentSeriesData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/systemLogs:
    get:
      tags:
        - Equipment
      summary: Get system logs
      operationId: getEquipmentSystemLogs
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Time duration in seconds
          required: false
          schema:
            type: integer
            default: 3600
        - name: type
          in: query
          description: Type to filter system logs. Multiple types will be a comma joined string like "Reboot,Restore"
          required: false
          schema:
            type: string
      responses:
        '200':
          description: Get system logs successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/equipmentSystemLogData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/topology:
    get:
      tags:
        - Network
      summary: Get network topology
      operationId: getTopologyData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get equipment data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/topologyData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/alerts:
    get:
      tags:
        - Network
      summary: Get network alerts
      operationId: getAlertData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: active
          in: query
          description: Fetch active alerts only or not
          required: false
          schema:
            type: boolean
        - name: severity
          in: query
          description: Severity filter
          required: false
          schema:
            type: string
            enum:
              - Error
              - Warning
              - Info
        - name: duration
          in: query
          description: Time duration in seconds
          required: false
          schema:
            type: integer
            default: 3600
      responses:
        '200':
          description: Get alerts successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/alertData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/traffic:
    get:
      tags:
        - Network
      summary: Get network traffic usage
      description: This API excludes extender traffic
      operationId: getTrafficData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: startTime
          in: query
          description: Unix timestamp of start time
          required: false
          schema:
            type: integer
        - name: endTime
          in: query
          description: Unix timestamp of end time
          required: false
          schema:
            type: integer
        - name: type
          in: query
          description: Traffic type
          required: false
          schema:
            type: string
            default: Internet
            enum:
              - Internet
              - WiFi
      responses:
        '200':
          description: Get traffic usage successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/networkTrafficData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/devices:
    get:
      tags:
        - Device
      summary: Get all devices data
      operationId: getAllDevicesData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get all devices data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/deviceData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/devices/{deviceId}:
    get:
      tags:
        - Device
      summary: Get device data
      operationId: getDeviceData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: deviceId
          in: path
          description: Unique key to identify a device
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get device data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/deviceData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    put:
      tags:
        - Device
      summary: Edit device data
      operationId: setDeviceData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: deviceId
          in: path
          description: Unique key to identify a device
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/deviceData'
        required: true
      responses:
        '200':
          description: Set device data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/devices/{deviceId}/actions:
    post:
      tags:
        - Device
      summary: Trigger a device action
      operationId: postDeviceAction
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: deviceId
          in: path
          description: Unique key to identify a device
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/deviceActionData'
        required: true
      responses:
        '200':
          description: Set device data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/upstreamConnections:
    get:
      tags:
        - Network Connection
      summary: Get all upstream data
      operationId: getAllUpstreamData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get upstream data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/upstreamData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/upstreamConnections/{connectionId}:
    get:
      tags:
        - Network Connection
      summary: Get upstream data
      operationId: getUpstreamData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: connectionId
          in: path
          description: Unique key to identify an upstream connection
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get upstream data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/upstreamData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/downstreamConnections:
    get:
      tags:
        - Network Connection
      summary: Get all downstream data
      operationId: getAllDownstreamData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get downstream data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/downstreamData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/downstreamConnections/{connectionId}:
    get:
      tags:
        - Network Connection
      summary: Get downstream data
      operationId: getDownstreamData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: connectionId
          in: path
          description: Unique key to identify a downstream connection
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get downstream data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/downstreamData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/radios:
    get:
      tags:
        - WiFi
      summary: Get all radio data
      operationId: getAllRadioData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get radio data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/radioData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/radios/{radioId}:
    get:
      tags:
        - WiFi
      summary: Get radio data
      operationId: getRadioData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: radioId
          in: path
          description: Unique key to identify a radio
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get radio data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/radioData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    put:
      tags:
        - WiFi
      summary: Set radio data
      description: This request will issue a RPC to CPE
      operationId: setRadioData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: radioId
          in: path
          description: Unique key to identify a radio
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/radioData'
        required: true
      responses:
        '200':
          description: Set radio data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/radios/{radioId}/actions:
    post:
      tags:
        - WiFi
      summary: Trigger a radio action
      description: This request will issue a RPC to CPE
      operationId: postRadioAction
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: radioId
          in: path
          description: Unique key to identify a radio
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/radioAction'
        required: true
      responses:
        '200':
          description: Set radio data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/radios/{radioId}/series:
    get:
      tags:
        - WiFi
      summary: Get radio time series data
      operationId: getRadioSeriesData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: radioId
          in: path
          description: Unique key to identify a radio
          required: true
          schema:
            type: string
        - name: duration
          in: query
          description: Time duration in seconds
          required: false
          schema:
            type: integer
            default: 3600
        - name: fields
          in: query
          description: First level field projection, timestamp can't be omitted. Omitting this parameter will get all the available fields. Multiple fields will be a comma joined string like "airTimeBusy,chennel".
          required: false
          schema:
            type: string
            enum:
              - airTimeBusy
              - channel
              - bytesReceived
              - bytesSent
              - airTimeBusyDeviceDistribution
      responses:
        '200':
          description: Get time series radio data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/radioSeriesData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/ssids:
    get:
      tags:
        - WiFi
      summary: Get all SSID data
      operationId: getAllSsidData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: alignment
          in: query
          description: API to align same Primary / Guest SSIDS or not
          required: false
          schema:
            type: boolean
            default: true
            enum:
              - true
              - false
      responses:
        '200':
          description: Get SSID data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ssidData'
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/ssids/{ssidId}:
    get:
      tags:
        - WiFi
      summary: Get SSID data
      operationId: getSsidData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: ssidId
          in: path
          description: Unique key to identify an SSID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get SSID data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ssidData'
        default:
          description: Unexpected errors
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    put:
      tags:
        - WiFi
      summary: Set SSID data
      description: This request will issue a RPC to CPE
      operationId: setSsidData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
        - name: ssidId
          in: path
          description: Unique key to identify an SSID
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ssidData'
        required: true
      responses:
        '200':
          description: Set SSID data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/ssids:
    get:
      tags:
        - WiFi
      summary: Get all SSID data (whole home network)
      operationId: getNetworkAllSsidData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: alignment
          in: query
          description: API to align same Primary / Guest SSIDS or not
          required: false
          schema:
            type: boolean
            default: true
            enum:
              - true
              - false
      responses:
        '200':
          description: Get SSID data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/wholeNetworkSsidData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/ssids/{ssidId}:
    get:
      tags:
        - WiFi
      summary: Get SSID data (whole home network)
      operationId: getNetworkSsidData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: ssidId
          in: path
          description: Unique key to identify an SSID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get SSID data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/wholeNetworkSsidData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
    put:
      tags:
        - WiFi
      summary: Set SSID data (whole home network)
      description: This request will issue a RPC to CPE
      operationId: setNetworkSsidData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: ssidId
          in: path
          description: Unique key to identify an SSID
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/wholeNetworkSsidData'
        required: true
      responses:
        '200':
          description: Set SSID data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/firmwares:
    get:
      tags:
        - Firmware
      summary: Get CPE's firmware data
      operationId: getFirmwareData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get firmware data successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/firmwareData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/firmwares/logs:
    get:
      tags:
        - Firmware
      summary: Get CPE's firmware log
      operationId: getFirmwareLogData
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Get firmware log successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/firmwareLogData'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
  /actiontec/api/v5/network/{networkId}/equipment/{equipmentId}/firmwares/actions:
    post:
      tags:
        - Firmware
      summary: Post a firmware action
      operationId: postFirmwareAction
      parameters:
        - name: networkId
          in: path
          description: Unique key to identify a home network
          required: true
          schema:
            type: string
        - name: equipmentId
          in: path
          description: Unique key to identify a CPE, may be S/N
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/firmwarePostActionData'
        required: true
      responses:
        '200':
          description: Post firmware action successfully
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
        default:
          description: Unexpected errors
          headers:
            X-API-Version:
              $ref: '#/components/headers/xApiVersion'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/errorResponse'
components:
  securitySchemes:
    xAuthBearer:
      type: apiKey
      in: header
      name: X-Authorization
      description: |
        ## Example
        ```
        X-Authorization: Bearer <token>
        ```
  schemas:
    loginPostData:
      type: object
      properties:
        username:
          type: string
        password:
          type: string
    errorResponse:
      type: object
      properties:
        message:
          type: string
          readOnly: true
    authResponseData:
      type: object
      properties:
        accessToken:
          type: string
    networkData_isp:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        name:
          type: string
          readOnly: true
      readOnly: true
    userProfileData_address:
      type: object
      properties:
        country:
          type: string
          readOnly: true
        state:
          type: string
          readOnly: true
        city:
          type: string
          readOnly: true
      readOnly: true
    userProfileData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        firstName:
          type: string
          readOnly: true
        lastName:
          type: string
          readOnly: true
        displayName:
          type: string
        email:
          type: string
          readOnly: true
        phone:
          type: string
          readOnly: true
        avatarUrl:
          type: string
          readOnly: true
        authProvider:
          type: string
          readOnly: true
          enum:
            - Actiontec
            - Micorsoft
            - Google
            - Facebook
            - Others
        isp:
          $ref: '#/components/schemas/networkData_isp'
        address:
          $ref: '#/components/schemas/userProfileData_address'
    networkData_subscription:
      type: object
      properties:
        downlinkRate:
          type: integer
        uplinkRate:
          type: integer
      description: These fields are filled by ISP
    networkData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        serviceTelephoneNumber:
          type: string
          description: This field is filled by ISP
        name:
          type: string
        ispManaged:
          type: boolean
          readOnly: true
        isp:
          $ref: '#/components/schemas/networkData_isp'
        subscription:
          $ref: '#/components/schemas/networkData_subscription'
    networkPostData:
      allOf:
        - $ref: '#/components/schemas/networkData'
        - type: object
          properties:
            subscriberId:
              type: string
              description: This field is filled by ISP
              writeOnly: true
    generalPostResponseData:
      required:
        - id
      type: object
      properties:
        id:
          type: string
          readOnly: true
    modelSnapshotData:
      type: object
      properties:
        fileName:
          type: string
        fileSize:
          type: number
          description: file size in bytes
        fileLocation:
          type: string
          description: URL of snapshot
          writeOnly: true
        fileEntry:
          type: string
          description: URL to post / get file
          readOnly: true
        fileStatus:
          type: string
          readOnly: true
          enum:
            - Initial
            - Uploading
            - Success
            - Error
    modelHardwareData:
      required:
        - availableLanTypes
        - availableRadioKeys
        - availableWanTypes
        - cpuCores
        - cpuSpeed
        - flashSpaceSize
        - memorySize
      type: object
      properties:
        memorySize:
          type: number
          description: In bytes
        flashSpaceSize:
          type: number
          description: In bytes
        cpuSpeed:
          type: number
        cpuCores:
          type: integer
          description: 1~64
        smmMemorySize:
          type: number
          description: In bytes
        smmFlashSpaceSize:
          type: number
          description: In bytes
        smmCpuMhz:
          type: number
        availableRadioKeys:
          type: array
          items:
            type: string
            enum:
              - 2.4G
              - 5G
              - 5GLo
              - 5GHi
              - 6G
        availableWanTypes:
          type: array
          items:
            type: string
            enum:
              - WiFi
              - Ethernet
              - MoCA
              - DSL
              - PON
        availableLanTypes:
          type: array
          items:
            type: string
            enum:
              - WiFi
              - Ethernet
              - MoCA
    equipmentData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
          enum:
            - Up
            - Down
        serialNumber:
          type: string
          readOnly: true
        macAddress:
          type: string
          readOnly: true
        modelName:
          type: string
          readOnly: true
        name:
          type: string
          description: friendly name
        fwVersion:
          type: string
          readOnly: true
        agentVersion:
          type: string
          readOnly: true
        cpuUsage:
          type: number
          readOnly: true
        memoryUsage:
          type: number
          readOnly: true
        availableMemory:
          type: number
          readOnly: true
        availableFlashSpace:
          type: number
          readOnly: true
        equipmentType:
          type: string
          readOnly: true
          enum:
            - Gateway
            - Extender
        equipmentTypeId:
          type: string
        lastRebootTime:
          type: integer
          readOnly: true
        lastDataProcessedTime:
          type: integer
          readOnly: true
        lastReportTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        snapshot:
          $ref: '#/components/schemas/modelSnapshotData'
        hardware:
          $ref: '#/components/schemas/modelHardwareData'
        features:
          type: array
          items:
            type: string
    equipmentPostData:
      type: object
      properties:
        serialNumber:
          type: string
        name:
          type: string
    equipmentPutData:
      type: object
      properties:
        name:
          type: string
          description: friendly name
    equipmentPostActionData_burstMode:
      type: object
      properties:
        duration:
          type: integer
          description: Burst mode duration in seconds
    equipmentPostActionData:
      type: object
      properties:
        action:
          type: string
          enum:
            - Reboot
            - RestoreDefault
            - EnableBurstMode
            - DisableBurstMode
            - EnableWPS
        burstMode:
          $ref: '#/components/schemas/equipmentPostActionData_burstMode'
    equipmentSeriesData:
      type: object
      properties:
        timestamp:
          type: integer
          readOnly: true
        cpuUsage:
          type: number
          readOnly: true
        memoryUsage:
          type: number
          readOnly: true
    equipmentSystemRebootLogData:
      type: object
      properties:
        reason:
          type: string
          readOnly: true
        uptime:
          type: integer
          description: CPE up time in seconds
          readOnly: true
    equipmentSystemLogData:
      type: object
      properties:
        time:
          type: integer
          description: Unix timestamp
          readOnly: true
        type:
          type: string
          readOnly: true
          enum:
            - Reboot
        details:
          oneOf:
            - $ref: '#/components/schemas/equipmentSystemRebootLogData'
    topologyEquipmentData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        serialNumber:
          type: string
          readOnly: true
        name:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
          enum:
            - Up
            - Down
        equipmentType:
          type: string
          readOnly: true
          enum:
            - Gateway
            - Extender
        macAddress:
          type: string
          readOnly: true
        upstream:
          type: string
          description: S/N of upstream equipment
          readOnly: true
        phyType:
          type: string
          readOnly: true
          enum:
            - WiFi
            - Ethernet
            - MoCA
            - DSL
            - PON
    topologyDeviceData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        macAddress:
          type: string
          readOnly: true
        name:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
          enum:
            - Up
            - Down
        deviceType:
          type: string
          readOnly: true
          enum:
            - Extender
            - SetTopBox
            - Other
            - MobilePhone
            - WiredPhone
            - Tablet
            - Laptop
            - Computer
            - Camera
            - Robot
            - Game
            - TV
            - Speaker
            - Printer
            - Bulb
        upstream:
          type: string
          description: S/N of upstream equipment
          readOnly: true
        phyType:
          type: string
          enum:
            - WiFi
            - Ethernet
            - MoCA
    topologyData:
      type: object
      properties:
        equipmentList:
          type: array
          items:
            $ref: '#/components/schemas/topologyEquipmentData'
        deviceList:
          type: array
          items:
            $ref: '#/components/schemas/topologyDeviceData'
        lastReportTime:
          type: integer
          description: Unix timestamp
          readOnly: true
    alertData_indicators:
      type: object
      properties:
        radioKey:
          type: string
          readOnly: true
        rebootTimes:
          type: integer
          readOnly: true
    alertData:
      type: object
      properties:
        type:
          type: string
          readOnly: true
          enum:
            - Equipment
            - Device
            - Network
        instId:
          type: string
          description: Network ID(STN) or CPE ID(S/N) or device MAC
          readOnly: true
        severity:
          type: string
          readOnly: true
          enum:
            - Error
            - Warning
            - Info
        reason:
          type: string
          readOnly: true
          enum:
            - No report
            - High memory usage
            - Radios disabled
            - High airtime usage
            - High downlink usage
            - High uplink usage
            - Reboot
            - Low signal
            - Legacy operating standard
            - No DFS
        startTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        endTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        active:
          type: boolean
          readOnly: true
        indicators:
          $ref: '#/components/schemas/alertData_indicators'
    networkTrafficData:
      type: object
      properties:
        deviceId:
          type: string
          readOnly: true
        bytesSent:
          type: integer
          readOnly: true
        bytesReceived:
          type: integer
          readOnly: true
      readOnly: true
    deviceAddressData:
      type: object
      properties:
        version:
          type: string
          readOnly: true
          enum:
            - IPv6
            - IPv4
        type:
          type: string
          readOnly: true
          enum:
            - GUA
            - LLA
            - ULA
        address:
          type: string
          readOnly: true
      readOnly: true
    deviceWirelessData:
      type: object
      properties:
        macAddress:
          type: string
          readOnly: true
        operatingStandards:
          type: string
          readOnly: true
          enum:
            - a
            - b
            - g
            - 'n'
            - ac
            - ax
        channel:
          type: integer
          readOnly: true
        rssi:
          type: integer
          readOnly: true
        airTimeUsage:
          type: number
          readOnly: true
        dualBandSupported:
          type: boolean
          readOnly: true
        dfsSupported:
          type: boolean
          readOnly: true
        ieee80211kSupported:
          type: boolean
          readOnly: true
        ieee80211vSupported:
          type: boolean
          readOnly: true
        ieee80211rSupported:
          type: boolean
          readOnly: true
        ssid:
          type: string
          readOnly: true
        radioKey:
          type: string
          readOnly: true
          enum:
            - 2.4G
            - 5G
            - 5GLo
            - 5GHi
            - 6G
        ssidKey:
          type: string
          readOnly: true
          enum:
            - Primary
            - Guest
            - Backhaul
            - Video
        upstream:
          type: string
          readOnly: true
      readOnly: true
    deviceInternetAccessData:
      type: object
      properties:
        blocked:
          type: boolean
        startTime:
          type: integer
          description: Unix timestamp
        duration:
          type: integer
      readOnly: true
    deviceSpeedTestData:
      type: object
      properties:
        rate:
          type: number
          readOnly: true
        lastTestTime:
          type: integer
          description: Unix timestamp
          readOnly: true
      readOnly: true
    deviceStatsData:
      type: object
      properties:
        bytesSent:
          type: integer
          readOnly: true
        bytesReceived:
          type: integer
          readOnly: true
        errorsSent:
          type: integer
          readOnly: true
        retransSent:
          type: integer
          readOnly: true
      readOnly: true
    deviceMarkedData:
      type: object
      properties:
        name:
          type: string
          readOnly: true
          enum:
            - MAC
        reason:
          type: string
          readOnly: true
          enum:
            - Virtual MAC
        severity:
          type: string
          readOnly: true
          enum:
            - Error
            - Warning
            - Info
      readOnly: true
    deviceData:
      type: object
      properties:
        id:
          type: string
          description: Should be MAC
          readOnly: true
        macAddress:
          type: string
          readOnly: true
        name:
          type: string
        hostName:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
          enum:
            - Up
            - Down
        deviceType:
          type: string
          description: Users are not allow to set a device as extender or set-top box
          enum:
            - Extender
            - SetTopBox
            - Other
            - MobilePhone
            - WiredPhone
            - Tablet
            - Laptop
            - Computer
            - Camera
            - Robot
            - Game
            - TV
            - Speaker
            - Printer
            - Bulb
        phyType:
          type: string
          readOnly: true
          enum:
            - WiFi
            - Ethernet
            - MoCA
        vendor:
          type: string
          readOnly: true
        addresses:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/deviceAddressData'
        downlinkPhyRate:
          type: number
          readOnly: true
        uplinkPhyRate:
          type: number
          readOnly: true
        lastReportTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        wireless:
          $ref: '#/components/schemas/deviceWirelessData'
        internetAccess:
          $ref: '#/components/schemas/deviceInternetAccessData'
        speedTest:
          $ref: '#/components/schemas/deviceSpeedTestData'
        stats:
          $ref: '#/components/schemas/deviceStatsData'
        markedAttributes:
          type: array
          readOnly: true
          items:
            $ref: '#/components/schemas/deviceMarkedData'
    deviceActionData:
      type: object
      properties:
        action:
          type: string
          enum:
            - InternetAccess
            - Reconnect
            - SpeedTest
        internetAccessAction:
          type: string
          enum:
            - Allow
            - Block
        startTime:
          type: integer
          description: Unix timestamp
        duration:
          type: integer
          description: Block duration in seconds
    ipv6AddressData:
      type: object
      properties:
        type:
          type: string
          readOnly: true
          enum:
            - GUA
            - LLA
            - ULA
        origin:
          type: string
          enum:
            - DHCPv6
            - AutoConfigured
            - IKEv2
            - MAP
            - WellKnown
            - Static
        address:
          type: string
        lifetimeState:
          type: string
          enum:
            - Valid
            - Infinite
            - Unknown
        lifetime:
          type: integer
          description: Unix timestamp
          readOnly: true
    dnsData:
      type: object
      properties:
        type:
          type: string
          enum:
            - DHCPv6
            - DHCPv4
            - RouterAdvertisement
            - IPCP
            - Static
        address:
          type: string
    upstreamIpv6Data:
      type: object
      properties:
        enabled:
          type: boolean
        protocol:
          type: string
          enum:
            - DHCPv6
            - PPPv6
            - Static
            - 6rd
            - None
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/ipv6AddressData'
        dnsServers:
          type: array
          items:
            $ref: '#/components/schemas/dnsData'
        defaultGateway:
          type: string
    upstreamData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        status:
          type: string
          readOnly: true
          enum:
            - Up
            - Down
        phyType:
          type: string
          readOnly: true
          enum:
            - Ehternet
            - DSL
            - MoCA
            - PON
            - WiFi
        phyTransmitRate:
          type: integer
          readOnly: true
        phyReceiveRate:
          type: integer
          readOnly: true
        lastChangeTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        lastReportTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        ipv6:
          $ref: '#/components/schemas/upstreamIpv6Data'
    downstreamIpv6Data:
      type: object
      properties:
        enabled:
          type: boolean
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/ipv6AddressData'
    downstreamData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        lastReportTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        ipv6:
          $ref: '#/components/schemas/downstreamIpv6Data'
    radioStatsData:
      type: object
      properties:
        bytesSent:
          type: integer
          readOnly: true
        bytesReceived:
          type: integer
          readOnly: true
        avgBytesSent:
          type: integer
          readOnly: true
        avgBytesReceived:
          type: integer
          readOnly: true
      readOnly: true
    radioData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        enabled:
          type: boolean
        status:
          type: string
          readOnly: true
          enum:
            - Up
            - Down
            - Error
        radioKey:
          type: string
          readOnly: true
          enum:
            - 2.4G
            - 5G
            - 5GLo
            - 5GHi
            - 6G
        autoChannelEnabled:
          type: boolean
        channel:
          type: integer
        channelBandwidth:
          type: integer
          enum:
            - 20
            - 40
            - 80
            - 160
        operatingStandards:
          type: array
          items:
            type: string
            enum:
              - a
              - b
              - g
              - 'n'
              - ac
              - ax
        dfsEnabled:
          type: boolean
        isBackhaul:
          type: boolean
          readOnly: true
        devicesAssociated:
          type: integer
          readOnly: true
        lastReportTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        stats:
          $ref: '#/components/schemas/radioStatsData'
    radioAction:
      type: object
      properties:
        action:
          type: string
          enum:
            - Reset
            - RescanChannel
            - RescanNeighbor
    radioSeriesData_airTimeBusyDeviceDistribution:
      type: object
      properties:
        airTimeBusy:
          type: number
          readOnly: true
        macAddress:
          type: string
          readOnly: true
        name:
          type: string
          readOnly: true
        deviceType:
          type: string
          readOnly: true
          enum:
            - Extender
            - SetTopBox
            - Other
            - MobilePhone
            - WiredPhone
            - Tablet
            - Laptop
            - Computer
            - Camera
            - Robot
            - Game
            - TV
            - Speaker
            - Printer
            - Bulb
    radioSeriesData:
      type: object
      properties:
        timestamp:
          type: integer
          readOnly: true
        airTimeBusy:
          type: number
          readOnly: true
        channel:
          type: integer
          readOnly: true
        bytesSent:
          type: integer
          readOnly: true
        bytesReceived:
          type: integer
          readOnly: true
        deltaBytesSent:
          type: integer
          readOnly: true
        deltaBytesReceived:
          type: integer
          readOnly: true
        airTimeBusyDeviceDistribution:
          type: array
          items:
            $ref: '#/components/schemas/radioSeriesData_airTimeBusyDeviceDistribution'
    ssidData:
      type: object
      properties:
        id:
          type: string
          readOnly: true
        enabled:
          type: boolean
        status:
          type: string
          readOnly: true
          enum:
            - Up
            - Down
            - Error
        bssid:
          type: string
          readOnly: true
        radioKeys:
          type: array
          readOnly: true
          items:
            type: string
            enum:
              - 2.4G
              - 5G
              - 5GLo
              - 5GHi
              - 6G
        radioIds:
          type: array
          readOnly: true
          items:
            type: string
        ssidKey:
          type: string
          readOnly: true
          enum:
            - Primary
            - Guest
            - Backhaul
            - Video
        name:
          type: string
        password:
          type: string
        securityMode:
          type: string
        lastReportTime:
          type: integer
          description: Unix timestamp
          readOnly: true
    wholeNetworkSsidData:
      allOf:
        - $ref: '#/components/schemas/ssidData'
        - type: object
          properties:
            serialNumber:
              type: string
              readOnly: true
    firmwareData:
      type: object
      properties:
        version:
          type: string
          readOnly: true
        active:
          type: boolean
          readOnly: true
    firmwareLogData:
      type: object
      properties:
        action:
          type: string
          readOnly: true
          enum:
            - Upgrade
            - Swap
        version:
          type: string
          readOnly: true
        fileEntry:
          type: string
          readOnly: true
        resultState:
          type: string
          readOnly: true
          enum:
            - Complete
            - Failed
        startTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        endTime:
          type: integer
          description: Unix timestamp
          readOnly: true
        faultCode:
          type: integer
          readOnly: true
    firmwarePostActionData:
      type: object
      properties:
        action:
          type: string
          enum:
            - Upgrade
            - Swap
        imageId:
          type: string
  headers:
    xApiVersion:
      description: AEI API version
      schema:
        type: integer
