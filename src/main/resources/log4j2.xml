<?xml version="1.0" encoding="UTF-8"?>
<Configuration monitorInterval="60">
    <Properties>
        <Property name="path">/home/<USER>/Property>
    </Properties>
    <Appenders>
        <Console name="Console-Appender" target="SYSTEM_OUT">
            <PatternLayout>
                <pattern>
                    [%-5level] %d{dd-MM-yyyy HH:mm:ss.SSS} [%t] %c{1} - %msg%n
                </pattern>
                >
            </PatternLayout>
        </Console>
        <RollingFile name="RollingFile" fileName="${path}/setup/logs/app.log" filePattern="${path}/setup/logs/app-%d{yyyy-MM-dd}-%i.log" >
            <PatternLayout>
                <pattern>%d [%-6p] %C{1}.%M(%F:%L) – %m%n</pattern>
            </PatternLayout>
            <Policies>
                <SizeBasedTriggeringPolicy size="50 MB" />
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
    </Appenders>
    <Loggers>
        <Logger name="org" level="info"
                additivity="false">
            <AppenderRef ref="RollingFile" />
            <AppenderRef ref="Console-Appender" />
        </Logger>
        <Root level="info">
            <AppenderRef ref="Console-Appender" />
        </Root>
    </Loggers>
</Configuration>
