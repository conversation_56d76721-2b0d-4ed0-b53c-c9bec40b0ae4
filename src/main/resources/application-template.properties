### Default Properties
server.port=9092
##### RELATIONAL DATABASE PROPERTIES #######
mysql.url=***********************************************************************************************************************
mysql.mt_url=*********************************************************************************************************************
mysql.username=_DATABASE_USER_
mysql.password=_DATABASE_PASSWORD_
mysql.driverClassName=com.mysql.jdbc.Driver
mysql.hibernateDialect=org.hibernate.dialect.MySQL5InnoDBDialect
mysql.hibernateShowSql=false
mysql.hibernateFormatSql=true
mysql.hibernateCreateMode=update
mysql.mtConnectionProvider=com.incs83.mt.MultiTenantService
mysql.mtTenantIdentifier=com.incs83.mt.CurrentTenantIdentifier
mysql.multitenancyScheme=DATABASE
mysql.connectionPoolClass=org.hibernate.c3p0.internal.C3P0ConnectionProvider
mysql.poolMinConnections=1
mysql.poolMaxConnections=20
mysql.poolTimeout=120
mysql.poolMaxStatements=10
mysql.generateMLTables=false
mysql.allowMerge=allow

mongodb.hostname=_MONGO_IP_
mongodb.username=_MONGO_USER_
mongodb.password=_MONGO_PASSWORD_
mongodb.port=12543
mongodb.database=actiontec
mongodb.authEnabled=true

kafka.bootstrapServers=_KAFKA_BOOTSTRAP_SERVERS_
kafka.topic=INTERNAL_CONTROL
kafka.numOfThreads=1
kafka.pollIntervalInMilliSeconds=10000

hazelcast.hostname:127.0.0.1

mail.api-key=*********************************************************************
app.mail.configured=true
cache.available=false
jwt.tokenSigningKey:xm8EV6Hy5RMFK4EEACIDAwQus

internalDns=_INTERNAL_DNS_

gcm.baseUrl=https://gcm-http.googleapis.com/gcm/send
gcm.apiKey=AAAAdtC1MfY:APA91bFfKXFHjTG1ShDLIhjBRtRtfI7Zx15ByVYvXtv5_2oU_3_BPQR-yjmI3Iv3TTr9uiyMAw2sVOfva-dztKjd8dpCOzUF7gNrN5cFZnmdPdge2a0OuTGm1XcsPn0BQllJtnjCat6O

cloudinary.name=incs83
cloudinary.key=984833146195414
cloudinary.secret=S3bQJXY7lbPuBRaBRwPdKLv0ftU

cron.enabled=false

twilioPhoneNumber=_TWILIO_NUMBER_
monitoringPhoneNumbers=_MONITORING_PHONE_NUMBERS_

############Multiple Mqtt connection###################
cmPort=8765
mqttAuthtoken=secret

################### MQTT kafka connect#####################
kafkaConnectIpAddr=_KAFKA_CONNECT_IP
##kafkaConnectIpAddr=127.0.0.1
kafkaConnectPort=8765
kafkaAuthtoken=secret2
kafkaTopics=REPORT/#

################### Swagger(Springdoc)#####################
springdoc.swagger-ui.operationsSorter=alpha
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.filter=true
springdoc.swagger-ui.disable-swagger-default-url=true
springdoc.swagger-ui.display-request-duration=true
springdoc.swagger-ui.docExpansion=none
springdoc.swagger-ui.urls[0].url=/swagger/v6/openapi-external.yaml
springdoc.swagger-ui.urls[0].name=Optim V6 API
springdoc.swagger-ui.urls[1].url=/swagger/v5/openapi-external.yaml
springdoc.swagger-ui.urls[1].name=Optim V5 API
springdoc.swagger-ui.urls[2].url=/swagger/v2/openapi-external.yaml
springdoc.swagger-ui.urls[2].name=Optim V2 API
springdoc.swagger-ui.version=5.25.3