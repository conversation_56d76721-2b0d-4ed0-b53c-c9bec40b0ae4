# Iot_cloud_backend 

## Prerequisites

Before you begin, ensure you have met the following requirements:

* You have installed the correct versions of:
  * Java 1.8
  * Gradle 6.8.3
* You have `awscli` installed and properly configured with your AWS credentials.

## Building and Pushing Docker Image to AWS ECR

Follow these steps to build your project and push the Docker image to AWS ECR:

1. Clean the project using Gradle:
   ```bash
   gradle clean

2. Build the project, skipping the tests:
   ```bash 
   gradle -x test build

3. Build the Docker image and push it to AWS ECR using the provided script:
    ```bash
   dockerBuildAndPush.sh


Please go to the ngoc-deployment repository, get the  OcPlatform helm chart to deploy to k8s cluster.
